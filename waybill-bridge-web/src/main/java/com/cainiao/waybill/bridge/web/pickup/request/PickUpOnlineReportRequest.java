package com.cainiao.waybill.bridge.web.pickup.request;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpOnlineReportRequest
 * @Description
 * @Date 2022/12/14 8:54 下午
 * @Version 1.0
 */
@Data
public class PickUpOnlineReportRequest implements Serializable {

    private static final long serialVersionUID = -8020035037379875441L;

    public static final String TODAY = "today";

    public static final String LASTDAY = "lastday";

    private String bizDate = TODAY;

    private String source = "JL";

    private String accessToken;

    private String cpCode = "all";
}
