package com.cainiao.waybill.bridge.privacy.api;

import java.util.Map;

/**
 * 短信托收转发
 *
 * <AUTHOR>
 */
public interface WaybillPrivacyMainNoSmsService {


    /**
     * 解决往主号上发送短信的运单 需求暂时变更为只转发邮政、极兔等未进行短信代发的CP 后续需要支持自买它收场景
     *
     * @param smsSign       短信签名  必填项
     * @param mailNo        完整运单号 必填项
     * @param privacyMainNo 虚拟主号 必填项
     * @param pickUpCode    取件码
     * @param ywyPhone      小件员号码
     * @param smsParam      短信内容和参数 必填项
     * @return
     */
    boolean smsSendByMailNo(String smsSign, String mailNo, String privacyMainNo, String pickUpCode, String ywyPhone, Map<String, String> smsParam);

}
