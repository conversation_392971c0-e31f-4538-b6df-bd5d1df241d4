package com.cainiao.waybill.bridge.biz.hsf.provider;


import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.cainiao.waybill.bridge.client.hsf.WaybillLDQueryService;
import com.cainiao.waybill.bridge.client.response.LocOrderResponse;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants;
import com.taobao.loc.common.domain.LocOrderDO;
import com.taobao.loc.common.domain.LocQueryOptionDO;
import com.taobao.loc.service.orderinfo.LocReadService;
import com.taobao.logistics.domain.SingleResultDO;
import com.taobao.logistics.domain.dataobject.OrderDO;
import com.taobao.logisticsdetail.common.domain.basic.LogisticsDetailDO;
import com.taobao.logisticsdetail.common.domain.basic.LogisticsDetailQueryOptionDO;
import com.taobao.logisticsdetail.common.domain.result.SingleResult;
import com.taobao.logisticsdetail.common.service.basic.LogisticsDetailReadService;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 电子面单查询物流详情接口服务
 * <AUTHOR>
 */
@HSFProvider(serviceInterface = WaybillLDQueryService.class)
public class WaybillLDQueryServiceImpl implements WaybillLDQueryService {

    @Resource
    private LogisticsDetailReadService logisticsDetailReadService;

    @Resource
    private LocReadService locReadService;

    private LogisticsDetailQueryOptionDO getOption(){
        //接口访问权限
        LogisticsDetailQueryOptionDO option = new LogisticsDetailQueryOptionDO();
        option.setAppName("WAYBILL-BRIDGE");
        option.setActor("RECEIVER");
        option.setShowOutData(true);
        return option;
    }

    @Override
    public List<LogisticsDetailDO> queryWaybillLogisticsDetail(String mailNo, String cpCode) {

        LogisticsDetailQueryOptionDO option = getOption();
        SingleResult<List<LogisticsDetailDO>> ldResult =
                logisticsDetailReadService.queryDetailListByMailNo(mailNo,cpCode,option);
        if(ldResult == null || !ldResult.isSuccess() || CollectionUtils.isEmpty(ldResult.getResult())){
            return null;
        }
        return ldResult.getResult();

    }

    @Override
    public List<String> queryMailNoListByOrderId(Long orderId) {

        LogisticsDetailQueryOptionDO option = getOption();
        SingleResult<List<LogisticsDetailDO>> result = logisticsDetailReadService.queryDetailListByTradeId(orderId,option);
        if(result == null || !result.isSuccess() || CollectionUtils.isEmpty(result.getResult())){
            return null;
        }
        List<String> mailNoList = result.getResult().stream()
                .map(LogisticsDetailDO::getMailNo)
                .distinct()
                .collect(Collectors.toList());
        return mailNoList;
    }

    @Override
    public LocOrderResponse queryLocOrderByTradeId(Long sellerId, Long tradeId) {

        LocQueryOptionDO locQueryOptionDO = new LocQueryOptionDO();
        locQueryOptionDO.setShowOrderGoods(true);
        SingleResultDO<List<LocOrderDO>> resultDO = locReadService.queryLogisticsOrderByTradeId(sellerId, tradeId,
                locQueryOptionDO, BridgeConstants.System.APP_NAME);
        return setLocOrderResponse(resultDO);
    }

    @Override
    public LocOrderResponse queryLocOrderByMailNo(String mailNo) {

        LocQueryOptionDO locQueryOptionDO = new LocQueryOptionDO();
        locQueryOptionDO.setShowOrderGoods(true);
        SingleResultDO<List<LocOrderDO>> resultDO =
                locReadService.queryLogisticsOrderByMailNo(mailNo, 1, locQueryOptionDO, BridgeConstants.System.APP_NAME);
        return setLocOrderResponse(resultDO);

    }

    private LocOrderResponse setLocOrderResponse(SingleResultDO<List<LocOrderDO>> resultDO){
        LocOrderResponse response = new LocOrderResponse();
        if (resultDO.isSuccess() && !CollectionUtils.isEmpty(resultDO.getResult())) {
            LocOrderDO locOrderDO = resultDO.getResult().get(0);
            OrderDO orderDO = locOrderDO.getOrderDO();
            response.setTradeId(orderDO.getTaobaoTradeId());
            response.setTradeSystem(orderDO.getFeature("tradeSystem"));
            response.setSellerId(orderDO.getUserId());
            response.setSellerNick(orderDO.getFeature("seller_nick"));
            response.setShopName(orderDO.getFeature2("shop_name"));
            response.setReceiverMobilePhone(orderDO.getReceiverMobilePhone());
            response.setReceiverDetailAddress(orderDO.getReceiverDetailAddress());
            response.setPrivacy(orderDO.getFeature("privacy"));
        }
        return response;
    }

}
