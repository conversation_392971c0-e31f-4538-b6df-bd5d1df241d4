package com.cainiao.waybill.bridge.web.pickup.ticket.role;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.common.user.WaybillBridgeUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

/**
 * <AUTHOR>
 * @date 2022/4/22 下午12:01
 */
@Configuration
public class CheckRoleConfig extends WebMvcConfigurerAdapter {

    @Resource
    private WaybillBridgeUserInfoService userInfoService;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        // 注册自定义的拦截器passwordStateInterceptor
        CheckRoleInterceptor checkRoleInterceptor = new CheckRoleInterceptor();
        registry.addInterceptor(checkRoleInterceptor).addPathPatterns("/ticket/**",
            "/admin/workOrderManage/workOrderManage", "admin/monitorData/monitorData", "/platform/order/**", "/cp/order/**")
                .excludePathPatterns("/ticket/userRole");

        // 注册自定义的拦截器passwordStateInterceptor
        CheckPickUpTicketRoleInterceptor checkPickUpTicketRoleInterceptor  = new CheckPickUpTicketRoleInterceptor();
        checkPickUpTicketRoleInterceptor.setUserInfoService(userInfoService);
        registry.addInterceptor(checkPickUpTicketRoleInterceptor).addPathPatterns("/pickup/**","/quote/**","/bill/**", "/fast/shipment/order/**");
    }
}
