package com.cainiao.waybill.bridge.model.dao;

import com.cainiao.waybill.bridge.model.domain.WaybillMobileVerificationCodeDO;
import com.taobao.common.dao.persistence.exception.DAOException;

/**
 *
 * 短信验证码信息
 *
 * Created by shouyuan.lzl on 2017-04-18 4:03 PM.
 */
public interface WaybillMobileVerificationCodeDAO {

    /**
     * 添加验证码
     *
     * @param waybillMobileVerificationCodeDO
     * @return
     * @throws DAOException
     */
    Integer addVerificationCode(WaybillMobileVerificationCodeDO waybillMobileVerificationCodeDO)  throws DAOException ;

    /**
     * 查询验证码
     *      有可能多条，取最新的一条
     * @param sellerId
     * @param mobile
     * @param verificationType
     * @return
     * @throws DAOException
     */
    WaybillMobileVerificationCodeDO queryVerificationCode(long sellerId, String mobile, int verificationType) throws DAOException ;

    /**
     * 获取某个时间验证码发送次数，有count（smsSendCount）的过程
     *
     * @param sellerId
     * @param mobile
     * @param verificationType
     * @param gmtCreate 日期部分  yyyy-MM-dd
     * @return
     * @throws DAOException
     */
    int countVerificationCode(long sellerId, String mobile, int verificationType, String gmtCreate) throws DAOException ;

    /**
     * 获取某个时间验证码发送次数，有count（smsSendCount）的过程
     * @param sellerId
     * @param verificationType
     * @param gmtCreate 日期部分  yyyy-MM-dd
     * @return
     * @throws DAOException
     */
    int countVerificationCode(long sellerId, int verificationType, String gmtCreate) throws  DAOException ;

    /**
     *
     * 记录验证code
     *
     * @param id  验证码数据的自动递增主键
     * @param checkSuccess  true 正确校验； false 错误校验
     * @return
     * @throws DAOException
     */
    boolean verifyVerificationCode(long id, boolean checkSuccess) throws DAOException ;

    /**
     * 验证码发送次数加一
     * @param id
     * @return
     * @throws DAOException
     */
    boolean smsSendCountPlusOne(long id) throws DAOException ;
}
