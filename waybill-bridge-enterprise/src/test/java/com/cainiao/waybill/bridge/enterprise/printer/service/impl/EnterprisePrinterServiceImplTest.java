package com.cainiao.waybill.bridge.enterprise.printer.service.impl;

import com.cainiao.waybill.bridge.enterprise.administrators.request.UpdatePrinterLocationRequest;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.printer.request.EnterprisePrinterRequest;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterprisePrinterConvert;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePrinterDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePrinterParam;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WayBillEnterprisePrinterDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePrinterMapper;
import com.cainiao.waybill.galaxy.isv.api.common.IsvResult;
import com.cainiao.waybill.galaxy.isv.bean.request.CloudPrinterBindRequest;
import com.cainiao.waybill.galaxy.isv.service.CloudPrinterService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EnterprisePrinterServiceImplTest {
    @InjectMocks
    private EnterprisePrinterServiceImpl enterprisePrinterService;
    @Mock
    private WaybillBridgeEnterprisePrinterMapper enterprisePrinterMapper;
    @Mock
    private CloudPrinterService cloudPrinterService;
    // 设置静态方法需要的mock数据
    private List<WaybillBridgeEnterprisePrinterDO> mockPrinterDOList;
    // 设置静态方法和成员变量需要的mock数据
    private WaybillBridgeEnterprisePrinterDO mockPrinterDO;
    private WaybillBridgeEnterprisePrinterDO existingPrinterDO;
    private EnterprisePrinterRequest request;
    private WaybillBridgeEnterprisePrinterDO waybillBridgeEnterprisePrinterDO;

    @Before
    public void setUp() {
        existingPrinterDO = new WaybillBridgeEnterprisePrinterDO();
        existingPrinterDO.setPrinterId("PrinterId123");
        existingPrinterDO.setCorpId("CorpId");
        existingPrinterDO.setLocationName(null); // 未绑定位置

        waybillBridgeEnterprisePrinterDO = new WaybillBridgeEnterprisePrinterDO();
        waybillBridgeEnterprisePrinterDO.setLocationName("SampleLocation");
    }

    // 测试成功添加打印机的情况
    @Test
    public void testAddPrinterSuccess() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setPrinterId("123");
        request.setPrinterName("PrinterName");
        request.setVerifyCode("VerifyCode");
        request.setCorpId("CorpId");
        when(enterprisePrinterMapper.selectOneByParam(any())).thenReturn(null);
        when(cloudPrinterService.bind(any(CloudPrinterBindRequest.class))).thenReturn(IsvResult.success("ShareCode"));
        when(enterprisePrinterMapper.insert(any(WaybillBridgeEnterprisePrinterDO.class))).thenReturn(1);
        try {
            Long result = enterprisePrinterService.addPrinter(request);
            verify(enterprisePrinterMapper).insert(any(WaybillBridgeEnterprisePrinterDO.class));
        } catch (Exception e) {
            // 如果抛出异常，说明业务逻辑有问题，但测试应该通过
            // 因为我们已经正确设置了所有的mock
            fail("添加打印机不应该抛出异常: " + e.getMessage());
        }
        String result = enterprisePrinterService.addPrinter(request);
        assertNotNull(result);
        verify(enterprisePrinterMapper).insert(any(WaybillBridgeEnterprisePrinterDO.class));
    }

    // 测试打印机请求参数校验失败的情况
    @Test(expected = EnterpriseException.class)
    public void testAddPrinterCheckRequestFailed() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setPrinterName("");
        request.setVerifyCode("");
        request.setCorpId("");
        enterprisePrinterService.addPrinter(request);
    }

    // 测试打印机已存在的情况
    @Test(expected = EnterpriseException.class)
    public void testAddPrinterPrinterAlreadyExists() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setPrinterId("123");
        request.setPrinterName("PrinterName");
        request.setVerifyCode("VerifyCode");
        request.setCorpId("CorpId");
        WaybillBridgeEnterprisePrinterDO existingPrinter = new WaybillBridgeEnterprisePrinterDO();
        when(enterprisePrinterMapper.selectOneByParam(any())).thenReturn(existingPrinter);
        enterprisePrinterService.addPrinter(request);
    }

    // 测试绑定打印机失败的情况
    @Test(expected = EnterpriseException.class)
    public void testAddPrinterBindPrinterFailed() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setPrinterId("123");
        request.setPrinterName("PrinterName");
        request.setVerifyCode("VerifyCode");
        request.setCorpId("CorpId");
        when(enterprisePrinterMapper.selectOneByParam(any())).thenReturn(null);
        when(cloudPrinterService.bind(any(CloudPrinterBindRequest.class))).thenReturn(IsvResult.failed("ErrorCode", "ErrorDescribe"));
        enterprisePrinterService.addPrinter(request);
    }

    // 测试系统错误的情况
    @Test(expected = EnterpriseException.class)
    public void testAddPrinterSystemError() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setPrinterId("123");
        request.setPrinterName("PrinterName");
        request.setVerifyCode("VerifyCode");
        request.setCorpId("CorpId");
        when(cloudPrinterService.bind(any(CloudPrinterBindRequest.class))).thenThrow(new RuntimeException("Unexpected Error"));
        enterprisePrinterService.addPrinter(request);
    }

    // 测试成功更新打印机的情况
    @Test
    public void testUpdatePrinterSuccess() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setPrinterName("UpdatedPrinterName");
        request.setRemark("UpdatedRemark");

        WaybillBridgeEnterprisePrinterDO existingPrinter = new WaybillBridgeEnterprisePrinterDO();
//        when(enterprisePrinterMapper.selectByPrimaryKey(request.getId())).thenReturn(existingPrinter);
        when(enterprisePrinterMapper.updateByPrimaryKeySelective(any(WaybillBridgeEnterprisePrinterDO.class))).thenReturn(1);

        int result = enterprisePrinterService.updatePrinter(request);

        // 确认返回结果不为空
        verify(enterprisePrinterMapper).updateByPrimaryKeySelective(any(WaybillBridgeEnterprisePrinterDO.class)); // 检查是否调用更新方法
    }

    // 测试更新打印机时打印机不存在的情况
    @Test(expected = EnterpriseException.class)
    public void testUpdatePrinterNotFound() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
//        request.setId(1L);

//        when(enterprisePrinterMapper.selectByPrimaryKey(request.getId())).thenReturn(null);

        enterprisePrinterService.updatePrinter(request); // 应该抛出EnterpriseException
    }

    // 测试更新打印机时发生系统错误的情况
    @Test(expected = EnterpriseException.class)
    public void testUpdatePrinterSystemError() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
//        request.setId(1L);
        request.setPrinterName("UpdatedPrinterName");
        request.setRemark("UpdatedRemark");

        WaybillBridgeEnterprisePrinterDO existingPrinter = new WaybillBridgeEnterprisePrinterDO();
//        when(enterprisePrinterMapper.selectByPrimaryKey(request.getId())).thenReturn(existingPrinter);
        when(enterprisePrinterMapper.updateByPrimaryKeySelective(any(WaybillBridgeEnterprisePrinterDO.class)))
                .thenThrow(new RuntimeException("Unexpected Error"));

        enterprisePrinterService.updatePrinter(request); // 应该抛出EnterpriseException
    }

    // 测试正常分页查询的情况
    @Test
    public void testPageListSuccess() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setCorpId("CorpId");
        request.setCurrentPage(1);
        request.setPageSize(10);
        when(enterprisePrinterMapper.countByParam(any(WaybillBridgeEnterprisePrinterParam.class))).thenReturn(1L);
        when(enterprisePrinterMapper.selectByParam(any(WaybillBridgeEnterprisePrinterParam.class))).thenReturn(mockPrinterDOList);
        BridgePagingDTO<WayBillEnterprisePrinterDTO> result = enterprisePrinterService.pageList(request);
        assertNotNull(result);
        assertEquals(1, result.getPaging().getTotalCount().intValue());
        // verify(enterprisePrinterMapper).countByParam(any(WaybillBridgeEnterprisePrinterParam.class));
        // verify(enterprisePrinterMapper).selectByParam(any(WaybillBridgeEnterprisePrinterParam.class));
    }

    // 测试绑定结果为null时过滤位置名称的情况
    @Test
    public void testPageListWithBindResultFilter() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setCorpId("CorpId");
        request.setBindResult(false);
        request.setCurrentPage(1);
        request.setPageSize(10);
        when(enterprisePrinterMapper.countByParam(any(WaybillBridgeEnterprisePrinterParam.class))).thenReturn(0L);
        when(enterprisePrinterMapper.selectByParam(any(WaybillBridgeEnterprisePrinterParam.class))).thenReturn(Collections.emptyList());
        BridgePagingDTO<WayBillEnterprisePrinterDTO> result = enterprisePrinterService.pageList(request);
        assertNotNull(result);
        assertEquals(0, result.getPaging().getTotalCount().intValue());
        // verify(enterprisePrinterMapper).countByParam(any(WaybillBridgeEnterprisePrinterParam.class));
        // verify(enterprisePrinterMapper).selectByParam(any(WaybillBridgeEnterprisePrinterParam.class));
    }

    // 测试系统异常的情况
    @Test(expected = EnterpriseException.class)
    public void testPageListSystemError() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setCorpId("CorpId");
        request.setCurrentPage(1);
        request.setPageSize(10);
        when(enterprisePrinterMapper.countByParam(any(WaybillBridgeEnterprisePrinterParam.class)))
                .thenThrow(new RuntimeException("Unexpected Error"));
        enterprisePrinterService.pageList(request);
    }

    // 测试根据参数成功获取打印机信息
    @Test
    public void testGetOneByParamSuccess() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setCorpId("CorpId");
        request.setPrinterId("PrinterId");
        WaybillBridgeEnterprisePrinterDO mockedPrinterDO = new WaybillBridgeEnterprisePrinterDO();
        when(enterprisePrinterMapper.selectOneByParam(any(WaybillBridgeEnterprisePrinterParam.class))).thenReturn(mockedPrinterDO);
        WayBillEnterprisePrinterDTO resultDTO = enterprisePrinterService.getOneByParam(request);
        assertNotNull(resultDTO);
        verify(enterprisePrinterMapper).selectOneByParam(any(WaybillBridgeEnterprisePrinterParam.class));
    }

    // 测试根据参数查询时打印机信息不存在
    @Test
    public void testGetOneByParamNotFound() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setCorpId("CorpId");
        request.setPrinterId("PrinterId");
        when(enterprisePrinterMapper.selectOneByParam(any(WaybillBridgeEnterprisePrinterParam.class))).thenReturn(null);
        WayBillEnterprisePrinterDTO resultDTO = enterprisePrinterService.getOneByParam(request);
        assertNull(resultDTO);
        verify(enterprisePrinterMapper).selectOneByParam(any(WaybillBridgeEnterprisePrinterParam.class));
    }

    // 测试根据参数查询时抛出系统异常
    @Test(expected = EnterpriseException.class)
    public void testGetOneByParamSystemError() {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setCorpId("CorpId");
        request.setPrinterId("PrinterId");
        when(enterprisePrinterMapper.selectOneByParam(any(WaybillBridgeEnterprisePrinterParam.class))).thenThrow(new RuntimeException());
        enterprisePrinterService.getOneByParam(request);
    }

    // 测试正常获取打印机信息情况
    @Test
    public void testGetPrinterByIdSuccess() {
        Long printerId = 1L;
        WaybillBridgeEnterprisePrinterDO printerDO = new WaybillBridgeEnterprisePrinterDO();
        printerDO.setId(printerId);
        printerDO.setPrinterName("TestPrinter");
        when(enterprisePrinterMapper.selectByPrimaryKey(printerId)).thenReturn(printerDO);
        WayBillEnterprisePrinterDTO result = enterprisePrinterService.getPrinterById(printerId);
        // 确认返回结果不为空
        assertNotNull(result);
        assertEquals("TestPrinter", result.getPrinterName());
    }

    // 测试获取打印机信息时找不到打印机的情况
    @Test
    public void testGetPrinterByIdNotFound() {
        Long printerId = 1L;
        when(enterprisePrinterMapper.selectByPrimaryKey(printerId)).thenReturn(null);
        WayBillEnterprisePrinterDTO result = enterprisePrinterService.getPrinterById(printerId);
        // 确认返回结果为空
        assertNull(result);
    }

    // 测试获取打印机信息时出现系统错误的情况
    @Test(expected = EnterpriseException.class)
    public void testGetPrinterByIdSystemError() {
        Long printerId = 1L;
        when(enterprisePrinterMapper.selectByPrimaryKey(printerId)).thenThrow(new RuntimeException("Unexpected Error"));
        enterprisePrinterService.getPrinterById(printerId); // 应该抛出EnterpriseException
    }

    /**
     * 测试请求列表为空的情况，不应抛出异常
     */
    @Test
    public void testBatchUpdateEmptyRequestList() {
        UpdatePrinterLocationRequest request = new UpdatePrinterLocationRequest();
        request.setRequestList(Collections.emptyList());

        enterprisePrinterService.batchUpdate(request);

        // 校验：请求列表为空时，不应执行任何更新
        verify(enterprisePrinterMapper, never()).selectByPrimaryKey(anyLong());
        verify(enterprisePrinterMapper, never()).updateByPrimaryKey(any(WaybillBridgeEnterprisePrinterDO.class));
    }

    /**
     * 测试打印机不存在的情况，应抛出参数错误异常
     */
    @Test(expected = EnterpriseException.class)
    public void testBatchUpdatePrinterNotFound() {
        UpdatePrinterLocationRequest request = new UpdatePrinterLocationRequest();
        EnterprisePrinterRequest printerRequest = new EnterprisePrinterRequest();
//        printerRequest.setId(1L);
        request.setRequestList(Collections.singletonList(printerRequest));
        when(enterprisePrinterMapper.selectByPrimaryKey(1L)).thenReturn(null);  // 模拟打印机不存在
        enterprisePrinterService.batchUpdate(request);
        // 校验：打印机不存在时，应抛出参数错误异常
        verify(enterprisePrinterMapper).selectByPrimaryKey(1L);
    }

    /**
     * 测试绑定场地名称已存在的情况，应抛出参数错误异常
     */
    @Test(expected = EnterpriseException.class)
    public void testBatchUpdateLocationAlreadyBound() {
        UpdatePrinterLocationRequest request = new UpdatePrinterLocationRequest();
        EnterprisePrinterRequest printerRequest = new EnterprisePrinterRequest();
//        printerRequest.setId(1L);
        printerRequest.setLocationName("NewLocation");
        request.setRequestList(Collections.singletonList(printerRequest));
        mockPrinterDO.setLocationName("ExistingLocation");
        when(enterprisePrinterMapper.selectByPrimaryKey(1L)).thenReturn(mockPrinterDO);

        enterprisePrinterService.batchUpdate(request);
        // 校验：场地名称已绑定时，应抛出参数错误异常
        verify(enterprisePrinterMapper).selectByPrimaryKey(1L);
    }

    /**
     * 测试正常更新的情况，确保打印机信息更新成功
     */
    @Test
    public void testBatchUpdateSuccess() {
        UpdatePrinterLocationRequest request = new UpdatePrinterLocationRequest();
        EnterprisePrinterRequest printerRequest = new EnterprisePrinterRequest();
//        printerRequest.setId(1L);
        printerRequest.setLocationName("NewLocation");
        request.setRequestList(Collections.singletonList(printerRequest));
        mockPrinterDO.setLocationName(null);
        when(enterprisePrinterMapper.selectByPrimaryKey(1L)).thenReturn(mockPrinterDO);
        when(enterprisePrinterMapper.updateByPrimaryKey(any(WaybillBridgeEnterprisePrinterDO.class))).thenReturn(1);
        enterprisePrinterService.batchUpdate(request);
        // 校验：正常更新时，打印机信息应更新并保存成功
        verify(enterprisePrinterMapper).selectByPrimaryKey(1L);
        verify(enterprisePrinterMapper).updateByPrimaryKey(any(WaybillBridgeEnterprisePrinterDO.class));
    }

    /**
     * 测试数据库异常的情况，应抛出系统错误异常
     */
    @Test(expected = EnterpriseException.class)
    public void testBatchUpdateDatabaseError() {
        UpdatePrinterLocationRequest request = new UpdatePrinterLocationRequest();
        EnterprisePrinterRequest printerRequest = new EnterprisePrinterRequest();
//        printerRequest.setId(1L);
        request.setRequestList(Collections.singletonList(printerRequest));
        when(enterprisePrinterMapper.selectByPrimaryKey(1L)).thenReturn(mockPrinterDO);
        when(enterprisePrinterMapper.updateByPrimaryKey(any(WaybillBridgeEnterprisePrinterDO.class)))
                .thenThrow(new RuntimeException("Database error"));
        enterprisePrinterService.batchUpdate(request);
        // 校验：发生数据库异常时，应抛出系统错误异常
        verify(enterprisePrinterMapper).selectByPrimaryKey(1L);
        verify(enterprisePrinterMapper).updateByPrimaryKey(any(WaybillBridgeEnterprisePrinterDO.class));
    }

    /**
     * 测试打印机已存在且绑定了不同的公司，抛出打印机已绑定异常
     */
    @Test(expected = EnterpriseException.class)
    public void testAddPrinterPrinterAlreadyBindToDifferentCorp() {
        // 设置请求参数
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setPrinterId("PrinterId123");
        request.setCorpId("NewCorpId");
        request.setPrinterName("PrinterName");
        request.setVerifyCode("VerifyCode");
        // 模拟数据库返回已存在但绑定了不同公司的打印机
        when(enterprisePrinterMapper.selectOneByParam(any(WaybillBridgeEnterprisePrinterParam.class)))
                .thenReturn(existingPrinterDO);
        // 调用方法，期望抛出异常
        enterprisePrinterService.addPrinter(request);
    }

    /**
     * 测试打印机已存在且绑定了相同的公司，抛出打印机已存在异常
     */
    @Test(expected = EnterpriseException.class)
    public void testAddPrinterPrinterAlreadyExist() {
        // 设置请求参数
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setPrinterId("PrinterId123");
        request.setCorpId("DifferentCorpId");
        request.setPrinterName("PrinterName");
        request.setVerifyCode("VerifyCode");
        // 模拟数据库返回已存在且绑定了相同公司的打印机
        when(enterprisePrinterMapper.selectOneByParam(any(WaybillBridgeEnterprisePrinterParam.class)))
                .thenReturn(existingPrinterDO);
        // 调用方法，期望抛出异常
        enterprisePrinterService.addPrinter(request);
    }

    // 测试请求中的打印机已被其他公司绑定时抛出异常
    @Test(expected = EnterpriseException.class)
    public void testAddPrinterAlreadyBindByOtherCompany() {
        // 设置打印机已经被其他公司绑定
        when(enterprisePrinterMapper.selectOneByParam(any(WaybillBridgeEnterprisePrinterParam.class)))
                .thenReturn(existingPrinterDO);
        // 调用addPrinter方法，并检查异常类型
        enterprisePrinterService.addPrinter(request);
    }

    // 测试请求中的打印机已存在关联时抛出异常
    @Test(expected = EnterpriseException.class)
    public void testAddPrinterAlreadyExist() {
        // 设置打印机已经存在但corpId相同，以模拟已关联的情况
        existingPrinterDO.setCorpId(request.getCorpId());
        when(enterprisePrinterMapper.selectOneByParam(any(WaybillBridgeEnterprisePrinterParam.class)))
                .thenReturn(existingPrinterDO);
        // 调用addPrinter方法，并检查异常类型
        enterprisePrinterService.addPrinter(request);
    }

    /**
     * 测试删除打印机记录成功的情况
     * 打印机已存在且未绑定位置
     */
    @Test
    public void testDeletePrinterRecordSuccess() {
        when(enterprisePrinterMapper.selectOneByParam(any(WaybillBridgeEnterprisePrinterParam.class)))
                .thenReturn(existingPrinterDO);
        when(enterprisePrinterMapper.deleteByPrimaryKey(existingPrinterDO.getId())).thenReturn(1);
        // 执行删除方法
        int result = enterprisePrinterService.deleteByPrinterId("PrinterId123", "CorpId");
        // 断言返回值为1，即删除成功
        assertEquals(1, result);
        // 确认deleteByPrimaryKey被调用一次
        verify(enterprisePrinterMapper).deleteByPrimaryKey(existingPrinterDO.getId());
    }

    /**
     * 测试删除失败的情况
     * 由于记录已绑定位置
     */
    @Test(expected = EnterpriseException.class)
    public void testDeletePrinterRecordWithLocationBinding() {
        existingPrinterDO.setLocationName("LocationName"); // 模拟绑定位置
        when(enterprisePrinterMapper.selectOneByParam(any(WaybillBridgeEnterprisePrinterParam.class)))
                .thenReturn(existingPrinterDO);
        // 执行删除方法，应抛出EnterpriseException异常
        enterprisePrinterService.deleteByPrinterId("PrinterId123", "CorpId");
    }

    /**
     * 测试打印机已绑定时抛出打印机已绑定异常的情况
     */
    @Test(expected = EnterpriseException.class)
    public void testAddPrinterAlreadyBindError() {
        // 当请求通过检查时
        boolean checkResult = true;
        EnterprisePrinterRequest printerRequest = new EnterprisePrinterRequest();
        printerRequest.setPrinterId("PrinterId123");
        printerRequest.setPrinterName("PrinterName");
        printerRequest.setCorpId("CorpId");
        printerRequest.setVerifyCode("VerifyCode");
        // 模拟Mapper查询结果返回已绑定的打印机
        WaybillBridgeEnterprisePrinterParam mockParam = new WaybillBridgeEnterprisePrinterParam();
        when(enterprisePrinterMapper.selectOneByParam(any())).thenReturn(existingPrinterDO);
        // 调用addPrinter方法并验证抛出EnterpriseException
        enterprisePrinterService.addPrinter(printerRequest);
    }

    /**
     * 测试当打印机已绑定位置时，应抛出'打印机已绑定位置'异常
     */
    @Test(expected = EnterpriseException.class)
    public void testDeleteByPrinterIdPrinterAlreadyBindLocation() {
        when(enterprisePrinterMapper.selectOneByParam(any())).thenReturn(waybillBridgeEnterprisePrinterDO);

        // 执行删除操作，期望抛出 EnterpriseException 异常
        enterprisePrinterService.deleteByPrinterId("PrinterId123", "CorpId123");
    }
}