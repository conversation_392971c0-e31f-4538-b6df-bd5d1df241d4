package com.cainiao.waybill.bridge.enterprise.utils;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;

import com.alibaba.security.tenant.common.model.RiskResult;
import com.alibaba.security.tenant.common.service.RequestService;
import com.cainiao.waybill.bridge.biz.utils.BridgeAddressUtil;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.util.Exceptions;
import com.cainiao.waybill.bridge.enterprise.administrators.request.WaybillEnterpriseOrderRequest;

import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.common.middleware.EnterpriseSwitch;
import com.cainiao.waybill.bridge.model.dto.AddressInfo;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import com.taobao.kfc.client.lite.bean.KeywordMatchResult;
import com.taobao.kfc.client.lite.service.LiteMergeSearchService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * 内容风控校验
 * <AUTHOR>
 * @date 2024/7/5 17:05
 **/
@Component
public class EnterpriseContentRiskWrapper {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Resource
    private RequestService requestService;

    @Resource
    private LiteMergeSearchService liteMergeSearchService;

    /**
     * 记录风险等级关键词
     */
    private static final String RISK_LEVEL_KEY = "risklevel";



    /**
     * 风控校验 制裁
     * @param request
     * @throws BridgeBaseException
     */
    public void checkRisk(WaybillEnterpriseOrderRequest request) throws EnterpriseException {
        if(!EnterpriseSwitch.orderRiskCheck){
            return;
        }
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("key", request.getLocationId() + "#" + request.getOuterOrderCode());
            map.put("senderUserName", request.getSenderName());
            map.put("senderMobile", request.getSenderMobile());
            if(StringUtil.isBlank(request.getSenderMobile())){
                map.put("senderMobile", request.getSenderPhone());
            }
            AddressInfo senderAddressReq = request.getSenderAddress();
            AddressDTO sendAddress = AddressUtils.addressConvert(senderAddressReq);
            map.put("sendAddr", BridgeAddressUtil.buildAddress(sendAddress));
            map.put("sendNation", BridgeAddressUtil.CHINA);
            if(null != senderAddressReq){
                map.put("sendProvince", senderAddressReq.getProvince());
                map.put("sendCity", senderAddressReq.getCity());
                map.put("sendDistrict", senderAddressReq.getArea());
                map.put("sendStreet", senderAddressReq.getTown());
                map.put("sendEndAddr", senderAddressReq.getAddressDetail());
            }
            map.put("receiverUserName", request.getConsigneeName());
            map.put("receiverMobile", request.getConsigneeMobile());
            if(StringUtil.isBlank(request.getConsigneeMobile())){
                map.put("receiverMobile", request.getConsigneePhone());
            }
            AddressInfo recAddress = request.getConsigneeAddress();
            AddressDTO addressDTO = AddressUtils.addressConvert(recAddress);
            map.put("recAddr", BridgeAddressUtil.buildAddress(addressDTO));
            map.put("recNation", BridgeAddressUtil.CHINA);
            if(null != recAddress){
                map.put("recProvince", recAddress.getProvince());
                map.put("recCity", recAddress.getCity());
                map.put("recDistrict", recAddress.getArea());
                map.put("recStreet", recAddress.getTown());
                map.put("recEndAddr", recAddress.getAddressDetail());
            }
            RiskResult riskResult = requestService.invoke("cn_sanction_taiwaijijian_check", map);

            if (riskResult != null && Boolean.TRUE.equals(riskResult.getResult()) && null != riskResult.getCustomKVs()) {
                Object riskLevelObj = riskResult.getCustomKVs().get(RISK_LEVEL_KEY);
                if (null != riskLevelObj && StringUtils.equals(riskLevelObj.toString(), RiskLevelEnum.HIGH.getLevel())) {
                    LOGGER.info(request.getOuterOrderCode(), EnterpriseErrorEnum.RISK_CHECK_FAILED.describe());

                    throw new EnterpriseException(EnterpriseErrorEnum.RISK_CHECK_FAILED.code(), EnterpriseErrorEnum.RISK_CHECK_FAILED.describe());
                }
            }
        } catch (EnterpriseException enterpriseException) {
            throw enterpriseException;
        } catch (Throwable e) {
            LOGGER.info(request.getOuterOrderCode(), EnterpriseErrorEnum.RISK_CHECK_FAILED, e);


        }
    }

    /**
     * 风控校验 违禁词
     * @param request
     * @throws BridgeBaseException
     */
    public void checkRiskWord(WaybillEnterpriseOrderRequest request) throws BridgeBaseException {
        if(!EnterpriseSwitch.orderRiskCheck){
            return;
        }
        try{
            boolean match = false;
            // 校验姓名
            if(EnterpriseSwitch.riskCheckUserName){
                match = matchRiskContent(request.getSenderName());
                match = match || matchRiskContent(request.getConsigneeName());
            }

            // 校验地址
            if(EnterpriseSwitch.riskCheckAddress){
                match = match || matchRiskContent(BridgeAddressUtil.buildAddress(AddressUtils.addressConvert(request.getSenderAddress())));
                match = match || matchRiskContent(BridgeAddressUtil.buildAddress(AddressUtils.addressConvert(request.getConsigneeAddress())));
            }

            // 校验备注
            if(EnterpriseSwitch.riskCheckRemark){
                match = match || matchRiskContent(request.getRemark());
            }
            if(match){
                throw Exceptions.newBridgeBaseException("content_risk_check_error", "存在违禁词");
            }
        }catch (BridgeBaseException e){
            LOGGER.info(request.getOuterOrderCode(), EnterpriseErrorEnum.RISK_CHECK_FAILED, e.getErrorCode(),e);
            throw e;
        }catch (Throwable e){
            LOGGER.info(request.getOuterOrderCode(), EnterpriseErrorEnum.RISK_CHECK_FAILED,e);
        }

    }

    /**
     * 是否命中违禁词内容
     * @param content
     * @return
     */
    private boolean matchRiskContent(String content) {
        if(StringUtils.isBlank(content)){
            return false;
        }
        try{
            KeywordMatchResult result = liteMergeSearchService.search(content,
                EnterpriseSwitch.KFC_CONTENT_RISK_CHECK_PACKAGE_LIST);
            if(result != null && result.isMatch()){
                LOGGER.info("命中违禁词内容.content:{}, result:{}", content, JSON.toJSONString(result.getMatchedKeywords()));
                return true;
            }

        }catch (Exception e){
            LOGGER.error("内容违禁词风控检测失败.");
        }
        return false;
    }

    /**
     * 风控风险等级枚举
     */
    enum RiskLevelEnum {
        /**
         * 高风险
         */
        HIGH("high", "高风险"),

        /**
         * 中风险
         */
        MEDIUM("medium", "中风险"),

        ;

        private String level;

        private String desc;

        RiskLevelEnum(String level, String desc) {
            this.level = level;
            this.desc = desc;
        }

        public String getLevel() {
            return level;
        }

        public String getDesc() {
            return desc;
        }
    }
}
