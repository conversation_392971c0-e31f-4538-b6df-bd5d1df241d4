package com.cainiao.waybill.bridge.biz.link.consumer;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.TraceFeatureKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpCpEnum;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.common.waybill.constants.WaybillPickUpActionConstant;
import com.taobao.pac.client.sdk.dataobject.request.TRACEPUSH.ExtendField;
import com.taobao.pac.client.sdk.dataobject.request.TRACEPUSH.Trace;
import com.taobao.pac.client.sdk.dataobject.request.TRACEPUSH.TracesElement;
import com.taobao.pac.client.sdk.dataobject.response.TRACEPUSH.TracepushResponse;
import com.taobao.pac.client.sdk.dataobject.request.TRACEPUSH.TracepushRequest;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 支付宝逆向轨迹推送
 * <AUTHOR>
 * @date 2025/1/7 21:13
 **/
@Service
public class TmsOrderTracePushConsumer {

    @Resource
    private com.cainiao.link.consumer.LinkClient commonLinkClient;

    private static final String DEFAULT_TIME_ZONE = "+8";

    private static final String DEFAULT_SPLIT = "_";

    private static final String DEFAULT_SITE_NO = "JDL0001";

    private static final String DEFAULT_SITE_NAME = "京东网点";

    private static final String DEFAULT_FACILITY_NO = "JDL0002";

    private static final String DEFAULT_FACILITY_NAME = "京东分拨";

    /**
     *  回传IOT物流CP名称
     */
    private static final String IOT_CP_NAME = "IOT_CP_NAME";

    /**
     * 推送支付宝逆向轨迹
     * @param pickUpEvent
     * @return
     */
    public TracepushResponse tracePushLink(WaybillPickUpEvent pickUpEvent) {
        TracepushResponse response = new TracepushResponse();
        try{
            PickUpLogUtil.info("支付宝IOT运单轨迹推送into:{}", JSONObject.toJSONString(pickUpEvent));
            TracepushRequest request = new TracepushRequest();
            TracesElement tracesElement = new TracesElement();
            // 物流公司编号
            tracesElement.setLogisticProviderID(pickUpEvent.getCpCode());
            tracesElement.setMailNos(pickUpEvent.getMailNo());
            tracesElement.setTxLogisticID(pickUpEvent.getOuterOrderCode());

            // 轨迹信息
            Trace trace = buildTraceInfo(pickUpEvent);
            FacilityTypeEnum facilityTypeEnum = FacilityTypeEnum.SITE;
            String facilityNo = DEFAULT_SITE_NO;
            String facilityName = DEFAULT_SITE_NAME;

            if (pickUpEvent.getAction().equals(WaybillPickUpActionConstant.TRANSPORT)) {
                facilityTypeEnum = FacilityTypeEnum.TRANSFER;
                facilityNo = DEFAULT_FACILITY_NO;
                facilityName = DEFAULT_FACILITY_NAME;
            }
            // 轨迹详情
            setTrace(trace, facilityTypeEnum, facilityNo, facilityName,
                pickUpEvent.getOuterOrderCode() + DEFAULT_SPLIT + pickUpEvent.getAction());

            List<Trace> traces = new ArrayList<>();
            traces.add(trace);
            tracesElement.setTraces(traces);
            List<TracesElement> tracesList = new ArrayList<>();
            tracesList.add(tracesElement);
            request.setTracesList(tracesList);

            // 执行轨迹推送
            PickUpLogUtil.info("支付宝IOT运单轨迹推送入参:{}", JSONObject.toJSONString(request));
            response = commonLinkClient.execute(request);
            PickUpLogUtil.info("支付宝IOT运单轨迹推送出参,mailNo:{},response:{}", pickUpEvent.getMailNo(), JSONObject.toJSONString(response));
            return response;
        }catch (Throwable e) {
            response.setSuccess(false);
            response.setErrorCode(Error.SYSTEM_ERROR.getErrorCode());
            response.setErrorMsg(e.getMessage());
            PickUpLogUtil.errLog(pickUpEvent.getMailNo(), PickUpConstants.Action.IOT_TRACE_PUSH_EXCEPTION.name(), "IOT_TRACE_PUSH_EXCEPTION", e.getMessage(), ExceptionUtils.getFullStackTrace(e));
            return response;
        }

    }

    /**
     * 设置轨迹详情
     * @param trace 轨迹详情
     * @param facilityTypeEnum 站点类型
     * @param facilityNo  站点编号
     * @param facilityName 站点名称
     * @param outBizCode 外部业务单号
     */
    private void setTrace(Trace trace, FacilityTypeEnum facilityTypeEnum, String facilityNo,
        String facilityName, String outBizCode) {
        trace.setFacilityType(facilityTypeEnum.getType());
        trace.setFacilityNo(facilityNo);
        trace.setFacilityName(facilityName);
        trace.setOutBizCode(outBizCode);
    }

    /**
     * 获取各轨迹节点描述
     * @param pickUpEvent
     * @return
     */
    private Trace buildTraceInfo(WaybillPickUpEvent pickUpEvent) {
        // 轨迹描述
        String traceDesc;
        // 小件员姓名
        String acceptCourierName = "";
        // 小件员电话
        String acceptCourierMobile = "";
        // 重量
        String weight = "";
        // 事件/操作
        String action = "";

        // 扩展信息列表
        List<ExtendField> extendFields = new ArrayList<>();
        String extInfo = pickUpEvent.getExtraInfo();
        Map<String,String> extMap = new HashMap<>(16);
        if(StringUtils.isNotBlank(extInfo)){
            extMap = JSONObject.parseObject(extInfo, Map.class);
        }
        for(String key : extMap.keySet()){
            ExtendField extendField = new ExtendField();
            extendField.setKey(key);
            extendField.setValue(extMap.get(key));
            extendFields.add(extendField);
        }

        ExtendField extendField = new ExtendField();
        extendField.setKey(IOT_CP_NAME);
        extendField.setValue(PickUpCpEnum.getNameByCode(pickUpEvent.getCpCode()));
        extendFields.add(extendField);

        switch (pickUpEvent.getAction()) {
            case WaybillPickUpActionConstant.WAIT:
                traceDesc = StringUtils.isBlank(pickUpEvent.getLastActionDetail()) ? "待接单"
                    : pickUpEvent.getLastActionDetail();
                action = IotTraceActionEnum.TRSP_NEW.getAction();
                break;
            case WaybillPickUpActionConstant.ACCEPT:
                traceDesc = StringUtils.isBlank(pickUpEvent.getLastActionDetail()) ? "已接单"
                    : pickUpEvent.getLastActionDetail();
                action = IotTraceActionEnum.ORDER_RECEIVED.getAction();
                acceptCourierName = extMap.get(TraceFeatureKey.ACCEPT_COURIER_NAME);
                acceptCourierMobile = extMap.get(TraceFeatureKey.ACCEPT_COURIER_MOBILE);
                break;
            case WaybillPickUpActionConstant.FAIL:
                traceDesc = StringUtils.isBlank(pickUpEvent.getLastActionDetail()) ? "已取消"
                    : pickUpEvent.getLastActionDetail();
                action = IotTraceActionEnum.TMS_REJECT.getAction();
                break;
            case WaybillPickUpActionConstant.GOT:
                traceDesc = StringUtils.isBlank(pickUpEvent.getLastActionDetail()) ? "已揽收"
                    : (pickUpEvent.getLastActionDetail().contains("揽收") ? pickUpEvent.getLastActionDetail() : "已揽收" + pickUpEvent.getLastActionDetail());
                action = IotTraceActionEnum.TMS_ACCEPT.getAction();
                weight = extMap.get(TraceFeatureKey.WEIGHT);
                break;
            case WaybillPickUpActionConstant.TRANSPORT:
                traceDesc = StringUtils.isBlank(pickUpEvent.getLastActionDetail()) ? "运输中"
                    : pickUpEvent.getLastActionDetail();
                action = IotTraceActionEnum.TMS_TRANSPORT_ING.getAction();
                break;
            case WaybillPickUpActionConstant.DELIVERING:
                traceDesc = StringUtils.isBlank(pickUpEvent.getLastActionDetail()) ? "派送中"
                    : pickUpEvent.getLastActionDetail();
                action = IotTraceActionEnum.TMS_DELIVERING.getAction();
                break;
            case WaybillPickUpActionConstant.SIGN:
                traceDesc = StringUtils.isBlank(pickUpEvent.getLastActionDetail()) ? "已签收"
                    : pickUpEvent.getLastActionDetail();
                action = IotTraceActionEnum.TMS_SIGN.getAction();
                break;
            default:
                traceDesc = "实操动作";
        }

        Trace trace = new Trace();
        trace.setTime(DateUtils.dateToStr(new Date(pickUpEvent.getActionGmtModified())));
        trace.setTz(DEFAULT_TIME_ZONE);
        if(StringUtils.isNotBlank(acceptCourierName)){
            trace.setContacter(acceptCourierName);
        }
        if(StringUtils.isNotBlank(acceptCourierMobile)){
            trace.setContactPhone(acceptCourierMobile);
        }
        if(StringUtils.isNotBlank(weight)){
            trace.setWeight(weight);
        }
        trace.setAction(action);
        trace.setDesc(traceDesc);
        trace.setExtendFields(extendFields);
        return trace;
    }

    /**
     * 站点类型枚举
     */
    public enum FacilityTypeEnum{

        /**
         * 站点
         */
        SITE("1", "站点"),

        /**
         * 分拨中心
         */
        TRANSFER("2", "分拨中心"),

        ;

        private String type;
        private String desc;

        FacilityTypeEnum(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }
        public String getType() {
            return type;
        }
        public String getDesc() {
            return desc;
        }
    }

    /**
     * iot轨迹动作枚举
     */
    public enum IotTraceActionEnum{
        /**
         * 待配接单
         */
        TRSP_NEW("TRSP_NEW", "待配接单"),

        /**
         * 配接单
         */
        ORDER_RECEIVED("ORDER_RECEIVED", "配接单"),

        /**
         * 配揽收
         */
        TMS_ACCEPT("TMS_ACCEPT", "配揽收"),

        /**
         * 配揽收失败
         */
        TMS_REJECT("TMS_REJECT", "配揽收失败"),

        /**
         * 运输中
         */
        TMS_TRANSPORT_ING("TMS_TRANSPORT_ING", "运输中"),

        /**
         * 派送中
         */
        TMS_DELIVERING("TMS_DELIVERING", "派送中"),

        /**
         * 签收
         */
        TMS_SIGN("TMS_SIGN", "签收"),

        ;
        private String action;
        private String desc;

        IotTraceActionEnum(String action, String desc) {
            this.action = action;
            this.desc = desc;
        }
        public String getAction() {
            return action;
        }
        public String getDesc() {
            return desc;
        }


    }

}
