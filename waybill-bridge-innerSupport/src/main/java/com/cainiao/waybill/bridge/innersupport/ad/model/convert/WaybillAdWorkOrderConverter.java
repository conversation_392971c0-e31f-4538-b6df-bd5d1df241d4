package com.cainiao.waybill.bridge.innersupport.ad.model.convert;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder.WaybillAdWorkOrderCreateRequest;
import com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder.WaybillAdWorkOrderUpdateRequest;
import com.cainiao.waybill.bridge.innersupport.ad.model.domain.WaybillAdWorkOrderDO;
import com.cainiao.waybill.bridge.innersupport.ad.model.dto.WaybillAdWorkOrderDTO;
import com.cainiao.waybill.bridge.innersupport.ad.web.vo.WaybillAdWorkOrderVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.List;

/**
* 工单表
* WaybillAdWorkOrder
*/
@Mapper(componentModel = "spring")
public interface WaybillAdWorkOrderConverter {

    WaybillAdWorkOrderConverter INSTANCE = Mappers.getMapper(WaybillAdWorkOrderConverter.class);

    @Mappings({
            @Mapping(source = "scanCodeScreenshotLinks", target = "scanCodeScreenshotLinks", qualifiedByName = "listToJsonString"),
            @Mapping(source = "paymentScreenshotLinks", target = "paymentScreenshotLinks", qualifiedByName = "listToJsonString"),
            @Mapping(source = "refundScreenshotLinks", target = "refundScreenshotLinks", qualifiedByName = "listToJsonString"),
            @Mapping(source = "mailNo", target = "mailNo", qualifiedByName = "removeSpace"),
    })
    WaybillAdWorkOrderDTO convertRequest2DTO(WaybillAdWorkOrderCreateRequest request);

    @Mappings({
            @Mapping(source = "scanCodeScreenshotLinks", target = "scanCodeScreenshotLinks", qualifiedByName = "listToJsonString"),
            @Mapping(source = "paymentScreenshotLinks", target = "paymentScreenshotLinks", qualifiedByName = "listToJsonString"),
            @Mapping(source = "refundScreenshotLinks", target = "refundScreenshotLinks", qualifiedByName = "listToJsonString"),
            @Mapping(source = "mailNo", target = "mailNo", qualifiedByName = "removeSpace"),
    })
    WaybillAdWorkOrderDTO convertRequest2DTO(WaybillAdWorkOrderUpdateRequest request);

    @Mappings({
    })
    WaybillAdWorkOrderDTO convertDO2DTO(WaybillAdWorkOrderDO detailDO);

    @Mappings({
    })
    WaybillAdWorkOrderDO convertDTO2DO(WaybillAdWorkOrderDTO detailDTO);


    List<WaybillAdWorkOrderVO> convertDTOList2VOList(List<WaybillAdWorkOrderDTO> tableData);

    @Mappings({
            @Mapping(source = "scanCodeScreenshotLinks", target = "scanCodeScreenshotLinks", qualifiedByName = "jsonStringToList"),
            @Mapping(source = "paymentScreenshotLinks", target = "paymentScreenshotLinks", qualifiedByName = "jsonStringToList"),
            @Mapping(source = "refundScreenshotLinks", target = "refundScreenshotLinks", qualifiedByName = "jsonStringToList"),
    })
    WaybillAdWorkOrderVO convertDTO2VO(WaybillAdWorkOrderDTO dto);

    @Named("listToJsonString")
    default String listToJsonString(List<String> list) {
        return list == null  ? null : JSON.toJSONString(list);

    }

    @Named("removeSpace")
    default String removeSpace(String str) {
        return str == null  ? null : str.trim();
    }

    @Named("jsonStringToList")
    default List<String> jsonStringToList(String jsonString) {
        if (jsonString == null ) {
            return Collections.emptyList();
        }
        return JSON.parseArray(jsonString, String.class);
    }
}