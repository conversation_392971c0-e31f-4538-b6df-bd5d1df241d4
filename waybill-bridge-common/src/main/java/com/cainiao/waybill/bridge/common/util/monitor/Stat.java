package com.cainiao.waybill.bridge.common.util.monitor;

/**
 * <AUTHOR> on 2019-07-22.
 */
public interface Stat {
    /**
     * 通常用于计算 qps
     * count +1
     */
    void count();

    /**
     * 通常用于计算 qps
     * count + count
     */
    void count(long count);

    /**
     * 通常用于计算 rt
     * count + count,
     * value + value
     */
    void countAndSum(long count, long value);

    /**
     * 通常用于计算 rt
     * value + value
     */
    void countAndSum(long value);

    /**
     * 通常用于计算 平均rt，最长rt，最短rt
     * value
     */
    void countAndSumAndMinMax(long value);

    /**
     * 记录一段时间内的最大值和最小值
     *
     * @param value 要记录的值
     */
    void minMax(long value);
}
