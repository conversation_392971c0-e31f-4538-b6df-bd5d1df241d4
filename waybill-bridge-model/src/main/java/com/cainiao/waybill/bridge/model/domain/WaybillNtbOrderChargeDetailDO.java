package com.cainiao.waybill.bridge.model.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class WaybillNtbOrderChargeDetailDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   结算id
     *
     *
     * @mbg.generated
     */
    private String chargeId;

    /**
     * Database Column Remarks:
     *   结算记录创建时间
     *
     *
     * @mbg.generated
     */
    private String chargeCreateTime;

    /**
     * Database Column Remarks:
     *   结算记录修改时间
     *
     *
     * @mbg.generated
     */
    private String chargeModifiedTime;

    /**
     * Database Column Remarks:
     *   业务发生时间
     *
     *
     * @mbg.generated
     */
    private String bizTime;

    /**
     * Database Column Remarks:
     *   序列号
     *
     *
     * @mbg.generated
     */
    private String sn;

    /**
     * Database Column Remarks:
     *   业务实体值（LBX号、LP号等等）
     *
     *
     * @mbg.generated
     */
    private String entityValue;

    /**
     * Database Column Remarks:
     *   用户类型：0消费者 1商家 2CP
     *
     *
     * @mbg.generated
     */
    private String userType;

    /**
     * Database Column Remarks:
     *   用户id
     *
     *
     * @mbg.generated
     */
    private String userId;

    /**
     * Database Column Remarks:
     *   用户昵称
     *
     *
     * @mbg.generated
     */
    private String userNick;

    /**
     * Database Column Remarks:
     *   收入标识：0 支出，1 收入
     *
     *
     * @mbg.generated
     */
    private String incomeFlag;

    /**
     * Database Column Remarks:
     *   费用项编码
     *
     *
     * @mbg.generated
     */
    private String feeCode;

    /**
     * Database Column Remarks:
     *   费用项名称
     *
     *
     * @mbg.generated
     */
    private String feeName;

    /**
     * Database Column Remarks:
     *   结算币种
     *
     *
     * @mbg.generated
     */
    private String payCurrency;

    /**
     * Database Column Remarks:
     *   结算金额，单位元
     *
     *
     * @mbg.generated
     */
    private BigDecimal chargeAmount;

    /**
     * Database Column Remarks:
     *   结算状态(0:新建 1:创建汇总单 2:已创建支付单 5：支付成功 -2：支付失败 -1:计算失败 8:等待商家端支付成功)
     *
     *
     * @mbg.generated
     */
    private String chargeStatus;

    /**
     * Database Column Remarks:
     *   事件类型编码
     *
     *
     * @mbg.generated
     */
    private String eventTypeCode;

    /**
     * Database Column Remarks:
     *   结算标识(0:正常 1:调账)
     *
     *
     * @mbg.generated
     */
    private String accountFlag;

    /**
     * Database Column Remarks:
     *   结算周期:1-实时；2-日；3-半月
     *
     *
     * @mbg.generated
     */
    private String chargingPeriod;

    /**
     * Database Column Remarks:
     *   服务商品编码
     *
     *
     * @mbg.generated
     */
    private String serviceItemCode;

    /**
     * Database Column Remarks:
     *   服务商品名
     *
     *
     * @mbg.generated
     */
    private String serviceItemName;

    /**
     * Database Column Remarks:
     *   总重量
     *
     *
     * @mbg.generated
     */
    private BigDecimal weightSum;

    /**
     * Database Column Remarks:
     *   对账标志(1:成功,0:不成功)
     *
     *
     * @mbg.generated
     */
    private String accChrOrdOkay;

    /**
     * Database Column Remarks:
     *   支付单id
     *
     *
     * @mbg.generated
     */
    private String payId;

    /**
     * Database Column Remarks:
     *   支付记录创建时间
     *
     *
     * @mbg.generated
     */
    private String payCreateTime;

    /**
     * Database Column Remarks:
     *   支付记录修改时间
     *
     *
     * @mbg.generated
     */
    private String payModifiedTime;

    /**
     * Database Column Remarks:
     *   结算单变成可支付的时间（例如由8变成0、由-1变成0等，0表示可支付）
     *
     *
     * @mbg.generated
     */
    private String payableTime;

    /**
     * Database Column Remarks:
     *   支付宝扣款时间，支付成功才会有
     *
     *
     * @mbg.generated
     */
    private String alipayTime;

    /**
     * Database Column Remarks:
     *   支付类型(0:N/A 1:汇金支付 2:收银台支付 3:线下支付)
     *
     *
     * @mbg.generated
     */
    private String payType;

    /**
     * Database Column Remarks:
     *   支付业务类型
     *
     *
     * @mbg.generated
     */
    private String payBizType;

    /**
     * Database Column Remarks:
     *   付款人userid
     *
     *
     * @mbg.generated
     */
    private String payerUserId;

    /**
     * Database Column Remarks:
     *   付款人支付宝帐号
     *
     *
     * @mbg.generated
     */
    private String payerAccountNo;

    /**
     * Database Column Remarks:
     *   收款人userid
     *
     *
     * @mbg.generated
     */
    private String payeeUserId;

    /**
     * Database Column Remarks:
     *   收款人支付宝帐号
     *
     *
     * @mbg.generated
     */
    private String payeeAccountNo;

    /**
     * Database Column Remarks:
     *   支付状态：1、初始状态；2、申请支付成功；-2、申请支付失败；3、支付成功；-100、作废
     *
     *
     * @mbg.generated
     */
    private String payStatus;

    /**
     * Database Column Remarks:
     *   支付扩展信息
     *
     *
     * @mbg.generated
     */
    private String payFeature;

    /**
     * Database Column Remarks:
     *   实际支付币种
     *
     *
     * @mbg.generated
     */
    private String payActualCurrency;

    /**
     * Database Column Remarks:
     *   实际支付金额，单位元
     *
     *
     * @mbg.generated
     */
    private BigDecimal payActualAmount;

    /**
     * Database Column Remarks:
     *   结算模板
     *
     *
     * @mbg.generated
     */
    private Long calcTemplateId;

    /**
     * Database Column Remarks:
     *   计费模式编码
     *
     *
     * @mbg.generated
     */
    private String calcModeCode;

    /**
     * Database Column Remarks:
     *   报价ID列表
     *
     *
     * @mbg.generated
     */
    private String feePriceId;

    /**
     * Database Column Remarks:
     *   汇总单ID
     *
     *
     * @mbg.generated
     */
    private String chargeOrderSumId;

    /**
     * Database Column Remarks:
     *   服务编码
     *
     *
     * @mbg.generated
     */
    private String serviceCode;

    /**
     * Database Column Remarks:
     *   公式
     *
     *
     * @mbg.generated
     */
    private String formula;

    /**
     * Database Column Remarks:
     *   版本
     *
     *
     * @mbg.generated
     */
    private String version;

    /**
     * Database Column Remarks:
     *   外部订单号
     *
     *
     * @mbg.generated
     */
    private String externalBizNo;

    /**
     * Database Column Remarks:
     *   匹配费率时间
     *
     *
     * @mbg.generated
     */
    private String expectChargeTime;

    /**
     * Database Column Remarks:
     *   订购关系id
     *
     *
     * @mbg.generated
     */
    private Long subscribeId;

    /**
     * Database Column Remarks:
     *   物流商品id
     *
     *
     * @mbg.generated
     */
    private Long logisticAuctionsId;

    /**
     * Database Column Remarks:
     *   币种汇率，报价币种转结算币种的汇率
     *
     *
     * @mbg.generated
     */
    private BigDecimal currencyRate;

    /**
     * Database Column Remarks:
     *   结算方
     *
     *
     * @mbg.generated
     */
    private String appointedAccount;

    /**
     * Database Column Remarks:
     *   报价id
     *
     *
     * @mbg.generated
     */
    private Long priceId;

    /**
     * Database Column Remarks:
     *   报价版本id
     *
     *
     * @mbg.generated
     */
    private Long quotedPriceId;

    /**
     * Database Column Remarks:
     *   报价币种
     *
     *
     * @mbg.generated
     */
    private String quotedCurrency;

    /**
     * Database Column Remarks:
     *   计算金额(元)
     *
     *
     * @mbg.generated
     */
    private BigDecimal quotedAmount;

    /**
     * Database Column Remarks:
     *   物流商品名称
     *
     *
     * @mbg.generated
     */
    private String logisticAuctionsName;

    /**
     * Database Column Remarks:
     *   报价名称
     *
     *
     * @mbg.generated
     */
    private String priceName;

    /**
     * Database Column Remarks:
     *   结算账期序列
     *
     *
     * @mbg.generated
     */
    private String accountPeriodSequence;

    /**
     * Database Column Remarks:
     *   合同号
     *
     *
     * @mbg.generated
     */
    private String signNumber;

    /**
     * Database Column Remarks:
     *   计费单元
     *
     *
     * @mbg.generated
     */
    private String calcPattern;

    /**
     * Database Column Remarks:
     *   子帐单数量
     *
     *
     * @mbg.generated
     */
    private Long subOrderCnt;

    /**
     * Database Column Remarks:
     *   总体积
     *
     *
     * @mbg.generated
     */
    private BigDecimal volumeSum;

    /**
     * Database Column Remarks:
     *   原始参考价格1
     *
     *
     * @mbg.generated
     */
    private String orgCharge1;

    /**
     * Database Column Remarks:
     *   原始参考价格2
     *
     *
     * @mbg.generated
     */
    private String orgCharge2;

    /**
     * Database Column Remarks:
     *   支付回执业务号
     *
     *
     * @mbg.generated
     */
    private String payVoucherNo;

    /**
     * Database Column Remarks:
     *   账单id
     *
     *
     * @mbg.generated
     */
    private Long chargeBillId;

    /**
     * Database Column Remarks:
     *   账单状态： 1：新建; 2：支付中; -2：申请支付失败; 3：支付成功; -100：作废
     *
     *
     * @mbg.generated
     */
    private String billStatus;

    /**
     * Database Column Remarks:
     *   是否支付成功（Y是N否）
     *
     *
     * @mbg.generated
     */
    private String isPaySuccess;

    /**
     * Database Column Remarks:
     *   菜鸟主体
     *
     *
     * @mbg.generated
     */
    private String orgUnit;

    /**
     * Database Column Remarks:
     *   是否财务账单确认（Y是N否）
     *
     *
     * @mbg.generated
     */
    private String isFinBillConfirm;

    /**
     * Database Column Remarks:
     *   财务账单确认时间
     *
     *
     * @mbg.generated
     */
    private String finBillConfirmTime;

    /**
     * Database Column Remarks:
     *   结算金额（折人民币，单位元）
     *
     *
     * @mbg.generated
     */
    private BigDecimal chargeAmountRmb;

    /**
     * Database Column Remarks:
     *   核销时间
     *
     *
     * @mbg.generated
     */
    private String writeoffTime;

    /**
     * Database Column Remarks:
     *   费用类型（0正常 2赔付 3折让）
     *
     *
     * @mbg.generated
     */
    private String feeType;

    /**
     * Database Column Remarks:
     *   用户体系（1淘宝 2菜鸟）
     *
     *
     * @mbg.generated
     */
    private String userSystem;

    /**
     * Database Column Remarks:
     *   报价明细id
     *
     *
     * @mbg.generated
     */
    private Long priceDetailId;

    /**
     * Database Column Remarks:
     *   租户id
     *
     *
     * @mbg.generated
     */
    private Long tenantId;

    /**
     * Database Column Remarks:
     *   是否坏账
     *
     *
     * @mbg.generated
     */
    private String isBadDebt;

    /**
     * Database Column Remarks:
     *   运单号
     *
     *
     * @mbg.generated
     */
    private String mailNo;

    /**
     * Database Column Remarks:
     *   结算分区字段：业务日期
     *
     *
     * @mbg.generated
     */
    private String ds;

    /**
     * Database Column Remarks:
     *   来源系统（acc结算3.0 cf结算4.0）
     *
     *
     * @mbg.generated
     */
    private String source;

    /**
     * Database Column Remarks:
     *   业务分类：whc 中小件，dj 大电大件，lst 零售通，ae 出口AE，thw 天猫出海，imp 跨境进口，other 其他
     *
     *
     * @mbg.generated
     */
    private String bizType;

    /**
     * Database Column Remarks:
     *   结算分区字段：1 商家，2 CP，other 其他
     *
     *
     * @mbg.generated
     */
    private String userClassify;

    /**
     * Database Column Remarks:
     *   结算记录扩展信息
     *
     *
     * @mbg.generated
     */
    private String chargeFeature;

    /**
     * Database Column Remarks:
     *   charge参数
     *
     *
     * @mbg.generated
     */
    private String chargeParams;

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_ntb_order_charge_detail.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_ntb_order_charge_detail.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_ntb_order_charge_detail.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.charge_id
     *
     * @mbg.generated
     */
    public String getChargeId() {
        return chargeId;
    }

    /**
     *
     * @param chargeId the value for waybill_ntb_order_charge_detail.charge_id
     *
     * @mbg.generated
     */
    public void setChargeId(String chargeId) {
        this.chargeId = chargeId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.charge_create_time
     *
     * @mbg.generated
     */
    public String getChargeCreateTime() {
        return chargeCreateTime;
    }

    /**
     *
     * @param chargeCreateTime the value for waybill_ntb_order_charge_detail.charge_create_time
     *
     * @mbg.generated
     */
    public void setChargeCreateTime(String chargeCreateTime) {
        this.chargeCreateTime = chargeCreateTime;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.charge_modified_time
     *
     * @mbg.generated
     */
    public String getChargeModifiedTime() {
        return chargeModifiedTime;
    }

    /**
     *
     * @param chargeModifiedTime the value for waybill_ntb_order_charge_detail.charge_modified_time
     *
     * @mbg.generated
     */
    public void setChargeModifiedTime(String chargeModifiedTime) {
        this.chargeModifiedTime = chargeModifiedTime;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.biz_time
     *
     * @mbg.generated
     */
    public String getBizTime() {
        return bizTime;
    }

    /**
     *
     * @param bizTime the value for waybill_ntb_order_charge_detail.biz_time
     *
     * @mbg.generated
     */
    public void setBizTime(String bizTime) {
        this.bizTime = bizTime;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.sn
     *
     * @mbg.generated
     */
    public String getSn() {
        return sn;
    }

    /**
     *
     * @param sn the value for waybill_ntb_order_charge_detail.sn
     *
     * @mbg.generated
     */
    public void setSn(String sn) {
        this.sn = sn;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.entity_value
     *
     * @mbg.generated
     */
    public String getEntityValue() {
        return entityValue;
    }

    /**
     *
     * @param entityValue the value for waybill_ntb_order_charge_detail.entity_value
     *
     * @mbg.generated
     */
    public void setEntityValue(String entityValue) {
        this.entityValue = entityValue;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.user_type
     *
     * @mbg.generated
     */
    public String getUserType() {
        return userType;
    }

    /**
     *
     * @param userType the value for waybill_ntb_order_charge_detail.user_type
     *
     * @mbg.generated
     */
    public void setUserType(String userType) {
        this.userType = userType;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.user_id
     *
     * @mbg.generated
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @param userId the value for waybill_ntb_order_charge_detail.user_id
     *
     * @mbg.generated
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.user_nick
     *
     * @mbg.generated
     */
    public String getUserNick() {
        return userNick;
    }

    /**
     *
     * @param userNick the value for waybill_ntb_order_charge_detail.user_nick
     *
     * @mbg.generated
     */
    public void setUserNick(String userNick) {
        this.userNick = userNick;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.income_flag
     *
     * @mbg.generated
     */
    public String getIncomeFlag() {
        return incomeFlag;
    }

    /**
     *
     * @param incomeFlag the value for waybill_ntb_order_charge_detail.income_flag
     *
     * @mbg.generated
     */
    public void setIncomeFlag(String incomeFlag) {
        this.incomeFlag = incomeFlag;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.fee_code
     *
     * @mbg.generated
     */
    public String getFeeCode() {
        return feeCode;
    }

    /**
     *
     * @param feeCode the value for waybill_ntb_order_charge_detail.fee_code
     *
     * @mbg.generated
     */
    public void setFeeCode(String feeCode) {
        this.feeCode = feeCode;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.fee_name
     *
     * @mbg.generated
     */
    public String getFeeName() {
        return feeName;
    }

    /**
     *
     * @param feeName the value for waybill_ntb_order_charge_detail.fee_name
     *
     * @mbg.generated
     */
    public void setFeeName(String feeName) {
        this.feeName = feeName;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.pay_currency
     *
     * @mbg.generated
     */
    public String getPayCurrency() {
        return payCurrency;
    }

    /**
     *
     * @param payCurrency the value for waybill_ntb_order_charge_detail.pay_currency
     *
     * @mbg.generated
     */
    public void setPayCurrency(String payCurrency) {
        this.payCurrency = payCurrency;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.charge_amount
     *
     * @mbg.generated
     */
    public BigDecimal getChargeAmount() {
        return chargeAmount;
    }

    /**
     *
     * @param chargeAmount the value for waybill_ntb_order_charge_detail.charge_amount
     *
     * @mbg.generated
     */
    public void setChargeAmount(BigDecimal chargeAmount) {
        this.chargeAmount = chargeAmount;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.charge_status
     *
     * @mbg.generated
     */
    public String getChargeStatus() {
        return chargeStatus;
    }

    /**
     *
     * @param chargeStatus the value for waybill_ntb_order_charge_detail.charge_status
     *
     * @mbg.generated
     */
    public void setChargeStatus(String chargeStatus) {
        this.chargeStatus = chargeStatus;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.event_type_code
     *
     * @mbg.generated
     */
    public String getEventTypeCode() {
        return eventTypeCode;
    }

    /**
     *
     * @param eventTypeCode the value for waybill_ntb_order_charge_detail.event_type_code
     *
     * @mbg.generated
     */
    public void setEventTypeCode(String eventTypeCode) {
        this.eventTypeCode = eventTypeCode;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.account_flag
     *
     * @mbg.generated
     */
    public String getAccountFlag() {
        return accountFlag;
    }

    /**
     *
     * @param accountFlag the value for waybill_ntb_order_charge_detail.account_flag
     *
     * @mbg.generated
     */
    public void setAccountFlag(String accountFlag) {
        this.accountFlag = accountFlag;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.charging_period
     *
     * @mbg.generated
     */
    public String getChargingPeriod() {
        return chargingPeriod;
    }

    /**
     *
     * @param chargingPeriod the value for waybill_ntb_order_charge_detail.charging_period
     *
     * @mbg.generated
     */
    public void setChargingPeriod(String chargingPeriod) {
        this.chargingPeriod = chargingPeriod;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.service_item_code
     *
     * @mbg.generated
     */
    public String getServiceItemCode() {
        return serviceItemCode;
    }

    /**
     *
     * @param serviceItemCode the value for waybill_ntb_order_charge_detail.service_item_code
     *
     * @mbg.generated
     */
    public void setServiceItemCode(String serviceItemCode) {
        this.serviceItemCode = serviceItemCode;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.service_item_name
     *
     * @mbg.generated
     */
    public String getServiceItemName() {
        return serviceItemName;
    }

    /**
     *
     * @param serviceItemName the value for waybill_ntb_order_charge_detail.service_item_name
     *
     * @mbg.generated
     */
    public void setServiceItemName(String serviceItemName) {
        this.serviceItemName = serviceItemName;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.weight_sum
     *
     * @mbg.generated
     */
    public BigDecimal getWeightSum() {
        return weightSum;
    }

    /**
     *
     * @param weightSum the value for waybill_ntb_order_charge_detail.weight_sum
     *
     * @mbg.generated
     */
    public void setWeightSum(BigDecimal weightSum) {
        this.weightSum = weightSum;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.acc_chr_ord_okay
     *
     * @mbg.generated
     */
    public String getAccChrOrdOkay() {
        return accChrOrdOkay;
    }

    /**
     *
     * @param accChrOrdOkay the value for waybill_ntb_order_charge_detail.acc_chr_ord_okay
     *
     * @mbg.generated
     */
    public void setAccChrOrdOkay(String accChrOrdOkay) {
        this.accChrOrdOkay = accChrOrdOkay;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.pay_id
     *
     * @mbg.generated
     */
    public String getPayId() {
        return payId;
    }

    /**
     *
     * @param payId the value for waybill_ntb_order_charge_detail.pay_id
     *
     * @mbg.generated
     */
    public void setPayId(String payId) {
        this.payId = payId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.pay_create_time
     *
     * @mbg.generated
     */
    public String getPayCreateTime() {
        return payCreateTime;
    }

    /**
     *
     * @param payCreateTime the value for waybill_ntb_order_charge_detail.pay_create_time
     *
     * @mbg.generated
     */
    public void setPayCreateTime(String payCreateTime) {
        this.payCreateTime = payCreateTime;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.pay_modified_time
     *
     * @mbg.generated
     */
    public String getPayModifiedTime() {
        return payModifiedTime;
    }

    /**
     *
     * @param payModifiedTime the value for waybill_ntb_order_charge_detail.pay_modified_time
     *
     * @mbg.generated
     */
    public void setPayModifiedTime(String payModifiedTime) {
        this.payModifiedTime = payModifiedTime;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.payable_time
     *
     * @mbg.generated
     */
    public String getPayableTime() {
        return payableTime;
    }

    /**
     *
     * @param payableTime the value for waybill_ntb_order_charge_detail.payable_time
     *
     * @mbg.generated
     */
    public void setPayableTime(String payableTime) {
        this.payableTime = payableTime;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.alipay_time
     *
     * @mbg.generated
     */
    public String getAlipayTime() {
        return alipayTime;
    }

    /**
     *
     * @param alipayTime the value for waybill_ntb_order_charge_detail.alipay_time
     *
     * @mbg.generated
     */
    public void setAlipayTime(String alipayTime) {
        this.alipayTime = alipayTime;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.pay_type
     *
     * @mbg.generated
     */
    public String getPayType() {
        return payType;
    }

    /**
     *
     * @param payType the value for waybill_ntb_order_charge_detail.pay_type
     *
     * @mbg.generated
     */
    public void setPayType(String payType) {
        this.payType = payType;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.pay_biz_type
     *
     * @mbg.generated
     */
    public String getPayBizType() {
        return payBizType;
    }

    /**
     *
     * @param payBizType the value for waybill_ntb_order_charge_detail.pay_biz_type
     *
     * @mbg.generated
     */
    public void setPayBizType(String payBizType) {
        this.payBizType = payBizType;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.payer_user_id
     *
     * @mbg.generated
     */
    public String getPayerUserId() {
        return payerUserId;
    }

    /**
     *
     * @param payerUserId the value for waybill_ntb_order_charge_detail.payer_user_id
     *
     * @mbg.generated
     */
    public void setPayerUserId(String payerUserId) {
        this.payerUserId = payerUserId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.payer_account_no
     *
     * @mbg.generated
     */
    public String getPayerAccountNo() {
        return payerAccountNo;
    }

    /**
     *
     * @param payerAccountNo the value for waybill_ntb_order_charge_detail.payer_account_no
     *
     * @mbg.generated
     */
    public void setPayerAccountNo(String payerAccountNo) {
        this.payerAccountNo = payerAccountNo;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.payee_user_id
     *
     * @mbg.generated
     */
    public String getPayeeUserId() {
        return payeeUserId;
    }

    /**
     *
     * @param payeeUserId the value for waybill_ntb_order_charge_detail.payee_user_id
     *
     * @mbg.generated
     */
    public void setPayeeUserId(String payeeUserId) {
        this.payeeUserId = payeeUserId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.payee_account_no
     *
     * @mbg.generated
     */
    public String getPayeeAccountNo() {
        return payeeAccountNo;
    }

    /**
     *
     * @param payeeAccountNo the value for waybill_ntb_order_charge_detail.payee_account_no
     *
     * @mbg.generated
     */
    public void setPayeeAccountNo(String payeeAccountNo) {
        this.payeeAccountNo = payeeAccountNo;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.pay_status
     *
     * @mbg.generated
     */
    public String getPayStatus() {
        return payStatus;
    }

    /**
     *
     * @param payStatus the value for waybill_ntb_order_charge_detail.pay_status
     *
     * @mbg.generated
     */
    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.pay_feature
     *
     * @mbg.generated
     */
    public String getPayFeature() {
        return payFeature;
    }

    /**
     *
     * @param payFeature the value for waybill_ntb_order_charge_detail.pay_feature
     *
     * @mbg.generated
     */
    public void setPayFeature(String payFeature) {
        this.payFeature = payFeature;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.pay_actual_currency
     *
     * @mbg.generated
     */
    public String getPayActualCurrency() {
        return payActualCurrency;
    }

    /**
     *
     * @param payActualCurrency the value for waybill_ntb_order_charge_detail.pay_actual_currency
     *
     * @mbg.generated
     */
    public void setPayActualCurrency(String payActualCurrency) {
        this.payActualCurrency = payActualCurrency;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.pay_actual_amount
     *
     * @mbg.generated
     */
    public BigDecimal getPayActualAmount() {
        return payActualAmount;
    }

    /**
     *
     * @param payActualAmount the value for waybill_ntb_order_charge_detail.pay_actual_amount
     *
     * @mbg.generated
     */
    public void setPayActualAmount(BigDecimal payActualAmount) {
        this.payActualAmount = payActualAmount;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.calc_template_id
     *
     * @mbg.generated
     */
    public Long getCalcTemplateId() {
        return calcTemplateId;
    }

    /**
     *
     * @param calcTemplateId the value for waybill_ntb_order_charge_detail.calc_template_id
     *
     * @mbg.generated
     */
    public void setCalcTemplateId(Long calcTemplateId) {
        this.calcTemplateId = calcTemplateId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.calc_mode_code
     *
     * @mbg.generated
     */
    public String getCalcModeCode() {
        return calcModeCode;
    }

    /**
     *
     * @param calcModeCode the value for waybill_ntb_order_charge_detail.calc_mode_code
     *
     * @mbg.generated
     */
    public void setCalcModeCode(String calcModeCode) {
        this.calcModeCode = calcModeCode;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.fee_price_id
     *
     * @mbg.generated
     */
    public String getFeePriceId() {
        return feePriceId;
    }

    /**
     *
     * @param feePriceId the value for waybill_ntb_order_charge_detail.fee_price_id
     *
     * @mbg.generated
     */
    public void setFeePriceId(String feePriceId) {
        this.feePriceId = feePriceId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.charge_order_sum_id
     *
     * @mbg.generated
     */
    public String getChargeOrderSumId() {
        return chargeOrderSumId;
    }

    /**
     *
     * @param chargeOrderSumId the value for waybill_ntb_order_charge_detail.charge_order_sum_id
     *
     * @mbg.generated
     */
    public void setChargeOrderSumId(String chargeOrderSumId) {
        this.chargeOrderSumId = chargeOrderSumId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.service_code
     *
     * @mbg.generated
     */
    public String getServiceCode() {
        return serviceCode;
    }

    /**
     *
     * @param serviceCode the value for waybill_ntb_order_charge_detail.service_code
     *
     * @mbg.generated
     */
    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.formula
     *
     * @mbg.generated
     */
    public String getFormula() {
        return formula;
    }

    /**
     *
     * @param formula the value for waybill_ntb_order_charge_detail.formula
     *
     * @mbg.generated
     */
    public void setFormula(String formula) {
        this.formula = formula;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.version
     *
     * @mbg.generated
     */
    public String getVersion() {
        return version;
    }

    /**
     *
     * @param version the value for waybill_ntb_order_charge_detail.version
     *
     * @mbg.generated
     */
    public void setVersion(String version) {
        this.version = version;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.external_biz_no
     *
     * @mbg.generated
     */
    public String getExternalBizNo() {
        return externalBizNo;
    }

    /**
     *
     * @param externalBizNo the value for waybill_ntb_order_charge_detail.external_biz_no
     *
     * @mbg.generated
     */
    public void setExternalBizNo(String externalBizNo) {
        this.externalBizNo = externalBizNo;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.expect_charge_time
     *
     * @mbg.generated
     */
    public String getExpectChargeTime() {
        return expectChargeTime;
    }

    /**
     *
     * @param expectChargeTime the value for waybill_ntb_order_charge_detail.expect_charge_time
     *
     * @mbg.generated
     */
    public void setExpectChargeTime(String expectChargeTime) {
        this.expectChargeTime = expectChargeTime;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.subscribe_id
     *
     * @mbg.generated
     */
    public Long getSubscribeId() {
        return subscribeId;
    }

    /**
     *
     * @param subscribeId the value for waybill_ntb_order_charge_detail.subscribe_id
     *
     * @mbg.generated
     */
    public void setSubscribeId(Long subscribeId) {
        this.subscribeId = subscribeId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.logistic_auctions_id
     *
     * @mbg.generated
     */
    public Long getLogisticAuctionsId() {
        return logisticAuctionsId;
    }

    /**
     *
     * @param logisticAuctionsId the value for waybill_ntb_order_charge_detail.logistic_auctions_id
     *
     * @mbg.generated
     */
    public void setLogisticAuctionsId(Long logisticAuctionsId) {
        this.logisticAuctionsId = logisticAuctionsId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.currency_rate
     *
     * @mbg.generated
     */
    public BigDecimal getCurrencyRate() {
        return currencyRate;
    }

    /**
     *
     * @param currencyRate the value for waybill_ntb_order_charge_detail.currency_rate
     *
     * @mbg.generated
     */
    public void setCurrencyRate(BigDecimal currencyRate) {
        this.currencyRate = currencyRate;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.appointed_account
     *
     * @mbg.generated
     */
    public String getAppointedAccount() {
        return appointedAccount;
    }

    /**
     *
     * @param appointedAccount the value for waybill_ntb_order_charge_detail.appointed_account
     *
     * @mbg.generated
     */
    public void setAppointedAccount(String appointedAccount) {
        this.appointedAccount = appointedAccount;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.price_id
     *
     * @mbg.generated
     */
    public Long getPriceId() {
        return priceId;
    }

    /**
     *
     * @param priceId the value for waybill_ntb_order_charge_detail.price_id
     *
     * @mbg.generated
     */
    public void setPriceId(Long priceId) {
        this.priceId = priceId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.quoted_price_id
     *
     * @mbg.generated
     */
    public Long getQuotedPriceId() {
        return quotedPriceId;
    }

    /**
     *
     * @param quotedPriceId the value for waybill_ntb_order_charge_detail.quoted_price_id
     *
     * @mbg.generated
     */
    public void setQuotedPriceId(Long quotedPriceId) {
        this.quotedPriceId = quotedPriceId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.quoted_currency
     *
     * @mbg.generated
     */
    public String getQuotedCurrency() {
        return quotedCurrency;
    }

    /**
     *
     * @param quotedCurrency the value for waybill_ntb_order_charge_detail.quoted_currency
     *
     * @mbg.generated
     */
    public void setQuotedCurrency(String quotedCurrency) {
        this.quotedCurrency = quotedCurrency;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.quoted_amount
     *
     * @mbg.generated
     */
    public BigDecimal getQuotedAmount() {
        return quotedAmount;
    }

    /**
     *
     * @param quotedAmount the value for waybill_ntb_order_charge_detail.quoted_amount
     *
     * @mbg.generated
     */
    public void setQuotedAmount(BigDecimal quotedAmount) {
        this.quotedAmount = quotedAmount;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.logistic_auctions_name
     *
     * @mbg.generated
     */
    public String getLogisticAuctionsName() {
        return logisticAuctionsName;
    }

    /**
     *
     * @param logisticAuctionsName the value for waybill_ntb_order_charge_detail.logistic_auctions_name
     *
     * @mbg.generated
     */
    public void setLogisticAuctionsName(String logisticAuctionsName) {
        this.logisticAuctionsName = logisticAuctionsName;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.price_name
     *
     * @mbg.generated
     */
    public String getPriceName() {
        return priceName;
    }

    /**
     *
     * @param priceName the value for waybill_ntb_order_charge_detail.price_name
     *
     * @mbg.generated
     */
    public void setPriceName(String priceName) {
        this.priceName = priceName;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.account_period_sequence
     *
     * @mbg.generated
     */
    public String getAccountPeriodSequence() {
        return accountPeriodSequence;
    }

    /**
     *
     * @param accountPeriodSequence the value for waybill_ntb_order_charge_detail.account_period_sequence
     *
     * @mbg.generated
     */
    public void setAccountPeriodSequence(String accountPeriodSequence) {
        this.accountPeriodSequence = accountPeriodSequence;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.sign_number
     *
     * @mbg.generated
     */
    public String getSignNumber() {
        return signNumber;
    }

    /**
     *
     * @param signNumber the value for waybill_ntb_order_charge_detail.sign_number
     *
     * @mbg.generated
     */
    public void setSignNumber(String signNumber) {
        this.signNumber = signNumber;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.calc_pattern
     *
     * @mbg.generated
     */
    public String getCalcPattern() {
        return calcPattern;
    }

    /**
     *
     * @param calcPattern the value for waybill_ntb_order_charge_detail.calc_pattern
     *
     * @mbg.generated
     */
    public void setCalcPattern(String calcPattern) {
        this.calcPattern = calcPattern;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.sub_order_cnt
     *
     * @mbg.generated
     */
    public Long getSubOrderCnt() {
        return subOrderCnt;
    }

    /**
     *
     * @param subOrderCnt the value for waybill_ntb_order_charge_detail.sub_order_cnt
     *
     * @mbg.generated
     */
    public void setSubOrderCnt(Long subOrderCnt) {
        this.subOrderCnt = subOrderCnt;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.volume_sum
     *
     * @mbg.generated
     */
    public BigDecimal getVolumeSum() {
        return volumeSum;
    }

    /**
     *
     * @param volumeSum the value for waybill_ntb_order_charge_detail.volume_sum
     *
     * @mbg.generated
     */
    public void setVolumeSum(BigDecimal volumeSum) {
        this.volumeSum = volumeSum;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.org_charge1
     *
     * @mbg.generated
     */
    public String getOrgCharge1() {
        return orgCharge1;
    }

    /**
     *
     * @param orgCharge1 the value for waybill_ntb_order_charge_detail.org_charge1
     *
     * @mbg.generated
     */
    public void setOrgCharge1(String orgCharge1) {
        this.orgCharge1 = orgCharge1;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.org_charge2
     *
     * @mbg.generated
     */
    public String getOrgCharge2() {
        return orgCharge2;
    }

    /**
     *
     * @param orgCharge2 the value for waybill_ntb_order_charge_detail.org_charge2
     *
     * @mbg.generated
     */
    public void setOrgCharge2(String orgCharge2) {
        this.orgCharge2 = orgCharge2;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.pay_voucher_no
     *
     * @mbg.generated
     */
    public String getPayVoucherNo() {
        return payVoucherNo;
    }

    /**
     *
     * @param payVoucherNo the value for waybill_ntb_order_charge_detail.pay_voucher_no
     *
     * @mbg.generated
     */
    public void setPayVoucherNo(String payVoucherNo) {
        this.payVoucherNo = payVoucherNo;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.charge_bill_id
     *
     * @mbg.generated
     */
    public Long getChargeBillId() {
        return chargeBillId;
    }

    /**
     *
     * @param chargeBillId the value for waybill_ntb_order_charge_detail.charge_bill_id
     *
     * @mbg.generated
     */
    public void setChargeBillId(Long chargeBillId) {
        this.chargeBillId = chargeBillId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.bill_status
     *
     * @mbg.generated
     */
    public String getBillStatus() {
        return billStatus;
    }

    /**
     *
     * @param billStatus the value for waybill_ntb_order_charge_detail.bill_status
     *
     * @mbg.generated
     */
    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.is_pay_success
     *
     * @mbg.generated
     */
    public String getIsPaySuccess() {
        return isPaySuccess;
    }

    /**
     *
     * @param isPaySuccess the value for waybill_ntb_order_charge_detail.is_pay_success
     *
     * @mbg.generated
     */
    public void setIsPaySuccess(String isPaySuccess) {
        this.isPaySuccess = isPaySuccess;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.org_unit
     *
     * @mbg.generated
     */
    public String getOrgUnit() {
        return orgUnit;
    }

    /**
     *
     * @param orgUnit the value for waybill_ntb_order_charge_detail.org_unit
     *
     * @mbg.generated
     */
    public void setOrgUnit(String orgUnit) {
        this.orgUnit = orgUnit;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.is_fin_bill_confirm
     *
     * @mbg.generated
     */
    public String getIsFinBillConfirm() {
        return isFinBillConfirm;
    }

    /**
     *
     * @param isFinBillConfirm the value for waybill_ntb_order_charge_detail.is_fin_bill_confirm
     *
     * @mbg.generated
     */
    public void setIsFinBillConfirm(String isFinBillConfirm) {
        this.isFinBillConfirm = isFinBillConfirm;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.fin_bill_confirm_time
     *
     * @mbg.generated
     */
    public String getFinBillConfirmTime() {
        return finBillConfirmTime;
    }

    /**
     *
     * @param finBillConfirmTime the value for waybill_ntb_order_charge_detail.fin_bill_confirm_time
     *
     * @mbg.generated
     */
    public void setFinBillConfirmTime(String finBillConfirmTime) {
        this.finBillConfirmTime = finBillConfirmTime;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.charge_amount_rmb
     *
     * @mbg.generated
     */
    public BigDecimal getChargeAmountRmb() {
        return chargeAmountRmb;
    }

    /**
     *
     * @param chargeAmountRmb the value for waybill_ntb_order_charge_detail.charge_amount_rmb
     *
     * @mbg.generated
     */
    public void setChargeAmountRmb(BigDecimal chargeAmountRmb) {
        this.chargeAmountRmb = chargeAmountRmb;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.writeoff_time
     *
     * @mbg.generated
     */
    public String getWriteoffTime() {
        return writeoffTime;
    }

    /**
     *
     * @param writeoffTime the value for waybill_ntb_order_charge_detail.writeoff_time
     *
     * @mbg.generated
     */
    public void setWriteoffTime(String writeoffTime) {
        this.writeoffTime = writeoffTime;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.fee_type
     *
     * @mbg.generated
     */
    public String getFeeType() {
        return feeType;
    }

    /**
     *
     * @param feeType the value for waybill_ntb_order_charge_detail.fee_type
     *
     * @mbg.generated
     */
    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.user_system
     *
     * @mbg.generated
     */
    public String getUserSystem() {
        return userSystem;
    }

    /**
     *
     * @param userSystem the value for waybill_ntb_order_charge_detail.user_system
     *
     * @mbg.generated
     */
    public void setUserSystem(String userSystem) {
        this.userSystem = userSystem;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.price_detail_id
     *
     * @mbg.generated
     */
    public Long getPriceDetailId() {
        return priceDetailId;
    }

    /**
     *
     * @param priceDetailId the value for waybill_ntb_order_charge_detail.price_detail_id
     *
     * @mbg.generated
     */
    public void setPriceDetailId(Long priceDetailId) {
        this.priceDetailId = priceDetailId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.tenant_id
     *
     * @mbg.generated
     */
    public Long getTenantId() {
        return tenantId;
    }

    /**
     *
     * @param tenantId the value for waybill_ntb_order_charge_detail.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.is_bad_debt
     *
     * @mbg.generated
     */
    public String getIsBadDebt() {
        return isBadDebt;
    }

    /**
     *
     * @param isBadDebt the value for waybill_ntb_order_charge_detail.is_bad_debt
     *
     * @mbg.generated
     */
    public void setIsBadDebt(String isBadDebt) {
        this.isBadDebt = isBadDebt;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.mail_no
     *
     * @mbg.generated
     */
    public String getMailNo() {
        return mailNo;
    }

    /**
     *
     * @param mailNo the value for waybill_ntb_order_charge_detail.mail_no
     *
     * @mbg.generated
     */
    public void setMailNo(String mailNo) {
        this.mailNo = mailNo;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.ds
     *
     * @mbg.generated
     */
    public String getDs() {
        return ds;
    }

    /**
     *
     * @param ds the value for waybill_ntb_order_charge_detail.ds
     *
     * @mbg.generated
     */
    public void setDs(String ds) {
        this.ds = ds;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.source
     *
     * @mbg.generated
     */
    public String getSource() {
        return source;
    }

    /**
     *
     * @param source the value for waybill_ntb_order_charge_detail.source
     *
     * @mbg.generated
     */
    public void setSource(String source) {
        this.source = source;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.biz_type
     *
     * @mbg.generated
     */
    public String getBizType() {
        return bizType;
    }

    /**
     *
     * @param bizType the value for waybill_ntb_order_charge_detail.biz_type
     *
     * @mbg.generated
     */
    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.user_classify
     *
     * @mbg.generated
     */
    public String getUserClassify() {
        return userClassify;
    }

    /**
     *
     * @param userClassify the value for waybill_ntb_order_charge_detail.user_classify
     *
     * @mbg.generated
     */
    public void setUserClassify(String userClassify) {
        this.userClassify = userClassify;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.charge_feature
     *
     * @mbg.generated
     */
    public String getChargeFeature() {
        return chargeFeature;
    }

    /**
     *
     * @param chargeFeature the value for waybill_ntb_order_charge_detail.charge_feature
     *
     * @mbg.generated
     */
    public void setChargeFeature(String chargeFeature) {
        this.chargeFeature = chargeFeature;
    }

    /**
     *
     * @return the value of waybill_ntb_order_charge_detail.charge_params
     *
     * @mbg.generated
     */
    public String getChargeParams() {
        return chargeParams;
    }

    /**
     *
     * @param chargeParams the value for waybill_ntb_order_charge_detail.charge_params
     *
     * @mbg.generated
     */
    public void setChargeParams(String chargeParams) {
        this.chargeParams = chargeParams;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", chargeId=").append(chargeId);
        sb.append(", chargeCreateTime=").append(chargeCreateTime);
        sb.append(", chargeModifiedTime=").append(chargeModifiedTime);
        sb.append(", bizTime=").append(bizTime);
        sb.append(", sn=").append(sn);
        sb.append(", entityValue=").append(entityValue);
        sb.append(", userType=").append(userType);
        sb.append(", userId=").append(userId);
        sb.append(", userNick=").append(userNick);
        sb.append(", incomeFlag=").append(incomeFlag);
        sb.append(", feeCode=").append(feeCode);
        sb.append(", feeName=").append(feeName);
        sb.append(", payCurrency=").append(payCurrency);
        sb.append(", chargeAmount=").append(chargeAmount);
        sb.append(", chargeStatus=").append(chargeStatus);
        sb.append(", eventTypeCode=").append(eventTypeCode);
        sb.append(", accountFlag=").append(accountFlag);
        sb.append(", chargingPeriod=").append(chargingPeriod);
        sb.append(", serviceItemCode=").append(serviceItemCode);
        sb.append(", serviceItemName=").append(serviceItemName);
        sb.append(", weightSum=").append(weightSum);
        sb.append(", accChrOrdOkay=").append(accChrOrdOkay);
        sb.append(", payId=").append(payId);
        sb.append(", payCreateTime=").append(payCreateTime);
        sb.append(", payModifiedTime=").append(payModifiedTime);
        sb.append(", payableTime=").append(payableTime);
        sb.append(", alipayTime=").append(alipayTime);
        sb.append(", payType=").append(payType);
        sb.append(", payBizType=").append(payBizType);
        sb.append(", payerUserId=").append(payerUserId);
        sb.append(", payerAccountNo=").append(payerAccountNo);
        sb.append(", payeeUserId=").append(payeeUserId);
        sb.append(", payeeAccountNo=").append(payeeAccountNo);
        sb.append(", payStatus=").append(payStatus);
        sb.append(", payFeature=").append(payFeature);
        sb.append(", payActualCurrency=").append(payActualCurrency);
        sb.append(", payActualAmount=").append(payActualAmount);
        sb.append(", calcTemplateId=").append(calcTemplateId);
        sb.append(", calcModeCode=").append(calcModeCode);
        sb.append(", feePriceId=").append(feePriceId);
        sb.append(", chargeOrderSumId=").append(chargeOrderSumId);
        sb.append(", serviceCode=").append(serviceCode);
        sb.append(", formula=").append(formula);
        sb.append(", version=").append(version);
        sb.append(", externalBizNo=").append(externalBizNo);
        sb.append(", expectChargeTime=").append(expectChargeTime);
        sb.append(", subscribeId=").append(subscribeId);
        sb.append(", logisticAuctionsId=").append(logisticAuctionsId);
        sb.append(", currencyRate=").append(currencyRate);
        sb.append(", appointedAccount=").append(appointedAccount);
        sb.append(", priceId=").append(priceId);
        sb.append(", quotedPriceId=").append(quotedPriceId);
        sb.append(", quotedCurrency=").append(quotedCurrency);
        sb.append(", quotedAmount=").append(quotedAmount);
        sb.append(", logisticAuctionsName=").append(logisticAuctionsName);
        sb.append(", priceName=").append(priceName);
        sb.append(", accountPeriodSequence=").append(accountPeriodSequence);
        sb.append(", signNumber=").append(signNumber);
        sb.append(", calcPattern=").append(calcPattern);
        sb.append(", subOrderCnt=").append(subOrderCnt);
        sb.append(", volumeSum=").append(volumeSum);
        sb.append(", orgCharge1=").append(orgCharge1);
        sb.append(", orgCharge2=").append(orgCharge2);
        sb.append(", payVoucherNo=").append(payVoucherNo);
        sb.append(", chargeBillId=").append(chargeBillId);
        sb.append(", billStatus=").append(billStatus);
        sb.append(", isPaySuccess=").append(isPaySuccess);
        sb.append(", orgUnit=").append(orgUnit);
        sb.append(", isFinBillConfirm=").append(isFinBillConfirm);
        sb.append(", finBillConfirmTime=").append(finBillConfirmTime);
        sb.append(", chargeAmountRmb=").append(chargeAmountRmb);
        sb.append(", writeoffTime=").append(writeoffTime);
        sb.append(", feeType=").append(feeType);
        sb.append(", userSystem=").append(userSystem);
        sb.append(", priceDetailId=").append(priceDetailId);
        sb.append(", tenantId=").append(tenantId);
        sb.append(", isBadDebt=").append(isBadDebt);
        sb.append(", mailNo=").append(mailNo);
        sb.append(", ds=").append(ds);
        sb.append(", source=").append(source);
        sb.append(", bizType=").append(bizType);
        sb.append(", userClassify=").append(userClassify);
        sb.append(", chargeFeature=").append(chargeFeature);
        sb.append(", chargeParams=").append(chargeParams);
        sb.append("]");
        return sb.toString();
    }
}