package com.cainiao.waybill.bridge.pickup;

import java.util.List;

import com.cainiao.waybill.bridge.SimpleResult;
import com.cainiao.waybill.bridge.pickup.bean.PickUpOrderLogisticDetailDTO;
import com.cainiao.waybill.bridge.pickup.bean.PickUpOrderTraceDTO;
import com.cainiao.waybill.bridge.pickup.bean.PickUpOrderTraceRequest;

/**
 * @Classname PickUpOrderService
 * @Description
 * @Date 2023/4/3 11:30 上午
 * @Created by zouping.fzp
 * @Version 1.0
 */
public interface PickUpOrderApiService {

    SimpleResult<PickUpOrderLogisticDetailDTO> queryOrderTrace(PickUpOrderTraceRequest request);
}
