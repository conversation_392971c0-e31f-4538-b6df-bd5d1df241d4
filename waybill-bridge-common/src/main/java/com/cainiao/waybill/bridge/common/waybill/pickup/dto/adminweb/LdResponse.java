package com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/21-下午9:10
 */
@Data
public class LdResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String outerOrderCode;
    /**
     * 运单号
     */
    private String mailNo;
    /**
     * 状态
     */
    private String status;
    /**
     * 快递公司
     */
    private String cpCodeName;
    /**
     * 详情列表
     */
    private List<ItemMap> ldList;
}
