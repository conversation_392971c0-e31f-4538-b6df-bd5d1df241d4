package com.cainiao.waybill.bridge.common.logger;

import ch.qos.logback.classic.pattern.NamedConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;

public class ClassConverter extends NamedConverter {
    public ClassConverter() {
    }
    @Override
    protected String getFullyQualifiedName(ILoggingEvent event) {
        StackTraceElement[] cda = event.getCallerData();
        if(cda==null){
            return "";
        }
        if(cda.length>0){
            return cda[cda.length-1].getClassName();
        }
        return "";
    }
}
