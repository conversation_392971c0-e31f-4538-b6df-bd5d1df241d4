package com.cainiao.waybill.bridge.model.dao.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.model.common.AbstractDAO;
import com.cainiao.waybill.bridge.model.dao.WaybillMobileVerificationCodeDAO;
import com.cainiao.waybill.bridge.model.domain.WaybillMobileVerificationCodeDO;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant;
import com.taobao.common.dao.persistence.exception.DAOException;
import com.taobao.tddl.client.sequence.exception.SequenceException;
import com.taobao.tddl.client.sequence.impl.GroupSequence;

/**
 *
 * 短信验证码信息
 *
 * Created by shouyuan.lzl on 2017-04-18 4:03 PM.
 */
public class WaybillMobileVerificationCodeDAOImpl extends AbstractDAO implements WaybillMobileVerificationCodeDAO {

    @Resource
    private GroupSequence mobileVerificationCodeSequence;

    private Long getId() throws DAOException {
        try {
            return mobileVerificationCodeSequence.nextValue();
        } catch (SequenceException e) {
            throw new DAOException(WaybillErrorConstant.SystemError.TDDL_SEQUENCE_ERROR.getErrorMsg(), e);
        } catch (Exception e) {
            throw new DAOException(e);
        }
    }

    @Override
    public Integer addVerificationCode(WaybillMobileVerificationCodeDO waybillMobileVerificationCodeDO) throws DAOException {

        waybillMobileVerificationCodeDO.setId(getId());
        return executeUpdate("waybillMobileVerificationCode.addVerificationCode", waybillMobileVerificationCodeDO, getDBRoute());
    }

    @Override
    public WaybillMobileVerificationCodeDO queryVerificationCode(long sellerId, String mobile, int verificationType) throws DAOException {

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("sellerId", sellerId);
        paramMap.put("mobile",mobile) ;
        paramMap.put("verificationType", verificationType);

        return (WaybillMobileVerificationCodeDO) executeQueryForObject("waybillMobileVerificationCode.queryVerificationCode", paramMap, getDBRoute()) ;
    }

    @Override
    public int countVerificationCode(long sellerId, String mobile, int verificationType, String gmtCreate) throws DAOException {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("sellerId", sellerId);
        paramMap.put("mobile",mobile) ;
        paramMap.put("verificationType", verificationType);
        paramMap.put("gmtCreate", gmtCreate);

        Integer count = (Integer) executeQueryForObject("waybillMobileVerificationCode.countVerificationCode", paramMap, getDBRoute()) ;

        return count == null ? 0 : count.intValue() ;
    }

    @Override
    public int countVerificationCode(long sellerId, int verificationType, String gmtCreate) throws DAOException {

        return countVerificationCode(sellerId,null, verificationType, gmtCreate) ;
    }

    @Override
    public boolean verifyVerificationCode(long id, boolean checkSuccess) throws DAOException {

        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("id", id);

        int rt ;
        if(checkSuccess) {
            rt = executeUpdate("waybillMobileVerificationCode.verifyVerificationCodeTrue", paramMap, getDBRoute());
        } else {
            rt = executeUpdate("waybillMobileVerificationCode.verifyVerificationCodeFalse", paramMap, getDBRoute());
        }

        return rt == 1;
    }

    @Override
    public boolean smsSendCountPlusOne(long id) throws DAOException {
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("id", id);

        int rt = executeUpdate("waybillMobileVerificationCode.smsSendCountPlusOne", paramMap, getDBRoute());

        return rt == 1 ;
    }
}
