// 右键菜单，可以一键生成哦。
// 配置格式参见 https://yuque.antfin-inc.com/xd6pvi/dr962p 
{
	"tableName":"waybill_common_config",
	"domainObjectName":"WaybillCommonConfig",
	"mapperName":"WaybillCommonConfigDAO",
	"appName":"CAINIAO_WAYBILL_BRIDGE_APP",
	"groupKey":"CAINIAO_WAYBILL_BRIDGE_GROUP",
	"split":false,
	"pattern":"_[0-9]+$",
	"anno":true,
	"antZdal":false,
	"appList":[
		"CAINIAO_WAYBILL_BRIDGE_APP$CAINIAO_WAYBILL_BRIDGE_GROUP"
	],
	"blobEnable":true,
	"businessPackageName":"",
	"businessTargetProject":"waybill-bridge-aliSocialWork/src/main/java",
	"catlog":"cainiao_waybill_bridge",
	"columnName":"id",
	"connectionURL":"",
	"createBaseType":true,
	"disableNamespace":false,
	"driverClass":"",
	"enableDeleteMethod":true,
	"fileEncode":"UTF-8",
	"generateBusiness":false,
	"generateKey":false,
	"ignoreColumn":false,
	"ignoreColumnValue":"id",
	"javaClientGeneratorTargetPackage":"com.cainiao.waybill.bridge.model.domain",
	"javaClientGeneratorTargetProject":"waybill-bridge-model/src/main/java",
	"javaClientGeneratorType":"XMLMAPPER",
	"javaModelGeneratorTargetPackage":"com.cainiao.waybill.bridge.model.domain",
	"javaModelGeneratorTargetProject":"waybill-bridge-model/src/main/java",
	"lastBizPackage":"",
	"lastBizProjectPath":"waybill-bridge-aliSocialWork/src/main/java",
	"lastTypeHadler":"",
	"lastTypeJava":"",
	"mergePolicy":1,
	"modelType":"flat",
	"myDataBase":null,
	"password":"",
	"replaceIs":false,
	"saveParameter":true,
	"saveParameterPath":".aligenerator",
	"security":true,
	"serializable":false,
	"simpleMethod":true,
	"sqlMapGeneratorTargetPackage":"sqlmapper",
	"sqlMapGeneratorTargetProject":"waybill-bridge-model/src/main/resources",
	"sqlStatement":"JDBC",
	"tableSchema":"",
	"targetRuntime":"MyBatis3",
	"tddl":true,
	"tddlMap":{
		"CAINIAO_WAYBILL_BRIDGE_APP":"CAINIAO_WAYBILL_BRIDGE_GROUP"
	},
	"typeHandlerMap":{},
	"typeMap":{},
	"useBaseDao":false,
	"useDBTime":false,
	"useDao":true,
	"useLombok":false,
	"useSqlCreate":false,
	"userId":"",
	"whenPresent":true,
	"generatedBy":"Aligenerator",
	"schemaVersion":"1.0"
}