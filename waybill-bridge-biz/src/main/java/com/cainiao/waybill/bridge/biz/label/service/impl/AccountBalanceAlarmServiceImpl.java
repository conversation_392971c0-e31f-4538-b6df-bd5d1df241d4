package com.cainiao.waybill.bridge.biz.label.service.impl;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
import com.cainiao.waybill.bridge.biz.label.manager.AccountBalanceAlarmManager;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.SystemError;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.util.BaseResultHelper;
import com.cainiao.waybill.bridge.common.label.dto.AccountBalanceAlarmDTO;
import com.cainiao.waybill.bridge.common.label.dto.AccountBalanceAlarmQueryDTO;
import com.cainiao.waybill.bridge.common.label.dto.response.AccountBalanceAlarmResponse;
import com.cainiao.waybill.bridge.common.label.service.AccountBalanceAlarmService;
import com.cainiao.waybill.bridge.common.label.validator.AccountBalanceAlarmValidator;
import com.cainiao.waybill.bridge.model.domain.AccountBalanceAlarmDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @since 2017/04/21
 * 面单余额设置impl
 */
@HSFProvider(serviceInterface = AccountBalanceAlarmService.class)
public class AccountBalanceAlarmServiceImpl implements AccountBalanceAlarmService {
    private final static Logger ACCOUNT_ALARM_LOGGER = LoggerFactory.getLogger(LogAppender.ACCOUNT_ALARM);

    @Resource
    private AccountBalanceAlarmManager accountBalanceAlarmManager;
    @Resource
    private LogManager logManager;

    @Override
    public BaseResultDTO<Void> saveAccountBalanceAlarm(AccountBalanceAlarmDTO accountBalanceAlarmDTO, ClientInfoDTO clientInfoDTO) {
        try{
            AccountBalanceAlarmValidator.validateSaveAccountBalanceAlarm(accountBalanceAlarmDTO,clientInfoDTO);
            accountBalanceAlarmManager.saveAccountBalanceAlarm(accountBalanceAlarmDTO);
            return BaseResultHelper.newResult();
        }catch (BridgeValidationException e ){
            logManager.logExceptionMessage("AccountBalanceAlarmServiceImpl#saveAccountBalanceAlarm", "BridgeValidationException,accountBalanceAlarmDTO:" + accountBalanceAlarmDTO,e,
                ACCOUNT_ALARM_LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(),e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("AccountBalanceAlarmServiceImpl#saveAccountBalanceAlarm", "BridgeBaseException accountBalanceAlarmDTO:" + accountBalanceAlarmDTO,e,
                ACCOUNT_ALARM_LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(),e.getErrorMessage());
        } catch (Throwable e) {
            logManager.logExceptionMessage("AccountBalanceAlarmServiceImpl#saveAccountBalanceAlarm", "Throwable accountBalanceAlarmDTO:" + accountBalanceAlarmDTO,e,
                ACCOUNT_ALARM_LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        }
    }

    @Override
    public BaseResultDTO<AccountBalanceAlarmResponse> getAccountBalanceAlarm(AccountBalanceAlarmQueryDTO accountBalanceAlarmQueryDTO,
                                                                             ClientInfoDTO clientInfoDTO) {
        try{
            AccountBalanceAlarmValidator.validateGetAccountBalanceAlarm(accountBalanceAlarmQueryDTO,clientInfoDTO);
            AccountBalanceAlarmDO accountBalanceAlarmDO = accountBalanceAlarmManager.getAccountBalanceAlarm(accountBalanceAlarmQueryDTO);
            return BaseResultHelper.newResult(transferDO2ResponseDTO(accountBalanceAlarmDO));
        }catch (BridgeValidationException e ){
            logManager.logExceptionMessage("AccountBalanceAlarmServiceImpl#getAccountBalanceAlarm", "BridgeValidationException accountBalanceAlarmQueryDTO:" + accountBalanceAlarmQueryDTO,e,
                ACCOUNT_ALARM_LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(),e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("AccountBalanceAlarmServiceImpl#getAccountBalanceAlarm", "BridgeBaseException accountBalanceAlarmQueryDTO:" + accountBalanceAlarmQueryDTO,e,
                ACCOUNT_ALARM_LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(),e.getErrorMessage());
        } catch (Throwable e) {
            logManager.logExceptionMessage("AccountBalanceAlarmServiceImpl#getAccountBalanceAlarm", "Throwable accountBalanceAlarmQueryDTO:" + accountBalanceAlarmQueryDTO,e,
                ACCOUNT_ALARM_LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        }
    }

    private AccountBalanceAlarmResponse transferDO2ResponseDTO(AccountBalanceAlarmDO accountBalanceAlarmDO){
        if(accountBalanceAlarmDO == null){
            return null;
        }
        AccountBalanceAlarmResponse accountBalanceAlarmResponseDTO = new AccountBalanceAlarmResponse();
        accountBalanceAlarmResponseDTO.setCpId(accountBalanceAlarmDO.getCpId());
        accountBalanceAlarmResponseDTO.setSellerId(accountBalanceAlarmDO.getAccountId());
        accountBalanceAlarmResponseDTO.setSegmentCode(accountBalanceAlarmDO.getSegmentCode());
        accountBalanceAlarmResponseDTO.setBranchCode(accountBalanceAlarmDO.getBranchCode());
        accountBalanceAlarmResponseDTO.setAlarmQuantity(accountBalanceAlarmDO.getAlarmQuantity());
        accountBalanceAlarmResponseDTO.setIntervalHour(accountBalanceAlarmDO.getIntervalHour());
        accountBalanceAlarmResponseDTO.setPhone(accountBalanceAlarmDO.getPhone());
        return accountBalanceAlarmResponseDTO;
    }
}
