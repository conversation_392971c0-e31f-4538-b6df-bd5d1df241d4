package com.cainiao.waybill.bridge.client.hsf;

import com.cainiao.waybill.bridge.SimpleResult;
import com.cainiao.waybill.bridge.client.response.CustomerInfoResponse;

/**
 * 客户服务
 * <AUTHOR>
 * @date 2024/8/23 14:03
 **/
public interface CnUserService {

    /**
     * 查询预充值客户服务开通状态
     * @param accountId
     * @return
     */
    SimpleResult<CustomerInfoResponse> queryAccountInfo(String accountId);

}
