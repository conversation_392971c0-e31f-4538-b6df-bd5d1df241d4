package com.cainiao.waybill.bridge.web.charity.validate;

import java.util.Objects;

import javax.validation.ValidationException;

import com.cainiao.waybill.bridge.biz.charity.constant.enums.BaseCodeEnum;

/**
 * <AUTHOR> zouping.fzp
 * @Classname ValidatorUtil
 * @Description
 * @Date 2022/8/25 4:31 下午
 * @Version 1.0
 */
public class ValidatorUtil {

    public static void isValidateEnumWhenNotNull(Byte val, Class<? extends Enum<? extends BaseCodeEnum>> baseCodeEnum, String errorMsg){
        Enum<? extends BaseCodeEnum>[] objects = baseCodeEnum.getEnumConstants();
        for (Enum<? extends BaseCodeEnum> object : objects) {
            if(Objects.equals(((BaseCodeEnum)object).getCode(), val)){
                return;
            }
        }
        throw new ValidationException(errorMsg);
    }

    public static boolean isValidateEnumWhenNotNull(Byte val, Class<? extends Enum<? extends BaseCodeEnum>> baseCodeEnum){
        Enum<? extends BaseCodeEnum>[] objects = baseCodeEnum.getEnumConstants();
        for (Enum<? extends BaseCodeEnum> object : objects) {
            if(Objects.equals(((BaseCodeEnum)object).getCode(), val)){
                return true;
            }
        }
       return false;
    }
}
