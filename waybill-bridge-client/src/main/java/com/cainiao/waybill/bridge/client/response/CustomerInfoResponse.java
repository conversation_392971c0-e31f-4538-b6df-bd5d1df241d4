package com.cainiao.waybill.bridge.client.response;

import java.io.Serializable;

import lombok.Data;

/**
 * 预充值客户信息
 * <AUTHOR>
 * @date 2024/8/23 15:03
 **/
@Data
public class CustomerInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账号ID
     */
    private String accountId;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 开通状态
     */
    private String serviceStatus;

    private String serviceStatusDesc;

    /**
     * 管控状态
     */
    private String manageStatus;

    /**
     * 管控状态
     */
    private String manageStatusDesc;



}
