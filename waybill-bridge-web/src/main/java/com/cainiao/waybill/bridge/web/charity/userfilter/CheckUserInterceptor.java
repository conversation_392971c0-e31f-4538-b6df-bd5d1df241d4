package com.cainiao.waybill.bridge.web.charity.userfilter;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;

import com.cainiao.cnlogin.api.context.CnUserInfo;
import com.cainiao.cnlogin.api.context.CnUserInfoUtil;
import com.cainiao.cnuser.client.ResultInfo;
import com.cainiao.cnuser.client.domain.CnUserInfoDO;
import com.cainiao.cnuser.client.service.CnUserInfoQueryService;
import com.cainiao.waybill.bridge.biz.common.user.WaybillBridgeUserInfoService;
import com.cainiao.waybill.bridge.common.config.diamond.WaybillCharityDiamondConfig;
import com.cainiao.waybill.bridge.common.config.diamond.WaybillCharityDiamondConfig.WaybillCharityConfig;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeUserInfoDTO;
import com.cainiao.waybill.bridge.web.charity.CharityLoginUserUtil;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @date 2022/4/22 上午11:56
 */
@Slf4j(topic = "CHARITY_INFO")
@Setter
public class CheckUserInterceptor implements HandlerInterceptor {

    private WaybillBridgeUserInfoService userInfoService;

    private CnUserInfoQueryService cnUserInfoQueryService;

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o)
        throws Exception {
        try {
            CnUserInfo userInfo = CnUserInfoUtil.getLoginContext();
            if (userInfo == null) {
                writeNoAuthResponse(httpServletResponse);
                return false;
            }
            // 加入用户mock
            Map<String, String> mockAccountId = WaybillCharityDiamondConfig.getConfig().getMockAccountId();
            if(mockAccountId != null && mockAccountId.get(String.valueOf(userInfo.getAccountId())) != null){
                userInfo.setAccountId(Long.valueOf(mockAccountId.get(String.valueOf(userInfo.getAccountId()))));
            }
            WaybillBridgeUserInfoDTO userInfoDTO = userInfoService.findCharityUserByCainiaoId(
                String.valueOf(userInfo.getAccountId()));
            if (userInfoDTO != null) {
                CharityLoginUserUtil.buildBaseReq(userInfoDTO.getId(), userInfoDTO.getScene(),
                    userInfoDTO.getRole());
                return true;
            }
            ResultInfo<CnUserInfoDO> resultInfo = cnUserInfoQueryService.getCnUserInfoDO(userInfo.getAccountId());
            if (resultInfo == null || !resultInfo.isSuccess() || resultInfo.getData() == null) {
                log.error("查询会员用户信息错误, accountId:{}, result:{}", userInfo.getAccountId(),
                    JSON.toJSONString(resultInfo));
                writeUserAuthErrorResponse(httpServletResponse);
                return false;
            }
            String phone = resultInfo.getData().getMobile();
            if(StringUtils.isBlank(phone)){
                writeUserAuthErrorResponse(httpServletResponse);
                return false;
            }
            WaybillBridgeUserInfoDTO bridgeUserInfoDTO = userInfoService.findCharityUser(phone);
            if (bridgeUserInfoDTO == null) {
                writeNoAuthResponse(httpServletResponse);
                return false;
            }
            WaybillBridgeUserInfoDTO updateUserInfoDTO = new WaybillBridgeUserInfoDTO();
            updateUserInfoDTO.setCainiaoUserId(String.valueOf(userInfo.getAccountId()));
            updateUserInfoDTO.setId(bridgeUserInfoDTO.getId());
            userInfoService.updateCharityUserById(updateUserInfoDTO);
        } catch (Throwable throwable) {
            log.error("权限校验异常", throwable);
            writeUserAuthErrorResponse(httpServletResponse);
            return false;
        }
        return true;
    }

    private void writeNoAuthResponse(HttpServletResponse response) throws IOException {
        response.setContentType("text/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(BaseResult.bizFail("no_auth", "没有权限，请联系管理员")));
        writer.flush();
        writer.close();
    }

    private void writeUserAuthErrorResponse(HttpServletResponse response) throws IOException {
        response.setContentType("text/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(BaseResult.bizFail("user_auth_error", "获取用户权限异常")));
        writer.flush();
        writer.close();
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o,
        ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
        Object o, Exception e) throws Exception {
        CharityLoginUserUtil.clear();
    }
}
