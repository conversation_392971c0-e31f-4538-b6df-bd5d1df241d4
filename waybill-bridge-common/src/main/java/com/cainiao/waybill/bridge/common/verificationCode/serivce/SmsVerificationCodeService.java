package com.cainiao.waybill.bridge.common.verificationCode.serivce;


import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.verificationCode.dto.request.SendVerificationCodeRequest;
import com.cainiao.waybill.bridge.common.verificationCode.dto.request.VerifyVirificationCodeRequest;

/**
 *
 * 短信验证码service
 *
 * Created by shouyuan.lzl on 2017-04-18 9:03 PM.
 */
public interface SmsVerificationCodeService {

    /**
     * 发送短信验证码
     * @return
     */
    BaseResultDTO<Void> sendVerificationCode(SendVerificationCodeRequest request, ClientInfoDTO clientInfoDTO) ;

    /**
     * 校验短信验证码
     * @return
     */
    BaseResultDTO<Boolean> verifyVirificationCode(VerifyVirificationCodeRequest request, ClientInfoDTO clientInfoDTO) ;
}
