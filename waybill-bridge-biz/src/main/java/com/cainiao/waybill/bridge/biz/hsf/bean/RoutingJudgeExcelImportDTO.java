package com.cainiao.waybill.bridge.biz.hsf.bean;

import java.io.Serializable;

import com.cainiao.waybill.bridge.biz.utils.excel.ExcelHeader;
import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname CharityProjectResponse
 * @Description
 * @Date 2022/8/23 8:22 下午
 * @Version 1.0
 */
@Data
public class RoutingJudgeExcelImportDTO implements Serializable {

    private static final long serialVersionUID = -5545059692679126393L;

    /**
     * 项目名称
     */
    @ExcelHeader("发货地址")
    private String senderAddress;

    /**
     * 组织名称
     */
    @ExcelHeader("收货地址")
    private String receiverAddress;

    @ExcelHeader(value = "错误信息", check = false)
    private String errorInfo;

    @ExcelHeader(value = "收货可达苏宁物流", check = false)
    private String snwlReachable;

    @ExcelHeader(value = "发货可达苏宁物流", check = false)
    private String snwlSenderReachable;

    @ExcelHeader(value = "收货申通", check = false)
    private String stoReachable;

    @ExcelHeader(value = "发货申通", check = false)
    private String stoSenderReachable;

    @ExcelHeader(value = "收货圆通", check = false)
    private String ytoReachable;

    @ExcelHeader(value = "发货圆通", check = false)
    private String ytoSenderReachable;

    @ExcelHeader(value = "收货韵达", check = false)
    private String yundaReachable;

    @ExcelHeader(value = "发货韵达", check = false)
    private String yundaSenderReachable;

    @ExcelHeader(value = "收货极兔速递", check = false)
    private String htkyReachable;

    @ExcelHeader(value = "发货极兔速递", check = false)
    private String htkySenderReachable;

    @ExcelHeader(value = "收货中通", check = false)
    private String ztoReachable;

    @ExcelHeader(value = "发货中通", check = false)
    private String ztoSenderReachable;

    @ExcelHeader(value = "收货德邦", check = false)
    private String dbkdReachable;

    @ExcelHeader(value = "发货德邦", check = false)
    private String dbkdSenderReachable;

    @ExcelHeader(value = "收货优速", check = false)
    private String ucReachable;

    @ExcelHeader(value = "发货优速", check = false)
    private String ucSenderReachable;

    @ExcelHeader(value = "收货邮政", check = false)
    private String postbReachable;

    @ExcelHeader(value = "发货邮政", check = false)
    private String postbSenderReachable;

}
