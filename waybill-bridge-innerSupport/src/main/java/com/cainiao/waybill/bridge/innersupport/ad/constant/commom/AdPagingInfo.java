package com.cainiao.waybill.bridge.innersupport.ad.constant.commom;

import lombok.Data;

/**
 * @Description 分页信息
 */
@Data
public class AdPagingInfo extends BaseDTO {

    private static final long serialVersionUID = -915907189129226503L;

    /**
     * 第几页
     */
    private Integer currentPage;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总条数
     */
    private Integer totalCount;

    public int getTotalPage() {
        return totalCount % pageSize == 0 ? totalCount / pageSize : (totalCount / pageSize + 1);
    }
}
