package com.cainiao.waybill.bridge.common.waybill.pickup.service;

/**
 * 物流详情重量提取service
 * 因为不同的物流公司回传的weight字段内容不一样，因此需要抽象一个service
 *
 * <AUTHOR>
 * @date  2021-06-22
 */
public interface LdWeightExtractService {

    /**
     * 提取重量数据
     *
     * @param weightStr 重量的字符串格式
     * @return 返回null则提取失败, 返回重量的单位：g
     */
    Double extractWeight(String weightStr);

    /**
     * 支持的cpCode
     * @return cpCode
     */
    String supportCpCode();
}
