package com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.type;

import com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder.WaybillAdWorkOrderUpdateRequest;
import com.cainiao.waybill.bridge.innersupport.ad.constant.emums.AdCustomRobotGroupTypeEnum;
import com.cainiao.waybill.bridge.innersupport.ad.constant.emums.AdUserRoleEnum;
import com.cainiao.waybill.bridge.innersupport.ad.model.dto.WaybillAdWorkOrderDTO;
import com.cainiao.waybill.bridge.innersupport.ad.util.CustomRobotGroupMessage;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 添加/修改备注
 */
public class RemarkUpdateActionType implements ActionType {

    private String userRole;

    public RemarkUpdateActionType(String userRole) {
        this.userRole = userRole;
    }

    @Override
    public boolean hasChanged(WaybillAdWorkOrderUpdateRequest request, WaybillAdWorkOrderDTO existingWorkOrder) {
        return request.getRemark() != null && !Objects.equals(existingWorkOrder.getRemark(), request.getRemark());
    }


    @Override
    public void handleChange(WaybillAdWorkOrderUpdateRequest request, WaybillAdWorkOrderDTO existingWorkOrder) {
        // 菜鸟客服
        if (Objects.equals(userRole, AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue())) {
            if (StringUtils.isNotBlank(existingWorkOrder.getAdvertiserId())) {
                // 添加/修改备注
                // 菜鸟客服 & 广告主群里，@广告主
                String sendContent = "【客诉提醒】，" + existingWorkOrder.getMailNo() + "，客诉工单备注已更新，备注内容："+ request.getRemark() + "，请查收！";
                String adCustomRobotGroupType = AdCustomRobotGroupTypeEnum.CAINIAO_AND_ADVERTISER_DING_TALK_GROUP.getValue();
                String adCustomRobotGroupTarget = existingWorkOrder.getAdvertiserId();
                String atUserRoleType = AdUserRoleEnum.ADVERTISER_ROLE.getValue();
                String messageContent = sendContent;
                CustomRobotGroupMessage.sendGroupMessage(
                        adCustomRobotGroupType,
                        adCustomRobotGroupTarget,
                        atUserRoleType,
                        messageContent
                );
            }
        }

        // 广告主
        if (Objects.equals(userRole, AdUserRoleEnum.ADVERTISER_ROLE.getValue())) {
            if (StringUtils.isNotBlank(existingWorkOrder.getAdvertiserId())) {
                // 添加/修改备注
                // 菜鸟客服 & 广告主群里，@菜鸟客服
                String sendContent = "【客诉提醒】，" + existingWorkOrder.getMailNo() + "，客诉工单备注已更新，备注内容："+ request.getRemark() + "，请查收！";
                String adCustomRobotGroupType = AdCustomRobotGroupTypeEnum.CAINIAO_AND_ADVERTISER_DING_TALK_GROUP.getValue();
                String adCustomRobotGroupTarget = existingWorkOrder.getAdvertiserId();
                String atUserRoleType = AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue();
                String messageContent = sendContent;
                CustomRobotGroupMessage.sendGroupMessage(
                        adCustomRobotGroupType,
                        adCustomRobotGroupTarget,
                        atUserRoleType,
                        messageContent
                );
            }
        }

        // 快递公司
        if (Objects.equals(userRole, AdUserRoleEnum.COURIER_COMPANY_ROLE.getValue())) {
            if (StringUtils.isNotBlank(existingWorkOrder.getCpCode())) {
                // 添加/修改备注
                // 菜鸟客服 & 快递公司群里，@菜鸟客服
                String sendContent = "【客诉提醒】，" + existingWorkOrder.getMailNo() + "，客诉工单备注已更新，备注内容："+ request.getRemark() + "，请查收！";
                String adCustomRobotGroupType = AdCustomRobotGroupTypeEnum.CAINIAO_AND_COURIER_COMPANY_DING_TALK_GROUP.getValue();
                String adCustomRobotGroupTarget = existingWorkOrder.getCpCode();
                String atUserRoleType = AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue();
                String messageContent = sendContent;
                CustomRobotGroupMessage.sendGroupMessage(
                        adCustomRobotGroupType,
                        adCustomRobotGroupTarget,
                        atUserRoleType,
                        messageContent
                );
            }
        }
    }
}
