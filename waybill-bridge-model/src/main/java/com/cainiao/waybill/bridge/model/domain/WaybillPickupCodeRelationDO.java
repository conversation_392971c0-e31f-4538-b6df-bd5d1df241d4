package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * Description:
 * <AUTHOR>
 * Date 2017-04-26
 */
public class WaybillPickupCodeRelationDO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 修改者
     */
    private String modifier;

    /**
     * cp编码
     */
    private String cpCode;

    /**
     * 网点编码，查询和管理的维度
     */
    private String branchCode;

    /**
     * 面单号
     */
    private String waybillCode;

    /**
     * 揽件码
     */
    private String pickupCode;

    /**
     * 小件员id，分表字段
     */
    private Long courierId;

    /**
     * 打印状态，0 未打印，1 已打印
     */
    private Byte printStatus;

    /**
     * 商家id，附属信息
     */
    private Long sellerId;

    /**
     * 商家名称，冗余，展示字段
     */
    private String sellerName;

    /**
     * 店铺名称，冗余，查询条件
     */
    private String shopName;

    /**
     * 扩展字段,json格式
     */
    private String feature ;
    /**
     * 包裹重量，克为单位
     */
    private Integer weight;


    /**
     * setter for column 创建时间
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * getter for column 创建时间
     */
    public Date getGmtCreate() {
        return this.gmtCreate;
    }

    /**
     * setter for column 修改时间
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * getter for column 修改时间
     */
    public Date getGmtModified() {
        return this.gmtModified;
    }

    /**
     * setter for column 修改者
     */
    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    /**
     * getter for column 修改者
     */
    public String getModifier() {
        return this.modifier;
    }

    /**
     * setter for column cp编码
     */
    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    /**
     * getter for column cp编码
     */
    public String getCpCode() {
        return this.cpCode;
    }

    /**
     * setter for column 网点编码，查询和管理的维度
     */
    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    /**
     * getter for column 网点编码，查询和管理的维度
     */
    public String getBranchCode() {
        return this.branchCode;
    }

    /**
     * setter for column 面单号
     */
    public void setWaybillCode(String waybillCode) {
        this.waybillCode = waybillCode;
    }

    /**
     * getter for column 面单号
     */
    public String getWaybillCode() {
        return this.waybillCode;
    }

    /**
     * setter for column 揽件码
     */
    public void setPickupCode(String pickupCode) {
        this.pickupCode = pickupCode;
    }

    /**
     * getter for column 揽件码
     */
    public String getPickupCode() {
        return this.pickupCode;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCourierId() {
        return courierId;
    }

    public void setCourierId(Long courierId) {
        this.courierId = courierId;
    }

    public Byte getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(Byte printStatus) {
        this.printStatus = printStatus;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    /**
     * setter for column 商家名称，冗余，查询条件
     */
    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    /**
     * getter for column 商家名称，冗余，查询条件
     */
    public String getSellerName() {
        return this.sellerName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }
}
