package com.cainiao.waybill.bridge.enterprise.administrators.service.impl;

import com.cainiao.waybill.bridge.enterprise.administrators.request.EnterpriseSettleAccountRequest;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseSettleAccountDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseSettleAccountParam;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseLocationDO;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseLocationParam;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseSettleAccountDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseSettleAccountMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillEnterpriseLocationMapper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.fail;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.*;
import static org.testng.AssertJUnit.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class EnterpriseSettleAccountServiceImplTest {
    @InjectMocks
    private EnterpriseSettleAccountServiceImpl enterpriseSettleAccountService;
    @Mock
    private WaybillBridgeEnterpriseSettleAccountMapper enterpriseSettleAccountMapper;
    @Mock
    private WaybillEnterpriseLocationMapper enterpriseLocationMapper;

    /**
     * 测试：参数waybillAccountId为空或无效时抛出异常
     * 场景：waybillAccountId为null
     */
    @Test(expected = EnterpriseException.class)
    public void testGetByWaybillAccountIdAndCorpIdThrowsExceptionWhenWaybillAccountIdIsNull() {
        // 调用被测试方法，并预期抛出异常
        enterpriseSettleAccountService.getByWaybillAccountIdAndCorpId(null, "corpId");
    }

    /**
     * 测试：参数waybillAccountId为空或无效时抛出异常
     * 场景：waybillAccountId为空字符串
     */
    @Test(expected = EnterpriseException.class)
    public void testGetByWaybillAccountIdAndCorpIdThrowsExceptionWhenWaybillAccountIdIsEmpty() {
        // 调用被测试方法，并预期抛出异常
        enterpriseSettleAccountService.getByWaybillAccountIdAndCorpId("", "corpId");
    }

    /**
     * 测试：根据waybillAccountId和corpId创建查询条件
     * 场景：成功创建查询条件并调用mapper方法
     */
    @Test
    public void testGetByWaybillAccountIdAndCorpIdCreateQueryParam() {
        WaybillBridgeEnterpriseSettleAccountDO mockDO = new WaybillBridgeEnterpriseSettleAccountDO();
        mockDO.setWaybillAccountId("mockId");
        mockDO.setCorpId("mockCorpId");
        when(enterpriseSettleAccountMapper.selectOneByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class)))
                .thenReturn(mockDO);
        // 调用被测试方法，验证查询参数创建
        WaybillEnterpriseSettleAccountDTO result = enterpriseSettleAccountService.getByWaybillAccountIdAndCorpId("mockId", "mockCorpId");
        assertNotNull(result); // 验证返回结果不为空
    }

    /**
     * 测试：将数据库返回的数据转换为DTO并返回
     * 场景：转换成功并返回DTO对象
     */
    @Test
    public void testGetByWaybillAccountIdAndCorpIdConvertFromDO() {
        // 创建mock的DO对象并设置返回值
        WaybillBridgeEnterpriseSettleAccountDO mockDO = new WaybillBridgeEnterpriseSettleAccountDO();
        mockDO.setWaybillAccountId("mockId");
        mockDO.setCpCode("mockCode");
        // 模拟enterpriseSettleAccountMapper返回值
        when(enterpriseSettleAccountMapper.selectOneByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class))).thenReturn(mockDO);
        // 调用被测试方法
        WaybillEnterpriseSettleAccountDTO resultDTO = enterpriseSettleAccountService.getByWaybillAccountIdAndCorpId("waybillAccountId", "corpId");

        // 验证转换结果
        assertNotNull(resultDTO); // 验证转换后的DTO对象非空
        assertEquals("mockId", resultDTO.getWaybillAccountId()); // 验证DTO对应字段值正确
        assertEquals("mockCode", resultDTO.getCpCode()); // 验证DTO对应字段值正确

        // 验证转换逻辑
        // verify(WaybillEnterpriseSettleAccountConverter.class, times(1)).convertFromDO(mockDO); // 根据需要验证静态方法调用
    }

    /**
     * 测试：成功删除记录
     * 场景：根据waybillAccountId和corpId成功查询到记录并成功删除
     */
    @Test
    public void testRemoveByWaybillAccountIdSuccess() {
        // 创建mock的DO对象并设置返回值
        WaybillBridgeEnterpriseSettleAccountDO mockDO = new WaybillBridgeEnterpriseSettleAccountDO();
        mockDO.setWaybillAccountId("mockId");
        mockDO.setCpCode("mockCode");

        // 模拟enterpriseSettleAccountMapper的行为
        when(enterpriseSettleAccountMapper.selectOneByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class)))
                .thenReturn(mockDO);
        when(enterpriseSettleAccountMapper.deleteByPrimaryKey(anyLong())).thenReturn(1);

        // 调用被测试方法
        int result = enterpriseSettleAccountService.removeByWaybillAccountId("mockId", "corpId");

        // 验证调用结果
        Assert.assertEquals(1, result); // 验证删除操作返回值
        // verify(enterpriseSettleAccountMapper, times(1)).deleteByPrimaryKey(mockDO.getId()); // 根据需要验证删除操作参数
    }

    /**
     * 测试：未找到记录进行删除
     * 场景：根据waybillAccountId和corpId未查询到任何记录
     */
    @Test
    public void testRemoveByWaybillAccountIdRecordNotFound() {
        // 模拟enterpriseSettleAccountMapper的行为返回null
        when(enterpriseSettleAccountMapper.selectOneByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class)))
                .thenReturn(null);

        // 调用被测试方法
        int result = enterpriseSettleAccountService.removeByWaybillAccountId("missingId", "corpId");

        // 验证调用结果
        Assert.assertEquals(0, result); // 验证删除操作返回值0，表示无记录可删除
    }

    /**
     * 测试：创建企业结算账户对象并设置属性
     * 场景：指定的请求数据能够正确设置属性，并正常插入
     */
    @Test
    public void testAddSettleAccountCreatesAndSetsAttributes() {
        EnterpriseSettleAccountRequest request = new EnterpriseSettleAccountRequest();
        request.setCorpId("corpId");
        request.setWaybillAccountNo("waybillAccountNo");

        // Mock list方法返回空列表
        when(enterpriseSettleAccountMapper.selectByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class)))
                .thenReturn(Collections.emptyList());

        // Mock insert方法，模拟插入成功
        when(enterpriseSettleAccountMapper.insert(any(WaybillBridgeEnterpriseSettleAccountDO.class)))
                .thenReturn(1);
        // 调用要测试的方法
        int result = enterpriseSettleAccountService.addSettleAccount(request);
        // 验证结果
        Assert.assertEquals(1, result); // 插入成功返回1
        // 验证方法参数
        verify(enterpriseSettleAccountMapper, times(1)).insert(any(WaybillBridgeEnterpriseSettleAccountDO.class));
    }

    /**
     * 测试：更新操作过程中捕获异常并记录错误日志后抛出系统错误异常
     * 场景：在数据库更新时抛出异常
     */
    @Test(expected = EnterpriseException.class)
    public void testEditSettleAccountThrowsSystemErrorOnException() {
        // 创建请求参数对象
        EnterpriseSettleAccountRequest request = new EnterpriseSettleAccountRequest();
        request.setWaybillAccountId("validAccountId");
        request.setCorpId("validCorpId");
        // 创建mock的DO对象
        WaybillBridgeEnterpriseSettleAccountDO mockDO = new WaybillBridgeEnterpriseSettleAccountDO();
        // 模拟enterpriseSettleAccountMapper返回值
        when(enterpriseSettleAccountMapper.selectOneByParam(any())).thenReturn(mockDO);
        // 模拟更新操作抛出异常
        doThrow(new RuntimeException("Database error")).when(enterpriseSettleAccountMapper).updateByPrimaryKeySelective(any());
        // 调用被测试方法，并预期抛出系统错误异常
        enterpriseSettleAccountService.editSettleAccount(request);
    }

    /**
     * 测试：更新时所有操作成功则返回更新结果
     * 场景：成功执行数据库更新并返回结果
     */
    @Test
    public void testEditSettleAccountReturnsUpdateResultOnSuccess() {
        // 创建请求参数对象
        EnterpriseSettleAccountRequest request = new EnterpriseSettleAccountRequest();
        request.setWaybillAccountId("validAccountId");
        request.setCorpId("validCorpId");
        request.setWaybillAccountNo("accountNo");
        request.setCpCode("cpCode");
        request.setProduct(Collections.singletonList("product"));
        request.setRemark("remark");
        request.setSign("sign");
        request.setBusinessType(1);
        // 创建mock的DO对象
        WaybillBridgeEnterpriseSettleAccountDO mockDO = new WaybillBridgeEnterpriseSettleAccountDO();
        // 模拟enterpriseSettleAccountMapper返回值
        when(enterpriseSettleAccountMapper.selectOneByParam(any())).thenReturn(mockDO);
        // 模拟更新操作返回值
        when(enterpriseSettleAccountMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        // 调用被测试方法
        int result = enterpriseSettleAccountService.editSettleAccount(request);
        // 验证调用结果
        Assert.assertEquals(1, result); // 验证更新结果
    }

    /**
     * 测试：当waybillAccountId为空时抛出异常
     * 场景：传入的waybillAccountId为空
     */
    @Test(expected = EnterpriseException.class)
    public void testGetByWaybillAccountIdAndCorpIdWaybillAccountIdIsEmpty() {
        // 调用被测试方法，传递空的waybillAccountId，期待抛出EnterpriseException
        enterpriseSettleAccountService.getByWaybillAccountIdAndCorpId("", "corpId");
    }

    /**
     * 测试：调用mapper接口根据参数查询单个结果
     * 场景：根据参数可以查询到单条数据
     */
    @Test
    public void testGetByWaybillAccountIdAndCorpIdQuerySingleResult() {
        WaybillBridgeEnterpriseSettleAccountDO mockDO = new WaybillBridgeEnterpriseSettleAccountDO();
        mockDO.setWaybillAccountId("mockId");
        mockDO.setCorpId("mockCorpId");
        when(enterpriseSettleAccountMapper.selectOneByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class)))
                .thenReturn(mockDO);
        // 调用被测试方法，验证查询结果
        WaybillEnterpriseSettleAccountDTO result = enterpriseSettleAccountService.getByWaybillAccountIdAndCorpId("mockId", "corpId");
        Assert.assertNotNull(result); // 验证非空
    }

    /**
     * 测试：将查询结果转换为DTO对象并返回
     * 场景：查询数据后转换为DTO对象
     */
    @Test
    public void testGetByWaybillAccountIdAndCorpIdConvertResultToDTO() {
        WaybillBridgeEnterpriseSettleAccountDO mockDO = new WaybillBridgeEnterpriseSettleAccountDO();
        mockDO.setWaybillAccountId("mockId");
        mockDO.setCorpId("mockCorpId");
        when(enterpriseSettleAccountMapper.selectOneByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class)))
                .thenReturn(mockDO);
        // 调用被测试方法，验证转换为DTO
        WaybillEnterpriseSettleAccountDTO result = enterpriseSettleAccountService.getByWaybillAccountIdAndCorpId("mockId", "corpId");
        Assert.assertNotNull(result); // 验证非空
        Assert.assertEquals("mockId", result.getWaybillAccountId()); // 校验转换后的DTO内容
    }

    /**
     * 测试：成功查询到数据，进行更新操作
     * 场景：输入有效的waybillAccountId和corpId，成功查询到对应记录，执行更新
     */
    @Test
    public void testEditSettleAccountSuccess() {
        EnterpriseSettleAccountRequest request = new EnterpriseSettleAccountRequest();
        request.setWaybillAccountId("waybillAccountId");
        request.setCorpId("corpId");
        WaybillBridgeEnterpriseSettleAccountDO accountDO = new WaybillBridgeEnterpriseSettleAccountDO();
        when(enterpriseSettleAccountMapper.selectOneByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class)))
                .thenReturn(accountDO);
        when(enterpriseSettleAccountMapper.updateByPrimaryKeySelective(any(WaybillBridgeEnterpriseSettleAccountDO.class)))
                .thenReturn(1);
        int result = enterpriseSettleAccountService.editSettleAccount(request);
        assertEquals(1, result);  // 更新成功返回1
        // 验证是否执行了updateByPrimaryKeySelective
        verify(enterpriseSettleAccountMapper, times(1)).updateByPrimaryKeySelective(any(WaybillBridgeEnterpriseSettleAccountDO.class));
    }

    /**
     * 测试：更新操作中发生异常，将抛出系统错误
     * 场景：在执行更新操作时发生异常，抛出系统错误
     */
    @Test
    public void testEditSettleAccountThrowsSystemError() {
        EnterpriseSettleAccountRequest request = new EnterpriseSettleAccountRequest();
        request.setWaybillAccountId("waybillAccountId");
        request.setCorpId("corpId");
        WaybillBridgeEnterpriseSettleAccountDO accountDO = new WaybillBridgeEnterpriseSettleAccountDO();
        when(enterpriseSettleAccountMapper.selectOneByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class)))
                .thenReturn(accountDO);
        when(enterpriseSettleAccountMapper.updateByPrimaryKeySelective(any(WaybillBridgeEnterpriseSettleAccountDO.class)))
                .thenThrow(new RuntimeException("update error"));
        try {
            enterpriseSettleAccountService.editSettleAccount(request);
            fail("Should have thrown EnterpriseException");
        } catch (EnterpriseException e) {
            assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getErrorCode());
        }
        // 验证是否执行了updateByPrimaryKeySelective
        verify(enterpriseSettleAccountMapper, times(1)).updateByPrimaryKeySelective(any(WaybillBridgeEnterpriseSettleAccountDO.class));
    }

    /**
     * 测试：请求中有locationId并且查询到对应的地址信息，则设置enterpriseSettleAccountDO的locationId
     * 场景：enterpriseSettleAccountDO更新locationId
     */
    @Test
    public void testAddSettleAccountSetsLocationIdWhenLocationExists() {
        // 创建请求参数对象
        EnterpriseSettleAccountRequest request = new EnterpriseSettleAccountRequest();
        request.setLocationId("locationId");
        request.setCorpId("corpId");
        request.setWaybillAccountNo("accountNo");
        // 创建mock的LocationDO对象
        WaybillEnterpriseLocationDO locationDO = new WaybillEnterpriseLocationDO();
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(locationDO);
        // 模拟list操作返回空集合以通过新增判断
        when(enterpriseSettleAccountMapper.selectByParam(any())).thenReturn(Collections.emptyList());
        // 模拟插入操作返回值
        when(enterpriseSettleAccountMapper.insert(any())).thenReturn(1);
        // 调用被测试方法
        int result = enterpriseSettleAccountService.addSettleAccount(request);
        // 验证插入结果
        Assert.assertEquals(1, result);
        //verify(locationDO, times(1)).getId(); // 验证是否调用了getId方法
    }

    /**
     * 测试：尝试插入新的企业结算账户记录，如果发生异常则抛出系统异常
     * 场景：数据库插入时抛出异常
     */
    @Test(expected = EnterpriseException.class)
    public void testAddSettleAccountThrowsSystemErrorOnInsertException() {
        // 创建请求参数对象
        EnterpriseSettleAccountRequest request = new EnterpriseSettleAccountRequest();
        request.setWaybillAccountNo("accountNo");
        request.setCorpId("corpId");
        // 模拟list操作返回空集合以通过新增判断
        when(enterpriseSettleAccountMapper.selectByParam(any())).thenReturn(Collections.emptyList());
        // 模拟插入操作抛出异常
        doThrow(new RuntimeException("Insert error")).when(enterpriseSettleAccountMapper).insert(any());
        // 调用被测试方法，并预期抛出系统错误异常
        enterpriseSettleAccountService.addSettleAccount(request);
    }

    /**
     * 测试：检查LocationId是否为空并查询location信息
     * 场景：根据LocationId成功查询到Location信息并加入到条件中
     */
    @Test(expected = EnterpriseException.class)
    public void testPageListWithValidLocationId() {
        // 创建请求参数对象
        EnterpriseSettleAccountRequest request = new EnterpriseSettleAccountRequest();
        request.setLocationId("locationId");
        request.setCorpId("corpId");
        request.setCurrentPage(1);
        request.setPageSize(10);
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(null);
        // 调用被测试方法
        enterpriseSettleAccountService.pageList(request);
    }

    /**
     * 测试：根据waybillAccountId和corpId未查到数据时抛出账户不存在异常
     * 场景：数据不存在，需捕获异常
     */
    @Test(expected = EnterpriseException.class)
    public void testEditSettleAccountThrowsAccountNotExistErrorWhenDataNotFound() {
        // 模拟请求参数
        EnterpriseSettleAccountRequest request = new EnterpriseSettleAccountRequest();
        request.setWaybillAccountId("nonExistingId");
        request.setCorpId("corpId");
        // 模拟selectOneByParam返回null，表示未找到数据
        when(enterpriseSettleAccountMapper.selectOneByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class)))
                .thenReturn(null);
        // 调用测试方法，并预期抛出账户不存在的异常
        enterpriseSettleAccountService.editSettleAccount(request);
    }

    /**
     * 测试：更新过程中出现异常时抛出系统错误异常
     * 场景：数据库更新操作抛异常需捕获系统错误
     */
    @Test(expected = EnterpriseException.class)
    public void testEditSettleAccountThrowsSystemErrorOnUpdateException() {
        // 模拟请求参数
        EnterpriseSettleAccountRequest request = new EnterpriseSettleAccountRequest();
        request.setWaybillAccountId("existingId");
        request.setCorpId("corpId");
        // 模拟查找到数据对象
        WaybillBridgeEnterpriseSettleAccountDO existingDO = new WaybillBridgeEnterpriseSettleAccountDO();
        when(enterpriseSettleAccountMapper.selectOneByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class)))
                .thenReturn(existingDO);
        // 模拟updateByPrimaryKeySelective抛出异常
        doThrow(new RuntimeException("Update error"))
                .when(enterpriseSettleAccountMapper)
                .updateByPrimaryKeySelective(any(WaybillBridgeEnterpriseSettleAccountDO.class));
        // 调用测试方法，并预期抛出系统错误异常
        enterpriseSettleAccountService.editSettleAccount(request);
    }

    /**
     * 测试：当查询结果为null时抛出EnterpriseException异常
     * 场景：根据waybillAccountId和corpId无对应的账户记录
     */
    @Test(expected = EnterpriseException.class)
    public void testRemoveByWaybillAccountIdThrowsExceptionWhenAccountIsNull() {
        // 模拟mapper返回null，表示无对应记录
        when(enterpriseSettleAccountMapper.selectOneByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class)))
                .thenReturn(null);
        // 调用被测试方法并预期抛出异常
        enterpriseSettleAccountService.removeByWaybillAccountId("mockId", "mockCorpId");
    }

    /**
     * 测试：验证调用mapper接口进行删除操作
     * 场景：成功查询到结果并执行删除操作
     */
    @Test
    public void testRemoveByWaybillAccountIdExecutesDeleteOperation() {
        // 创建mock的DO对象并设置返回值
        WaybillBridgeEnterpriseSettleAccountDO mockDO = new WaybillBridgeEnterpriseSettleAccountDO();
        mockDO.setWaybillAccountId("mockId");
        mockDO.setCorpId("mockCorpId");
        mockDO.setId(1L); // 设置mock的ID用于删除
        // 模拟mapper查询返回mock对象
        when(enterpriseSettleAccountMapper.selectOneByParam(any(WaybillBridgeEnterpriseSettleAccountParam.class)))
                .thenReturn(mockDO);
        // 模拟delete操作返回值
        when(enterpriseSettleAccountMapper.deleteByPrimaryKey(mockDO.getId())).thenReturn(1);
        // 调用被测试方法
        int result = enterpriseSettleAccountService.removeByWaybillAccountId("mockId", "mockCorpId");
        // 验证删除操作被执行
        verify(enterpriseSettleAccountMapper, times(1)).deleteByPrimaryKey(mockDO.getId());
        // 验证返回值正确
        Assert.assertEquals(1, result);
    }

    /**
     * 测试：检查LocationId是否有效
     */
    @Test(expected = EnterpriseException.class)
    public void testListWithValidLocationId() {
        // 创建请求参数对象
        EnterpriseSettleAccountRequest request = new EnterpriseSettleAccountRequest();
        request.setLocationId("locationId");
        request.setCorpId("corpId");
        request.setCurrentPage(1);
        request.setPageSize(10);
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(null);
        // 调用被测试方法
        enterpriseSettleAccountService.list(request);
    }

    /**
     * 测试：编辑企业结算账户时账户不存在
     * 场景：数据库插入时抛出异常
     */
    @Test(expected = EnterpriseException.class)
    public void testEditSettleAccountWhenAccountNotExit() {
        EnterpriseSettleAccountRequest request = new EnterpriseSettleAccountRequest();
        request.setWaybillAccountNo("accountNo");
        request.setCorpId("corpId");
        when(enterpriseSettleAccountMapper.selectOneByParam(any())).thenReturn(null);
        // 调用被测试方法，并预期抛出系统错误异常
        enterpriseSettleAccountService.editSettleAccount(request);
    }
}