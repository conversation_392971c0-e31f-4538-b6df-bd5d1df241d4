package com.cainiao.waybill.bridge.innersupport.ad.config;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.cainiao.waybill.ad.api.service.WaybillAdTicketService;
import org.springframework.context.annotation.Configuration;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/12/5 14:54
 */
@Configuration
public class AdBeanConfig {

    @HSFConsumer(serviceVersion = "${ad.spring.hsf.version}")
    public WaybillAdTicketService waybillAdTicketService;

}
