package com.cainiao.waybill.bridge.common.label.dto;


import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * Created by guochao.tgc on 2016/6/3.
 */
public class Item extends BaseDTO {
    private static final long serialVersionUID = 4451620198948356234L;


    /**
     * 商品类型
     */
    private String            name;

    /**
     * 商品数量
     */
    private int               count;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Item)) {
            return false;
        }

        Item item = (Item) o;

        if (getCount() != item.getCount()) {
            return false;
        }
        return !(getName() != null ? !getName().equals(item.getName()) : item.getName() != null);

    }

    @Override
    public int hashCode() {
        int result = getName() != null ? getName().hashCode() : 0;
        result = 31 * result + getCount();
        return result;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }



}
