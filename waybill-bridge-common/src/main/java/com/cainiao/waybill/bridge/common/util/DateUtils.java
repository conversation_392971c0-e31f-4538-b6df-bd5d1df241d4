package com.cainiao.waybill.bridge.common.util;


import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 日期 工具类
 * <p>
 * Created by shouyuan.lzl on 2017-04-27 5:49 PM.
 */
public class DateUtils {

    public static String defaultPattern = "yyyy-MM-dd HH:mm:ss";

    public static String pattern24hh = "yyyy-MM-dd HH:mm:ss";

    public static String patternSimple24hh = "yyyyMMddHHmmss";

    // 24小时格式 数据库扩展字段使用
    public static String pattern24hhFeature = "yyyy-MM-dd HH-mm-ss";

    public static final String patternDsDay = "yyyy-MM-dd";

    public static final String patternMonth = "yyyyMM";

    /**
     * 生成当前时间的yyyy-MM-dd
     *
     * @return
     */
    public static String generateCurrentYMD() {
        Date date = new Date();
        return DateFormatUtils.format(date, patternDsDay);
    }

    public static String generateCurrentMonth() {
        Date date = new Date();
        return DateFormatUtils.format(date, patternMonth);
    }

    /**
     * 将数据库扩展日期字符串转换为默认日期格式
     * yyyy-MM-dd HH-mm-ss -> yyyy-MM-dd HH:mm:ss
     * @param dateStr
     * @return
     */
    public static String parseByDbPattern(String dateStr) {
        return parseByPattern(dateStr, pattern24hhFeature, pattern24hh);
    }

    /**
     * 将指定格式的日期字符串转换为指定格式的日期字符串
     * @param dateStr
     * @param oldPattern
     * @param newPattern
     * @return
     */
    public static String parseByPattern(String dateStr, String oldPattern, String newPattern) {
        if(StringUtils.isAnyBlank(dateStr, oldPattern, newPattern)){
            return null;
        }
        try {
            Date date = strToDate(dateStr, oldPattern);
            return dateToStr(date, newPattern);
        }catch (Exception e){
            return null;
        }
    }



    /**
     * 生成当前时间的前一天 yyyy-MM-dd
     * @return
     */
    public static String generateLastDayYMD() {
        Date date = addDay(new Date(), -1);
        return DateFormatUtils.format(date, patternDsDay);
    }

    /**
     * 生成当前时间 yyyy-MM-dd
     * @return
     */
    public static String generateCurrentDayYMD() {
        Date date = new Date();
        return DateFormatUtils.format(date, patternDsDay);
    }

    /**
     * 将指定格式的字符串转换为Date类型的时间
     *
     * @param str
     * @param pattern 制定的日期类型，null为默认"yyyy-MM-dd HH:mm:ss.SSS"；
     * @return
     * @throws ParseException
     */
    public static Date strToDate(String str, String pattern) throws ParseException {
        if(StringUtils.isBlank(str)){
            return null;
        }
        if (pattern == null) {
            pattern = "yyyy-MM-dd HH:mm:ss.SSS";
        }
        DateFormat ymdhmsFormat = new SimpleDateFormat(pattern);
        return ymdhmsFormat.parse(str);
    }

    public static Date strToDateStart(String str, String pattern) throws ParseException  {
        Date date = strToDate(str, pattern);
        return dateToDateStart(date) ;
    }

    public static Date strToDateEnd(String str, String pattern) throws ParseException {
        Date date = strToDate(str, pattern);
        return dateToDateEnd(date) ;
    }

    /**
     * 获取当前日期前n天
     * @param n
     * @return
     */
    public static Date getDateBeforeNDays(int n) {
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 计算n天前的日期
        LocalDate dateBeforeDays = today.minusDays(n);

        // 将 LocalDate 转换为 ZonedDateTime
        ZonedDateTime zonedDateTime = dateBeforeDays.atStartOfDay(ZoneId.systemDefault());
        // 将 ZonedDateTime 转换为 Date
        return Date.from(zonedDateTime.toInstant());
    }

    public static Date dateToDateStart(Date date) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);

        return calendar.getTime();
    }

    /**
     * 获取日期的结束时间(24时整)
     * @param date
     * @return
     */
    public static Date dateToDateEnd(Date date) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MILLISECOND, 999);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.HOUR_OF_DAY, 23);

        return calendar.getTime();
    }

    public static Date addDay(Date date,int i){
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE,i);
        return c.getTime();
    }

    public static String dateToString(Date date) {
        if(date == null){
            return null;
        }
        DateFormat df = new SimpleDateFormat(defaultPattern);
        return df.format(date);
    }

    public static String dateToStr(Date date) {
        if(date == null){
            return null;
        }
        DateFormat df = new SimpleDateFormat(pattern24hh);
        return df.format(date);
    }

    public static String dateToStr(Date date, String pattern) {
        if(date == null){
            return null;
        }
        DateFormat df = new SimpleDateFormat(pattern);
        return df.format(date);
    }

    public static long currentTime() {
        Date date = new Date();
        return date.getTime();
    }


    public static String dateToString(Date date,String pattern) {
    	return DateFormatUtils.format(date, pattern);
    }

    public static Calendar dateToCalendar(Date date){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar;
    }

    /**
     * 判断两个时间的月份间隔跨度
     * @param startDate
     * @param endDate
     * @return
     */
    public static int getIntervalOfMonth(Date startDate,Date endDate){

        Calendar start = dateToCalendar(startDate);
        Calendar end = dateToCalendar(endDate);

        int yearInterval =  end.get(Calendar.YEAR) - start.get(Calendar.YEAR);
        if(end.get(Calendar.MONTH) < start.get(Calendar.MONTH)
                || (end.get(Calendar.MONTH) == start.get(Calendar.MONTH)
                && end.get(Calendar.DATE) < start.get(Calendar.DATE))){
            yearInterval--;
        }

        int monthInterval = (end.get(Calendar.MONTH) + 12) - start.get(Calendar.MONTH);
        if(end.get(Calendar.DATE) < start.get(Calendar.DATE)){
            monthInterval--;
        }
        monthInterval %= 12;

        return Math.abs(yearInterval * 12 + monthInterval);
    }

    public static void main(String[] args) throws Exception{
        System.out.println(strToDate("2025-04-16 18:14:00", defaultPattern).getTime());
        System.out.println(strToDate("2025-06-01 00:00:00", defaultPattern).getTime());
        System.out.println(dateToStr(getLastHourDate(strToDate("2024-06-25 18:00:00", defaultPattern), 19)));
        System.out.println(dateToStr(getLastHourDate(strToDate("2024-06-25 20:00:00", defaultPattern), 19)));
        System.out.println(dateToStr(new Date(1745715600000L)));
        System.out.println(dateToStr(new Date(1745722800000L)));
        System.out.println(getLastMonthDsFormat());
    }

    /**
     * 获取两个日期的间隔天数数 求天数时除24小时即可
     * @param startDate
     * @param endDate
     * @return
     */
    public static double getIntervalOfDays(Date startDate,Date endDate){
        return getIntervalOfHour(startDate, endDate) / 24;
    }

    /**
     * 获取两个日期的间隔小时数
     * @param startDate
     * @param endDate
     * @return
     */
    public static double getIntervalOfHour(Date startDate,Date endDate){
        return getIntervalOfSecond(startDate, endDate) / (60 * 60);
    }

    /**
     * 获取两个日期的间隔秒数
     * @param startDate
     * @param endDate
     * @return
     */
    public static double getIntervalOfSecond(Date startDate,Date endDate){
        long startTime = startDate.getTime();
        long endTime = endDate.getTime();
        long intervalTime = (endTime >= startTime) ? (endTime - startTime):(startTime - endTime);
        return intervalTime * 1.0 / 1000;
    }

    /**
     * 是否今天
     */
    public static boolean isToday(Date date) {
        if (date == null) {
            return false;
        }
        final LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return LocalDate.now().isEqual(localDate);
    }

    public static Date addMinutes(Date originalDate, int minutes) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(originalDate);
        calendar.add(Calendar.MINUTE, minutes);
        return calendar.getTime();
    }

    public static Date addHours(Date originalDate, int hours) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(originalDate);
        calendar.add(Calendar.HOUR_OF_DAY, hours);
        return calendar.getTime();
    }

    /**
     * 获取指定时间n分钟的时间
     * @param originalTime
     * @param n
     * @return
     */
    public static LocalDateTime addMinutes(LocalDateTime originalTime, int n) {
        return originalTime.plusMinutes(n);
    }

    /**
     * 获取指定时间n小时的时间
     * @param originalTime
     * @param n
     * @return
     */
    public static LocalDateTime addHours(LocalDateTime originalTime, int n) {
        return originalTime.plusHours(n);
    }

    /**
     * 获取上个月起止时间
     * 第一天0时至最后一天24时
     * @return
     */
    public static Date[] getLastMonthStartAndEnd() {
        LocalDate today = LocalDate.now();

        // 获取上个月的第一天和最后一天
        LocalDate start = today.minusMonths(1).withDayOfMonth(1);
        LocalDate end = today.minusMonths(1).withDayOfMonth(today.minusMonths(1).lengthOfMonth());
        // 将 endDate 设置为上个月最后一天的 24:00，即下个月的第一天的 00:00
        LocalDate endDateAtMidnight = end.plusDays(1);
        // 转换为 Date 对象
        Date startDate = Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(endDateAtMidnight.atStartOfDay(ZoneId.systemDefault()).toInstant());

        return new Date[]{startDate, endDate};
    }

    /**
     * 格式化获取上个月每天的日期 格式化为yyyyMMdd
     * @return
     */
    public static List<String> getLastMonthDsFormat() {
        List<String> dates = new ArrayList<>();
        LocalDate today = LocalDate.now();
        // 获取当月的第一天
        LocalDate firstDayOfCurrentMonth = today.withDayOfMonth(1);
        // 计算上个月的第一天
        LocalDate firstDayOfLastMonth = firstDayOfCurrentMonth.minusMonths(1);
        // 计算上个月的最后一天（当月的第一天减1天）
        LocalDate lastDayOfLastMonth = firstDayOfCurrentMonth.minusDays(1);

        // 遍历上个月的每一天
        LocalDate currentDate = firstDayOfLastMonth;
        while (!currentDate.isAfter(lastDayOfLastMonth)) {
            // 格式化为 yyyyMMdd
            String formattedDate = currentDate.format(DateTimeFormatter.BASIC_ISO_DATE);
            dates.add(formattedDate);
            currentDate = currentDate.plusDays(1);
        }

        return dates;
    }

    /**
     * 日期中小时值超过指定值设置为指定小时
     * @param sourceDate
     * @return
     */
    public static Date getLastHourDate(Date sourceDate, int h){
        if (sourceDate == null || h < 0) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sourceDate);

        int hour = calendar.get(Calendar.HOUR_OF_DAY);

        if (hour > h) {
            calendar.set(Calendar.HOUR_OF_DAY, h);
        }

        return calendar.getTime();
    }

    /**
     * 判断当前时间是否在指定时间段内
     * @param startDate
     * @param endDate
     * @return
     */
    public static boolean isCurrentTimeBetween(Date startDate, Date endDate) {
        if(startDate == null || endDate == null) {
            return false;
        }
        Date currentDate = new Date();

        if (endDate.before(startDate)) {
            return false;
        } else {
            return !currentDate.before(startDate) && !currentDate.after(endDate);
        }
    }
}
