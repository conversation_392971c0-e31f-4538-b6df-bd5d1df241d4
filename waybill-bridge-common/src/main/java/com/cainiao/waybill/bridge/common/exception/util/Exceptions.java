package com.cainiao.waybill.bridge.common.exception.util;


import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.base.Constants.ErrorConstant;

/**
 * Created by nut on 16/2/25.
 */
public class Exceptions {

    private static String format(String errorMessagePattern, Object... valuesForErrorMessagePlaceHolders) {
        // 将valuesForErrorMessagePlaceHolders中的值替换到到errorMessage中的占位符中,占位符使用slf4j的格式
        return ExceptionMessageFormatter.arrayFormat(errorMessagePattern, valuesForErrorMessagePlaceHolders);
    }

    /**
     * 可如此使用:throw Exceptions.newBridgeValidatorException("BALANCE_NOT_ENOUGH",
     * "account balance is not enough for cpid:{},seller_id:{},branch_code:{}",101,12345,54321);
     * 
     * @return
     */
    public static BridgeValidationException newBridgeValidatorException(String errorCode, String errorMessagePattern, Object... valuesForErrorMessagePlaceHolders) {
        String replacedErrorMessage = format(errorMessagePattern, valuesForErrorMessagePlaceHolders);
        return new BridgeValidationException(errorCode, replacedErrorMessage);
    }

    public static BridgeValidationException newBridgeValidatorException(ErrorConstant errorConstant) {
        return new BridgeValidationException(errorConstant.getErrorCode(), errorConstant.getErrorMsg());
    }

    /**
     * 注意,该方法是业务操作捕获到异常以后抛出的,一定有cause
     * 
     * @param errorCode
     * @param errorMessagePattern
     * @param cause
     * @param valuesForErrorMessagePlaceHolders
     * @return
     */
    public static BridgeBaseException newBridgeBaseException(String errorCode, String errorMessagePattern, Throwable cause, Object... valuesForErrorMessagePlaceHolders) {
        String replacedErrorMessage = format(errorMessagePattern, valuesForErrorMessagePlaceHolders);
        return new BridgeBaseException(errorCode, replacedErrorMessage, cause);
    }

    /**
     * 注意,该方法是调用第三方接口返回ERROR_CODE,ERROR_MESSAGE后抛出的,用于代表第三方接口异常,所以没有cause.
     * 
     * @param errorCode
     * @param errorMessagePattern
     * @param valuesForErrorMessagePlaceHolders
     * @return
     */
    public static BridgeBaseException newBridgeBaseException(String errorCode, String errorMessagePattern, Object... valuesForErrorMessagePlaceHolders) {
        String replacedErrorMessage = format(errorMessagePattern, valuesForErrorMessagePlaceHolders);
        return new BridgeBaseException(errorCode, replacedErrorMessage);
    }

    public static BridgeBaseException newBridgeBaseException(ErrorConstant errorConstant, Object... valuesForErrorMessagePlaceHolders) {
        return newBridgeBaseException(errorConstant.getErrorCode(), errorConstant.getErrorMsg(), valuesForErrorMessagePlaceHolders);
    }

    public static BridgeBaseException newBridgeBaseException(ErrorConstant errorConstant, Throwable cause, Object... valuesForErrorMessagePlaceHolders) {
        return newBridgeBaseException(errorConstant.getErrorCode(), errorConstant.getErrorMsg(), cause, valuesForErrorMessagePlaceHolders);
    }

}
