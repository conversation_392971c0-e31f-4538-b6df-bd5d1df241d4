package com.cainiao.waybill.bridge.biz.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.cainiao.waybill.bridge.biz.delaytask.PickUpAutoSwitchCpDTO;
import com.cainiao.waybill.bridge.biz.delaytask.PickUpAutoSwitchCpTaskProcess;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.biz.hsf.PickUpLcTestService;
import com.cainiao.waybill.bridge.biz.hsf.bean.FixGotInfoBean;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.middleware.metaq.WaybillPickUpGuoguoEventConsumer;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.TraceFeatureKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpDetailStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.dto.lite.LitePreChargeResultDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.RoutingInfo;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillAutoSwitchCpManager;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.routing.dto.RouteBaseRequest;
import com.cainiao.waybill.bridge.biz.pickup.routing.manager.RoutingManager;
import com.cainiao.waybill.bridge.biz.pickup.service.LitePreChargeService;
import com.cainiao.waybill.bridge.biz.ticket.dto.PagingResponse;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpOrderDetailDTO;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpOrderListQueryRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.TicketRoleEnum;
import com.cainiao.waybill.bridge.biz.ticket.service.PickUpOrderService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCpCodeUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Cp;
import com.cainiao.waybill.bridge.biz.pickup.lc.event.handler.PickUpSignedEventHandler;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpFeatureUtil;
import com.cainiao.waybill.bridge.biz.wrapper.WaybillPickUpTairWrapper;
import com.cainiao.waybill.bridge.common.constants.LogisticStatusEnum;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEventBuilder;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.util.BridgeDateUtil;
import com.cainiao.waybill.bridge.common.util.CpCodeAndBrandCodeConverter;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.common.waybill.constants.WaybillPickUpActionConstant;
import com.cainiao.waybill.bridge.common.waybill.pickup.dto.LogisticSubscribeDTO;
import com.cainiao.waybill.bridge.common.waybill.pickup.service.LogisticSubscribeService;
import com.cainiao.waybill.bridge.common.waybill.pickup.service.PickUpEventOuterDispatcher;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpTicketDO;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpDetailMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpTicketMapper;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import com.cainiao.waybill.common.util.FeatureUtils;
import com.cainiao.waybill.galaxy.order.domain.dto.BaseResult;
import com.cainiao.waybill.galaxy.order.domain.dto.CloudPrintApplyInfosResponse;
import com.cainiao.waybill.bridge.common.waybill.pickup.service.WaybillPickUpEventSender;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.cainiao.waybill.common.util.FeatureUtils;
import com.taobao.logisticsdetail.common.domain.TraceDetailDO;
import com.taobao.logisticsdetail.common.domain.basic.LogisticsDetailDO;
import com.taobao.logisticsdetail.common.domain.basic.LogisticsDetailQueryOptionDO;
import com.taobao.logisticsdetail.common.domain.result.SingleResult;
import com.taobao.logisticsdetail.common.service.basic.LogisticsDetailReadService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.HashMap;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/6/18
 */
@HSFProvider(serviceInterface = PickUpLcTestService.class)
public class PickUpLcTestServiceImpl implements PickUpLcTestService {

    private LogisticSubscribeService logisticSubscribeService;
    private LogisticsDetailReadService logisticsDetailReadService;
    private PickUpEventOuterDispatcher pickUpEventOuterDispatcher;
    private WaybillPickUpEventSender waybillPickUpEventSender;

    @Resource
    private WaybillPickUpOrderManager waybillPickUpOrderManager;

    @Autowired
    private WaybillPickUpOrderManager pickUpOrderManager;

    @Autowired
    private WaybillAutoSwitchCpManager waybillAutoSwitchCpManager;

    @Autowired
    private WaybillPickUpDetailMapper waybillPickUpDetailMapper;

    @Autowired
    private PickUpAutoSwitchCpTaskProcess taskProcess;

    @Resource
    private RoutingManager routingManager;

    @Resource
    private WaybillPickUpGuoguoEventConsumer guoguoEventConsumer;

    @Autowired
    private PickUpOrderService pickUpOrderService;

    @Resource
    private LitePreChargeService litePreChargeService;

    @Resource
    private WaybillPickUpTicketMapper waybillPickUpTicketMapper;

    @Autowired
    WaybillPickUpTairWrapper waybillPickUpTairWrapper;

    private static final String WEIGHT_ATTR_KEY = "trace_weight";

    // 工单缓存过期时间为 14天
    private static final Integer EXPIRE_TIME = 1000 * 60 * 60 * 24 * 14;

    @Autowired
    public PickUpLcTestServiceImpl(LogisticSubscribeService logisticSubscribeService,
        LogisticsDetailReadService logisticsDetailReadService,
        PickUpEventOuterDispatcher pickUpEventOuterDispatcher,
        WaybillPickUpEventSender waybillPickUpEventSender) {
        this.logisticSubscribeService = logisticSubscribeService;
        this.logisticsDetailReadService = logisticsDetailReadService;
        this.pickUpEventOuterDispatcher = pickUpEventOuterDispatcher;
        this.waybillPickUpEventSender = waybillPickUpEventSender;
    }

    @Override
    public BaseResultDTO<Void> lcSubscribe(LogisticSubscribeDTO subscribeDTO) {
        return logisticSubscribeService.subscribe(subscribeDTO);
    }

    @Override
    public BaseResultDTO<SingleResult<List<LogisticsDetailDO>>> queryWaybillLCDetail(String mailNo, String cpCode) {
        LogisticsDetailQueryOptionDO option = new LogisticsDetailQueryOptionDO();
        option.setAppName(BridgeSwitch.lcHsfInvokeAppName);
        option.setActor("RECEIVER");
        SingleResult<List<LogisticsDetailDO>> lcDetailResult = logisticsDetailReadService.queryDetailListByMailNo(
            mailNo,
            cpCode, option);

        BaseResultDTO<SingleResult<List<LogisticsDetailDO>>> ans = new BaseResultDTO<>();
        ans.setModule(lcDetailResult);
        ans.setSuccess(true);
        return ans;
    }

    // 实现 querySignWaybillCode，查询面单物流详情并判断是否签收
    @Override
    public List<String> querySignWaybillCode(List<String> mailNoList) {
        List<String> signMailNoList = Lists.newArrayList();
        for (String mailNo : mailNoList) {
            WaybillPickUpDetailDO pickUpDetailDO = waybillPickUpOrderManager.get(mailNo);
            if(pickUpDetailDO == null){
                continue;
            }
            String cpCode = pickUpDetailDO.getCpCode();
            if (cpCode.equals(Cp.GUOGUO.name()) || cpCode.equals(Cp.FHD.name())) {
                Map<String, String> featureMap = FeatureUtils.parseFromString(pickUpDetailDO.getFeature());
                cpCode = featureMap.get(PickUpConstants.TraceFeatureKey.REAL_CP_CODE);
                cpCode = CpCodeAndBrandCodeConverter.cpCodeToBrandCode(BridgeSwitch.logisticsDetailCpCodeAndBrandCodeMapping, cpCode);
            }
            LogisticsDetailQueryOptionDO option = new LogisticsDetailQueryOptionDO();
            option.setAppName(BridgeSwitch.lcHsfInvokeAppName);
            option.setActor("RECEIVER");
            SingleResult<List<LogisticsDetailDO>> lcDetailResult = logisticsDetailReadService.queryDetailListByMailNo(
                mailNo, cpCode, option);
            if(lcDetailResult != null && lcDetailResult.isSuccess()){
                for (LogisticsDetailDO logisticsDetailDO : ListUtil.non(lcDetailResult.getResult())) {
                    if (logisticsDetailDO.getStatus() != null && StringUtils.equals(logisticsDetailDO.getStatus().getStatusCode(),"SIGN")) {
                        signMailNoList.add(mailNo);
                    }
                }
            }
        }
        return signMailNoList;
    }

    @Override
    public String getLcWeight(String mailNo, String cpCode) {
        BaseResultDTO<SingleResult<List<LogisticsDetailDO>>> resultDTO = queryWaybillLCDetail(mailNo, cpCode);
        SingleResult<List<LogisticsDetailDO>> singleResult = resultDTO.getModule();
        List<LogisticsDetailDO> logisticsDetailDOList = singleResult.getResult();
        return getWeight(logisticsDetailDOList);
    }

    private String getWeight(List<LogisticsDetailDO> detailDOS) {
        if (CollectionUtils.isEmpty(detailDOS)) {
            return null;
        }
        for (LogisticsDetailDO detailDO : detailDOS) {
            List<TraceDetailDO> traceDetailDOS = detailDO.getDetailList();
            if (CollectionUtils.isEmpty(traceDetailDOS)) {
                continue;
            }
            for (TraceDetailDO traceDetailDO : traceDetailDOS) {
                if (!Objects.equals(traceDetailDO.getStatus(), LogisticStatusEnum.ACCEPT.name())) {
                    continue;
                }
                String weightStr = traceDetailDO.getWeight();
                if (StringUtils.isNotBlank(weightStr)) {
                    return weightStr;
                }
                if (traceDetailDO.getAttr() != null) {
                    weightStr = traceDetailDO.getAttr().get(WEIGHT_ATTR_KEY);
                    if (StringUtils.isNotBlank(weightStr)) {
                        return weightStr;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public BaseResultDTO<Void> waybillPackPubInvoke(WaybillPickUpEvent pickUpEvent) {
        return pickUpEventOuterDispatcher.dispatch(pickUpEvent);
    }

    @Override
    public BaseResultDTO<List<String>> replayGot(List<String> mailNoList) throws Exception {
        List<String> failMailNoList = Lists.newArrayList();
        for (String mailNo : mailNoList) {
            try {
                WaybillPickUpDetailDO detailDO = pickUpOrderManager.get(mailNo);
                if (detailDO == null) {
                    failMailNoList.add(mailNo);
                    continue;
                }
                WaybillPickUpEvent pickUpEvent = new WaybillPickUpEvent();
                pickUpEvent.setCpCode(PickUpCpCodeUtil.expressToPlatformCpCode(detailDO));
                pickUpEvent.setMailNo(detailDO.getMailNo());
                pickUpEvent.setResCode(detailDO.getResCode());
                pickUpEvent.setOuterOrderCode(detailDO.getOuterOrderCode());
                pickUpEvent.setAction(WaybillPickUpActionConstant.GOT);
                pickUpEvent.setActionDesc(WaybillPickUpActionConstant.getActionDesc(WaybillPickUpActionConstant.GOT));
                pickUpEvent.setOrderChannel(detailDO.getOrderChannels());

                LogisticsDetailQueryOptionDO option = new LogisticsDetailQueryOptionDO();
                option.setAppName(BridgeSwitch.lcHsfInvokeAppName);
                option.setActor("RECEIVER");
                SingleResult<List<LogisticsDetailDO>> lcDetailResult
                    = logisticsDetailReadService.queryDetailListByMailNo(
                    mailNo, pickUpEvent.getCpCode(), option);
                if (lcDetailResult.isSuccess() && CollectionUtils.isNotEmpty(lcDetailResult.getResult())
                    && CollectionUtils.isNotEmpty(lcDetailResult.getResult().get(0).getDetailList())) {
                    pickUpEvent.setLastActionDetail(lcDetailResult.getResult().get(0).getDetailList().get(0).getDesc());
                }

                Map<String, String> map = FeatureUtils.parseFromString(detailDO.getFeature());
                pickUpEvent.setLinkCpCode(map.get(TraceFeatureKey.LINK_CP_CODE));
                Date gotTime = PickUpFeatureUtil.parseTime(map.get(PickUpConstants.TraceFeatureKey.GOT_TIME));
                if (gotTime != null) {
                    pickUpEvent.setActionGmtModified(gotTime.getTime());
                }

                String weight = map.get(PickUpConstants.TraceFeatureKey.CP_WEIGHT);

                LitePreChargeResultDTO priceDetail = litePreChargeService.getPriceDetail(detailDO, weight);

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("weight", weight);
                if (priceDetail != null) {
                    jsonObject.putAll(PickUpFeatureUtil.originJsonStrToMap(JSON.toJSONString(priceDetail)));
                }

                pickUpEvent.setExtraInfo(jsonObject.toJSONString());

                BaseResultDTO<Void> dispatchResult = pickUpEventOuterDispatcher.dispatch(pickUpEvent);
                if (dispatchResult == null || dispatchResult.isFailure()) {
                    failMailNoList.add(mailNo);
                }

            } catch (Exception exception) {
                failMailNoList.add(mailNo);
                PickUpLogUtil.info("reply_got_error, mailNo:{} ", mailNo, exception);
            }
        }

        BaseResultDTO<List<String>> baseResultDTO = new BaseResultDTO<>();
        baseResultDTO.setModule(failMailNoList);

        PickUpLogUtil.info("reply_got_result, mailNoList:{} ", JSON.toJSONString(failMailNoList));
        return baseResultDTO;
    }

    @Override
    public BaseResultDTO<List<String>> replayModifyWeight(List<String> mailNoList) throws Exception {
        List<String> failMailNoList = Lists.newArrayList();
        for (String mailNo : mailNoList) {
            try {
                WaybillPickUpDetailDO detailDO = pickUpOrderManager.get(mailNo);
                if (detailDO == null) {
                    failMailNoList.add(mailNo);
                    continue;
                }
                WaybillPickUpEvent pickUpEvent = new WaybillPickUpEvent();
                pickUpEvent.setCpCode(PickUpCpCodeUtil.expressToPlatformCpCode(detailDO));
                pickUpEvent.setMailNo(detailDO.getMailNo());
                pickUpEvent.setResCode(detailDO.getResCode());
                pickUpEvent.setOuterOrderCode(detailDO.getOuterOrderCode());
                pickUpEvent.setAction(WaybillPickUpActionConstant.MODIFY_WEIGHT);
                pickUpEvent.setActionDesc(WaybillPickUpActionConstant.getActionDesc(WaybillPickUpActionConstant.MODIFY_WEIGHT));
                pickUpEvent.setOrderChannel(detailDO.getOrderChannels());

                LogisticsDetailQueryOptionDO option = new LogisticsDetailQueryOptionDO();
                option.setAppName(BridgeSwitch.lcHsfInvokeAppName);
                option.setActor("RECEIVER");
                SingleResult<List<LogisticsDetailDO>> lcDetailResult
                        = logisticsDetailReadService.queryDetailListByMailNo(
                        mailNo, pickUpEvent.getCpCode(), option);
                if (lcDetailResult.isSuccess() && CollectionUtils.isNotEmpty(lcDetailResult.getResult())
                        && CollectionUtils.isNotEmpty(lcDetailResult.getResult().get(0).getDetailList())) {
                    pickUpEvent.setLastActionDetail(lcDetailResult.getResult().get(0).getDetailList().get(0).getDesc());
                }

                Map<String, String> map = FeatureUtils.parseFromString(detailDO.getFeature());

                if (map.get(TraceFeatureKey.CP_OLD_WEIGHT) == null) {
                    failMailNoList.add(mailNo);
                    PickUpLogUtil.info("reply_modify_weight_invalid, mailNo:{} ", mailNo);
                    continue;
                }

                pickUpEvent.setLinkCpCode(map.get(TraceFeatureKey.LINK_CP_CODE));
                Date modifyTime = PickUpFeatureUtil.parseTime(map.get(TraceFeatureKey.CP_WEIGHT_HAPPEN_TIME));
                if (modifyTime != null) {
                    pickUpEvent.setActionGmtModified(modifyTime.getTime());
                }

                String weight = map.get(PickUpConstants.TraceFeatureKey.CP_WEIGHT);

                LitePreChargeResultDTO priceDetail = litePreChargeService.getPriceDetail(detailDO, weight);

                JSONObject jsonObject = new JSONObject();
                jsonObject.put("weight", weight);
                if (priceDetail != null) {
                    jsonObject.putAll(PickUpFeatureUtil.originJsonStrToMap(JSON.toJSONString(priceDetail)));
                }

                pickUpEvent.setExtraInfo(jsonObject.toJSONString());

                BaseResultDTO<Void> dispatchResult = pickUpEventOuterDispatcher.dispatch(pickUpEvent);
                if (dispatchResult == null || dispatchResult.isFailure()) {
                    failMailNoList.add(mailNo);
                    PickUpLogUtil.info("reply_modify_weight_fail, mailNo:{} ", mailNo);
                }

            } catch (Exception exception) {
                failMailNoList.add(mailNo);
                PickUpLogUtil.info("reply_modify_weight_error, mailNo:{} ", mailNo, exception);
            }
        }

        BaseResultDTO<List<String>> baseResultDTO = new BaseResultDTO<>();
        baseResultDTO.setModule(failMailNoList);

        PickUpLogUtil.info("reply_modify_weight_result, mailNoList:{} ", JSON.toJSONString(failMailNoList));
        return baseResultDTO;
    }

    @Override
    public LitePreChargeResultDTO gotPrice(String mailNo) throws Exception {
        final WaybillPickUpDetailDO detailDO = pickUpOrderManager.get(mailNo);
        final Map<String, String> map = FeatureUtils.parseFromString(detailDO.getFeature());
        final String weight = map.get(PickUpConstants.TraceFeatureKey.CP_WEIGHT);
        return litePreChargeService.getPriceDetail(detailDO, weight);
    }

    @Override
    public BaseResult<String> createSwitchCpGotCheckTriggerTask(List<String> mailNoList, Date triggerTime)
        throws Exception {
        for (String mailNo : mailNoList) {
            WaybillPickUpDetailDO detailDO = pickUpOrderManager.get(mailNo);

            Map<String, String> featureMap = FeatureUtils.parseFromString(detailDO.getFeature());
            String branchCode = featureMap.get(PickUpConstants.TraceFeatureKey.ACCEPT_ORG_CODE);
            if(StringUtils.isBlank(branchCode)){
                //  调用分拣码接口，获取接单网点
                RouteBaseRequest routeBaseRequest = new RouteBaseRequest();
                routeBaseRequest.setCpCode(detailDO.getCpCode());
                //  都用寄件地址去算
                routeBaseRequest.setReceiveAddress(new AddressDTO(detailDO.getConsigneeAddress()));
                routeBaseRequest.setSendAddress(new AddressDTO(detailDO.getSendAddress()));

                RoutingInfo routingInfo = routingManager.calculateRoutingInfo(routeBaseRequest);
                branchCode = routingInfo.getReceiveBranchCode();
            }

            PickUpAutoSwitchCpDTO pickUpAutoSwitchCpDTO = PickUpAutoSwitchCpDTO.builder()
                .orderCreateDate(detailDO.getGmtCreate())
                .cpCode(detailDO.getCpCode())
                .resCode(detailDO.getResCode())
                .outerOrderCode(detailDO.getOuterOrderCode())
                .senderAddress(new AddressDTO(detailDO.getSendAddress()))
                .consignAddress(new AddressDTO(detailDO.getConsigneeAddress()))
                .acceptBranchCode(branchCode)
                .bizType(detailDO.getBizType())
                .triggerTime(triggerTime)
                .appointGotStartTime(detailDO.getAppointGotStartTime())
                .appointGotEndTime(detailDO.getAppointGotEndTime()).build();
            waybillAutoSwitchCpManager.publishSwitchTask(pickUpAutoSwitchCpDTO);
        }
        return null;
    }

    @Override
    public BaseResult<Map<String, Boolean>> switchToCp(List<String> mailNoList, Boolean triggerNow, String switchCpCode) {
        Map<String, Boolean> map = new HashMap<>();
        for (String mailNo : mailNoList) {
            WaybillPickUpDetailDO detailDO = pickUpOrderManager.get(mailNo);

            try{
                boolean result = taskProcess.process(detailDO.getResCode(), detailDO.getOuterOrderCode(), switchCpCode);
                map.put(mailNo, result);
            }catch (Throwable throwable){
                PickUpLogUtil.errLog(mailNo,"process_to_guoguo_error",  "error", "处理失败", throwable);
                map.put(mailNo, false);
            }
        }
        BaseResult<Map<String, Boolean>> baseResult = new BaseResult<>();
        baseResult.setData(map);
        return baseResult;
    }

    @Override
    public BaseResult<Void> fixCpCode(Long id, String cpCode) {
        WaybillPickUpDetailDO detailDO = new WaybillPickUpDetailDO();
        detailDO.setCpCode(cpCode);
        detailDO.setId(id);
        waybillPickUpDetailMapper.updateById(detailDO);
        return null;
    }

    @Override
    public BaseResult<Void> fixOutOrderId(Long id, String outOrderId) {
        WaybillPickUpDetailDO detailDO = new WaybillPickUpDetailDO();
        detailDO.setOuterOrderCode(outOrderId);
        detailDO.setId(id);
        waybillPickUpDetailMapper.updateById(detailDO);
        return new BaseResult<>();
    }

    @Override
    public BaseResult<Void> fixMailNoAndCpCode(Long id, String mailNo, String cpCode, String realCpCode) {
        List<WaybillPickUpDetailDO> list = waybillPickUpDetailMapper.selectByIdList(com.google.common.collect.Lists.newArrayList(id));
        for (WaybillPickUpDetailDO waybillPickUpDetailDO : ListUtil.non(list)) {
            WaybillPickUpDetailDO detailDO = new WaybillPickUpDetailDO();
            detailDO.setMailNo(mailNo);
            detailDO.setCpCode(cpCode);
            detailDO.setId(waybillPickUpDetailDO.getId());

            if(StringUtils.isNotBlank(realCpCode)){
                Map<String, String> featureMap = FeatureUtils.parseFromString(waybillPickUpDetailDO.getFeature());
                featureMap.put(PickUpConstants.TraceFeatureKey.REAL_CP_CODE, realCpCode);
                detailDO.setFeature(FeatureUtils.parseFromMap(featureMap));
            }
            waybillPickUpDetailMapper.updateById(detailDO);
        }
        return BaseResult.success(null);
    }

    @Override
    public BaseResult<Void> fixStatus(Long id, int status) {
        WaybillPickUpDetailDO detailDO = new WaybillPickUpDetailDO();
        detailDO.setId(id);
        detailDO.setStatus(status);
        waybillPickUpDetailMapper.updateById(detailDO);

        return BaseResult.success(null);
    }

    @Override
    public BaseResult<Void> fixFeature(Long id, String feature) {
        WaybillPickUpDetailDO detailDO = new WaybillPickUpDetailDO();
        detailDO.setId(id);
        detailDO.setFeature(feature);
        waybillPickUpDetailMapper.updateById(detailDO);
        return BaseResult.success(null);
    }

    @Override
    public BaseResult<Void> fixFeature(List<FixGotInfoBean> list) {
        int num = 0;
        for (FixGotInfoBean fixGotInfoBean : ListUtil.non(list)) {
            WaybillPickUpDetailDO detailDO = waybillPickUpDetailMapper.selectByMailNo(fixGotInfoBean.getMailNo());
            if(detailDO == null){
                continue;
            }
            Map<String, String> featureMap = FeatureUtils.parseFromString(detailDO.getFeature());
            featureMap.put(PickUpConstants.TraceFeatureKey.CP_WEIGHT, fixGotInfoBean.getWeight());
            String date = PickUpFeatureUtil.formatTime(BridgeDateUtil.strToDate(fixGotInfoBean.getGotTime()));
            featureMap.put(PickUpConstants.TraceFeatureKey.CP_WEIGHT_HAPPEN_TIME, date);
            featureMap.put(PickUpConstants.TraceFeatureKey.GOT_TIME, date);
            featureMap.put(PickUpConstants.TraceFeatureKey.GOT_PUSH_TIME, date);

            WaybillPickUpDetailDO updateDO = new WaybillPickUpDetailDO();
            updateDO.setId(detailDO.getId());
            updateDO.setFeature(FeatureUtils.featureMapToString(featureMap));
            waybillPickUpDetailMapper.updateById(updateDO);
            num ++;
        }
        return null;
    }

    @Override
    public BaseResultDTO<String> replaySignLogistics(String mailNo) throws ParseException {
        PickUpLogUtil.info("replaySignLogistics mailNo:{}", mailNo);
        BaseResultDTO<String> baseResultDTO = new BaseResultDTO<>();

        WaybillPickUpDetailDO pickUpDetailDO = waybillPickUpOrderManager.get(mailNo);
        if(pickUpDetailDO == null){
            baseResultDTO.setSuccess(false);
            baseResultDTO.addErrorMessage("order_not_found", "订单不存在");
            return baseResultDTO;
        }
        String cpCode = pickUpDetailDO.getCpCode();
        if (cpCode.equals(Cp.GUOGUO.name()) || cpCode.equals(Cp.FHD.name())) {
            Map<String, String> featureMap = FeatureUtils.parseFromString(pickUpDetailDO.getFeature());
            cpCode = featureMap.get(PickUpConstants.TraceFeatureKey.REAL_CP_CODE);
            cpCode = CpCodeAndBrandCodeConverter.cpCodeToBrandCode(BridgeSwitch.logisticsDetailCpCodeAndBrandCodeMapping,
                cpCode);
        }


        LogisticsDetailQueryOptionDO option = new LogisticsDetailQueryOptionDO();
        option.setAppName(BridgeSwitch.lcHsfInvokeAppName);
        option.setActor("RECEIVER");
        SingleResult<List<LogisticsDetailDO>> lcDetailResult = logisticsDetailReadService.queryDetailListByMailNo(
            mailNo,
            cpCode, option);
        if (!lcDetailResult.isSuccess() || CollectionUtils.isEmpty(lcDetailResult.getResult())) {
            PickUpLogUtil.info("replay_sign_logistics_detail_null: " + mailNo + ", detail: " + lcDetailResult.getResult());

            baseResultDTO.setSuccess(false);
            baseResultDTO.addErrorMessage(lcDetailResult.getResultCode(), lcDetailResult.getResultDesc());
            return baseResultDTO;
        }

        TraceDetailDO traceDetail = null;
        for (LogisticsDetailDO logisticsDetailDO : lcDetailResult.getResult()) {
            if (logisticsDetailDO.getStatus().getStatusCode().equals("SIGN")) {
                for (TraceDetailDO traceDetailDO : logisticsDetailDO.getDetailList()) {
                    if (traceDetailDO.getStatus().equals("SIGN")) {
                        traceDetail = traceDetailDO;
                    }
                }
            }
        }
        if (traceDetail == null) {
            PickUpLogUtil.info("replay_sign_logistics_not_sign: " + mailNo);

            baseResultDTO.setSuccess(false);
            baseResultDTO.addErrorMessage("package_not_sign", "包裹未签收");
            return baseResultDTO;
        }

        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        WaybillPickUpEventBuilder ansBuilder = WaybillPickUpEventBuilder.builder()
            .action("SIGN")
            .cpCode(cpCode)
            .mailNo(mailNo)
            .actionDesc(traceDetail.getStatusDesc())
            .lastActionDetail(traceDetail.getStanderdDesc())
            .actionGmtModified(format.parse(traceDetail.getOcurrTimeStr()).getTime())
            .outerOrderCode(pickUpDetailDO.getOuterOrderCode())
            .resCode(pickUpDetailDO.getResCode())
            .linkCpCode(
                PickUpFeatureUtil.getFromMulString(pickUpDetailDO, PickUpConstants.TraceFeatureKey.LINK_CP_CODE));

        waybillPickUpEventSender.send(ansBuilder.build());
        PickUpLogUtil.info("replay_sign_logistics: " + mailNo);

        baseResultDTO.setSuccess(true);
        baseResultDTO.setModule(JSON.toJSONString(ansBuilder.build()));
        return baseResultDTO;
    }

    @Override
    public void replaySignLogistics(List<String> mailNoList) throws ParseException {
        PickUpLogUtil.info("replaySignLogistics mailNoList:{}", mailNoList);
        for (String mailNo : mailNoList) {
            replaySignLogistics(mailNo);
        }
    }

    @Override
    public void replayGgMsg(String msg, String tag, int reconsumeTimes) throws BridgeBaseException, ParseException {
        PickUpLogUtil.info("replayGgMsg msg:{},tag:{},reconsumeTimes:{}",msg, tag, reconsumeTimes);
        guoguoEventConsumer.processMsg(msg, tag, reconsumeTimes);
    }

    @Override
    public BaseResultDTO<String> judgeFakeGotMailNoList(String orderChannel, String startTime, String endTime, List<String> gotStatusList, int overHour) {

        PickUpOrderListQueryRequest queryRequest = new PickUpOrderListQueryRequest();
        queryRequest.setOrderChannels(orderChannel);
        queryRequest.setStartTime(startTime);
        queryRequest.setEndTime(endTime);
        queryRequest.setOrderStatus(PickUpDetailStatusEnum.GOT.getValue());
        queryRequest.setCurrentPage(1);
        queryRequest.setPageSize(1000);
        queryRequest.setRole(TicketRoleEnum.admin.name());
        List<String> fakeGotMailNoList = Lists.newArrayList();

        List<String> exceptionMailNoList = Lists.newArrayList();
        while(true){
            PagingResponse<PickUpOrderDetailDTO> pagingResponse = pickUpOrderService.queryOrderList(queryRequest);

            PickUpLogUtil.info("fake_got_query_order: " + queryRequest.getCurrentPage() + ", total: " + pagingResponse.getPaging().getTotalCount());

            for (PickUpOrderDetailDTO tableDatum : ListUtil.non(pagingResponse.getTableData())) {
                boolean result = false;
                try{
                    result = judgeFakeGot(tableDatum.getCpCode(), tableDatum.getRealCpCode(), tableDatum.getMailNo(), gotStatusList, overHour);
                }catch (Throwable throwable){
                    PickUpLogUtil.errLog("", "fake_got_query_ld_exception", "fake_got_query_ld_exception", "", throwable);
                    exceptionMailNoList.add(tableDatum.getMailNo());
                }
                if(result){
                    fakeGotMailNoList.add(tableDatum.getMailNo());
                }
            }

            if(queryRequest.getCurrentPage() >= pagingResponse.getPaging().getTotalPage()
            || pagingResponse.getTableData().size() < queryRequest.getPageSize()){
                break;
            }
            queryRequest.setCurrentPage(queryRequest.getCurrentPage() + 1);
        }
        PickUpLogUtil.info("fakeGotMailNoList: " + fakeGotMailNoList);
        PickUpLogUtil.info("exceptionMailNoList: " + exceptionMailNoList);
        BaseResultDTO<String> baseResultDTO = new BaseResultDTO<>();
        return baseResultDTO;
    }

    private boolean judgeFakeGot(String cpCode, String realCpCode, String mailNo, List<String> gotStatusList, int overHour){
        if(StringUtils.isNotBlank(realCpCode)){
            cpCode = realCpCode;
        }

        BaseResultDTO<String> baseResultDTO = new BaseResultDTO<>();

        LogisticsDetailQueryOptionDO option = new LogisticsDetailQueryOptionDO();
        option.setAppName(BridgeSwitch.lcHsfInvokeAppName);
        option.setActor("RECEIVER");
        SingleResult<List<LogisticsDetailDO>> lcDetailResult = logisticsDetailReadService.queryDetailListByMailNo(
            mailNo,
            cpCode, option);
        if (!lcDetailResult.isSuccess() || CollectionUtils.isEmpty(lcDetailResult.getResult())) {
            PickUpLogUtil.info("replay_sign_logistics_detail_null: " + mailNo + ", detail: " + lcDetailResult.getResult());

            baseResultDTO.setSuccess(false);
            baseResultDTO.addErrorMessage(lcDetailResult.getResultCode(), lcDetailResult.getResultDesc());
            return true;
        }

        TraceDetailDO traceDetail = null;
        String gotTime = "";
        for (LogisticsDetailDO logisticsDetailDO : lcDetailResult.getResult()) {
            for (TraceDetailDO traceDetailDO : logisticsDetailDO.getDetailList()) {
                if (gotStatusList.contains(traceDetailDO.getStatus())) {
                    gotTime = traceDetailDO.getOcurrTimeStr();
                   continue;
                }
                traceDetail = traceDetailDO;
            }
        }
        Date gotDate = BridgeDateUtil.strToDate(gotTime, BridgeDateUtil.pattern24hh);
        return traceDetail == null && gotDate != null && gotDate.before(DateUtils.addHours(new Date(), -1 * overHour));
    }

    @Override
    public BaseResultDTO<Void> insertPickUpTicketNoToTair(List<Long> ids) {

        // 查询出无花果的所有工单
        for (Long id: ids) {
            WaybillPickUpTicketDO waybillPickUpTicketDO = waybillPickUpTicketMapper.selectByPrimaryKey(id);
            String extraInfo = waybillPickUpTicketDO.getExtraInfo();
            JSONObject extraInfoJson = new JSONObject();
            if (StringUtil.isNotBlank(extraInfo)) {
                extraInfoJson = JSON.parseObject(extraInfo);
                Object taskNo = extraInfoJson.get(TraceFeatureKey.TASK_NO);
                if (taskNo == null) {
                    continue;
                }
                waybillPickUpTairWrapper.put("WHG_TICKET_" + taskNo, String.valueOf(waybillPickUpTicketDO.getId()), EXPIRE_TIME);

            }
        }
        BaseResultDTO<Void> baseResultDTO = new BaseResultDTO<>();
        baseResultDTO.setSuccess(true);
        return baseResultDTO;
    }
}
