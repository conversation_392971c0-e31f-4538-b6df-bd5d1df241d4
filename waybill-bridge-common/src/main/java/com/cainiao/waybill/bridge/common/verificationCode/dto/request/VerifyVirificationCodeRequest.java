package com.cainiao.waybill.bridge.common.verificationCode.dto.request;


import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 *
 * 验证短信验证码的request dto
 *
 * Created by shouyuan.lzl on 2017-04-25 2:09 PM.
 */
public class VerifyVirificationCodeRequest extends BaseDTO {

    private static final long serialVersionUID = -9039758990077619661L;
    /**
     * 商家id, 必填
     */
    private Long sellerId ;

    /**
     * 快递员id， 必填
     */
    private Long courierId ;

    /**
     * 快递员电话，必填
     */
    private String courierMobile ;

    /**
     * 短信验证码，必填
     */
    private String verificationCode ;

    /**
     * 验证码类型 1 商家和快递员绑定申请的验证码， 必填
     */
    private Integer verificationCodeType ;

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public Long getCourierId() {
        return courierId;
    }

    public void setCourierId(Long courierId) {
        this.courierId = courierId;
    }

    public String getCourierMobile() {
        return courierMobile;
    }

    public void setCourierMobile(String courierMobile) {
        this.courierMobile = courierMobile;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public Integer getVerificationCodeType() {
        return verificationCodeType;
    }

    public void setVerificationCodeType(Integer verificationCodeType) {
        this.verificationCodeType = verificationCodeType;
    }
}
