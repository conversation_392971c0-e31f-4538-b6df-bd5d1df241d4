package com.cainiao.waybill.bridge.enterprise.utils;


import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/20 16:19
 */
public class DateUtil {

    public static String defaultPattern = "yyyy-MM-dd HH:mm:ss";


    public static String pattern24Format = "yyyyMMddHHmmss";

    public static String patternDate = "MMdd";

//
//    public static String dateToString(Date date) {
//        DateFormat df = new SimpleDateFormat(defaultPattern);
//        return df.format(date);
//    }

    public static String dateToStr(Date date, String pattern24Format) {
        DateFormat df = new SimpleDateFormat(pattern24Format);
        return df.format(date);
    }
//
//    public static Date strToDate(String str) {
//        if (StringUtils.isBlank(str)) {
//            return null;
//        }
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(defaultPattern);
//        try {
//            return simpleDateFormat.parse(str);
//        } catch (ParseException e) {
//            return null;
//        }
//    }
//
//    /**
//     * 判断两个时间的月份间隔跨度
//     *
//     * @param startDate
//     * @param endDate
//     * @return
//     */
//    public static int getIntervalOfMonth(Date startDate, Date endDate) {
//
//        Calendar start = dateToCalendar(startDate);
//        Calendar end = dateToCalendar(endDate);
//
//        int yearInterval = end.get(Calendar.YEAR) - start.get(Calendar.YEAR);
//        if (end.get(Calendar.MONTH) < start.get(Calendar.MONTH)
//                || (end.get(Calendar.MONTH) == start.get(Calendar.MONTH)
//                && end.get(Calendar.DATE) < start.get(Calendar.DATE))) {
//            yearInterval--;
//        }
//
//        int monthInterval = (end.get(Calendar.MONTH) + 12) - start.get(Calendar.MONTH);
//        if (end.get(Calendar.DATE) < start.get(Calendar.DATE)) {
//            monthInterval--;
//        }
//        monthInterval %= 12;
//
//        return Math.abs(yearInterval * 12 + monthInterval);
//    }
//
//    public static Calendar dateToCalendar(Date date) {
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(date);
//        return calendar;
//    }
//
//    public static Date dateToDateStart(Date date) {
//
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(date);
//        calendar.set(Calendar.MILLISECOND, 0);
//        calendar.set(Calendar.SECOND, 0);
//        calendar.set(Calendar.MINUTE, 0);
//        calendar.set(Calendar.HOUR_OF_DAY, 0);
//
//        return calendar.getTime();
//    }
//
//    public static Date dateToDateEnd(Date date) {
//
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(date);
//        calendar.set(Calendar.MILLISECOND, 0);
//        calendar.set(Calendar.SECOND, 59);
//        calendar.set(Calendar.MINUTE, 59);
//        calendar.set(Calendar.HOUR_OF_DAY, 23);
//
//        return calendar.getTime();
//    }
//
//    public static Date addDay(Date date, int i) {
//        Calendar c = Calendar.getInstance();
//        c.setTime(date);
//        c.add(Calendar.DATE, i);
//        return c.getTime();
//    }
//
//    public static int getYear() {
//        LocalDate today = LocalDate.now();
//        return today.getYear();
//    }


}
