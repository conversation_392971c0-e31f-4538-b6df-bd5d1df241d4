package com.cainiao.waybill.bridge.common.base;

/**
 * <AUTHOR> zouping.fzp
 * @Classname ScenarioConstant
 * @Description
 * @Date 2022/12/15 4:37 下午
 * @Version 1.0
 */
public interface ScenarioConstant {

    /**
     * 创建订单
     */
    String CREATE_ORDER = "CREATE_ORDER";

    String UPDATE_ORDER = "UPDATE_ORDER";


    String EXCEPTION_ORDER = "EXCEPTION_ORDER";

    String ORDER_CALL_BACK = "ORDER_CALL_BACK";

    String OVER_TIME_AUTO_TASK = "OVER_TIME_AUTO_TASK";

    String FHD_ORDER_NOT_EXITS_CANCEL_ERROR = "FHD_ORDER_NOT_EXITS_CANCEL_ERROR";

    String WAYBILL_PICK_UP_LOGISTICS_DETAIL_QUERY = "WAYBILL_PICK_UP_LOGISTICS_DETAIL_QUERY";

    String WAYBILL_PICK_UP_ORDER_INFO_PUSH = "WAYBILL_PICK_UP_ORDER_INFO_PUSH";
    /**
     * 物流详情查询
     */
    String WAYBILL_PICK_UP_LOGISTICS_DETAIL_GET = "WAYBILL_PICK_UP_LOGISTICS_DETAIL_GET";

    /**
     * 物流详情轨迹订阅
     */
    String WAYBILL_LOGISTICS_DETAIL_SUB = "WAYBILL_LOGISTICS_DETAIL_SUB";

    /**
     * 物流详情轨迹推送
     */
    String WAYBILL_LOGISTICS_DETAIL_PUB = "WAYBILL_LOGISTICS_DETAIL_PUB";

    /**
     * HSF下单
     */
    String TMS_WAYBILL_HSF_CREATE_ORDER = "TMS_WAYBILL_HSF_CREATE_ORDER";

    /**
     * 算法方案方案保鲜
     */
    String ALGORITHM_SCHEME_FRESHNESS = "ALGORITHM_SCHEME_FRESHNESS";

    /**
     * 内部LINK下单
     */
    String TMS_WAYBILL_LINK_CREATE_ORDER = "TMS_WAYBILL_LINK_CREATE_ORDER";

}
