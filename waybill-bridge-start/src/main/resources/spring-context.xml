<?xml version="1.0" encoding="utf-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.0.xsd  
            http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.0.xsd  
            http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.0.xsd"
	default-autowire="byName">

	<import resource="classpath*:/spring/biz-notify.xml" />
	<import resource="classpath*:/spring/biz-service.xml"/>
	<import resource="classpath*:/dbconfig/applicationContext-model.xml"/>
	<import resource="classpath*:/spring/biz-manager.xml"/>
	<import resource="classpath*:/spring/biz-rc.xml"/>
	<import resource="classpath*:/spring/biz-pac.xml"/>
	<import resource="classpath*:/spring/biz-iss.xml"/>

</beans>
