package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastvalidator.constraints.exception.FastValidatorException;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.exception.PickUpBusinessException;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpCancelOrderRequest;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillPickUpOrderService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_CANCEL.WaybillPickUpCancelRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PICK_UP_CANCEL.WaybillPickUpCancelResponse;
import com.taobao.pac.client.sdk.receiveservice.WaybillPickUpCancelService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/6/25 下午3:36
 */
public class TmsWaybillPickUpCancelServiceImpl implements WaybillPickUpCancelService {
    private static final String WEI_DIAN_CHANNEL = "WEIDIAN";
    private static final String WEI_DIAN_APP_KEY = "156650";

    @Resource
    private WaybillPickUpOrderService waybillPickUpOrderService;

    @Resource
    private WaybillPickUpOrderManager waybillPickUpOrderManager;

    @Override
    public WaybillPickUpCancelResponse invoke(ReceiveParams<WaybillPickUpCancelRequest> receiveParams) {
        PickUpLogUtil.info("淘外订单取消入参："+JSON.toJSONString(receiveParams));
        PickUpCancelOrderRequest request = new PickUpCancelOrderRequest();
        BeanUtils.copyProperties(receiveParams.getRequestDataObject(), request);
        request.setFromAppKey(receiveParams.getFromAppkey());
        request.setOrderChannels(receiveParams.getRequestDataObject().getOrderChannels());
        WaybillPickUpCancelResponse linkResponse = new WaybillPickUpCancelResponse();
        if (StringUtil.isBlank(receiveParams.getRequestDataObject().getCancelDesc())) {
            linkResponse.setSuccess(false);
            linkResponse.setErrorCode(PickUpConstants.Error.LINK_ORDER_CANCEL_DESC_EMPTY.getErrorCode());
            linkResponse.setErrorMsg(PickUpConstants.Error.LINK_ORDER_CANCEL_DESC_EMPTY.getErrorMsg());
            return linkResponse;
        }
        try {
            String orderChannels = receiveParams.getRequestDataObject().getOrderChannels();
            String resCode;
            //  微点使用的是修改前的API，修改前API是没有orderChannels字段的，阿里云上线前的历史订单resCode任然为fromAppKey，上线后需要拼接默认orderChannels
            if (WEI_DIAN_APP_KEY.equals(receiveParams.getFromAppkey())) {
                WaybillPickUpDetailDO detailDO = waybillPickUpOrderManager.get(receiveParams.getFromAppkey(), request.getOuterOrderCode());
                PickUpLogUtil.info("cancel detailDo : " + JSON.toJSONString(detailDO));
                if (detailDO == null) {
                    //  未查到则可能是阿里云上线后的订单，此时需要拼接默认orderChannels
                    resCode = PickUpCommonUtil.buildResCode(receiveParams.getFromAppkey(), WEI_DIAN_CHANNEL);
                } else {
                    resCode = receiveParams.getFromAppkey();
                }
            } else {
                //  阿里云与后续平台，会传orderChannels，直接使用
                resCode = PickUpCommonUtil.buildResCode(receiveParams.getFromAppkey(), orderChannels);
            }

            request.setResCode(resCode);
            waybillPickUpOrderService.cancel(request);
            PickUpLogUtil.info("cancel suc");
        } catch (FastValidatorException e) {
            linkResponse.setSuccess(false);
            linkResponse.setErrorMsg(e.getCode());
            linkResponse.setMessage(e.getCode());
            PickUpLogUtil.errLog(request.getOuterOrderCode(), PickUpConstants.Action.PICK_UP_CANCEL_LINK.name(), "", e.getMessage(), e);
        } catch (BridgeBaseException e) {
            linkResponse.setSuccess(false);
            linkResponse.setErrorCode(e.getErrorCode());
            linkResponse.setErrorMsg(e.getErrorMessage());
            PickUpLogUtil.errLog(request.getOuterOrderCode(), PickUpConstants.Action.PICK_UP_CANCEL_LINK.name(),
                    e.getErrorCode(), e.getErrorMessage(), e);
        } catch (PickUpBusinessException pickUpBusinessException){
            linkResponse.setSuccess(false);
            linkResponse.setErrorCode(pickUpBusinessException.getErrorCode());
            linkResponse.setErrorMsg(pickUpBusinessException.getErrorMessage());
            PickUpLogUtil.errLog(request.getOuterOrderCode(), PickUpConstants.Action.PICK_UP_CANCEL_LINK.name(),
                    pickUpBusinessException.getErrorCode(), pickUpBusinessException.getErrorMessage(), pickUpBusinessException);
        } catch (BridgeBusinessException bridgeBusinessException) {
            linkResponse.setSuccess(false);
            linkResponse.setErrorCode(bridgeBusinessException.getErrorCode());
            linkResponse.setErrorMsg(bridgeBusinessException.getErrorMessage());
            PickUpLogUtil.errLog(request.getOuterOrderCode(), PickUpConstants.Action.PICK_UP_CANCEL_LINK.name(),
                    bridgeBusinessException.getErrorCode(), bridgeBusinessException.getErrorMessage(), bridgeBusinessException);
        } catch (Throwable th) {
            linkResponse.setSuccess(false);
            linkResponse.setErrorMsg(th.getMessage());
            linkResponse.setMessage(th.getMessage());
            PickUpLogUtil.errLog(request.getOuterOrderCode(), PickUpConstants.Action.PICK_UP_CANCEL_LINK.name(), "", th.getMessage(), th);
        }
        PickUpLogUtil.info(JSON.toJSONString(linkResponse));
        return linkResponse;
    }
}
