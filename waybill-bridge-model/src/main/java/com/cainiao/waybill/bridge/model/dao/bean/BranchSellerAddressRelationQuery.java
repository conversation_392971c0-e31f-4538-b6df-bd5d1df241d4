package com.cainiao.waybill.bridge.model.dao.bean;

import java.util.Date;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-28
 */
public class BranchSellerAddressRelationQuery {

    /**
     * cp 编码
     */
    private String cpCode;

    /**
     * 网点编码
     */
    private String branchCode;

    /**
     * 合作日期查询起始值
     */
    private Date cooperationDateStart;

    /**
     * 合作日期查询结束值
     */
    private Date cooperationDateEnd;

    /**
     * 商家名称，支持最左匹配
     */
    private String sellerNamePrefix;

    /**
     * 商家名称，支持最左匹配
     */
    private String shopNamePrefix;

    /**
     * 小件员id
     */
    private Long courierId;

    /**
     * 合作状态
     * 0:取消合作
     * 1:合作中
     */
    private Byte cooperationStatus;

    public String getCpCode() {
        return cpCode;
    }

    public BranchSellerAddressRelationQuery setCpCode(String cpCode) {
        this.cpCode = cpCode;
        return this;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public BranchSellerAddressRelationQuery setBranchCode(String branchCode) {
        this.branchCode = branchCode;
        return this;
    }

    public Date getCooperationDateStart() {
        return cooperationDateStart;
    }

    public BranchSellerAddressRelationQuery setCooperationDateStart(Date cooperationDateStart) {
        this.cooperationDateStart = cooperationDateStart;
        return this;
    }

    public Date getCooperationDateEnd() {
        return cooperationDateEnd;
    }

    public BranchSellerAddressRelationQuery setCooperationDateEnd(Date cooperationDateEnd) {
        this.cooperationDateEnd = cooperationDateEnd;
        return this;
    }

    public String getSellerNamePrefix() {
        return sellerNamePrefix;
    }

    public BranchSellerAddressRelationQuery setSellerNamePrefix(String sellerNamePrefix) {
        this.sellerNamePrefix = sellerNamePrefix;
        return this;
    }

    public Long getCourierId() {
        return courierId;
    }

    public BranchSellerAddressRelationQuery setCourierId(Long courierId) {
        this.courierId = courierId;
        return this;
    }

    public Byte getCooperationStatus() {
        return cooperationStatus;
    }

    public BranchSellerAddressRelationQuery setCooperationStatus(Byte cooperationStatus) {
        this.cooperationStatus = cooperationStatus;
        return this;
    }

    public String getShopNamePrefix() {
        return shopNamePrefix;
    }

    public BranchSellerAddressRelationQuery setShopNamePrefix(String shopNamePrefix) {
        this.shopNamePrefix = shopNamePrefix;
        return this;
    }
}
