package com.cainiao.waybill.bridge.model.dao.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.common.util.Page;
import com.cainiao.waybill.bridge.model.common.AbstractDAO;
import com.cainiao.waybill.bridge.model.dao.WaybillPickupCodeRelationDAO;
import com.cainiao.waybill.bridge.model.dao.bean.PickupCodeRelationQuery;
import com.cainiao.waybill.bridge.model.domain.WaybillPickupCodeRelationDO;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant;
import com.taobao.common.dao.persistence.exception.DAOException;
import com.taobao.tddl.client.sequence.exception.SequenceException;
import com.taobao.tddl.client.sequence.impl.GroupSequence;
import org.apache.commons.lang.StringUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-27
 */
public class WaybillPickupCodeRelationDAOImpl extends AbstractDAO implements WaybillPickupCodeRelationDAO {

    @Resource
    private GroupSequence waybillPickupCodeRelationSequence;

    @Override
    public WaybillPickupCodeRelationDO queryByWaybillCode(Long courierId, String cpCode, String waybillCode) throws DAOException {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("courierId", courierId);
        paramMap.put("cpCode", cpCode);
        paramMap.put("waybillCode", waybillCode);

        return (WaybillPickupCodeRelationDO) executeQueryForObject("waybillPickupCodeRelation.selectByWaybillCode", paramMap, getTDDLDBRoute());
    }

    @Override
    public Page<WaybillPickupCodeRelationDO> queryPickupWaybillWithPage(PickupCodeRelationQuery query, int pageNo, int pageSize) throws DAOException {
        Long courierId = query.getCourierId();
        Date createDateStart = query.getCreateDateStart();
        Date createDateEnd = query.getCreateDateEnd();
        Byte printStatus = query.getPrintStatus();
        String cpCode = query.getCpCode();
        String branchCode = query.getBranchCode();
        String pickupCode = query.getPickupCode();
        String shopName = query.getShopName();

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("courierId", courierId);
        if (StringUtils.isNotBlank(cpCode) && StringUtils.isNotBlank(pickupCode)) {
            paramMap.put("cpCode", cpCode);
            paramMap.put("branchCode", branchCode);
        }
        if (createDateStart != null && createDateEnd != null) {
            paramMap.put("createDateStart", createDateStart);
            paramMap.put("createDateEnd", createDateEnd);
        }
        if (StringUtils.isNotBlank(pickupCode)) {
            paramMap.put("pickupCode", pickupCode);
        }
        if (printStatus != null) {
            paramMap.put("printStatus", printStatus);
        }
        if (StringUtils.isNotBlank(shopName)) {
            paramMap.put("shopNamePrefix", shopName.trim());
        }

        return executeQueryForPageWithCount("waybillPickupCodeRelation.selectByMultiConditions", paramMap, pageSize, pageNo, getTDDLDBRoute());
    }

    @Override
    public Integer insert(WaybillPickupCodeRelationDO relationDO) throws DAOException {
        relationDO.setId(nextId());
        return this.executeUpdate("waybillPickupCodeRelation.insert", relationDO, getTDDLDBRoute());
    }

    @Override
    public Integer updatePrintStatusById(Long id, Long courierId, Byte printStatus) throws DAOException {
        Map<String, Object> paramMap = new HashMap<>(3);

        paramMap.put("id", id);
        paramMap.put("courierId", courierId);
        paramMap.put("printStatus", printStatus);
        return this.executeUpdate("waybillPickupCodeRelation.update", paramMap, getTDDLDBRoute());
    }

    @Override
    public Integer updateWeightById(Long id, Long courierId, Integer weight) throws DAOException {
        Map<String, Object> paramMap = new HashMap<>(3);

        paramMap.put("id", id);
        paramMap.put("courierId", courierId);
        paramMap.put("weight", weight);

        return this.executeUpdate("waybillPickupCodeRelation.updateWeight", paramMap, getTDDLDBRoute());
    }

    //通过TDDL获取唯一主键
    private Long nextId() throws DAOException {
        try {
            return waybillPickupCodeRelationSequence.nextValue();
        } catch (SequenceException e) {
            throw new DAOException(WaybillErrorConstant.SystemError.TDDL_SEQUENCE_ERROR.getErrorMsg(), e);
        } catch (Throwable e) {
            throw new DAOException(e);
        }
    }
}
