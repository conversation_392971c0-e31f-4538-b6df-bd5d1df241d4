package com.cainiao.waybill.bridge.biz.link.pickup.service.impl.cprouting;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.Data;
import org.apache.commons.compress.utils.Lists;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpIndicatorConfig
 * @Description
 * @Date 2022/10/26 10:47 下午
 * @Version 1.0
 */
@Data
public class PickUpIndicatorConfig implements Serializable {

    private static final long serialVersionUID = -1364841176530948009L;

    /**
     * 灰度发件地址
     */
    List<String> graySenderAreaList;

    /**
     * 灰度量
     */
    Integer grayRate;

    /**
     * 是否长期指标
     * 指标时间段前缀
     */
    Boolean longIndicator;

    /**
     * 指标时间段前缀
     */
    String indicatorPeriodPrefix;

    /**
     * 指标权重
     */
    Map<String, Integer> indicatorWeight;

    /**
     * 质量权重
     */
    Integer serviceQualityWeight;

    /**
     * 成本权重
     */
    Integer costWeight;

    /**
     * 期望投诉率
     */
    Double expectComplainRate;

    /**
     * 相比最低价上浮比例
     */
    Double costUpLimitRate;

    /**
     * cp投诉率均值
     */
    Map<String, Double> cpAverageComplainRate;

    /**
     * 质量优先
     */
    Integer qualityFirstRate = 100;

    /**
     * 排除掉的cpCode列表
     */
    List<String> excludeCpList = Lists.newArrayList();
}
