package com.cainiao.waybill.bridge.common.exception;


import com.cainiao.waybill.bridge.common.base.Constants;

/**
 * 校验异常,包含参数校验和业务校验
 */
public class BridgeValidationException extends BridgeBaseException {

    private static final long serialVersionUID = 1L;

    public BridgeValidationException(String errorCode, String errorMessage, Throwable cause) {
        super(errorCode, errorMessage, cause);
    }

    public BridgeValidationException(String errorCode, String errorMessage) {
        super(errorCode, errorMessage);
    }

    public BridgeValidationException(Constants.ErrorConstant errorConstant) {
        super(errorConstant.getErrorCode(), errorConstant.getErrorMsg());
    }


    /**
     * 此处干掉了错误堆栈,因为本异常为直接抛出,只需要errorCode和errorMessage,并且没有cause
     *
     * @return
     */
    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }
}
