package com.cainiao.waybill.bridge.enterprise.outbound.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 该类用于处理访问令牌的响应结果。
 * <AUTHOR>
 * @date 2025-04-15 11:01:40
 */
@Data
public class OrderPickUpPushRequest implements Serializable {

    private static final long serialVersionUID = 7991047277665943995L;

    /**
     * 用户Id
     */
    private String userId;

    /**
     * 企业Id
     */
    private String corpId;

    /**
     * cp的名称
     */
    private String cpName;

    /**
     * 取件码
     *
     */
    private String pickupCode;

    /**
     * 运单号
     *
     */
    private String waybillCode;
}
