<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>淘外工作台</title>
    <link
            ref="shortcut icon"
            type="image/png"
            href="//img.alicdn.com/tfs/TB1CZ0ARpXXXXX7XXXXXXXXXXXX-32-32.png"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="dns-prefetch" href="https://img.alicdn.com" />
    <script crossorigin src="https://o.alicdn.com/cn-arch-fe/cone-arms-latest/index.umd.es5.production.js"></script>
    <link rel="styleSheet" href="https://cn.alicdn.com/lib/cn-ui/cn-ui/${cnuiVersion}/dist/cn-ui.css" />
    <link
            rel="stylesheet"
            href="${webResourceVersion}/assets/index.css"
    />
    <link
            rel="stylesheet"
            href="${webResourceVersion}/assets/vendor.css"
    />
</head>

<body>
<div id="root">
    <div
            style="
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100vw;
          height: 90vh;
          font-size: 13px;
          color: #999;
        "
    >
        加载中...
    </div>
    <div id="loading"></div>
</div>

<script
        src="//g.alicdn.com/code/lib/??babel-polyfill/6.26.0/polyfill.min.js,react/17.0.0/umd/react.production.min.js,react-dom/17.0.0/umd/react-dom.production.min.js"
        charset="utf-8"
></script>
<script src="https://g.alicdn.com/code/lib/prop-types/15.7.2/prop-types.min.js"></script>
    <script  src="https://cn.alicdn.com/lib/cn-ui/cn-ui/${cnuiVersion}/dist/cn-ui.js"></script>

<script>
    if (window.React && window.PropTypes) {
        React.PropTypes = PropTypes;
    }
    window.VERSION = "";
</script>
    <script type="module" src="${webResourceVersion}/index.js"></script>
    <script type="module" src="${webResourceVersion}/vendor.js"></script>
</body>
</html>
