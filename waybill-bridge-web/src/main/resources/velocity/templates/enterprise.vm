

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>菜鸟小邮局</title>
    <meta name="aplus-ifr-pv" content="1" />
    <link
            rel="icon"
            type="image/png"
            href="https://img.alicdn.com/tfs/TB1CZ0ARpXXXXX7XXXXXXXXXXXX-32-32.png"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <link
            href="//g.alicdn.com/code/npm/@alife/cn-domain-form-item/0.2.38/cn-domain-form-item-onecode-pc.css"
            rel="stylesheet"
    />
    <link
            rel="stylesheet"
            href="https://cn.alicdn.com/lib/cn-ui/cn-ui/0.12.50/dist/cn-ui.css"
    />
    <script>
        // 检查是否在钉钉环境中
        const isDingTalk = /dingtalk/i.test(navigator.userAgent.toLowerCase());
        if (isDingTalk) {
            // 使用document.write同步加载钉钉 JS API，确保在后续脚本执行前加载完成
            document.write('<script src="https://g.alicdn.com/dingding/dingtalk-jsapi/2.10.3/dingtalk.open.js" crossorigin="anonymous"><\/script>');
        }
    </script>
    <script
            crossorigin
            src="https://gw.alipayobjects.com/os/lib/??react/17.0.2/umd/react.production.min.js,react-dom/17.0.2/umd/react-dom.production.min.js"
    ></script>
    <script
            crossorigin
            src="https://o.alicdn.com/cn-arch-fe/cone-arms-latest/index.umd.es5.production.js"
    ></script>
    <script>
        // Check if in DingTalk environment and load remote debug script if needed
        if (/dingtalk/i.test(navigator.userAgent.toLowerCase())) {
            const debugScript = document.createElement('script');
            debugScript.src =
                    'https://g.alicdn.com/code/npm/@ali/dingtalk-h5-remote-debug/0.1.3/index.js';
            document.head.appendChild(debugScript);
        }
    </script>
    <meta name="wpk-bid" content="dta_2_141135" />
    <script>
        var isDingtalk = navigator && /DingTalk/.test(navigator.userAgent);
        var isProductEnv =
                window &&
                window.location &&
                window.location.host &&
                window.location.host.indexOf('127.0.0.1') === -1 &&
                window.location.host.indexOf('localhost') === -1 &&
                window.location.host.indexOf('192.168.') === -1;
        // 如果有其它测试域名，请一起排掉，减少测试环境对生产环境监控的干扰
        if (isProductEnv && isDingtalk) {
            !(function (c, i, e, b) {
                var h = i.createElement('script');
                var f = i.getElementsByTagName('script')[0];
                h.type = 'text/javascript';
                h.crossorigin = true;
                h.onload = function () {
                    c[b] || (c[b] = new c.wpkReporter({ bid: 'dta_2_141135' }));
                    c[b].installAll();
                };
                f.parentNode.insertBefore(h, f);
                h.src = e;
            })(
                    window,
                    document,
                    'https://g.alicdn.com/woodpeckerx/jssdk??wpkReporter.js',
                    '__wpk',
            );
        }
    </script>
    <link rel="stylesheet" href="${enterpriseWebUrl}/assets/index.css">
</head>

<body>
<div id="app-root"></div>
<script src="https://cn.alicdn.com/lib/cn-ui/cn-ui/0.12.50/dist/cn-ui.js"></script>
<script
        crossorigin
        defer
        src="//g.alicdn.com/code/npm/@alife/cn-domain-form-item/0.2.38/cn-domain-form-item-onecode-pc.js"
></script>

<script type="module" src="${enterpriseWebUrl}/assets/index.js"></script>
</body>
</html>

