package com.cainiao.waybill.bridge.enterprise.route.convert;

import com.cainiao.waybill.bridge.enterprise.utils.AddressUtils;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoDO;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseLocationDO;
import com.cainiao.waybill.bridge.model.dto.WaybillAccount;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseLocationDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserInfoDTO;
import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/18
 **/
public class WaybillEnterpriseLocationConverter {

    /**
     * DTO模型转换成DO模型
     * @param waybillEnterpriseLocationDTO DTO模型
     */
    public static WaybillEnterpriseLocationDO convertFromDTO(WaybillEnterpriseLocationDTO waybillEnterpriseLocationDTO) {
        if (waybillEnterpriseLocationDTO == null) {
            return null;
        }
        WaybillEnterpriseLocationDO waybillEnterpriseLocationDO = new WaybillEnterpriseLocationDO();
        BeanUtils.copyProperties(waybillEnterpriseLocationDTO,waybillEnterpriseLocationDO);
        waybillEnterpriseLocationDO.setLocationAddressCode(AddressUtils.convertToFormattedString(waybillEnterpriseLocationDTO.getLocationAddressCode()));
        waybillEnterpriseLocationDO.setLocationAddress(AddressUtils.convertToFormattedString(waybillEnterpriseLocationDTO.getLocationAddress()));
        return waybillEnterpriseLocationDO;
    }

    /**
     * DO模型转换成DTO模型
     * @param waybillEnterpriseLocationDO DO模型
     */
    public static WaybillEnterpriseLocationDTO convertFromDO(WaybillEnterpriseLocationDO waybillEnterpriseLocationDO) {
        if (waybillEnterpriseLocationDO == null) {
            return null;
        }
        WaybillEnterpriseLocationDTO waybillEnterpriseLocationDTO = new WaybillEnterpriseLocationDTO();
        BeanUtils.copyProperties(waybillEnterpriseLocationDO,waybillEnterpriseLocationDTO);
        waybillEnterpriseLocationDTO.setLocationAddressCode(AddressUtils.parse(waybillEnterpriseLocationDO.getLocationAddressCode()));
        waybillEnterpriseLocationDTO.setLocationAddress(AddressUtils.parse(waybillEnterpriseLocationDO.getLocationAddress()));
        return waybillEnterpriseLocationDTO;
    }


    /**
     * DO模型转换成DTO模型
     * @param waybillEnterpriseLocationDOList DO模型
     */
    public static List<WaybillEnterpriseLocationDTO> convertFromDOList(List<WaybillEnterpriseLocationDO> waybillEnterpriseLocationDOList) {
        List<WaybillEnterpriseLocationDTO> waybillEnterpriseLocationDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(waybillEnterpriseLocationDOList)) {
            return waybillEnterpriseLocationDTOList;
        }
        waybillEnterpriseLocationDOList.forEach(waybillEnterpriseLocationDO -> {
            WaybillEnterpriseLocationDTO waybillEnterpriseLocationDTO = new WaybillEnterpriseLocationDTO();
            BeanUtils.copyProperties(waybillEnterpriseLocationDO,waybillEnterpriseLocationDTO);
            waybillEnterpriseLocationDTO.setLocationAddressCode(AddressUtils.parse(waybillEnterpriseLocationDO.getLocationAddressCode()));
            waybillEnterpriseLocationDTO.setLocationAddress(AddressUtils.parse(waybillEnterpriseLocationDO.getLocationAddress()));
            List<WaybillAccount> accounts = new Gson().fromJson(waybillEnterpriseLocationDO.getWaybillAccountVid(), new TypeToken<List<WaybillAccount>>() {
            }.getType());
            waybillEnterpriseLocationDTO.setWaybillAccountsList(accounts);
            waybillEnterpriseLocationDTO.setLocationId(waybillEnterpriseLocationDO.getLocationId());
            waybillEnterpriseLocationDTOList.add(waybillEnterpriseLocationDTO);
        });
        return waybillEnterpriseLocationDTOList;
    }
}
