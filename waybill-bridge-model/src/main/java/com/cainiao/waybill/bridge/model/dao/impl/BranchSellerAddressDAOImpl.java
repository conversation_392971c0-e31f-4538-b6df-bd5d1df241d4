package com.cainiao.waybill.bridge.model.dao.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.common.util.Page;
import com.cainiao.waybill.bridge.model.common.AbstractDAO;
import com.cainiao.waybill.bridge.model.dao.BranchSellerAddressDAO;
import com.cainiao.waybill.bridge.model.dao.bean.BranchSellerAddressRelationQuery;
import com.cainiao.waybill.bridge.model.domain.BranchSellerAddressDO;
import com.google.common.collect.Maps;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant;
import com.taobao.common.dao.persistence.exception.DAOException;
import com.taobao.tddl.client.sequence.exception.SequenceException;
import com.taobao.tddl.client.sequence.impl.GroupSequence;
import org.apache.commons.lang.StringUtils;

/**
 * Created by <PERSON><PERSON><PERSON>yong on 2017/4/18.
 */
public class BranchSellerAddressDAOImpl extends AbstractDAO implements BranchSellerAddressDAO {

    @Resource
    private GroupSequence branchSellerAddressSequence;

    @Override
    public List<BranchSellerAddressDO> queryBySellerId(Long sellerId) throws DAOException {
        Map<String, Object> param = Maps.newHashMap();
        param.put("sellerId", sellerId);
        return this.executeQueryForList("branchSellerAddress.selectBySellerId", param, getDBRoute());
    }

    @Override
    public Page<BranchSellerAddressDO> queryByBranch(BranchSellerAddressRelationQuery query, int pageNo, int pageSize) throws DAOException {
        Long courierId = query.getCourierId();
        Date coopDateStart = query.getCooperationDateStart();
        Date coopDateEnd = query.getCooperationDateEnd();
        Byte cooperationStatus = query.getCooperationStatus();
        String cpCode = query.getCpCode();
        String branchCode = query.getBranchCode();
        String sellerName = query.getSellerNamePrefix();
        String shopName = query.getShopNamePrefix();

        if (StringUtils.isBlank(cpCode) || StringUtils.isBlank(branchCode)) {
            throw new IllegalArgumentException("cpCode 和 branchCode不能为空");
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("cpCode", cpCode);
        paramMap.put("branchCode", branchCode);

        if (coopDateStart != null && coopDateEnd != null && coopDateEnd.after(coopDateStart)) {
            paramMap.put("coopDateStart", coopDateStart);
            paramMap.put("coopDateEnd", coopDateEnd);
        }
        if (courierId != null && courierId > 0) {
            paramMap.put("courierId", courierId);
        }
        if (cooperationStatus != null) {
            paramMap.put("cooperationStatus", cooperationStatus);
        }
        if (StringUtils.isNotBlank(sellerName)) {
            paramMap.put("sellerNamePrefix", sellerName.trim());
        }
        if (StringUtils.isNotBlank(shopName)) {
            paramMap.put("shopNamePrefix", shopName.trim());
        }

        return executeQueryForPageWithCount("branchSellerAddress.selectByBranch", paramMap, pageSize, pageNo, getDBRoute());
    }

    @Override
    public BranchSellerAddressDO queryByCpSellerAddress(String cpCode, Long sellerId, Long divisionId, String addressDetail) throws DAOException {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("cpCode", cpCode);
        paramMap.put("sellerId", sellerId);
        paramMap.put("divisionId", divisionId);
        paramMap.put("addressDetail", addressDetail);

        return (BranchSellerAddressDO)executeQueryForObject("branchSellerAddress.selectByCpSellerAddress", paramMap, getDBRoute());
    }

    @Override
    public BranchSellerAddressDO queryById(Long id) throws DAOException {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", id);
        return (BranchSellerAddressDO)executeQueryForObject("branchSellerAddress.selectById", paramMap, getDBRoute());
    }

    @Override
    public Integer insert(BranchSellerAddressDO branchSellerAddressDO) throws DAOException {
        branchSellerAddressDO.setId(nextId());
        return this.executeUpdate("branchSellerAddress.insert", branchSellerAddressDO, getDBRoute());
    }

    @Override
    public Integer update(BranchSellerAddressDO branchSellerAddressDO) throws DAOException  {
        return this.executeUpdate("branchSellerAddress.update", branchSellerAddressDO, getDBRoute());
    }

    @Override
    public List<BranchSellerAddressDO> queryByGtModifyDateAndCooperation(Date modifyDate,  int start, int pageSize) throws DAOException {

        Map<String, Object> paramMap = Maps.newHashMap() ;
        paramMap.put("start",start ) ;
        paramMap.put("pageSize",pageSize) ;
        // TODO 守源 添加编辑字段
        paramMap.put("gmt_update", modifyDate) ;

        return this.executeQueryForList("branchSellerAddress.queryByGtModifyDateAndCooperation", paramMap, getDBRoute());
    }

    @Override
    public Integer countByGtModifyDateAndCooperation(Date modifyDate) throws DAOException {
        Map<String, Object> paramMap = Maps.newHashMap() ;
        // TODO 守源 添加编辑字段
        paramMap.put("gmt_update", modifyDate) ;

        return (Integer)this.executeQueryForObject("branchSellerAddress.countByGtModifyDateAndCooperation", paramMap, getDBRoute());
    }

    //通过TDDL获取唯一主键
    private Long nextId() throws DAOException {
        try {
            return branchSellerAddressSequence.nextValue();
        } catch (SequenceException e) {
            throw new DAOException(WaybillErrorConstant.SystemError.TDDL_SEQUENCE_ERROR.getErrorMsg(), e);
        } catch (Throwable e) {
            throw new DAOException(e);
        }
    }
}
