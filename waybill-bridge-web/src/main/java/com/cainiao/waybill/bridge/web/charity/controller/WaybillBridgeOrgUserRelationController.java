package com.cainiao.waybill.bridge.web.charity.controller;

import java.util.List;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.biz.charity.constant.CharityConstant;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CharityOrgTypeEnum;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CharityRoleEnum;
import com.cainiao.waybill.bridge.biz.charity.request.CharityOrgUserRelationDeleteRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityOrgUserRelationExcelImportRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityOrgUserRelationQueryRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityOrgUserRelationSaveRequest;
import com.cainiao.waybill.bridge.biz.charity.response.CharityExcelResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityOrgResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityOrgUserRelationExcelExportResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityOrgUserRelationResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityPagingResponse;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityOrgService;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityOrgUserRelationService;
import com.cainiao.waybill.bridge.biz.charity.util.CharityUtil;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelExportUtil;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelImportUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.AssertUtil;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.model.charity.DTO.WaybillCharityOrgDTO;
import com.cainiao.waybill.bridge.web.charity.CharityBaseResult;
import com.cainiao.waybill.bridge.web.charity.CharityExcelUtil;
import com.cainiao.waybill.bridge.web.charity.CharityLoginUserUtil;
import com.cainiao.waybill.bridge.web.common.util.ExeclUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Api(tags = "组织用户管理")
@RestController
@RequestMapping(value = "/charity/org/user/")
@Slf4j(topic = "CHARITY_INFO")
public class WaybillBridgeOrgUserRelationController extends BaseController {

    @Autowired
    private WaybillCharityOrgUserRelationService orgUserRelationService;

    @Autowired
    private WaybillCharityOrgService charityOrgService;

    @ApiOperation("查询组织用户列表")
    @ResponseBody
    @RequestMapping("/list")
    public CharityBaseResult<CharityPagingResponse<CharityOrgUserRelationResponse>> list(
        @RequestBody CharityOrgUserRelationQueryRequest request) {
        return CharityBaseResult.success(orgUserRelationService.queryUserList(request));
    }

    @ApiOperation("组织用户列表导出")
    @ResponseBody
    @RequestMapping("/export")
    public CharityBaseResult<CharityExcelResponse> export(@RequestBody CharityOrgUserRelationQueryRequest request) {
        CharityLoginUserUtil.popLoginInfo(request);
        String url = CharityExcelUtil.exportFile(request, "组织用户列表", CharityOrgUserRelationExcelExportResponse.class,
            request1 -> orgUserRelationService.queryUserList(request1));
        return CharityBaseResult.success(new CharityExcelResponse(url));
    }

    @ApiOperation("导入文件")
    @ResponseBody
    @RequestMapping("/import")
    public CharityBaseResult<CharityExcelResponse> importExcel(@RequestParam("file") MultipartFile file) {
        List<CharityOrgUserRelationExcelImportRequest> list = ExcelImportUtil.loadData(file,
            CharityOrgUserRelationExcelImportRequest.class,
            1000);
        List<CharityOrgUserRelationExcelImportRequest> errorList = Lists.newArrayList();
        for (CharityOrgUserRelationExcelImportRequest importRequest : ListUtil.non(list)) {
            try {
                CharityOrgUserRelationSaveRequest request = convertSaveRequest(importRequest);
                CharityLoginUserUtil.popLoginInfo(request);
                BridgeValidator.validate(request);

                orgUserRelationService.saveCharityUser(request);
                return CharityBaseResult.success();
            } catch (BridgeBusinessException exception) {
                importRequest.setErrorInfo(exception.getErrorMessage());
                errorList.add(importRequest);
            } catch (Exception exception) {
                importRequest.setErrorInfo(exception.getMessage());
                errorList.add(importRequest);
            }
        }
        if (CollectionUtils.isEmpty(errorList)) {
            return CharityBaseResult.success();
        }
        Workbook workbook = ExcelExportUtil.build(errorList, "组织用户导入失败列表",
            CharityOrgUserRelationExcelImportRequest.class);
        String url = ExeclUtil.exportToOSS(workbook, CharityConstant.OSS_TEMP_FILE_DIR);
        return CharityBaseResult.success(new CharityExcelResponse(url));
    }

    @NotNull
    private CharityOrgUserRelationSaveRequest convertSaveRequest(
        CharityOrgUserRelationExcelImportRequest importRequest) {
        CharityOrgUserRelationSaveRequest request = new CharityOrgUserRelationSaveRequest();
        request.setOrgUserNick(importRequest.getOrgUserNick());
        request.setPhone(importRequest.getPhone());
        request.setProject(CharityUtil.getProjectByName(importRequest.getProjectName()).getProject());

        // 查询寄件网点
        CharityOrgResponse orgResponse = charityOrgService.findByName(request.getProject(),
            importRequest.getOrgName());
        if (orgResponse != null) {
            request.setOrgCode(orgResponse.getOrgCode());
        }
        return request;
    }

    /**
     * 创建
     */
    @ApiOperation("创建组织用户")
    @ResponseBody
    @RequestMapping("/save")
    public CharityBaseResult<Void> create(@RequestBody CharityOrgUserRelationSaveRequest request) {
        // 鉴权 管理员可修改所有记录；非管理员，机构类型为省级中心，仅能修改本省的记录；非管理员，机构类型为非省级中心，仅能修改本机构的记录。
        boolean isAdmin = CharityRoleEnum.admin.name().equals(request.getRole());
        if(!isAdmin){
            // 查询所属机构类型是否有省级中心机构
            List<WaybillCharityOrgDTO> belongOrgList = charityOrgService.listOrgByUserId(request.getProject(), request.getLoginUserId());

            List<WaybillCharityOrgDTO> provinceOrgList = belongOrgList.stream().filter(
                orgInfo -> CharityOrgTypeEnum.provinceCenter.getCode().equals(orgInfo.getType())).collect(
                Collectors.toList());
            // 查询所选的机构是否属于当前省份
            CharityOrgResponse orgResponse = charityOrgService.findByCode(request.getProject(), request.getOrgCode());
            if(null == orgResponse ){
                return CharityBaseResult.bizFail("ORG_NOT_FOUND","机构信息不存在");
            }
            // 省级机构仅判定省份一致即可
            List<WaybillCharityOrgDTO> filterList = provinceOrgList.stream().filter(
                item -> StringUtils.equals(orgResponse.getProvince(), item.getProvince())).collect(
                Collectors.toList());
            // 其他非省级中心机构需判定是同一机构
            List<WaybillCharityOrgDTO> filterSelfList = belongOrgList.stream().filter(
                item -> StringUtils.equals(orgResponse.getOrgCode(), item.getOrgCode())).collect(
                Collectors.toList());
            PickUpLogUtil.info("用户归属机构列表.request:{},belongOrgList:{},filterList:{},filterSelfList:{}",
                JSONObject.toJSONString(request),
                JSONObject.toJSONString(belongOrgList),
                JSONObject.toJSONString(filterList),
                JSONObject.toJSONString(filterSelfList));
            if(CollectionUtils.isEmpty(filterList) && CollectionUtils.isEmpty(filterSelfList)){
                return CharityBaseResult.bizFail("PERMISSION_ERROR","您无该机构权限无法创建组织用户");
            }

        }
        BridgeValidator.validate(request);
        if (request.getOrgUserRelationId() != null && request.getOrgUserRelationId() > 0) {
            orgUserRelationService.updateCharityUser(request);
            return CharityBaseResult.success();
        }
        orgUserRelationService.saveCharityUser(request);
        return CharityBaseResult.success();
    }

    /**
     * 创建
     */
    @ApiOperation("删除组织用户")
    @ResponseBody
    @RequestMapping("/delete")
    public CharityBaseResult<Void> delete(@RequestBody CharityOrgUserRelationDeleteRequest request) {
        orgUserRelationService.deleteCharityUser(request, request.getOrgUserRelationId());
        return CharityBaseResult.success();
    }
}
