package com.cainiao.waybill.bridge.biz.fast.service;

import com.cainiao.waybill.bridge.biz.fast.request.FastShipmentRefundQueryRequest;
import com.cainiao.waybill.bridge.biz.fast.request.FastShipmentRefundRequest;
import com.cainiao.waybill.bridge.biz.fast.response.CodeVO;
import com.cainiao.waybill.bridge.biz.fast.response.FastShipmentOrderRecSenResponse;
import com.cainiao.waybill.bridge.biz.fast.response.FastShipmentOrderRefundResponse;
import com.cainiao.waybill.bridge.biz.fast.request.FastShipmentOrderRequest;
import com.cainiao.waybill.bridge.biz.fast.response.EnumVO;
import com.cainiao.waybill.bridge.biz.fast.response.FastShipmentOrderResponse;
import com.cainiao.waybill.bridge.biz.ticket.dto.PagingResponse;
import com.cainiao.waybill.galaxy.order.domain.dto.BaseResult;
import com.cainiao.waybill.galaxy.payment.types.DownLoadExcelResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 *
 *  <AUTHOR>
 */
public interface FastShipmentOrderService {

    /**
     * order列表查询
     *
     * @param request
     */

    PagingResponse<FastShipmentOrderResponse> queryOrderList(FastShipmentOrderRequest request);

    /**
     * 单条详情
     *
     * @param id
     * @return
     */
    BaseResult<FastShipmentOrderResponse> queryOrderDetail(Long id);

    /**
     * 查询支付方式枚举
     *
     * @param
     * @return
     */
    List<EnumVO> queryPayStatusEnum();

    /**
     * 提交退款申请
     *
     * @param
     * @return
     */
    BaseResult<Long> createRefund(FastShipmentRefundRequest request);

    /**
     * 上传附件
     *
     * @param file
     */
    Map<String, String> uploadAttachment(MultipartFile file);

    /**
     * 退款记录
     *
     * @param request
     */
    PagingResponse<FastShipmentOrderRefundResponse> queryRefundInfo(FastShipmentRefundQueryRequest request);

    /**
     * 查询运力来源枚举
     */
    List<EnumVO> queryApplyTypeEnum();


    /**
     * 查询cpCode列表
     */
    List<CodeVO> queryCpList();

    /**
     * 运力订单列表导出
     * @param request
     */
    BaseResult<String> orderListExport(FastShipmentOrderRequest request);


    BaseResult<DownLoadExcelResponse> downLoadExcel(String fileId);
    /**
     * 运力订单收寄人信息
     */
    FastShipmentOrderRecSenResponse queryRecSenderInfoByType(Long id,Integer type);
}