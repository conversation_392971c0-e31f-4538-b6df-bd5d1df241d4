package com.cainiao.waybill.bridge.model.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class WaybillBridgeUserInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    private Long id;

    /**
     *   创建时间
     */
    private Date gmtCreate;

    /**
     *   修改时间
     */
    private Date gmtModified;

    /**
     *   账户昵称
     */
    private String nick;

    /**
     *   账户名称
     */
    private String phone;

    /**
     *   菜鸟会员id
     */
    private String cainiaoUserId;

    /**
     *    淘宝会员id
     */
    private String tbUserId;

    /**
     *   业务场景编码：Ntb淘外，OneFound壹基金
     */
    private String scene;

    /**
     *   状态，1在用，-1停用
     */
    private Byte status;

    /**
     *   角色
     */
    private String role;

    /**
     *   扩展字段
     */
    private String feature;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCainiaoUserId() {
        return cainiaoUserId;
    }

    public void setCainiaoUserId(String cainiaoUserId) {
        this.cainiaoUserId = cainiaoUserId;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }

    public String getTbUserId() {
        return tbUserId;
    }

    public void setTbUserId(String tbUserId) {
        this.tbUserId = tbUserId;
    }
}
