package com.cainiao.waybill.bridge.common.util;

import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @author: yexin
 * @date: 2022-03-29 21:02
 **/
public class MarkdownUtil {

    public static String bold(String content) {
        return "**" + content + "**";
    }

    public static String title6(String content) {
        return "###### " + content;
    }

    public static String list(String content) {
        return "- " + content;
    }

    public static String enter() {
        return " \n ";
    }


    public static String font(String content, int size) {
        return String.format("<font size=%s>%s</font>", size, content);
    }

    public static String table(List<String> hearderList, List<List<String>> data, Function<String, String> headerFunction, Function<String, String> dataFunction){
        StringBuilder stringBuilder = new StringBuilder();
        if(headerFunction != null){
            hearderList = hearderList.stream().map(headerFunction).collect(Collectors.toList());
        }
        if(dataFunction != null){
            for (int i = 0; i < data.size(); i++) {
                data.set(i, data.get(i).stream().map(dataFunction).collect(Collectors.toList()));
            }
        }
        // 添加表头
        stringBuilder.append("| ").append(String.join(" | ", hearderList)).append(" |");
        stringBuilder.append(enter());

        // 添加 分割线
        for (int i = 0; i < hearderList.size(); i++) {
            stringBuilder.append("| ").append(":----:").append(" ");
        }
        stringBuilder.append("|");

        stringBuilder.append(enter());

        // 拼接数据
        for (List<String> datum : data) {
            stringBuilder.append("| ").append(String.join(" | ", datum)).append(" |");
            stringBuilder.append(enter());
        }
        return stringBuilder.toString();
    }

}
