package com.cainiao.waybill.bridge.web.pickup.response;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpRightResource
 * @Description
 * @Date 2022/11/29 4:22 下午
 * @Version 1.0
 */
@Data
public class PickUpRightResource implements Serializable {

    private static final long serialVersionUID = -6565763669764845679L;

    private Integer resourceId;

    private String resourceName;

    private List<PickUpRightResource> sub;
}
