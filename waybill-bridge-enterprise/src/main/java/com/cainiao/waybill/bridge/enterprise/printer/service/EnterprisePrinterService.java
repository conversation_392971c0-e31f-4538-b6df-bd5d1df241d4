package com.cainiao.waybill.bridge.enterprise.printer.service;

import com.cainiao.waybill.bridge.enterprise.printer.request.EnterprisePrinterRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.PrintContractRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.UpdatePrinterLocationRequest;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WayBillEnterprisePrinterDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25
 **/
public interface EnterprisePrinterService {


    /**
     * 获取打印机绑定码
     * @param printerId 打印机编码
     * @return 绑定码
     */
    void getVerifyCode(String printerId);


    /**
     * 添加打印机
     * @param request 打印机参数
     * @return 是否成功
     */
    String addPrinter(EnterprisePrinterRequest request);


    /**
     * 更新打印机
     * @param request 打印机参数
     * @return 是否成功
     */
    int updatePrinter(EnterprisePrinterRequest request);


    /**
     * 打印机分页查询
     * @param request 查询参数
     * @return 分页结果
     */
    BridgePagingDTO<WayBillEnterprisePrinterDTO> pageList(EnterprisePrinterRequest request);


    /**
     * 删除打印机
     * @param id id
     * @return 是否成功
     */
    int deleteById(Long id);

    /**
     * 根据参数获取打印机
     * @param request 查询参数
     * @return 打印机
     */
    WayBillEnterprisePrinterDTO getOneByParam(EnterprisePrinterRequest request);

    /**
     * 根据主键获取打印机
     * @param id 主键
     * @return 打印机
     */
    WayBillEnterprisePrinterDTO getPrinterById(Long id);

    /**
     * 根据场地名称获取打印机
     * @param locationName 场地名称
     * @return 打印机
     */
    List<WayBillEnterprisePrinterDTO> getListByLocationName(String locationName, String corpId);

    /**
     * 批量更新
     * @param request 请求
     */
    void batchUpdate(UpdatePrinterLocationRequest request);

    /**
     * 打印联系人面单
     * @param printContractRequest 单号
     */
    void printExpressContractInformation(PrintContractRequest printContractRequest);


    /**
     * 删除打印机
     * @param printerId printerId
     * @return 是否成功
     */
    int deleteByPrinterId(String printerId, String corpId);

    /**
     * 根据主键获取打印机
     * @param printerId printerId
     * @return 打印机
     */
    WayBillEnterprisePrinterDTO getPrinterByPrinterId(String printerId, String corpId);
}
