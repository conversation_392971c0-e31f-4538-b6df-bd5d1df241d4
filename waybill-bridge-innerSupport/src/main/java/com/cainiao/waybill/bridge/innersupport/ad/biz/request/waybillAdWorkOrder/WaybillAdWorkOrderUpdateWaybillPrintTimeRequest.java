package com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder;

import com.cainiao.waybill.bridge.innersupport.ad.biz.request.base.AdBaseRequest;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
* 更新面单打印时间
* WaybillAdWorkOrderUpdateWaybillPrintTimeRequest
*/
@Data
public class WaybillAdWorkOrderUpdateWaybillPrintTimeRequest extends AdBaseRequest {
    /**
    * 主键 ids
    */
    @NotNull
    private List<Long> ids;
}