package com.cainiao.waybill.bridge.web.pickup.vo;

import com.cainiao.waybill.bridge.biz.utils.excel.ExcelHeader;
import lombok.Data;

/**
 * 重点客户指标
 * <AUTHOR>
 * @date 2025/2/19 20:24
 **/
@Data
public class PickUpKeyRobotVO {

    /**
     * 客户名
     */
    @ExcelHeader("客户")
    private String customer;

    /**
     * 订单数
     */
    @ExcelHeader("订单数")
    private Integer orderNum;

    /**
     * 工单去重进线数
     */
    @ExcelHeader("去重进线数")
    private Integer distinctTicketNum;

    /**
     * 工单去重进线率
     */
    @ExcelHeader("去重进线率")
    private String distinctTicketRate;

    /**
     * 30分钟接单率
     */
    @ExcelHeader("30m接单率")
    private String acceptIn30mRate;

    /**
     * 应揽及时揽收率
     */
    @ExcelHeader("应揽及时揽率")
    private String shouldGotInTimeGotRate;


}
