package com.cainiao.waybill.bridge.web.pickup.ticket.role;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.cainiao.cnlogin.api.context.CnUserInfo;
import com.cainiao.cnlogin.api.context.CnUserInfoUtil;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketConstant;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.web.pickup.response.TicketRole;
import com.taobao.diamond.client.Diamond;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/22 上午11:58
 */
@Slf4j
public class TicketRoleUtil {
    @SneakyThrows
    public static TicketRole currentRole() {
        CnUserInfo userInfo = CnUserInfoUtil.getLoginContext();
        String roleStr = Diamond.getConfig(PickUpTicketConstant.DIAMOND_ROLE_DATA_ID, PickUpTicketConstant.DIAMOND_ROLE_GROUP, 3000);
        List<TicketRole> roles = JSON.parseArray(roleStr, TicketRole.class);
        TicketRole role = roles.stream().filter(s -> s.getAccount().equals(userInfo.getAccount())).findFirst().orElse(null);
        PickUpLogUtil.info("roles : " + JSON.toJSONString(roles));
        PickUpLogUtil.info("cur role : " + JSON.toJSONString(role));
        PickUpLogUtil.info("cur userInfo : " + JSON.toJSONString(userInfo));
        if (role == null) {
            log.error("userInfo:" + JSON.toJSONString(userInfo));
            log.error("roleStr:" + roleStr);
            return null;
        }
        if (PickUpTicketConstant.RoleType.cp.name().equals(role.getRoleType()) || PickUpTicketConstant.RoleType.platform.name().equals(role.getRoleType())) {
            if (StringUtil.isBlank(role.getSource())) {
                throw new RuntimeException("role is blank");
            }
        }
        return role;
    }
}
