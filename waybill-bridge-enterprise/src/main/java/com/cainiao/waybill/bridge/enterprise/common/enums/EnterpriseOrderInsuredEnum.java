package com.cainiao.waybill.bridge.enterprise.common.enums;

/**
 * <AUTHOR>
 * @date 2025/6/11
 **/
public enum EnterpriseOrderInsuredEnum {

    INSURED(0, "已保价"),

    NOT_INSURED(1, "不保价"),

    ;

    final Integer code;

    final String describe;

    public Integer getCode() {
        return code;
    }

    public String getDescribe() {
        return describe;
    }

    EnterpriseOrderInsuredEnum(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }
}
