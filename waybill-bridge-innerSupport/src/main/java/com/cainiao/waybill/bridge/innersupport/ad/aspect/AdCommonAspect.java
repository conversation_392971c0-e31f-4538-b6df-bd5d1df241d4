package com.cainiao.waybill.bridge.innersupport.ad.aspect;

import com.alibaba.fastjson.JSON;
import com.cainiao.cnlogin.api.context.CnUserInfo;
import com.cainiao.cnlogin.api.context.CnUserInfoUtil;
import com.cainiao.cnuser.client.ResultInfo;
import com.cainiao.cnuser.client.domain.CnUserInfoDO;
import com.cainiao.waybill.bridge.innersupport.ad.biz.request.base.AdBaseRequest;
import com.cainiao.waybill.bridge.innersupport.ad.constant.AdCommonConstant;
import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.AdErrorConstants;
import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.config.diamond.WaybillAdWorkOrderDiamondConfig;
import com.cainiao.waybill.bridge.innersupport.ad.exception.AdBusinessException;
import com.cainiao.waybill.bridge.innersupport.ad.model.dto.WaybillAdUserDTO;
import com.cainiao.waybill.bridge.innersupport.ad.web.AdBaseResult;
import com.cainiao.waybill.bridge.innersupport.ad.web.wrapper.AdCnLoginWrapper;
import com.taobao.hsf.exception.HSFTimeOutException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.StringUtils;
import javax.annotation.Resource;
import java.lang.reflect.Method;

/**
 * 统一处理controller的：日志打印、登录信息设置、角色校验功能
 */
@Aspect
@Component
public class AdCommonAspect {
    private final static Logger log = LoggerFactory.getLogger(AdCommonConstant.LOG_NAME);

    @Resource
    private AdCnLoginWrapper cnLoginManager;

    @Around("execution(public * com.cainiao.waybill.bridge.innersupport.ad.web.controller..*.*(..))")
    public Object execute(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String classMethodName = signature.getDeclaringType().getSimpleName() + "#" + signature.getMethod().getName();
        try {
            AdBaseRequest baseRequest = getAdBaseRequest(joinPoint);
            //设置用户登录信息
            setLoginInfo(baseRequest);
            //打印方法名和入参
            log.error(classMethodName + ",--------request-------:" + JSON.toJSONString(joinPoint.getArgs()));
            //执行正常业务逻辑
            Object result = joinPoint.proceed();
            //打印返回值
            log.error(classMethodName + ",--------response--------:" + JSON.toJSONString(result));
            return result;
        } catch (AdBusinessException businessException) {
            log.error(classMethodName + ",--------businessException-------:" + businessException.toString(), businessException);
            return AdBaseResult.bizFail(businessException);
        } catch (HSFTimeOutException hsfTimeOutException) {
            log.error(classMethodName + ",--------hsfTimeOutException-------:" + hsfTimeOutException.getMessage(), hsfTimeOutException);
            return AdBaseResult.bizFail(AdErrorConstants.HSF_TIMEOUT_ERROR);
        } catch (Throwable th) {
            log.error(classMethodName + ",--------throwable-------:" + th.getMessage(), th);
            return AdBaseResult.bizFail(AdErrorConstants.SYSTEM_ERROR.getErrorCode(), th.getMessage());
        }
    }

    /**
     * 获取adBaseRequest
     * @param joinPoint
     * @return
     */
    private AdBaseRequest getAdBaseRequest(ProceedingJoinPoint joinPoint) {
        for (Object arg : joinPoint.getArgs()) {
            if (arg instanceof AdBaseRequest) {
                return (AdBaseRequest) arg;
            }
        }
        return null;
    }

    /**
     * 设置登录信息
     *
     * @param request
     */
    private void setLoginInfo(AdBaseRequest request) {
        if (request == null) {
            return;
        }
        WaybillAdWorkOrderDiamondConfig.WaybillAdWorkOrderConfig config = WaybillAdWorkOrderDiamondConfig.getConfig();
        // Mock 用户登录
        Boolean adWorkOrderMockLogin = config.getAdWorkOrderMockLogin();
        if (adWorkOrderMockLogin) {
            WaybillAdUserDTO mockUser = new WaybillAdUserDTO();
            mockUser.setMobile("***********");
            mockUser.setName("用户xxx");
            request.setLoginUserInfo(mockUser);
            return;
        }
        WaybillAdUserDTO waybillAdUserDTO = new WaybillAdUserDTO();
        CnUserInfo userInfo = CnUserInfoUtil.getLoginContext();
        if (userInfo == null) {
            throw new AdBusinessException(AdErrorConstants.GET_CN_USER_LOGIN_INFO_IS_NULL);
        }
        ResultInfo<CnUserInfoDO> cnLoginUserInfo = cnLoginManager.getCnLoginUserInfo(userInfo.getAccountId());
        String mobile = cnLoginUserInfo.getData().getMobile();
        // 操作人
        String nickName = cnLoginUserInfo.getData().getDisplayName();
        if (StringUtils.isBlank(mobile)) {
            throw new AdBusinessException(AdErrorConstants.CN_USERINFO_MOBILE_IS_NULL);
        }
        waybillAdUserDTO.setMobile(mobile);
        waybillAdUserDTO.setName(nickName);
        request.setLoginUserInfo(waybillAdUserDTO);
    }
}
