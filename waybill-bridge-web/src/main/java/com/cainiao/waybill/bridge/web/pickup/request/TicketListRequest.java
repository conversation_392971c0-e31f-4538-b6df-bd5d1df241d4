package com.cainiao.waybill.bridge.web.pickup.request;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/4/18 下午3:02
 */
@Getter
@Setter
public class TicketListRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    private String mailNo;
    private String outerOrderCode;

    private String ticketType;
    private String[] ticketTypeList;

    private Date startTime;
    private Date endTime;

    private String orderStatus;
    private String[] orderStatusList;

    private String labelContent;
    private String[] labelContentList;

    private String ticketStatus;
    private String[] ticketStatusList;
    /**
     * 第几页,从1开始
     */
    private Integer currentPage;
    /**
     * 每页大小
     */
    private Integer pageSize;
    /**
     * 寄件人省份
     */
    private String province;
    private String[] provinceList;
    /**
     * 平台名称
     */
    private String platform;
    private String[] platformList;
    /**
     * 工单来源
     */
    private String primarySource;
    private String[] primarySourceList;

    private String[] cpList;

}
