package com.cainiao.waybill.bridge.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author: yexin
 * @date: 2022-03-31 17:16
 **/
public class ChineseCheckUtil {

    private static Pattern p = Pattern.compile("\\s*|\t*|\r*|\n*");

    /**
     * 判断str中是否包含fitLength个中文字符
     *
     * @param str       :
     * @param fitLength :
     * @return :
     */
    public static boolean chineseCharLengthFit(String str, int fitLength) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        int chineseLength = 0;
        for (int i = 0; i < str.length(); i++) {
            if (isChinese(str.charAt(i))) {
                chineseLength++;
            }
        }
        return chineseLength >= fitLength;
    }

    /**
     * 判断是否为一个中文字符
     *
     * @param c :
     * @return :
     */
    public static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        if (ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否为乱码
     */
    public static boolean isMessyCode(String strName) {
        Matcher m = p.matcher(strName);
        String after = m.replaceAll("");
        String temp = after.replaceAll("\\p{P}", "");
        char[] ch = temp.trim().toCharArray();
        float chLength = ch.length;
        float count = 0;
        for (int i = 0; i < ch.length; i++) {
            char c = ch[i];
            if (!Character.isLetterOrDigit(c)) {
                if (!isChinese(c)) {
                    count = count + 1;
                }
            }
        }
        float result = count / chLength;
        return result > 0.4;
    }

}
