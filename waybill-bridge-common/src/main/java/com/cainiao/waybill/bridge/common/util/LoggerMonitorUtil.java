package com.cainiao.waybill.bridge.common.util;

import java.sql.Timestamp;

import com.cainiao.waybill.bridge.common.base.MonitorContext;
import com.google.common.base.Strings;
import com.taobao.eagleeye.EagleEye;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LoggerMonitorUtil {
    private static ThreadLocal<Object> threadLocal = new ThreadLocal();
    public static Logger MONITOR_LOGGER = LoggerFactory.getLogger("PICK_UP_MONITOR_INFO");
    public static Logger MONITOR_V2_LOGGER = LoggerFactory.getLogger("PICK_UP_MONITOR_INFO_V2");
    private static final String SEPARATE = "|";
    private static final String SUB_SEPARATE="#";

    /**
     * 鹰眼上下文场景标
     */
    public final static String EAGLE_EYE_CHANNEL_CODE_TAG = "cncpc";

    public final static String EAGLE_EYE_USER_ID_TAG = "cncpu";

    public static void start(String scenario, String fromApp) {
        MonitorContext monitorContext = new MonitorContext();
        monitorContext.setScenario(scenario);
        monitorContext.setFromApp(fromApp);
        threadLocal.set(monitorContext);
        return;
    }

    /**
     * 入口埋点
     *
     * @param scenario
     * @param subCode
     * @param code
     * @param fromApp
     */
    public static void start(String scenario, String subCode, String code, String fromApp) {
        MonitorContext monitorContext = new MonitorContext();
        monitorContext.setScenario(scenario);
        monitorContext.setSubCode(subCode);
        monitorContext.setCode(code);
        monitorContext.setFromApp(fromApp);
        threadLocal.set(monitorContext);
        return;
    }

    /**
     * 调用成功
     *
     * @param resultCode
     */
    public static void success(String resultCode,String externalFailCode) {
        try {
            log(resultCode, externalFailCode,true);
        } catch (Throwable e) {
            MONITOR_LOGGER.error("monitor_log_success_fail", e);
        } finally {
            try {
                threadLocal.remove();
            } catch (Throwable e) {
                MONITOR_LOGGER.error("monitor_log_success_fail", e);
            }
        }
    }

    public static void end(boolean result){
        if(result){
            success(null);
        }else{
            fail(null);
        }
    }

    public static void end(boolean result, String resultCode){
        if(result){
            success(resultCode);
        }else{
            fail(resultCode);
        }
    }

    /**
     * 调用成功
     *
     * @param resultCode
     */
    public static void success(String resultCode) {
        success(resultCode,null);
    }

    public static void fail(String resultCode,String externalFailCode){
        try {
            log(resultCode,externalFailCode,false);
        } catch (Throwable e) {
            MONITOR_LOGGER.error("monitor_log_fail_fail", e);
        } finally {
            try {
                threadLocal.remove();
            } catch (Throwable e) {
                MONITOR_LOGGER.error("monitor_log_fail_fail", e);
            }
        }
    }
    /**
     * 调用失败
     *
     */
    public static void fail(String resultCode) {
        fail(resultCode,null);
    }

    public static void log(String code,String externalCode,boolean success) {
        MonitorContext monitorContext = getMonitorContext();
        if (monitorContext == null) {
            return;
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(new Timestamp(System.currentTimeMillis()));
        stringBuilder.append(SEPARATE);
        stringBuilder.append(EagleEye.getTraceId());
        stringBuilder.append(SEPARATE);
        stringBuilder.append(monitorContext.getScenario());
        stringBuilder.append(SEPARATE);
        if (success) {
            stringBuilder.append(1);
        } else {
            stringBuilder.append(0);
        }
        stringBuilder.append(SEPARATE);
        if (success) {
            stringBuilder.append(0);
        } else {
            stringBuilder.append(1);
        }
        stringBuilder.append(SEPARATE);
        stringBuilder.append(code);
        if (!Strings.isNullOrEmpty(externalCode)){
            stringBuilder.append(SUB_SEPARATE);
            stringBuilder.append(externalCode);
        }
        stringBuilder.append(SEPARATE);
        stringBuilder.append(monitorContext.getSubCode());
        stringBuilder.append(SEPARATE);
        if (!Strings.isNullOrEmpty(monitorContext.getCode())) {
            stringBuilder.append(monitorContext.getCode());
        }
        stringBuilder.append(SEPARATE);
        stringBuilder.append(System.currentTimeMillis() - monitorContext.getStartTime().getTime());
        stringBuilder.append(SEPARATE);
        String channelCode = EagleEye.getUserData(EAGLE_EYE_CHANNEL_CODE_TAG);
        if(channelCode != null) {
            stringBuilder.append(channelCode);
        }else {
            stringBuilder.append(monitorContext.getFromApp());
        }
        stringBuilder.append(SEPARATE);
        if (monitorContext.getUserData() != null) {
            stringBuilder.append(monitorContext.getUserData());
        }
        stringBuilder.append(SEPARATE);
        String userId = EagleEye.getUserData(EAGLE_EYE_USER_ID_TAG);
        if(userId != null) {
            stringBuilder.append(userId);
        }
        stringBuilder.append(SEPARATE);
        MONITOR_LOGGER.error(stringBuilder.toString());
    }

    public static MonitorContext getMonitorContext() {
        Object t = threadLocal.get();
        if (t == null) {
            return null;
        }
        if (!(t instanceof MonitorContext)) {
            return null;
        }
        return (MonitorContext) t;
    }

    public static void monitor(String... keys) {
        MONITOR_V2_LOGGER.info(EagleEye.getTraceId() + ";" + String.join("|", keys) + ";");
    }

    public static void monitor(long rt, String... keys) {
        MONITOR_V2_LOGGER.info(EagleEye.getTraceId() + ";" + String.join("|", keys) + ";" + rt);
    }
}
