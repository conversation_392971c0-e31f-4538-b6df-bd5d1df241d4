package com.cainiao.waybill.bridge.enterprise.common;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpLogisticsStatusEnum;
import com.cainiao.waybill.bridge.enterprise.authority.domain.EnterpriseAppConfig;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseConfigEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseOrderStatusEnum;
import com.google.common.collect.Lists;
import com.taobao.cainiao.waybill.client.utils.WaybillLifeCycleNotifyDTOBuilder;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.annotation.NameSpace;
import com.taobao.csp.switchcenter.bean.Switch;

import java.util.*;

/**
 * switch配置
 * <AUTHOR>
 * @date 2025-04-11 17:30:19
 */
@SwitchGroup
@NameSpace(nameSpace = "enterpriseSwitch")
public class EnterpriseSwitchHolder {

    @AppSwitch(des = "钉钉请求URL", level = Switch.Level.p3)
    public static Map<String, String> DINGDING_REQUEST_URL = new HashMap<>();

    @AppSwitch(des = "钉钉请求配置", level = Switch.Level.p3)
    public static Map<String, String> ENTERPRISE_CONFIG = new HashMap<>();
    static {
        ENTERPRISE_CONFIG.put(EnterpriseConfigEnum.appId.name(), "141135");
        ENTERPRISE_CONFIG.put(EnterpriseConfigEnum.pickUpTemplateId.name(), "25638fb97a3f48fd96c2ed72ae6d5413");
    }

    @AppSwitch(des = "企业寄件使用预约单的做集收的CP", level = Switch.Level.p3)
    public static List<String> APPOINT_ORDER_SUPPORT_CP = Lists.newArrayList("SF");

    @AppSwitch(des = "面单联系人信息模板", level = Switch.Level.p3)
    public static Map<String,String> CONTRACT_TEMPLATE_URL = new HashMap<>();
    static {
        CONTRACT_TEMPLATE_URL.put("SF", "https://cloudprint.cainiao.com/template/standard/474941/37");
        CONTRACT_TEMPLATE_URL.put("EMS", "https://cloudprint.cainiao.com/template/standard/345208/39");
    }

    @AppSwitch(des = "企业寄件测试用户", level = Switch.Level.p3)
    public static List<String> ENTERPRISE_TEST_USER = Lists.newArrayList();

    @AppSwitch(des = "快递公司映射", level = Switch.Level.p3)
    public static Map<String, String> CP_CODE_MAPPING = new HashMap<>();
    static {
        CP_CODE_MAPPING.put("SF", "顺丰");
        CP_CODE_MAPPING.put("EMS", "EMS");
    }

    @AppSwitch(des = "钉钉请求配置", level = Switch.Level.p3)
    public static Map<String, EnterpriseAppConfig> ENTERPRISE_CLIENT_CONFIG = new HashMap<>();
    static {

        // 内部测试应用-菜鸟小邮局
        EnterpriseAppConfig enterpriseInnerAppConfig = new EnterpriseAppConfig();
        enterpriseInnerAppConfig.setClientId("suite4osrsvov6ciqriak");
        enterpriseInnerAppConfig.setClientSecret("KX9M4ai_MKhPJegW4VIveRkG7KHOxXHZrnI-MV7VCO8pUqNBdfECL_fHx8-6lmNa");
        enterpriseInnerAppConfig.setCustomKey("suite4osrsvov6ciqriak");
        enterpriseInnerAppConfig.setCustomSecret("KX9M4ai_MKhPJegW4VIveRkG7KHOxXHZrnI-MV7VCO8pUqNBdfECL_fHx8-6lmNa");
        enterpriseInnerAppConfig.setClientName("杭州菜鸟供应链管理有限公司");
        enterpriseInnerAppConfig.setAgentId("3751198359");
        ENTERPRISE_CLIENT_CONFIG.put("ding25e7b590c204cde5ffe93478753d9884", enterpriseInnerAppConfig);
    }


    @AppSwitch(des = "当前企业管理员用户ID列表", level = Switch.Level.p3)
    public static Map<String, List<String>> CORP_ADMIN_USER_LIST = new HashMap<>();
    static {
        CORP_ADMIN_USER_LIST.put("ding10dfcbc0c7dabea235c2f4657eb6378f", Arrays.asList("10000149", "992979", ""));
    }


    @AppSwitch(des = "当前企业寄件OrderStatus映射", level = Switch.Level.p3)
    public static Map<Byte, Integer> ENTERPRISE_ORDER_STATUS_MAPPING = new HashMap<>();
    static {
        ENTERPRISE_ORDER_STATUS_MAPPING.put(WaybillLifeCycleNotifyDTOBuilder.Actions.LOGISTIC_PICKUP.getByteAction(), EnterpriseOrderStatusEnum.IN_TRANSIT.getCode());
        ENTERPRISE_ORDER_STATUS_MAPPING.put(WaybillLifeCycleNotifyDTOBuilder.Actions.APPLY_NEW.getByteAction(), EnterpriseOrderStatusEnum.PENDING.getCode());
        ENTERPRISE_ORDER_STATUS_MAPPING.put(WaybillLifeCycleNotifyDTOBuilder.Actions.LOGISTIC_SIGN.getByteAction(), EnterpriseOrderStatusEnum.SIGNED.getCode());
        ENTERPRISE_ORDER_STATUS_MAPPING.put(WaybillLifeCycleNotifyDTOBuilder.Actions.APPLY_CANCEL.getByteAction(), EnterpriseOrderStatusEnum.CANCELED.getCode());
    }

    @AppSwitch(des = "当前企业寄件ActionStatus映射", level = Switch.Level.p3)
    public static Map<Byte, Integer> ENTERPRISE_ACTION_STATUS_MAPPING = new HashMap<>();

    static {
        ENTERPRISE_ACTION_STATUS_MAPPING.put(WaybillLifeCycleNotifyDTOBuilder.Actions.LOGISTIC_PICKUP.getByteAction(), PickUpLogisticsStatusEnum.ACCEPT.getCode());
        ENTERPRISE_ACTION_STATUS_MAPPING.put(WaybillLifeCycleNotifyDTOBuilder.Actions.APPLY_NEW.getByteAction(), PickUpLogisticsStatusEnum.CREATE.getCode());
        ENTERPRISE_ACTION_STATUS_MAPPING.put(WaybillLifeCycleNotifyDTOBuilder.Actions.LOGISTIC_SIGN.getByteAction(), PickUpLogisticsStatusEnum.SIGN.getCode());
        ENTERPRISE_ACTION_STATUS_MAPPING.put(WaybillLifeCycleNotifyDTOBuilder.Actions.APPLY_CANCEL.getByteAction(), PickUpLogisticsStatusEnum.CANCEL.getCode());
    }
}
