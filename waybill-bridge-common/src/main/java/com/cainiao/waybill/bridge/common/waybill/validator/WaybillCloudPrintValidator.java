package com.cainiao.waybill.bridge.common.waybill.validator;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.validator.BridgeBaseValidator;
import com.cainiao.waybill.bridge.common.waybill.dto.request.QueryCloudPrintPictureRequest;

/**
 *
 * 电子面单云打印入参校验
 *
 * Created by shouyuan.lzl on 2017-04-28 3:13 PM.
 */
public class WaybillCloudPrintValidator extends BridgeBaseValidator {

    /**
     * 获取云打印面单图片入参校验
     * @param request
     * @param clientInfoDTO
     * @throws BridgeValidationException
     */
    public static void validateQueryCloudPrintPicture(QueryCloudPrintPictureRequest request, ClientInfoDTO clientInfoDTO) throws BridgeValidationException {

        validate(request != null, "request不能为空");
        validate(longGtZero(request.getWaybillSellerId()),"waybillSellerId需要大于0");
        validate(stringHasChar(request.getWaybillCode()),"waybillCode需要有值");
        validate(stringHasChar(request.getCpCode()),"cpCode需要有值");

        validateClientInfoDTO(clientInfoDTO);
    }

}
