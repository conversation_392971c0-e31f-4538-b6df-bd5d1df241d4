package com.cainiao.waybill.bridge.common.label.dto.request;

import java.util.Map;

import com.cainiao.waybill.bridge.common.dto.BaseDTO;
import com.cainiao.waybill.bridge.common.label.dto.TpnTemplateEnum;

/**
 * 千牛消息请求 DTO
 *
 * <AUTHOR>
 * @since 2018/04/20
 */
public class TpnMsgRequest extends BaseDTO {
    private static final long serialVersionUID = 6366851501994524443L;
    /**
     * 商家账户id 必填
     */
    private Long sellerId;

    /**
     * 业务Id，问题排查使用，必填
     */
    private Long bizId;
    /**
     * 消息标题 必填
     */
    private Map<String, String> titleParams;
    /**
     * 消息内容 必填
     */
    private Map<String, String> contentParams;
    /**
     * 消息模板类型 必填
     */
    private TpnTemplateEnum templateType;

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public Long getBizId() {
        return bizId;
    }

    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    public Map<String, String> getTitleParams() {
        return titleParams;
    }

    public void setTitleParams(Map<String, String> titleParams) {
        this.titleParams = titleParams;
    }

    public Map<String, String> getContentParams() {
        return contentParams;
    }

    public void setContentParams(Map<String, String> contentParams) {
        this.contentParams = contentParams;
    }

    public TpnTemplateEnum getTemplateType() {
        return templateType;
    }

    public void setTemplateType(TpnTemplateEnum templateType) {
        this.templateType = templateType;
    }
}
