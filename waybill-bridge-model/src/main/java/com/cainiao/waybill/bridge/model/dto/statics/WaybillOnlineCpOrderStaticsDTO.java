package com.cainiao.waybill.bridge.model.dto.statics;

import java.io.Serializable;

import lombok.Data;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * <AUTHOR> zouping.fzp
 * @Classname WaybillOnlineChannelOrderStaticsDTO
 * @Description
 * @Date 2023/11/10 15:40
 * @Version 1.0
 */
@Data
public class WaybillOnlineCpOrderStaticsDTO implements Serializable {

    private static final long serialVersionUID = -7619515193945608454L;

    /**
     * 代理商
     */
    private String agent;

    /**
     * cpCode
     */
    private String cpCode;

    /**
     * 应揽件数
     */
    private int shouldGotNum;

    /**
     * 已揽件数
     */
    private int gotedNum;

    /**
     * 及时揽件数
     */
    private int inTimeGotNum;

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}

        if (o == null || getClass() != o.getClass()) {return false;}

        WaybillOnlineCpOrderStaticsDTO that = (WaybillOnlineCpOrderStaticsDTO)o;

        return new EqualsBuilder().append(cpCode, that.cpCode).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(cpCode).toHashCode();
    }
}
