package com.cainiao.waybill.bridge.enterprise.post.controller;

import com.cainiao.waybill.bridge.enterprise.administrators.request.User;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.UserContext;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.post.request.EnterprisePostRequest;
import com.cainiao.waybill.bridge.enterprise.post.service.WaybillEnterprisePostService;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterprisePostDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EnterprisePostControllerTest {

    @InjectMocks
    private EnterprisePostController enterprisePostController;
    @Mock
    private WaybillEnterprisePostService waybillEnterprisePostService;
    @Mock
    private UserContext userContext;


    /**
     * 测试当postId为空时返回参数错误
     */
    @Test
    public void testDeletePostByPostIdParamError() throws Exception {
        String postId = null;
        EnterpriseBaseResult<Boolean> result = enterprisePostController.deletePostByPostId(postId);
        assertEquals(EnterpriseErrorEnum.PARAM_ERROR.code(), result.getErrorCode());
        assertEquals("postId不能为空", result.getErrorMsg());
    }

    /**
     * 测试删除成功返回成功结果
     */
    @Test
    public void testDeletePostByPostIdSuccess() throws Exception {
        String postId = "PO123123213";
        User testUser = Mockito.mock(User.class);
        when(testUser.getCorpId()).thenReturn("testCorpId");
        UserContext.setUser(testUser);
        doNothing().when(waybillEnterprisePostService).deletePostByPostId(postId, UserContext.getUser().getCorpId());
        EnterpriseBaseResult<Boolean> result = enterprisePostController.deletePostByPostId(postId);
        assertTrue(result.isSuccess());
        // assertEquals(true, result.getData());
    }

    /**
     * 测试出现异常时返回系统错误
     */
    @Test
    public void testDeletePostByPostIdSystemError() throws Exception {
        String postId = "PO123123213";
        User testUser = Mockito.mock(User.class);
        when(testUser.getCorpId()).thenReturn("testCorpId");
        UserContext.setUser(testUser);
        doThrow(new RuntimeException("SYSTEM_ERROR")).when(waybillEnterprisePostService).deletePostByPostId(postId,
                UserContext.getUser().getCorpId());

        EnterpriseBaseResult<Boolean> result = enterprisePostController.deletePostByPostId(postId);
        assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), result.getErrorCode());
    }

    /**
     * 测试postId为空时返回参数错误
     */
    @Test
    public void testGetPostByPostIdParamError() throws Exception {
        String postId = null;
        EnterpriseBaseResult<WaybillEnterprisePostDTO> result = enterprisePostController.getPostByPostId(postId);
        assertEquals(EnterpriseErrorEnum.PARAM_ERROR.code(), result.getErrorCode());
        assertEquals("postId不能为空", result.getErrorMsg());
    }

    /**
     * 测试获取邮局信息成功并返回
     */
    @Test
    public void testGetPostByPostIdSuccess() throws Exception {
        String postId = "PO123123213";
        WaybillEnterprisePostDTO dto = new WaybillEnterprisePostDTO();
        when(waybillEnterprisePostService.getPostByPostId(postId, "mockCorpId")).thenReturn(dto);
        // 模拟静态方法UserContext.getUser().getCorpId()的返回值
        User user = mock(User.class);
        when(UserContext.getUser()).thenReturn(user);
        when(user.getCorpId()).thenReturn("mockCorpId");
        EnterpriseBaseResult<WaybillEnterprisePostDTO> result = enterprisePostController.getPostByPostId(postId);
        assertTrue(result.isSuccess());
    }

    /**
     * 测试异常情况下返回系统错误
     */
    @Test
    public void testGetPostByPostIdSystemError() throws Exception {
        String postId = "PO123123213";
        doThrow(new RuntimeException("SYSTEM_ERROR")).when(waybillEnterprisePostService).getPostByPostId(postId, "mockCorpId");
        // 模拟静态方法UserContext.getUser().getCorpId()的返回值
        User user = mock(User.class);
        when(UserContext.getUser()).thenReturn(user);
        when(user.getCorpId()).thenReturn("mockCorpId");

        EnterpriseBaseResult<WaybillEnterprisePostDTO> result = enterprisePostController.getPostByPostId(postId);
        assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), result.getErrorCode());
    }
}