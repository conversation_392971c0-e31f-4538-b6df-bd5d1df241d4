package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.biz.logistics.dto.SubscribeAppInfo;
import com.cainiao.waybill.bridge.biz.logistics.dto.TmsWaybillLogisticsDetailQueryRequest;
import com.cainiao.waybill.bridge.biz.logistics.dto.TmsWaybillLogisticsDetailQueryResponse;
import com.cainiao.waybill.bridge.biz.logistics.manager.TmsWaybillLogisticDetailManager;
import com.cainiao.waybill.bridge.biz.logistics.util.LogisticsDetailMapper;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.base.ScenarioConstant;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant;
import com.cainiao.waybill.bridge.common.util.LoggerMonitorUtil;
import com.taobao.eagleeye.EagleEye;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.api.open.ReceiveService;
import com.taobao.pac.client.sdk.dataobject.request.TMS_WAYBILL_LOGISTICS_QUERY.TmsWaybillLogisticsQueryRequest;
import com.taobao.pac.client.sdk.dataobject.response.TMS_WAYBILL_LOGISTICS_QUERY.TmsWaybillLogisticsQueryResponse;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;


/**
 * 获取物流详情轨迹信息
 * <AUTHOR>
 * @date 2024/2/27 17:36
 */
@Slf4j

public class WaybillPickUpLogisticsDetailGetServiceForLinkImpl implements ReceiveService<TmsWaybillLogisticsQueryRequest,
        TmsWaybillLogisticsQueryResponse> {

    @Resource
    private TmsWaybillLogisticDetailManager tmsWaybillLogisticDetailManager;

    @Override
    public TmsWaybillLogisticsQueryResponse invoke(ReceiveParams<TmsWaybillLogisticsQueryRequest> receiveParams) {
        log.warn("WaybillPickUpLogisticsDetailGetServiceForLinkImpl request:{}", JSON.toJSONString(receiveParams));
        TmsWaybillLogisticsQueryResponse response = new TmsWaybillLogisticsQueryResponse();
        TmsWaybillLogisticsQueryRequest requestDataObject = receiveParams.getRequestDataObject();
        try {
            String mailNo = requestDataObject.getMailNo();
            String cpCode = requestDataObject.getCpCode();
            if (StringUtil.isBlank(mailNo) || StringUtil.isBlank(cpCode)) {
                response.setSuccess(false);
                response.setErrorCode(BridgeErrorConstant.BaseError.PARAMETER_IS_NULL.getErrorCode());
                response.setErrorMsg(BridgeErrorConstant.BaseError.PARAMETER_IS_NULL.getErrorMsg());
                return response;
            }
            LoggerMonitorUtil.start(ScenarioConstant.WAYBILL_PICK_UP_LOGISTICS_DETAIL_GET, receiveParams.getFromAppkey(), mailNo, cpCode);
            TmsWaybillLogisticsDetailQueryRequest request = LogisticsDetailMapper.MAPPER.convertRequest(requestDataObject);

            SubscribeAppInfo subscribeAppInfo = new SubscribeAppInfo();
            subscribeAppInfo.setLinkAppKey(receiveParams.getFromAppkey());
            subscribeAppInfo.setLinkResCode(receiveParams.getCpCode());
            request.setSubscribeAppInfo(subscribeAppInfo);

            TmsWaybillLogisticsDetailQueryResponse queryResponse = tmsWaybillLogisticDetailManager.queryLogisticsDetail(request);
            response.setSuccess(true);
            response.setResult(LogisticsDetailMapper.MAPPER.convertResponse(queryResponse));
            return response;
        } catch (Throwable e) {
            response.setSuccess(false);
            response.setErrorCode(BridgeErrorConstant.BaseError.UNKNOWN.getErrorCode());
            response.setErrorMsg(BridgeErrorConstant.BaseError.UNKNOWN.getErrorMsg());
            PickUpLogUtil.errLog(requestDataObject.getMailNo(), PickUpConstants.Action.LOGISTICS_DETAIL_GET_LINK.name(), "", e.getMessage(), e);
        } finally {
            LoggerMonitorUtil.end(response.isSuccess(), response.getErrorCode());
        }
        return response;
    }

}
