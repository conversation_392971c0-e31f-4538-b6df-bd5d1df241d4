//package com.cainiao.waybill.bridge.biz.link.label.service.impl;
//
//import java.util.List;
//import java.util.Map;
//
//import javax.annotation.Resource;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//
//import com.cainiao.iss.constants.ResultCodeConstant;
//import com.cainiao.iss.domain.dto.ExecutableTask;
//import com.cainiao.iss.domain.result.BaseResult;
//import com.cainiao.lego.exception.ProtectException;
//import com.cainiao.lego.framework.execution.ProtectionExecutor;
//import com.cainiao.link.client.ConciseLinkClient;
//import com.cainiao.logisticscloud.link.api.protocol.SendResult;
//import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
//import com.cainiao.waybill.bridge.biz.label.manager.WaybillPickupCodeRelationManager;
//import com.cainiao.waybill.bridge.biz.wrapper.ForestWrapper;
//import com.cainiao.waybill.bridge.biz.wrapper.LocWrapper;
//import com.cainiao.waybill.bridge.biz.wrapper.PrintEncryptWrapper;
//import com.cainiao.waybill.bridge.biz.wrapper.WaybillCloudPrintWrapper;
//import com.cainiao.waybill.bridge.biz.wrapper.WaybillQueryServiceWrapper;
//import com.cainiao.waybill.bridge.common.constants.BridgeConstants.ProtectionResource;
//import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.SystemError;
//import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
//import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
//import com.cainiao.waybill.bridge.common.util.DateUtils;
//import com.cainiao.waybill.bridge.model.domain.WaybillPickupCodeRelationDO;
//import com.cainiao.waybill.common.seller.dto.WaybillDetailInfo;
//import com.cainiao.waybill.common.util.AddressUtils;
//import com.taobao.cainiao.waybill.exception.WaybillBaseException;
//import com.taobao.cainiao.waybillprint.client.domain.encrypt.CloudPrintInfoDTO;
//import com.taobao.loc.common.domain.LocOrderDO;
//import com.taobao.logistics.domain.dataobject.OrderGoodsDO;
//import com.taobao.pac.client.sdk.dataobject.request.TMS_WAYBILL_PAPERLESS_DATA_SEND.AddressDTO;
//import com.taobao.pac.client.sdk.dataobject.request.TMS_WAYBILL_PAPERLESS_DATA_SEND.TmsWaybillPaperlessDataSendRequest;
//import com.taobao.pac.client.sdk.dataobject.request.TMS_WAYBILL_PAPERLESS_DATA_SEND.UserInfoDTO;
//import com.taobao.pac.client.sdk.dataobject.response.TMS_WAYBILL_PAPERLESS_DATA_SEND.TmsWaybillPaperlessDataSendResponse;
//import org.apache.commons.collections.MapUtils;
//import org.apache.commons.lang.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
///**
// * 无纸化信息下发 handler
// *
// * 下发收件人信息、发件人信息、揽件码、面单号、打印信息(加密)
// * 用于 cp 展示无纸化面单列表，用户小件员便携打印等
// *
// * <AUTHOR>
// * @date 2017/08/02
// */
//@Component
//public class PaperLessDataPushHandler {
//
//    private static final String S_LIMIT = "S_LIMIT";
//
//    private final static Logger LOGGER = LoggerFactory.getLogger(LogAppender.LINK_CP);
//
//    @Resource
//    private LogManager logManager;
//
//    @Resource
//    private WaybillPickupCodeRelationManager waybillPickupCodeRelationManager;
//
//    @Resource
//    private ConciseLinkClient linkClient;
//
//    @Resource
//    private WaybillQueryServiceWrapper waybillQueryServiceWrapper;
//
//    @Resource
//    private LocWrapper locWrapper;
//
//    @Resource
//    private ForestWrapper forestWrapper;
//
//    @Resource
//    private WaybillCloudPrintWrapper waybillCloudPrintWrapper;
//
//    @Resource
//    private PrintEncryptWrapper printEncryptWrapper;
//
//    public BaseResult execute(ExecutableTask task) {
//        BaseResult baseResult = new BaseResult();
//
//        try {
//            String cpCode = task.getParamsMap().get("cpCode");
//            TmsWaybillPaperlessDataSendRequest request = buildRequest(task);
//            String pacLog = getPacLogKey(cpCode, request.getWaybillCode());
//
//            // SendResult<TmsWaybillPaperlessDataSendResponse> response = tmsWaybillPaperlessDataSendService.send(pacLog, null, request.getCpCode(), request, true);
//            SendResult<TmsWaybillPaperlessDataSendResponse> response = ProtectionExecutor.execWithThreadProtect(
//                ProtectionResource.generatePushRes(cpCode),
//                () -> {
//                    return linkClient.sendMsgSyncWithSysResult(pacLog,  cpCode, request);
//                });
//
//            if (!response.isSuccess()) {
//                throw new WaybillBaseException(response.getErrorCode(), response.getErrorMsg() + "^^^fromPAC");
//            }
//
//            baseResult.setSuccess(true);
//            return baseResult;
//
//        } catch (BridgeBaseException e) {
//            logManager.logExceptionMessage(
//                "PaperLessDataPushHandler#execute fail",
//                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + "request:" + task,
//                e, LOGGER);
//
//            baseResult.setSuccess(false);
//            baseResult.setResultCode(S_LIMIT);
//            baseResult.setResultDesc(e.getMessage());
//            return baseResult;
//        } catch (ProtectException e) {
//            logManager.logExceptionMessage(
//                "PaperLessDataPushHandler#execute fail",
//                "request:" + task,
//                e, LOGGER);
//
//            baseResult.setSuccess(false);
//            baseResult.setResultCode(S_LIMIT);
//            baseResult.setResultDesc(e.getMessage());
//            return baseResult;
//        } catch (Throwable e) {
//            logManager.logExceptionMessage(
//                "PaperLessDataPushHandler#execute fail",
//                "request:" + task,
//                e, LOGGER);
//
//            baseResult.setSuccess(false);
//            baseResult.setResultCode(ResultCodeConstant.NEED_REDO);
//            baseResult.setResultDesc(SystemError.SYSTEM_BUSY.getErrorMsg());
//            return baseResult;
//        }
//    }
//
//    private String getPacLogKey(String cpCode, String waybillCode){
//        return cpCode + "_" + waybillCode;
//    }
//
//    private TmsWaybillPaperlessDataSendRequest buildRequest(ExecutableTask task) throws BridgeBaseException {
//        TmsWaybillPaperlessDataSendRequest request = new TmsWaybillPaperlessDataSendRequest();
//
//        Map<String, String> param = task.getParamsMap();
//
//        Long courierId = Long.valueOf(param.get("courierId"));
//        String waybillCode = param.get("waybillCode");
//        String cpCode = param.get("cpCode");
//
//        WaybillPickupCodeRelationDO relationDO = waybillPickupCodeRelationManager.queryByWaybillCode(courierId, cpCode, waybillCode);
//        if (relationDO == null) {
//            throw new BridgeBaseException("waybill_pickup_code_not_found", "找不到此揽件码面单");
//        }
//
//        request.setBranchCode(relationDO.getBranchCode());
//        request.setWaybillCode(relationDO.getWaybillCode());
//        request.setPickupCode(relationDO.getPickupCode());
//        request.setCourierId(relationDO.getCourierId());
//        request.setSellerNick(relationDO.getSellerName());
//        request.setShopName(relationDO.getShopName());
//        request.setGmtCreate(DateUtils.dateToString(relationDO.getGmtCreate()));
//        request.setSellerId(relationDO.getSellerId());
//
//        // 构造发件人信息
//        WaybillDetailInfo waybillDetailInfo = waybillQueryServiceWrapper.queryWaybillDetail(relationDO.getCourierId(), relationDO.getCpCode(), relationDO.getWaybillCode());
//
//        // Sender sender
//        UserInfoDTO sender = new UserInfoDTO();
//        sender.setName(waybillDetailInfo.getSendName());
//        sender.setMobile(waybillDetailInfo.getSendMobile());
//        sender.setPhone(waybillDetailInfo.getSendPhone());
//        AddressDTO senderAddress = parseAddress(waybillDetailInfo.getShippingAddress());
//        if (senderAddress == null) {
//            throw new BridgeBaseException("sender_address_parse_wrong", "发货地址解析出错");
//        }
//        sender.setAddress(senderAddress);
//        request.setSender(sender);
//
//        // 构造收件人信息
//        UserInfoDTO consignee = new UserInfoDTO();
//        consignee.setName(waybillDetailInfo.getConsigneeName());
//        consignee.setMobile(waybillDetailInfo.getConsigneeMobile());
//        consignee.setPhone(waybillDetailInfo.getConsigneePhone());
//        AddressDTO consigneeAddress = parseAddress(waybillDetailInfo.getConsigneeAddress());
//        if (consigneeAddress == null) {
//            throw new BridgeBaseException("consignee_address_parse_wrong", "发货地址解析出错");
//        }
//        consignee.setAddress(consigneeAddress);
//        request.setConsignee(consignee);
//
//        // 构造打印信息(需要有品类信息)
//        String cat = queryCatByTradeId(relationDO.getSellerId(), waybillDetailInfo.getBizSubCode());
//        String printData = buildPrintData(relationDO.getCourierId(), relationDO.getCpCode(), relationDO.getWaybillCode(), cat, relationDO.getPickupCode());
//        request.setPrintData(printData);
//
//        return request;
//    }
//
//    /**
//     * 生成打印信息（需要包含品名），并且加密
//     * @param courierId
//     * @param cpCode
//     * @param waybillCode
//     * @param cat 品类信息
//     * @return 加密打印报文
//     */
//    private String buildPrintData(Long courierId, String cpCode, String waybillCode, String cat, String pickupCode)
//        throws BridgeBaseException {
//        String printData = waybillCloudPrintWrapper.queryPrintData(courierId, waybillCode, cpCode) ;
//        JSONObject printDataMap = JSON.parseObject(printData);
//        String templateURL = printDataMap.getString("templateURL");
//        JSONObject dataMap = printDataMap.getJSONObject("data");
//        if (StringUtils.isNotBlank(cat)) {
//            dataMap.put("extra", buildExtraMap(cat));
//        }
//        if (StringUtils.isNotBlank(pickupCode)) {
//            dataMap.put("pickupCode", pickupCode);
//        }
//        String dataContent = dataMap.toJSONString();
//
//        CloudPrintInfoDTO cloudPrintInfoDTO = printEncryptWrapper.encrypt(templateURL, dataContent);
//        return JSON.toJSONString(cloudPrintInfoDTO);
//    }
//
//    /**
//     * 手动构造 extraMap "extra{item:{detail:'衣服'}}"；
//     *
//     * @param cat 品类信息
//     * @return 增加了物品信息的打印数据
//     */
//    private JSONObject buildExtraMap(String cat) {
//        // 构造 extraMap
//        JSONObject extraMap = new JSONObject();
//        JSONObject itemMap = new JSONObject();
//        itemMap.put("detail", cat != null && cat.length() > 5 ? cat.substring(0, 5) : cat);
//        extraMap.put("item", itemMap);
//
//        return extraMap;
//    }
//
//    /**
//     * 根据订单号获取品类信息。获取第一个商品品类即可。
//     * @param sellerId
//     * @param tradeIdStr
//     * @return
//     * @throws BridgeBaseException
//     */
//    private String queryCatByTradeId(Long sellerId, String tradeIdStr) throws BridgeBaseException {
//        Long tradeId;
//        try {
//            tradeId = Long.valueOf(tradeIdStr);
//        } catch (NumberFormatException e) {
//            throw new BridgeBaseException("waybill_trade_id_is_not_number", "面单详情中的subBizCode不是数字");
//        }
//        List<LocOrderDO> orderDOS = locWrapper.queryOrdersByTradeId(sellerId, tradeId);
//        if (orderDOS != null) {
//            for (LocOrderDO orderDO : orderDOS) {
//                List<OrderGoodsDO> goodsDOS = orderDO.getOrderGoods();
//                if (goodsDOS != null) {
//                    for (OrderGoodsDO goodsDO: goodsDOS) {
//                        String cat = extractCatName(goodsDO.getFeatureMap());
//                        if (StringUtils.isNotBlank(cat)) {
//                            return cat;
//                        }
//                    }
//                }
//            }
//        }
//        return null;
//    }
//
//    /**
//     * 从商品特征值中提取类目
//     *
//     * @param featureMap 商品特征值feature字段map
//     * @return 类目名
//     */
//    private String extractCatName(Map<String, String> featureMap) {
//        if (MapUtils.isNotEmpty(featureMap)) {
//            String catId = featureMap.get("realRootCat");
//            if (StringUtils.isNotBlank(catId)) {
//                try{
//                    return forestWrapper.getCatNameByCatId(Long.valueOf(catId));
//                }catch (Throwable e){
//                    LOGGER.error("PaperLessDataPushHandler#extractCatName exception,catId={}",new Object[]{catId}, e);
//                }
//            }
//        }
//        return StringUtils.EMPTY;
//    }
//
//    private AddressDTO parseAddress(String address) throws BridgeBaseException {
//        AddressDTO addressDTO = null;
//        com.cainiao.waybill.common.admin.dto.AddressDTO originAddress = AddressUtils.getAddressFromString(address);
//        if (originAddress != null) {
//            addressDTO = new AddressDTO();
//            addressDTO.setProvinceName(originAddress.getProvinceName());
//            addressDTO.setCityName(originAddress.getCityName());
//            addressDTO.setAreaName(originAddress.getAreaName());
//            addressDTO.setTownName(originAddress.getTownName());
//            addressDTO.setDetailAddress(originAddress.getAddressDetail());
//        }
//        return addressDTO;
//    }
//}
