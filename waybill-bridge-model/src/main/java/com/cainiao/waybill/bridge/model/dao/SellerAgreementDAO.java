package com.cainiao.waybill.bridge.model.dao;

import com.cainiao.waybill.bridge.model.domain.SellerAgreementDO;
import com.taobao.common.dao.persistence.exception.DAOException;

/**
 * 商家签署协议数据表访问对象
 * <AUTHOR>
 * @date 2017-06-12
 */
public interface SellerAgreementDAO {

    /**
     * 查询商家最近签署的协议
     *
     * @param sellerId
     * @return
     * @throws DAOException
     */
    SellerAgreementDO queryBySellerId(Long sellerId) throws DAOException;

    /**
     * 基本插入函数
     * @param sellerAgreementDO
     * @return
     * @throws DAOException
     */
    Integer insert(SellerAgreementDO sellerAgreementDO) throws DAOException;

}
