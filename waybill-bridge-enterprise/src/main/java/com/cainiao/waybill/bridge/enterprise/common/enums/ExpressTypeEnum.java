package com.cainiao.waybill.bridge.enterprise.common.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/7
 **/
public enum ExpressTypeEnum {

    SF("SF", "顺丰"),

    EMS("E<PERSON>", "EMS"),

    OTHER("<PERSON>THER", "其他"),

    ;


    public String expressCode;
    public String desc;

    public String getExpressCode() {
        return expressCode;
    }

    public void setExpressCode(String expressCode) {
        this.expressCode = expressCode;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

     ExpressTypeEnum(String expressCode, String desc) {
        this.expressCode = expressCode;
        this.desc = desc;
    }

    public static String getByExpressCode(String expressCode) {
        if (null == expressCode) {
            return null;
        }
        for (ExpressTypeEnum value : ExpressTypeEnum.values()) {
            if(Objects.equals(value.getExpressCode(), expressCode)){
                return value.getDesc();
            }
        }
        return null;
    }
}
