package com.cainiao.waybill.bridge.web.pickup.admin.controller;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.exception.PickUpBusinessException;
import com.cainiao.waybill.bridge.biz.pickup.service.AdminWebCommonReportService;
import com.cainiao.waybill.bridge.biz.pickup.service.AdminWebReportService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb.*;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.common.util.AclPermissionHelper;
import com.cainiao.waybill.bridge.web.pickup.admin.PermissionCheck;
import com.cainiao.waybill.bridge.web.pickup.constant.PickUpWebAdminConstant;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.List;

/**
 * 平台订单监控报表相关
 *
 * <AUTHOR>
 * @date 2021/11/15-下午4:09
 */
@RestController
@RequestMapping("/platform/order/report")
public class PlatformOrderReportController {

    @Resource
    private AdminWebReportService adminWebReportService;

    @Resource
    private AclPermissionHelper aclPermissionHelper;

    @Resource
    private HttpServletRequest httpServletRequest;


    /**
     * 获取已接入的平台列表
     *
     * @return :
     */
    @GetMapping("/channels")
    public BaseResult<List<ItemMap>> getOrderChannels() {
        return BaseResult.success(PickUpCommonUtil.parseAdminWebMapItem(PickUpConstants.AdminWeb.MapInfoKey.ORDER_PLATFORM.name()));
    }

    /**
     * @param startTime：查询起始时间
     * @param endTime：查询终止时间
     * @param channel：订单渠道来源
     * @return ：依次按照时间范围、订单渠道来源、CP，三个维度来统计取消、接单、电联、揽收等率值
     */
    @GetMapping
    public BaseResult<PlatformReportResponse> count(String startTime, String endTime, String channel) {
        if (PermissionCheck.checkReportPermission()) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorCode(),
                PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorMsg());
        }

        PickUpLogUtil.info("/platform/order/report : startTime=" + startTime + ", endTime=" + endTime + ", channel=" + channel);
        if (StringUtil.isBlank(channel)) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
        }
        try {
            PlatformReportResponse platformReportResponse = adminWebReportService.platformStatistics(startTime, endTime, channel);
            return BaseResult.success(platformReportResponse);
        } catch (PickUpBusinessException businessException) {
            return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpWebAdminConstant.LogAction.PLATFORM_ORDER_REPORT.name(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
            return BaseResult.systemFail();
        }
    }

    /**
     * @param startTime：查询起始时间
     * @param endTime：查询终止时间
     * @param channel：订单渠道来源
     * @return ：依次按照时间范围、订单渠道来源、CP，三个维度来统计取消、接单、电联、揽收等率值
     */
    @GetMapping("/got/count")
    public BaseResult<PlatformGotReportResponse> gotCount(String startTime, String endTime, String channel) {
        if (PermissionCheck.checkReportPermission()) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorCode(),
                PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorMsg());
        }

        PickUpLogUtil.info("/platform/order/report/got/count : startTime=" + startTime + ", endTime=" + endTime + ", channel=" + channel);
        if (StringUtil.isBlank(channel)) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
        }
        try {
            PlatformGotReportResponse platformGotReportResponse = adminWebReportService.platformGotStatistics(startTime, endTime, channel);
            return BaseResult.success(platformGotReportResponse);
        } catch (PickUpBusinessException businessException) {
            return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpWebAdminConstant.LogAction.PLATFORM_ORDER_REPORT.name(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
            return BaseResult.systemFail();
        }
    }



    @PostMapping("/detail")
    public BaseResult<OrderDetailResponse> detail(@RequestBody OrderDetailRequest detailRequest) {

        if (PermissionCheck.checkCustomerServicePermission()) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorCode(),
                PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorMsg());
        }

        PickUpLogUtil.info("/platform/order/report/detail : detailRequest=" + JSON.toJSONString(detailRequest));
        if (StringUtil.isBlank(detailRequest.getType()) || StringUtil.isBlank(detailRequest.getPlatformName())) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
        }
        try {
            AdminWebCommonReportService.convertDateStr2Date(detailRequest);
            OrderDetailResponse response = adminWebReportService.orderDetailQuery(detailRequest,PickUpConstants.AdminWeb.ReportEnum.PLATFORM);
            return BaseResult.success(response);
        } catch (PickUpBusinessException businessException) {
            return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpWebAdminConstant.LogAction.PLATFORM_ORDER_DETAIL.name(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
            return BaseResult.systemFail();
        }
    }


}
