package com.cainiao.waybill.bridge.enterprise.userPhoneNum.service;

import com.cainiao.waybill.bridge.enterprise.userPhoneNum.requset.EnterpriseUserPhoneNumRequest;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserPhoneNumDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/14
 **/
public interface EnterpriseUserPhoneNumService {

    /**
     * 添加手机号
     * @param request 用户数据
     * @return 添加结果
     */
    int addPhoneNum(EnterpriseUserPhoneNumRequest request);

    /**
     * 根据userId获取手机号
     * @param userId 用户id
     * @return 手机号信息
     */
    WaybillEnterpriseUserPhoneNumDTO getByUserIdAndPhoneNum(String userId, String phoneNum);

    /**
     * 根据userId获取手机号
     * @param userId 用户id
     * @return 手机号信息
     */
    List<String> getUserPhoneByUserId(String userId);

    /**
     * 删除手机号
     * @param userPhoneNumRequest 用户数据
     * @return 删除结果
     */
    int deletePhoneNum(EnterpriseUserPhoneNumRequest userPhoneNumRequest);

}
