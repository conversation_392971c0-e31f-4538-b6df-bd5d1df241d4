package com.cainiao.waybill.bridge.enterprise.common.constant;

import java.util.HashMap;
import java.util.Map;

public class DingTalkApiUrlConstant {

    /**
     * 获取可访问企业相关信息的accessToken的URL
     */
    public static final String URL_GET_CORP_TOKEN = "https://oapi.dingtalk.com/service/get_corp_token";

    /**
     * 获取用户在企业内userId的接口URL
     */
    public static final String URL_GET_USER_INFO = "http://openapi-dewu.cainiao.com:8888/dingding-express/topapi/v2/user/getuserinfo";

    /**
     * 获取用户详情信息
     */
    public static final String URL_USER_GET = "http://openapi-dewu.cainiao.com:8888/dingding-express/topapi/v2/user/get";

    /**
     * 获取用户UserId
     */
    public static final String URL_GET_BY_UNIONID = "http://openapi-dewu.cainiao.com:8888/dingding-express/topapi/user/getbyunionid";

    /**
     * 获取部门详情
     */
    public static final String GET_DEPARTMENT = "http://openapi-dewu.cainiao.com:8888/dingding-express/topapi/v2/department/get";

    /**
     * 获取公司详情
     */
    public static final String GET_AUTH_INFO = "http://openapi-dewu.cainiao.com:8888/dingding-express/service/get_auth_info";


    /**
     * 获取 企业管理员列表
     */
    public static final String GET_ADMIN_USERID_LIST = "https://oapi.dingtalk.com/topapi/user/listadmin";

    /**
     * 获取 企业管理员列表
     */
    public static final String GET_USER_INFO_BY_CODE = "https://oapi.dingtalk.com/topapi/v2/user/getuserinfo";

    /**
     * 根据userId获取用户详情
     */
    public static final String GET_USER_INFO_BY_USERID = "https://oapi.dingtalk.com/topapi/v2/user/get";

    /**
     * 获取企业部门
     */
    public static final String GET_CORP_DEPARTMENT = "https://oapi.dingtalk.com/topapi/v2/department/listsub";

    /**
     * 获取子部门id列表
     */
    public static final String GET_CORP_SUB_DEPARTMENT = "https://oapi.dingtalk.com/topapi/v2/department/listsubid";

    /**
     * 获取部门下所有用户
     */
    public static final String GET_DEPARTMENT_USER = "https://oapi.dingtalk.com/topapi/user/listid";

    /**
     * 获取部门下所有用户
     */
    public static final String GET_USER_ALL_DEPARTMENT = "https://oapi.dingtalk.com/topapi/v2/department/listparentbyuser";

    /**
     * 发送消息
     */
    public static final String SENG_MSG_BY_TEMPLATE = "https://oapi.dingtalk.com/topapi/message/corpconversation/sendbytemplate";

    /**
     * 根据unionId获取用户详情
     */
    public static final String GET_USER_INFO_BY_UNION_ID = "https://oapi.dingtalk.com/topapi/user/getbyunionid";

    /**
     * 根据部门id获取部门详情
     */
    public static final String GET_USER_DEPT_BY_DEPT_ID = "https://oapi.dingtalk.com/topapi/v2/department/get";

    /**
     * 获取企业授权信息
     */
    public static final String GET_ENTERPRISE_AUTH = "https://oapi.dingtalk.com/service/get_auth_info";

    public static Map<String, String> dingDingRequestUrl = new HashMap<>();

    static {
        dingDingRequestUrl.put("GET_CORP_TOKEN", URL_GET_CORP_TOKEN);
        dingDingRequestUrl.put("GET_USER_INFO", URL_GET_USER_INFO);
        dingDingRequestUrl.put("USER_GET", URL_USER_GET);
        dingDingRequestUrl.put("GET_BY_UNIONID", URL_GET_BY_UNIONID);
        dingDingRequestUrl.put("GET_DEPARTMENT", GET_DEPARTMENT);
        dingDingRequestUrl.put("GET_AUTH_INFO", GET_AUTH_INFO);
        dingDingRequestUrl.put("GET_ADMIN_USERID_LIST", GET_ADMIN_USERID_LIST);
        dingDingRequestUrl.put("GET_CORP_DEPARTMENT", GET_CORP_DEPARTMENT);
        dingDingRequestUrl.put("GET_DEPARTMENT_USER", GET_DEPARTMENT_USER);
        dingDingRequestUrl.put("SENG_MSG_BY_TEMPLATE", SENG_MSG_BY_TEMPLATE);
    }

}
