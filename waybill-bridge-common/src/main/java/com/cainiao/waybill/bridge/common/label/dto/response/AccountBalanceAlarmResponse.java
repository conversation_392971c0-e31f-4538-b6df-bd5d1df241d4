package com.cainiao.waybill.bridge.common.label.dto.response;

import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * 账户余额告警配置返回结果
 * <AUTHOR>
 * @since 2017/04/21
 */
public class AccountBalanceAlarmResponse extends BaseDTO {
    private static final long serialVersionUID = 926101621794858889L;
    /**
     * 这里sellerId非传统意义的商家id 是取号的账户id
     */
    private Long    sellerId;

    /**
     * 物流商ID
     */
    private Long    cpId;

    /**
     * 网点编码
     */
    private String  branchCode;

    /**
     * 余额告警值
     */
    private Integer alarmQuantity;

    /**
     * 发送间隔时间（小时）
     */
    private Integer intervalHour;

    /**
     * 号段用途编码,用于区分一个CP下的不同号段
     */
    private String segmentCode;

    /**
     * 用于短信提醒的电话号码
     */
    private String phone;

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public Long getCpId() {
        return cpId;
    }

    public void setCpId(Long cpId) {
        this.cpId = cpId;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public Integer getAlarmQuantity() {
        return alarmQuantity;
    }

    public void setAlarmQuantity(Integer alarmQuantity) {
        this.alarmQuantity = alarmQuantity;
    }

    public Integer getIntervalHour() {
        return intervalHour;
    }

    public void setIntervalHour(Integer intervalHour) {
        this.intervalHour = intervalHour;
    }

    public String getSegmentCode() {
        return segmentCode;
    }

    public void setSegmentCode(String segmentCode) {
        this.segmentCode = segmentCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
