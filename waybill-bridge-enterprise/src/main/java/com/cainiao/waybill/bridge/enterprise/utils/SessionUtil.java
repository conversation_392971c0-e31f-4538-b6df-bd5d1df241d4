package com.cainiao.waybill.bridge.enterprise.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.cainiao.waybill.bridge.enterprise.administrators.request.User;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.CompressionCodecs;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import org.apache.commons.codec.binary.Base64;

/**
 * <AUTHOR> on 2023/5/30.
 */
public class SessionUtil {
    private static final String MACHINE = String.valueOf(System.currentTimeMillis() % 1000000000);
    private static final AtomicLong ID = new AtomicLong(0);

    // 令牌秘钥
    private static final String secret = "waybill-bridge-enterprise";

    // 令牌有效期（默认60分钟）
    private static final Long expiration = 60 * 60 * 24 * 30L;

    /**
     * 随机码
     */
    public static String getUUID(){
        return UUID.randomUUID().toString().replaceAll("-","");
    }

    /**
     * 生成token
     * @param
     * @return
     */
    private static String getJwtBuilder(String subject) {
        SecretKey secretKey = generalKey();
        return Jwts.builder()									//这里其实就是new一个JwtBuilder，设置jwt的body
                .setSubject(subject)							//sub(Subject)：代表这个JWT的主体，即它的所有人，是一个json字符串
                .setId(getUUID())								//设置jti(JWT ID)：是JWT的唯一标识，根据业务需要
                .signWith(SignatureAlgorithm.HS256, secretKey)	//设置签名使用的签名算法和签名使用的秘钥
                .setExpiration(getExpirationDate())
                .compressWith(CompressionCodecs.GZIP)
                .compact();
    }

    /**
     * 生成token
     * @param
     * @return
     */
    private static String getJwtBuilder(String subject, String salt) {
        SecretKey secretKey = generalKey(salt);
        return Jwts.builder()									//这里其实就是new一个JwtBuilder，设置jwt的body
                .setSubject(subject)							//sub(Subject)：代表这个JWT的主体，即它的所有人，是一个json字符串
                .setId(getUUID())								//设置jti(JWT ID)：是JWT的唯一标识，根据业务需要
                .signWith(SignatureAlgorithm.HS256, secretKey)	//设置签名使用的签名算法和签名使用的秘钥
                .setExpiration(getExpirationDate())
                .compressWith(CompressionCodecs.GZIP)
                .compact();
    }

    /**
     * 生成token的过期时间
     * @return
     */
    private static Date getExpirationDate(){
        return new Date(System.currentTimeMillis() + expiration * 1000);
    }

    /**
     * 解析
     * @param token
     * @return
     * @throws Exception
     */
    public static Claims parseJWT(String token) throws Exception {
        SecretKey secretKey = generalKey();
        return Jwts.parser()
                .setSigningKey(secretKey)
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 生成加密后的秘钥 secretKey
     *
     * @return
     */
    public static SecretKey generalKey() {
        byte[] encodedKey = Base64.decodeBase64(secret);
        SecretKey key = new SecretKeySpec(encodedKey, 0, encodedKey.length, "AES");
        return key;
    }

    /**
     * 生成加密后的秘钥 secretKey
     *
     * @return
     */
    public static SecretKey generalKey(String secretKey) {
        byte[] encodedKey = Base64.decodeBase64(secretKey);
        SecretKey key = new SecretKeySpec(encodedKey, 0, encodedKey.length, "AES");
        return key;
    }

    /**
     * 获取当前token的用户信息
     * @param token
     * @return
     */
    public static User getUserFromToken(String token){
        String userString;
        try {
            Claims claims = parseJWT(token);
            userString = claims.getSubject();
            return JSONObject.parseObject(userString, User.class);
        }catch (Exception e){
            return null;
        }
    }

    /**
     * 根据用户信息生成token
     */
    public static String generateToken(User user) {
        return getJwtBuilder(JSON.toJSONString(user));
    }

    /**
     * 根据用户信息生成token
     */
    public static String generateToken(User user, String salt) {
        return getJwtBuilder(JSON.toJSONString(user), salt);
    }

    /**
     * 刷新token
     */
    public String refreshToken(String token) throws Exception {
        SecretKey secretKey = generalKey();
        String subject = Jwts
                .parser()
                .setSigningKey(secretKey)
                .parseClaimsJws(token)
                .getBody().getSubject();

        // 验证用户信息
        User user = JSONObject.parseObject(subject,User.class);
        if (null == user) {
            return "";
        }
        return getJwtBuilder(JSON.toJSONString(user));
    }

    public static String getSession() {
        return MACHINE + '-' + ID.incrementAndGet();
    }

    public static boolean validateToken(String token) {
        try {
            SecretKey secretKey = generalKey();
            Claims claims = Jwts.parser()
                    .setSigningKey(secretKey)
                    .parseClaimsJws(token)
                    .getBody();

            // 检查令牌是否已过期
            Date expirationDate = claims.getExpiration();
            return expirationDate.after(new Date());
        } catch (Exception e) {
            // 各种异常表明验证失败：签名无效、格式错误、令牌过期等
            return false;
        }
    }

    public static String getUserId(String token){
        User userBaseInfo = getUserFromToken(token);
        if(null == userBaseInfo){
            return null;
        }
        return userBaseInfo.getUserId();
    }

    public static String getUserType(String token){
        User userBaseInfo = getUserFromToken(token);
        if(null == userBaseInfo){
            return null;
        }
        return userBaseInfo.getUserType();
    }

//    public static Map<String, Object> getUserInfoFromToken(String token) {
//        Map<String, String> userInfo = new HashMap<>();
//        try {
//            Claims claims = parseJWT(token);
//            userInfo.put("userId", claims.get("userId", String.class));
//            userInfo.put("userType", claims.get("userType", String.class));
//        } catch (Exception e) {
//            // 处理解析异常
//        }
//        return userInfo;
//    }

}
