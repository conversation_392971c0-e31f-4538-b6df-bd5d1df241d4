package com.cainiao.waybill.bridge.enterprise.utils;

import com.cainiao.waybill.bridge.enterprise.common.EnterpriseSwitchHolder;
import com.cainiao.waybill.bridge.enterprise.common.constant.DingTalkApiUrlConstant;
import org.apache.commons.lang3.StringUtils;

public class DingTalkUtils {

    /**
     * 获取钉钉请求的URL
     *
     * <p>根据请求类型获取对应的钉钉请求URL，如果初始获取失败则尝试使用备用URL。
     *
     * @param requestType 请求类型，用于决定URL的类型和路径
     * @return 返回钉钉请求的URL，如果URL为空，则尝试获取备用URL
     */
    public static String getDingDIngUrl(String requestType) {
        String url = EnterpriseSwitchHolder.DINGDING_REQUEST_URL.get(requestType);
        if (StringUtils.isEmpty(url)) {
            url = DingTalkApiUrlConstant.dingDingRequestUrl.get(requestType);
        }
        return url;
    }


}
