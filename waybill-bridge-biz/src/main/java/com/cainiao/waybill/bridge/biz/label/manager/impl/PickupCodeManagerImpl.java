package com.cainiao.waybill.bridge.biz.label.manager.impl;

import java.util.Random;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.label.manager.PickupCodeManager;
import com.cainiao.waybill.bridge.biz.label.manager.PickupNumSequenceTairManager;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.LabelError;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.util.Exceptions;
import com.cainiao.waybill.bridge.model.dao.BranchPickupSequenceDAO;
import com.cainiao.waybill.bridge.model.domain.BranchSequenceDO;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant;
import com.taobao.common.dao.persistence.exception.DAOException;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-27
 */
@Service
public class PickupCodeManagerImpl implements PickupCodeManager {

    @Resource
    private BranchPickupSequenceDAO branchPickupSequenceDAO;

    @Resource
    private PickupNumSequenceTairManager pickupNumSequenceTairManager;


    /**
     * 生成揽件码函数
     *   1. 锁定 branchSequence 中对应网点的记录；获取网点最新的 newSequence = oldSequence + 1，并更新记录。
     *   2. 然后 serialNum = newSequence % 揽件码总数
     *   3. 查表得到 serialNum 对应的 pickupNum
     *   4. 组装上对应的 cp 信息并返回
     * @param cpCode
     * @param branchCode
     * @return
     */
    @Override
    public String generatePickupCode(String cpCode, String branchCode) throws BridgeBaseException {

        try {
            Long branchSequence = getOrInitBranchSequence(cpCode, branchCode);
            Long serialNum = branchSequence % BridgeConstants.PickupNum.COUNT;
            Long pickupNum = pickupNumSequenceTairManager.getPickupNum(serialNum);
            if (pickupNum == null) {
                throw Exceptions.newBridgeBaseException(LabelError.PICKUP_NUM_NOT_FOUND_ERROR);
            }

            String pickupHeader = BridgeSwitch.cpCodePickupHeaderMap.get(cpCode.toUpperCase());
            if (pickupHeader == null) {
                throw Exceptions.newBridgeBaseException(LabelError.CP_CODE_NO_PICKUP_HEADER_ERROR);
            }

            return pickupHeader + pickupNum;

        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(WaybillErrorConstant.SystemError.DAO_EXCEPTION.getErrorCode(),
                    WaybillErrorConstant.SystemError.DAO_EXCEPTION.getErrorMsg(), e);
        }
    }


    /**
     * 获取数据库中网点对应的 sequence
     *   如果库中没有则执行插入操作，branch 对应的初始 sequence 设置为 0 ~ 99999 下的随机数
     * @param cpCode
     * @param branchCode
     * @return
     * @throws DAOException
     */
    private Long getOrInitBranchSequence(String cpCode, String branchCode) throws DAOException {
        BranchSequenceDO branchSequenceDO = branchPickupSequenceDAO.queryByCpAndBranchCode(cpCode, branchCode);
        if (branchSequenceDO == null) {
            branchSequenceDO = new BranchSequenceDO();
            branchSequenceDO.setCpCode(cpCode);
            branchSequenceDO.setBranchCode(branchCode);

            Random random = new Random();
            Integer startSeq = random.nextInt(BridgeConstants.PickupNum.BRANCH_SEQUENCE_RANDOM_BOUND);
            branchSequenceDO.setSequence(startSeq.longValue());
            // 通过数据库 uk 防止插入两个相同的网点
            branchPickupSequenceDAO.insert(branchSequenceDO);
        }

        return branchPickupSequenceDAO.incrementAndGet(cpCode, branchCode);
    }
}
