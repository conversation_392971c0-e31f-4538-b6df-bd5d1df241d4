package com.cainiao.waybill.bridge.biz.hsf;

import com.cainiao.waybill.common.dto.ClientInfoDTO;
import com.cainiao.waybill.common.result.BaseResultDTO;
import com.cainiao.waybill.common.seller.dto.PossibleCpCodesByWaybillCodeRequest;
import com.cainiao.waybill.common.seller.dto.PossibleCpCodesByWaybillCodeResponse;
import com.cainiao.waybill.common.seller.dto.WaybillDetailQueryDTO;
import com.cainiao.waybill.common.seller.dto.WaybillDetailQueryResponseDTO;
import com.cainiao.waybill.galaxy.order.domain.dto.BaseResult;
import com.cainiao.waybill.galaxy.order.domain.dto.CloudPrintApplyInfosRequest;
import com.cainiao.waybill.galaxy.order.domain.dto.CloudPrintApplyInfosResponse;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/1/18-下午5:05
 */
public interface WaybillTestService {

    BaseResultDTO<WaybillDetailQueryResponseDTO> waybillDetailQueryWithoutSellerId(WaybillDetailQueryDTO request, boolean isQueryHistoricalData, ClientInfoDTO clientInfo);

    BaseResultDTO<List<PossibleCpCodesByWaybillCodeResponse>> queryPossibleCpCodesByWaybillCode(String mailNo, ClientInfoDTO clientInfo);

    BaseResultDTO<WaybillDetailQueryResponseDTO> waybillDetailQueryWithoutSellerIdAndCp(String waybillCode, boolean isQueryHistoricalData, ClientInfoDTO clientInfo);

    BaseResult<CloudPrintApplyInfosResponse> CloudPrintInfosForInner(CloudPrintApplyInfosRequest request);

    BaseResultDTO<Integer> sendSmsToUser(String templateCode, Map<String, Map<String, String>> mobileToParams,
        String userId, String channelCode, String sign);
}
