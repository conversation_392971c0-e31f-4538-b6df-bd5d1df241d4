#菜鸟平台技术部制作的java应用基础镜像
FROM reg.docker.alibaba-inc.com/aone-base/cn-pandora_boot-base:online
ARG SAR_VERSION=2022-03-release-hsf2
RUN rm -rf /home/<USER>/taobao-hsf.tgz && \
wget -c "http://pandora.alibaba-inc.com/pandora-web/sar/2024-09-release-fix-hsf/taobao-hsf.tgz" -O /home/<USER>/taobao-hsf.tgz

# 这里替换成你自己的应用名
ARG appname=waybill-bridge

RUN echo "APP_BASE_DIR_NAME=${appname}" >> /home/<USER>/setappenv.sh

VOLUME /home/<USER>/${appname}/logs /home/<USER>/${appname}/forest

# 1、如果你需要定制你的nginx配置文件，比如新增一个ip白名单文件ip.conf,可以把这个文件写在 environment/webconf/conf 目录下，然后修改 nginx-proxy.conf 文件，并放置在该目录下，在dockerfile中，使用COPY指令，拷贝到镜像的 /home/<USER>/webconf/conf/（必须是这个目录）
# 2、如果你的是老应用，线上环境的nginx-proxy.conf 文件已经变化了很多了，可以直接 拷贝线上的该文件，到 environment/webconf/conf 目录下，同样使用 COPY指令，拷贝到镜像的 /home/<USER>/webconf/conf/（必须是这个目录）
# 3、如上，老应用可以直接拷贝线上的配置文件来编辑，注意目录路径
# 4、对于新应用，如果没有任何定制的话，不用打开COPY这一行，基础模板里面默认都会开启http服务，server_name 是default，也就是都支持，如果需要定制，可以拷贝已有应用的该文件，来修改
## COPY webconf/ /home/<USER>/webconf/conf/


# 1、对于老应用的容器启动脚本修改过的，或者新老应用需要定制的，也可以使用如上方案来操作，比如定义目录 environment/appconf/，存放 容器的脚本或者配置
## COPY appconf/ /home/<USER>/appconf/conf/


COPY ${appname}.tgz /home/<USER>/${appname}/target/${appname}.tgz


# 安装 Python 3.7 和 pip3
# 如果此处构建失败，一般是连接超时导致，重试即可
RUN yum install -y gcc openssl-devel bzip2-devel libffi-devel && \
    cd /usr/src && \
    wget https://routing-face.oss-cn-hangzhou.aliyuncs.com/%E5%85%AC%E5%85%B1/Python-3.7.12.tgz && \
    tar xzf Python-3.7.12.tgz && \
    cd Python-3.7.12 && \
    ./configure --enable-optimizations && \
    make altinstall && \
    ln -sf /usr/local/bin/pip3.7 /usr/bin/pip3 && \
    ln -sf /usr/local/bin/python3.7 /usr/bin/python3 && \
    rm -f /usr/src/Python-3.7.12.tgz && \
    rm -rf /usr/src/Python-3.7.12 && \
    yum clean all

# 可选: 确认 Python 和 pip 安装成功
RUN python3 --version && pip3 --version


# 安装 依赖
# 如果此处构建失败，一般是连接超时导致，重试即可
# 因pip默认访问国外的https://pypi.org/simple来下载pip包，这个地址经常被墙，现在改为：https://pypi.antfin-inc.com/simple/ 蚂蚁集团内部源，具体参考文档：https://aliyuque.antfin.com/aone/build_user_guide/2005?spm=2a764a39.2ef5001f.0.0.2a7b2fa4sPmHbc
RUN python3 -m pip install --no-cache-dir -i https://pypi.antfin-inc.com/simple/ --upgrade pip && \
    pip3 install -v pandas -i http://artlab.alibaba-inc.com/1/pypi/simple --trusted-host artlab.alibaba-inc.com && \
    pip3 install -v numpy -i http://artlab.alibaba-inc.com/1/pypi/simple --trusted-host artlab.alibaba-inc.com && \
    pip3 install -v odps -i http://artlab.alibaba-inc.com/1/pypi/simple --trusted-host artlab.alibaba-inc.com && \
    pip3 install ortools -i http://artlab.alibaba-inc.com/1/pypi/simple --trusted-host artlab.alibaba-inc.com && \
    pip3 install urllib3==1.26.6 -i http://artlab.alibaba-inc.com/1/pypi/simple --trusted-host artlab.alibaba-inc.com && \
    pip3 install flask -i http://artlab.alibaba-inc.com/1/pypi/simple --trusted-host artlab.alibaba-inc.com && \
    yum clean all



# 复制 Python 文件到容器中
COPY py/model.py /home/<USER>
COPY py/ntb_flask.py /home/<USER>

# 设置工作目录
WORKDIR /home/<USER>

#这条指令告诉 Docker 容器将会在运行时监听 8050 端口。需要注意的是，这只是一个文档说明，不会实际开放该端口。要想外部访问这个端口，需要在 docker run 时使用 -p 或 --publish 参数将容器的端口映射到宿主机的端口上
#EXPOSE 8050

# 启动 Python 文件
ENTRYPOINT ["python3 ntb_flask.py & /home/<USER>/start.sh"]

#CCC接入脚本，用于统计增量代码行覆盖率，需要放在dockerfile最下面，生产环境不要添加
RUN echo -e '\n ccc_oss_url="http://ccc-start.cn-zhangjiakou.oss-internal.aliyun-inc.com/"' >> /home/<USER>/appconf/bin/setenv.sh \
    &&  echo 'ENV=$(curl http://jmenv.tbsite.net:8080/env)' >> /home/<USER>/appconf/bin/setenv.sh \
    &&  echo 'if [[ $ENV =~ "daily" ]]; then' >> /home/<USER>/appconf/bin/setenv.sh \
    &&  echo '    ccc_oss_url="http://ccc-start.oss-cn-zhangjiakou.aliyuncs.com/"' >> /home/<USER>/appconf/bin/setenv.sh \
    &&  echo 'fi' >> /home/<USER>/appconf/bin/setenv.sh \
    &&  echo 'if [[ ! -z "${SERVICE_OPTS}" && ! $(echo "${SERVICE_OPTS}" | grep -q "ccc_startup") ]]; then' >> /home/<USER>/appconf/bin/setenv.sh \
    &&  echo '    SERVICE_OPTS="${SERVICE_OPTS} `curl ${ccc_oss_url}ccc_startup.sh |sh`"' >> /home/<USER>/appconf/bin/setenv.sh \
    &&  echo 'elif [[ ! -z "${CATALINA_OPTS}" && ! $(echo "${CATALINA_OPTS}" | grep -q "ccc_startup") ]]; then' >> /home/<USER>/appconf/bin/setenv.sh \
    &&  echo '    CATALINA_OPTS="${CATALINA_OPTS} `curl ${ccc_oss_url}ccc_startup.sh |sh`"' >> /home/<USER>/appconf/bin/setenv.sh \
    &&  echo 'fi' >> /home/<USER>/appconf/bin/setenv.sh

