package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class WaybillKeyLogDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   渠道
     *
     *
     * @mbg.generated
     */
    private String orderChannels;

    /**
     * Database Column Remarks:
     *   业务ID
     *
     *
     * @mbg.generated
     */
    private String bizId;

    /**
     * Database Column Remarks:
     *   鹰眼ID
     *
     *
     * @mbg.generated
     */
    private String traceId;

    /**
     * Database Column Remarks:
     *   日志级别
     *
     *
     * @mbg.generated
     */
    private String logLevel;

    /**
     * Database Column Remarks:
     *   日志动作标识
     *
     *
     * @mbg.generated
     */
    private String action;

    /**
     * Database Column Remarks:
     *   日志代码
     *
     *
     * @mbg.generated
     */
    private String logCode;

    /**
     * Database Column Remarks:
     *   日志描述
     *
     *
     * @mbg.generated
     */
    private String logMsg;

    /**
     * Database Column Remarks:
     *   业务数据
     *
     *
     * @mbg.generated
     */
    private String businessData;

    /**
     * Database Column Remarks:
     *   异常信息
     *
     *
     * @mbg.generated
     */
    private String exception;

    /**
     * Database Column Remarks:
     *   备注
     *
     *
     * @mbg.generated
     */
    private String remark;

    /**
     *
     * @return the value of waybill_key_log.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_key_log.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_key_log.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_key_log.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_key_log.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_key_log.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_key_log.order_channels
     *
     * @mbg.generated
     */
    public String getOrderChannels() {
        return orderChannels;
    }

    /**
     *
     * @param orderChannels the value for waybill_key_log.order_channels
     *
     * @mbg.generated
     */
    public void setOrderChannels(String orderChannels) {
        this.orderChannels = orderChannels;
    }

    /**
     *
     * @return the value of waybill_key_log.biz_id
     *
     * @mbg.generated
     */
    public String getBizId() {
        return bizId;
    }

    /**
     *
     * @param bizId the value for waybill_key_log.biz_id
     *
     * @mbg.generated
     */
    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    /**
     *
     * @return the value of waybill_key_log.trace_id
     *
     * @mbg.generated
     */
    public String getTraceId() {
        return traceId;
    }

    /**
     *
     * @param traceId the value for waybill_key_log.trace_id
     *
     * @mbg.generated
     */
    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    /**
     *
     * @return the value of waybill_key_log.log_level
     *
     * @mbg.generated
     */
    public String getLogLevel() {
        return logLevel;
    }

    /**
     *
     * @param logLevel the value for waybill_key_log.log_level
     *
     * @mbg.generated
     */
    public void setLogLevel(String logLevel) {
        this.logLevel = logLevel;
    }

    /**
     *
     * @return the value of waybill_key_log.action
     *
     * @mbg.generated
     */
    public String getAction() {
        return action;
    }

    /**
     *
     * @param action the value for waybill_key_log.action
     *
     * @mbg.generated
     */
    public void setAction(String action) {
        this.action = action;
    }

    /**
     *
     * @return the value of waybill_key_log.log_code
     *
     * @mbg.generated
     */
    public String getLogCode() {
        return logCode;
    }

    /**
     *
     * @param logCode the value for waybill_key_log.log_code
     *
     * @mbg.generated
     */
    public void setLogCode(String logCode) {
        this.logCode = logCode;
    }

    /**
     *
     * @return the value of waybill_key_log.log_msg
     *
     * @mbg.generated
     */
    public String getLogMsg() {
        return logMsg;
    }

    /**
     *
     * @param logMsg the value for waybill_key_log.log_msg
     *
     * @mbg.generated
     */
    public void setLogMsg(String logMsg) {
        this.logMsg = logMsg;
    }

    /**
     *
     * @return the value of waybill_key_log.business_data
     *
     * @mbg.generated
     */
    public String getBusinessData() {
        return businessData;
    }

    /**
     *
     * @param businessData the value for waybill_key_log.business_data
     *
     * @mbg.generated
     */
    public void setBusinessData(String businessData) {
        this.businessData = businessData;
    }

    /**
     *
     * @return the value of waybill_key_log.exception
     *
     * @mbg.generated
     */
    public String getException() {
        return exception;
    }

    /**
     *
     * @param exception the value for waybill_key_log.exception
     *
     * @mbg.generated
     */
    public void setException(String exception) {
        this.exception = exception;
    }

    /**
     *
     * @return the value of waybill_key_log.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @param remark the value for waybill_key_log.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", orderChannels=").append(orderChannels);
        sb.append(", bizId=").append(bizId);
        sb.append(", traceId=").append(traceId);
        sb.append(", logLevel=").append(logLevel);
        sb.append(", action=").append(action);
        sb.append(", logCode=").append(logCode);
        sb.append(", logMsg=").append(logMsg);
        sb.append(", businessData=").append(businessData);
        sb.append(", exception=").append(exception);
        sb.append(", remark=").append(remark);
        sb.append("]");
        return sb.toString();
    }
}