package com.cainiao.waybill.bridge.biz.user.manager;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.common.mobileCode.service.SmsService;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error;
import com.cainiao.waybill.bridge.biz.pickup.constants.WtBizTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPreChargeTradeListManager;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.wrapper.CnMemberClientWrapper;
import com.cainiao.waybill.bridge.biz.wrapper.TairManagerWrapper;
import com.cainiao.waybill.bridge.biz.wrapper.WtPayPlatformWrapper;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants.SMSSendInfo;
import com.cainiao.waybill.bridge.common.dto.WtAccountDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.constants.CustomerManageStatusEnum;
import com.cainiao.waybill.bridge.model.dto.CnMemberInfoDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillPreChargeTradeListMapper;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/29 18:48
 **/
@Component
public class CnUserManagerImpl implements CnUserManager {

    @Resource
    WaybillPreChargeTradeListMapper waybillPreChargeTradeListMapper;

    @Resource
    private WtPayPlatformWrapper wtPayPlatformWrapper;

    @Resource
    private CnMemberClientWrapper cnMemberClientWrapper;

    @Resource
    private TairManagerWrapper tairManagerWrapper;

    @Resource
    private SmsService smsService;

    @Resource
    private WaybillPreChargeTradeListManager waybillPreChargeTradeListManager;

    @Override
    public String queryPreChargeManageStatus(String accountId) {
        // mock测试账号
        if(StringUtils.isNotBlank(accountId)
            && BridgeSwitch.MOCK_PRE_CHARGE_ACCOUNT_ID_LIST.contains(accountId)
            && StringUtils.isNotBlank(BridgeSwitch.MOCK_PRE_CHARGE_MANAGE_STATUS)){
            return BridgeSwitch.MOCK_PRE_CHARGE_MANAGE_STATUS;
        }

        try {
            // 查询账户余额
            WtAccountDTO accountInfo = wtPayPlatformWrapper.queryAccount(accountId);
            int balance = accountInfo.getBalance().intValue();

            // 查询上个月用户平均日扣款金额 - 单位分
            Date[] lastMonthArray = DateUtils.getLastMonthStartAndEnd();
            Date start = lastMonthArray[0];
            Date end = lastMonthArray[1];

            String cacheKey = accountId + "_" +start.getTime() + "_" + end.getTime();
            Long totalAmount;
            Object cacheAmountObj = tairManagerWrapper.get(cacheKey);
            if(cacheAmountObj == null){
                // 查询上月用户扣款金额总额
                totalAmount = waybillPreChargeTradeListManager.querySumPayAmount(start, end, accountId,
                    WtBizTypeEnum.PAY.getType());
                tairManagerWrapper.put(cacheKey, totalAmount, 60 * 60 * 24 * 31);
            }else {
                totalAmount = (Long)cacheAmountObj;
            }

            int days = ((Double)DateUtils.getIntervalOfDays(start, end)).intValue();

            // 用户平均日扣款金额(分)
            int payAvgAmount = totalAmount.intValue() / days;

            // 预充值禁止下单最小余额(分)
            int defaultMinPrePayDayAvgAmount = BridgeSwitch.DEFAULT_MIN_PRE_PAY_DAY_AVG_AMOUNT * 100;
            // 兜底最小金额
            if(payAvgAmount < defaultMinPrePayDayAvgAmount){
                payAvgAmount = defaultMinPrePayDayAvgAmount;
            }
            if (payAvgAmount > balance) {
                // 发送欠款通知短信
                sendSms(accountId, BridgeSwitch.PRE_CHARGE_FORBID_SMS_TEMPLATE_CODE);
                return CustomerManageStatusEnum.INVALID.getStatus();
            } else if (payAvgAmount * BridgeSwitch.PRE_CHARGE_MANGE_WARN_DAYS > balance) {
                // 发送提醒充值短信
                sendSms(accountId, BridgeSwitch.PRE_CHARGE_WARN_SMS_TEMPLATE_CODE);
                return CustomerManageStatusEnum.WARN.getStatus();
            }
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog(accountId, PickUpConstants.Action.PRE_CHARGE_QUERY_MANAGE_STATUS_ERROR.name(), e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog(accountId, PickUpConstants.Action.PRE_CHARGE_QUERY_MANAGE_STATUS_ERROR.name(), Error.SYSTEM_ERROR, ExceptionUtils.getFullStackTrace(e));
        }
        return CustomerManageStatusEnum.VALID.getStatus();
    }

    /**
     * 发送提醒充值短信
     * @param accountId
     * @param templateCode
     */
    private void sendSms(String accountId, String templateCode) {

        String cacheKey = accountId + "_" + templateCode;
        Object cacheAmountObj = tairManagerWrapper.get(cacheKey);
        if(cacheAmountObj != null){
            // 防打扰 同一天内同一个客户相同短信不再重发
            PickUpLogUtil.info("预充值预警短信触发防重发拦截|cacheKey:" + cacheKey);
            return;
        }

        CnMemberInfoDTO cnMemberInfoDTO = cnMemberClientWrapper.queryCnAccountInfo(accountId);
        if(cnMemberInfoDTO == null || StringUtils.isAnyBlank(cnMemberInfoDTO.getMobile(), templateCode)){
            throw new BridgeBusinessException(PickUpConstants.Error.CN_MEMBER_INFO_ERROR.getErrorCode(), PickUpConstants.Error.CN_MEMBER_INFO_ERROR.getErrorMsg());
        }
        Map<String, String> params = new HashMap<>(4);
        String phone = cnMemberInfoDTO.getMobile();
        params.put("phone", phone);
        params.put("username", cnMemberInfoDTO.getName());
        boolean flag = smsService.sendTwSms(cnMemberInfoDTO.getMobile(), templateCode, params);
        if(!flag){
            PickUpLogUtil.errLog("预充值预警短信发送失败", PickUpConstants.Action.PRE_CHARGE_SEND_SMS_ERROR.name(), Error.SYSTEM_ERROR, "accountId:" + accountId + ",phone:" + phone + ",templateCode:" + templateCode);
            throw new BridgeBusinessException(PickUpConstants.Error.PRE_CHARGE_SEND_SMS_ERROR.getErrorCode(), PickUpConstants.Error.PRE_CHARGE_SEND_SMS_ERROR.getErrorMsg());
        }else{
            // 缓存1天
            tairManagerWrapper.put(cacheKey, 1, 60 * 60 * 24);
            PickUpLogUtil.info("预充值预警短信发送成功|accountId:" + accountId + ",phone:" + phone + ",templateCode:" + templateCode);
        }

    }


}
