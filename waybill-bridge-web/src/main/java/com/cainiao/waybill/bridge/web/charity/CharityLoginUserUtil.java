package com.cainiao.waybill.bridge.web.charity;

import com.cainiao.cnlogin.api.context.CnUserInfo;
import com.cainiao.cnlogin.api.context.CnUserInfoUtil;
import com.cainiao.waybill.bridge.biz.charity.request.CharityBaseRequest;

/**
 * <AUTHOR> zouping.fzp
 * @Classname LoginUserUtil
 * @Description
 * @Date 2022/8/29 11:43 上午
 * @Version 1.0
 */
public class CharityLoginUserUtil {

    private static final ThreadLocal<CharityBaseRequest> LOGIN_REQ = new ThreadLocal<>();

    public static void buildBaseReq(Long loginUserId, String scene, String role) {
        CharityBaseRequest request = new CharityBaseRequest();
        request.setLoginUserId(loginUserId);
        request.setScene(scene);
        request.setRole(role);
        LOGIN_REQ.set(request);
    }

    private static CharityBaseRequest getBaseReq(){
        return LOGIN_REQ.get();
    }

    public static void clear(){
       LOGIN_REQ.remove();
    }

    public static <T extends CharityBaseRequest> void popLoginInfo(T params){
        CharityBaseRequest baseRequest = getBaseReq();
        params.setScene(baseRequest.getScene());
        params.setLoginUserId(baseRequest.getLoginUserId());
        params.setLoginAccountId(baseRequest.getLoginAccountId());
        params.setRole(baseRequest.getRole());
    }
}
