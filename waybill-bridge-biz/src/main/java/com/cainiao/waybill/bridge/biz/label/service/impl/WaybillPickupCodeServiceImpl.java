package com.cainiao.waybill.bridge.biz.label.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;

//import com.cainiao.iss.domain.result.PublishResult;
import com.cainiao.rc.dto.ClientInfo;
import com.cainiao.rc.dto.RcResult;
import com.cainiao.rc.dto.resource.RcCompanyDTO;
import com.cainiao.rc.service.resource.client.RcCompanyReadServiceClient;
import com.cainiao.waybill.bridge.biz.label.manager.AccountBalanceAlarmManager;
import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
import com.cainiao.waybill.bridge.biz.label.manager.WaybillPickupCodeRelationManager;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
//import com.cainiao.waybill.bridge.biz.middleware.iss.BridgeISSClient;
import com.cainiao.waybill.bridge.biz.utils.FeatureJsonUtils;
import com.cainiao.waybill.bridge.biz.wrapper.RcCompanyReadWrapper;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants.WaybillPickupCodeFeature;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.LabelError;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.exception.util.Exceptions;
import com.cainiao.waybill.bridge.common.label.dto.AccountBalanceAlarmQueryDTO;
import com.cainiao.waybill.bridge.common.label.dto.WaybillPickupCodeDTO;
import com.cainiao.waybill.bridge.common.label.dto.request.UpdateWeightRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeApplyNewRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeQueryRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeUpdateRequest;
import com.cainiao.waybill.bridge.common.label.dto.response.WaybillPickupCodeApplyResponse;
import com.cainiao.waybill.bridge.common.label.service.WaybillPickupCodeService;
import com.cainiao.waybill.bridge.common.label.validator.WaybillPickupCodeValidator;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.util.BaseResultHelper;
import com.cainiao.waybill.bridge.common.util.Page;
import com.cainiao.waybill.bridge.model.domain.WaybillPickupCodeRelationDO;
import com.taobao.cainiao.waybill.constants.WaybillConstant.SegmentCodeEnum;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/**
 * Description: 揽件码面单服务 impl
 *
 * <AUTHOR>
 * @Date 2017-04-27
 */
@HSFProvider(serviceInterface = WaybillPickupCodeService.class)
public class WaybillPickupCodeServiceImpl implements WaybillPickupCodeService {

    private final static Logger LOGGER = LoggerFactory.getLogger(LogAppender.WAYBILL_PICKUP_CODE);

    @Resource
    private LogManager logManager;

    @Resource
    private WaybillPickupCodeRelationManager waybillPickupCodeRelationManager;

    @Resource
    private AccountBalanceAlarmManager waybillAccountBalanceAlarmManager;

    @Resource
    private RcCompanyReadWrapper rcCompanyReadWrapper;

    //@Resource
    //private BridgeISSClient bridgeISSClient;

    //@Override
    //public BaseResultDTO<WaybillPickupCodeApplyResponse> applyNew(WaybillPickupCodeApplyNewRequest request,
    //                                                              Long courierId, ClientInfoDTO clientInfoDTO) {
    //
    //    try {
    //        // 参数校验
    //        WaybillPickupCodeValidator.validateApplyNewRequest(request, courierId, clientInfoDTO);
    //
    //        // 生成"揽件码"面单
    //        WaybillPickupCodeRelationDO relationDO = waybillPickupCodeRelationManager.createPickupCodeWaybill(request,
    //            courierId, clientInfoDTO);
    //        /**
    //         * 电子面单已有的余额提醒是通过生命周期消息，在waybill-async中异步处理的。
    //         * 标签项目1.0 暂不耦合waybill-async，同步check余额消息
    //         */
    //        try {
    //            Long cpId = rcCompanyReadWrapper.getCpIdByCpCode(request.getCpCode());
    //            if (cpId == null) {
    //                throw Exceptions.newBridgeBaseException("RC_QUERY_FAIL", "rc query fail");
    //            }
    //            AccountBalanceAlarmQueryDTO accountBalanceAlarmQueryDTO = new AccountBalanceAlarmQueryDTO();
    //            accountBalanceAlarmQueryDTO.setCpId(cpId);
    //            accountBalanceAlarmQueryDTO.setAccountId(courierId);
    //            accountBalanceAlarmQueryDTO.setBranchCode(request.getBranchCode());
    //            accountBalanceAlarmQueryDTO.setSegmentCode(SegmentCodeEnum.NORMAL_SEGMENT_CODE);
    //            waybillAccountBalanceAlarmManager.checkAndSendAlarm(accountBalanceAlarmQueryDTO);
    //        } catch (Throwable e) {
    //            logManager.logExceptionMessage(
    //                "WaybillPickupCodeServiceImpl#waybillAccountBalanceAlarmManager.checkAndSendAlarm fail",
    //                "request:" + request + "courierId:" + courierId,
    //                e, LOGGER);
    //        }
    //        // 构造response
    //        WaybillPickupCodeApplyResponse response = new WaybillPickupCodeApplyResponse();
    //        response.setWaybillCode(relationDO.getWaybillCode());
    //        response.setPickupCode(relationDO.getPickupCode());
    //
    //        // 下发面单信息
    //        pushPaperlessDataTask(courierId, relationDO.getCpCode(), relationDO.getWaybillCode());
    //
    //        return BaseResultHelper.newResult(response);
    //    } catch (BridgeValidationException e) {
    //        logManager.logExceptionMessage("applyNewWaybill",
    //            "errCode:" + e.getErrorCode() + ", errMsg:" + e.getErrorMessage() + ", request:" + request + ", courierId:"
    //                + courierId, e, LOGGER);
    //        return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
    //    } catch (BridgeBaseException e) {
    //        logManager.logExceptionMessage("applyNewWaybill",
    //            "errCode:" + e.getErrorCode() + ", errMsg:" + e.getErrorMessage() + ", request:" + request + ", courierId:"
    //                + courierId, e, LOGGER);
    //        return BaseResultHelper.errorResult(BridgeErrorConstant.SystemError.SYSTEM_BUSY);
    //    } catch (Throwable e) {
    //        logManager.logExceptionMessage("applyNewWaybill", "WaybillPickupCodeApplyNewRequest:" + request, e, LOGGER);
    //        return BaseResultHelper.errorResult(BridgeErrorConstant.SystemError.SYSTEM_BUSY);
    //    }
    //}

    @Override
    public BaseResultDTO<Page<WaybillPickupCodeDTO>> query(WaybillPickupCodeQueryRequest request, Long courierId,
                                                           ClientInfoDTO clientInfoDTO) {
        try {
            // 参数校验
            WaybillPickupCodeValidator.validateQueryRequest(request, courierId, clientInfoDTO);

            // 查询数据库
            Page<WaybillPickupCodeRelationDO> relationDOs = waybillPickupCodeRelationManager.queryRelations(request,
                courierId, clientInfoDTO);

            // 构造response
            return BaseResultHelper.newResult(buildWaybillPickupCodePageResponse(relationDOs));

        } catch (BridgeValidationException e) {
            logManager.logExceptionMessage("queryPickupCode",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request + ", courierId:"
                    + courierId, e,
                LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("queryPickupCode",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request + ", courierId:"
                    + courierId, e,
                LOGGER);
            return BaseResultHelper.errorResult(BridgeErrorConstant.SystemError.SYSTEM_BUSY);
        } catch (Throwable e) {
            logManager.logExceptionMessage("applyNewWaybill",
                "WaybillPickupCodeApplyNewRequest:" + request + ", courierId:" + courierId, e,
                LOGGER);
            return BaseResultHelper.errorResult(BridgeErrorConstant.SystemError.SYSTEM_BUSY);
        }
    }

    private Page<WaybillPickupCodeDTO> buildWaybillPickupCodePageResponse(
        Page<WaybillPickupCodeRelationDO> relationDOPage) {
        List<WaybillPickupCodeDTO> waybillPickupCodeDTOS = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(relationDOPage.getData())) {
            for (WaybillPickupCodeRelationDO relationDO : relationDOPage.getData()) {
                WaybillPickupCodeDTO waybillPickupCodeDTO = new WaybillPickupCodeDTO();

                waybillPickupCodeDTO.setCourierId(relationDO.getCourierId());
                waybillPickupCodeDTO.setCpCode(relationDO.getCpCode());
                waybillPickupCodeDTO.setWaybillCode(relationDO.getWaybillCode());
                waybillPickupCodeDTO.setPickupCode(relationDO.getPickupCode());
                waybillPickupCodeDTO.setSellerName(relationDO.getSellerName());
                waybillPickupCodeDTO.setShopName(relationDO.getShopName());
                waybillPickupCodeDTO.setCreateDate(DateFormatUtils.format(relationDO.getGmtCreate(), "yyyy-MM-dd"));
                waybillPickupCodeDTO.setWeight(relationDO.getWeight());

                waybillPickupCodeDTO.setSettlement("未设置");
                if(!StringUtils.isEmpty(relationDO.getFeature()) && FeatureJsonUtils.keyIsExist(relationDO.getFeature(), WaybillPickupCodeFeature.FEATURE_SETTLERMENT)) {
                    String settlement = FeatureJsonUtils.getStringValue(relationDO.getFeature(), WaybillPickupCodeFeature.FEATURE_SETTLERMENT) ;
                    waybillPickupCodeDTO.setSettlement(settlement);
                }

                waybillPickupCodeDTOS.add(waybillPickupCodeDTO);
            }
        }

        Page<WaybillPickupCodeDTO> response = new Page<>(waybillPickupCodeDTOS, relationDOPage.getStart(),
            relationDOPage.getSize(), relationDOPage.getTotalRecord());
        return response;
    }

    @Override
    public BaseResultDTO<Void> updatePrintStatus(WaybillPickupCodeUpdateRequest request, Long courierId,
                                                 ClientInfoDTO clientInfoDTO) {
        try {
            // 参数校验
            WaybillPickupCodeValidator.validateUpdateRequest(request, courierId, clientInfoDTO);

            // 更新打印状态
            waybillPickupCodeRelationManager.updatePrintStatus(request, courierId, clientInfoDTO);

            // 构造response
            return BaseResultHelper.newResult();

        } catch (BridgeValidationException e) {
            logManager.logExceptionMessage("updatePrintStatus",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request + ", courierId:"
                    + courierId, e,
                LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("updatePrintStatus",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request + ", courierId:"
                    + courierId, e,
                LOGGER);
            return BaseResultHelper.errorResult(WaybillErrorConstant.SystemError.SYSTEM_BUSY.getErrorCode(),
                WaybillErrorConstant.SystemError.SYSTEM_BUSY.getErrorMsg());
        } catch (Throwable e) {
            logManager.logExceptionMessage("updatePrintStatus",
                "WaybillPickupCodeUpdateRequest:" + request + ", courierId:" + courierId, e,
                LOGGER);
            return BaseResultHelper.errorResult(WaybillErrorConstant.SystemError.SYSTEM_BUSY.getErrorCode(),
                WaybillErrorConstant.SystemError.SYSTEM_BUSY.getErrorMsg());
        }
    }

    @Override
    public BaseResultDTO<Void> updateWeight(UpdateWeightRequest request, Long courierId, ClientInfoDTO clientInfoDTO) {
        try {
            WaybillPickupCodeValidator.validateUpdateWeightRequest(request, courierId, clientInfoDTO);

            waybillPickupCodeRelationManager.updateWeight(request, courierId);

            // 构造response
            return BaseResultHelper.newResult();
        } catch (BridgeValidationException e) {
            logManager.logExceptionMessage("updateWeight",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request + ", courierId:"
                    + courierId, e,
                LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("updateWeight",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request + ", courierId:"
                    + courierId, e,
                LOGGER);
            return BaseResultHelper.errorResult(WaybillErrorConstant.SystemError.SYSTEM_BUSY.getErrorCode(),
                WaybillErrorConstant.SystemError.SYSTEM_BUSY.getErrorMsg());
        } catch (Throwable e) {
            logManager.logExceptionMessage("updateWeight",
                "WaybillPickupCodeUpdateRequest:" + request + ", courierId:" + courierId, e,
                LOGGER);
            return BaseResultHelper.errorResult(WaybillErrorConstant.SystemError.SYSTEM_BUSY.getErrorCode(),
                WaybillErrorConstant.SystemError.SYSTEM_BUSY.getErrorMsg());
        }
    }

    ///**
    // * 发货下发iss消息
    // *
    // * @param courierId
    // * @param cpCode
    // * @param waybillCode
    // * @throws BridgeBaseException
    // */
    //private void pushPaperlessDataTask(Long courierId, String cpCode, String waybillCode) throws BridgeBaseException {
    //
    //    if(BridgeSwitch.needSendDataCpList.contains(cpCode)) {
    //        // 调用ISS，发起下发任务; 失败需要配置棱镜报警
    //        PublishResult publishResult;
    //        try {
    //            publishResult = bridgeISSClient.pushPaperlessDataTask(courierId, cpCode, waybillCode);
    //        } catch (Throwable e)  {
    //            throw new BridgeBaseException(LabelError.PAPERLESS_DATA_PUSH_ISS_ERROR.getErrorCode(), LabelError.PAPERLESS_DATA_PUSH_ISS_ERROR.getErrorMsg());
    //        }
    //        if (publishResult== null || !publishResult.isSuccess()) {
    //            throw new BridgeBaseException(LabelError.PAPERLESS_DATA_PUSH_ISS_ERROR.getErrorCode(), LabelError.PAPERLESS_DATA_PUSH_ISS_ERROR.getErrorMsg());
    //        }
    //    }
    //}
}
