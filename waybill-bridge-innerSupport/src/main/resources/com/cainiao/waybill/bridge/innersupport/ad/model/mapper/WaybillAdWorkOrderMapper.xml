<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cainiao.waybill.bridge.innersupport.ad.model.mapper.WaybillAdWorkOrderMapper">
    <sql id="TableName">
        waybill_ad_work_order
    </sql>

    <sql id="BaseColumnList">
     `id`, `gmt_create`, `gmt_modified`, `work_order_date`, `mail_no`, `mobile`, `cp_name`, `cp_code`, `advertiser_name`, `advertiser_id`, `work_order_source`, `is_expedited`, `deal_progress`, `is_timeout`, `operator`, `scan_code_screenshot_links`, `payment_screenshot_links`, `refund_screenshot_links`, `customer_complaint_content`, `remark`, `completion_time`, `waybill_print_time`, `ad_id`, `feature`
    </sql>

    <resultMap id="BaseResultMap" type="com.cainiao.waybill.bridge.innersupport.ad.model.domain.WaybillAdWorkOrderDO" autoMapping="false">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP"/>
        <result column="gmt_modified" property="gmtModified" jdbcType="TIMESTAMP"/>
        <result column="work_order_date" property="workOrderDate" jdbcType="TIMESTAMP"/>
        <result column="mail_no" property="mailNo" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="cp_name" property="cpName" jdbcType="VARCHAR"/>
        <result column="cp_code" property="cpCode" jdbcType="VARCHAR"/>
        <result column="advertiser_name" property="advertiserName" jdbcType="VARCHAR"/>
        <result column="advertiser_id" property="advertiserId" jdbcType="VARCHAR"/>
        <result column="work_order_source" property="workOrderSource" jdbcType="INTEGER"/>
        <result column="is_expedited" property="isExpedited" jdbcType="TINYINT"/>
        <result column="deal_progress" property="dealProgress" jdbcType="INTEGER"/>
        <result column="is_timeout" property="isTimeout" jdbcType="TINYINT"/>
        <result column="operator" property="operator" jdbcType="VARCHAR"/>
        <result column="scan_code_screenshot_links" property="scanCodeScreenshotLinks" jdbcType="OTHER"/>
        <result column="payment_screenshot_links" property="paymentScreenshotLinks" jdbcType="OTHER"/>
        <result column="refund_screenshot_links" property="refundScreenshotLinks" jdbcType="OTHER"/>
        <result column="customer_complaint_content" property="customerComplaintContent" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="completion_time" property="completionTime" jdbcType="TIMESTAMP"/>
        <result column="waybill_print_time" property="waybillPrintTime" jdbcType="TIMESTAMP"/>
        <result column="ad_id" property="adId" jdbcType="BIGINT"/>
        <result column="feature" property="feature" jdbcType="VARCHAR"/>
    </resultMap>

    <insert id="insert" useGeneratedKeys="true" keyProperty="waybillAdWorkOrder.id">
        INSERT INTO
        <include refid="TableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `id`,
            `gmt_create`,
            `gmt_modified`,
            `work_order_date`,
            `mail_no`,
            `mobile`,
            `cp_name`,
            `cp_code`,
            `advertiser_name`,
            `advertiser_id`,
            `work_order_source`,
            `is_expedited`,
            `deal_progress`,
            `is_timeout`,
            `operator`,
            `scan_code_screenshot_links`,
            `payment_screenshot_links`,
            `refund_screenshot_links`,
            `customer_complaint_content`,
            `remark`,
            `completion_time`,
            `waybill_print_time`,
            `ad_id`,
            `feature`,

        </trim>
        <trim prefix="VALUES(" suffix=")" suffixOverrides=",">
            #{waybillAdWorkOrder.id,jdbcType=BIGINT},
            #{waybillAdWorkOrder.gmtCreate,jdbcType=TIMESTAMP},
            #{waybillAdWorkOrder.gmtModified,jdbcType=TIMESTAMP},
            #{waybillAdWorkOrder.workOrderDate,jdbcType=TIMESTAMP},
            #{waybillAdWorkOrder.mailNo,jdbcType=VARCHAR},
            #{waybillAdWorkOrder.mobile,jdbcType=VARCHAR},
            #{waybillAdWorkOrder.cpName,jdbcType=VARCHAR},
            #{waybillAdWorkOrder.cpCode,jdbcType=VARCHAR},
            #{waybillAdWorkOrder.advertiserName,jdbcType=VARCHAR},
            #{waybillAdWorkOrder.advertiserId,jdbcType=VARCHAR},
            #{waybillAdWorkOrder.workOrderSource,jdbcType=INTEGER},
            #{waybillAdWorkOrder.isExpedited,jdbcType=TINYINT},
            #{waybillAdWorkOrder.dealProgress,jdbcType=INTEGER},
            #{waybillAdWorkOrder.isTimeout,jdbcType=TINYINT},
            #{waybillAdWorkOrder.operator,jdbcType=VARCHAR},
            #{waybillAdWorkOrder.scanCodeScreenshotLinks,jdbcType=OTHER},
            #{waybillAdWorkOrder.paymentScreenshotLinks,jdbcType=OTHER},
            #{waybillAdWorkOrder.refundScreenshotLinks,jdbcType=OTHER},
            #{waybillAdWorkOrder.customerComplaintContent,jdbcType=VARCHAR},
            #{waybillAdWorkOrder.remark,jdbcType=VARCHAR},
            #{waybillAdWorkOrder.completionTime,jdbcType=TIMESTAMP},
            #{waybillAdWorkOrder.waybillPrintTime,jdbcType=TIMESTAMP},
            #{waybillAdWorkOrder.adId,jdbcType=BIGINT},
            #{waybillAdWorkOrder.feature,jdbcType=VARCHAR},

        </trim>
    </insert>

    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="waybillAdWorkOrder.id">
        INSERT INTO
        <include refid="TableName"/>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="waybillAdWorkOrder.id != null">
                `id`,
            </if>
            <if test="waybillAdWorkOrder.gmtCreate != null">
                `gmt_create`,
            </if>
            <if test="waybillAdWorkOrder.gmtModified != null">
                `gmt_modified`,
            </if>
            <if test="waybillAdWorkOrder.workOrderDate != null">
                `work_order_date`,
            </if>
            <if test="waybillAdWorkOrder.mailNo != null">
                `mail_no`,
            </if>
            <if test="waybillAdWorkOrder.mobile != null">
                `mobile`,
            </if>
            <if test="waybillAdWorkOrder.cpName != null">
                `cp_name`,
            </if>
            <if test="waybillAdWorkOrder.cpCode != null">
                `cp_code`,
            </if>
            <if test="waybillAdWorkOrder.advertiserName != null">
                `advertiser_name`,
            </if>
            <if test="waybillAdWorkOrder.advertiserId != null">
                `advertiser_id`,
            </if>
            <if test="waybillAdWorkOrder.workOrderSource != null">
                `work_order_source`,
            </if>
            <if test="waybillAdWorkOrder.isExpedited != null">
                `is_expedited`,
            </if>
            <if test="waybillAdWorkOrder.dealProgress != null">
                `deal_progress`,
            </if>
            <if test="waybillAdWorkOrder.isTimeout != null">
                `is_timeout`,
            </if>
            <if test="waybillAdWorkOrder.operator != null">
                `operator`,
            </if>
            <if test="waybillAdWorkOrder.scanCodeScreenshotLinks != null">
                `scan_code_screenshot_links`,
            </if>
            <if test="waybillAdWorkOrder.paymentScreenshotLinks != null">
                `payment_screenshot_links`,
            </if>
            <if test="waybillAdWorkOrder.refundScreenshotLinks != null">
                `refund_screenshot_links`,
            </if>
            <if test="waybillAdWorkOrder.customerComplaintContent != null">
                `customer_complaint_content`,
            </if>
            <if test="waybillAdWorkOrder.remark != null">
                `remark`,
            </if>
            <if test="waybillAdWorkOrder.completionTime != null">
                `completion_time`,
            </if>
            <if test="waybillAdWorkOrder.waybillPrintTime != null">
                `waybill_print_time`,
            </if>
            <if test="waybillAdWorkOrder.adId != null">
                `ad_id`,
            </if>
            <if test="waybillAdWorkOrder.feature != null">
                `feature`,
            </if>

        </trim>
        <trim prefix="VALUES(" suffix=")" suffixOverrides=",">
            <if test="waybillAdWorkOrder.id != null">
                #{waybillAdWorkOrder.id,jdbcType=BIGINT},
            </if>
            <if test="waybillAdWorkOrder.gmtCreate != null">
                #{waybillAdWorkOrder.gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="waybillAdWorkOrder.gmtModified != null">
                #{waybillAdWorkOrder.gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="waybillAdWorkOrder.workOrderDate != null">
                #{waybillAdWorkOrder.workOrderDate,jdbcType=TIMESTAMP},
            </if>
            <if test="waybillAdWorkOrder.mailNo != null">
                #{waybillAdWorkOrder.mailNo,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.mobile != null">
                #{waybillAdWorkOrder.mobile,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.cpName != null">
                #{waybillAdWorkOrder.cpName,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.cpCode != null">
                #{waybillAdWorkOrder.cpCode,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.advertiserName != null">
                #{waybillAdWorkOrder.advertiserName,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.advertiserId != null">
                #{waybillAdWorkOrder.advertiserId,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.workOrderSource != null">
                #{waybillAdWorkOrder.workOrderSource,jdbcType=INTEGER},
            </if>
            <if test="waybillAdWorkOrder.isExpedited != null">
                #{waybillAdWorkOrder.isExpedited,jdbcType=TINYINT},
            </if>
            <if test="waybillAdWorkOrder.dealProgress != null">
                #{waybillAdWorkOrder.dealProgress,jdbcType=INTEGER},
            </if>
            <if test="waybillAdWorkOrder.isTimeout != null">
                #{waybillAdWorkOrder.isTimeout,jdbcType=TINYINT},
            </if>
            <if test="waybillAdWorkOrder.operator != null">
                #{waybillAdWorkOrder.operator,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.scanCodeScreenshotLinks != null">
                #{waybillAdWorkOrder.scanCodeScreenshotLinks,jdbcType=OTHER},
            </if>
            <if test="waybillAdWorkOrder.paymentScreenshotLinks != null">
                #{waybillAdWorkOrder.paymentScreenshotLinks,jdbcType=OTHER},
            </if>
            <if test="waybillAdWorkOrder.refundScreenshotLinks != null">
                #{waybillAdWorkOrder.refundScreenshotLinks,jdbcType=OTHER},
            </if>
            <if test="waybillAdWorkOrder.customerComplaintContent != null">
                #{waybillAdWorkOrder.customerComplaintContent,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.remark != null">
                #{waybillAdWorkOrder.remark,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.completionTime != null">
                #{waybillAdWorkOrder.completionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="waybillAdWorkOrder.waybillPrintTime != null">
                #{waybillAdWorkOrder.waybillPrintTime,jdbcType=TIMESTAMP},
            </if>
            <if test="waybillAdWorkOrder.adId != null">
                #{waybillAdWorkOrder.adId,jdbcType=BIGINT},
            </if>
            <if test="waybillAdWorkOrder.feature != null">
                #{waybillAdWorkOrder.feature,jdbcType=VARCHAR},
            </if>

        </trim>
    </insert>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from
        <include refid="TableName"/>
        where
        `id` = #{id}
    </select>

    <sql id="pageSelectSql">
        <!--  时间范围，开始时间、结束时间-->
        <if test="waybillAdWorkOrder.startTime != null and waybillAdWorkOrder.startTime != '' and waybillAdWorkOrder.endTime != null and waybillAdWorkOrder.endTime != ''">
            AND work_order_date between FROM_UNIXTIME(#{waybillAdWorkOrder.startTime} / 1000) and FROM_UNIXTIME(#{waybillAdWorkOrder.endTime} / 1000)
        </if>
        <if test="waybillAdWorkOrder.dealProgress != null">
            AND deal_progress = #{waybillAdWorkOrder.dealProgress}
        </if>
        <if test="waybillAdWorkOrder.cpCode != null">
            AND cp_code = #{waybillAdWorkOrder.cpCode}
        </if>
        <if test="waybillAdWorkOrder.advertiserId != null">
            AND advertiser_id = #{waybillAdWorkOrder.advertiserId}
        </if>
        <if test="waybillAdWorkOrder.workOrderSource != null">
            AND work_order_source = #{waybillAdWorkOrder.workOrderSource}
        </if>
        <if test="waybillAdWorkOrder.mailNo != null">
            AND mail_no = #{waybillAdWorkOrder.mailNo}
        </if>
        <if test="waybillAdWorkOrder.mobile != null">
            AND mobile = #{waybillAdWorkOrder.mobile}
        </if>
    </sql>

    <select id="countByParam" parameterType="com.cainiao.waybill.bridge.innersupport.ad.model.domain.WaybillAdWorkOrderParam" resultType="java.lang.Long">
        select count(*) from
        <include refid="TableName"/>
        where 1=1
        <include refid="pageSelectSql" />
    </select>

    <select id="selectByParam" parameterType="com.cainiao.waybill.bridge.innersupport.ad.model.domain.WaybillAdWorkOrderParam" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from
        <include refid="TableName"/>
        where 1=1
        <include refid="pageSelectSql" />
        ORDER BY
            CASE
                WHEN `deal_progress` = 1 THEN 0
                ELSE 1
            END,
            is_expedited DESC,
            gmt_create DESC
        limit #{waybillAdWorkOrder.currentPage},#{waybillAdWorkOrder.pageSize}
    </select>


    <delete id="deleteByPrimaryKey">
        DELETE from
        <include refid="TableName"/>
        where
        `id` = #{id}
    </delete>

    <update id="updateByPrimaryKey">
        update
        <include refid="TableName"/>
        set
        <trim suffixOverrides=",">
            `gmt_create` = #{waybillAdWorkOrder.gmtCreate,jdbcType=TIMESTAMP},
            `gmt_modified` = #{waybillAdWorkOrder.gmtModified,jdbcType=TIMESTAMP},
            `work_order_date` = #{waybillAdWorkOrder.workOrderDate,jdbcType=TIMESTAMP},
            `mail_no` = #{waybillAdWorkOrder.mailNo,jdbcType=VARCHAR},
            `mobile` = #{waybillAdWorkOrder.mobile,jdbcType=VARCHAR},
            `cp_name` = #{waybillAdWorkOrder.cpName,jdbcType=VARCHAR},
            `cp_code` = #{waybillAdWorkOrder.cpCode,jdbcType=VARCHAR},
            `advertiser_name` = #{waybillAdWorkOrder.advertiserName,jdbcType=VARCHAR},
            `advertiser_id` = #{waybillAdWorkOrder.advertiserId,jdbcType=VARCHAR},
            `work_order_source` = #{waybillAdWorkOrder.workOrderSource,jdbcType=INTEGER},
            `is_expedited` = #{waybillAdWorkOrder.isExpedited,jdbcType=TINYINT},
            `deal_progress` = #{waybillAdWorkOrder.dealProgress,jdbcType=INTEGER},
            `is_timeout` = #{waybillAdWorkOrder.isTimeout,jdbcType=TINYINT},
            `operator` = #{waybillAdWorkOrder.operator,jdbcType=VARCHAR},
            `scan_code_screenshot_links` = #{waybillAdWorkOrder.scanCodeScreenshotLinks,jdbcType=OTHER},
            `payment_screenshot_links` = #{waybillAdWorkOrder.paymentScreenshotLinks,jdbcType=OTHER},
            `refund_screenshot_links` = #{waybillAdWorkOrder.refundScreenshotLinks,jdbcType=OTHER},
            `customer_complaint_content` = #{waybillAdWorkOrder.customerComplaintContent,jdbcType=VARCHAR},
            `remark` = #{waybillAdWorkOrder.remark,jdbcType=VARCHAR},
            `completion_time` = #{waybillAdWorkOrder.completionTime,jdbcType=TIMESTAMP},
            `waybill_print_time` = #{waybillAdWorkOrder.waybillPrintTime,jdbcType=TIMESTAMP},
            `ad_id` = #{waybillAdWorkOrder.adId,jdbcType=BIGINT},
            `feature` = #{waybillAdWorkOrder.feature,jdbcType=VARCHAR},
        </trim>
        where
        `id` = #{waybillAdWorkOrder.id}
    </update>

    <update id="updateByPrimaryKeySelective">
        update
        <include refid="TableName"/>
        set
        <trim suffixOverrides=",">
            <if test="waybillAdWorkOrder.gmtCreate != null">
                `gmt_create` = #{waybillAdWorkOrder.gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="waybillAdWorkOrder.gmtModified != null">
                `gmt_modified` = #{waybillAdWorkOrder.gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="waybillAdWorkOrder.workOrderDate != null">
                `work_order_date` = #{waybillAdWorkOrder.workOrderDate,jdbcType=TIMESTAMP},
            </if>
            <if test="waybillAdWorkOrder.mailNo != null">
                `mail_no` = #{waybillAdWorkOrder.mailNo,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.mobile != null">
                `mobile` = #{waybillAdWorkOrder.mobile,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.cpName != null">
                `cp_name` = #{waybillAdWorkOrder.cpName,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.cpCode != null">
                `cp_code` = #{waybillAdWorkOrder.cpCode,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.advertiserName != null">
                `advertiser_name` = #{waybillAdWorkOrder.advertiserName,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.advertiserId != null">
                `advertiser_id` = #{waybillAdWorkOrder.advertiserId,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.workOrderSource != null">
                `work_order_source` = #{waybillAdWorkOrder.workOrderSource,jdbcType=INTEGER},
            </if>
            <if test="waybillAdWorkOrder.isExpedited != null">
                `is_expedited` = #{waybillAdWorkOrder.isExpedited,jdbcType=TINYINT},
            </if>
            <if test="waybillAdWorkOrder.dealProgress != null">
                `deal_progress` = #{waybillAdWorkOrder.dealProgress,jdbcType=INTEGER},
            </if>
            <if test="waybillAdWorkOrder.isTimeout != null">
                `is_timeout` = #{waybillAdWorkOrder.isTimeout,jdbcType=TINYINT},
            </if>
            <if test="waybillAdWorkOrder.operator != null">
                `operator` = #{waybillAdWorkOrder.operator,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.scanCodeScreenshotLinks != null">
                `scan_code_screenshot_links` = #{waybillAdWorkOrder.scanCodeScreenshotLinks,jdbcType=OTHER},
            </if>
            <if test="waybillAdWorkOrder.paymentScreenshotLinks != null">
                `payment_screenshot_links` = #{waybillAdWorkOrder.paymentScreenshotLinks,jdbcType=OTHER},
            </if>
            <if test="waybillAdWorkOrder.refundScreenshotLinks != null">
                `refund_screenshot_links` = #{waybillAdWorkOrder.refundScreenshotLinks,jdbcType=OTHER},
            </if>
            <if test="waybillAdWorkOrder.customerComplaintContent != null">
                `customer_complaint_content` = #{waybillAdWorkOrder.customerComplaintContent,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.remark != null">
                `remark` = #{waybillAdWorkOrder.remark,jdbcType=VARCHAR},
            </if>
            <if test="waybillAdWorkOrder.completionTime != null">
                `completion_time` = #{waybillAdWorkOrder.completionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="waybillAdWorkOrder.waybillPrintTime != null">
                `waybill_print_time` = #{waybillAdWorkOrder.waybillPrintTime,jdbcType=TIMESTAMP},
            </if>
            <if test="waybillAdWorkOrder.adId != null">
                `ad_id` = #{waybillAdWorkOrder.adId,jdbcType=BIGINT},
            </if>
            <if test="waybillAdWorkOrder.feature != null">
                `feature` = #{waybillAdWorkOrder.feature,jdbcType=VARCHAR},
            </if>
        </trim>
        where
        `id` = #{waybillAdWorkOrder.id}
    </update>
</mapper>