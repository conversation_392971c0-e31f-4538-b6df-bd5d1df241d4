package com.cainiao.waybill.bridge.enterprise.common.enums;


import java.util.Objects;

public enum EnterpriseOrderItemEnum {
    /**
     * 文件
     */
    FILE(1, "文件"),
    /**
     * 合同
     */
    CONTRACT(2, "合同"),
    /**
     * 服饰
     */
    CLOTHING(3, "服饰"),

    /**
     * 化妆品
     */
    COSMETICS(4, "化妆品"),

    /**
     * 其他
     */
    OTHER(5, "其他"),
    ;

    final Integer code;
    final String describe;

    EnterpriseOrderItemEnum(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }
    public Integer getCode() {
        return code;
    }
    public String getDescribe() {
        return describe;
    }


    public static String getDesByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (EnterpriseOrderItemEnum value : EnterpriseOrderItemEnum.values()) {
            if(Objects.equals(value.getCode(), code)){
                return value.describe;
            }
        }
        return null;
    }
}
