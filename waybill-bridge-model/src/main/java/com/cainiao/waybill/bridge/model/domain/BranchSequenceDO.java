package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * Description:处理网点“揽件码”订单并发情况，sequence 服务表
 * <AUTHOR>
 * Date 2017-04-26
 */
public class BranchSequenceDO {

    /**
     * 主键
     */
    private long id;

    /**
     * 网点编码
     */
    private String branchCode;

    /**
     * cp编码
     */
    private String cpCode;

    /**
     * 网点“揽件码”面单自增序列
     */
    private Long sequence;

    /**
     * 修改时间
     */
    private Date gmtModified;

    public Date getGmtModified() {
        return gmtModified;
    }

    public BranchSequenceDO setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
        return this;
    }

    /**
     * setter for column 主键
     */
    public void setId(long id) {
        this.id = id;
    }

    /**
     * getter for column 主键
     */
    public long getId() {
        return this.id;
    }

    /**
     * setter for column 网点编码
     */
    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    /**
     * getter for column 网点编码
     */
    public String getBranchCode() {
        return this.branchCode;
    }

    /**
     * setter for column cp编码
     */
    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    /**
     * getter for column cp编码
     */
    public String getCpCode() {
        return this.cpCode;
    }

    public Long getSequence() {
        return sequence;
    }

    public void setSequence(Long sequence) {
        this.sequence = sequence;
    }
}