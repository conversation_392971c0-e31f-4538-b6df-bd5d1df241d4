package com.cainiao.waybill.bridge.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class QuoteDetailInfoParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public QuoteDetailInfoParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public QuoteDetailInfoParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public QuoteDetailInfoParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public QuoteDetailInfoParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public QuoteDetailInfoParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public QuoteDetailInfoParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andQuoteIdIsNull() {
            addCriterion("quote_id is null");
            return (Criteria) this;
        }

        public Criteria andQuoteIdIsNotNull() {
            addCriterion("quote_id is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteIdEqualTo(Long value) {
            addCriterion("quote_id =", value, "quoteId");
            return (Criteria) this;
        }

        public Criteria andQuoteIdNotEqualTo(Long value) {
            addCriterion("quote_id <>", value, "quoteId");
            return (Criteria) this;
        }

        public Criteria andQuoteIdGreaterThan(Long value) {
            addCriterion("quote_id >", value, "quoteId");
            return (Criteria) this;
        }

        public Criteria andQuoteIdGreaterThanOrEqualTo(Long value) {
            addCriterion("quote_id >=", value, "quoteId");
            return (Criteria) this;
        }

        public Criteria andQuoteIdLessThan(Long value) {
            addCriterion("quote_id <", value, "quoteId");
            return (Criteria) this;
        }

        public Criteria andQuoteIdLessThanOrEqualTo(Long value) {
            addCriterion("quote_id <=", value, "quoteId");
            return (Criteria) this;
        }

        public Criteria andQuoteIdIn(List<Long> values) {
            addCriterion("quote_id in", values, "quoteId");
            return (Criteria) this;
        }

        public Criteria andQuoteIdNotIn(List<Long> values) {
            addCriterion("quote_id not in", values, "quoteId");
            return (Criteria) this;
        }

        public Criteria andQuoteIdBetween(Long value1, Long value2) {
            addCriterion("quote_id between", value1, value2, "quoteId");
            return (Criteria) this;
        }

        public Criteria andQuoteIdNotBetween(Long value1, Long value2) {
            addCriterion("quote_id not between", value1, value2, "quoteId");
            return (Criteria) this;
        }

        public Criteria andSendProvIsNull() {
            addCriterion("send_prov is null");
            return (Criteria) this;
        }

        public Criteria andSendProvIsNotNull() {
            addCriterion("send_prov is not null");
            return (Criteria) this;
        }

        public Criteria andSendProvEqualTo(String value) {
            addCriterion("send_prov =", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvNotEqualTo(String value) {
            addCriterion("send_prov <>", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvGreaterThan(String value) {
            addCriterion("send_prov >", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvGreaterThanOrEqualTo(String value) {
            addCriterion("send_prov >=", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvLessThan(String value) {
            addCriterion("send_prov <", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvLessThanOrEqualTo(String value) {
            addCriterion("send_prov <=", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvLike(String value) {
            addCriterion("send_prov like", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvNotLike(String value) {
            addCriterion("send_prov not like", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvIn(List<String> values) {
            addCriterion("send_prov in", values, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvNotIn(List<String> values) {
            addCriterion("send_prov not in", values, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvBetween(String value1, String value2) {
            addCriterion("send_prov between", value1, value2, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvNotBetween(String value1, String value2) {
            addCriterion("send_prov not between", value1, value2, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendCityIsNull() {
            addCriterion("send_city is null");
            return (Criteria) this;
        }

        public Criteria andSendCityIsNotNull() {
            addCriterion("send_city is not null");
            return (Criteria) this;
        }

        public Criteria andSendCityEqualTo(String value) {
            addCriterion("send_city =", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityNotEqualTo(String value) {
            addCriterion("send_city <>", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityGreaterThan(String value) {
            addCriterion("send_city >", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityGreaterThanOrEqualTo(String value) {
            addCriterion("send_city >=", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityLessThan(String value) {
            addCriterion("send_city <", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityLessThanOrEqualTo(String value) {
            addCriterion("send_city <=", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityLike(String value) {
            addCriterion("send_city like", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityNotLike(String value) {
            addCriterion("send_city not like", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityIn(List<String> values) {
            addCriterion("send_city in", values, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityNotIn(List<String> values) {
            addCriterion("send_city not in", values, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityBetween(String value1, String value2) {
            addCriterion("send_city between", value1, value2, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityNotBetween(String value1, String value2) {
            addCriterion("send_city not between", value1, value2, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendAreaIsNull() {
            addCriterion("send_area is null");
            return (Criteria) this;
        }

        public Criteria andSendAreaIsNotNull() {
            addCriterion("send_area is not null");
            return (Criteria) this;
        }

        public Criteria andSendAreaEqualTo(String value) {
            addCriterion("send_area =", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaNotEqualTo(String value) {
            addCriterion("send_area <>", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaGreaterThan(String value) {
            addCriterion("send_area >", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaGreaterThanOrEqualTo(String value) {
            addCriterion("send_area >=", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaLessThan(String value) {
            addCriterion("send_area <", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaLessThanOrEqualTo(String value) {
            addCriterion("send_area <=", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaLike(String value) {
            addCriterion("send_area like", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaNotLike(String value) {
            addCriterion("send_area not like", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaIn(List<String> values) {
            addCriterion("send_area in", values, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaNotIn(List<String> values) {
            addCriterion("send_area not in", values, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaBetween(String value1, String value2) {
            addCriterion("send_area between", value1, value2, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaNotBetween(String value1, String value2) {
            addCriterion("send_area not between", value1, value2, "sendArea");
            return (Criteria) this;
        }

        public Criteria andReceProvIsNull() {
            addCriterion("rece_prov is null");
            return (Criteria) this;
        }

        public Criteria andReceProvIsNotNull() {
            addCriterion("rece_prov is not null");
            return (Criteria) this;
        }

        public Criteria andReceProvEqualTo(String value) {
            addCriterion("rece_prov =", value, "receProv");
            return (Criteria) this;
        }

        public Criteria andReceProvNotEqualTo(String value) {
            addCriterion("rece_prov <>", value, "receProv");
            return (Criteria) this;
        }

        public Criteria andReceProvGreaterThan(String value) {
            addCriterion("rece_prov >", value, "receProv");
            return (Criteria) this;
        }

        public Criteria andReceProvGreaterThanOrEqualTo(String value) {
            addCriterion("rece_prov >=", value, "receProv");
            return (Criteria) this;
        }

        public Criteria andReceProvLessThan(String value) {
            addCriterion("rece_prov <", value, "receProv");
            return (Criteria) this;
        }

        public Criteria andReceProvLessThanOrEqualTo(String value) {
            addCriterion("rece_prov <=", value, "receProv");
            return (Criteria) this;
        }

        public Criteria andReceProvLike(String value) {
            addCriterion("rece_prov like", value, "receProv");
            return (Criteria) this;
        }

        public Criteria andReceProvNotLike(String value) {
            addCriterion("rece_prov not like", value, "receProv");
            return (Criteria) this;
        }

        public Criteria andReceProvIn(List<String> values) {
            addCriterion("rece_prov in", values, "receProv");
            return (Criteria) this;
        }

        public Criteria andReceProvNotIn(List<String> values) {
            addCriterion("rece_prov not in", values, "receProv");
            return (Criteria) this;
        }

        public Criteria andReceProvBetween(String value1, String value2) {
            addCriterion("rece_prov between", value1, value2, "receProv");
            return (Criteria) this;
        }

        public Criteria andReceProvNotBetween(String value1, String value2) {
            addCriterion("rece_prov not between", value1, value2, "receProv");
            return (Criteria) this;
        }

        public Criteria andReceCityIsNull() {
            addCriterion("rece_city is null");
            return (Criteria) this;
        }

        public Criteria andReceCityIsNotNull() {
            addCriterion("rece_city is not null");
            return (Criteria) this;
        }

        public Criteria andReceCityEqualTo(String value) {
            addCriterion("rece_city =", value, "receCity");
            return (Criteria) this;
        }

        public Criteria andReceCityNotEqualTo(String value) {
            addCriterion("rece_city <>", value, "receCity");
            return (Criteria) this;
        }

        public Criteria andReceCityGreaterThan(String value) {
            addCriterion("rece_city >", value, "receCity");
            return (Criteria) this;
        }

        public Criteria andReceCityGreaterThanOrEqualTo(String value) {
            addCriterion("rece_city >=", value, "receCity");
            return (Criteria) this;
        }

        public Criteria andReceCityLessThan(String value) {
            addCriterion("rece_city <", value, "receCity");
            return (Criteria) this;
        }

        public Criteria andReceCityLessThanOrEqualTo(String value) {
            addCriterion("rece_city <=", value, "receCity");
            return (Criteria) this;
        }

        public Criteria andReceCityLike(String value) {
            addCriterion("rece_city like", value, "receCity");
            return (Criteria) this;
        }

        public Criteria andReceCityNotLike(String value) {
            addCriterion("rece_city not like", value, "receCity");
            return (Criteria) this;
        }

        public Criteria andReceCityIn(List<String> values) {
            addCriterion("rece_city in", values, "receCity");
            return (Criteria) this;
        }

        public Criteria andReceCityNotIn(List<String> values) {
            addCriterion("rece_city not in", values, "receCity");
            return (Criteria) this;
        }

        public Criteria andReceCityBetween(String value1, String value2) {
            addCriterion("rece_city between", value1, value2, "receCity");
            return (Criteria) this;
        }

        public Criteria andReceCityNotBetween(String value1, String value2) {
            addCriterion("rece_city not between", value1, value2, "receCity");
            return (Criteria) this;
        }

        public Criteria andReceAreaIsNull() {
            addCriterion("rece_area is null");
            return (Criteria) this;
        }

        public Criteria andReceAreaIsNotNull() {
            addCriterion("rece_area is not null");
            return (Criteria) this;
        }

        public Criteria andReceAreaEqualTo(String value) {
            addCriterion("rece_area =", value, "receArea");
            return (Criteria) this;
        }

        public Criteria andReceAreaNotEqualTo(String value) {
            addCriterion("rece_area <>", value, "receArea");
            return (Criteria) this;
        }

        public Criteria andReceAreaGreaterThan(String value) {
            addCriterion("rece_area >", value, "receArea");
            return (Criteria) this;
        }

        public Criteria andReceAreaGreaterThanOrEqualTo(String value) {
            addCriterion("rece_area >=", value, "receArea");
            return (Criteria) this;
        }

        public Criteria andReceAreaLessThan(String value) {
            addCriterion("rece_area <", value, "receArea");
            return (Criteria) this;
        }

        public Criteria andReceAreaLessThanOrEqualTo(String value) {
            addCriterion("rece_area <=", value, "receArea");
            return (Criteria) this;
        }

        public Criteria andReceAreaLike(String value) {
            addCriterion("rece_area like", value, "receArea");
            return (Criteria) this;
        }

        public Criteria andReceAreaNotLike(String value) {
            addCriterion("rece_area not like", value, "receArea");
            return (Criteria) this;
        }

        public Criteria andReceAreaIn(List<String> values) {
            addCriterion("rece_area in", values, "receArea");
            return (Criteria) this;
        }

        public Criteria andReceAreaNotIn(List<String> values) {
            addCriterion("rece_area not in", values, "receArea");
            return (Criteria) this;
        }

        public Criteria andReceAreaBetween(String value1, String value2) {
            addCriterion("rece_area between", value1, value2, "receArea");
            return (Criteria) this;
        }

        public Criteria andReceAreaNotBetween(String value1, String value2) {
            addCriterion("rece_area not between", value1, value2, "receArea");
            return (Criteria) this;
        }

        public Criteria andFirstWeightIsNull() {
            addCriterion("first_weight is null");
            return (Criteria) this;
        }

        public Criteria andFirstWeightIsNotNull() {
            addCriterion("first_weight is not null");
            return (Criteria) this;
        }

        public Criteria andFirstWeightEqualTo(Integer value) {
            addCriterion("first_weight =", value, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightNotEqualTo(Integer value) {
            addCriterion("first_weight <>", value, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightGreaterThan(Integer value) {
            addCriterion("first_weight >", value, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("first_weight >=", value, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightLessThan(Integer value) {
            addCriterion("first_weight <", value, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightLessThanOrEqualTo(Integer value) {
            addCriterion("first_weight <=", value, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightIn(List<Integer> values) {
            addCriterion("first_weight in", values, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightNotIn(List<Integer> values) {
            addCriterion("first_weight not in", values, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightBetween(Integer value1, Integer value2) {
            addCriterion("first_weight between", value1, value2, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightNotBetween(Integer value1, Integer value2) {
            addCriterion("first_weight not between", value1, value2, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightIsNull() {
            addCriterion("step_weight is null");
            return (Criteria) this;
        }

        public Criteria andStepWeightIsNotNull() {
            addCriterion("step_weight is not null");
            return (Criteria) this;
        }

        public Criteria andStepWeightEqualTo(Integer value) {
            addCriterion("step_weight =", value, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightNotEqualTo(Integer value) {
            addCriterion("step_weight <>", value, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightGreaterThan(Integer value) {
            addCriterion("step_weight >", value, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("step_weight >=", value, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightLessThan(Integer value) {
            addCriterion("step_weight <", value, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightLessThanOrEqualTo(Integer value) {
            addCriterion("step_weight <=", value, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightIn(List<Integer> values) {
            addCriterion("step_weight in", values, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightNotIn(List<Integer> values) {
            addCriterion("step_weight not in", values, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightBetween(Integer value1, Integer value2) {
            addCriterion("step_weight between", value1, value2, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightNotBetween(Integer value1, Integer value2) {
            addCriterion("step_weight not between", value1, value2, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceIsNull() {
            addCriterion("first_weight_price is null");
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceIsNotNull() {
            addCriterion("first_weight_price is not null");
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceEqualTo(Integer value) {
            addCriterion("first_weight_price =", value, "firstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceNotEqualTo(Integer value) {
            addCriterion("first_weight_price <>", value, "firstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceGreaterThan(Integer value) {
            addCriterion("first_weight_price >", value, "firstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("first_weight_price >=", value, "firstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceLessThan(Integer value) {
            addCriterion("first_weight_price <", value, "firstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceLessThanOrEqualTo(Integer value) {
            addCriterion("first_weight_price <=", value, "firstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceIn(List<Integer> values) {
            addCriterion("first_weight_price in", values, "firstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceNotIn(List<Integer> values) {
            addCriterion("first_weight_price not in", values, "firstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceBetween(Integer value1, Integer value2) {
            addCriterion("first_weight_price between", value1, value2, "firstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceNotBetween(Integer value1, Integer value2) {
            addCriterion("first_weight_price not between", value1, value2, "firstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceIsNull() {
            addCriterion("step_weight_price is null");
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceIsNotNull() {
            addCriterion("step_weight_price is not null");
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceEqualTo(Integer value) {
            addCriterion("step_weight_price =", value, "stepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceNotEqualTo(Integer value) {
            addCriterion("step_weight_price <>", value, "stepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceGreaterThan(Integer value) {
            addCriterion("step_weight_price >", value, "stepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("step_weight_price >=", value, "stepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceLessThan(Integer value) {
            addCriterion("step_weight_price <", value, "stepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceLessThanOrEqualTo(Integer value) {
            addCriterion("step_weight_price <=", value, "stepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceIn(List<Integer> values) {
            addCriterion("step_weight_price in", values, "stepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceNotIn(List<Integer> values) {
            addCriterion("step_weight_price not in", values, "stepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceBetween(Integer value1, Integer value2) {
            addCriterion("step_weight_price between", value1, value2, "stepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceNotBetween(Integer value1, Integer value2) {
            addCriterion("step_weight_price not between", value1, value2, "stepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceIsNull() {
            addCriterion("user_first_weight_price is null");
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceIsNotNull() {
            addCriterion("user_first_weight_price is not null");
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceEqualTo(Integer value) {
            addCriterion("user_first_weight_price =", value, "userFirstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceNotEqualTo(Integer value) {
            addCriterion("user_first_weight_price <>", value, "userFirstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceGreaterThan(Integer value) {
            addCriterion("user_first_weight_price >", value, "userFirstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_first_weight_price >=", value, "userFirstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceLessThan(Integer value) {
            addCriterion("user_first_weight_price <", value, "userFirstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceLessThanOrEqualTo(Integer value) {
            addCriterion("user_first_weight_price <=", value, "userFirstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceIn(List<Integer> values) {
            addCriterion("user_first_weight_price in", values, "userFirstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceNotIn(List<Integer> values) {
            addCriterion("user_first_weight_price not in", values, "userFirstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceBetween(Integer value1, Integer value2) {
            addCriterion("user_first_weight_price between", value1, value2, "userFirstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceNotBetween(Integer value1, Integer value2) {
            addCriterion("user_first_weight_price not between", value1, value2, "userFirstWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceIsNull() {
            addCriterion("user_step_weight_price is null");
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceIsNotNull() {
            addCriterion("user_step_weight_price is not null");
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceEqualTo(Integer value) {
            addCriterion("user_step_weight_price =", value, "userStepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceNotEqualTo(Integer value) {
            addCriterion("user_step_weight_price <>", value, "userStepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceGreaterThan(Integer value) {
            addCriterion("user_step_weight_price >", value, "userStepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_step_weight_price >=", value, "userStepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceLessThan(Integer value) {
            addCriterion("user_step_weight_price <", value, "userStepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceLessThanOrEqualTo(Integer value) {
            addCriterion("user_step_weight_price <=", value, "userStepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceIn(List<Integer> values) {
            addCriterion("user_step_weight_price in", values, "userStepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceNotIn(List<Integer> values) {
            addCriterion("user_step_weight_price not in", values, "userStepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceBetween(Integer value1, Integer value2) {
            addCriterion("user_step_weight_price between", value1, value2, "userStepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceNotBetween(Integer value1, Integer value2) {
            addCriterion("user_step_weight_price not between", value1, value2, "userStepWeightPrice");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceIsNull() {
            addCriterion("use_step_price is null");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceIsNotNull() {
            addCriterion("use_step_price is not null");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceEqualTo(String value) {
            addCriterion("use_step_price =", value, "useStepPrice");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceNotEqualTo(String value) {
            addCriterion("use_step_price <>", value, "useStepPrice");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceGreaterThan(String value) {
            addCriterion("use_step_price >", value, "useStepPrice");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceGreaterThanOrEqualTo(String value) {
            addCriterion("use_step_price >=", value, "useStepPrice");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceLessThan(String value) {
            addCriterion("use_step_price <", value, "useStepPrice");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceLessThanOrEqualTo(String value) {
            addCriterion("use_step_price <=", value, "useStepPrice");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceLike(String value) {
            addCriterion("use_step_price like", value, "useStepPrice");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceNotLike(String value) {
            addCriterion("use_step_price not like", value, "useStepPrice");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceIn(List<String> values) {
            addCriterion("use_step_price in", values, "useStepPrice");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceNotIn(List<String> values) {
            addCriterion("use_step_price not in", values, "useStepPrice");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceBetween(String value1, String value2) {
            addCriterion("use_step_price between", value1, value2, "useStepPrice");
            return (Criteria) this;
        }

        public Criteria andUseStepPriceNotBetween(String value1, String value2) {
            addCriterion("use_step_price not between", value1, value2, "useStepPrice");
            return (Criteria) this;
        }

        public Criteria andStepFeatureIsNull() {
            addCriterion("step_feature is null");
            return (Criteria) this;
        }

        public Criteria andStepFeatureIsNotNull() {
            addCriterion("step_feature is not null");
            return (Criteria) this;
        }

        public Criteria andStepFeatureEqualTo(String value) {
            addCriterion("step_feature =", value, "stepFeature");
            return (Criteria) this;
        }

        public Criteria andStepFeatureNotEqualTo(String value) {
            addCriterion("step_feature <>", value, "stepFeature");
            return (Criteria) this;
        }

        public Criteria andStepFeatureGreaterThan(String value) {
            addCriterion("step_feature >", value, "stepFeature");
            return (Criteria) this;
        }

        public Criteria andStepFeatureGreaterThanOrEqualTo(String value) {
            addCriterion("step_feature >=", value, "stepFeature");
            return (Criteria) this;
        }

        public Criteria andStepFeatureLessThan(String value) {
            addCriterion("step_feature <", value, "stepFeature");
            return (Criteria) this;
        }

        public Criteria andStepFeatureLessThanOrEqualTo(String value) {
            addCriterion("step_feature <=", value, "stepFeature");
            return (Criteria) this;
        }

        public Criteria andStepFeatureLike(String value) {
            addCriterion("step_feature like", value, "stepFeature");
            return (Criteria) this;
        }

        public Criteria andStepFeatureNotLike(String value) {
            addCriterion("step_feature not like", value, "stepFeature");
            return (Criteria) this;
        }

        public Criteria andStepFeatureIn(List<String> values) {
            addCriterion("step_feature in", values, "stepFeature");
            return (Criteria) this;
        }

        public Criteria andStepFeatureNotIn(List<String> values) {
            addCriterion("step_feature not in", values, "stepFeature");
            return (Criteria) this;
        }

        public Criteria andStepFeatureBetween(String value1, String value2) {
            addCriterion("step_feature between", value1, value2, "stepFeature");
            return (Criteria) this;
        }

        public Criteria andStepFeatureNotBetween(String value1, String value2) {
            addCriterion("step_feature not between", value1, value2, "stepFeature");
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioIsNull() {
            addCriterion("volume_weight_ratio is null");
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioIsNotNull() {
            addCriterion("volume_weight_ratio is not null");
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioEqualTo(Integer value) {
            addCriterion("volume_weight_ratio =", value, "volumeWeightRatio");
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioNotEqualTo(Integer value) {
            addCriterion("volume_weight_ratio <>", value, "volumeWeightRatio");
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioGreaterThan(Integer value) {
            addCriterion("volume_weight_ratio >", value, "volumeWeightRatio");
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioGreaterThanOrEqualTo(Integer value) {
            addCriterion("volume_weight_ratio >=", value, "volumeWeightRatio");
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioLessThan(Integer value) {
            addCriterion("volume_weight_ratio <", value, "volumeWeightRatio");
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioLessThanOrEqualTo(Integer value) {
            addCriterion("volume_weight_ratio <=", value, "volumeWeightRatio");
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioIn(List<Integer> values) {
            addCriterion("volume_weight_ratio in", values, "volumeWeightRatio");
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioNotIn(List<Integer> values) {
            addCriterion("volume_weight_ratio not in", values, "volumeWeightRatio");
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioBetween(Integer value1, Integer value2) {
            addCriterion("volume_weight_ratio between", value1, value2, "volumeWeightRatio");
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioNotBetween(Integer value1, Integer value2) {
            addCriterion("volume_weight_ratio not between", value1, value2, "volumeWeightRatio");
            return (Criteria) this;
        }

        public Criteria andRouteRewardIsNull() {
            addCriterion("route_reward is null");
            return (Criteria) this;
        }

        public Criteria andRouteRewardIsNotNull() {
            addCriterion("route_reward is not null");
            return (Criteria) this;
        }

        public Criteria andRouteRewardEqualTo(Integer value) {
            addCriterion("route_reward =", value, "routeReward");
            return (Criteria) this;
        }

        public Criteria andRouteRewardNotEqualTo(Integer value) {
            addCriterion("route_reward <>", value, "routeReward");
            return (Criteria) this;
        }

        public Criteria andRouteRewardGreaterThan(Integer value) {
            addCriterion("route_reward >", value, "routeReward");
            return (Criteria) this;
        }

        public Criteria andRouteRewardGreaterThanOrEqualTo(Integer value) {
            addCriterion("route_reward >=", value, "routeReward");
            return (Criteria) this;
        }

        public Criteria andRouteRewardLessThan(Integer value) {
            addCriterion("route_reward <", value, "routeReward");
            return (Criteria) this;
        }

        public Criteria andRouteRewardLessThanOrEqualTo(Integer value) {
            addCriterion("route_reward <=", value, "routeReward");
            return (Criteria) this;
        }

        public Criteria andRouteRewardIn(List<Integer> values) {
            addCriterion("route_reward in", values, "routeReward");
            return (Criteria) this;
        }

        public Criteria andRouteRewardNotIn(List<Integer> values) {
            addCriterion("route_reward not in", values, "routeReward");
            return (Criteria) this;
        }

        public Criteria andRouteRewardBetween(Integer value1, Integer value2) {
            addCriterion("route_reward between", value1, value2, "routeReward");
            return (Criteria) this;
        }

        public Criteria andRouteRewardNotBetween(Integer value1, Integer value2) {
            addCriterion("route_reward not between", value1, value2, "routeReward");
            return (Criteria) this;
        }

        public Criteria andFeatueIsNull() {
            addCriterion("featue is null");
            return (Criteria) this;
        }

        public Criteria andFeatueIsNotNull() {
            addCriterion("featue is not null");
            return (Criteria) this;
        }

        public Criteria andFeatueEqualTo(String value) {
            addCriterion("featue =", value, "featue");
            return (Criteria) this;
        }

        public Criteria andFeatueNotEqualTo(String value) {
            addCriterion("featue <>", value, "featue");
            return (Criteria) this;
        }

        public Criteria andFeatueGreaterThan(String value) {
            addCriterion("featue >", value, "featue");
            return (Criteria) this;
        }

        public Criteria andFeatueGreaterThanOrEqualTo(String value) {
            addCriterion("featue >=", value, "featue");
            return (Criteria) this;
        }

        public Criteria andFeatueLessThan(String value) {
            addCriterion("featue <", value, "featue");
            return (Criteria) this;
        }

        public Criteria andFeatueLessThanOrEqualTo(String value) {
            addCriterion("featue <=", value, "featue");
            return (Criteria) this;
        }

        public Criteria andFeatueLike(String value) {
            addCriterion("featue like", value, "featue");
            return (Criteria) this;
        }

        public Criteria andFeatueNotLike(String value) {
            addCriterion("featue not like", value, "featue");
            return (Criteria) this;
        }

        public Criteria andFeatueIn(List<String> values) {
            addCriterion("featue in", values, "featue");
            return (Criteria) this;
        }

        public Criteria andFeatueNotIn(List<String> values) {
            addCriterion("featue not in", values, "featue");
            return (Criteria) this;
        }

        public Criteria andFeatueBetween(String value1, String value2) {
            addCriterion("featue between", value1, value2, "featue");
            return (Criteria) this;
        }

        public Criteria andFeatueNotBetween(String value1, String value2) {
            addCriterion("featue not between", value1, value2, "featue");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("quote_id =", value, "quoteId");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("quote_id <>", value, "quoteId");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("quote_id >", value, "quoteId");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("quote_id >=", value, "quoteId");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("quote_id <", value, "quoteId");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("quote_id <=", value, "quoteId");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("quote_id in", values, "quoteId");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("quote_id not in", values, "quoteId");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("quote_id between", value1, value2, "quoteId");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("quote_id not between", value1, value2, "quoteId");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov =", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov <>", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov >", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov >=", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov <", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov <=", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov like", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov not like", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("send_prov in", values, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("send_prov not in", values, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("send_prov between", value1, value2, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("send_prov not between", value1, value2, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city =", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city <>", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city >", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city >=", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city <", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city <=", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city like", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city not like", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("send_city in", values, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("send_city not in", values, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("send_city between", value1, value2, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("send_city not between", value1, value2, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area =", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area <>", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area >", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area >=", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area <", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area <=", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area like", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area not like", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("send_area in", values, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("send_area not in", values, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("send_area between", value1, value2, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("send_area not between", value1, value2, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceProvEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_prov =", value, "receProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceProvNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_prov <>", value, "receProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceProvGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_prov >", value, "receProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceProvGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_prov >=", value, "receProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceProvLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_prov <", value, "receProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceProvLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_prov <=", value, "receProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceProvLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_prov like", value, "receProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceProvNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_prov not like", value, "receProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceProvInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("rece_prov in", values, "receProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceProvNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("rece_prov not in", values, "receProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceProvBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("rece_prov between", value1, value2, "receProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceProvNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("rece_prov not between", value1, value2, "receProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceCityEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_city =", value, "receCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceCityNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_city <>", value, "receCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceCityGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_city >", value, "receCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceCityGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_city >=", value, "receCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceCityLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_city <", value, "receCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceCityLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_city <=", value, "receCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceCityLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_city like", value, "receCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceCityNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_city not like", value, "receCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceCityInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("rece_city in", values, "receCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceCityNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("rece_city not in", values, "receCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceCityBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("rece_city between", value1, value2, "receCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceCityNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("rece_city not between", value1, value2, "receCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceAreaEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_area =", value, "receArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceAreaNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_area <>", value, "receArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceAreaGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_area >", value, "receArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceAreaGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_area >=", value, "receArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceAreaLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_area <", value, "receArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceAreaLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_area <=", value, "receArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceAreaLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_area like", value, "receArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceAreaNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("rece_area not like", value, "receArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceAreaInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("rece_area in", values, "receArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceAreaNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("rece_area not in", values, "receArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceAreaBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("rece_area between", value1, value2, "receArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceAreaNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("rece_area not between", value1, value2, "receArea");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight =", value, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight <>", value, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight >", value, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight >=", value, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight <", value, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight <=", value, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("first_weight in", values, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("first_weight not in", values, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("first_weight between", value1, value2, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("first_weight not between", value1, value2, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight =", value, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight <>", value, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight >", value, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight >=", value, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight <", value, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight <=", value, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("step_weight in", values, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("step_weight not in", values, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("step_weight between", value1, value2, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("step_weight not between", value1, value2, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight_price =", value, "firstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight_price <>", value, "firstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight_price >", value, "firstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight_price >=", value, "firstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight_price <", value, "firstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight_price <=", value, "firstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("first_weight_price in", values, "firstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("first_weight_price not in", values, "firstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("first_weight_price between", value1, value2, "firstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightPriceNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("first_weight_price not between", value1, value2, "firstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight_price =", value, "stepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight_price <>", value, "stepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight_price >", value, "stepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight_price >=", value, "stepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight_price <", value, "stepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight_price <=", value, "stepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("step_weight_price in", values, "stepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("step_weight_price not in", values, "stepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("step_weight_price between", value1, value2, "stepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightPriceNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("step_weight_price not between", value1, value2, "stepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_first_weight_price =", value, "userFirstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_first_weight_price <>", value, "userFirstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_first_weight_price >", value, "userFirstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_first_weight_price >=", value, "userFirstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_first_weight_price <", value, "userFirstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_first_weight_price <=", value, "userFirstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_first_weight_price in", values, "userFirstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_first_weight_price not in", values, "userFirstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_first_weight_price between", value1, value2, "userFirstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserFirstWeightPriceNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_first_weight_price not between", value1, value2, "userFirstWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_step_weight_price =", value, "userStepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_step_weight_price <>", value, "userStepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_step_weight_price >", value, "userStepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_step_weight_price >=", value, "userStepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_step_weight_price <", value, "userStepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_step_weight_price <=", value, "userStepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_step_weight_price in", values, "userStepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_step_weight_price not in", values, "userStepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_step_weight_price between", value1, value2, "userStepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUserStepWeightPriceNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_step_weight_price not between", value1, value2, "userStepWeightPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUseStepPriceEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("use_step_price =", value, "useStepPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUseStepPriceNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("use_step_price <>", value, "useStepPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUseStepPriceGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("use_step_price >", value, "useStepPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUseStepPriceGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("use_step_price >=", value, "useStepPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUseStepPriceLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("use_step_price <", value, "useStepPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUseStepPriceLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("use_step_price <=", value, "useStepPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUseStepPriceLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("use_step_price like", value, "useStepPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUseStepPriceNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("use_step_price not like", value, "useStepPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUseStepPriceInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("use_step_price in", values, "useStepPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUseStepPriceNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("use_step_price not in", values, "useStepPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUseStepPriceBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("use_step_price between", value1, value2, "useStepPrice");
            }
            return (Criteria) this;
        }

        public Criteria andUseStepPriceNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("use_step_price not between", value1, value2, "useStepPrice");
            }
            return (Criteria) this;
        }

        public Criteria andStepFeatureEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("step_feature =", value, "stepFeature");
            }
            return (Criteria) this;
        }

        public Criteria andStepFeatureNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("step_feature <>", value, "stepFeature");
            }
            return (Criteria) this;
        }

        public Criteria andStepFeatureGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("step_feature >", value, "stepFeature");
            }
            return (Criteria) this;
        }

        public Criteria andStepFeatureGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("step_feature >=", value, "stepFeature");
            }
            return (Criteria) this;
        }

        public Criteria andStepFeatureLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("step_feature <", value, "stepFeature");
            }
            return (Criteria) this;
        }

        public Criteria andStepFeatureLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("step_feature <=", value, "stepFeature");
            }
            return (Criteria) this;
        }

        public Criteria andStepFeatureLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("step_feature like", value, "stepFeature");
            }
            return (Criteria) this;
        }

        public Criteria andStepFeatureNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("step_feature not like", value, "stepFeature");
            }
            return (Criteria) this;
        }

        public Criteria andStepFeatureInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("step_feature in", values, "stepFeature");
            }
            return (Criteria) this;
        }

        public Criteria andStepFeatureNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("step_feature not in", values, "stepFeature");
            }
            return (Criteria) this;
        }

        public Criteria andStepFeatureBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("step_feature between", value1, value2, "stepFeature");
            }
            return (Criteria) this;
        }

        public Criteria andStepFeatureNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("step_feature not between", value1, value2, "stepFeature");
            }
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("volume_weight_ratio =", value, "volumeWeightRatio");
            }
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("volume_weight_ratio <>", value, "volumeWeightRatio");
            }
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("volume_weight_ratio >", value, "volumeWeightRatio");
            }
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("volume_weight_ratio >=", value, "volumeWeightRatio");
            }
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("volume_weight_ratio <", value, "volumeWeightRatio");
            }
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("volume_weight_ratio <=", value, "volumeWeightRatio");
            }
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("volume_weight_ratio in", values, "volumeWeightRatio");
            }
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("volume_weight_ratio not in", values, "volumeWeightRatio");
            }
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("volume_weight_ratio between", value1, value2, "volumeWeightRatio");
            }
            return (Criteria) this;
        }

        public Criteria andVolumeWeightRatioNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("volume_weight_ratio not between", value1, value2, "volumeWeightRatio");
            }
            return (Criteria) this;
        }

        public Criteria andRouteRewardEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("route_reward =", value, "routeReward");
            }
            return (Criteria) this;
        }

        public Criteria andRouteRewardNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("route_reward <>", value, "routeReward");
            }
            return (Criteria) this;
        }

        public Criteria andRouteRewardGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("route_reward >", value, "routeReward");
            }
            return (Criteria) this;
        }

        public Criteria andRouteRewardGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("route_reward >=", value, "routeReward");
            }
            return (Criteria) this;
        }

        public Criteria andRouteRewardLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("route_reward <", value, "routeReward");
            }
            return (Criteria) this;
        }

        public Criteria andRouteRewardLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("route_reward <=", value, "routeReward");
            }
            return (Criteria) this;
        }

        public Criteria andRouteRewardInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("route_reward in", values, "routeReward");
            }
            return (Criteria) this;
        }

        public Criteria andRouteRewardNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("route_reward not in", values, "routeReward");
            }
            return (Criteria) this;
        }

        public Criteria andRouteRewardBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("route_reward between", value1, value2, "routeReward");
            }
            return (Criteria) this;
        }

        public Criteria andRouteRewardNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("route_reward not between", value1, value2, "routeReward");
            }
            return (Criteria) this;
        }

        public Criteria andFeatueEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("featue =", value, "featue");
            }
            return (Criteria) this;
        }

        public Criteria andFeatueNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("featue <>", value, "featue");
            }
            return (Criteria) this;
        }

        public Criteria andFeatueGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("featue >", value, "featue");
            }
            return (Criteria) this;
        }

        public Criteria andFeatueGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("featue >=", value, "featue");
            }
            return (Criteria) this;
        }

        public Criteria andFeatueLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("featue <", value, "featue");
            }
            return (Criteria) this;
        }

        public Criteria andFeatueLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("featue <=", value, "featue");
            }
            return (Criteria) this;
        }

        public Criteria andFeatueLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("featue like", value, "featue");
            }
            return (Criteria) this;
        }

        public Criteria andFeatueNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("featue not like", value, "featue");
            }
            return (Criteria) this;
        }

        public Criteria andFeatueInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("featue in", values, "featue");
            }
            return (Criteria) this;
        }

        public Criteria andFeatueNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("featue not in", values, "featue");
            }
            return (Criteria) this;
        }

        public Criteria andFeatueBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("featue between", value1, value2, "featue");
            }
            return (Criteria) this;
        }

        public Criteria andFeatueNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("featue not between", value1, value2, "featue");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *报价配置ID，关联报价配置表ID
         */
        QUOTEID("quote_id"),
        /**
         *出发省份
         */
        SENDPROV("send_prov"),
        /**
         *出发城市
         */
        SENDCITY("send_city"),
        /**
         *出发区县
         */
        SENDAREA("send_area"),
        /**
         *到达省份
         */
        RECEPROV("rece_prov"),
        /**
         *到达城市
         */
        RECECITY("rece_city"),
        /**
         *到达区县
         */
        RECEAREA("rece_area"),
        /**
         *首重，单位:g
         */
        FIRSTWEIGHT("first_weight"),
        /**
         *续重，单位:g
         */
        STEPWEIGHT("step_weight"),
        /**
         *首重价格，单位：分
         */
        FIRSTWEIGHTPRICE("first_weight_price"),
        /**
         *续重价格，单位：分
         */
        STEPWEIGHTPRICE("step_weight_price"),
        /**
         *对客首重价格，单位：分
         */
        USERFIRSTWEIGHTPRICE("user_first_weight_price"),
        /**
         *对客续重价格，单位：分
         */
        USERSTEPWEIGHTPRICE("user_step_weight_price"),
        /**
         *是否阶梯报价,Y/N
         */
        USESTEPPRICE("use_step_price"),
        /**
         *阶梯报价配置
         */
        STEPFEATURE("step_feature"),
        /**
         *计抛比系数
         */
        VOLUMEWEIGHTRATIO("volume_weight_ratio"),
        /**
         *线路奖励，单位：分
         */
        ROUTEREWARD("route_reward"),
        /**
         *扩展信息
         */
        FEATUE("featue");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}