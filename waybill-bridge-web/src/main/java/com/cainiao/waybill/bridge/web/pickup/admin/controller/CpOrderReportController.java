package com.cainiao.waybill.bridge.web.pickup.admin.controller;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.exception.PickUpBusinessException;
import com.cainiao.waybill.bridge.biz.pickup.service.AdminWebCommonReportService;
import com.cainiao.waybill.bridge.biz.pickup.service.AdminWebReportService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb.*;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.common.util.AclPermissionHelper;
import com.cainiao.waybill.bridge.web.pickup.admin.PermissionCheck;
import com.cainiao.waybill.bridge.web.pickup.constant.PickUpWebAdminConstant;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/2-下午4:02
 */
@RestController
@RequestMapping("/cp/order/report")
public class CpOrderReportController {

    @Resource
    private AdminWebReportService adminWebReportService;

    @Resource
    private AclPermissionHelper aclPermissionHelper;

    @Resource
    private HttpServletRequest httpServletRequest;

    /**
     * 获取已接入的CP列表
     *
     * @return :
     */
    @GetMapping("/cplist")
    public BaseResult<List<ItemMap>> getOrderChannels() {
        return BaseResult.success(PickUpCommonUtil.parseAdminWebMapItem(PickUpConstants.AdminWeb.MapInfoKey.CP_LIST.name()));
    }

    /**
     * @param startTime：查询起始时间
     * @param endTime：查询终止时间
     * @param cpCode：cpCode
     * @return ：依次按照时间范围、CP、订单渠道来源，三个维度来统计取消、接单、电联、揽收等率值
     */
    @GetMapping
    public BaseResult<CpReportResponse> count(String startTime, String endTime, String cpCode) {
        if (PermissionCheck.checkReportPermission()) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorCode(),
                PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorMsg());
        }
        PickUpLogUtil.info("/cp/order/report : startTime=" + startTime + ", endTime=" + endTime + ", cpCode=" + cpCode);
        if (StringUtil.isBlank(cpCode)) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
        }
        try {
            CpReportResponse cpReportResponse = adminWebReportService.cpStatistics(startTime, endTime, cpCode);
            return BaseResult.success(cpReportResponse);
        } catch (PickUpBusinessException businessException) {
            return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpWebAdminConstant.LogAction.CP_ORDER_REPORT.name(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
            return BaseResult.systemFail();
        }
    }

    @PostMapping("/pro")
    public BaseResult<ProvinceReportResponse> provinceCount(@RequestBody OrderDetailRequest cpOrderDetailRequest) {
        if (PermissionCheck.checkReportPermission()) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorCode(),
                PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorMsg());
        }

        PickUpLogUtil.info("/cp/order/report/pro : " + JSON.toJSONString(cpOrderDetailRequest));
        if (cpOrderDetailRequest == null || StringUtil.isBlank(cpOrderDetailRequest.getCpName())) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
        }
        try {
            AdminWebCommonReportService.convertDateStr2Date(cpOrderDetailRequest);
            ProvinceReportResponse provinceReportResponse = adminWebReportService.cpProStatistics(cpOrderDetailRequest);
            return BaseResult.success(provinceReportResponse);
        } catch (PickUpBusinessException businessException) {
            return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpWebAdminConstant.LogAction.CP_ORDER_REPORT_PRO.name(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
            return BaseResult.systemFail();
        }
    }

    @PostMapping("/detail")
    public BaseResult<OrderDetailResponse> detail(@RequestBody OrderDetailRequest cpOrderDetailRequest) {
        PickUpLogUtil.info("/cp/order/report/detail : " + JSON.toJSONString(cpOrderDetailRequest));

        if (PermissionCheck.checkCustomerServicePermission()) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorCode(),
                PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorMsg());
        }

        if (cpOrderDetailRequest == null || StringUtil.isBlank(cpOrderDetailRequest.getCpName()) || StringUtil.isBlank(cpOrderDetailRequest.getType())) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
        }
        try {
            AdminWebCommonReportService.convertDateStr2Date(cpOrderDetailRequest);
            OrderDetailResponse orderDetailResponse = adminWebReportService.orderDetailQuery(cpOrderDetailRequest, PickUpConstants.AdminWeb.ReportEnum.CP);
            return BaseResult.success(orderDetailResponse);
        } catch (PickUpBusinessException businessException) {
            return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpWebAdminConstant.LogAction.CP_ORDER_DETAIL.name(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
            return BaseResult.systemFail();
        }
    }

}
