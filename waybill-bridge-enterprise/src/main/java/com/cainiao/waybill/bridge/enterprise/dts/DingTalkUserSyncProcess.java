package com.cainiao.waybill.bridge.enterprise.dts;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.administrators.service.DingTalkUserService;
import com.cainiao.waybill.common.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/9
 * @description 钉钉用户同步
 **/
@Component
public class DingTalkUserSyncProcess extends JavaProcessor {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    private static final String CORP_ID = "corpId";

    private static final String START = "start";

    private static final String END = "end";

    @Autowired
    private DingTalkUserService dingTalkUserService;

    @Override
    public ProcessResult process(JobContext jobContext) throws Exception {
        String jobParameters = jobContext.getJobParameters();
        LOGGER.info("DingTalkAdminSyncProcess jobParameters:{}", JSONObject.toJSONString(jobParameters));
        if (StringUtils.isBlank(jobParameters)) {
            return new ProcessResult(true);
        }
        JSONObject jsonObject = JSONObject.parseObject(jobParameters);
        String corpIdStr = jsonObject.getString(CORP_ID);

        Integer start = jsonObject.getInteger(START);

        Integer end = jsonObject.getInteger(END);

        try {
            List<String> corpIdList = Arrays.stream(corpIdStr.split(","))
                    .map(String::trim)  // 去除项前后的空格
                    .filter(StringUtil::isNotBlank)
                    .collect(Collectors.toList());
            corpIdList.forEach(corpId -> {
                dingTalkUserService.dingTalkUserSync(corpId, start, end);
            });
            return new ProcessResult(true);
        } catch (Exception e) {
            LOGGER.error("DingTalkAdminSyncProcess error:{}", ExceptionUtils.getStackTrace(e));
            return new ProcessResult(false);
        }
    }
}
