package com.cainiao.waybill.bridge.biz.link.pickup.service.impl.cprouting;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.cainiao.waybill.bridge.biz.pickup.dto.CpDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpCpConfigInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.create.pre.PickUpCreatePreTdAppointTimeSlotDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.RouteReachableResult;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

/**
 * <AUTHOR> zouping.fzp
 * @Classname CpRountingContext
 * @Description
 * @Date 2022/8/2 2:03 下午
 * @Version 1.0
 */
@Data
public class PickUpCpBizRoutingContext implements Serializable {

    private static final long serialVersionUID = -4910964914572407240L;
    /**
     * 优先使用的cp
     */
    private PickUpCpConfigInfo cpInfo;

    /**
     * 备用服务商列表
     */
    private List<PickUpCpConfigInfo> backUpCpList = Lists.newArrayList();

    /**
     * 命中规则
     */
    private String routerStrategyId;

    /**
     * 缓存cp可达性校验结果
     */
    Map<PickUpCpConfigInfo, RouteReachableResult> cpReachResult = Maps.newHashMap();
}
