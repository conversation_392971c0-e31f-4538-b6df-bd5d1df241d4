package com.cainiao.waybill.bridge.enterprise.utils;

import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.taobao.tair.DataEntry;
import com.taobao.tair.Result;
import com.taobao.tair.ResultCode;
import com.taobao.tair.etc.TairConstant;
import com.taobao.tair.impl.mc.MultiClusterTairManager;
import com.taobao.unifiedsession.core.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * <AUTHOR> y<PERSON><PERSON>
 * @Classname EnterpriseCacheUtil
 * @Description
 * @Date 2022/9/1 9:50 上午
 * @Version 1.0
 */
@Component
public class EnterpriseCacheUtil {
    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    /**
     * tair 空间，和 waybill 共用
     */
    private static final int TAIR_NAMESPACE = 5645;

    @Qualifier("waybillPickUpTairManager")
    @Resource
    private MultiClusterTairManager tairManager;

    public void put(Serializable key, Serializable value, int expireTime) {
        ResultCode resultCode = tairManager.put(TAIR_NAMESPACE, key, value, TairConstant.NOT_CARE_VERSION, expireTime);
        if (!resultCode.isSuccess()) {
            LOGGER.error("cache_write_error, result:{}", JSON.toJSONObject(resultCode));
            throw new BridgeBusinessException("cache_write_error", "缓存写入失败");
        }
    }

    public Object get(Serializable key) {
        Result<DataEntry> resultCode = tairManager.get(TAIR_NAMESPACE, key);
        if (!resultCode.isSuccess() && resultCode.getValue() == null) {
            LOGGER.error("cache_get_error, result:{}", JSON.toJSONObject(resultCode));
            throw new BridgeBusinessException("cache_read_error", "缓存读取失败");
        }
        if (resultCode.getValue() == null) {
            return null;
        }
        return resultCode.getValue().getValue();
    }

    public void del(Serializable key) {
        ResultCode resultCode = tairManager.delete(TAIR_NAMESPACE, key);
        if (!resultCode.isSuccess()) {
            LOGGER.error("cache_del_error, result:{}", JSON.toJSONObject(resultCode));
            throw new BridgeBusinessException("cache_del_error", "缓存读取失败");
        }
    }

}
