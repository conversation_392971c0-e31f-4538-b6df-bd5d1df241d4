package com.cainiao.waybill.bridge.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class WaybillEnterpriseLocationParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public WaybillEnterpriseLocationParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public WaybillEnterpriseLocationParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public WaybillEnterpriseLocationParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public WaybillEnterpriseLocationParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public WaybillEnterpriseLocationParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public WaybillEnterpriseLocationParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCorpIdIsNull() {
            addCriterion("corp_id is null");
            return (Criteria) this;
        }

        public Criteria andCorpIdIsNotNull() {
            addCriterion("corp_id is not null");
            return (Criteria) this;
        }

        public Criteria andCorpIdEqualTo(String value) {
            addCriterion("corp_id =", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdNotEqualTo(String value) {
            addCriterion("corp_id <>", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdGreaterThan(String value) {
            addCriterion("corp_id >", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdGreaterThanOrEqualTo(String value) {
            addCriterion("corp_id >=", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdLessThan(String value) {
            addCriterion("corp_id <", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdLessThanOrEqualTo(String value) {
            addCriterion("corp_id <=", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdLike(String value) {
            addCriterion("corp_id like", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdNotLike(String value) {
            addCriterion("corp_id not like", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdIn(List<String> values) {
            addCriterion("corp_id in", values, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdNotIn(List<String> values) {
            addCriterion("corp_id not in", values, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdBetween(String value1, String value2) {
            addCriterion("corp_id between", value1, value2, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdNotBetween(String value1, String value2) {
            addCriterion("corp_id not between", value1, value2, "corpId");
            return (Criteria) this;
        }

        public Criteria andLocationNameIsNull() {
            addCriterion("location_name is null");
            return (Criteria) this;
        }

        public Criteria andLocationNameIsNotNull() {
            addCriterion("location_name is not null");
            return (Criteria) this;
        }

        public Criteria andLocationNameEqualTo(String value) {
            addCriterion("location_name =", value, "locationName");
            return (Criteria) this;
        }

        public Criteria andLocationNameNotEqualTo(String value) {
            addCriterion("location_name <>", value, "locationName");
            return (Criteria) this;
        }

        public Criteria andLocationNameGreaterThan(String value) {
            addCriterion("location_name >", value, "locationName");
            return (Criteria) this;
        }

        public Criteria andLocationNameGreaterThanOrEqualTo(String value) {
            addCriterion("location_name >=", value, "locationName");
            return (Criteria) this;
        }

        public Criteria andLocationNameLessThan(String value) {
            addCriterion("location_name <", value, "locationName");
            return (Criteria) this;
        }

        public Criteria andLocationNameLessThanOrEqualTo(String value) {
            addCriterion("location_name <=", value, "locationName");
            return (Criteria) this;
        }

        public Criteria andLocationNameLike(String value) {
            addCriterion("location_name like", value, "locationName");
            return (Criteria) this;
        }

        public Criteria andLocationNameNotLike(String value) {
            addCriterion("location_name not like", value, "locationName");
            return (Criteria) this;
        }

        public Criteria andLocationNameIn(List<String> values) {
            addCriterion("location_name in", values, "locationName");
            return (Criteria) this;
        }

        public Criteria andLocationNameNotIn(List<String> values) {
            addCriterion("location_name not in", values, "locationName");
            return (Criteria) this;
        }

        public Criteria andLocationNameBetween(String value1, String value2) {
            addCriterion("location_name between", value1, value2, "locationName");
            return (Criteria) this;
        }

        public Criteria andLocationNameNotBetween(String value1, String value2) {
            addCriterion("location_name not between", value1, value2, "locationName");
            return (Criteria) this;
        }

        public Criteria andLocationAddressIsNull() {
            addCriterion("location_address is null");
            return (Criteria) this;
        }

        public Criteria andLocationAddressIsNotNull() {
            addCriterion("location_address is not null");
            return (Criteria) this;
        }

        public Criteria andLocationAddressEqualTo(String value) {
            addCriterion("location_address =", value, "locationAddress");
            return (Criteria) this;
        }

        public Criteria andLocationAddressNotEqualTo(String value) {
            addCriterion("location_address <>", value, "locationAddress");
            return (Criteria) this;
        }

        public Criteria andLocationAddressGreaterThan(String value) {
            addCriterion("location_address >", value, "locationAddress");
            return (Criteria) this;
        }

        public Criteria andLocationAddressGreaterThanOrEqualTo(String value) {
            addCriterion("location_address >=", value, "locationAddress");
            return (Criteria) this;
        }

        public Criteria andLocationAddressLessThan(String value) {
            addCriterion("location_address <", value, "locationAddress");
            return (Criteria) this;
        }

        public Criteria andLocationAddressLessThanOrEqualTo(String value) {
            addCriterion("location_address <=", value, "locationAddress");
            return (Criteria) this;
        }

        public Criteria andLocationAddressLike(String value) {
            addCriterion("location_address like", value, "locationAddress");
            return (Criteria) this;
        }

        public Criteria andLocationAddressNotLike(String value) {
            addCriterion("location_address not like", value, "locationAddress");
            return (Criteria) this;
        }

        public Criteria andLocationAddressIn(List<String> values) {
            addCriterion("location_address in", values, "locationAddress");
            return (Criteria) this;
        }

        public Criteria andLocationAddressNotIn(List<String> values) {
            addCriterion("location_address not in", values, "locationAddress");
            return (Criteria) this;
        }

        public Criteria andLocationAddressBetween(String value1, String value2) {
            addCriterion("location_address between", value1, value2, "locationAddress");
            return (Criteria) this;
        }

        public Criteria andLocationAddressNotBetween(String value1, String value2) {
            addCriterion("location_address not between", value1, value2, "locationAddress");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidIsNull() {
            addCriterion("waybill_account_vid is null");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidIsNotNull() {
            addCriterion("waybill_account_vid is not null");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidEqualTo(String value) {
            addCriterion("waybill_account_vid =", value, "waybillAccountVid");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidNotEqualTo(String value) {
            addCriterion("waybill_account_vid <>", value, "waybillAccountVid");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidGreaterThan(String value) {
            addCriterion("waybill_account_vid >", value, "waybillAccountVid");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidGreaterThanOrEqualTo(String value) {
            addCriterion("waybill_account_vid >=", value, "waybillAccountVid");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidLessThan(String value) {
            addCriterion("waybill_account_vid <", value, "waybillAccountVid");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidLessThanOrEqualTo(String value) {
            addCriterion("waybill_account_vid <=", value, "waybillAccountVid");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidLike(String value) {
            addCriterion("waybill_account_vid like", value, "waybillAccountVid");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidNotLike(String value) {
            addCriterion("waybill_account_vid not like", value, "waybillAccountVid");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidIn(List<String> values) {
            addCriterion("waybill_account_vid in", values, "waybillAccountVid");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidNotIn(List<String> values) {
            addCriterion("waybill_account_vid not in", values, "waybillAccountVid");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidBetween(String value1, String value2) {
            addCriterion("waybill_account_vid between", value1, value2, "waybillAccountVid");
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidNotBetween(String value1, String value2) {
            addCriterion("waybill_account_vid not between", value1, value2, "waybillAccountVid");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeIsNull() {
            addCriterion("location_address_code is null");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeIsNotNull() {
            addCriterion("location_address_code is not null");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeEqualTo(String value) {
            addCriterion("location_address_code =", value, "locationAddressCode");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeNotEqualTo(String value) {
            addCriterion("location_address_code <>", value, "locationAddressCode");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeGreaterThan(String value) {
            addCriterion("location_address_code >", value, "locationAddressCode");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeGreaterThanOrEqualTo(String value) {
            addCriterion("location_address_code >=", value, "locationAddressCode");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeLessThan(String value) {
            addCriterion("location_address_code <", value, "locationAddressCode");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeLessThanOrEqualTo(String value) {
            addCriterion("location_address_code <=", value, "locationAddressCode");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeLike(String value) {
            addCriterion("location_address_code like", value, "locationAddressCode");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeNotLike(String value) {
            addCriterion("location_address_code not like", value, "locationAddressCode");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeIn(List<String> values) {
            addCriterion("location_address_code in", values, "locationAddressCode");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeNotIn(List<String> values) {
            addCriterion("location_address_code not in", values, "locationAddressCode");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeBetween(String value1, String value2) {
            addCriterion("location_address_code between", value1, value2, "locationAddressCode");
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeNotBetween(String value1, String value2) {
            addCriterion("location_address_code not between", value1, value2, "locationAddressCode");
            return (Criteria) this;
        }

        public Criteria andLocationIdIsNull() {
            addCriterion("location_id is null");
            return (Criteria) this;
        }

        public Criteria andLocationIdIsNotNull() {
            addCriterion("location_id is not null");
            return (Criteria) this;
        }

        public Criteria andLocationIdEqualTo(String value) {
            addCriterion("location_id =", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdNotEqualTo(String value) {
            addCriterion("location_id <>", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdGreaterThan(String value) {
            addCriterion("location_id >", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdGreaterThanOrEqualTo(String value) {
            addCriterion("location_id >=", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdLessThan(String value) {
            addCriterion("location_id <", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdLessThanOrEqualTo(String value) {
            addCriterion("location_id <=", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdLike(String value) {
            addCriterion("location_id like", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdNotLike(String value) {
            addCriterion("location_id not like", value, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdIn(List<String> values) {
            addCriterion("location_id in", values, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdNotIn(List<String> values) {
            addCriterion("location_id not in", values, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdBetween(String value1, String value2) {
            addCriterion("location_id between", value1, value2, "locationId");
            return (Criteria) this;
        }

        public Criteria andLocationIdNotBetween(String value1, String value2) {
            addCriterion("location_id not between", value1, value2, "locationId");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id =", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id <>", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id >", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id >=", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id <", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id <=", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id like", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id not like", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("corp_id in", values, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("corp_id not in", values, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("corp_id between", value1, value2, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("corp_id not between", value1, value2, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andLocationNameEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_name =", value, "locationName");
            }
            return (Criteria) this;
        }

        public Criteria andLocationNameNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_name <>", value, "locationName");
            }
            return (Criteria) this;
        }

        public Criteria andLocationNameGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_name >", value, "locationName");
            }
            return (Criteria) this;
        }

        public Criteria andLocationNameGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_name >=", value, "locationName");
            }
            return (Criteria) this;
        }

        public Criteria andLocationNameLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_name <", value, "locationName");
            }
            return (Criteria) this;
        }

        public Criteria andLocationNameLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_name <=", value, "locationName");
            }
            return (Criteria) this;
        }

        public Criteria andLocationNameLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_name like", value, "locationName");
            }
            return (Criteria) this;
        }

        public Criteria andLocationNameNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_name not like", value, "locationName");
            }
            return (Criteria) this;
        }

        public Criteria andLocationNameInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("location_name in", values, "locationName");
            }
            return (Criteria) this;
        }

        public Criteria andLocationNameNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("location_name not in", values, "locationName");
            }
            return (Criteria) this;
        }

        public Criteria andLocationNameBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("location_name between", value1, value2, "locationName");
            }
            return (Criteria) this;
        }

        public Criteria andLocationNameNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("location_name not between", value1, value2, "locationName");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address =", value, "locationAddress");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address <>", value, "locationAddress");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address >", value, "locationAddress");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address >=", value, "locationAddress");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address <", value, "locationAddress");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address <=", value, "locationAddress");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address like", value, "locationAddress");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address not like", value, "locationAddress");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("location_address in", values, "locationAddress");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("location_address not in", values, "locationAddress");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("location_address between", value1, value2, "locationAddress");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("location_address not between", value1, value2, "locationAddress");
            }
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("waybill_account_vid =", value, "waybillAccountVid");
            }
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("waybill_account_vid <>", value, "waybillAccountVid");
            }
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("waybill_account_vid >", value, "waybillAccountVid");
            }
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("waybill_account_vid >=", value, "waybillAccountVid");
            }
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("waybill_account_vid <", value, "waybillAccountVid");
            }
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("waybill_account_vid <=", value, "waybillAccountVid");
            }
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("waybill_account_vid like", value, "waybillAccountVid");
            }
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("waybill_account_vid not like", value, "waybillAccountVid");
            }
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("waybill_account_vid in", values, "waybillAccountVid");
            }
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("waybill_account_vid not in", values, "waybillAccountVid");
            }
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("waybill_account_vid between", value1, value2, "waybillAccountVid");
            }
            return (Criteria) this;
        }

        public Criteria andWaybillAccountVidNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("waybill_account_vid not between", value1, value2, "waybillAccountVid");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark =", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark <>", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark >", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark >=", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark <", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark <=", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark like", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark not like", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("remark in", values, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("remark not in", values, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("remark between", value1, value2, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("remark not between", value1, value2, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address_code =", value, "locationAddressCode");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address_code <>", value, "locationAddressCode");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address_code >", value, "locationAddressCode");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address_code >=", value, "locationAddressCode");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address_code <", value, "locationAddressCode");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address_code <=", value, "locationAddressCode");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address_code like", value, "locationAddressCode");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_address_code not like", value, "locationAddressCode");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("location_address_code in", values, "locationAddressCode");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("location_address_code not in", values, "locationAddressCode");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("location_address_code between", value1, value2, "locationAddressCode");
            }
            return (Criteria) this;
        }

        public Criteria andLocationAddressCodeNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("location_address_code not between", value1, value2, "locationAddressCode");
            }
            return (Criteria) this;
        }

        public Criteria andLocationIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_id =", value, "locationId");
            }
            return (Criteria) this;
        }

        public Criteria andLocationIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_id <>", value, "locationId");
            }
            return (Criteria) this;
        }

        public Criteria andLocationIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_id >", value, "locationId");
            }
            return (Criteria) this;
        }

        public Criteria andLocationIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_id >=", value, "locationId");
            }
            return (Criteria) this;
        }

        public Criteria andLocationIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_id <", value, "locationId");
            }
            return (Criteria) this;
        }

        public Criteria andLocationIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_id <=", value, "locationId");
            }
            return (Criteria) this;
        }

        public Criteria andLocationIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_id like", value, "locationId");
            }
            return (Criteria) this;
        }

        public Criteria andLocationIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("location_id not like", value, "locationId");
            }
            return (Criteria) this;
        }

        public Criteria andLocationIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("location_id in", values, "locationId");
            }
            return (Criteria) this;
        }

        public Criteria andLocationIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("location_id not in", values, "locationId");
            }
            return (Criteria) this;
        }

        public Criteria andLocationIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("location_id between", value1, value2, "locationId");
            }
            return (Criteria) this;
        }

        public Criteria andLocationIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("location_id not between", value1, value2, "locationId");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *企业唯一id
         */
        CORPID("corp_id"),
        /**
         *办公地点自定义名称
         */
        LOCATIONNAME("location_name"),
        /**
         *办公场地详细地址
         */
        LOCATIONADDRESS("location_address"),
        /**
         *关联月结账号配置
         */
        WAYBILLACCOUNTVID("waybill_account_vid"),
        /**
         *备注
         */
        REMARK("remark"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *地址编码
         */
        LOCATIONADDRESSCODE("location_address_code"),
        /**
         *场地业务唯一字段
         */
        LOCATIONID("location_id");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}