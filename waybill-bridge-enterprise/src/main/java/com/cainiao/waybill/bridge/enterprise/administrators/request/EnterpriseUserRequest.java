package com.cainiao.waybill.bridge.enterprise.administrators.request;

import com.cainiao.waybill.bridge.enterprise.common.Paging;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePlatformEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2025/4/16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class EnterpriseUserRequest extends Paging implements Serializable {

    private static final long serialVersionUID = 6338972990272623149L;
    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户类型
     * @see com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseUserTypeEnum
     */
    private String userType;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 备注
     */
    private String remake;

    /**
     * 来源平台
     * @see EnterprisePlatformEnum
     */
    private String from;

    /**
     * 手机号
     */
    private String phoneNum;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 小邮局id
     */
    private Long postId;

    /**
     * 场地id
     */
    private Long locationId;

    /**
     * 搜索值
     */
    private String searchValue;
}
