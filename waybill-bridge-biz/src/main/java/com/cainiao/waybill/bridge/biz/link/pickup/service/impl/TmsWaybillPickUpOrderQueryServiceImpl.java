package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.manager.PickUpCpOrderQueryManager;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.manager.factory.PickUpCpOrderQueryComponentFactory;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillPickUpTicketService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_ORDER_DETAIL_QUERY.WaybillOrderDetailQueryRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_ORDER_DETAIL_QUERY.WaybillOrderDetailQueryResponse;
import com.taobao.pac.client.sdk.receiveservice.WaybillOrderDetailQueryService;

/**
 * 提供订单查询接口
 *
 * @author: zouping.fzp
 * @date: 2023-02-10 10:41
 **/
public class TmsWaybillPickUpOrderQueryServiceImpl implements WaybillOrderDetailQueryService {

    @Resource
    private WaybillPickUpOrderManager waybillPickUpOrderManager;

    @Override
    public WaybillOrderDetailQueryResponse invoke(ReceiveParams<WaybillOrderDetailQueryRequest> receiveParams) {
        PickUpLogUtil.info("WaybillPickUpOrderQueryRequest : " + receiveParams);
        //String fromResCode = receiveParams.getCpCode();
        String fromAppKey = PickUpCommonUtil.mappingFromAppKey(receiveParams.getFromAppkey());
        WaybillOrderDetailQueryRequest detailQueryRequest = receiveParams.getRequestDataObject();
        WaybillOrderDetailQueryResponse linkTicketCreateResponse = new WaybillOrderDetailQueryResponse();

        try {
            String resCode = PickUpCommonUtil.buildResCode(fromAppKey, detailQueryRequest.getOrderChannels());

            WaybillPickUpDetailDO waybillPickUpDetailDO = waybillPickUpOrderManager.get(resCode,
                detailQueryRequest.getOuterOrderCode());
            if(waybillPickUpDetailDO == null){
                throw new BridgeBaseException("order_not_found", "订单不存在");
            }

            PickUpCpOrderQueryManager pickUpCpOrderQueryManager = PickUpCpOrderQueryComponentFactory.getCpOrderQueryManager(waybillPickUpDetailDO.getCpCode());


            WaybillOrderDetailQueryResponse response = pickUpCpOrderQueryManager.queryOrder(waybillPickUpDetailDO);
            response.setSuccess(true);

            return response;
        } catch (BridgeBaseException e) {
            linkTicketCreateResponse.setSuccess(false);
            linkTicketCreateResponse.setErrorCode(e.getErrorCode());
            linkTicketCreateResponse.setErrorMsg(e.getErrorMessage());
            PickUpLogUtil.errLog("", PickUpConstants.Action.PICK_UP_ORDER_DETAIL_QUERY.name(), e.getErrorCode(), e.getErrorMessage(), e);
        } catch (Throwable e) {
            linkTicketCreateResponse.setSuccess(false);
            linkTicketCreateResponse.setErrorMsg(e.getMessage());
            PickUpLogUtil.errLog("", PickUpConstants.Action.PICK_UP_ORDER_DETAIL_QUERY.name(), "", e.getMessage(), e);
        }
        return linkTicketCreateResponse;
    }
}
