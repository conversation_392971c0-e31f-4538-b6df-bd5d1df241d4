package com.cainiao.waybill.bridge.common.label.dto;


import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * Created by guochao.tgc on 2016/6/3.
 */
public class UserInfoDTO extends BaseDTO {
    private static final long serialVersionUID = -6707576969667513126L;

    /**
     * 姓名
     */
    private String            name;
    /**
     * 固定电话
     */
    private String            phone;
    /**
     * 手机
     */
    private String            mobile;

    /**
     * 地址信息
     */
    private AddressDTO        address;


    public UserInfoDTO() {}

    public UserInfoDTO(String name, String phone, String mobile, AddressDTO address) {
        this.name = name;
        this.phone = phone;
        this.mobile = mobile;
        this.address = address;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public AddressDTO getAddress() {
        return address;
    }

    public void setAddress(AddressDTO address) {
        this.address = address;
    }

}
