package com.cainiao.waybill.bridge.biz.hsf.provider;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastvalidator.core.FastValidatorUtils;

import com.cainiao.waybill.bridge.SimpleResult;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpDetailBizTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpCreateOrderRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpCreateOrderResponse;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillPickUpOrderService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.wrapper.ContentRiskWrapper;
import com.cainiao.waybill.bridge.client.entity.GoodsInfo;
import com.cainiao.waybill.bridge.client.hsf.TmsWaybillPickUpService;
import com.cainiao.waybill.bridge.client.request.PickUpHsfCreateOrderRequest;
import com.cainiao.waybill.bridge.client.response.PickUpHsfCreateOrderResponse;
import com.cainiao.waybill.bridge.common.base.ScenarioConstant;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.util.LoggerMonitorUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

/**
 * 淘外订单服务HSF接口
 * <AUTHOR>
 * @date 2024/7/5 15:41
 **/
@Slf4j
@HSFProvider(serviceInterface = TmsWaybillPickUpService.class)
public class TmsWaybillPickUpServiceImpl implements TmsWaybillPickUpService {

    @Resource
    private ContentRiskWrapper contentRiskWrapper;
    @Resource
    private WaybillPickUpOrderService waybillPickUpOrderService;

    private static final String ERROR_MSG_REPLACE_CODE = "param error|";

    private static final String ERROR_MSG_SUFFIX = ";";

    @Override
    public SimpleResult<PickUpHsfCreateOrderResponse> createOrder(PickUpHsfCreateOrderRequest request) {
        PickUpLogUtil.info("HSF下单接口入参:{}", null == request ? "null" : JSONObject.toJSONString(request));
        LoggerMonitorUtil.start(ScenarioConstant.TMS_WAYBILL_HSF_CREATE_ORDER, null);

        SimpleResult<PickUpHsfCreateOrderResponse> result = SimpleResult.success(null);
        try {
            // 1 参数校验
            checkRequestParam(request);

            PickUpCreateOrderRequest orderRequest = new PickUpCreateOrderRequest();
            BeanUtils.copyProperties(request, orderRequest);
            // 处理物品信息
            enhanceGoodsInfo(request, orderRequest);
            orderRequest.setFromAppKey(request.getResourceCode());

            String resCode = PickUpCommonUtil.buildResCode(request.getResourceCode(), request.getOrderChannels());
            orderRequest.setResCode(resCode);

            // 2.1 风控-制裁
            contentRiskWrapper.checkRisk(orderRequest);
            // 2.2 风控-违禁词
            contentRiskWrapper.checkRiskWord(orderRequest);

            //  bizType为空，默认为实时下单模式
            if (orderRequest.getBizType() == null) {
                orderRequest.setBizType(PickUpDetailBizTypeEnum.NORMAL.getValue());
            }
            Map<String, String> featureMap = Maps.newHashMap();
            featureMap.put(PickUpConstants.TraceFeatureKey.LINK_CP_CODE, orderRequest.getFromAppKey());
            orderRequest.setFeatureMap(featureMap);

            // 3 下单
            PickUpCreateOrderResponse response = waybillPickUpOrderService.create(orderRequest);
            PickUpHsfCreateOrderResponse hsfResponse = new PickUpHsfCreateOrderResponse();
            BeanUtils.copyProperties(response, hsfResponse);
            hsfResponse.setOuterOrderCode(request.getOuterOrderCode());
            result.setErrorCode("SUCCESS");
            result.setData(hsfResponse);
            PickUpLogUtil.info("HSF下单接口出参:{}", JSONObject.toJSONString(hsfResponse));
        }catch (BridgeBaseException e){
            result.setSuccess(false);
            result.setErrorCode(e.getErrorCode());
            result.setErrorMsg(e.getErrorMessage());
            PickUpLogUtil.errLog(request.getOuterOrderCode(), PickUpConstants.Action.HSF_CREATE_ORDER_EXCEPTION.name(), e.getErrorCode(), e.getErrorMessage(), ExceptionUtils.getFullStackTrace(e));
        }catch (Throwable e) {
            result.setSuccess(false);
            result.setErrorCode(Error.SYSTEM_ERROR.getErrorCode());
            result.setErrorMsg(Error.SYSTEM_ERROR.getErrorMsg());
            PickUpLogUtil.errLog(request.getOuterOrderCode(), PickUpConstants.Action.HSF_CREATE_ORDER_EXCEPTION.name(), "HSF_CREATE_ORDER_EXCEPTION", e.getMessage(), ExceptionUtils.getFullStackTrace(e));
        }finally {
            LoggerMonitorUtil.end(result.isSuccess(), result.getErrorCode());
        }
        return result;
    }

    /**
     * 商品信息兜底
     * @param hsfRequest
     * @param createRequest
     */
    private void enhanceGoodsInfo(PickUpHsfCreateOrderRequest hsfRequest, PickUpCreateOrderRequest createRequest) {
        List<GoodsInfo> hsfGoodsList = hsfRequest.getGoodsInfos();
        List<com.cainiao.waybill.bridge.biz.pickup.dto.GoodsInfo> goodsInfoList = new ArrayList<>();
        for(GoodsInfo hsfGoodsInfo: hsfGoodsList){
            com.cainiao.waybill.bridge.biz.pickup.dto.GoodsInfo goodsInfo = new com.cainiao.waybill.bridge.biz.pickup.dto.GoodsInfo();
            if(StringUtils.isBlank(hsfGoodsInfo.getName())){
                goodsInfo.setName("默认商品");
            }else{
                goodsInfo.setName(hsfGoodsInfo.getName());
            }
            if(null == hsfGoodsInfo.getWeight()){
                goodsInfo.setWeight(1000);
            }else {
                goodsInfo.setWeight(hsfGoodsInfo.getWeight());
            }
            if(null == hsfGoodsInfo.getQuantity()){
                goodsInfo.setQuantity(1);
            }else {
                goodsInfo.setQuantity(hsfGoodsInfo.getQuantity());
            }
            goodsInfoList.add(goodsInfo);
        }
        createRequest.setGoodsInfos(goodsInfoList);

    }

    /**
     * 下单参数校验
     * @param request
     */
    private void checkRequestParam(PickUpHsfCreateOrderRequest request) throws BridgeBaseException {
        if(request == null){
            throw new BridgeBaseException(PickUpConstants.Error.SYSTEM_PARAM_ERROR.getErrorCode(), PickUpConstants.Error.SYSTEM_PARAM_ERROR.getErrorMsg());
        }

        if(StringUtils.isBlank(request.getResourceCode())){
            throw new BridgeBaseException(PickUpConstants.Error.FROM_APP_KEY_NULL_ERROR.getErrorCode(), PickUpConstants.Error.FROM_APP_KEY_NULL_ERROR.getErrorMsg());
        }

        if(CollectionUtils.isEmpty(request.getGoodsInfos())){
            throw new BridgeBaseException(PickUpConstants.Error.GOODS_INFO_NULL_ERROR.getErrorCode(), PickUpConstants.Error.GOODS_INFO_NULL_ERROR.getErrorMsg());
        }

        if(StringUtils.isAllBlank(request.getSendMobile(), request.getSendPhone())){
            throw new BridgeBaseException(PickUpConstants.Error.SEND_PHONE_OR_MOBILE_IS_NULL.getErrorCode(), PickUpConstants.Error.SEND_PHONE_OR_MOBILE_IS_NULL.getErrorMsg());
        }

        if(StringUtils.isAllBlank(request.getConsigneeMobile(), request.getConsigneePhone())){
            throw new BridgeBaseException(PickUpConstants.Error.CONSIGNEE_PHONE_OR_MOBILE_IS_NULL.getErrorCode(), PickUpConstants.Error.CONSIGNEE_PHONE_OR_MOBILE_IS_NULL.getErrorMsg());
        }

        // 预约单 校验预约时间
        boolean appointTimeNull = null == request.getAppointGotStartTime() || null == request.getAppointGotEndTime();
        if(null != request.getBizType()
            && PickUpDetailBizTypeEnum.APPOINT.getValue() == request.getBizType()
            && appointTimeNull){
            throw new BridgeBaseException(PickUpConstants.Error.PICK_UP_CREATE_APPOINT_TIME_NULL.getErrorCode(), PickUpConstants.Error.PICK_UP_CREATE_APPOINT_TIME_NULL.getErrorMsg());
        }

        // 通用参数校验
        Set<ConstraintViolation<PickUpHsfCreateOrderRequest>> validationSet = FastValidatorUtils.validate(request);
        if (CollectionUtils.isNotEmpty(validationSet)){
            StringBuilder errMsg = new StringBuilder();
            for (ConstraintViolation<PickUpHsfCreateOrderRequest> constraintViolation : validationSet) {
                String errorMsg = constraintViolation.getMessage().replace(ERROR_MSG_REPLACE_CODE, "");
                errMsg.append(errorMsg).append(ERROR_MSG_SUFFIX);
            }
            if(errMsg.toString().endsWith(ERROR_MSG_SUFFIX)){
                errMsg.deleteCharAt(errMsg.length() - 1);
            }
            throw new BridgeBaseException(PickUpConstants.Error.SYSTEM_PARAM_ERROR.getErrorCode(), errMsg.toString());
        }

        if(null != request.getSendAddress() && StringUtils.isBlank(request.getSendAddress().getProvinceName())){
            throw new BridgeBaseException(Error.SEND_ADDRESS_PROVINCE_EMPTY.getErrorCode(), PickUpConstants.Error.SEND_ADDRESS_PROVINCE_EMPTY.getErrorMsg());
        }
        if(null != request.getSendAddress() && StringUtils.isBlank(request.getSendAddress().getCityName())){
            throw new BridgeBaseException(Error.SEND_ADDRESS_CITY_EMPTY.getErrorCode(), PickUpConstants.Error.SEND_ADDRESS_CITY_EMPTY.getErrorMsg());
        }
        if(null != request.getSendAddress() && StringUtils.isBlank(request.getSendAddress().getAddressDetail())){
            throw new BridgeBaseException(Error.SEND_ADDRESS_DETAIL_ADDRESS_EMPTY.getErrorCode(), PickUpConstants.Error.SEND_ADDRESS_DETAIL_ADDRESS_EMPTY.getErrorMsg());
        }
        if(null != request.getConsigneeAddress() && StringUtils.isBlank(request.getConsigneeAddress().getProvinceName())){
            throw new BridgeBaseException(Error.CONSIGNEE_ADDRESS_PROVINCE_EMPTY.getErrorCode(), PickUpConstants.Error.CONSIGNEE_ADDRESS_PROVINCE_EMPTY.getErrorMsg());
        }
        if(null != request.getConsigneeAddress() && StringUtils.isBlank(request.getConsigneeAddress().getCityName())){
            throw new BridgeBaseException(Error.CONSIGNEE_ADDRESS_CITY_EMPTY.getErrorCode(), PickUpConstants.Error.CONSIGNEE_ADDRESS_CITY_EMPTY.getErrorMsg());
        }
        if(null != request.getConsigneeAddress() && StringUtils.isBlank(request.getConsigneeAddress().getAddressDetail())){
            throw new BridgeBaseException(Error.CONSIGNEE_ADDRESS_DETAIL_EMPTY.getErrorCode(), PickUpConstants.Error.CONSIGNEE_ADDRESS_DETAIL_EMPTY.getErrorMsg());
        }

    }
}
