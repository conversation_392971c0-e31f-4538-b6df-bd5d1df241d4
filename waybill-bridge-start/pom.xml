<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>com.cainiao.waybill</groupId>
        <artifactId>waybill-bridge</artifactId>
        <version>${waybill-bridge.version}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>waybill-bridge-start</artifactId>
    <packaging>jar</packaging>
    <version>${waybill-bridge.version}</version>
    <name>waybill-bridge-start</name>

    <dependencies>
        <dependency>
            <groupId>com.cainiao.waybill</groupId>
            <artifactId>waybill-bridge-biz</artifactId>
            <exclusions>
            	<exclusion>
            		<artifactId>slf4j-simple</artifactId>
            		<groupId>org.slf4j</groupId>
            	</exclusion>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>org.jboss.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cainiao.waybill</groupId>
            <artifactId>waybill-bridge-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-simple</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cainiao.waybill</groupId>
            <artifactId>waybill-bridge-aliSocialWork</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-simple</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cainiao.waybill</groupId>
            <artifactId>waybill-bridge-innerSupport</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-simple</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.cainiao.waybill</groupId>
            <artifactId>waybill-bridge-enterprise</artifactId>
        </dependency>

        <dependency>
            <groupId>com.taobao.pandora</groupId>
            <artifactId>taobao-hsf.sar</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-sentinel-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-alimonitor-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-eagleeye-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.pandora</groupId>
            <artifactId>pandora-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.platform.shared</groupId>
            <artifactId>buc.sso.client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>buc-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cainiao</groupId>
            <artifactId>spring-startup-starter</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.taobao.pandora</groupId>
                <artifactId>pandora-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.alibaba.citrus.tool</groupId>
                <artifactId>autoconfig-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>autoconfig</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 解压fat jar到target/${project-name}目录 -->
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <configuration>
                            <tasks>
                                <unzip
                                        src="${project.build.directory}/${project.build.finalName}.${project.packaging}"
                                        dest="${project.build.directory}/waybill-bridge" />
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
