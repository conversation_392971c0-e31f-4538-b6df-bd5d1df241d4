package com.cainiao.waybill.bridge.biz.fast.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/26 12:14
 */
@Data
public class FastShipmentOrderRefundResponse {

    /**
     * id
     */

    private Long id;

    /**
     * 运单号
     */

    private String waybillCode;

    /**
     * 打印机id
     */
    private String printerId;
    /**
     * 运力模式
     */
    private String transportCapacity;

    /**
     * 运力来源
     */
    private String applyType;

    /**
     * 面单所属的CpCode
     */
    private String cpCode;

    /**
     * 网点名称
     */
    private String cpBranchName;
    /**
     * 小件员名称
     */
    private String courierName;

    /**
     * 小件员号码
     */
    private String courierPhone;
    /**
     * 寄件人姓名
     */
    private String sendName;
    /**
     * 寄件人手机号
     */
    private String sendPhone;

    /**
     * 寄件省份
     */
    private String sourceProvince;

    /**
     * 寄件城市
     */
    private String sourceCity;
    /**
     * 收件省份
     */
    private String destinationProvince;

    /**
     * 收件城市
     */
    private String destinationCity;


    /**
     * 下单时间
     */
    private String gmtCreate;

    /**
     * 揽收时间
     */
    private String gotTime;
    /**
     * 支付时间
     */
    private String paidTime;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 结算状态
     */
    private String payStatus;
    /**
     * 重量 kg
     */
    private String weight;

    /**
     * 运费金额
     */
    private Double totalPrice;
    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    private String gmtCreateRefund;

    /**
     * 退款金额
     */
    private Double refundAmount;
    /**
     * 退款原因
     */
    private String refundReason;
    /**
     * 审批状态 待审批、同意、拒绝
     */
    private String approvalStatus;
    /**
     * 退款状态 成功、失败
     */
    private String refundStatus;
    /**
     * 支付时间(退款成功时间)
     */
    private String payTime;

    /**
     * 附件
     */
    private List<String> attachment;

    /**
     * 昵称
     */
    private String loginName;
}
