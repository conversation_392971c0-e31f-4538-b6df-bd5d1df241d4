package com.cainiao.waybill.bridge.common.metaq;

import lombok.Data;

/**
 * 小件员接单后，发送的延时消息的消息实体。
 * 设计该消息实体时考虑的点：
 *     消费者拿到消息时，如何判断是否电联；
 *     判断未电联时，调用物流云接口时的必要参数；
 *     如何找到接单时，向数据库中新增的对应初始状态的语音外呼记录。
 *
 * mailNo、acceptCourierMobile、bizType确定一条语音外呼记录。（tips：DB存在三者一致的不同row也不影响，目的是记录该订单的接单小件员是否被语音提醒）
 * <AUTHOR>
 * @date 2021/9/14-下午8:34
 */
@Data
public class WaybillPickUpAcceptVoiceEvent {

    private String cpCode;

    /**
     * 运单号
     */
    private String mailNo;

    /**
     * 接单小件员手机号
     */
    private String acceptCourierMobile;

    /**
     * 业务类型： 语音外呼可能还有其它应用场景，通过该字段区分。1：淘外电商平台-电联接单小件员30分钟内联系消费者预约上门取件时间
     */
    private Integer bizType;
}
