package com.cainiao.waybill.bridge.enterprise.route.convert;

import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserPhoneNumDO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserPhoneNumDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/14
 **/
public class WaybillBridgeEnterpriseUserPhoneNumConvert {

    /**
     * DTO模型转换成DO模型
     * @param waybillEnterprisePhoneNumDTO DTO模型
     */
    public static WaybillBridgeEnterpriseUserPhoneNumDO convertFromDTO(WaybillEnterpriseUserPhoneNumDTO waybillEnterprisePhoneNumDTO) {
        if (waybillEnterprisePhoneNumDTO == null) {
            return null;
        }
        WaybillBridgeEnterpriseUserPhoneNumDO waybillBridgeEnterpriseUserPhoneNumDO = new WaybillBridgeEnterpriseUserPhoneNumDO();
        BeanUtils.copyProperties(waybillEnterprisePhoneNumDTO,waybillBridgeEnterpriseUserPhoneNumDO);
        return waybillBridgeEnterpriseUserPhoneNumDO;
    }

    /**
     * DO模型转换成DTO模型
     * @param waybillBridgeEnterpriseUserPhoneNumDO DO模型
     */
    public static WaybillEnterpriseUserPhoneNumDTO convertFromDO(WaybillBridgeEnterpriseUserPhoneNumDO waybillBridgeEnterpriseUserPhoneNumDO) {
        if (waybillBridgeEnterpriseUserPhoneNumDO == null) {
            return null;
        }
        WaybillEnterpriseUserPhoneNumDTO waybillEnterpriseUserPhoneNumDTO = new WaybillEnterpriseUserPhoneNumDTO();
        BeanUtils.copyProperties(waybillBridgeEnterpriseUserPhoneNumDO,waybillEnterpriseUserPhoneNumDTO);
        return waybillEnterpriseUserPhoneNumDTO;
    }


    /**
     * DO模型转换成DTO模型
     * @param enterpriseUserPhoneNumDOList DO模型
     */
    public static List<WaybillEnterpriseUserPhoneNumDTO> convertFromDOList(List<WaybillBridgeEnterpriseUserPhoneNumDO> enterpriseUserPhoneNumDOList) {
        List<WaybillEnterpriseUserPhoneNumDTO> waybillEnterpriseUserPhoneNumDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(enterpriseUserPhoneNumDOList)) {
            return waybillEnterpriseUserPhoneNumDTOList;
        }
        enterpriseUserPhoneNumDOList.forEach(waybillBridgeEnterprisePrinterDO -> {
            WaybillEnterpriseUserPhoneNumDTO waybillEnterpriseUserPhoneNumDTO = new WaybillEnterpriseUserPhoneNumDTO();
            BeanUtils.copyProperties(waybillBridgeEnterprisePrinterDO,waybillEnterpriseUserPhoneNumDTO);
            waybillEnterpriseUserPhoneNumDTOList.add(waybillEnterpriseUserPhoneNumDTO);
        });
        return waybillEnterpriseUserPhoneNumDTOList;
    }

}
