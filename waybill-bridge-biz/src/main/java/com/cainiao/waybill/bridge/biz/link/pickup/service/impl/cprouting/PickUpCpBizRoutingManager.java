package com.cainiao.waybill.bridge.biz.link.pickup.service.impl.cprouting;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.cainiao.waybill.bridge.biz.link.pickup.service.impl.check.PickUpCheckAvailableManager;
import com.cainiao.waybill.bridge.biz.link.pickup.service.impl.check.PickUpCheckResult;
import com.cainiao.waybill.bridge.biz.middleware.BridgeRouterSwitch;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.config.PickUpPlatformConfigDiamond;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpAgentEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Cp;
import com.cainiao.waybill.bridge.biz.pickup.dto.CpRouteRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpCpConfigInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.RoutingInfo;
import com.cainiao.waybill.bridge.biz.pickup.routing.dto.RouteBaseRequest;
import com.cainiao.waybill.bridge.biz.pickup.routing.manager.PickUpReachableManager;
import com.cainiao.waybill.bridge.biz.pickup.routing.manager.RoutingManager;
import com.cainiao.waybill.bridge.biz.pickup.service.PickUpAlgorithmService;
import com.cainiao.waybill.bridge.biz.router.DTO.BridgeRouterRequest;
import com.cainiao.waybill.bridge.biz.router.DTO.BridgeRouterResult;
import com.cainiao.waybill.bridge.biz.router.DTO.CpInfo;
import com.cainiao.waybill.bridge.biz.router.core.BridgeRouterCoreService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.wrapper.IGraphServiceWrapper;
import com.cainiao.waybill.bridge.biz.wrapper.TairManagerWrapper;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.model.domain.PickUpAlgorithmSchemeDO;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpCpBizTypeRoutingManager
 * @Description
 * @Date 2023/8/9 11:35 上午
 * @Version 1.0
 */
@Component
public class PickUpCpBizRoutingManager {

    @Resource
    private PickUpReachableManager pickUpReachableManager;

    @Resource
    private RoutingManager routingManager;

    @Resource
    private BridgeRouterCoreService routerCoreService;

    @Resource
    private PickUpCheckAvailableManager pickUpCheckAvailableManager;

    @Resource
    private WaybillPickUpCpRoutingCalculationHelper routingCalculationHelper;

    @Resource
    private PickUpAlgorithmService pickUpAlgorithmService;

    @Resource
    private IGraphServiceWrapper iGraphServiceWrapper;

    @Resource
    private TairManagerWrapper tairManagerWrapper;

    public PickUpCpBizRoutingContext routing(CpRouteRequest cpRouteRequest) throws BridgeBaseException {
        PickUpCpBizRoutingContext context = new PickUpCpBizRoutingContext();
        try {
            PickUpCheckResult pickUpCheckResult = pickUpCheckAvailableManager.checkAvailable(cpRouteRequest, false);
            PickUpLogUtil.info("support_cp_result, request:{}, result:{}", JSON.toJSONString(cpRouteRequest), JSON.toJSONString(pickUpCheckResult));
            if (Boolean.FALSE.equals(pickUpCheckResult.getAvailable()) || CollectionUtils.isEmpty(
                pickUpCheckResult.getAvailableCpList())) {
                throw new BridgeBusinessException("order_channel_cp_not_available",
                    pickUpCheckResult.getUnavailableReason());
            }

            List<PickUpCpConfigInfo> availableCpList = pickUpCheckResult.getAvailableCpList();


            context.getCpReachResult().putAll(pickUpCheckResult.getRouteReachableResult());

            // 预处理，获取圆通网点代码
            String senderYtoBranchCode = getBranchCode(Cp.YTO.name(), cpRouteRequest.getSendAddress());
            cpRouteRequest.setOrgCode(senderYtoBranchCode);
            cpRouteRequest.setCpCodeForOrgCode(Cp.YTO.name());

            PickUpCpConfigInfo pickUpCpConfigInfo;

            // 规则路由
            BridgeRouterResult routerResult = configRouter(cpRouteRequest, senderYtoBranchCode, availableCpList);
            PickUpLogUtil.info("router_result, request:{}, result:{}", JSON.toJSONString(cpRouteRequest), JSON.toJSONString(routerResult));

            if (routerResult != null && StringUtils.isNotBlank(routerResult.getRouterStrategyId())) {
                CpInfo cpInfo = ListUtil.stream(routerResult.getAvailableCpList()).findFirst().orElse(null);
                PickUpCpConfigInfo configInfo = findByCpInfo(availableCpList, cpInfo);
                if (configInfo == null) {
                    if(BridgeSwitch.asyncCreateOrderFromAppKey.contains(cpRouteRequest.getFromAppKey())){
                        // 第一优先级CP不可达时使用备份可达CP
                        configInfo = availableCpList.get(0);
                        String originCp = null;
                        if(cpInfo != null){
                            originCp = cpInfo.getCpCode() + "-" + cpInfo.getAgent();
                        }
                        String backUpCp = configInfo.getCpCode() + "-" + configInfo.getAgent();
                        PickUpLogUtil.info(cpRouteRequest.getOuterOrderCode() + "|默认CP不可达使用兜底CP|原始CP|" + originCp + "|兜底CP|" + backUpCp);
                    } else {
                        return context;
                    }
                }

                context.setCpInfo(configInfo);
                context.setRouterStrategyId(routerResult.getRouterStrategyId());
                context.setBackUpCpList(availableCpList);
                context.getBackUpCpList().remove(configInfo);

                // 未命中规则路由时，按照之前的路由逻辑
            } else {

                // 新算法生效则优先使用 如不生效使用以前逻辑兜底
                if(BridgeSwitch.OPEN_ALGORITHM_CALC_V2 && checkAlgorithmAvailable(cpRouteRequest)){
                    return routingByAlgorithmScheme(context, availableCpList, cpRouteRequest);
                }

                // 不启用算法分单的客户 优先取第一个可用CP
                if(!BridgeRouterSwitch.calculateCpRouteFromAppKeyList.contains(cpRouteRequest.getFromAppKey())){
                    context.setCpInfo(availableCpList.get(0));
                    availableCpList.remove(0);
                    context.setBackUpCpList(availableCpList);
                    return context;
                }

                // 启用算法分单的客户 判断是否命中算法自动分单
                Boolean matchAutoOrderFlag = matchAutoOrderGray(cpRouteRequest.getOrderChannels());
                String cpCode;
                String autoCpInfo = null;
                String realCpInfo = null;
                // 命中算法自动分单
                if(matchAutoOrderFlag){
                    String searchKey ;
                    if(!BridgeSwitch.OPEN_NEW_IGRAPH_VERSION){
                        // 老版本：线路-渠道/线路-版本
                        // 不配置时默认为渠道编码作为版本号
                        String searkeyVersion = BridgeSwitch.IGRAPH_PG_SEARCH_KEY_MAP.get(cpRouteRequest.getOrderChannels());
                        if(StringUtils.isBlank(searkeyVersion)){
                            searkeyVersion = cpRouteRequest.getOrderChannels();
                        }
                        searchKey = cpRouteRequest.getSendAddress().getProvinceName() + "-" +
                            cpRouteRequest.getConsignAddress().getProvinceName() + "-" +
                            searkeyVersion;
                    }else {
                        // 新版本：线路-渠道-版本v2   对于同一个客户来说，渠道和产品一一对应
                        searchKey = cpRouteRequest.getSendAddress().getProvinceName() + "-" +
                            cpRouteRequest.getConsignAddress().getProvinceName() + "-" +
                            cpRouteRequest.getOrderChannels() + "-" +
                            BridgeSwitch.CURRENT_IGRAPH_KEY_VERSION;
                    }
                    Map<String, Double> cpRateMap = iGraphServiceWrapper.searchRouterOrderRate(searchKey);

                    // 根据算法结果解析CP运力配置
                    CpInfo cpInfo = calculateCpInfo(searchKey, cpRouteRequest.getFromAppKey(), cpRouteRequest.getOrderChannels(), cpRateMap);
                    if(null == cpInfo){
                        cpCode = null;
                    }else{
                        cpCode = cpInfo.getCpCode();
                        autoCpInfo = cpInfo.getCpCode()+"-"+cpInfo.getAgent();
                    }
                    pickUpCpConfigInfo = findByCpInfo(availableCpList, cpInfo);

                }else {
                    List<String> cpCodeList = ListUtil.stream(availableCpList).map(
                        PickUpCpConfigInfo::getCpCode).collect(Collectors.toList());
                    // 按优先级计算路由cp
                    cpCode = routingCalculationHelper.calculation(cpRouteRequest, cpCodeList);
                    pickUpCpConfigInfo = ListUtil.stream(availableCpList)
                        .filter(x -> StringUtils.equals(cpCode, x.getCpCode()))
                        .filter(x -> PickUpAgentEnum.OPEN_CP.getAgent().equals(x.getAgent()))
                        .findAny().orElse(null);
                }

                if(pickUpCpConfigInfo == null){
                    // 第一优先级CP不可达时使用备份可达CP
                    pickUpCpConfigInfo = availableCpList.get(0);
                    String originCp = null;
                    if(StringUtils.isNotBlank(cpCode)){
                        originCp = cpCode + "-" + PickUpAgentEnum.OPEN_CP.getAgent();
                    }
                    String backUpCp = pickUpCpConfigInfo.getCpCode() + "-" + pickUpCpConfigInfo.getAgent();
                    PickUpLogUtil.info(cpRouteRequest.getOuterOrderCode() + "|默认CP不可达使用兜底CP|原始CP|" + originCp + "|兜底CP|" + backUpCp);
                }
                realCpInfo = pickUpCpConfigInfo.getCpCode() + "-" + pickUpCpConfigInfo.getAgent();
                // 算法分单是否生效
                boolean isEfficient = StringUtils.equals(autoCpInfo, realCpInfo);
                PickUpLogUtil.info(cpRouteRequest.getOuterOrderCode() + "|算法自动分单=" + matchAutoOrderFlag + "|算法分单生效=" + isEfficient + "|算法决策CP=" + autoCpInfo +"|实际CP=" + realCpInfo + "|渠道=" + cpRouteRequest.getOrderChannels()+"|指定算法版本");
                availableCpList.remove(pickUpCpConfigInfo);
                context.setCpInfo(pickUpCpConfigInfo);
                context.setBackUpCpList(availableCpList);
            }

            return context;
        }catch (Exception e){
            PickUpLogUtil.errLog(cpRouteRequest.getOuterOrderCode(),
                PickUpConstants.Action.ORDER_CREATE_ROUTING_EXCEPTION.name(),"ORDER_CREATE_ROUTING_EXCEPTION","路由分单异常",
                ExceptionUtils.getFullStackTrace(e));
            return context;
        }

    }

    /**
     * 校验新的算法是否生效
     * @param cpRouteRequest
     * @return
     */
    private boolean checkAlgorithmAvailable(CpRouteRequest cpRouteRequest) {
        PickUpAlgorithmSchemeDO schemeDO = pickUpAlgorithmService.queryAvailableAlgorithmScheme(cpRouteRequest.getOrderChannels());
        return null != schemeDO;
    }

    /**
     * 算法方案分单计算
     * @param context
     * @param availableCpList
     * @param cpRouteRequest
     * @return
     */
    private PickUpCpBizRoutingContext routingByAlgorithmScheme(PickUpCpBizRoutingContext context, List<PickUpCpConfigInfo> availableCpList, CpRouteRequest cpRouteRequest) {
        PickUpCpConfigInfo pickUpCpConfigInfo;
        // 查询生效的算法方案版本号
        String version = pickUpAlgorithmService.queryAvailableAlgorithmSchemeVersion(cpRouteRequest.getOrderChannels());

        // 查询生效的算法方案测算key
        String calcKey = pickUpAlgorithmService.queryAvailableAlgorithmSchemeCalcKey(cpRouteRequest.getOrderChannels());
        if(StringUtils.isAnyBlank(version, calcKey)){
            return context;
        }
        // 根据key和线路信息查询分单比 key: 省份-省份-渠道-测算key-版本号
        String searchKey = cpRouteRequest.getSendAddress().getProvinceName() + "-" +
            cpRouteRequest.getConsignAddress().getProvinceName() + "-" +
            cpRouteRequest.getOrderChannels() + "-" +
            calcKey + "-" +
            version;
        Map<String, Double> cpRateMap = iGraphServiceWrapper.searchRouterOrderRateByAlgorithm(searchKey);
        // 根据算法结果解析CP运力配置
        CpInfo cpInfo = calculateCpInfo(searchKey, cpRouteRequest.getFromAppKey(), cpRouteRequest.getOrderChannels(), cpRateMap);
        String cpCode;
        String autoCpInfo = null;
        String realCpInfo = null;

        if(null == cpInfo){
            cpCode = null;
        }else{
            cpCode = cpInfo.getCpCode();
            autoCpInfo = cpInfo.getCpCode()+"-"+cpInfo.getAgent()+"-"+cpInfo.getBizType();
        }
        pickUpCpConfigInfo = findByCpInfo(availableCpList, cpInfo);
        if(pickUpCpConfigInfo == null){
            // 第一优先级CP不可达时使用备份可达CP
            pickUpCpConfigInfo = availableCpList.get(0);
            String originCp = null;
            if(StringUtils.isNotBlank(cpCode)){
                originCp = cpCode + "-" + PickUpAgentEnum.OPEN_CP.getAgent();
            }
            String backUpCp = pickUpCpConfigInfo.getCpCode() + "-" + pickUpCpConfigInfo.getAgent();
            PickUpLogUtil.info(cpRouteRequest.getOuterOrderCode() + "|默认CP不可达使用兜底CP|原始CP|" + originCp + "|兜底CP|" + backUpCp);
        }
        if(null == pickUpCpConfigInfo.getBizType()){
            // 兜底为逆向
            pickUpCpConfigInfo.setBizType(1);
        }
        realCpInfo = pickUpCpConfigInfo.getCpCode() + "-" + pickUpCpConfigInfo.getAgent()+ "-" + pickUpCpConfigInfo.getBizType();
        // 算法分单是否生效
        boolean isEfficient = StringUtils.equals(autoCpInfo, realCpInfo);
        PickUpLogUtil.info(cpRouteRequest.getOuterOrderCode() + "|算法自动分单=true|算法分单生效=" + isEfficient + "|算法决策CP=" + autoCpInfo +"|实际CP=" + realCpInfo + "|渠道=" + cpRouteRequest.getOrderChannels()+"|算法方案版本");
        availableCpList.remove(pickUpCpConfigInfo);
        context.setCpInfo(pickUpCpConfigInfo);
        context.setBackUpCpList(availableCpList);
        return context;
    }

    /**
     * 按渠道取配置 是否命中算法自动分单灰度
     * @param orderChannels
     * @return
     */
    private boolean matchAutoOrderGray(String orderChannels) {
        Double rate = BridgeSwitch.ALGORITHM_AUTO_ORDER_MAP.get(orderChannels);
        if(null == rate){
            // 未配置时不分流量
            return false;
        }
        // 启用并命中算法自动分单
        return  ThreadLocalRandom.current().nextDouble() <= rate;
    }

    /**
     * 根据算法结果解析CP运力配置
     * 按各CP的权重随机分单
     * @param searchKey
     * @param fromAppKy
     * @param cpRateMap
     * @return
     */
    private CpInfo calculateCpInfo(String searchKey, String fromAppKy,String orderChannels, Map<String, Double> cpRateMap) {
        // 按权重从小到大排序
        Map<String, Double> sortedCpRateMap = cpRateMap.entrySet()
            .stream()
            .sorted(Map.Entry.comparingByValue())
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                (e1, e2) -> e1,
                LinkedHashMap::new));
        Set<String> keys = sortedCpRateMap.keySet();
        double current = ThreadLocalRandom.current().nextDouble();
        //  YTO-OPEN: 0.01
        //  YTO-WHG: 0.05
        //  GUOGUO-OPEN: 0.94
        Double tempValue = 0d;
        for(String key: keys){
            if(key.split("-").length == 2 &&  current <= sortedCpRateMap.get(key) + tempValue){
                CpInfo cpInfo = new CpInfo();
                String cpCode = key.split("-")[0];
                String agent = key.split("-")[1];
                cpInfo.setCpCode(cpCode);
                cpInfo.setAgent(agent);
                Integer bizType = PickUpPlatformConfigDiamond.getCpBizType(fromAppKy, cpCode, agent);
                if(null == bizType){
                    bizType = PickUpPlatformConfigDiamond.getCpBizType(fromAppKy, orderChannels, cpCode, agent);
                }
                cpInfo.setBizType(bizType);
                PickUpLogUtil.info("算法决策命中.searchKey:{},命中运力CP:{},cpRateMap:{}", searchKey, cpCode+"-"+agent, cpRateMap);
                return cpInfo;
            }else if(key.split("-").length == 3 &&  current <= sortedCpRateMap.get(key) + tempValue){
                //  YTO-OPEN-1: 0.01
                CpInfo cpInfo = new CpInfo();
                String cpCode = key.split("-")[0];
                String agent = key.split("-")[1];
                String cpBizType = key.split("-")[2];
                cpInfo.setCpCode(cpCode);
                cpInfo.setAgent(agent);
                cpInfo.setBizType(Integer.parseInt(cpBizType));
                PickUpLogUtil.info("算法决策命中.searchKey:{},命中运力CP业务类型:{},cpRateMap:{}", searchKey, cpCode+"-"+agent+"-"+cpBizType, cpRateMap);
                return cpInfo;
            }
            tempValue += sortedCpRateMap.get(key);
        }
        return null;
    }

    private PickUpCpConfigInfo findByCpInfo(List<PickUpCpConfigInfo> configInfoList, CpInfo cpInfo) {
        if (cpInfo == null) {
            return null;
        }
        for (PickUpCpConfigInfo pickUpCpConfigInfo : ListUtil.non(configInfoList)) {
            if (StringUtils.equals(pickUpCpConfigInfo.getAgent(), cpInfo.getAgent())
                && StringUtils.equals(pickUpCpConfigInfo.getCpCode(), cpInfo.getCpCode())) {
                if(null == pickUpCpConfigInfo.getBizType()){
                    return pickUpCpConfigInfo;
                }else if(Objects.equals(pickUpCpConfigInfo.getBizType(), cpInfo.getBizType())){
                    return pickUpCpConfigInfo;
                }
            }
        }
        return null;
    }

    private BridgeRouterResult configRouter(CpRouteRequest cpRouteRequest, String senderYtoBranchCode,
        List<PickUpCpConfigInfo> list) {
        BridgeRouterRequest routerRequest = BridgeRouterRequest.builder()
            .availableCpList(buildRouterAvailableCpList(list))
            .bizType(cpRouteRequest.getBizType())
            .fromAppKey(cpRouteRequest.getFromAppKey())
            .sendName(cpRouteRequest.getSendName())
            .sendMobile(cpRouteRequest.getSendMobile())
            .sendPhone(cpRouteRequest.getSendPhone())
            .sendAddress(cpRouteRequest.getSendAddress())
            .consignName(cpRouteRequest.getConsignName())
            .consignMobile(cpRouteRequest.getConsignMobile())
            .consignPhone(cpRouteRequest.getConsignPhone())
            .consignAddress(cpRouteRequest.getConsignAddress())
            .appointGotStartTime(cpRouteRequest.getAppointGotStartTime())
            .appointGotEndTime(cpRouteRequest.getAppointGotEndTime())
            .sendMobile(cpRouteRequest.getSendMobile())
            .resCode(cpRouteRequest.getResCode())
            .orgCode(senderYtoBranchCode)
            .outerOrderCode(cpRouteRequest.getOuterOrderCode())
            .build();

        BridgeRouterResult routerResult = new BridgeRouterResult();

        try {
            routerResult = routerCoreService.router(routerRequest);
        } catch (Throwable throwable) {
            PickUpLogUtil.errLog("", "router_exception", "router_exception",
                "路由异常: " + JSON.toJSONString(cpRouteRequest), throwable);
        }
        return routerResult;
    }

    /**
     * 获取接单的网点code
     */
    private String getBranchCode(String cpCode, AddressDTO senderAddress) {
        // 裹裹无法获取网点
        if (Cp.GUOGUO.name().equals(cpCode)) {
            return null;
        }
        //  调用分拣码接口，获取接单网点
        RouteBaseRequest routeBaseRequest = new RouteBaseRequest();
        routeBaseRequest.setCpCode(cpCode);
        //  都用寄件地址去算
        routeBaseRequest.setReceiveAddress(senderAddress);
        routeBaseRequest.setSendAddress(senderAddress);

        RoutingInfo routingInfo = null;
        try {
            routingInfo = routingManager.calculateRoutingInfo(routeBaseRequest);
        } catch (Exception exception) {
            return null;
        }
        return routingInfo.getReceiveBranchCode();
    }

    private List<CpInfo> buildRouterAvailableCpList(List<PickUpCpConfigInfo> cpConfigInfoList) {
        List<CpInfo> list = new ArrayList<>();
        for (PickUpCpConfigInfo pickUpCpConfigInfo : ListUtil.non(cpConfigInfoList)) {
            CpInfo cpInfo = new CpInfo();
            cpInfo.setCpCode(pickUpCpConfigInfo.getCpCode());
            cpInfo.setAgent(pickUpCpConfigInfo.getAgent());
            cpInfo.setBizType(pickUpCpConfigInfo.getBizType());
            list.add(cpInfo);
        }
        return list;
    }

}
