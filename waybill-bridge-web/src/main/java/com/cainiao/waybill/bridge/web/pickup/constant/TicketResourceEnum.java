package com.cainiao.waybill.bridge.web.pickup.constant;

import java.util.List;

import com.cainiao.waybill.bridge.biz.ticket.dto.TicketRoleEnum;
import com.google.common.collect.Lists;
import lombok.Getter;

/**
 * <AUTHOR> zouping.fzp
 * @Classname TicketResourceEnum
 * @Description
 * @Date 2022/11/29 4:18 下午
 * @Version 1.0
 */
public enum TicketResourceEnum {

    /**
     * 管理员
     */
    CN_TICKET_MANAGER(1, "CN_TICKET_MANAGER"),
    /**
     * 菜鸟客服
     */
    CP_TICKET_MANAGER(2, "CP_TICKET_MANAGER"),
    /**
     * 服务商客服
     */
    PLATFORM_MANAGER(3, "PLATFORM_MANAGER"),
    /**
     * 订单管理
     */
    ORDER_MANAGER(4, "ORDER_MANAGER"),
    /**
     * 运力订单管理
     */
    CAPACITY_ORDER_MANAGER(5,"CAPACITY_ORDER_MANAGER"),
    /**
     * 订单路由
     */
    ORDER_ROUTER(6,"ORDER_ROUTER"),

    PLATFORM_ORDER(7, "PLATFORM_ORDER"),

    // 物流详情账单
    LOGISTIC_FBI(8, "LOGISTIC_FBI"),

    /**
     * 算法方案
     */
    ALGORITHM_MANAGER(9, "ALGORITHM_MANAGER"),

    /**
     * 报价管理
     */
    QUOTE_MANAGER(10, "QUOTE_MANAGER"),

    /**
     * 账单管理
     */
    BILL_MANAGER(11, "BILL_MANAGER"),

    ;

    @Getter
    private final Integer resourceId;

    @Getter
    private final String resourceName;

    TicketResourceEnum(Integer resourceId, String resourceName) {
        this.resourceId = resourceId;
        this.resourceName = resourceName;
    }

    public static List<TicketResourceEnum> roleResourceList(TicketRoleEnum roleEnum) {
        if (TicketRoleEnum.admin == roleEnum) {
            return Lists.newArrayList(TicketResourceEnum.values());
        }
        if (TicketRoleEnum.cnCs == roleEnum) {
            return Lists.newArrayList(CN_TICKET_MANAGER, ORDER_MANAGER, CAPACITY_ORDER_MANAGER);
        }
        if (TicketRoleEnum.cpCs == roleEnum) {
            return Lists.newArrayList(TicketResourceEnum.CP_TICKET_MANAGER);
        }
        if (TicketRoleEnum.platCs == roleEnum) {
            return Lists.newArrayList(TicketResourceEnum.PLATFORM_MANAGER, PLATFORM_ORDER, LOGISTIC_FBI);
        }
        return Lists.newArrayList();
    }
}
