package com.cainiao.waybill.bridge.innersupport.ad.config;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;

import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.annotation.NameSpace;
import com.taobao.csp.switchcenter.bean.Switch.Level;

/**
 * <AUTHOR>
 * @date 2025/2/24 16:53
 **/
@SwitchGroup
@NameSpace(nameSpace = "bridgeInnerSwitch")
public class BridgeInnerSwitch {

    @AppSwitch(des = "阿里云账号ak", level = Level.p3)
    public static String CLOUD_ACCOUNT_ACCESS_KEY = "";


    @AppSwitch(des = "广告工单页面下线", level = Level.p3)
    public static boolean AD_WORK_ORDER_OFFLINE = true;

}
