package com.cainiao.waybill.bridge.enterprise.common.enums;

/**
 * <AUTHOR>
 * @date 2025/4/18
 **/
public enum EnterpriseUserStatusEnum {

    ENABLE(0, "启用"),

    DISABLE(1, "禁用");

    private Integer status;
    private String desc;

    public Integer getStatus(){
        return status;
    }

    public String getDesc(){
        return desc;
    }

    EnterpriseUserStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
