package com.cainiao.waybill.bridge.common.waybill.pickup.service;

import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;

/**
 * 物流生命周期事件，对外推送service
 *
 * <AUTHOR>
 * @date 2021-06-16
 */
public interface PickUpEventOuterDispatcher {

    /**
     * 对外推送推送，此方法此简单调用下发方法进行推送，不涉及限流、重试等逻辑
     * 单纯封装推送的行为，使上层的逻辑不用感知怎么推送给外部的。
     *
     * @param pickUpEvent 生命周期事件
     * @return 是否推送成功
     */
    BaseResultDTO<Void> dispatch(WaybillPickUpEvent pickUpEvent);
}
