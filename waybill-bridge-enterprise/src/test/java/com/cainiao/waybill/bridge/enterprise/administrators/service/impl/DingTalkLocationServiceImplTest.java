package com.cainiao.waybill.bridge.enterprise.administrators.service.impl;

import com.cainiao.waybill.bridge.enterprise.administrators.request.LocationRequest;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseUserTypeEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePostDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePrinterDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoDO;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseLocationDO;
import com.cainiao.waybill.bridge.model.dto.AddressInfo;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseLocationDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePostMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePrinterMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillEnterpriseLocationMapper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.testng.collections.Lists;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DingTalkLocationServiceImplTest {
    @InjectMocks
    private DingTalkLocationServiceImpl dingTalkLocationService;
    @Mock
    private WaybillBridgeEnterprisePostMapper enterprisePostMapper;
    @Mock
    private WaybillEnterpriseLocationMapper enterpriseLocationMapper;
    @Mock
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;
    @Mock
    private WaybillBridgeEnterprisePrinterMapper enterprisePrinterMapper;

    // 测试用例：id为null时抛出参数错误异常
    @Test(expected = EnterpriseException.class)
    public void testDeleteLocationByIdIdIsNull() {
        // 调用方法并期望抛出EnterpriseException异常
        dingTalkLocationService.deleteLocationById(null);
    }

    // 测试用例：场地不存在时，抛出场地不存在异常
    @Test(expected = EnterpriseException.class)
    public void testDeleteLocationByIdLocationNotExists() {
        Long id = 1L;
        // 模拟场地不存在的场景
        when(enterpriseLocationMapper.selectByPrimaryKey(id)).thenReturn(null);
        // 调用方法并期望抛出EnterpriseException异常
        dingTalkLocationService.deleteLocationById(id);
    }

    // 测试用例：成功删除场地及其关联信息
    @Test
    public void testDeleteLocationByIdSuccess() {
        Long id = 1L;
        WaybillEnterpriseLocationDO locationDO = mock(WaybillEnterpriseLocationDO.class);
        // 设置必要的mock返回值，避免空指针异常
        when(locationDO.getCorpId()).thenReturn("testCorpId");
        when(locationDO.getLocationName()).thenReturn("testLocationName");
        // 模拟场地存在
        when(enterpriseLocationMapper.selectByPrimaryKey(id)).thenReturn(locationDO);
        // 模拟关联小邮局
        when(enterprisePostMapper.selectByParam(any())).thenReturn(Collections.emptyList());
        // 模拟关联打印机
        when(enterprisePrinterMapper.selectByParam(any())).thenReturn(Collections.emptyList());
        // 模拟关联用户
        when(enterpriseUserInfoMapper.selectByParam(any())).thenReturn(Collections.emptyList());
        // 模拟删除位置成功
        when(enterpriseLocationMapper.deleteByPrimaryKey(id)).thenReturn(1);
        // 调用方法
        int result = dingTalkLocationService.deleteLocationById(id);
        // 验证删除操作成功
        assertEquals(1, result);
        // 验证已调用相关的Mapper方法
        verify(enterpriseLocationMapper).selectByPrimaryKey(id);
        verify(enterpriseLocationMapper).deleteByPrimaryKey(id);
        // verify(enterprisePostMapper).selectByParam(any());
        // verify(enterprisePrinterMapper).selectByParam(any());
        // verify(enterpriseUserInfoMapper).selectByParam(any());
    }

    /**
     * 测试：使用enterpriseLocationMapper根据param查询对象
     * 场景：查询对象存在
     */
    @Test
    public void testGetLocationByLocationIdValidData() {
        // 创建mock的DO对象
        WaybillEnterpriseLocationDO mockDO = mock(WaybillEnterpriseLocationDO.class);

        // 模拟enterpriseLocationMapper返回值
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(mockDO);
        // 调用被测试方法
        WaybillEnterpriseLocationDTO resultDTO = dingTalkLocationService.getLocationByLocationId("locationId", "corpId");
        // 验证调用结果
        assertNotNull(resultDTO); // 验证转换后的DTO对象非空
        // verify(mockDO, times(1)).getCorpId(); // 不验证field级别方法
    }

    /**
     * 测试：使用enterpriseLocationMapper根据param查询对象
     * 场景：查询对象不存在
     */
    @Test
    public void testGetLocationByLocationIdNoData() {
        // 模拟enterpriseLocationMapper返回值为null
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(null);
        // 调用被测试方法
        WaybillEnterpriseLocationDTO resultDTO = dingTalkLocationService.getLocationByLocationId("locationId", "corpId");
        // 验证调用结果
        assertNull(resultDTO); // 验证转换后的DTO对象为空
    }

    // 测试用例：检查指定locationId是否存在，不存在则抛出异常
    @Test(expected = EnterpriseException.class)
    public void testDeleteByLocationIdLocationNotExists() {
        String locationId = "loc123";
        String corpId = "corp123";
        // 模拟场地不存在的场景
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(null);
        // 调用方法并期望抛出EnterpriseException异常
        dingTalkLocationService.deleteByLocationId(locationId, corpId);
    }

    // 测试用例：删除关联的小邮局相关数据
    @Test
    public void testDeleteByLocationIdDeleteRelatedPost() {
        String locationId = "loc123";
        String corpId = "corp123";
        WaybillEnterpriseLocationDO locationDO = mock(WaybillEnterpriseLocationDO.class);
        when(locationDO.getLocationName()).thenReturn("Test Location");
        when(locationDO.getCorpId()).thenReturn("Test Corp ID");
        when(enterpriseLocationMapper.selectByPrimaryKey(id)).thenReturn(locationDO);
        
        // 创建真实的List对象，避免空指针异常
        List<WaybillBridgeEnterprisePostDO> postList = Collections.singletonList(new WaybillBridgeEnterprisePostDO());
        postList.get(0).setId(1L);
        when(enterprisePostMapper.selectByParam(any())).thenReturn(postList);
        when(locationDO.getId()).thenReturn(1L);
        when(locationDO.getCorpId()).thenReturn(corpId);
        // 模拟场地存在
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(locationDO);
        // 模拟选择关联的小邮局
        when(enterprisePostMapper.selectByParam(any())).thenReturn(Collections.singletonList(mock(WaybillBridgeEnterprisePostDO.class)));
        // 模拟删除邮局方法
        when(enterprisePostMapper.deleteByPrimaryKey(anyLong())).thenReturn(1);
        
        List<WaybillBridgeEnterprisePrinterDO> printerList = Collections.singletonList(new WaybillBridgeEnterprisePrinterDO());
        printerList.get(0).setId(1L);
        when(enterprisePrinterMapper.selectByParam(any())).thenReturn(printerList);
        // 呼叫方法
        dingTalkLocationService.deleteByLocationId(locationId, corpId);
        // 验证删除邮局已被调用
        verify(enterprisePostMapper).deleteByPrimaryKey(anyLong());
    }

    // 测试用例：删除关联的打印机相关数据
    @Test
    public void testDeleteByLocationIdDeleteRelatedPrinter() {
        String locationId = "loc123";
        String corpId = "corp123";
        WaybillEnterpriseLocationDO locationDO = mock(WaybillEnterpriseLocationDO.class);
        when(locationDO.getCorpId()).thenReturn(corpId);
        when(locationDO.getLocationName()).thenReturn("Sample Location");
        // 模拟场地存在
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(locationDO);
        // 模拟选择关联的打印机
        when(enterprisePrinterMapper.selectByParam(any())).thenReturn(Collections.singletonList(mock(WaybillBridgeEnterprisePrinterDO.class)));
        // 模拟删除打印机方法
        when(enterprisePrinterMapper.deleteByPrimaryKey(anyLong())).thenReturn(1);
        
        when(enterpriseUserInfoMapper.selectByParam(any())).thenReturn(Collections.emptyList());
        when(enterpriseLocationMapper.deleteByPrimaryKey(id)).thenReturn(1);
        int result = dingTalkLocationService.deleteLocationById(id);
        // 呼叫方法
        dingTalkLocationService.deleteByLocationId(locationId, corpId);
        // 验证删除打印机已被调用
        verify(enterprisePrinterMapper).deleteByPrimaryKey(anyLong());
    }

    // 测试用例：更新关联的用户信息，将小邮局管理员置为员工
    @Test
    public void testDeleteByLocationIdUpdateUserInfo() {
        String locationId = "loc123";
        String corpId = "corp123";
        WaybillEnterpriseLocationDO locationDO = mock(WaybillEnterpriseLocationDO.class);
        when(locationDO.getId()).thenReturn(1L);
        when(locationDO.getCorpId()).thenReturn(corpId);
        // 模拟场地存在
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(locationDO);
        // 模拟选择关联的用户
        WaybillBridgeEnterpriseUserInfoDO userInfoDO = mock(WaybillBridgeEnterpriseUserInfoDO.class);
        when(userInfoDO.getUserType()).thenReturn(EnterpriseUserTypeEnum.POST_ADMIN.name());
        when(enterpriseUserInfoMapper.selectByParam(any())).thenReturn(Collections.singletonList(userInfoDO));
        // 模拟更新用户信息方法
        when(enterpriseUserInfoMapper.updateByPrimaryKey(any(WaybillBridgeEnterpriseUserInfoDO.class))).thenReturn(1);
        // 呼叫方法
        dingTalkLocationService.deleteByLocationId(locationId, corpId);
        // 验证更新用户信息已被调用
        verify(enterpriseUserInfoMapper).updateByPrimaryKey(any(WaybillBridgeEnterpriseUserInfoDO.class));
    }

    // 测试用例：删除指定locationId的数据记录
    @Test
    public void testDeleteByLocationIdDeleteLocationRecord() {
        String locationId = "loc123";
        String corpId = "corp123";
        WaybillEnterpriseLocationDO locationDO = mock(WaybillEnterpriseLocationDO.class);
        when(locationDO.getId()).thenReturn(1L);
        when(locationDO.getCorpId()).thenReturn(corpId);
        // 模拟场地存在
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(locationDO);
        // 模拟删除位置成功
        when(enterpriseLocationMapper.deleteByPrimaryKey(anyLong())).thenReturn(1);
        // 呼叫方法
        int result = dingTalkLocationService.deleteByLocationId(locationId, corpId);
        // 验证删除操作成功
        assertEquals(1, result);
        // 验证删除位置已被调用
        verify(enterpriseLocationMapper).deleteByPrimaryKey(anyLong());
    }

    @Test
    public void testDeleteByLocationId() {
        String locationId = "loc123";
        String corpId = "corp123";

        WaybillEnterpriseLocationDO locationDO = new WaybillEnterpriseLocationDO();
        locationDO.setId(1L);
        locationDO.setCorpId(corpId);
        locationDO.setLocationId(locationId);
        locationDO.setLocationName("LocationName");
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(locationDO);

        WaybillBridgeEnterprisePostDO postDO = new WaybillBridgeEnterprisePostDO();
        postDO.setId(1L);
        when(enterprisePostMapper.selectByParam(any())).thenReturn(Lists.newArrayList(postDO));
        when(enterprisePostMapper.deleteByPrimaryKey(anyLong())).thenReturn(1);

        WaybillBridgeEnterprisePrinterDO printerDO = new WaybillBridgeEnterprisePrinterDO();
        postDO.setId(1L);
        when(enterprisePrinterMapper.selectByParam(any())).thenReturn(Lists.newArrayList(printerDO));
        when(enterprisePrinterMapper.deleteByPrimaryKey(anyLong())).thenReturn(1);

        WaybillBridgeEnterpriseUserInfoDO userInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        userInfoDO.setId(1L);
        userInfoDO.setUserType(EnterpriseUserTypeEnum.POST_ADMIN.name());
        when(enterpriseUserInfoMapper.selectByParam(any())).thenReturn(Lists.newArrayList(userInfoDO));
        when(enterpriseUserInfoMapper.updateByPrimaryKey(any(WaybillBridgeEnterpriseUserInfoDO.class))).thenReturn(1);

        when(enterpriseLocationMapper.deleteByPrimaryKey(anyLong())).thenReturn(1);
        int delete = dingTalkLocationService.deleteByLocationId(locationId, corpId);
        assertEquals(1, delete);
    }

    @Test
    public void testAddLocationSuccess() {
        LocationRequest request = new LocationRequest();
        request.setCorpId("corpId");
        request.setLocationName("locationName");
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(new WaybillEnterpriseLocationDO());

        AddressInfo addressInfo = new AddressInfo();
        addressInfo.setProvince("province");
        when(enterpriseLocationMapper.insert(any())).thenReturn(1);
        String locationId = dingTalkLocationService.addLocation(request);
        Assert.assertNotNull(locationId);
    }
}