package com.cainiao.waybill.bridge.ticket.api;

import com.cainiao.waybill.bridge.ticket.api.request.WaybillTicketCreateRequest;
import com.cainiao.waybill.bridge.ticket.api.request.WaybillTicketHurryRequest;
import com.cainiao.waybill.bridge.ticket.api.request.WaybillTicketReplyRequest;
import com.cainiao.waybill.bridge.ticket.api.response.WaybillTicketResponse;

import java.util.List;

/**
 * 末端工单对接 菜鸟内部服务实现
 * <AUTHOR>
 */
public interface WaybillEndTicketService {

    /**
     * 创建工单 将创建信息保存在数据库
     * @param createRequest
     * @return 结果内容说明，成功时为空字符串
     */
    String saveCreateTicket(WaybillTicketCreateRequest createRequest);

    /**
     * 更新工单 将回复信息保存在数据库
     * @param replyRequest
     * @return 结果内容说明，成功时为空字符串
     */
    String saveReplyTicket(WaybillTicketReplyRequest replyRequest);

    /**
     * 催单 将催单信息保存在数据库
     * @param hurryRequest
     * @return 结果内容说明，成功时为空字符串
     */
    String hurryTicket(WaybillTicketHurryRequest hurryRequest);

    /**
     * 指定运单号，查询未被取消的工单列表
     * @param mailNo
     * @return
     */
    List<WaybillTicketResponse> selectTicket(String mailNo);

    /**
     * 清空测试数据专用
     * @param authSecret
     */
    void clearTestTicket(String authSecret);
}
