package com.cainiao.waybill.bridge.enterprise.administrators.request;

import com.cainiao.waybill.bridge.enterprise.common.Paging;
import lombok.Data;

import java.util.List;

/**
 * 月结账号请求
 * <AUTHOR>
 * @date 2025-04-23 17:41:53
 */
@Data
public class EnterpriseSettleAccountRequest extends Paging{

    /**
     * 主键id
     */
    private String waybillAccountId;
    /**
     * 月结账号
     */
    private String waybillAccountNo;

    /**
     * 运力服务商
     */
    private String cpCode;

    /**
     * 产品类型
     * @see com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseProductTypeEnum
     */
    private List<String> product;

    /**
     * 备注
     */
    private String remark;

    /**
     * 签名
     */
    private String sign;

    /**
     * 场地id
     */
    private String locationId;

    /**
     * 月结账号结算类型
     */
    private Integer businessType;

}
