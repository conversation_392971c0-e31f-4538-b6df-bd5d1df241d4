package com.cainiao.waybill.bridge.biz.hsf.provider;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.alibaba.alipmc.api.model.bpm.Variable;
import com.alibaba.alipmc.api.model.task.Task;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.ExceptionUtil;
import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.biz.hsf.BpmsCallbackService;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.CommonConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.QuoteFeatureKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.algorithm.AlgorithmApproveStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.algorithm.AlgorithmStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.bill.AdjustFileStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.bill.BillSummaryStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.quote.QuoteApproveStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.quote.QuoteStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.service.BillInfoService;
import com.cainiao.waybill.bridge.biz.pickup.service.QuoteInfoService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.wrapper.BpmsWrapper;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.model.domain.PickUpAlgorithmSchemeDO;
import com.cainiao.waybill.bridge.model.domain.PickUpAlgorithmSchemeParam;
import com.cainiao.waybill.bridge.model.domain.QuoteConfigInfoDO;
import com.cainiao.waybill.bridge.model.mapper.PickUpAlgorithmSchemeMapper;
import com.cainiao.waybill.galaxy.order.common.ApprovalStatusEnum;
import com.cainiao.waybill.galaxy.order.service.RefundService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR> on 2023/9/7.
 */
@Slf4j
@HSFProvider(serviceInterface = BpmsCallbackService.class)
public class BpmsCallbackServiceImpl implements BpmsCallbackService {

    @Resource
    RefundService refundService;

    @Resource
    private PickUpAlgorithmSchemeMapper pickUpAlgorithmSchemeMapper;

    @Resource
    private QuoteInfoService quoteInfoService;

    @Resource
    private BillInfoService billInfoService;

    /**
     * 审批同意
     */
    private static final String APPROVE_AGREE = "Agree";

    /**
     * 审批拒绝
     */
    private static final String APPROVE_DISAGREE = "Disagree";

    @Resource
    private BpmsWrapper bpmsWrapper;

    /**
     * 同步退款结果
     */
    public int refundResultSync(String waybillCode, String refundId, String result) {
        log.warn("refundResultSync waybillCode:{}, refundId:{}, result:{}", waybillCode, refundId, result);
        if (ApprovalStatusEnum.AGREE.getCode().toString().equals(result)) {
            refundService.refundApproval(Long.valueOf(refundId), ApprovalStatusEnum.AGREE.getCode());
            return 1;
        }
        if (ApprovalStatusEnum.REFUSE.getCode().toString().equals(result)) {
            refundService.refundApproval(Long.valueOf(refundId), ApprovalStatusEnum.REFUSE.getCode());
            return 1;
        }
        // 取消了审批
        refundService.refundApproval(Long.valueOf(refundId), ApprovalStatusEnum.CANCEL.getCode());
        return 1;
    }

    @Override
    public void completeAlgorithmApprove(String processInstanceId) {
        PickUpLogUtil.info("completeAlgorithmApprove_start.processInstanceId:{}", processInstanceId);
        if(StringUtils.isBlank(processInstanceId)){
            PickUpLogUtil.errLog(processInstanceId, "completeAlgorithmApprove_error:processInstanceId is null", "", "审批任务ID为空");
            return;
        }
        List<Task> list = bpmsWrapper.getTasksByProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(list)) {
            PickUpLogUtil.errLog(processInstanceId, "completeAlgorithmApprove_error.taskList is empty.", "", "审批任务为空");
            return;
        }
        List<Task> disagreeTaskList = list.stream().filter(task ->
                StringUtils.equals(APPROVE_DISAGREE, task.getOutResult()))
            .collect(Collectors.toList());
        PickUpAlgorithmSchemeDO schemeDetail = new PickUpAlgorithmSchemeDO();
        PickUpAlgorithmSchemeParam schemeParam = new PickUpAlgorithmSchemeParam();
        schemeParam.createCriteria().andApproveKeyEqualTo(processInstanceId);
        String status = AlgorithmApproveStatusEnum.APPROVE_REJECT.getStatus();
        if(CollectionUtils.isNotEmpty(disagreeTaskList)){
            // 审批拒绝
            schemeDetail.setApproveStatus(status);
            pickUpAlgorithmSchemeMapper.updateByParamSelective(schemeDetail, schemeParam);
        }else {
            List<Task> finalTaskList = list.stream().filter(task ->
                    StringUtils.equals(BridgeSwitch.PICK_UP_ALGORITHM_APPROVE_FINAL_TASK_GROUP_NAME, task.getGroupName())
                    && StringUtils.equals(APPROVE_AGREE, task.getOutResult()))
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(finalTaskList)) {
                PickUpLogUtil.errLog(processInstanceId, "completeAlgorithmApprove_error.finalTaskList is empty.", "", "审批通过任务为空");
                return;
            }

            Task task = finalTaskList.get(0);
            PickUpLogUtil.info("completeAlgorithmApprove_save.processInstanceId:{}, task:{}", processInstanceId,
                JSONObject.toJSONString(task));
            if (StringUtils.equals(task.getOutResult(), APPROVE_AGREE)) {
                status = AlgorithmApproveStatusEnum.APPROVE_PASS.getStatus();
                schemeDetail.setEffectiveTime(DateUtils.dateToDateEnd(new Date()));
                schemeDetail.setStatus(AlgorithmStatusEnum.DISABLE.getStatus());
                schemeDetail.setApproveStatus(status);
                pickUpAlgorithmSchemeMapper.updateByParamSelective(schemeDetail, schemeParam);
            }
        }

    }

    @Override
    public void completeQuoteConfigApprove(String processInstanceId) {
        // 更新审批状态
        PickUpLogUtil.info("completeQuoteConfigApprove_start.processInstanceId:{}", processInstanceId);
        if(StringUtils.isBlank(processInstanceId)){
            PickUpLogUtil.errLog(processInstanceId, "completeQuoteConfigApprove_error:processInstanceId is null", "", "审批任务ID为空");
            return;
        }
        List<Variable> variableList = bpmsWrapper.getVariablesOfProcessInstance(processInstanceId);
        List<Variable> filterList = variableList.stream().filter(
                variable -> StringUtils.equals(variable.getName(), QuoteFeatureKey.APPROVE_QUOTE_KEY))
            .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(filterList)){
            // 审批变量为空
            PickUpLogUtil.errLog(processInstanceId, "completeQuoteConfigApprove_error.variableList is empty.", "", "审批变量为空");
            return;
        }
        String quoteKey = filterList.get(0).getValue();
        QuoteConfigInfoDO quoteConfigInfo = quoteInfoService.queryQuoteConfigByQuoteKey(Long.valueOf(quoteKey));
        if(null == quoteConfigInfo){
            PickUpLogUtil.errLog(processInstanceId, "completeQuoteConfigApprove_error.quoteConfigInfoDO is null.", "", "报价配置为空");
            return;
        }

        List<Task> list = bpmsWrapper.getTasksByProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(list)) {
            PickUpLogUtil.errLog(processInstanceId, "completeQuoteConfigApprove_error.taskList is empty.", "", "审批任务为空");
            return;
        }
        List<Task> disagreeTaskList = list.stream().filter(task ->
                StringUtils.equals(APPROVE_DISAGREE, task.getOutResult()))
            .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(disagreeTaskList)){
            // 审批拒绝
            quoteConfigInfo.setApproveStatus(QuoteApproveStatusEnum.APPROVE_REJECT.getStatus());
            quoteConfigInfo.setGmtModified(new Date());
            quoteInfoService.updateQuoteConfig(quoteConfigInfo);
        }else {
            // 最后节点审批通过
            List<Task> finalTaskList = list.stream().filter(task ->
                    StringUtils.equals(BridgeSwitch.PICK_UP_QUOTE_APPROVE_FINAL_TASK_GROUP_NAME, task.getGroupName())
                    && StringUtils.equals(APPROVE_AGREE, task.getOutResult()))
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(finalTaskList)) {
                PickUpLogUtil.errLog(processInstanceId, "completeQuoteConfigApprove_error.finalTaskList is empty.", "", "审批通过任务为空");
                return;
            }

            Task task = finalTaskList.get(0);
            PickUpLogUtil.info("completeQuoteConfigApprove_save.processInstanceId:{}, task:{}", processInstanceId,
                JSONObject.toJSONString(task));
            if (StringUtils.equals(task.getOutResult(), APPROVE_AGREE)) {
                // 审批通过 更新报价为已生效/待生效
                quoteConfigInfo.setApproveStatus(QuoteApproveStatusEnum.APPROVE_PASS.getStatus());
                Date effectiveTimeStart = quoteConfigInfo.getEffectiveTimeStart();
                Date effectiveTimeEnd = quoteConfigInfo.getEffectiveTimeEnd();
                Date currentDate = new Date();

                if(currentDate.getTime() >= effectiveTimeStart.getTime() && currentDate.getTime() <= effectiveTimeEnd.getTime()){
                    quoteConfigInfo.setStatus(QuoteStatusEnum.EFFECTIVE.getStatus());
                }else if(currentDate.getTime() > effectiveTimeEnd.getTime()){
                    quoteConfigInfo.setStatus(QuoteStatusEnum.INVALID.getStatus());
                } else{
                    quoteConfigInfo.setStatus(QuoteStatusEnum.WAIT_EFFECTIVE.getStatus());
                }

                quoteConfigInfo.setGmtModified(new Date());
                quoteInfoService.updateQuoteConfig(quoteConfigInfo);

                if(quoteConfigInfo.getStatus().equals(QuoteStatusEnum.EFFECTIVE.getStatus())){
                    // 更新客户为已生效
                    quoteInfoService.effactiveSettlementStatusByOwnerId(quoteConfigInfo.getOwnerId());
                }

                PickUpLogUtil.info("completeQuoteConfigApprove_save.报价生效状态已变更完成。quoteConfigInfo:{}", JSONObject.toJSONString(quoteConfigInfo));
            }
        }

    }

    @Override
    public void completeBillAdjustConfigApprove(String processInstanceId) {
        // 更新审批状态
        PickUpLogUtil.info("completeBillAdjustConfigApprove_start.processInstanceId:{}", processInstanceId);
        if(StringUtils.isBlank(processInstanceId)){
            PickUpLogUtil.errLog(processInstanceId, "completeBillAdjustConfigApprove_error:processInstanceId is null", "", "审批任务ID为空");
            return;
        }
        List<Variable> variableList = bpmsWrapper.getVariablesOfProcessInstance(processInstanceId);
        Variable variableBillId = variableList.stream().filter(
                variable -> StringUtils.equals(variable.getName(), QuoteFeatureKey.APPROVE_ADJUST_BILL_ID_KEY))
            .findFirst().orElse(null);
        Variable variableAdjustUser = variableList.stream().filter(
                variable -> StringUtils.equals(variable.getName(), QuoteFeatureKey.APPROVE_ADJUST_USER))
            .findFirst().orElse(null);
        Variable variableAdjustMonth = variableList.stream().filter(
                variable -> StringUtils.equals(variable.getName(), QuoteFeatureKey.APPROVE_ADJUST_MONTH))
            .findFirst().orElse(null);
        Variable variableAdjustBillAmount = variableList.stream().filter(
                variable -> StringUtils.equals(variable.getName(), QuoteFeatureKey.APPROVE_ADJUST_BILL_AMOUNT))
            .findFirst().orElse(null);
        Variable variableAdjustAmount = variableList.stream().filter(
                variable -> StringUtils.equals(variable.getName(), QuoteFeatureKey.APPROVE_ADJUST_AMOUNT))
            .findFirst().orElse(null);
        Variable adjustIdAmountMapObj = variableList.stream().filter(
                variable -> StringUtils.equals(variable.getName(), QuoteFeatureKey.APPROVE_ADJUST_ID_AMOUNT_MAP))
            .findFirst().orElse(null);

        if(null == variableBillId
            || null == variableAdjustUser
            || null == variableAdjustMonth
            || null == variableAdjustBillAmount
            || null == variableAdjustAmount){
            // 审批变量为空
            PickUpLogUtil.errLog(processInstanceId, "completeBillAdjustConfigApprove_error.variableList is empty.", "", "审批变量为空");
            return;
        }
        String adjustIdAmountMapStr = adjustIdAmountMapObj.getValue();

        Map<String, String> adjustIdAmountMap = JSONObject.parseObject(adjustIdAmountMapStr, HashMap.class);

        if(CollectionUtils.isEmpty(adjustIdAmountMap.keySet())){
            PickUpLogUtil.errLog(processInstanceId, "completeBillAdjustConfigApprove_error.billList is empty.", "", "账单调账ID为空");
        }
        Set<String> billIdList = adjustIdAmountMap.keySet();

        List<Task> list = bpmsWrapper.getTasksByProcessInstanceId(processInstanceId);
        if (CollectionUtils.isEmpty(list)) {
            PickUpLogUtil.errLog(processInstanceId, "completeBillAdjustConfigApprove_error.taskList is empty.", "", "审批任务为空");
            return;
        }
        List<Task> disagreeTaskList = list.stream().filter(task ->
                StringUtils.equals(APPROVE_DISAGREE, task.getOutResult()))
            .collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(disagreeTaskList)){
            // 审批拒绝
            for(String billId: billIdList){
                billInfoService.updateBillStatus(billId, BillSummaryStatusEnum.ADJUSTMENT_APPROVE_REJECT.getStatus(), false);
            }
        }else {
            // 最后节点审批通过
            List<Task> finalTaskList = list.stream().filter(task ->
                    StringUtils.equals(BridgeSwitch.PICK_UP_ADJUST_APPROVE_FINAL_TASK_GROUP_NAME, task.getGroupName())
                    && StringUtils.equals(APPROVE_AGREE, task.getOutResult()))
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(finalTaskList)) {
                PickUpLogUtil.errLog(processInstanceId, "completeBillAdjustConfigApprove_error.finalTaskList is empty.", "", "审批通过任务为空");
                return;
            }

            Task task = finalTaskList.get(0);
            PickUpLogUtil.info("completeBillAdjustConfigApprove_save.processInstanceId:{}, task:{}", processInstanceId,
                JSONObject.toJSONString(task));
            if (StringUtils.equals(task.getOutResult(), APPROVE_AGREE)) {
                // 审批通过
                for(String billId: billIdList){
                    try{
                        String amountStr = adjustIdAmountMap.get(billId);
                        // 单位：分
                        int amount = Integer.parseInt(amountStr.split(CommonConstants.SPLIT_POINT)[0]);

                        // 事务更新账单状态和金额
                        updateAdjustApproveDone(billId, amount);
                        PickUpLogUtil.info("completeBillAdjustConfigApprove_save.调账完成。billId:{},amount:{}", billId, amount);
                    }catch (Exception e){
                        PickUpLogUtil.errLog(processInstanceId, "completeBillAdjustConfigApprove_error.updateBillAmount_error.", billId, "更新账单金额失败",
                            ExceptionUtil.getStackTrace(e));
                        billInfoService.updateBillStatus(billId, BillSummaryStatusEnum.ADJUSTMENT_APPROVE_AGREE.getStatus(), false);
                    }
                }
                PickUpLogUtil.info("completeBillAdjustConfigApprove_save.调账生效状态已变更完成。quoteConfigInfo:{}", billIdList);
            }
        }
    }

    /**
     * 更新调账完成后的状态和金额
     * @param billId
     * @param amount 单位：分
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "bridgeTransactionManager")
    public void updateAdjustApproveDone(String billId, Integer amount) {
        // 更新账单状态和金额
        billInfoService.updateBillAmountAndStatus(billId, amount, BillSummaryStatusEnum.ADJUSTMENT_BILL_DONE.getStatus());
        // 更新调账文件状态
        billInfoService.updateBillAdjustFileStatus(billId, AdjustFileStatusEnum.DONE.getStatus());
        // 更新调账记录状态
        billInfoService.updateBillAdjustStatus(billId, AdjustFileStatusEnum.DONE.getStatus());
    }
}
