package com.cainiao.waybill.bridge.web.pickup.ticket.role;

import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022/4/22 上午11:56
 */
@Slf4j
public class CheckRoleInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) throws Exception {
        //log.info("preHandleUrl:" + httpServletRequest.getRequestURI());
        if(TicketRoleUtil.currentRole() == null){
            log.error(PickUpTicketConstant.NO_ROLE_MSG);
            return false;
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }
}
