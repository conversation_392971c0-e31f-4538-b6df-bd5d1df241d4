package com.cainiao.waybill.bridge.ticket.api.enums;

import java.util.Objects;

/**
 * 工单类型枚举值
 * <AUTHOR>
 */
public enum TicketTypeEnum {

    TYPE_1(1,"签收核实"),
    TYPE_2(2,"核实取件码"),
    TYPE_3(3,"丢失/破损"),

    ;

    private Integer code;
    private String content;

    TicketTypeEnum(Integer code,String content){
        this.code = code;
        this.content = content;
    }

    public Integer getCode(){
        return code;
    }

    public String getContent(){
        return content;
    }

    public static String getContentByCode(Integer inputCode){
        if (inputCode == null){
            return null;
        }
        for (TicketTypeEnum ticketTypeEnum : TicketTypeEnum.values()) {
            if (Objects.equals(ticketTypeEnum.getCode(),inputCode)){
                return ticketTypeEnum.getContent();
            }
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(getContentByCode(1));
    }


}
