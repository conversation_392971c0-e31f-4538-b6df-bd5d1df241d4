package com.cainiao.waybill.bridge.biz.utils.excel;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.Collection;
import java.util.List;

import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.MarkdownUtil;
import com.google.common.collect.Lists;
import com.taobao.util.CollectionUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ReflectionUtils;

/**
 * <AUTHOR>
 */
@Slf4j
public class MarkdownExportUtil {

    /**
     * 组装list对象为excel数据，
     * 注：依赖"T"对象属性的定义顺序。作为excel的列
     */
    public static <T> String build(Collection<T> dataList, Class<T> tClass) {
        if (CollectionUtil.isEmpty(dataList)) {
            dataList = Lists.newArrayList();
        }
        List<Field> fields = Lists.newArrayList();
        //fields的顺序为，属性的定义顺序
        ReflectionUtils.doWithFields(tClass, fields::add,
            field -> field.isAnnotationPresent(ExcelHeader.class));
        List<String> header = Lists.newArrayList();
        for (int i = 0; i < fields.size(); i++) {
            Field field = fields.get(i);
            ExcelHeader excelHeader = field.getAnnotation(
                ExcelHeader.class);
            if (excelHeader != null) {
                header.add(excelHeader.value());
            }
        }
        List<List<String>> data = Lists.newArrayList();
        for (T item : dataList) {
            List<String> list = Lists.newArrayList();
            for (int i = 0; i < fields.size(); i++) {
                Field field = fields.get(i);
                PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(tClass, field.getName());
                try {
                    Object obj = propertyDescriptor.getReadMethod().invoke(item);
                    if (obj == null || StringUtils.isBlank(obj.toString())) {
                        obj = "-";
                    }
                    list.add(obj.toString());

                } catch (IllegalAccessException | InvocationTargetException e) {
                    log.error("excel_export_error, class:{}, method:{}",
                        item.getClass(), field.getName(), e);
                    throw new BridgeBusinessException("excel_export_error", "excel导出失败");
                }
            }
            data.add(list);
        }

        return MarkdownUtil.table(header, data,null, null);
    }

    public static void main(String[] args) {
        Test test = new Test();
        test.test = "123";
        String str = build(Lists.newArrayList(test), Test.class);
        System.out.println(str);
    }

    @Data
    static class Test{

        @ExcelHeader("测试")
        String test;
    }

}
