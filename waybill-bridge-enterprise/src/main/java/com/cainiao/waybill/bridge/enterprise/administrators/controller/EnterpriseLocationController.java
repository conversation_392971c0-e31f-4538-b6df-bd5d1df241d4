package com.cainiao.waybill.bridge.enterprise.administrators.controller;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.administrators.request.*;
import com.cainiao.waybill.bridge.enterprise.administrators.service.DingTalkLocationService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.UserContext;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseLocationDTO;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.web.bind.annotation.*;

/**
 * 企业寄件--场地相关入口
 */
@RestController
@RequestMapping(value = "/enterprise/location")
public class EnterpriseLocationController {
    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private DingTalkLocationService dingTalkLocationService;


    @PostMapping("/page")
    @Description("分页查询场地")
    EnterpriseBaseResult<BridgePagingDTO<WaybillEnterpriseLocationDTO>> page(@RequestBody LocationRequest request) {

        try {
            LOGGER.info("page request:{}", JSON.toJSONString(request));
            BridgePagingDTO<WaybillEnterpriseLocationDTO> result = dingTalkLocationService.pageList(request);
            return EnterpriseBaseResult.success(result);
        } catch (Exception e) {
            LOGGER.error("page enterprise location page list error, request:{}", JSON.toJSONString(request), e);
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @PostMapping("/addLocation")
    @Description("新增场地")
    EnterpriseBaseResult<String> addLocation(@RequestBody LocationRequest request) {
        LOGGER.info("addLocation request:{}", JSON.toJSONString(request));
        try {
            String addResult = dingTalkLocationService.addLocation(request);
            return EnterpriseBaseResult.success(addResult);
        } catch (Exception e) {
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @PostMapping("/editLocation")
    @Description("编辑场地")
    EnterpriseBaseResult<Boolean> editLocation(@RequestBody LocationRequest request) {
        LOGGER.info("editLocation request:{}", JSON.toJSONString(request));
        try {
            if(null != request.getWaybillAccountsList()){
                request.setWaybillAccountsString(new Gson().toJson(request.getWaybillAccountsList()));
            }
            int addResult = dingTalkLocationService.updateLocation(request);
            return EnterpriseBaseResult.success(addResult == 1);
        } catch (Exception e) {
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("/deleteById")
    @Description("删除场地")
    @Deprecated
    EnterpriseBaseResult<Boolean> deleteById(@RequestParam("id") Long id) {
        int deleteResult = dingTalkLocationService.deleteLocationById(id);
        return EnterpriseBaseResult.success(deleteResult == 1);
    }

    @GetMapping("/getLocationById")
    @Description("根据id获取场地")
    @Deprecated
    EnterpriseBaseResult<WaybillEnterpriseLocationDTO> getLocationById(@RequestParam("id") Long id) {
        if (id == null) {
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.PARAM_ERROR.code(), "id不能为空");
        }
        return EnterpriseBaseResult.success(dingTalkLocationService.getById(id));
    }

    @GetMapping("/deleteByLocationId")
    @Description("删除场地")
    EnterpriseBaseResult<Boolean> deleteByLocationId(@RequestParam("locationId") String locationId) {
        if (StringUtils.isEmpty(locationId)) {
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.PARAM_ERROR.code(), "locationId不能为空");
        }
        int deleteResult = dingTalkLocationService.deleteByLocationId(locationId, UserContext.getUser().getCorpId());
        return EnterpriseBaseResult.success(deleteResult == 1);
    }

    @GetMapping("/getLocationByLocationId")
    @Description("根据id获取场地")
    EnterpriseBaseResult<WaybillEnterpriseLocationDTO> getLocationByLocationId(@RequestParam("locationId") String locationId) {
        if (StringUtils.isEmpty(locationId)) {
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.PARAM_ERROR.code(), "locationId不能为空");
        }
        return EnterpriseBaseResult.success(dingTalkLocationService.getLocationByLocationId(locationId, UserContext.getUser().getCorpId()));
    }
}
