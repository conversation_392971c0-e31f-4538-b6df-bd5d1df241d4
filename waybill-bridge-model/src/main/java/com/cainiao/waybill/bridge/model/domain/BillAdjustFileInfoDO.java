package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class BillAdjustFileInfoDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   账单ID
     *
     *
     * @mbg.generated
     */
    private String billId;

    /**
     * Database Column Remarks:
     *   调账文件ID
     *
     *
     * @mbg.generated
     */
    private Long adjustFileId;

    /**
     * Database Column Remarks:
     *   调账类型
     *
     *
     * @mbg.generated
     */
    private String adjustType;

    /**
     * Database Column Remarks:
     *   文件名称
     *
     *
     * @mbg.generated
     */
    private String fileName;

    /**
     * Database Column Remarks:
     *   文件地址
     *
     *
     * @mbg.generated
     */
    private String fileUrl;

    /**
     * Database Column Remarks:
     *   上传时间
     *
     *
     * @mbg.generated
     */
    private Date uploadDate;

    /**
     * Database Column Remarks:
     *   上传人
     *
     *
     * @mbg.generated
     */
    private Long uploadUserId;

    /**
     * Database Column Remarks:
     *   备注
     *
     *
     * @mbg.generated
     */
    private String remark;

    /**
     * Database Column Remarks:
     *   状态
     *
     *
     * @mbg.generated
     */
    private String status;

    /**
     * Database Column Remarks:
     *   调账总金额，单位分
     *
     *
     * @mbg.generated
     */
    private Long adjustAmount;

    /**
     *
     * @return the value of bill_adjust_file_info.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for bill_adjust_file_info.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of bill_adjust_file_info.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for bill_adjust_file_info.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of bill_adjust_file_info.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for bill_adjust_file_info.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of bill_adjust_file_info.bill_id
     *
     * @mbg.generated
     */
    public String getBillId() {
        return billId;
    }

    /**
     *
     * @param billId the value for bill_adjust_file_info.bill_id
     *
     * @mbg.generated
     */
    public void setBillId(String billId) {
        this.billId = billId;
    }

    /**
     *
     * @return the value of bill_adjust_file_info.adjust_file_id
     *
     * @mbg.generated
     */
    public Long getAdjustFileId() {
        return adjustFileId;
    }

    /**
     *
     * @param adjustFileId the value for bill_adjust_file_info.adjust_file_id
     *
     * @mbg.generated
     */
    public void setAdjustFileId(Long adjustFileId) {
        this.adjustFileId = adjustFileId;
    }

    /**
     *
     * @return the value of bill_adjust_file_info.adjust_type
     *
     * @mbg.generated
     */
    public String getAdjustType() {
        return adjustType;
    }

    /**
     *
     * @param adjustType the value for bill_adjust_file_info.adjust_type
     *
     * @mbg.generated
     */
    public void setAdjustType(String adjustType) {
        this.adjustType = adjustType;
    }

    /**
     *
     * @return the value of bill_adjust_file_info.file_name
     *
     * @mbg.generated
     */
    public String getFileName() {
        return fileName;
    }

    /**
     *
     * @param fileName the value for bill_adjust_file_info.file_name
     *
     * @mbg.generated
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     *
     * @return the value of bill_adjust_file_info.file_url
     *
     * @mbg.generated
     */
    public String getFileUrl() {
        return fileUrl;
    }

    /**
     *
     * @param fileUrl the value for bill_adjust_file_info.file_url
     *
     * @mbg.generated
     */
    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    /**
     *
     * @return the value of bill_adjust_file_info.upload_date
     *
     * @mbg.generated
     */
    public Date getUploadDate() {
        return uploadDate;
    }

    /**
     *
     * @param uploadDate the value for bill_adjust_file_info.upload_date
     *
     * @mbg.generated
     */
    public void setUploadDate(Date uploadDate) {
        this.uploadDate = uploadDate;
    }

    /**
     *
     * @return the value of bill_adjust_file_info.upload_user_id
     *
     * @mbg.generated
     */
    public Long getUploadUserId() {
        return uploadUserId;
    }

    /**
     *
     * @param uploadUserId the value for bill_adjust_file_info.upload_user_id
     *
     * @mbg.generated
     */
    public void setUploadUserId(Long uploadUserId) {
        this.uploadUserId = uploadUserId;
    }

    /**
     *
     * @return the value of bill_adjust_file_info.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @param remark the value for bill_adjust_file_info.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     *
     * @return the value of bill_adjust_file_info.status
     *
     * @mbg.generated
     */
    public String getStatus() {
        return status;
    }

    /**
     *
     * @param status the value for bill_adjust_file_info.status
     *
     * @mbg.generated
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     *
     * @return the value of bill_adjust_file_info.adjust_amount
     *
     * @mbg.generated
     */
    public Long getAdjustAmount() {
        return adjustAmount;
    }

    /**
     *
     * @param adjustAmount the value for bill_adjust_file_info.adjust_amount
     *
     * @mbg.generated
     */
    public void setAdjustAmount(Long adjustAmount) {
        this.adjustAmount = adjustAmount;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", billId=").append(billId);
        sb.append(", adjustFileId=").append(adjustFileId);
        sb.append(", adjustType=").append(adjustType);
        sb.append(", fileName=").append(fileName);
        sb.append(", fileUrl=").append(fileUrl);
        sb.append(", uploadDate=").append(uploadDate);
        sb.append(", uploadUserId=").append(uploadUserId);
        sb.append(", remark=").append(remark);
        sb.append(", status=").append(status);
        sb.append(", adjustAmount=").append(adjustAmount);
        sb.append("]");
        return sb.toString();
    }
}