package com.cainiao.waybill.bridge.web.charity;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import com.cainiao.waybill.bridge.biz.charity.constant.CharityConstant;
import com.cainiao.waybill.bridge.biz.charity.request.CharityPagingBaseRequest;
import com.cainiao.waybill.bridge.biz.charity.response.CharityPagingResponse;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelExportUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.web.common.util.ExeclUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> zouping.fzp
 * @Classname CharityExcelUtil
 * @Description
 * @Date 2022/9/6 5:09 下午
 * @Version 1.0
 */
@Slf4j(topic = "CHARITY_INFO")
public class CharityExcelUtil {

    public static <E, F, T extends CharityPagingBaseRequest, R extends CharityPagingResponse<F>> String exportFile(
        T pagingBaseRequest, String fileName, Class<E> tClass, Function<T, R> supplier) {
        pagingBaseRequest.setCurrentPage(1);
        pagingBaseRequest.setPageSize(100);

        int totalPage = 1;
        List<E> list = Lists.newArrayList();
        while (pagingBaseRequest.getCurrentPage() <= totalPage) {
            R r = supplier.apply(pagingBaseRequest);
            // 限制最大导出一万
            if (r.getPaging().getTotalCount() > 10000) {
                throw new BridgeBusinessException("export_num_over_limit", "导出数量超过一万条，请缩小导出条件");
            }
            List<E> resultList = ListUtil.stream(r.getTableData()).map(x -> {
                try {
                    E e = tClass.newInstance();
                    BeanUtils.copyProperties(x, e);
                    return e;
                } catch (Exception exception) {
                    log.error("导出文件失败", exception);
                    throw new BridgeBusinessException("excel_export_exception", "文件导出异常");
                }
            }).collect(Collectors.toList());

            list.addAll(resultList);
            totalPage = r.getPaging().getTotalPage();
            pagingBaseRequest.setCurrentPage(pagingBaseRequest.getCurrentPage() + 1);
        }
        Workbook workbook = ExcelExportUtil.build(list, fileName, tClass);
        return ExeclUtil.exportToOSS(workbook, CharityConstant.OSS_TEMP_FILE_DIR);
    }
}
