package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class BillSummaryInfoDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   账单月份
     *
     *
     * @mbg.generated
     */
    private String billMonth;

    /**
     * Database Column Remarks:
     *   账单ID
     *
     *
     * @mbg.generated
     */
    private String billId;

    /**
     * Database Column Remarks:
     *   账单类型
     *
     *
     * @mbg.generated
     */
    private String billType;

    /**
     * Database Column Remarks:
     *   账单状态
     *
     *
     * @mbg.generated
     */
    private String billStatus;

    /**
     * Database Column Remarks:
     *   归属客户ID
     *
     *
     * @mbg.generated
     */
    private Long ownerId;

    /**
     * Database Column Remarks:
     *   淘宝会员ID
     *
     *
     * @mbg.generated
     */
    private String tbUserId;

    /**
     * Database Column Remarks:
     *   菜鸟会员ID
     *
     *
     * @mbg.generated
     */
    private String cnUserId;

    /**
     * Database Column Remarks:
     *   CP名称
     *
     *
     * @mbg.generated
     */
    private String cpName;

    /**
     * Database Column Remarks:
     *   CP编码
     *
     *
     * @mbg.generated
     */
    private String cpCode;

    /**
     * Database Column Remarks:
     *   产品编码
     *
     *
     * @mbg.generated
     */
    private String productCode;

    /**
     * Database Column Remarks:
     *   结算原始金额，即系统根据录入的价格和客户当月实际消耗产生的费用，单位分
     *
     *
     * @mbg.generated
     */
    private Integer originalSettlementAmount;

    /**
     * Database Column Remarks:
     *   调账金额，单位分
     *
     *
     * @mbg.generated
     */
    private Integer adjustmentAmount;

    /**
     * Database Column Remarks:
     *   账单金额，单位分
     *
     *
     * @mbg.generated
     */
    private Integer billAmount;

    /**
     * Database Column Remarks:
     *   已付金额，单位分
     *
     *
     * @mbg.generated
     */
    private Integer paymentAmount;

    /**
     * Database Column Remarks:
     *   待付金额，单位分
     *
     *
     * @mbg.generated
     */
    private Integer payableAmount;

    /**
     * Database Column Remarks:
     *   出账时间
     *
     *
     * @mbg.generated
     */
    private Date billingTime;

    /**
     * Database Column Remarks:
     *   账单确认时间
     *
     *
     * @mbg.generated
     */
    private Date confirmTime;

    /**
     * Database Column Remarks:
     *   调账时间
     *
     *
     * @mbg.generated
     */
    private Date adjustmentTime;

    /**
     * Database Column Remarks:
     *   支付时间
     *
     *
     * @mbg.generated
     */
    private Date paymentTime;

    /**
     * Database Column Remarks:
     *   确认支付时间
     *
     *
     * @mbg.generated
     */
    private Date confirmPaymentTime;

    /**
     * Database Column Remarks:
     *   确认支付方式
     *
     *
     * @mbg.generated
     */
    private String confirmPaymentWay;

    /**
     * Database Column Remarks:
     *   账单核销时间
     *
     *
     * @mbg.generated
     */
    private Date cancellationArchiveTime;

    /**
     * Database Column Remarks:
     *   逾期支付状态，未逾期/已逾期
     *
     *
     * @mbg.generated
     */
    private String overduePayStatus;

    /**
     * Database Column Remarks:
     *   审批状态
     *
     *
     * @mbg.generated
     */
    private String approveStatus;

    /**
     * Database Column Remarks:
     *   扩展字段
     *
     *
     * @mbg.generated
     */
    private String feature;

    /**
     *
     * @return the value of bill_summary_info.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for bill_summary_info.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of bill_summary_info.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for bill_summary_info.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of bill_summary_info.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for bill_summary_info.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of bill_summary_info.bill_month
     *
     * @mbg.generated
     */
    public String getBillMonth() {
        return billMonth;
    }

    /**
     *
     * @param billMonth the value for bill_summary_info.bill_month
     *
     * @mbg.generated
     */
    public void setBillMonth(String billMonth) {
        this.billMonth = billMonth;
    }

    /**
     *
     * @return the value of bill_summary_info.bill_id
     *
     * @mbg.generated
     */
    public String getBillId() {
        return billId;
    }

    /**
     *
     * @param billId the value for bill_summary_info.bill_id
     *
     * @mbg.generated
     */
    public void setBillId(String billId) {
        this.billId = billId;
    }

    /**
     *
     * @return the value of bill_summary_info.bill_type
     *
     * @mbg.generated
     */
    public String getBillType() {
        return billType;
    }

    /**
     *
     * @param billType the value for bill_summary_info.bill_type
     *
     * @mbg.generated
     */
    public void setBillType(String billType) {
        this.billType = billType;
    }

    /**
     *
     * @return the value of bill_summary_info.bill_status
     *
     * @mbg.generated
     */
    public String getBillStatus() {
        return billStatus;
    }

    /**
     *
     * @param billStatus the value for bill_summary_info.bill_status
     *
     * @mbg.generated
     */
    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus;
    }

    /**
     *
     * @return the value of bill_summary_info.owner_id
     *
     * @mbg.generated
     */
    public Long getOwnerId() {
        return ownerId;
    }

    /**
     *
     * @param ownerId the value for bill_summary_info.owner_id
     *
     * @mbg.generated
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     *
     * @return the value of bill_summary_info.tb_user_id
     *
     * @mbg.generated
     */
    public String getTbUserId() {
        return tbUserId;
    }

    /**
     *
     * @param tbUserId the value for bill_summary_info.tb_user_id
     *
     * @mbg.generated
     */
    public void setTbUserId(String tbUserId) {
        this.tbUserId = tbUserId;
    }

    /**
     *
     * @return the value of bill_summary_info.cn_user_id
     *
     * @mbg.generated
     */
    public String getCnUserId() {
        return cnUserId;
    }

    /**
     *
     * @param cnUserId the value for bill_summary_info.cn_user_id
     *
     * @mbg.generated
     */
    public void setCnUserId(String cnUserId) {
        this.cnUserId = cnUserId;
    }

    /**
     *
     * @return the value of bill_summary_info.cp_name
     *
     * @mbg.generated
     */
    public String getCpName() {
        return cpName;
    }

    /**
     *
     * @param cpName the value for bill_summary_info.cp_name
     *
     * @mbg.generated
     */
    public void setCpName(String cpName) {
        this.cpName = cpName;
    }

    /**
     *
     * @return the value of bill_summary_info.cp_code
     *
     * @mbg.generated
     */
    public String getCpCode() {
        return cpCode;
    }

    /**
     *
     * @param cpCode the value for bill_summary_info.cp_code
     *
     * @mbg.generated
     */
    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    /**
     *
     * @return the value of bill_summary_info.product_code
     *
     * @mbg.generated
     */
    public String getProductCode() {
        return productCode;
    }

    /**
     *
     * @param productCode the value for bill_summary_info.product_code
     *
     * @mbg.generated
     */
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    /**
     *
     * @return the value of bill_summary_info.original_settlement_amount
     *
     * @mbg.generated
     */
    public Integer getOriginalSettlementAmount() {
        return originalSettlementAmount;
    }

    /**
     *
     * @param originalSettlementAmount the value for bill_summary_info.original_settlement_amount
     *
     * @mbg.generated
     */
    public void setOriginalSettlementAmount(Integer originalSettlementAmount) {
        this.originalSettlementAmount = originalSettlementAmount;
    }

    /**
     *
     * @return the value of bill_summary_info.adjustment_amount
     *
     * @mbg.generated
     */
    public Integer getAdjustmentAmount() {
        return adjustmentAmount;
    }

    /**
     *
     * @param adjustmentAmount the value for bill_summary_info.adjustment_amount
     *
     * @mbg.generated
     */
    public void setAdjustmentAmount(Integer adjustmentAmount) {
        this.adjustmentAmount = adjustmentAmount;
    }

    /**
     *
     * @return the value of bill_summary_info.bill_amount
     *
     * @mbg.generated
     */
    public Integer getBillAmount() {
        return billAmount;
    }

    /**
     *
     * @param billAmount the value for bill_summary_info.bill_amount
     *
     * @mbg.generated
     */
    public void setBillAmount(Integer billAmount) {
        this.billAmount = billAmount;
    }

    /**
     *
     * @return the value of bill_summary_info.payment_amount
     *
     * @mbg.generated
     */
    public Integer getPaymentAmount() {
        return paymentAmount;
    }

    /**
     *
     * @param paymentAmount the value for bill_summary_info.payment_amount
     *
     * @mbg.generated
     */
    public void setPaymentAmount(Integer paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    /**
     *
     * @return the value of bill_summary_info.payable_amount
     *
     * @mbg.generated
     */
    public Integer getPayableAmount() {
        return payableAmount;
    }

    /**
     *
     * @param payableAmount the value for bill_summary_info.payable_amount
     *
     * @mbg.generated
     */
    public void setPayableAmount(Integer payableAmount) {
        this.payableAmount = payableAmount;
    }

    /**
     *
     * @return the value of bill_summary_info.billing_time
     *
     * @mbg.generated
     */
    public Date getBillingTime() {
        return billingTime;
    }

    /**
     *
     * @param billingTime the value for bill_summary_info.billing_time
     *
     * @mbg.generated
     */
    public void setBillingTime(Date billingTime) {
        this.billingTime = billingTime;
    }

    /**
     *
     * @return the value of bill_summary_info.confirm_time
     *
     * @mbg.generated
     */
    public Date getConfirmTime() {
        return confirmTime;
    }

    /**
     *
     * @param confirmTime the value for bill_summary_info.confirm_time
     *
     * @mbg.generated
     */
    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    /**
     *
     * @return the value of bill_summary_info.adjustment_time
     *
     * @mbg.generated
     */
    public Date getAdjustmentTime() {
        return adjustmentTime;
    }

    /**
     *
     * @param adjustmentTime the value for bill_summary_info.adjustment_time
     *
     * @mbg.generated
     */
    public void setAdjustmentTime(Date adjustmentTime) {
        this.adjustmentTime = adjustmentTime;
    }

    /**
     *
     * @return the value of bill_summary_info.payment_time
     *
     * @mbg.generated
     */
    public Date getPaymentTime() {
        return paymentTime;
    }

    /**
     *
     * @param paymentTime the value for bill_summary_info.payment_time
     *
     * @mbg.generated
     */
    public void setPaymentTime(Date paymentTime) {
        this.paymentTime = paymentTime;
    }

    /**
     *
     * @return the value of bill_summary_info.confirm_payment_time
     *
     * @mbg.generated
     */
    public Date getConfirmPaymentTime() {
        return confirmPaymentTime;
    }

    /**
     *
     * @param confirmPaymentTime the value for bill_summary_info.confirm_payment_time
     *
     * @mbg.generated
     */
    public void setConfirmPaymentTime(Date confirmPaymentTime) {
        this.confirmPaymentTime = confirmPaymentTime;
    }

    /**
     *
     * @return the value of bill_summary_info.confirm_payment_way
     *
     * @mbg.generated
     */
    public String getConfirmPaymentWay() {
        return confirmPaymentWay;
    }

    /**
     *
     * @param confirmPaymentWay the value for bill_summary_info.confirm_payment_way
     *
     * @mbg.generated
     */
    public void setConfirmPaymentWay(String confirmPaymentWay) {
        this.confirmPaymentWay = confirmPaymentWay;
    }

    /**
     *
     * @return the value of bill_summary_info.cancellation_archive_time
     *
     * @mbg.generated
     */
    public Date getCancellationArchiveTime() {
        return cancellationArchiveTime;
    }

    /**
     *
     * @param cancellationArchiveTime the value for bill_summary_info.cancellation_archive_time
     *
     * @mbg.generated
     */
    public void setCancellationArchiveTime(Date cancellationArchiveTime) {
        this.cancellationArchiveTime = cancellationArchiveTime;
    }

    /**
     *
     * @return the value of bill_summary_info.overdue_pay_status
     *
     * @mbg.generated
     */
    public String getOverduePayStatus() {
        return overduePayStatus;
    }

    /**
     *
     * @param overduePayStatus the value for bill_summary_info.overdue_pay_status
     *
     * @mbg.generated
     */
    public void setOverduePayStatus(String overduePayStatus) {
        this.overduePayStatus = overduePayStatus;
    }

    /**
     *
     * @return the value of bill_summary_info.approve_status
     *
     * @mbg.generated
     */
    public String getApproveStatus() {
        return approveStatus;
    }

    /**
     *
     * @param approveStatus the value for bill_summary_info.approve_status
     *
     * @mbg.generated
     */
    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    /**
     *
     * @return the value of bill_summary_info.feature
     *
     * @mbg.generated
     */
    public String getFeature() {
        return feature;
    }

    /**
     *
     * @param feature the value for bill_summary_info.feature
     *
     * @mbg.generated
     */
    public void setFeature(String feature) {
        this.feature = feature;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", billMonth=").append(billMonth);
        sb.append(", billId=").append(billId);
        sb.append(", billType=").append(billType);
        sb.append(", billStatus=").append(billStatus);
        sb.append(", ownerId=").append(ownerId);
        sb.append(", tbUserId=").append(tbUserId);
        sb.append(", cnUserId=").append(cnUserId);
        sb.append(", cpName=").append(cpName);
        sb.append(", cpCode=").append(cpCode);
        sb.append(", productCode=").append(productCode);
        sb.append(", originalSettlementAmount=").append(originalSettlementAmount);
        sb.append(", adjustmentAmount=").append(adjustmentAmount);
        sb.append(", billAmount=").append(billAmount);
        sb.append(", paymentAmount=").append(paymentAmount);
        sb.append(", payableAmount=").append(payableAmount);
        sb.append(", billingTime=").append(billingTime);
        sb.append(", confirmTime=").append(confirmTime);
        sb.append(", adjustmentTime=").append(adjustmentTime);
        sb.append(", paymentTime=").append(paymentTime);
        sb.append(", confirmPaymentTime=").append(confirmPaymentTime);
        sb.append(", confirmPaymentWay=").append(confirmPaymentWay);
        sb.append(", cancellationArchiveTime=").append(cancellationArchiveTime);
        sb.append(", overduePayStatus=").append(overduePayStatus);
        sb.append(", approveStatus=").append(approveStatus);
        sb.append(", feature=").append(feature);
        sb.append("]");
        return sb.toString();
    }
}