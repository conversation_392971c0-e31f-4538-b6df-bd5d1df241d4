package com.cainiao.waybill.bridge.biz.label.manager;

import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.label.dto.TpnMsgDTO;
import com.cainiao.waybill.bridge.common.label.dto.request.TpnMsgRequest;

/**
 * 千牛消息manager
 *
 * <AUTHOR>
 * @since 2017/04/21
 */
public interface TpnMsgManager {
    /**
     * 发送千牛消息
     *
     * @param tpnMsgDTO
     * @return
     */
    @Deprecated
    String sendTpnMsg(TpnMsgDTO tpnMsgDTO) throws BridgeBaseException;

    /**
     * 发送千牛消息
     *
     * @return
     */
    boolean sendTpnMsg(TpnMsgRequest request) throws BridgeBaseException;

    /**
     * 给指定商家授权发送千牛消息
     *
     * @param sellerId
     * @return
     */
    void authorizeTpn(Long sellerId) throws BridgeBaseException;
}
