package com.cainiao.waybill.bridge.biz.label.manager;

import java.util.List;

import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.label.dto.request.BranchSellerAddRelQueryRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.CreateBranchSellerAddRelRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.ReplaceCourierRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.UnbindCourierRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.UpdateSettlementTypeRequest;
import com.cainiao.waybill.bridge.common.util.Page;
import com.cainiao.waybill.bridge.model.domain.BranchSellerAddressDO;

/**
 * Description: 网点-商家地址-小件员绑定关系 manager
 *
 * <AUTHOR>
 * @Date 2017-04-28
 */
public interface BranchSellerAddressRelationManager {
	
	/**
	 * 根据id查询商家地址小件员绑定关系
	 * 
	 * @param relationId
	 * @return
	 * @throws BridgeBaseException
	 */
	BranchSellerAddressDO getRelationByRelationId(Long relationId) throws BridgeBaseException;
	
    /**
     * 网点查询商家地址小件员绑定关系
     *
     * @param request
     * @return
     * @throws BridgeBaseException
     */
    Page<BranchSellerAddressDO> queryRelationsForBranch(BranchSellerAddRelQueryRequest request) throws BridgeBaseException;

    /**
     * 商家查询和网点小件员的绑定关系
     * @param sellerId
     * @return
     * @throws BridgeBaseException
     */
    List<BranchSellerAddressDO> queryRelationsForSeller(Long sellerId) throws BridgeBaseException;

    /**
     * 创建绑定关系
     * @param request
     * @param modifier 操作人
     * @throws BridgeBaseException
     */
    Long createRelation(CreateBranchSellerAddRelRequest request, String modifier) throws BridgeBaseException;

    /**
     * 解绑小件员
     * @param request
     * @param modifier
     * @throws BridgeBaseException
     */
    void unbindCourier(UnbindCourierRequest request, String modifier) throws BridgeBaseException;

    /**
     * 换绑小件员
     * @param request
     * @param modifier
     * @throws BridgeBaseException
     */
    void replaceCourier(ReplaceCourierRequest request, String modifier) throws BridgeBaseException;

    /**
     * 修改客户的结算类型
     * @param request
     * @param modifier
     * @throws BridgeBaseException
     */
    void updateSettlementType(UpdateSettlementTypeRequest request, String modifier) throws BridgeBaseException;

    /**
     * 生效客户的结算类型,dts任务使用
     * @param id
     * @param feature
     * @throws BridgeBaseException
     */
    void activeSettlementType(long id, String feature) throws BridgeBaseException;

    /**
     * 统计dts定期更新的总数
     * @return
     * @throws BridgeBaseException
     */
    int countForDtsSettlementTypeUpdate() throws BridgeBaseException ;

    /**
     * 分页获取dts定期更新的列表
     * @param start
     * @param pageSize
     * @return
     * @throws BridgeBaseException
     */
    List<BranchSellerAddressDO> queryForDtsSettlementTypeUpdate(int start, int pageSize) throws BridgeBaseException ;
}
