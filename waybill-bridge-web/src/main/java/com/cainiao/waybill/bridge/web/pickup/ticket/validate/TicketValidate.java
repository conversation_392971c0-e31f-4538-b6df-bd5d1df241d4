package com.cainiao.waybill.bridge.web.pickup.ticket.validate;

import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpTicketQueryRequest;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;

/**
 * <AUTHOR> zouping.fzp
 * @Classname TicketValidate
 * @Description
 * @Date 2022/11/29 11:14 上午
 * @Version 1.0
 */
public class TicketValidate {

    public static void validateQueryParam(PickUpTicketQueryRequest queryRequest) throws BridgeValidationException {
        if(queryRequest == null){
            throw new BridgeValidationException("params_empty", "请求参数为空");
        }
    }

    public static <T> void assertNotNull(T t, String errorMsg) throws BridgeValidationException {
        if(t == null){
            throw new BridgeValidationException("params_error", errorMsg);
        }
    }
}
