package com.cainiao.waybill.bridge.common.util;

import java.util.regex.Pattern;

import com.cainiao.waybill.common.util.StringUtil;

/**
 * 手机号工具类型
 * <AUTHOR> zouping.fzp
 * @Classname MobileUtil
 * @Description
 * @Date 2023/9/18 7:42 PM
 * @Version 1.0
 */
public class MobileUtil {


    private final static Pattern MOBILE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 判断是否是手机号
     * @param mobile
     * @return
     */
    public static boolean isMobile(String mobile){
        if(StringUtil.isBlank(mobile)){
            return false;
        }
        return MOBILE_PATTERN.matcher(mobile).matches();
    }
}
