package com.cainiao.waybill.bridge.enterprise.authority.request;

import com.cainiao.waybill.bridge.enterprise.common.BaseRequest;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePlatformEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 该类用于处理访问令牌的响应结果。
 * <AUTHOR>
 * @date 2025-04-15 11:01:40
 */
@Data
public class LoginRequest extends BaseRequest implements Serializable {
    /**
     * salt加密盐
     */
    private String salt;
}
