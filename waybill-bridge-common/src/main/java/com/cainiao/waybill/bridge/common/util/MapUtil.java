package com.cainiao.waybill.bridge.common.util;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * <AUTHOR> zouping.fzp
 * @Classname ListUtil
 * @Description
 * @Date 2022/8/3 5:03 下午
 * @Version 1.0
 */
public class MapUtil {


    public static <T, K> K get(Map<T, K> map, T t) {
        if (map == null || t == null) {
            return null;
        }
        return map.get(t);
    }

    public static <K, V> Set<Entry<K,V>> entrySet(Map<K, V> map) {
        if (map == null) {
            map = new HashMap<>();
        }
        return map.entrySet();
    }
}
