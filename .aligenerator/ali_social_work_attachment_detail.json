// 右键菜单，可以一键生成哦。
// 配置格式参见 https://yuque.antfin-inc.com/xd6pvi/dr962p 
{
	"tableName":"ali_social_work_attachment_detail",
	"domainObjectName":"AliSocialWorkAttachmentDetail",
	"mapperName":"AliSocialWorkAttachmentDetailDAO",
	"appName":"waybill_bridge",
	"groupKey":"",
	"split":false,
	"pattern":"_[0-9]+$",
	"anno":true,
	"antZdal":false,
	"appList":[
		"waybill_bridge$",
		"cainiao_waybill_bridge@11.158.231.96:3425 [kuka_6a049b6b1509490b9e7d074d77a5c69f]$",
		"waybill-bridge$"
	],
	"blobEnable":false,
	"businessPackageName":"com.cainiao.waybill.bridge.alisocial.model.test",
	"businessTargetProject":"waybill-bridge-aliSocialWork/src/main/java",
	"catlog":"",
	"columnName":"id",
	"connectionURL":"",
	"createBaseType":false,
	"disableNamespace":false,
	"driverClass":"",
	"enableDeleteMethod":true,
	"fileEncode":"UTF-8",
	"generateBusiness":true,
	"generateKey":true,
	"ignoreColumn":false,
	"ignoreColumnValue":"id",
	"javaClientGeneratorTargetPackage":"com.cainiao.waybill.bridge.alisocial.model.test",
	"javaClientGeneratorTargetProject":"waybill-bridge-aliSocialWork/src/main/java",
	"javaClientGeneratorType":"XMLMAPPER",
	"javaModelGeneratorTargetPackage":"com.cainiao.waybill.bridge.alisocial.model.test",
	"javaModelGeneratorTargetProject":"waybill-bridge-aliSocialWork/src/main/java",
	"lastBizPackage":"com.cainiao.waybill.bridge.alisocial.model.test",
	"lastBizProjectPath":"waybill-bridge-aliSocialWork/src/main/java",
	"lastTypeHadler":"",
	"lastTypeJava":"",
	"mergePolicy":1,
	"modelType":"flat",
	"myDataBase":{
		"tables":[
			{
				"columns":[
					{
						"autoIncrement":true,
						"generatedColumn":false,
						"length":0,
						"name":"id",
						"scale":0,
						"comment":"主键",
						"type":"bigint",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"gmt_create",
						"scale":0,
						"comment":"创建时间",
						"type":"datetime",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"gmt_modified",
						"scale":0,
						"comment":"修改时间",
						"type":"datetime",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"relief_project_id",
						"scale":0,
						"comment":"所属项目id",
						"type":"bigint",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":64,
						"name":"name",
						"scale":0,
						"comment":"附件资料名称",
						"type":"varchar(64)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"executing_agency_id",
						"scale":0,
						"comment":"上传执行机构",
						"type":"bigint",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"scene",
						"scale":0,
						"comment":"资料场景",
						"type":"int",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":512,
						"name":"remarks",
						"scale":0,
						"comment":"备注",
						"type":"varchar(512)",
						"enableNull":true
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":512,
						"name":"url",
						"scale":0,
						"comment":"资料地址",
						"type":"varchar(512)",
						"enableNull":true
					}
				],
				"name":"ali_social_work_attachment_detail",
				"comment":"项目资料详情",
				"primaryKey":"id"
			}
		],
		"name":""
	},
	"password":"",
	"replaceIs":false,
	"saveParameter":true,
	"saveParameterPath":".aligenerator",
	"security":true,
	"serializable":true,
	"simpleMethod":true,
	"sqlMapGeneratorTargetPackage":"com.cainiao.waybill.bridge.alisocial.model.mapper",
	"sqlMapGeneratorTargetProject":"waybill-bridge-aliSocialWork/src/main/java",
	"sqlStatement":"JDBC",
	"tableSchema":"",
	"targetRuntime":"MyBatis3",
	"tddl":true,
	"tddlMap":{
		"cainiao_waybill_bridge@11.158.231.96:3425 [kuka_6a049b6b1509490b9e7d074d77a5c69f]":"",
		"waybill-bridge":"",
		"waybill_bridge":""
	},
	"typeHandlerMap":{},
	"typeMap":{},
	"useBaseDao":false,
	"useDBTime":false,
	"useDao":true,
	"useLombok":true,
	"useSqlCreate":true,
	"userId":"",
	"whenPresent":true,
	"generatedBy":"Aligenerator",
	"schemaVersion":"1.0"
}