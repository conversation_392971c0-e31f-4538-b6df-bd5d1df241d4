package com.cainiao.waybill.bridge.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class QuoteConfigInfoParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public QuoteConfigInfoParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public QuoteConfigInfoParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public QuoteConfigInfoParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public QuoteConfigInfoParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public QuoteConfigInfoParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public QuoteConfigInfoParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andQuoteNameIsNull() {
            addCriterion("quote_name is null");
            return (Criteria) this;
        }

        public Criteria andQuoteNameIsNotNull() {
            addCriterion("quote_name is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteNameEqualTo(String value) {
            addCriterion("quote_name =", value, "quoteName");
            return (Criteria) this;
        }

        public Criteria andQuoteNameNotEqualTo(String value) {
            addCriterion("quote_name <>", value, "quoteName");
            return (Criteria) this;
        }

        public Criteria andQuoteNameGreaterThan(String value) {
            addCriterion("quote_name >", value, "quoteName");
            return (Criteria) this;
        }

        public Criteria andQuoteNameGreaterThanOrEqualTo(String value) {
            addCriterion("quote_name >=", value, "quoteName");
            return (Criteria) this;
        }

        public Criteria andQuoteNameLessThan(String value) {
            addCriterion("quote_name <", value, "quoteName");
            return (Criteria) this;
        }

        public Criteria andQuoteNameLessThanOrEqualTo(String value) {
            addCriterion("quote_name <=", value, "quoteName");
            return (Criteria) this;
        }

        public Criteria andQuoteNameLike(String value) {
            addCriterion("quote_name like", value, "quoteName");
            return (Criteria) this;
        }

        public Criteria andQuoteNameNotLike(String value) {
            addCriterion("quote_name not like", value, "quoteName");
            return (Criteria) this;
        }

        public Criteria andQuoteNameIn(List<String> values) {
            addCriterion("quote_name in", values, "quoteName");
            return (Criteria) this;
        }

        public Criteria andQuoteNameNotIn(List<String> values) {
            addCriterion("quote_name not in", values, "quoteName");
            return (Criteria) this;
        }

        public Criteria andQuoteNameBetween(String value1, String value2) {
            addCriterion("quote_name between", value1, value2, "quoteName");
            return (Criteria) this;
        }

        public Criteria andQuoteNameNotBetween(String value1, String value2) {
            addCriterion("quote_name not between", value1, value2, "quoteName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIsNull() {
            addCriterion("owner_name is null");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIsNotNull() {
            addCriterion("owner_name is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerNameEqualTo(String value) {
            addCriterion("owner_name =", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotEqualTo(String value) {
            addCriterion("owner_name <>", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameGreaterThan(String value) {
            addCriterion("owner_name >", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameGreaterThanOrEqualTo(String value) {
            addCriterion("owner_name >=", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLessThan(String value) {
            addCriterion("owner_name <", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLessThanOrEqualTo(String value) {
            addCriterion("owner_name <=", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameLike(String value) {
            addCriterion("owner_name like", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotLike(String value) {
            addCriterion("owner_name not like", value, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameIn(List<String> values) {
            addCriterion("owner_name in", values, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotIn(List<String> values) {
            addCriterion("owner_name not in", values, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameBetween(String value1, String value2) {
            addCriterion("owner_name between", value1, value2, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotBetween(String value1, String value2) {
            addCriterion("owner_name not between", value1, value2, "ownerName");
            return (Criteria) this;
        }

        public Criteria andOwnerIdIsNull() {
            addCriterion("owner_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIdIsNotNull() {
            addCriterion("owner_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerIdEqualTo(Long value) {
            addCriterion("owner_id =", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotEqualTo(Long value) {
            addCriterion("owner_id <>", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdGreaterThan(Long value) {
            addCriterion("owner_id >", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdGreaterThanOrEqualTo(Long value) {
            addCriterion("owner_id >=", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdLessThan(Long value) {
            addCriterion("owner_id <", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdLessThanOrEqualTo(Long value) {
            addCriterion("owner_id <=", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdIn(List<Long> values) {
            addCriterion("owner_id in", values, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotIn(List<Long> values) {
            addCriterion("owner_id not in", values, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdBetween(Long value1, Long value2) {
            addCriterion("owner_id between", value1, value2, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotBetween(Long value1, Long value2) {
            addCriterion("owner_id not between", value1, value2, "ownerId");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeIsNull() {
            addCriterion("quote_type is null");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeIsNotNull() {
            addCriterion("quote_type is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeEqualTo(String value) {
            addCriterion("quote_type =", value, "quoteType");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeNotEqualTo(String value) {
            addCriterion("quote_type <>", value, "quoteType");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeGreaterThan(String value) {
            addCriterion("quote_type >", value, "quoteType");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeGreaterThanOrEqualTo(String value) {
            addCriterion("quote_type >=", value, "quoteType");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeLessThan(String value) {
            addCriterion("quote_type <", value, "quoteType");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeLessThanOrEqualTo(String value) {
            addCriterion("quote_type <=", value, "quoteType");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeLike(String value) {
            addCriterion("quote_type like", value, "quoteType");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeNotLike(String value) {
            addCriterion("quote_type not like", value, "quoteType");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeIn(List<String> values) {
            addCriterion("quote_type in", values, "quoteType");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeNotIn(List<String> values) {
            addCriterion("quote_type not in", values, "quoteType");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeBetween(String value1, String value2) {
            addCriterion("quote_type between", value1, value2, "quoteType");
            return (Criteria) this;
        }

        public Criteria andQuoteTypeNotBetween(String value1, String value2) {
            addCriterion("quote_type not between", value1, value2, "quoteType");
            return (Criteria) this;
        }

        public Criteria andCpCodeIsNull() {
            addCriterion("cp_code is null");
            return (Criteria) this;
        }

        public Criteria andCpCodeIsNotNull() {
            addCriterion("cp_code is not null");
            return (Criteria) this;
        }

        public Criteria andCpCodeEqualTo(String value) {
            addCriterion("cp_code =", value, "cpCode");
            return (Criteria) this;
        }

        public Criteria andCpCodeNotEqualTo(String value) {
            addCriterion("cp_code <>", value, "cpCode");
            return (Criteria) this;
        }

        public Criteria andCpCodeGreaterThan(String value) {
            addCriterion("cp_code >", value, "cpCode");
            return (Criteria) this;
        }

        public Criteria andCpCodeGreaterThanOrEqualTo(String value) {
            addCriterion("cp_code >=", value, "cpCode");
            return (Criteria) this;
        }

        public Criteria andCpCodeLessThan(String value) {
            addCriterion("cp_code <", value, "cpCode");
            return (Criteria) this;
        }

        public Criteria andCpCodeLessThanOrEqualTo(String value) {
            addCriterion("cp_code <=", value, "cpCode");
            return (Criteria) this;
        }

        public Criteria andCpCodeLike(String value) {
            addCriterion("cp_code like", value, "cpCode");
            return (Criteria) this;
        }

        public Criteria andCpCodeNotLike(String value) {
            addCriterion("cp_code not like", value, "cpCode");
            return (Criteria) this;
        }

        public Criteria andCpCodeIn(List<String> values) {
            addCriterion("cp_code in", values, "cpCode");
            return (Criteria) this;
        }

        public Criteria andCpCodeNotIn(List<String> values) {
            addCriterion("cp_code not in", values, "cpCode");
            return (Criteria) this;
        }

        public Criteria andCpCodeBetween(String value1, String value2) {
            addCriterion("cp_code between", value1, value2, "cpCode");
            return (Criteria) this;
        }

        public Criteria andCpCodeNotBetween(String value1, String value2) {
            addCriterion("cp_code not between", value1, value2, "cpCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeIsNull() {
            addCriterion("product_code is null");
            return (Criteria) this;
        }

        public Criteria andProductCodeIsNotNull() {
            addCriterion("product_code is not null");
            return (Criteria) this;
        }

        public Criteria andProductCodeEqualTo(String value) {
            addCriterion("product_code =", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotEqualTo(String value) {
            addCriterion("product_code <>", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeGreaterThan(String value) {
            addCriterion("product_code >", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeGreaterThanOrEqualTo(String value) {
            addCriterion("product_code >=", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeLessThan(String value) {
            addCriterion("product_code <", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeLessThanOrEqualTo(String value) {
            addCriterion("product_code <=", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeLike(String value) {
            addCriterion("product_code like", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotLike(String value) {
            addCriterion("product_code not like", value, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeIn(List<String> values) {
            addCriterion("product_code in", values, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotIn(List<String> values) {
            addCriterion("product_code not in", values, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeBetween(String value1, String value2) {
            addCriterion("product_code between", value1, value2, "productCode");
            return (Criteria) this;
        }

        public Criteria andProductCodeNotBetween(String value1, String value2) {
            addCriterion("product_code not between", value1, value2, "productCode");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeIsNull() {
            addCriterion("settlement_type is null");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeIsNotNull() {
            addCriterion("settlement_type is not null");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeEqualTo(String value) {
            addCriterion("settlement_type =", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotEqualTo(String value) {
            addCriterion("settlement_type <>", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeGreaterThan(String value) {
            addCriterion("settlement_type >", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeGreaterThanOrEqualTo(String value) {
            addCriterion("settlement_type >=", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeLessThan(String value) {
            addCriterion("settlement_type <", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeLessThanOrEqualTo(String value) {
            addCriterion("settlement_type <=", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeLike(String value) {
            addCriterion("settlement_type like", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotLike(String value) {
            addCriterion("settlement_type not like", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeIn(List<String> values) {
            addCriterion("settlement_type in", values, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotIn(List<String> values) {
            addCriterion("settlement_type not in", values, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeBetween(String value1, String value2) {
            addCriterion("settlement_type between", value1, value2, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotBetween(String value1, String value2) {
            addCriterion("settlement_type not between", value1, value2, "settlementType");
            return (Criteria) this;
        }

        public Criteria andFeeTypeIsNull() {
            addCriterion("fee_type is null");
            return (Criteria) this;
        }

        public Criteria andFeeTypeIsNotNull() {
            addCriterion("fee_type is not null");
            return (Criteria) this;
        }

        public Criteria andFeeTypeEqualTo(String value) {
            addCriterion("fee_type =", value, "feeType");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNotEqualTo(String value) {
            addCriterion("fee_type <>", value, "feeType");
            return (Criteria) this;
        }

        public Criteria andFeeTypeGreaterThan(String value) {
            addCriterion("fee_type >", value, "feeType");
            return (Criteria) this;
        }

        public Criteria andFeeTypeGreaterThanOrEqualTo(String value) {
            addCriterion("fee_type >=", value, "feeType");
            return (Criteria) this;
        }

        public Criteria andFeeTypeLessThan(String value) {
            addCriterion("fee_type <", value, "feeType");
            return (Criteria) this;
        }

        public Criteria andFeeTypeLessThanOrEqualTo(String value) {
            addCriterion("fee_type <=", value, "feeType");
            return (Criteria) this;
        }

        public Criteria andFeeTypeLike(String value) {
            addCriterion("fee_type like", value, "feeType");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNotLike(String value) {
            addCriterion("fee_type not like", value, "feeType");
            return (Criteria) this;
        }

        public Criteria andFeeTypeIn(List<String> values) {
            addCriterion("fee_type in", values, "feeType");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNotIn(List<String> values) {
            addCriterion("fee_type not in", values, "feeType");
            return (Criteria) this;
        }

        public Criteria andFeeTypeBetween(String value1, String value2) {
            addCriterion("fee_type between", value1, value2, "feeType");
            return (Criteria) this;
        }

        public Criteria andFeeTypeNotBetween(String value1, String value2) {
            addCriterion("fee_type not between", value1, value2, "feeType");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andApproveStatusIsNull() {
            addCriterion("approve_status is null");
            return (Criteria) this;
        }

        public Criteria andApproveStatusIsNotNull() {
            addCriterion("approve_status is not null");
            return (Criteria) this;
        }

        public Criteria andApproveStatusEqualTo(String value) {
            addCriterion("approve_status =", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotEqualTo(String value) {
            addCriterion("approve_status <>", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusGreaterThan(String value) {
            addCriterion("approve_status >", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusGreaterThanOrEqualTo(String value) {
            addCriterion("approve_status >=", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusLessThan(String value) {
            addCriterion("approve_status <", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusLessThanOrEqualTo(String value) {
            addCriterion("approve_status <=", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusLike(String value) {
            addCriterion("approve_status like", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotLike(String value) {
            addCriterion("approve_status not like", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusIn(List<String> values) {
            addCriterion("approve_status in", values, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotIn(List<String> values) {
            addCriterion("approve_status not in", values, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusBetween(String value1, String value2) {
            addCriterion("approve_status between", value1, value2, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotBetween(String value1, String value2) {
            addCriterion("approve_status not between", value1, value2, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartIsNull() {
            addCriterion("effective_time_start is null");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartIsNotNull() {
            addCriterion("effective_time_start is not null");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartEqualTo(Date value) {
            addCriterion("effective_time_start =", value, "effectiveTimeStart");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartNotEqualTo(Date value) {
            addCriterion("effective_time_start <>", value, "effectiveTimeStart");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartGreaterThan(Date value) {
            addCriterion("effective_time_start >", value, "effectiveTimeStart");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartGreaterThanOrEqualTo(Date value) {
            addCriterion("effective_time_start >=", value, "effectiveTimeStart");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartLessThan(Date value) {
            addCriterion("effective_time_start <", value, "effectiveTimeStart");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartLessThanOrEqualTo(Date value) {
            addCriterion("effective_time_start <=", value, "effectiveTimeStart");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartIn(List<Date> values) {
            addCriterion("effective_time_start in", values, "effectiveTimeStart");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartNotIn(List<Date> values) {
            addCriterion("effective_time_start not in", values, "effectiveTimeStart");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartBetween(Date value1, Date value2) {
            addCriterion("effective_time_start between", value1, value2, "effectiveTimeStart");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartNotBetween(Date value1, Date value2) {
            addCriterion("effective_time_start not between", value1, value2, "effectiveTimeStart");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndIsNull() {
            addCriterion("effective_time_end is null");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndIsNotNull() {
            addCriterion("effective_time_end is not null");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndEqualTo(Date value) {
            addCriterion("effective_time_end =", value, "effectiveTimeEnd");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndNotEqualTo(Date value) {
            addCriterion("effective_time_end <>", value, "effectiveTimeEnd");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndGreaterThan(Date value) {
            addCriterion("effective_time_end >", value, "effectiveTimeEnd");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndGreaterThanOrEqualTo(Date value) {
            addCriterion("effective_time_end >=", value, "effectiveTimeEnd");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndLessThan(Date value) {
            addCriterion("effective_time_end <", value, "effectiveTimeEnd");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndLessThanOrEqualTo(Date value) {
            addCriterion("effective_time_end <=", value, "effectiveTimeEnd");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndIn(List<Date> values) {
            addCriterion("effective_time_end in", values, "effectiveTimeEnd");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndNotIn(List<Date> values) {
            addCriterion("effective_time_end not in", values, "effectiveTimeEnd");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndBetween(Date value1, Date value2) {
            addCriterion("effective_time_end between", value1, value2, "effectiveTimeEnd");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndNotBetween(Date value1, Date value2) {
            addCriterion("effective_time_end not between", value1, value2, "effectiveTimeEnd");
            return (Criteria) this;
        }

        public Criteria andFileUrlIsNull() {
            addCriterion("file_url is null");
            return (Criteria) this;
        }

        public Criteria andFileUrlIsNotNull() {
            addCriterion("file_url is not null");
            return (Criteria) this;
        }

        public Criteria andFileUrlEqualTo(String value) {
            addCriterion("file_url =", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlNotEqualTo(String value) {
            addCriterion("file_url <>", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlGreaterThan(String value) {
            addCriterion("file_url >", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlGreaterThanOrEqualTo(String value) {
            addCriterion("file_url >=", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlLessThan(String value) {
            addCriterion("file_url <", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlLessThanOrEqualTo(String value) {
            addCriterion("file_url <=", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlLike(String value) {
            addCriterion("file_url like", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlNotLike(String value) {
            addCriterion("file_url not like", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlIn(List<String> values) {
            addCriterion("file_url in", values, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlNotIn(List<String> values) {
            addCriterion("file_url not in", values, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlBetween(String value1, String value2) {
            addCriterion("file_url between", value1, value2, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlNotBetween(String value1, String value2) {
            addCriterion("file_url not between", value1, value2, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFeatureIsNull() {
            addCriterion("feature is null");
            return (Criteria) this;
        }

        public Criteria andFeatureIsNotNull() {
            addCriterion("feature is not null");
            return (Criteria) this;
        }

        public Criteria andFeatureEqualTo(String value) {
            addCriterion("feature =", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotEqualTo(String value) {
            addCriterion("feature <>", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThan(String value) {
            addCriterion("feature >", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanOrEqualTo(String value) {
            addCriterion("feature >=", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLessThan(String value) {
            addCriterion("feature <", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanOrEqualTo(String value) {
            addCriterion("feature <=", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLike(String value) {
            addCriterion("feature like", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotLike(String value) {
            addCriterion("feature not like", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureIn(List<String> values) {
            addCriterion("feature in", values, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotIn(List<String> values) {
            addCriterion("feature not in", values, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureBetween(String value1, String value2) {
            addCriterion("feature between", value1, value2, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotBetween(String value1, String value2) {
            addCriterion("feature not between", value1, value2, "feature");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("is_delete is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("is_delete is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(String value) {
            addCriterion("is_delete =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(String value) {
            addCriterion("is_delete <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(String value) {
            addCriterion("is_delete >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(String value) {
            addCriterion("is_delete >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(String value) {
            addCriterion("is_delete <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(String value) {
            addCriterion("is_delete <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLike(String value) {
            addCriterion("is_delete like", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotLike(String value) {
            addCriterion("is_delete not like", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<String> values) {
            addCriterion("is_delete in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<String> values) {
            addCriterion("is_delete not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(String value1, String value2) {
            addCriterion("is_delete between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(String value1, String value2) {
            addCriterion("is_delete not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andQuoteKeyIsNull() {
            addCriterion("quote_key is null");
            return (Criteria) this;
        }

        public Criteria andQuoteKeyIsNotNull() {
            addCriterion("quote_key is not null");
            return (Criteria) this;
        }

        public Criteria andQuoteKeyEqualTo(Long value) {
            addCriterion("quote_key =", value, "quoteKey");
            return (Criteria) this;
        }

        public Criteria andQuoteKeyNotEqualTo(Long value) {
            addCriterion("quote_key <>", value, "quoteKey");
            return (Criteria) this;
        }

        public Criteria andQuoteKeyGreaterThan(Long value) {
            addCriterion("quote_key >", value, "quoteKey");
            return (Criteria) this;
        }

        public Criteria andQuoteKeyGreaterThanOrEqualTo(Long value) {
            addCriterion("quote_key >=", value, "quoteKey");
            return (Criteria) this;
        }

        public Criteria andQuoteKeyLessThan(Long value) {
            addCriterion("quote_key <", value, "quoteKey");
            return (Criteria) this;
        }

        public Criteria andQuoteKeyLessThanOrEqualTo(Long value) {
            addCriterion("quote_key <=", value, "quoteKey");
            return (Criteria) this;
        }

        public Criteria andQuoteKeyIn(List<Long> values) {
            addCriterion("quote_key in", values, "quoteKey");
            return (Criteria) this;
        }

        public Criteria andQuoteKeyNotIn(List<Long> values) {
            addCriterion("quote_key not in", values, "quoteKey");
            return (Criteria) this;
        }

        public Criteria andQuoteKeyBetween(Long value1, Long value2) {
            addCriterion("quote_key between", value1, value2, "quoteKey");
            return (Criteria) this;
        }

        public Criteria andQuoteKeyNotBetween(Long value1, Long value2) {
            addCriterion("quote_key not between", value1, value2, "quoteKey");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteNameEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_name =", value, "quoteName");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteNameNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_name <>", value, "quoteName");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteNameGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_name >", value, "quoteName");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteNameGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_name >=", value, "quoteName");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteNameLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_name <", value, "quoteName");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteNameLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_name <=", value, "quoteName");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteNameLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_name like", value, "quoteName");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteNameNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_name not like", value, "quoteName");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteNameInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("quote_name in", values, "quoteName");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteNameNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("quote_name not in", values, "quoteName");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteNameBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("quote_name between", value1, value2, "quoteName");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteNameNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("quote_name not between", value1, value2, "quoteName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerNameEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("owner_name =", value, "ownerName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("owner_name <>", value, "ownerName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerNameGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("owner_name >", value, "ownerName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerNameGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("owner_name >=", value, "ownerName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerNameLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("owner_name <", value, "ownerName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerNameLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("owner_name <=", value, "ownerName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerNameLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("owner_name like", value, "ownerName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("owner_name not like", value, "ownerName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerNameInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("owner_name in", values, "ownerName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("owner_name not in", values, "ownerName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerNameBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("owner_name between", value1, value2, "ownerName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerNameNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("owner_name not between", value1, value2, "ownerName");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("owner_id =", value, "ownerId");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("owner_id <>", value, "ownerId");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("owner_id >", value, "ownerId");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("owner_id >=", value, "ownerId");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("owner_id <", value, "ownerId");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("owner_id <=", value, "ownerId");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("owner_id in", values, "ownerId");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("owner_id not in", values, "ownerId");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("owner_id between", value1, value2, "ownerId");
            }
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("owner_id not between", value1, value2, "ownerId");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteTypeEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_type =", value, "quoteType");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteTypeNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_type <>", value, "quoteType");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteTypeGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_type >", value, "quoteType");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteTypeGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_type >=", value, "quoteType");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteTypeLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_type <", value, "quoteType");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteTypeLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_type <=", value, "quoteType");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteTypeLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_type like", value, "quoteType");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteTypeNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("quote_type not like", value, "quoteType");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteTypeInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("quote_type in", values, "quoteType");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteTypeNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("quote_type not in", values, "quoteType");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteTypeBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("quote_type between", value1, value2, "quoteType");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteTypeNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("quote_type not between", value1, value2, "quoteType");
            }
            return (Criteria) this;
        }

        public Criteria andCpCodeEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("cp_code =", value, "cpCode");
            }
            return (Criteria) this;
        }

        public Criteria andCpCodeNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("cp_code <>", value, "cpCode");
            }
            return (Criteria) this;
        }

        public Criteria andCpCodeGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("cp_code >", value, "cpCode");
            }
            return (Criteria) this;
        }

        public Criteria andCpCodeGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("cp_code >=", value, "cpCode");
            }
            return (Criteria) this;
        }

        public Criteria andCpCodeLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("cp_code <", value, "cpCode");
            }
            return (Criteria) this;
        }

        public Criteria andCpCodeLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("cp_code <=", value, "cpCode");
            }
            return (Criteria) this;
        }

        public Criteria andCpCodeLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("cp_code like", value, "cpCode");
            }
            return (Criteria) this;
        }

        public Criteria andCpCodeNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("cp_code not like", value, "cpCode");
            }
            return (Criteria) this;
        }

        public Criteria andCpCodeInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("cp_code in", values, "cpCode");
            }
            return (Criteria) this;
        }

        public Criteria andCpCodeNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("cp_code not in", values, "cpCode");
            }
            return (Criteria) this;
        }

        public Criteria andCpCodeBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("cp_code between", value1, value2, "cpCode");
            }
            return (Criteria) this;
        }

        public Criteria andCpCodeNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("cp_code not between", value1, value2, "cpCode");
            }
            return (Criteria) this;
        }

        public Criteria andProductCodeEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("product_code =", value, "productCode");
            }
            return (Criteria) this;
        }

        public Criteria andProductCodeNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("product_code <>", value, "productCode");
            }
            return (Criteria) this;
        }

        public Criteria andProductCodeGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("product_code >", value, "productCode");
            }
            return (Criteria) this;
        }

        public Criteria andProductCodeGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("product_code >=", value, "productCode");
            }
            return (Criteria) this;
        }

        public Criteria andProductCodeLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("product_code <", value, "productCode");
            }
            return (Criteria) this;
        }

        public Criteria andProductCodeLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("product_code <=", value, "productCode");
            }
            return (Criteria) this;
        }

        public Criteria andProductCodeLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("product_code like", value, "productCode");
            }
            return (Criteria) this;
        }

        public Criteria andProductCodeNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("product_code not like", value, "productCode");
            }
            return (Criteria) this;
        }

        public Criteria andProductCodeInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("product_code in", values, "productCode");
            }
            return (Criteria) this;
        }

        public Criteria andProductCodeNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("product_code not in", values, "productCode");
            }
            return (Criteria) this;
        }

        public Criteria andProductCodeBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("product_code between", value1, value2, "productCode");
            }
            return (Criteria) this;
        }

        public Criteria andProductCodeNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("product_code not between", value1, value2, "productCode");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type =", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type <>", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type >", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type >=", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type <", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type <=", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type like", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type not like", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("settlement_type in", values, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("settlement_type not in", values, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("settlement_type between", value1, value2, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("settlement_type not between", value1, value2, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andFeeTypeEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("fee_type =", value, "feeType");
            }
            return (Criteria) this;
        }

        public Criteria andFeeTypeNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("fee_type <>", value, "feeType");
            }
            return (Criteria) this;
        }

        public Criteria andFeeTypeGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("fee_type >", value, "feeType");
            }
            return (Criteria) this;
        }

        public Criteria andFeeTypeGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("fee_type >=", value, "feeType");
            }
            return (Criteria) this;
        }

        public Criteria andFeeTypeLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("fee_type <", value, "feeType");
            }
            return (Criteria) this;
        }

        public Criteria andFeeTypeLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("fee_type <=", value, "feeType");
            }
            return (Criteria) this;
        }

        public Criteria andFeeTypeLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("fee_type like", value, "feeType");
            }
            return (Criteria) this;
        }

        public Criteria andFeeTypeNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("fee_type not like", value, "feeType");
            }
            return (Criteria) this;
        }

        public Criteria andFeeTypeInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("fee_type in", values, "feeType");
            }
            return (Criteria) this;
        }

        public Criteria andFeeTypeNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("fee_type not in", values, "feeType");
            }
            return (Criteria) this;
        }

        public Criteria andFeeTypeBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("fee_type between", value1, value2, "feeType");
            }
            return (Criteria) this;
        }

        public Criteria andFeeTypeNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("fee_type not between", value1, value2, "feeType");
            }
            return (Criteria) this;
        }

        public Criteria andStatusEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("status =", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("status <>", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("status >", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("status >=", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("status <", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("status <=", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("status like", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("status not like", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("status in", values, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("status not in", values, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("status between", value1, value2, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("status not between", value1, value2, "status");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status =", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status <>", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status >", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status >=", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status <", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status <=", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status like", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status not like", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("approve_status in", values, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("approve_status not in", values, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("approve_status between", value1, value2, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("approve_status not between", value1, value2, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time_start =", value, "effectiveTimeStart");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time_start <>", value, "effectiveTimeStart");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time_start >", value, "effectiveTimeStart");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time_start >=", value, "effectiveTimeStart");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time_start <", value, "effectiveTimeStart");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time_start <=", value, "effectiveTimeStart");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("effective_time_start in", values, "effectiveTimeStart");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("effective_time_start not in", values, "effectiveTimeStart");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("effective_time_start between", value1, value2, "effectiveTimeStart");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeStartNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("effective_time_start not between", value1, value2, "effectiveTimeStart");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time_end =", value, "effectiveTimeEnd");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time_end <>", value, "effectiveTimeEnd");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time_end >", value, "effectiveTimeEnd");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time_end >=", value, "effectiveTimeEnd");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time_end <", value, "effectiveTimeEnd");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time_end <=", value, "effectiveTimeEnd");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("effective_time_end in", values, "effectiveTimeEnd");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("effective_time_end not in", values, "effectiveTimeEnd");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("effective_time_end between", value1, value2, "effectiveTimeEnd");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEndNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("effective_time_end not between", value1, value2, "effectiveTimeEnd");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url =", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url <>", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url >", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url >=", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url <", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url <=", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url like", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url not like", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("file_url in", values, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("file_url not in", values, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("file_url between", value1, value2, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("file_url not between", value1, value2, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature =", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature <>", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature >", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature >=", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature <", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature <=", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature like", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature not like", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("feature in", values, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("feature not in", values, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("feature between", value1, value2, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("feature not between", value1, value2, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("is_delete =", value, "isDelete");
            }
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("is_delete <>", value, "isDelete");
            }
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("is_delete >", value, "isDelete");
            }
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("is_delete >=", value, "isDelete");
            }
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("is_delete <", value, "isDelete");
            }
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("is_delete <=", value, "isDelete");
            }
            return (Criteria) this;
        }

        public Criteria andIsDeleteLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("is_delete like", value, "isDelete");
            }
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("is_delete not like", value, "isDelete");
            }
            return (Criteria) this;
        }

        public Criteria andIsDeleteInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("is_delete in", values, "isDelete");
            }
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("is_delete not in", values, "isDelete");
            }
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("is_delete between", value1, value2, "isDelete");
            }
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("is_delete not between", value1, value2, "isDelete");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteKeyEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("quote_key =", value, "quoteKey");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteKeyNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("quote_key <>", value, "quoteKey");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteKeyGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("quote_key >", value, "quoteKey");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteKeyGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("quote_key >=", value, "quoteKey");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteKeyLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("quote_key <", value, "quoteKey");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteKeyLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("quote_key <=", value, "quoteKey");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteKeyInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("quote_key in", values, "quoteKey");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteKeyNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("quote_key not in", values, "quoteKey");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteKeyBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("quote_key between", value1, value2, "quoteKey");
            }
            return (Criteria) this;
        }

        public Criteria andQuoteKeyNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("quote_key not between", value1, value2, "quoteKey");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *报价名称
         */
        QUOTENAME("quote_name"),
        /**
         *归属主体名称
         */
        OWNERNAME("owner_name"),
        /**
         *归属主体ID
         */
        OWNERID("owner_id"),
        /**
         *报价类型
         */
        QUOTETYPE("quote_type"),
        /**
         *CP编码
         */
        CPCODE("cp_code"),
        /**
         *产品编码
         */
        PRODUCTCODE("product_code"),
        /**
         *结算方案类型
         */
        SETTLEMENTTYPE("settlement_type"),
        /**
         *费用项
         */
        FEETYPE("fee_type"),
        /**
         *生效状态
         */
        STATUS("status"),
        /**
         *审批状态
         */
        APPROVESTATUS("approve_status"),
        /**
         *生效起始时间
         */
        EFFECTIVETIMESTART("effective_time_start"),
        /**
         *生效截止时间
         */
        EFFECTIVETIMEEND("effective_time_end"),
        /**
         *报价文件URL
         */
        FILEURL("file_url"),
        /**
         *扩展字段
         */
        FEATURE("feature"),
        /**
         *是否删除
         */
        ISDELETE("is_delete"),
        /**
         *报价配置key
         */
        QUOTEKEY("quote_key");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}