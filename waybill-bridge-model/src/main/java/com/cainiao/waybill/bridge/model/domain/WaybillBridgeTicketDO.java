package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * This class corresponds to the database table waybill_bridge_ticket
 * <AUTHOR>
 * @date 2022/10/08
 */
public class WaybillBridgeTicketDO {
    /**
     *   主键
     *
     * This field corresponds to the database column waybill_bridge_ticket.id
     */
    private Long id;

    /**
     *   创建时间
     *
     * This field corresponds to the database column waybill_bridge_ticket.gmt_create
     */
    private Date gmtCreate;

    /**
     *   修改时间
     *
     * This field corresponds to the database column waybill_bridge_ticket.gmt_modified
     */
    private Date gmtModified;

    /**
     *   物流运单号
     *
     * This field corresponds to the database column waybill_bridge_ticket.mail_no
     */
    private String mailNo;

    /**
     *   入柜时间
     *
     * This field corresponds to the database column waybill_bridge_ticket.into_box_time
     */
    private Date intoBoxTime;

    /**
     *   工单发起方  1：消费者  2：客服小二
     *
     * This field corresponds to the database column waybill_bridge_ticket.operator_type
     */
    private String operatorType;

    /**
     *   工单类型 1：签收核实 2：核实取件码 3：丢失/破损
     *
     * This field corresponds to the database column waybill_bridge_ticket.ticket_type
     */
    private String ticketType;

    /**
     *   工单状态 0:取消 1:待处理 2:处理中 3:完结
     *
     * This field corresponds to the database column waybill_bridge_ticket.ticket_status
     */
    private String ticketStatus;

    /**
     *   工单创建内容
     *
     * This field corresponds to the database column waybill_bridge_ticket.ticket_create_content
     */
    private String ticketCreateContent;

    /**
     *   工单回复内容
     *
     * This field corresponds to the database column waybill_bridge_ticket.ticket_resp_content
     */
    private String ticketRespContent;

    /**
     *   工单附件列表名称
     *
     * This field corresponds to the database column waybill_bridge_ticket.ticket_files
     */
    private String ticketFiles;

    /**
     *   是否进行催单 0:不进行 1:进行
     *
     * This field corresponds to the database column waybill_bridge_ticket.hurry_mark
     */
    private Byte hurryMark;

    /**
     *   扩展字段
     *
     * This field corresponds to the database column waybill_bridge_ticket.feature
     */
    private String feature;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getMailNo() {
        return mailNo;
    }

    public void setMailNo(String mailNo) {
        this.mailNo = mailNo == null ? null : mailNo.trim();
    }

    public Date getIntoBoxTime() {
        return intoBoxTime;
    }

    public void setIntoBoxTime(Date intoBoxTime) {
        this.intoBoxTime = intoBoxTime;
    }

    public String getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(String operatorType) {
        this.operatorType = operatorType == null ? null : operatorType.trim();
    }

    public String getTicketType() {
        return ticketType;
    }

    public void setTicketType(String ticketType) {
        this.ticketType = ticketType == null ? null : ticketType.trim();
    }

    public String getTicketStatus() {
        return ticketStatus;
    }

    public void setTicketStatus(String ticketStatus) {
        this.ticketStatus = ticketStatus == null ? null : ticketStatus.trim();
    }

    public String getTicketCreateContent() {
        return ticketCreateContent;
    }

    public void setTicketCreateContent(String ticketCreateContent) {
        this.ticketCreateContent = ticketCreateContent == null ? null : ticketCreateContent.trim();
    }

    public String getTicketRespContent() {
        return ticketRespContent;
    }

    public void setTicketRespContent(String ticketRespContent) {
        this.ticketRespContent = ticketRespContent == null ? null : ticketRespContent.trim();
    }

    public String getTicketFiles() {
        return ticketFiles;
    }

    public void setTicketFiles(String ticketFiles) {
        this.ticketFiles = ticketFiles == null ? null : ticketFiles.trim();
    }

    public Byte getHurryMark() {
        return hurryMark;
    }

    public void setHurryMark(Byte hurryMark) {
        this.hurryMark = hurryMark;
    }

    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature == null ? null : feature.trim();
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @return String
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", mailNo=").append(mailNo);
        sb.append(", intoBoxTime=").append(intoBoxTime);
        sb.append(", operatorType=").append(operatorType);
        sb.append(", ticketType=").append(ticketType);
        sb.append(", ticketStatus=").append(ticketStatus);
        sb.append(", ticketCreateContent=").append(ticketCreateContent);
        sb.append(", ticketRespContent=").append(ticketRespContent);
        sb.append(", ticketFiles=").append(ticketFiles);
        sb.append(", hurryMark=").append(hurryMark);
        sb.append(", feature=").append(feature);
        sb.append("]");
        return sb.toString();
    }
}