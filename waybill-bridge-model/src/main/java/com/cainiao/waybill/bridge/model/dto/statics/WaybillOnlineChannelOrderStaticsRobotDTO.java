package com.cainiao.waybill.bridge.model.dto.statics;

import java.io.Serializable;

import lombok.Data;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * <AUTHOR> zouping.fzp
 * @Classname WaybillOnlineChannelOrderStaticsDTO
 * @Description
 * @Date 2023/11/10 15:40
 * @Version 1.0
 */
@Data
public class WaybillOnlineChannelOrderStaticsRobotDTO implements Serializable {

    private static final long serialVersionUID = -7619515193945608454L;

    /**
     * 订单渠道
     */
    private String orderChannels;

    /**
     * 订单数量
     */
    private int num;

    /**
     * 应取件数量
     */
    private int shouldGotNum;

    /**
     * 已取件数量
     */
    private int gotedNum;

    /**
     * 取件率
     */
    private String gotRate;

    /**
     * 工单量
     */
    private int ticketNum;

    /**
     * 工单去重量
     */
    private int distinctTicketNum;

    /**
     * 工单率
     */
    private String ticketRate;

    /**
     * 工单去重率
     */
    private String distinctTicketRate;

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}

        if (o == null || getClass() != o.getClass()) {return false;}

        WaybillOnlineChannelOrderStaticsRobotDTO that = (WaybillOnlineChannelOrderStaticsRobotDTO)o;

        return new EqualsBuilder().append(orderChannels, that.orderChannels).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(orderChannels).toHashCode();
    }
}
