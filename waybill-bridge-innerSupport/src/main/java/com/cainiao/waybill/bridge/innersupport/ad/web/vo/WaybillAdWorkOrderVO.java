package com.cainiao.waybill.bridge.innersupport.ad.web.vo;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
* 工单表
* WaybillAdWorkOrder
*/
@Data
public class WaybillAdWorkOrderVO {

    /**
    * 主键
    * type BIGINT
    */
    private Long id;
    /**
    * 创建时间
    * type DATETIME
    */
    private Date gmtCreate;
    /**
    * 更新时间
    * type DATETIME
    */
    private Date gmtModified;
    /**
    * 日期
    * type DATETIME
    */
    private Date workOrderDate;
    /**
    * 运单号
    * type VARCHAR
    */
    private String mailNo;
    /**
    * 电话
    * type VARCHAR
    */
    private String mobile;
    /**
    * 快递公司名称
    * type VARCHAR
    */
    private String cpName;
    /**
    * 快递公司代码
    * type VARCHAR
    */
    private String cpCode;
    /**
    * 广告主名称
    * type VARCHAR
    */
    private String advertiserName;
    /**
    * 广告主id
    * type VARCHAR
    */
    private String advertiserId;
    /**
    * 工单来源，1-快递，2-淘宝商家，3-菜鸟小二
    * type INT
    */
    private Integer workOrderSource;
    /**
    * 是否加急，1是，0否
    * type TINYINT
    */
    private Short isExpedited;
    /**
    * 处理进度，1-处理中，2-已退款，3-已完结(非退款)，4-已完结(异常完结)，5-异常挂起
    * type INT
    */
    private Integer dealProgress;
    /**
    * 是否超时，1是，0否
    * type TINYINT
    */
    private Short isTimeout;
    /**
    * 操作人
    * type VARCHAR
    */
    private String operator;
    /**
    * 包裹扫码截图
    * type JSON
    */
    private List<String> scanCodeScreenshotLinks;
    /**
    * 支付截图
    * type JSON
    */
    private List<String> paymentScreenshotLinks;
    /**
    * 退款截图
    * type JSON
    */
    private List<String> refundScreenshotLinks;
    /**
    * 客诉原声
    * type VARCHAR
    */
    private String customerComplaintContent;
    /**
    * 备注
    * type VARCHAR
    */
    private String remark;
    /**
     * 工单完结时间
     * type DATETIME
     */
    private Date completionTime;
    /**
     * 面单打印时间
     * type DATETIME
     */
    private Date waybillPrintTime;
    /**
     * 创意id
     * type BIGINT
     */
    private Long adId;
    /**
    * 扩展属性字段
    * type VARCHAR
    */
    private String feature;
}