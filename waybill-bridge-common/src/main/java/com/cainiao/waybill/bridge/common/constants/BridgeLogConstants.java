package com.cainiao.waybill.bridge.common.constants;

/**
 * 日志常量类
 *
 */
public class BridgeLogConstants {

    public interface LogAppender {
        /**
         * 应用默认
         */
        String APPLICATION = "APPLICATION";

        /**
         * 短信验证码
         */
        String MOBILE_CODE = "MOBILE_CODE" ;

        /**
         * 电子面单云打印
         */
        String WAYBILL_CLOUD_PRINT = "WAYBILL_CLOUD_PRINT" ;
        /**
         * 千牛消息
         */
        String TPN_MSG = "TPN_MSG" ;

        /**
         * 网点-商家地址-小件员绑定关系管理
         */
        String BRANCH_SELLER_ADDRESS = "BRANCH_SELLER_ADDRESS" ;

        /**
         * 电子面单-揽件码关系管理
         */
        String WAYBILL_PICKUP_CODE = "WAYBILL_PICKUP_CODE" ;
        /**
         * 余额提醒消息
         */
        String ACCOUNT_ALARM = "ACCOUNT_ALARM" ;
        /**
         * 商家协议签署
         */
        String SELLER_AGREEMENT = "SELLER_AGREEMENT" ;

        /**
         * 中间件日志输出
         */
        String MIDDLE_WARE = "MIDDLE_WARE" ;
        
        /**
         * cp调用link接口日志输出
         */
        String LINK_CP = "LINK_CP" ;

        /**
         * 企业寄件
         */
        String WAYBILL_ENTERPRISE = "WAYBILL_ENTERPRISE" ;
    }
}
