package com.cainiao.waybill.bridge.web.charity.validate;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;
import javax.validation.ReportAsSingleViolation;

import com.alibaba.fastvalidator.constraints.validator.IsEnumValidator;

import com.cainiao.waybill.bridge.biz.charity.constant.enums.BaseCodeEnum;

/**
 * <AUTHOR> zouping.fzp
 * @Classname IsEnumValue
 * @Description
 * @Date 2022/8/25 4:57 下午
 * @Version 1.0
 */
@Documented
@Constraint(
    validatedBy = {IsEnumValidator.class}
)
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@ReportAsSingleViolation
public @interface IsEnumValue {
    Class<? extends Enum<? extends BaseCodeEnum>> enumType();

    boolean notNull() default false;

    String message() default "should be {enumType} type";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
