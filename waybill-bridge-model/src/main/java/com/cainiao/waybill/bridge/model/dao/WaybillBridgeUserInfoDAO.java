package com.cainiao.waybill.bridge.model.dao;

import java.util.List;

import com.cainiao.waybill.bridge.model.dao.bean.WaybillBridgeUserInfoParameter;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeUserInfoDTO;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public interface WaybillBridgeUserInfoDAO {

    /**
     * 查询账户
     */
    WaybillBridgeUserInfoDTO find(String phone);

    /**
     * 列表查询
     * @param param
     */
    List<WaybillBridgeUserInfoDTO> list(WaybillBridgeUserInfoParameter param);

    /**
     * 创建
     * @param param
     */
    WaybillBridgeUserInfoDTO create(WaybillBridgeUserInfoParameter param);

    /**
     * 选择性修改
     * @param dto
     * @param param
     */
    void updateSelective(WaybillBridgeUserInfoDTO dto, WaybillBridgeUserInfoParameter param);
}
