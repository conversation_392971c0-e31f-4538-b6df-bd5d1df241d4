package com.cainiao.waybill.bridge.enterprise.post.controller;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.administrators.request.EnterpriseUserRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.GetEnterpriseUserInfoRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.ListAdminRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.UserInfoRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.response.ListAdminResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.response.UserInfoResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.service.DingTalkUserService;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.UserContext;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.post.request.EnterprisePostRequest;
import com.cainiao.waybill.bridge.enterprise.post.service.WaybillEnterprisePostService;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterprisePostDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserInfoDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/enterprise/post")
public class EnterprisePostController {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private WaybillEnterprisePostService waybillEnterprisePostService;


    @PostMapping("/page")
    @Description("分页查询小邮局")
    EnterpriseBaseResult<BridgePagingDTO<WaybillEnterprisePostDTO>> page(@RequestBody EnterprisePostRequest request) {
        LOGGER.info("page request:{}", JSON.toJSONString(request));
        request.setCorpId(UserContext.getUser().getCorpId());
        try {
            return EnterpriseBaseResult.success(waybillEnterprisePostService.pageList(request));
        } catch (Exception e) {
            LOGGER.error("page error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @PostMapping("/addPost")
    @Description("添加小邮局")
    EnterpriseBaseResult<Void> addPost(@RequestBody EnterprisePostRequest request) {
        LOGGER.info("addPost request:{}", JSON.toJSONString(request));
        try {
            waybillEnterprisePostService.addPost(request);
            return EnterpriseBaseResult.success();
        } catch (EnterpriseException e) {
            LOGGER.error("addPost error", e);
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getErrorMessage());
        }
    }


    @PostMapping("/updatePost")
    @Description("修改小邮局")
    EnterpriseBaseResult<Void> updatePost(@RequestBody EnterprisePostRequest request) {
        LOGGER.info("updatePost request:{}", JSON.toJSONString(request));
        try {
            waybillEnterprisePostService.updatePost(request);
            return EnterpriseBaseResult.success();
        } catch (EnterpriseException e) {
            LOGGER.error("updatePost error", e);
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getErrorMessage());
        }
    }


    @GetMapping("/deleteById")
    @Description("删除小邮局")
    @Deprecated
    EnterpriseBaseResult<Boolean> deleteById(@RequestParam("id") Long id) {
        try {
            if (id == null) {
                return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.PARAM_ERROR.code(), "id不能为空");
            }
            waybillEnterprisePostService.deleteById(id);
            return EnterpriseBaseResult.success();
        } catch (Exception e) {
            LOGGER.error("deleteById error", e);
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("/getById")
    @Description("获取小邮局信息")
    @Deprecated
    EnterpriseBaseResult<WaybillEnterprisePostDTO> getById(@RequestParam("id") Long id) {
        try {
            if (id == null) {
                return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.PARAM_ERROR.code(), "id不能为空");
            }
            return EnterpriseBaseResult.success(waybillEnterprisePostService.getById(id));
        } catch (Exception e) {
            LOGGER.error("getById error", e);
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @PostMapping("/getPostList")
    @Description("查询小邮局")
    EnterpriseBaseResult<List<WaybillEnterprisePostDTO>> getPostList(@RequestBody EnterprisePostRequest request) {
        LOGGER.info("getPostList request:{}", JSON.toJSONString(request));
        try {
            return EnterpriseBaseResult.success(waybillEnterprisePostService.getPostList(request));
        } catch (Exception e) {
            LOGGER.error("page error", e);
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("/deletePostByPostId")
    @Description("删除小邮局")
    EnterpriseBaseResult<Boolean> deletePostByPostId(@RequestParam("postId") String postId) {
        try {
            if (StringUtils.isEmpty(postId)) {
                return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.PARAM_ERROR.code(), "postId不能为空");
            }
            waybillEnterprisePostService.deletePostByPostId(postId, UserContext.getUser().getCorpId());
            return EnterpriseBaseResult.success();
        } catch (Exception e) {
            LOGGER.error("deletePostByPostId error:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("/getPostByPostId")
    @Description("获取小邮局信息")
    EnterpriseBaseResult<WaybillEnterprisePostDTO> getPostByPostId(@RequestParam("postId") String postId) {
        try {
            if (StringUtils.isEmpty(postId)) {
                return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.PARAM_ERROR.code(), "postId不能为空");
            }
            return EnterpriseBaseResult.success(waybillEnterprisePostService.getPostByPostId(postId, UserContext.getUser().getCorpId()));
        } catch (Exception e) {
            LOGGER.error("getPostByPostId error:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }
}
