package com.cainiao.waybill.bridge.common.util.monitor;

import java.util.Arrays;

/**
 * 监控埋点key
 * <AUTHOR> on 2019-07-22.
 */
class Key {
    private final String[] keys;
    private final int hash;

    public Key(String key1, String key2, String key3) {
        keys = new String[] {key1, key2, key3};
        hash = hash();
    }

    public Key(String key1, String... moreKeys) {
        String[] keys = new String[1 + moreKeys.length];
        keys[0] = key1;
        System.arraycopy(moreKeys, 0, keys, 1, moreKeys.length);
        this.keys = keys;
        hash = hash();
    }

    private int hash() {
        int result = 1;
        result = 31 * result + Arrays.hashCode(keys);
        return result;
    }

    @Override
    public int hashCode() {
        return hash;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) { return true; }
        if (obj == null) { return false; }
        if (getClass() != obj.getClass()) { return false; }
        Key other = (Key)obj;
        if (hash != 0 && other.hash != 0 && hash != other.hash) { return false; }
        return Arrays.equals(keys, other.keys);
    }

    @Override
    public String toString() {
        return String.join(",", keys) + ',';
    }
}
