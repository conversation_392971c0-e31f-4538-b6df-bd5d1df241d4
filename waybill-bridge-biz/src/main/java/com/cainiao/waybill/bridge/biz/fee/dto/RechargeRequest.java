package com.cainiao.waybill.bridge.biz.fee.dto;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**、
 * 结算重算请求
 * <AUTHOR>
 * @date 2024/7/22 21:11
 **/
@Data
public class RechargeRequest {

    /**
     * 重算任务ID
     */
    @NotBlank(message = "重算任务ID不能为空")
    private String taskId;

    /**
     * 业务单号
     */
    @NotBlank(message = "业务单号不能为空")
    private String orderCode;

    /**
     * 来源系统
     */
    @NotBlank(message = "来源系统不能为空")
    private String sourceSystem;

    /**
     * 服务产品编码
     */
    @NotBlank(message = "服务产品编码不能为空")
    private String serviceItemCode;

    /**
     * 计费节点编码
     */
    @NotBlank(message = "计费节点编码不能为空")
    private String serviceNodeCode;

    /**
     * 用户ID(计费主体ID)
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 上游修改的业务参数键值对，JSON字符串
     */
    @NotBlank(message = "业务参数不能为空")
    private String bizParams;

    /**
     * 需要重算的费用项编码
     */
    @NotBlank(message = "费用项编码不能为空")
    private String feeCode;

    /**
     * 重算一级原因，结算方案提供
     */
    @NotBlank(message = "重算一级原因不能为空")
    private String rechargeReasonLevel1;

    /**
     * 重算二级原因，结算方案提供
     */
    @NotBlank(message = "重算二级原因不能为空")
    private String rechargeReasonLevel2;

    /**
     * 重算三级原因，结算方案提供
     */
    private String rechargeReasonLevel3;

    /**
     * 外部系统唯一健，上游生成
     */
    @NotBlank(message = "外部系统唯一健不能为空")
    private String externalId;

    /**
     * 版本号
     */
    @NotBlank(message = "版本号不能为空")
    private String version;
}
