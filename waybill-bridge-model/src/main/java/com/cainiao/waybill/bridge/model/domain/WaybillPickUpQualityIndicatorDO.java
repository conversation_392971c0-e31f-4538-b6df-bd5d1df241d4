package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

import lombok.Data;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */

/**
 *
 * <AUTHOR>
 */
@Data
public class WaybillPickUpQualityIndicatorDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   业务时间
     *
     *
     * @mbg.generated
     */
    private String bizDate;

    /**
     * Database Column Remarks:
     * 服务商代码
     *
     * @mbg.generated
     */
    private String cpCode;

    /**
     * Database Column Remarks:
     *   指标名称
     *
     *
     * @mbg.generated
     */
    private String indicatorName;

    /**
     * Database Column Remarks:
     *   指标key
     *
     *
     * @mbg.generated
     */
    private String indicatorKey;

    /**
     * Database Column Remarks:
     *   指标率
     *
     *
     * @mbg.generated
     */
    private String indicatorVal;
}
