package com.cainiao.waybill.bridge.enterprise.outbound.controller;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.administrators.request.User;
import com.cainiao.waybill.bridge.enterprise.common.UserContext;
import com.cainiao.waybill.bridge.enterprise.outbound.request.EnterprisePackBoundRequest;
import com.cainiao.waybill.bridge.enterprise.outbound.response.PackCpInfo;
import com.cainiao.waybill.bridge.enterprise.outbound.service.WaybillBridgeEnterprisePackBoundService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeEnterprisePackBoundDTO;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/17
 **/
@RestController
@RequestMapping(value = "/enterprise/express")
public class EnterprisePackBoundController {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private WaybillBridgeEnterprisePackBoundService enterprisePackBoundService;

    @PostMapping("/outbound/page")
    @Description("分页查询到件")
    EnterpriseBaseResult<BridgePagingDTO<WaybillBridgeEnterprisePackBoundDTO>> page(@RequestBody EnterprisePackBoundRequest request) {
        LOGGER.info("page request:{}", JSON.toJSONString(request));
        try {
            User user = UserContext.getUser();
            LOGGER.info("page user:{}", JSON.toJSONString(user));
            if (user == null) {
                return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "用户未登录");
            }
            request.setOperatorUserId(user.getUserId());
            return EnterpriseBaseResult.success(enterprisePackBoundService.outboundPage(request));
        } catch (Exception e) {
            LOGGER.error("page error :{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @PostMapping("/inbound")
    @Description("入库")
    EnterpriseBaseResult<Boolean> inbound(@RequestBody EnterprisePackBoundRequest request) {
        LOGGER.info("inbound request:{}", JSON.toJSONString(request));
        try {
            int inboundResult = enterprisePackBoundService.expressInStore(request);
            return EnterpriseBaseResult.success(inboundResult == 1);
        } catch (Exception e) {
            LOGGER.error("inbound error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @PostMapping("/outbound/batchOut")
    @Description("批量取件")
    EnterpriseBaseResult<List<String>> batchOutbound(@RequestBody EnterprisePackBoundRequest request) {
        LOGGER.info("batchOutbound request:{}", JSON.toJSONString(request));
        try {
            return EnterpriseBaseResult.success(enterprisePackBoundService.batchPickUp(request));
        } catch (Exception e) {
            LOGGER.error("batchOutbound error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("/getCpInfo")
    @Description("获取面单运商信息")
    EnterpriseBaseResult<PackCpInfo> queryPossibleCpInfoByWaybillCode(@RequestParam("waybillCode") String waybillCode) {
        LOGGER.info("queryPossibleCpCodesByWaybillCode request:{}", waybillCode);
        try {
            return EnterpriseBaseResult.success(enterprisePackBoundService.queryPossibleCpCodesByWaybillCode(waybillCode));
        } catch (Exception e) {
            LOGGER.error("{} queryPossibleCpInfoByWaybillCode error: {}", waybillCode, ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("/getCpMapping")
    @Description("获取当前支持入库的CP")
    EnterpriseBaseResult<Map<String, String>> getCpMapping() {
        try {
            return EnterpriseBaseResult.success(enterprisePackBoundService.supportCpMapping());
        } catch (Exception e) {
            LOGGER.error("getCpMapping error", e);
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }
}

