package com.cainiao.waybill.bridge.common.constants;


import com.cainiao.waybill.bridge.common.base.Constants.ConstantCollection;
import com.cainiao.waybill.bridge.common.base.Constants.ErrorConstant;

/**
 * 电子面单所有错误信息集合
 *
 * <AUTHOR>
 */
public class BridgeErrorConstant {

    public static class BaseError extends ConstantCollection {
        public static final ErrorConstant PARAMETER_IS_NULL = newConstant("parameter is null", "入参为null");
        public static final ErrorConstant PARAMETER_ILLEGAL = newConstant("parameter is illegal", "入参为非法");

        public static final ErrorConstant UNKNOWN = newConstant("unknown error", "未知错误");
    }


    public static class ServiceValidate extends ConstantCollection {
        public static final ErrorConstant REQUEST_PARAM_CANNOT_NULL = newConstant("request_param_can_not_null", "参数不完整,不能为空");
        public static final ErrorConstant CLIENT_APP_INFO_CANNOT_NULL = newConstant("clientAppInfo can not be null", "clientAppInfo 参数不能为空！");
        public static final ErrorConstant CLIENT_INFO_DTO_CAN_NOT_NULL = newConstant("clientInfoDTO can not null", "clientInfoDTO 参数不能为空！");
        public static final ErrorConstant APPNAME_CAN_NOT_NULL = newConstant("appname can not null", "appname 参数不能为空！");
    }

    public static class SystemError extends ConstantCollection {
        public static final ErrorConstant SYSTEM_DEFAULT_ERROR = newConstant("system default error", "系统默认错误");
        public static final ErrorConstant TDDL_SEQUENCE_ERROR = newConstant("get tddl sequence error", "获取TDDL Sequence失败!");
        public static final ErrorConstant FLOW_CONTROL_ERROR = newConstant("interface API is deprecated", "接口已经被降级");
        public static final ErrorConstant SYSTEM_BUSY = newConstant("system is busy now, retry later", "系统繁忙，请稍后重试");
        public static final ErrorConstant INTERFACE_DEPRECATED = newConstant("this API is deprecated, please update ASAP", "此接口已停止服务，请尽快完成接口升级");
        public static final ErrorConstant DAO_EXCEPTION = newConstant("DAO_EXCEPTION", "数据库操作异常");
        public static final ErrorConstant DATE_PARSE_EXCEPTION = newConstant("DATE_PARSE_EXCEPTION", "字符串转换为时间失败");
    }


    public static class MobileCodeError extends ConstantCollection {
        public static final ErrorConstant PARAMETER_ERROR = newConstant("parameter error","请求参数有误") ;
        public static final ErrorConstant BUSINESS_ERROR = newConstant("business error","业务失败") ;
    }

    public static class LabelError extends ConstantCollection {
        public static final ErrorConstant PARAMETER_ERROR = newConstant("parameter error","请求参数有误") ;
        public static final ErrorConstant BUSINESS_ERROR = newConstant("business error","业务失败") ;

        /**
         * 参数相关错误
         */
        public static final ErrorConstant DATE_FORMAT_ERROR = newConstant("date format error","日期格式错误") ;

        /**
         * 申请新揽件码面单相关错误
         */
        public static final ErrorConstant APPLY_NEW_NOT_BIND_ERROR = newConstant("seller and courier not bind error","商家和小件员绑定关系不存在") ;
        public static final ErrorConstant CP_CODE_NO_PICKUP_HEADER_ERROR = newConstant("cpCode has no pickupHeader error","找不到cp对应的揽件码头") ;
        public static final ErrorConstant PICKUP_NUM_NOT_FOUND_ERROR = newConstant("pickup num not found error","找不到对应的揽件码数字") ;

        /**
         * 调用第三方相关错误
         */
        public static final ErrorConstant  CAN_NOT_GET_TEMPLATE_URL_ERROR = newConstant("can not get templateURL error","获取不到 cpCode 对应的 templateURL");

        /**
         * 网点商家地址小件员绑定相关错误。
         * （中文文案是业务方确定）
         */
        public static final ErrorConstant  SELLER_ADDRESS_BIND_DUPLICATE_ERROR = newConstant("seller address bind cp duplicate error","一个发货地址只能与同一快递公司建立一次服务绑定关系");

        /**
         * 无纸化面单信息下发iss失败
         */
        public static final ErrorConstant  PAPERLESS_DATA_PUSH_ISS_ERROR = newConstant("paperless data push iss error","无纸化面单信息下发iss失败");

        /**
         * 通过二维码获取小件员信息
         */
        public static final ErrorConstant COURIER_GET_FAIL = newConstant("COURIER_GET_FAIL", "从物流公司获取快递员信息失败") ;
        public static final ErrorConstant COURIER_IS_NOT_ALLOW = newConstant("COURIER_IS_NOT_ALLOW", "物流公司未开通该快递员千牛扫码权限") ;
    }
}
