package com.cainiao.waybill.bridge.enterprise.common.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/17
 **/
public enum EnterprisePackInboundBizTypeEnum {

    PACK( 0, "包裹"),

    FILE(1, "文件"),

    OTHER(2, "异常件"),
    ;

    final Integer code;

    final String describe;

    EnterprisePackInboundBizTypeEnum(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescribe() {
        return describe;
    }

    public static String getDesByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (EnterprisePackInboundBizTypeEnum value : EnterprisePackInboundBizTypeEnum.values()) {
            if(Objects.equals(value.getCode(), code)){
                return value.describe;
            }
        }
        return null;
    }
}
