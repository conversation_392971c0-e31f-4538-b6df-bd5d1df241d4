package com.cainiao.waybill.bridge.enterprise.administrators.service.impl;

import com.aliyun.dingtalkcontact_1_0.Client;
import com.base.BaseTest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.EnterpriseUserRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.User;
import com.cainiao.waybill.bridge.enterprise.administrators.response.UserDeptInfoResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.response.UserInfoResponse;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import com.cainiao.waybill.bridge.enterprise.common.UserContext;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterpriseUserInfoConverter;
import com.cainiao.waybill.bridge.enterprise.utils.PhoneNumberValidator;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoParam;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserPhoneNumDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserPhoneNumParam;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserInfoDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePostMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserPhoneNumMapper;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledExecutorService;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * <a href="https://alidocs.dingtalk.com/i/nodes/dpYLaezmVNRMGX56C1aKjlBOVrMqPxX6">单元测试编写&生成FAQ</a>
 * Created By Thub IDEA插件
 */
@RunWith(MockitoJUnitRunner.class)
public class DingTalkUserServiceImplTest extends BaseTest {

    @InjectMocks
    private DingTalkUserServiceImpl dingTalkUserServiceImpl;

    @Mock
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;
    @Mock
    private DingTalkAuthService dingTalkAuthService;
    @Mock
    private WaybillBridgeEnterpriseUserInfoConverter waybillBridgeEnterpriseUserInfoConverter;
    @Mock
    private WaybillBridgeEnterpriseUserPhoneNumMapper enterpriseUserPhoneNumMapper;
    @Mock
    private WaybillBridgeEnterprisePostMapper enterprisePostMapper;
    @Mock
    private Client dingTalkContractClient;
    @Mock
    private Executor threadPoolExecutor;
    @Mock
    private ScheduledExecutorService scheduled;


    @Before
    public void setUp() throws Exception {
        // 初始化Mock对象
    }

    @Test(expected = EnterpriseException.class)
    public void testRemoveAdminByUserId() {
        // Setup, prepare mock data
        when(enterpriseUserInfoMapper.selectOneByParam(any()))
                .thenReturn(new WaybillBridgeEnterpriseUserInfoDO());
        when(enterpriseUserInfoMapper.updateByPrimaryKeySelective(any()))
                .thenReturn(0);
        User user = new User();
        user.setUserId("userId");
        UserContext.setUser(user);
        // Prepare method arguments

        // Run the method to be tested
        dingTalkUserServiceImpl.removeAdminByUserId("userId");
    }

    @Test()
    public void testRemoveAdminByUserId1() {
        // Setup, prepare mock data
        when(enterpriseUserInfoMapper.selectOneByParam(any()))
                .thenReturn(new WaybillBridgeEnterpriseUserInfoDO());
        when(enterpriseUserInfoMapper.updateByPrimaryKeySelective(any()))
                .thenReturn(0);
        User user = new User();
        user.setUserId("1212");
        user.setCorpId("wr4r4");
        UserContext.setUser(user);
        // Prepare method arguments

        // Run the method to be tested
        dingTalkUserServiceImpl.removeAdminByUserId("userId");
    }

    /**
     * 测试搜索值是有效的电话号码且是四位数字
     */
    @Test
    public void testGetUserBySearchValueValidPhoneNumberFourDigits() {
        // Mock相关依赖
        EnterpriseUserRequest request = new EnterpriseUserRequest();
        request.setSearchValue("1234");
        request.setCorpId("testCorpId");
        request.setCurrentPage(1);
        request.setPageSize(10);

        when(PhoneNumberValidator.isValidNumber("1234")).thenReturn(true);
        when(PhoneNumberValidator.isFourDigitNumber("1234")).thenReturn(true);

        List<WaybillBridgeEnterpriseUserPhoneNumDO> userPhoneNumDOList = Lists.newArrayList();
        when(enterpriseUserPhoneNumMapper.selectByParam(any(WaybillBridgeEnterpriseUserPhoneNumParam.class)))
                .thenReturn(userPhoneNumDOList);
        BridgePagingDTO<WaybillEnterpriseUserInfoDTO> result = dingTalkUserServiceImpl.getUserBySearchValue(request);

        // 校验结果
        assertNotNull(result);
        assertTrue(result.getTableData().isEmpty());
        // verify(waybillBridgeEnterpriseUserInfoMapper, times(0)).countByParam(any());
    }

    /**
     * 测试搜索值是有效的电话号码但不是四位数字
     */
    @Test
    public void testGetUserBySearchValueValidPhoneNumberNotFourDigits() {
        // Mock相关依赖
        EnterpriseUserRequest request = new EnterpriseUserRequest();
        request.setSearchValue("12345");
        request.setCorpId("testCorpId");
        request.setCurrentPage(1);
        request.setPageSize(10);

        when(PhoneNumberValidator.isValidNumber("12345")).thenReturn(true);
        when(PhoneNumberValidator.isFourDigitNumber("12345")).thenReturn(false);

        BridgePagingDTO<WaybillEnterpriseUserInfoDTO> result = dingTalkUserServiceImpl.getUserBySearchValue(request);
        // 校验结果
        assertNotNull(result);
        assertTrue(result.getTableData().isEmpty());
        // verify(enterpriseUserPhoneNumMapper, times(0)).selectByParam(any());
    }

    /**
     * 测试搜索值不是有效的电话号码
     */
    @Test
    public void testGetUserBySearchValueInvalidPhoneNumber() {
        // Mock相关依赖
        EnterpriseUserRequest request = new EnterpriseUserRequest();
        request.setSearchValue("abcd");
        request.setCorpId("testCorpId");
        request.setCurrentPage(1);
        request.setPageSize(10);
        when(PhoneNumberValidator.isValidNumber("abcd")).thenReturn(false);

        List<WaybillBridgeEnterpriseUserInfoDO> enterpriseUserInfoDOList = Lists.newArrayList(new WaybillBridgeEnterpriseUserInfoDO());
        when(enterpriseUserInfoMapper.selectBySearchValue("abcd", null, "testCorpId"))
                .thenReturn(enterpriseUserInfoDOList);
        when(enterpriseUserInfoMapper.countByParam(any())).thenReturn(1L);
        when(waybillBridgeEnterpriseUserInfoConverter.convertFromDOList(enterpriseUserInfoDOList)).thenReturn(Lists.newArrayList());
        BridgePagingDTO<WaybillEnterpriseUserInfoDTO> result = dingTalkUserServiceImpl.getUserBySearchValue(request);
        // 校验结果
        assertNotNull(result);
        assertFalse(result.getTableData().isEmpty());
        // verify(waybillBridgeEnterpriseUserInfoConverter, times(1)).convertFromDOList(enterpriseUserInfoDOList);
    }

    /**
     * 测试用户具有岗位信息时的处理逻辑
     */
    @Test
    public void testRemoveAdminByUserIdUserWithPostInfo() {
        // Mock UserContext返回当前用户
        User mockUser = new User();
        mockUser.setUserId("testUserId");
        mockUser.setCorpId("testCorpId");
        when(UserContext.getUser()).thenReturn(mockUser);
        // Mock用户信息
        WaybillBridgeEnterpriseUserInfoDO mockUserInfo = new WaybillBridgeEnterpriseUserInfoDO();
        mockUserInfo.setUserId("testUserId");
        mockUserInfo.setPostId(12345L);
        when(enterpriseUserInfoMapper.selectOneByParam(any(WaybillBridgeEnterpriseUserInfoParam.class))).thenReturn(mockUserInfo);
        // Mock用户更新
        when(enterpriseUserInfoMapper.updateByPrimaryKeySelective(any(WaybillBridgeEnterpriseUserInfoDO.class))).thenReturn(1);
        int result = dingTalkUserServiceImpl.removeAdminByUserId("testUserId");
        // 验证结果
        assertEquals(1, result);
    }

    /**
     * 测试成功获取到用户当前部门信息后循环获取用户父级部门信息
     */
    @Test(timeout = 5000)
    public void testGetByUserIdSuccessFindingParentDeptInfo() {
        // 设置测试数据
        String userId = "testUserId";
        String corpId = "testCorpId";
        // Mock相关依赖
        WaybillBridgeEnterpriseUserInfoDO userInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        when(enterpriseUserInfoMapper.selectOneByParam(any(WaybillBridgeEnterpriseUserInfoParam.class)))
                .thenReturn(userInfoDO);
        WaybillEnterpriseUserInfoDTO userInfoDTO = new WaybillEnterpriseUserInfoDTO();
        userInfoDTO.setCorpId(corpId);
        userInfoDTO.setUserId(userId);
        when(waybillBridgeEnterpriseUserInfoConverter.convertFromDO(userInfoDO)).thenReturn(userInfoDTO);
        UserInfoResponse dingTalkUserInfo = new UserInfoResponse();
        dingTalkUserInfo.setDeptIdList(Lists.newArrayList(1L));
        when(dingTalkAuthService.getAccessToken(anyString(), anyString())).thenReturn("testAccessToken");
        when(dingTalkUserServiceImpl.getUserInfoToDingTalkByUserId(corpId, userId)).thenReturn(dingTalkUserInfo);
        UserDeptInfoResponse userDeptInfoResponse = new UserDeptInfoResponse();
        userDeptInfoResponse.setDeptId(1L);
        userDeptInfoResponse.setParentDeptId(0L);
        when(dingTalkUserServiceImpl.getUserDeptInfoById(corpId, 1L)).thenReturn(userDeptInfoResponse);
        when(dingTalkUserServiceImpl.getUserDeptInfoById(corpId, 0L)).thenReturn(null);
        // 执行测试方法
        WaybillEnterpriseUserInfoDTO result = dingTalkUserServiceImpl.getByUserId(userId, corpId);
        // 校验结果
        assertNotNull(result);
        assertNotNull(result.getDeptList());
        assertTrue(result.getDeptList().size() > 0);
    }
}