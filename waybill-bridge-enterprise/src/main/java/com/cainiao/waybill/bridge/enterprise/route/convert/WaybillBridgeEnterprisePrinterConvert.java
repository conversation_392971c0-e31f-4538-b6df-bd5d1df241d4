package com.cainiao.waybill.bridge.enterprise.route.convert;

import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePrinterDO;
import com.cainiao.waybill.bridge.model.dto.WayBillEnterprisePrinterDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25
 **/
public class WaybillBridgeEnterprisePrinterConvert {

    /**
     * DTO模型转换成DO模型
     * @param wayBillEnterprisePrinterDTO DTO模型
     */
    public static WaybillBridgeEnterprisePrinterDO convertFromDTO(WayBillEnterprisePrinterDTO wayBillEnterprisePrinterDTO) {
        if (wayBillEnterprisePrinterDTO == null) {
            return null;
        }
        WaybillBridgeEnterprisePrinterDO waybillBridgeEnterprisePrinterDO = new WaybillBridgeEnterprisePrinterDO();
        BeanUtils.copyProperties(wayBillEnterprisePrinterDTO,waybillBridgeEnterprisePrinterDO);
        return waybillBridgeEnterprisePrinterDO;
    }

    /**
     * DO模型转换成DTO模型
     * @param waybillBridgeEnterprisePrinterDO DO模型
     */
    public static WayBillEnterprisePrinterDTO convertFromDO(WaybillBridgeEnterprisePrinterDO waybillBridgeEnterprisePrinterDO) {
        if (waybillBridgeEnterprisePrinterDO == null) {
            return null;
        }
        WayBillEnterprisePrinterDTO wayBillEnterprisePrinterDTO = new WayBillEnterprisePrinterDTO();
        BeanUtils.copyProperties(waybillBridgeEnterprisePrinterDO,wayBillEnterprisePrinterDTO);
        return wayBillEnterprisePrinterDTO;
    }


    /**
     * DO模型转换成DTO模型
     * @param wayBillEnterprisePrinterDOList DO模型
     */
    public static List<WayBillEnterprisePrinterDTO> convertFromDOList(List<WaybillBridgeEnterprisePrinterDO> wayBillEnterprisePrinterDOList) {
        List<WayBillEnterprisePrinterDTO> wayBillEnterprisePrinterDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(wayBillEnterprisePrinterDOList)) {
            return wayBillEnterprisePrinterDTOList;
        }
        wayBillEnterprisePrinterDOList.forEach(waybillBridgeEnterprisePrinterDO -> {
            WayBillEnterprisePrinterDTO wayBillEnterprisePrinterDTO = new WayBillEnterprisePrinterDTO();
            BeanUtils.copyProperties(waybillBridgeEnterprisePrinterDO,wayBillEnterprisePrinterDTO);
            wayBillEnterprisePrinterDTOList.add(wayBillEnterprisePrinterDTO);
        });
        return wayBillEnterprisePrinterDTOList;
    }

}
