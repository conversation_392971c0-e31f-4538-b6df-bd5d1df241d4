package com.cainiao.waybill.bridge.enterprise.utils.excel;

import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.ObjectMetadata;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.poi.ss.usermodel.Workbook;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/3/20 16:19
 */
@Slf4j
public class ExcelUtil {


    public static String exportToOSS(Workbook workbook, String dir) {
        if (null == workbook) {
            throw new EnterpriseException("workbook is null", "导出文件失败");
        }

        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setObjectAcl(CannedAccessControlList.PublicRead);
        metadata.setExpirationTime(DateUtils.addMinutes(new Date(), 15));

        String date = DateUtil.dateToStr(new Date(), DateUtil.pattern24Format);

        String fileName = dir + "/" + workbook.getSheetName(0) + date + ".xlsx";
        String imgUrl = OssUtils.uploadToOSS(fileName, convert2InputStream(workbook), metadata);
        return imgUrl;
    }

    /**
     * workbook转为流
     *
     * @param wb
     * @return
     */
    private static InputStream convert2InputStream(Workbook wb) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            wb.write(bos);
            byte[] bytes = bos.toByteArray();
            return new ByteArrayInputStream(bytes);
        } catch (Throwable e) {
            log.error("WORKBOOK_CONVERT_STREAM_ERROR", e);
            return null;
        }
    }

}
