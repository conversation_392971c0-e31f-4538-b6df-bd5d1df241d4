# coding=utf-8
import logging
from flask import Flask, request, jsonify
from model import main

logging.basicConfig(filename='/home/<USER>/waybill-bridge/logs/algorithm_output.log', level=logging.INFO,
                    format='%(asctime)s - %(message)s')

app = Flask(__name__)


@app.route('/optimize', methods=['POST'])
def optimize():
    try:
        query = request.json  # 从请求中获取 JSON 数据
        logging.info('Algorithm request json: %s', query)

        solver_result, msg = main(query)

        logging.info('Algorithm solver result: %s', msg)

        return jsonify(success=True, result=msg)
    except Exception as e:
        logging.error('Algorithm error occurred: %s', e)
        return jsonify(success=False, error=str(e)), 500  # 返回500状态码和错误信息


if __name__ == "__main__":
    logging.info("python algorithm server init success")
    app.run(host='0.0.0.0', port=8050)

