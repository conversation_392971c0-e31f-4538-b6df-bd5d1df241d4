package com.cainiao.waybill.bridge.enterprise.common.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/17
 **/
public enum EnterprisePackInboundStatusEnum {

    PENDING_PICK_UP( 0, "待取件"),

    ALREADY_PICK_UP(1, "已取件"),

    ABNORMAL_PACK(2, "异常件"),
    ;

    final Integer code;

    final String describe;

    EnterprisePackInboundStatusEnum(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescribe() {
        return describe;
    }

    public static String getDesByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (EnterprisePackInboundStatusEnum value : EnterprisePackInboundStatusEnum.values()) {
            if(Objects.equals(value.getCode(), code)){
                return value.describe;
            }
        }
        return null;
    }
}
