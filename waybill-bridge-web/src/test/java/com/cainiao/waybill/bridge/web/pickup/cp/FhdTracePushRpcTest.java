/*
package com.cainiao.waybill.bridge.web.pickup.cp;

import static org.mockito.Mockito.*;

import java.util.Map;

import com.aliyun.openservices.ons.api.Producer;
import com.cainiao.waybill.bridge.BaseTest;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.manager.channel.PickUpFhdYtoOrderManagerImpl;
import com.cainiao.waybill.bridge.biz.pickup.manager.impl.WaybillPickUpOrderManagerImpl;
import com.cainiao.waybill.bridge.biz.pickup.service.impl.WaybillPickUpEventMetaQSender;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

*/
/**
 * <a href="https://alidocs.dingtalk.com/i/nodes/dpYLaezmVNRMGX56C1aKjlBOVrMqPxX6">单元测试编写&生成FAQ</a>
 * Created By Thub IDEA插件
 *//*

public class FhdTracePushRpcTest extends BaseTest {

    @InjectMocks
    private FhdTracePushRpc fhdTracePushRpc;
    @Mock
    private WaybillPickUpOrderManagerImpl waybillPickUpOrderManager;
    @Mock
    private WaybillPickUpEventMetaQSender waybillPickUpEventMetaQSender = new WaybillPickUpEventMetaQSender(Mockito.mock(
        Producer.class));

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        initSwitch(BridgeSwitch.class, "ConfigTest.json");
    }

    @Test
    public void testTracePush() {
        WaybillPickUpDetailDO detailDO = new WaybillPickUpDetailDO();
        detailDO.setMailNo("YT2504168104642");
        detailDO.setCpCode("YTO");
        detailDO.setResCode("waybill-bridge#TEST");
        detailDO.setOuterOrderCode("123456");

        doReturn(detailDO).when(waybillPickUpOrderManager).get(anyString(), anyString());
        doReturn(detailDO).when(waybillPickUpOrderManager).getById(anyLong());

        ArgumentCaptor<WaybillPickUpEvent> captor = ArgumentCaptor.forClass(WaybillPickUpEvent.class);

        doReturn(new BaseResultDTO<Void>("objectId", null))
            .when(waybillPickUpEventMetaQSender).send(any());

        Map<String, Object> result = fhdTracePushRpc.tracePush(13687L, String.valueOf(1671532850),
            "531cd0e1-1f42-4843-ba76-a760c516606e", "11394ce0d10f0f7494421a4fca8d1c03", readJson("Params.json"));


        // Verify the result
        verify(waybillPickUpEventMetaQSender, times(1))
            .send(captor.capture());
        Assert.assertEquals(captor.getValue().getMailNo(), "YT2504168104642");
        Assert.assertEquals(captor.getValue().getCpCode(), "YTO");
    }
}
*/
