package com.cainiao.waybill.bridge.enterprise.post.service.impl;

import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.common.constant.EnterpriseCommonConstant;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseSourceFormEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseUserTypeEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.post.request.EnterprisePostRequest;
import com.cainiao.waybill.bridge.enterprise.post.service.WaybillEnterprisePostService;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterprisePostConverter;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterpriseUserInfoConverter;
import com.cainiao.waybill.bridge.enterprise.utils.RandomNumberGeneratorUtils;
import com.cainiao.waybill.bridge.model.domain.*;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterprisePostDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePostMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillEnterpriseLocationMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/20
 **/
@Service
public class WaybillEnterprisePostServiceImpl implements WaybillEnterprisePostService {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private WaybillBridgeEnterprisePostMapper enterprisePostMapper;

    @Autowired
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;

    @Autowired
    private WaybillBridgeEnterprisePostConverter enterprisePostConverter;

    @Autowired
    private WaybillBridgeEnterpriseUserInfoConverter enterpriseUserInfoConverter;

    @Autowired
    private WaybillEnterpriseLocationMapper enterpriseLocationMapper;

    @Resource
    private TransactionTemplate bridgeTransactionTemplate;


    @Override
    public BridgePagingDTO<WaybillEnterprisePostDTO> pageList(EnterprisePostRequest request) {
        WaybillBridgeEnterprisePostParam waybillBridgeEnterprisePostParam = new WaybillBridgeEnterprisePostParam();
        waybillBridgeEnterprisePostParam.setPage(true);
        waybillBridgeEnterprisePostParam.setPagination(request.getCurrentPage(), request.getPageSize());
        waybillBridgeEnterprisePostParam.appendOrderByClause(WaybillBridgeEnterprisePostParam.OrderCondition.GMTMODIFIED, WaybillBridgeEnterprisePostParam.SortType.DESC);

        WaybillBridgeEnterprisePostParam.Criteria criteria = waybillBridgeEnterprisePostParam.createCriteria();
        criteria.andCorpIdEqualTo(request.getCorpId());

        if (StringUtils.isNotBlank(request.getPostName())) {
            criteria.andPostNameEqualTo(request.getPostName());
        }
        if (StringUtils.isNotBlank(request.getLocationId())) {
            WaybillEnterpriseLocationParam locationParam = new WaybillEnterpriseLocationParam();
            locationParam.createCriteria()
                    .andLocationIdEqualTo(request.getLocationId())
                    .andCorpIdEqualTo(request.getCorpId());
            WaybillEnterpriseLocationDO waybillEnterpriseLocationDO = enterpriseLocationMapper.selectOneByParam(locationParam);
            if (waybillEnterpriseLocationDO == null) {
                throw new EnterpriseException(EnterpriseErrorEnum.LOCATION_NOT_EXIST.code(), EnterpriseErrorEnum.LOCATION_NOT_EXIST.describe());
            }
            criteria.andLocationIdEqualTo(waybillEnterpriseLocationDO.getId());
        }
        if (EnterpriseSourceFormEnum.POST.name().equals(request.getSourceFrom())) {
            WaybillBridgeEnterpriseUserInfoDO userInfo = enterpriseUserInfoMapper.selectByUserId(request.getUserId(), request.getCorpId());
            if (userInfo == null) {
                LOGGER.error("userInfo is null or postId is null, userId:{}", request.getUserId());
                return BridgePagingDTO.build(Lists.newArrayList(), 0,
                        request.getCurrentPage(), request.getPageSize());
            }
            // 邮局管理员角色只允许查看自己管理的小邮局信息
            if (EnterpriseUserTypeEnum.POST_ADMIN.name().equals(userInfo.getUserType())) {
                criteria.andIdEqualTo(userInfo.getPostId());
            }
            if (EnterpriseUserTypeEnum.EMPLOYEE.name().equals(userInfo.getUserType())) {
                LOGGER.error("userInfo is EMPLOYEE, userId:{}", request.getUserId());
                return BridgePagingDTO.build(Lists.newArrayList(), 0,
                        request.getCurrentPage(), request.getPageSize());
            }
        }

        try {
            long totalCount = enterprisePostMapper.countByParam(waybillBridgeEnterprisePostParam);
            List<WaybillBridgeEnterprisePostDO> waybillBridgeEnterprisePostDOList = enterprisePostMapper.selectByParam(waybillBridgeEnterprisePostParam);
            return BridgePagingDTO.build(enterprisePostConverter.convertFromDOList(waybillBridgeEnterprisePostDOList), totalCount,
                    request.getCurrentPage(), request.getPageSize());
        } catch (Exception e) {
            LOGGER.error("enterprise post pageList error : {}", ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public void addPost(EnterprisePostRequest enterprisePostRequest) {
        WaybillBridgeEnterprisePostParam waybillBridgeEnterprisePostParam = new WaybillBridgeEnterprisePostParam();
        waybillBridgeEnterprisePostParam.createCriteria()
                .andCorpIdEqualTo(enterprisePostRequest.getCorpId())
                .andPostNameEqualTo(enterprisePostRequest.getPostName());

        WaybillBridgeEnterprisePostDO dbEnterpriseDO = enterprisePostMapper.selectOneByParam(waybillBridgeEnterprisePostParam);
        if (dbEnterpriseDO != null) {
            throw new EnterpriseException(EnterpriseErrorEnum.POST_ALREADY_EXIST.code(), EnterpriseErrorEnum.POST_ALREADY_EXIST.describe());
        }

        bridgeTransactionTemplate.execute(new TransactionCallback<Void>() {
            @Override
            public Void doInTransaction(TransactionStatus status) {
                try {
                    WaybillBridgeEnterprisePostDO waybillBridgeEnterprisePostDO = new WaybillBridgeEnterprisePostDO();
                    waybillBridgeEnterprisePostDO.setGmtCreate(new Date());
                    waybillBridgeEnterprisePostDO.setGmtModified(new Date());
                    waybillBridgeEnterprisePostDO.setCorpId(enterprisePostRequest.getCorpId());
                    waybillBridgeEnterprisePostDO.setPostName(enterprisePostRequest.getPostName());
                    if (StringUtils.isNotBlank(enterprisePostRequest.getLocationId())) {
                        WaybillEnterpriseLocationParam locationParam = new WaybillEnterpriseLocationParam();
                        locationParam.createCriteria()
                                .andLocationIdEqualTo(enterprisePostRequest.getLocationId())
                                .andCorpIdEqualTo(enterprisePostRequest.getCorpId());
                        WaybillEnterpriseLocationDO waybillEnterpriseLocationDO = enterpriseLocationMapper.selectOneByParam(locationParam);
                        if (waybillEnterpriseLocationDO == null) {
                            throw new EnterpriseException(EnterpriseErrorEnum.LOCATION_NOT_EXIST.code(), EnterpriseErrorEnum.LOCATION_NOT_EXIST.describe());
                        }
                        waybillBridgeEnterprisePostDO.setLocationId(waybillEnterpriseLocationDO.getId());
                    }
                    waybillBridgeEnterprisePostDO.setRemark(enterprisePostRequest.getRemake());
                    waybillBridgeEnterprisePostDO.setFeature(enterprisePostRequest.getFeature());
                    String postId = RandomNumberGeneratorUtils.generateRandomNumberWithPrefix(EnterpriseCommonConstant.POST_ID_PREFIX, 10);
                    waybillBridgeEnterprisePostDO.setPostId(postId);
                    enterprisePostMapper.insert(waybillBridgeEnterprisePostDO);

                    WaybillBridgeEnterprisePostDO insertEnterpriseDO = enterprisePostMapper.selectOneByParam(waybillBridgeEnterprisePostParam);

                    List<String> postAdminList = enterprisePostRequest.getPostAdminList();
                    if (CollectionUtils.isNotEmpty(postAdminList)) {
                        postAdminList.forEach(adminUserId -> {
                            WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectByUserId(adminUserId, enterprisePostRequest.getCorpId());
                            waybillBridgeEnterpriseUserInfoDO.setGmtModified(new Date());

                            Integer dbUserTypeStatus = EnterpriseUserTypeEnum.getStatus(waybillBridgeEnterpriseUserInfoDO.getUserType());
                            if (dbUserTypeStatus != null && dbUserTypeStatus > EnterpriseUserTypeEnum.POST_ADMIN.status) {
                                waybillBridgeEnterpriseUserInfoDO.setUserType(EnterpriseUserTypeEnum.POST_ADMIN.name());
                            }

                            if (waybillBridgeEnterpriseUserInfoDO.getPostId() != null) {
                                throw new EnterpriseException(EnterpriseErrorEnum.POST_BIND_ERROR.code(),
                                        String.format("用户%s已绑定小邮局", waybillBridgeEnterpriseUserInfoDO.getUserName())
                                );
                            }

                            waybillBridgeEnterpriseUserInfoDO.setPostId(insertEnterpriseDO.getId());
                            enterpriseUserInfoMapper.updateByPrimaryKeySelective(waybillBridgeEnterpriseUserInfoDO);
                        });
                    }
                } catch (Exception e) {
                    LOGGER.error("enterprise post addPost error : {}", ExceptionUtils.getStackTrace(e));
                    status.setRollbackOnly();
                    throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
                }
                return null;
            }
        });
    }

    @Override
    public void updatePost(EnterprisePostRequest enterprisePostRequest) {
        WaybillBridgeEnterprisePostParam postParam = new WaybillBridgeEnterprisePostParam();
        postParam.createCriteria()
                .andCorpIdEqualTo(enterprisePostRequest.getCorpId())
                .andPostIdEqualTo(enterprisePostRequest.getPostId());
        WaybillBridgeEnterprisePostDO waybillBridgeEnterprisePostDO = enterprisePostMapper.selectOneByParam(postParam);
        if (waybillBridgeEnterprisePostDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.POST_NOT_EXIST.code(), EnterpriseErrorEnum.POST_NOT_EXIST.describe());
        }

        waybillBridgeEnterprisePostDO.setGmtModified(new Date());
        waybillBridgeEnterprisePostDO.setPostName(enterprisePostRequest.getPostName());
        if (StringUtils.isNotBlank(enterprisePostRequest.getLocationId())) {
            WaybillEnterpriseLocationParam locationParam = new WaybillEnterpriseLocationParam();
            locationParam.createCriteria()
                    .andLocationIdEqualTo(enterprisePostRequest.getLocationId())
                    .andCorpIdEqualTo(enterprisePostRequest.getCorpId());
            WaybillEnterpriseLocationDO waybillEnterpriseLocationDO = enterpriseLocationMapper.selectOneByParam(locationParam);
            if (waybillEnterpriseLocationDO == null) {
                throw new EnterpriseException(EnterpriseErrorEnum.LOCATION_NOT_EXIST.code(), EnterpriseErrorEnum.LOCATION_NOT_EXIST.describe());
            }
            waybillBridgeEnterprisePostDO.setLocationId(waybillEnterpriseLocationDO.getId());
        }
        waybillBridgeEnterprisePostDO.setRemark(enterprisePostRequest.getRemake());
        waybillBridgeEnterprisePostDO.setFeature(enterprisePostRequest.getFeature());

        try {
            // 同步管理员
            List<String> postAdminList = enterprisePostRequest.getPostAdminList();
            WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
            enterpriseUserInfoParam.createCriteria()
                    .andPostIdEqualTo(waybillBridgeEnterprisePostDO.getId())
                    .andCorpIdEqualTo(waybillBridgeEnterprisePostDO.getCorpId());
            List<WaybillBridgeEnterpriseUserInfoDO> enterpriseUserInfoDOList = enterpriseUserInfoMapper.selectByParam(enterpriseUserInfoParam);
            // 获取当前小邮局管理员列表,判断是否需要解绑
            List<String> bindPostUserId = enterpriseUserInfoDOList.stream()
                    .map(WaybillBridgeEnterpriseUserInfoDO::getUserId)
                    .collect(Collectors.toList());

            // 获取新增小邮局管理员
            List<String> newAdminUserIdList = Lists.newArrayList(postAdminList);
            newAdminUserIdList.removeAll(bindPostUserId);
            if (CollectionUtils.isNotEmpty(newAdminUserIdList)) {
                newAdminUserIdList.forEach(adminUserId -> {
                    WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectByUserId(adminUserId, enterprisePostRequest.getCorpId());
                    waybillBridgeEnterpriseUserInfoDO.setGmtModified(new Date());
                    Integer dbUserTypeStatus = EnterpriseUserTypeEnum.getStatus(waybillBridgeEnterpriseUserInfoDO.getUserType());
                    // 是否需要更改权限
                    if (dbUserTypeStatus != null && dbUserTypeStatus > EnterpriseUserTypeEnum.POST_ADMIN.status) {
                        waybillBridgeEnterpriseUserInfoDO.setUserType(EnterpriseUserTypeEnum.POST_ADMIN.name());
                    }
                    if (waybillBridgeEnterpriseUserInfoDO.getPostId() != null) {
                        throw new EnterpriseException(EnterpriseErrorEnum.POST_BIND_ERROR.code(), String.format("该用户%s已绑定小邮局", waybillBridgeEnterpriseUserInfoDO.getUserName()));
                    }
                    waybillBridgeEnterpriseUserInfoDO.setPostId(waybillBridgeEnterprisePostDO.getId());
                    enterpriseUserInfoMapper.updateByPrimaryKeySelective(waybillBridgeEnterpriseUserInfoDO);
                });
            }

            // 获取删除的小邮局管理员
            List<String> deleteAdminUserIdList = Lists.newArrayList(bindPostUserId);
            deleteAdminUserIdList.removeAll(postAdminList);
            if (CollectionUtils.isNotEmpty(deleteAdminUserIdList)) {
                deleteAdminUserIdList.forEach(adminUserId -> {
                    WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectByUserId(adminUserId, enterprisePostRequest.getCorpId());
                    waybillBridgeEnterpriseUserInfoDO.setGmtModified(new Date());
                    waybillBridgeEnterpriseUserInfoDO.setPostId(null);
                    // 用户身份为小邮局管理员,解绑时身份改为员工
                    if (EnterpriseUserTypeEnum.POST_ADMIN.name().equals(waybillBridgeEnterpriseUserInfoDO.getUserType())) {
                        waybillBridgeEnterpriseUserInfoDO.setUserType(EnterpriseUserTypeEnum.EMPLOYEE.name());
                    }
                    enterpriseUserInfoMapper.updateByPrimaryKey(waybillBridgeEnterpriseUserInfoDO);
                });
            }
            enterprisePostMapper.updateByPrimaryKey(waybillBridgeEnterprisePostDO);
        } catch (Exception e) {
            LOGGER.error("updatePost error, reason:{}", ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public void deleteById(Long id) {
        WaybillBridgeEnterprisePostDO waybillBridgeEnterprisePostDO = enterprisePostMapper.selectByPrimaryKey(id);
        if (waybillBridgeEnterprisePostDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.POST_NOT_EXIST.code(), EnterpriseErrorEnum.POST_NOT_EXIST.describe());
        }

        try {
            WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
            enterpriseUserInfoParam.createCriteria().andPostIdEqualTo(waybillBridgeEnterprisePostDO.getId());
            List<WaybillBridgeEnterpriseUserInfoDO> enterpriseUserInfoDOList = enterpriseUserInfoMapper.selectByParam(enterpriseUserInfoParam);
            if (CollectionUtils.isNotEmpty(enterpriseUserInfoDOList)) {
                enterpriseUserInfoDOList.forEach(enterpriseUserInfoDO -> {
                    enterpriseUserInfoDO.setGmtModified(new Date());
                    enterpriseUserInfoDO.setPostId(null);
                    enterpriseUserInfoMapper.updateByPrimaryKey(enterpriseUserInfoDO);
                });
            }
            enterprisePostMapper.deleteByPrimaryKey(id);
        } catch (Exception e) {
            LOGGER.error("deleteById error, reason:{}", ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public WaybillEnterprisePostDTO getById(Long id) {
        WaybillBridgeEnterprisePostDO waybillBridgeEnterprisePostDO = enterprisePostMapper.selectByPrimaryKey(id);
        WaybillEnterprisePostDTO waybillEnterprisePostDTO = enterprisePostConverter.convertFromDO(waybillBridgeEnterprisePostDO);
        if (waybillBridgeEnterprisePostDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.POST_NOT_EXIST.code(), EnterpriseErrorEnum.POST_NOT_EXIST.describe());
        }
        WaybillBridgeEnterpriseUserInfoParam param = new WaybillBridgeEnterpriseUserInfoParam();
        param.createCriteria().andPostIdEqualTo(waybillBridgeEnterprisePostDO.getId());
        List<WaybillBridgeEnterpriseUserInfoDO> waybillBridgeEnterpriseUserInfoDOList = enterpriseUserInfoMapper.selectByParam(param);
        waybillEnterprisePostDTO.setAdminInfoList(enterpriseUserInfoConverter.convertFromDOList(waybillBridgeEnterpriseUserInfoDOList));
        return waybillEnterprisePostDTO;
    }

    @Override
    public List<WaybillEnterprisePostDTO> getPostList(EnterprisePostRequest request) {
        WaybillBridgeEnterprisePostParam waybillBridgeEnterprisePostParam = new WaybillBridgeEnterprisePostParam();
        WaybillBridgeEnterprisePostParam.Criteria criteria = waybillBridgeEnterprisePostParam.createCriteria();
        criteria.andCorpIdEqualTo(request.getCorpId());

        if (StringUtils.isNotBlank(request.getPostName())) {
            criteria.andPostNameEqualTo(request.getPostName());
        }
        if (StringUtils.isNotBlank(request.getLocationId())) {
            WaybillEnterpriseLocationParam locationParam = new WaybillEnterpriseLocationParam();
            locationParam.createCriteria()
                    .andLocationIdEqualTo(request.getLocationId())
                    .andCorpIdEqualTo(request.getCorpId());
            WaybillEnterpriseLocationDO waybillEnterpriseLocationDO = enterpriseLocationMapper.selectOneByParam(locationParam);
            if (waybillEnterpriseLocationDO == null) {
                throw new EnterpriseException(EnterpriseErrorEnum.LOCATION_NOT_EXIST.code(), EnterpriseErrorEnum.LOCATION_NOT_EXIST.describe());
            }
            criteria.andLocationIdEqualTo(waybillEnterpriseLocationDO.getId());
        }

        try {
            List<WaybillBridgeEnterprisePostDO> waybillBridgeEnterprisePostDOList = enterprisePostMapper.selectByParam(waybillBridgeEnterprisePostParam);
            return enterprisePostConverter.convertFromDOList(waybillBridgeEnterprisePostDOList);
        } catch (Exception e) {
            LOGGER.error("enterprise post pageList error : {}", ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public void deletePostByPostId(String postId, String corpId) {
        WaybillBridgeEnterprisePostParam postParam = new WaybillBridgeEnterprisePostParam();
        postParam.createCriteria()
                .andCorpIdEqualTo(corpId)
                .andPostIdEqualTo(postId);
        WaybillBridgeEnterprisePostDO waybillBridgeEnterprisePostDO = enterprisePostMapper.selectOneByParam(postParam);
        if (waybillBridgeEnterprisePostDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.POST_NOT_EXIST.code(), EnterpriseErrorEnum.POST_NOT_EXIST.describe());
        }

        bridgeTransactionTemplate.execute(new TransactionCallback<Void>() {
            @Override
            public Void doInTransaction(TransactionStatus status) {
                try {
                    WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
                    enterpriseUserInfoParam.createCriteria()
                            .andPostIdEqualTo(waybillBridgeEnterprisePostDO.getId())
                            .andCorpIdEqualTo(waybillBridgeEnterprisePostDO.getCorpId());
                    List<WaybillBridgeEnterpriseUserInfoDO> enterpriseUserInfoDOList = enterpriseUserInfoMapper.selectByParam(enterpriseUserInfoParam);
                    if (CollectionUtils.isNotEmpty(enterpriseUserInfoDOList)) {
                        enterpriseUserInfoDOList.forEach(enterpriseUserInfoDO -> {
                            enterpriseUserInfoDO.setGmtModified(new Date());
                            enterpriseUserInfoDO.setPostId(null);
                            enterpriseUserInfoMapper.updateByPrimaryKey(enterpriseUserInfoDO);
                        });
                    }
                    enterprisePostMapper.deleteByPrimaryKey(waybillBridgeEnterprisePostDO.getId());
                } catch (Exception e) {
                    LOGGER.error("deletePostByPostId error, reason:{}", ExceptionUtils.getStackTrace(e));
                    throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
                }
                return null;
            }
        });
    }

    @Override
    public WaybillEnterprisePostDTO getPostByPostId(String postId, String corpId) {
        WaybillBridgeEnterprisePostParam postParam = new WaybillBridgeEnterprisePostParam();
        postParam.createCriteria()
                .andCorpIdEqualTo(corpId)
                .andPostIdEqualTo(postId);
        WaybillBridgeEnterprisePostDO waybillBridgeEnterprisePostDO = enterprisePostMapper.selectOneByParam(postParam);
        if (waybillBridgeEnterprisePostDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.POST_NOT_EXIST.code(), EnterpriseErrorEnum.POST_NOT_EXIST.describe());
        }
        WaybillEnterprisePostDTO waybillEnterprisePostDTO = enterprisePostConverter.convertFromDO(waybillBridgeEnterprisePostDO);
        WaybillBridgeEnterpriseUserInfoParam param = new WaybillBridgeEnterpriseUserInfoParam();
        param.createCriteria()
                .andPostIdEqualTo(waybillBridgeEnterprisePostDO.getId())
                .andCorpIdEqualTo(waybillBridgeEnterprisePostDO.getCorpId());
        List<WaybillBridgeEnterpriseUserInfoDO> waybillBridgeEnterpriseUserInfoDOList = enterpriseUserInfoMapper.selectByParam(param);
        waybillEnterprisePostDTO.setAdminInfoList(enterpriseUserInfoConverter.convertFromDOList(waybillBridgeEnterpriseUserInfoDOList));
        return waybillEnterprisePostDTO;
    }
}
