package com.cainiao.waybill.bridge.enterprise.administrators.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户详情
 * <AUTHOR>
 * @date 2025-04-16 10:35:36
 */
@Data
public class UserInfoResponse implements Serializable {

    private static final long serialVersionUID = 5285929346148870237L;
    /**
     * 身份等级
     */
    private Long sysLevel;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 是否管理员
     */
    private Boolean sys;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户名称
     */
    private String userType;

    /**
     * 盐
     */
    private String salt;

    /**
     * 手机号
     */
    private String phoneNum;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 部门ID
     */
    private List<Long> deptIdList;

}
