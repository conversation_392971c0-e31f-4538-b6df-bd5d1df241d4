package com.cainiao.waybill.bridge.common.util;

import com.alibaba.common.lang.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 单位单位处理工具类
 *
 * @author: yexin
 * @date: 2022-04-07 19:58
 **/
public class WeightUnitUtil {

    public static BigDecimal gToKg(Integer gValue) {
        if (gValue == null) {
            return null;
        }
        return BigDecimal.valueOf(gValue).divide(BigDecimal.valueOf(1000), 3, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 将kg转换为g
     */
    public static int kg2g(String kgStrValue) {
        if (StringUtil.isBlank(kgStrValue)) {
            return 0;
        }
        try {
            BigDecimal decimal = new BigDecimal(kgStrValue);
            decimal = decimal.multiply(new BigDecimal(1000));
            return decimal.intValue();
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 将kg转换为g
     */
    public static int kg2g(Integer kgValue) {
        if (kgValue == null) {
            return 0;
        }
        try {
            BigDecimal decimal = new BigDecimal(kgValue);
            decimal = decimal.multiply(new BigDecimal(1000));
            return decimal.intValue();
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 判断入参的2个重量是否存在价格跨区
     */
    public static boolean gIntValueIsCrossRegion(String weightStr1, String weightStr2) {
        //System.out.print(weightStr1 + "---" + weightStr2 + "---");
        if (StringUtils.isBlank(weightStr1) || StringUtils.isBlank(weightStr2)) {
            return false;
        }
        int weight1 = gIntValue(weightStr1);
        int weight2 = gIntValue(weightStr2);
        if (weight1 == 0 || weight2 == 0) {
            return false;
        }
        int weight1Len = weightStr1.length();
        int weight2Len = weightStr2.length();
        if (weight1Len == weight2Len) {
            //  值相同，肯定未跨区
            if (weight1 == weight2) {
                return false;
            }
            //  小于1000g,不跨区
            if (weight1Len < 4) {
                return false;
            }
            //  长度相同，值不相同，但是有一个值为1000，那么必然跨区
            if (weight1 == 1000 || weight2 == 1000) {
                return true;
            }

            //  到这里，长度相同，长度>=4

            //  1.处理千位之前的数值，存在不同肯定跨区
            String reWeightStr1 = StringUtils.reverse(weightStr1);
            String reWeightStr2 = StringUtils.reverse(weightStr2);
            for (int i = 4; i < reWeightStr1.length(); i++) {
                if (reWeightStr1.charAt(i) != reWeightStr2.charAt(i)) {
                    return true;
                }
            }

            //  2.处理千位后面的数值

            //  千位上的数值
            char str1ThousandNum = weightStr1.charAt(weight1Len - 4);
            char str2ThousandNum = weightStr2.charAt(weight2Len - 4);
            //  千位后面的数值是否全为0
            boolean str1AfterThousandIsEmpty = true;
            boolean str2AfterThousandIsEmpty = true;
            for (int i = 1; i < 4; i++) {
                if (weightStr1.charAt(weight1Len - i) != '0') {
                    str1AfterThousandIsEmpty = false;
                }
                if (weightStr2.charAt(weight2Len - i) != '0') {
                    str2AfterThousandIsEmpty = false;
                }
            }
            //System.out.print("---" + str1ThousandNum + "---" + str1AfterThousandIsEmpty + "---");
            //System.out.print("---" + str2ThousandNum + "---" + str2AfterThousandIsEmpty + "---");

            if (str1ThousandNum == str2ThousandNum) {
                //  千位相同：都为0 或者 都不为0 时，不跨区
                if ((str1AfterThousandIsEmpty && str2AfterThousandIsEmpty) || (!str1AfterThousandIsEmpty && !str2AfterThousandIsEmpty)) {
                    return false;
                } else {
                    return true;
                }
            }
            //  千位不相同：千位值差值为1，且千位值大的其它位值全为0，千位值小的其它位不全为0时，不跨区
            char maxThousandNum = str1ThousandNum;
            char minThousandNum = str2ThousandNum;
            boolean maxAfterThousandIsEmpty = str1AfterThousandIsEmpty;
            boolean minAfterThousandIsEmpty = str2AfterThousandIsEmpty;
            if (str2ThousandNum > str1ThousandNum) {
                maxThousandNum = str2ThousandNum;
                minThousandNum = str1ThousandNum;
                maxAfterThousandIsEmpty = str2AfterThousandIsEmpty;
                minAfterThousandIsEmpty = str1AfterThousandIsEmpty;
            }

            if (maxThousandNum - minThousandNum == 1 && (maxAfterThousandIsEmpty && !minAfterThousandIsEmpty)) {
                return false;
            }

            return true;
        } else {
            //  长度不同，且都小于1000,不跨区
            if (weight1Len < 4 && weight2Len < 4) {
                return false;
            } else if (weight1Len < 4) {
                //  1长度小于4，2长度不小于4。当2刚好为1000时，不跨区
                return weight2 != 1000;
            } else if (weight2Len < 4) {
                //  2长度小于4，1长度不小于4。当1刚好为1000时，不跨区
                return weight1 != 1000;
            }
            //  到达这里，1和2长度不同，且都是长度都是大于等4的。那么必然跨区
            return true;
        }
    }


    /**
     * 校验的重量是否超过阈值
     */
    public static boolean checkWeightOverLimit(String weightStr1, Integer weightOverLimit) {
        if (StringUtils.isBlank(weightStr1)) {
            return false;
        }
        int weight1 = gIntValue(weightStr1);
        if(weight1 >= weightOverLimit){
            return true;
        }
        return false;
    }

    public static boolean checkWeightInInterval(String weightStr, Integer weightLimitLeft, Integer weightLimitRight) {
        if (StringUtils.isBlank(weightStr) || weightLimitLeft == null || weightLimitRight == null || weightLimitLeft > weightLimitRight) {
            return false;
        }
        int weight = gIntValue(weightStr);
        return weight > weightLimitLeft && weight < weightLimitRight;
    }

    /**
     * 将单位为g的重量字符串转换为数值
     */
    public static int gIntValue(String gWeightStr) {
        if (StringUtil.isBlank(gWeightStr)) {
            return 0;
        }
        try {
            BigDecimal decimal = new BigDecimal(gWeightStr);
            return decimal.intValue();
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}
