package com.cainiao.waybill.bridge.enterprise.printer.request;

import com.cainiao.waybill.bridge.enterprise.common.Paging;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/4/25
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class EnterprisePrinterRequest extends Paging {

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 打印机名称
     */
    private String printerName;

    /**
     * 打印机编码
     */
    private String printerId;

    /**
     * 打印机验证码
     */
    private String verifyCode;

    /**
     * 场地id
     */
    private Long locationId;

    /**
     * 场地名称
     */
    private String locationName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 绑定结果
     */
    private Boolean bindResult;

    public static boolean checkRequest(EnterprisePrinterRequest request) {
        return !StringUtils.isBlank(request.getPrinterId()) && !StringUtils.isBlank(request.getPrinterName())
                && !StringUtils.isBlank(request.getVerifyCode()) && !StringUtils.isBlank(request.getCorpId());
    }
}
