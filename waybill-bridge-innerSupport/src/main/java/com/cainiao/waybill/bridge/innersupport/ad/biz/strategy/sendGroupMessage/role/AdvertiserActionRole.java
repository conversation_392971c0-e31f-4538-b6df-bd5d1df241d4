package com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.role;

import com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.type.*;
import com.cainiao.waybill.bridge.innersupport.ad.constant.emums.AdUserRoleEnum;

public class AdvertiserActionRole extends ActionRole {
    public AdvertiserActionRole() {
        actionTypes.add(new RemarkUpdateActionType(AdUserRoleEnum.ADVERTISER_ROLE.getValue()));
        actionTypes.add(new RefundCompletedUpdateActionType(AdUserRoleEnum.ADVERTISER_ROLE.getValue()));
        actionTypes.add(new NoRefundCompletedUpdateActionType(AdUserRoleEnum.ADVERTISER_ROLE.getValue()));
        actionTypes.add(new AbnormallyCompletedUpdateActionType(AdUserRoleEnum.ADVERTISER_ROLE.getValue()));
        actionTypes.add(new PendingUpdateActionType(AdUserRoleEnum.ADVERTISER_ROLE.getValue()));
    }
}

