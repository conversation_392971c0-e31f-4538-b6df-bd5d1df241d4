package com.cainiao.waybill.bridge.common.waybill.serivce;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.waybill.dto.request.QueryCloudPrintPictureRequest;

/**
 *
 * 电子面单云打印服务接口
 *
 * Created by shouyuan.lzl on 2017-04-27 8:47 PM.
 */
public interface WaybillCloudPrintService {

    /**
     * 获取电子面单云打印的面单图片数据
     * @param request
     * @param clientInfoDTO
     * @return
     */
    BaseResultDTO<byte[]> queryCloudPrintPicture(QueryCloudPrintPictureRequest request, ClientInfoDTO clientInfoDTO) ;

}
