package com.cainiao.waybill.bridge.biz.hsf;

import com.cainiao.cne.sns.client.dto.base.CneResult;
import com.cainiao.cne.sns.common.dto.ReceiveOrderQueryDTO;
import com.cainiao.cne.sns.common.enums.ReceiveOrderDTO;
import com.cainiao.geography.api.bean.JurisdictionChain;
import com.cainiao.geography.api.bean.VersionSpec;
import com.cainiao.geography.api.request.QueryAddr;
import com.cainiao.geography.api.response.QueryResp;
import com.cainiao.routingservice.common.dto.ClientInfoDTO;
import com.cainiao.routingservice.common.dto.reach.ReachableServiceResponseDTO;
import com.cainiao.routingservice.common.dto.reach.RoutingReachableRequestDTO;
import com.cainiao.routingservice.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.ComputingCourierInfo;
import com.taobao.tair.ResultCode;

import java.io.Serializable;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/23-上午10:10
 */
public interface RouteTestService {
    BaseResultDTO<ReachableServiceResponseDTO> routingReachable(RoutingReachableRequestDTO requestDTO, ClientInfoDTO clientInfoDTO);

    Object get(Serializable key);

    List<ComputingCourierInfo> getCourier(String key);

    ResultCode put(Serializable key, Serializable value, int expireTime);

    QueryResp<List<JurisdictionChain>> queryJurisdictionByAddr(String var1, QueryAddr var2, VersionSpec var3);

    String isTownship(String start,String end,List<Integer> statusList) throws ParseException;

    Integer prefixDecr(Serializable pkey, Serializable skey, int value, int defaultValue, int expireTime);

    ResultCode prefixSetCount(Serializable pkey, Serializable skey, int count);

    Object prefixGet(Serializable pkey, Serializable skey);

    String routingJudge(String bucketName, String fileName, boolean senderJudge, int thread, int maxNum,  List<String> cpList);

    CneResult<ReceiveOrderDTO> zmkmRoutingReachable(ReceiveOrderQueryDTO receiveOrderQueryDTO) throws Exception;


}
