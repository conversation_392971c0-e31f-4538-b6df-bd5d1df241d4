package com.cainiao.waybill.bridge.biz.utils.pickup;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Date;

import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.ObjectMetadata;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Workbook;

/**
 * excel工具
 * <AUTHOR>
 * @date 2025-03-20
 */
@Slf4j
public class ExcelUtil {

    private static final String XLSX = ".xlsx";

    private static final String XLS = ".xls";

    /**
     * 导出excel文件到oss
     * @param workbook
     * @param dir
     * @return
     */
    public static String exportExcelToOss(Workbook workbook, String dir) {
        if (null == workbook) {
            throw new BridgeBusinessException("workbook is null", "导出文件失败");
        }

        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setObjectAcl(CannedAccessControlList.PublicRead);
        metadata.setExpirationTime(DateUtils.addMinutes(new Date(), 15));

        String date = DateUtils.dateToStr(new Date(), DateUtils.patternSimple24hh);
        String fileOrigionName = workbook.getSheetName(0);
        String suffix = XLSX;
        if(fileOrigionName.endsWith(XLSX)){
            fileOrigionName = fileOrigionName.substring(0, fileOrigionName.length() - 5);
            suffix = XLSX;
        }else if(fileOrigionName.endsWith(XLS)){
            fileOrigionName = fileOrigionName.substring(0, fileOrigionName.length() - 4);
            suffix = XLS;
        }

        String fileName = dir + "/" + fileOrigionName +"_" + date + suffix;
        return OssUtils.uploadToOSS(fileName, convert2InputStream(workbook), metadata);
    }

    /**
     * workbook转为流
     *
     * @param wb
     * @return
     */
    private static InputStream convert2InputStream(Workbook wb) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            wb.write(bos);
            byte[] bytes = bos.toByteArray();
            return new ByteArrayInputStream(bytes);
        } catch (Throwable e) {
            log.error("WORKBOOK_CONVERT_STREAM_ERROR", e);
            return null;
        }
    }

    /**
     * 从Cell中获取value
     * @param cell Excel的一个单元格
     * @return 字符串
     */
    public static String getCellValue (Cell cell ){
        String res;
        //如果单元格不存在或者内容为空,返回""
        if( cell == null || cell.getCellType() == Cell.CELL_TYPE_BLANK ){
            res = "";
        }
        //如果数字或者布尔类型,转化为字符串
        else if( cell.getCellType() == Cell.CELL_TYPE_NUMERIC || cell.getCellType() == Cell.CELL_TYPE_BOOLEAN){
            res = String.valueOf(cell.getNumericCellValue());
        }
        //如果是字符串类型,直接返回
        else if( cell.getCellType() == Cell.CELL_TYPE_STRING) {
            res = cell.getStringCellValue();
        } else { // 可能存在公式cell等
            res = "";
        }
        //如果获取到的内容为空,那么返回空字符串
        if( res == null ){
            res = "";
        }
        return StringUtils.trim(res);
    }



}
