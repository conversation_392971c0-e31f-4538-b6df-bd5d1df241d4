package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant;
import com.cainiao.waybill.bridge.common.util.LoggerMonitorUtil;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.api.open.ReceiveService;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_ORDER_INFO_PUSH.FeeDetail;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_ORDER_INFO_PUSH.OrderInfo;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_ORDER_INFO_PUSH.PackageInfo;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_ORDER_INFO_PUSH.WaybillPickUpOrderInfoPushRequest;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_ORDER_INFO_PUSH_BATCH.RouteItem;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_ORDER_INFO_PUSH_BATCH.WaybillPickUpOrderInfoPushBatchRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PICK_UP_ORDER_INFO_PUSH.WaybillPickUpOrderInfoPushResponse;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PICK_UP_ORDER_INFO_PUSH_BATCH.WaybillPickUpOrderInfoPushBatchResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 接收CP批量轨迹推送
 * <AUTHOR> on 2024/6/5.
 */
public class WaybillPickUpOrderInfoPushBatchServiceImpl implements ReceiveService<WaybillPickUpOrderInfoPushBatchRequest, WaybillPickUpOrderInfoPushBatchResponse> {

    @Resource
    WaybillPickUpOrderInfoPushServiceForLinkImpl waybillPickUpOrderInfoPushServiceForLinkImpl;

    @Override
    public WaybillPickUpOrderInfoPushBatchResponse invoke(ReceiveParams<WaybillPickUpOrderInfoPushBatchRequest> receiveParams) {
        PickUpLogUtil.info("WaybillPickUpOrderInfoPushServiceBatchImpl invoke {}", JSON.toJSONString(receiveParams));
        try {
            final WaybillPickUpOrderInfoPushBatchRequest requestDataObject = receiveParams.getRequestDataObject();
            final List<RouteItem> routeList = requestDataObject.getRouteList();
            if (CollectionUtils.isEmpty(routeList)) {
                return new WaybillPickUpOrderInfoPushBatchResponse();
            }
            final ArrayList<WaybillPickUpOrderInfoPushResponse> failed = new ArrayList<>();
            for (RouteItem routeItem : routeList) {
                final ReceiveParams<WaybillPickUpOrderInfoPushRequest> copy = new ReceiveParams<>();
                copy.setFromAppkey(receiveParams.getFromAppkey());
                copy.setToAppkey(receiveParams.getToAppkey());
                copy.setCpCode(receiveParams.getCpCode());
                copy.setToAppkey(receiveParams.getToAppkey());
                final WaybillPickUpOrderInfoPushRequest dataObject = new WaybillPickUpOrderInfoPushRequest();
                copy.setMsgType(dataObject.getApi());
                dataObject.setOrderId(routeItem.getOrderId());
                dataObject.setBizOrderId(routeItem.getBizOrderId());
                dataObject.setMailNo(routeItem.getMailNo());
                if (routeItem.getOrderInfo() != null) {
                    final OrderInfo orderInfo = new OrderInfo();
                    BeanUtils.copyProperties(routeItem.getOrderInfo(), orderInfo);
                    dataObject.setOrderInfo(orderInfo);
                }
                if (routeItem.getPackageInfo() != null) {
                    final PackageInfo packageInfo = new PackageInfo();
                    BeanUtils.copyProperties(routeItem.getPackageInfo(), packageInfo);
                    dataObject.setPackageInfo(packageInfo);
                }
                if (routeItem.getFeeList() != null) {
                    final List<FeeDetail> collect = routeItem.getFeeList().stream().map(fee -> {
                        final FeeDetail feeDetail = new FeeDetail();
                        BeanUtils.copyProperties(fee, feeDetail);
                        return feeDetail;
                    }).collect(Collectors.toList());
                    dataObject.setFeeList(collect);
                }

                copy.setRequestDataObject(dataObject);
                final WaybillPickUpOrderInfoPushResponse invoke = waybillPickUpOrderInfoPushServiceForLinkImpl.invoke(copy);
                if (!invoke.isSuccess()) {
                    PickUpLogUtil.error("WaybillPickUpOrderInfoPushServiceBatchImpl invoke fail on request {}, {} ", JSON.toJSONString(copy), JSON.toJSONString(invoke));
                    LoggerMonitorUtil.monitor(PickUpConstants.Error.WAYBILL_PICK_UP_ORDER_INFO_PUSH_BATCH_ONE_FAIL.getErrorCode(), invoke.getErrorMsg());
                    failed.add(invoke);
                }
            }
            if (!failed.isEmpty()) {
                // 如果全失败，则返回失败，大部分是单个
                if (failed.size() == routeList.size()) {
                    final WaybillPickUpOrderInfoPushBatchResponse response = new WaybillPickUpOrderInfoPushBatchResponse();
                    response.setSuccess(false);
                    response.setErrorCode(failed.stream().map(WaybillPickUpOrderInfoPushResponse::getErrorCode).collect(Collectors.joining(";")));
                    response.setErrorMsg(failed.stream().map(WaybillPickUpOrderInfoPushResponse::getErrorMsg).collect(Collectors.joining(";")));
                    return response;
                } else {
                    // 部分失败，人工处理
                    final String errorMsg = failed.stream().map(WaybillPickUpOrderInfoPushResponse::getErrorMsg).collect(Collectors.joining(","));
                    LoggerMonitorUtil.monitor(PickUpConstants.Error.WAYBILL_PICK_UP_ORDER_INFO_PUSH_BATCH_SOME_FAIL.getErrorCode(), errorMsg);
                    return new WaybillPickUpOrderInfoPushBatchResponse();
                }
            }
            return new WaybillPickUpOrderInfoPushBatchResponse();
        } catch (Exception e) {
            PickUpLogUtil.error("WaybillPickUpOrderInfoPushServiceBatchImpl invoke error: {}", ExceptionUtils.getStackTrace(e));
            LoggerMonitorUtil.monitor(PickUpConstants.Error.WAYBILL_PICK_UP_ORDER_INFO_PUSH_BATCH_ERROR.getErrorCode(), e.getMessage());
            final WaybillPickUpOrderInfoPushBatchResponse response = new WaybillPickUpOrderInfoPushBatchResponse();
            response.setSuccess(false);
            response.setErrorCode(BridgeErrorConstant.BaseError.UNKNOWN.getErrorCode());
            response.setErrorMsg(BridgeErrorConstant.BaseError.UNKNOWN.getErrorMsg());
            return response;
        }
    }
}
