package com.cainiao.waybill.bridge.web.charity.controller;

import com.cainiao.waybill.bridge.biz.charity.constant.CharityConstant;
import com.cainiao.waybill.bridge.biz.charity.constant.annotation.RateLimitAnnotation;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CharityOrgTypeEnum;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CharityRoleEnum;
import com.cainiao.waybill.bridge.biz.charity.request.*;
import com.cainiao.waybill.bridge.biz.charity.response.CharityActivityPictureQueryResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityOrgResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityPagingResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityPkgBatchDownResponse;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityActivityPictureService;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityOrgService;
import com.cainiao.waybill.bridge.biz.charity.util.CharityCacheUtil;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.model.charity.DTO.WaybillCharityFileDTO;
import com.cainiao.waybill.bridge.model.charity.DTO.WaybillCharityFileParameter;
import com.cainiao.waybill.bridge.model.charity.DTO.WaybillCharityOrgDTO;
import com.cainiao.waybill.bridge.web.charity.CharityBaseResult;
import io.swagger.annotations.Api;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 活动图片界面管理
 * <AUTHOR>
 */
@Api(tags = "图片界面管理")
@RestController
@RequestMapping(value = "/charity/picture")
public class WaybillCharityActivityPictureController extends BaseController{

    @Autowired
    private WaybillCharityActivityPictureService waybillCharityActivityPictureService;

    @Autowired
    private WaybillCharityOrgService charityOrgService;

    @Autowired
    private CharityCacheUtil charityCacheUtil;

    public static final String CACHE_PREFIX = "charity_download_key_";
    private static final Executor threadPoolExecutor =
            new ThreadPoolExecutor(2, 5,1, TimeUnit.HOURS,
                    new ArrayBlockingQueue<>(128),new ThreadPoolExecutor.AbortPolicy());

    /**
     * 活动图片列表查询
     * 支持多条件
     */
    @ResponseBody
    @RequestMapping("/list")
    public CharityBaseResult<CharityPagingResponse<CharityActivityPictureQueryResponse>> list(@RequestBody CharityActivityPictureQueryRequest activityPictureQueryRequest){
        BridgeValidator.validate(activityPictureQueryRequest);
        return CharityBaseResult.success(waybillCharityActivityPictureService.queryCharityPictureList(activityPictureQueryRequest));
    }

    ///**
    // * 活动图片预览
    // * 支持多条件
    // */
    //@ResponseBody
    //@RequestMapping("/view")
    //public CharityBaseResult<String> view(@RequestBody CharityActivityPictureViewRequest activityPictureViewRequest){
    //    BridgeValidator.validate(activityPictureViewRequest);
    //    return CharityBaseResult.success(waybillCharityActivityPictureService.generatePkgImageUrl(activityPictureViewRequest.getImgName(),false));
    //}

    /**
     * 活动图片备注
     * 支持多条件
     */
    @ResponseBody
    @RequestMapping("/mark")
    public CharityBaseResult<Void> mark(@RequestBody CharityActivityPictureMarkRequest activityPictureMarkRequest){
        BridgeValidator.validate(activityPictureMarkRequest);
        // 鉴权 管理员可修改所有记录；非管理员，机构类型为省级中心，仅能修改本省的记录；非管理员，机构类型为非省级中心，仅能修改本机构的记录。
        boolean isAdmin = CharityRoleEnum.admin.name().equals(activityPictureMarkRequest.getRole());
        if(!isAdmin){
            WaybillCharityFileParameter charityFileParameter = new WaybillCharityFileParameter();
            charityFileParameter.setId(activityPictureMarkRequest.getImgId());
            WaybillCharityFileDTO waybillCharityFileDTO = waybillCharityActivityPictureService.findOne(charityFileParameter);
            if(null == waybillCharityFileDTO){
                return CharityBaseResult.bizFail("PICTURE_NOT_FOUND","图片不存在");
            }
            CharityOrgResponse orgResponse = charityOrgService.findByCode(CharityConstant.WARM_PROJECT, waybillCharityFileDTO.getOrgCode());
            if(null == orgResponse){
                return CharityBaseResult.bizFail("CHARITY_NOT_FOUND","执行单位不存在");
            }
            // 查询所属机构类型是否有省级中心机构
            List<WaybillCharityOrgDTO> belongOrgList = charityOrgService.listOrgByUserId(CharityConstant.WARM_PROJECT, activityPictureMarkRequest.getLoginUserId());

            List<WaybillCharityOrgDTO> provinceOrgList = belongOrgList.stream().filter(
                orgInfo -> CharityOrgTypeEnum.provinceCenter.getCode().equals(orgInfo.getType())).collect(
                Collectors.toList());

            // 省级机构仅判定省份一致即可
            List<WaybillCharityOrgDTO> filterList = provinceOrgList.stream().filter(
                item -> StringUtils.equals(orgResponse.getProvince(), item.getProvince())).collect(
                Collectors.toList());
            // 其他非省级中心机构需判定是同一机构
            List<WaybillCharityOrgDTO> filterSelfList = belongOrgList.stream().filter(
                item -> StringUtils.equals(orgResponse.getOrgCode(), item.getOrgCode())).collect(
                Collectors.toList());
            if(CollectionUtils.isEmpty(filterList) && CollectionUtils.isEmpty(filterSelfList)){
                return CharityBaseResult.bizFail("PERMISSION_ERROR","您无该机构权限无法修改包裹详细信息");
            }

        }

        waybillCharityActivityPictureService.markContent(activityPictureMarkRequest);
        return CharityBaseResult.success();
    }

    /**
     * 上传本地文件到oss
     */
    @ResponseBody
    @RequestMapping("/upload")
    public CharityBaseResult<String> upload(@RequestParam("file") MultipartFile file){
        if(file == null){
            return CharityBaseResult.fail("上传文件不存在");
        }
        waybillCharityActivityPictureService.uploadImgToOss(file);
        return CharityBaseResult.success("上传文件："+file.getOriginalFilename()+",成功!");
    }


    /**
     * 批量下载/打包包裹图片,返回下载zip包的url地址
     */
    @ResponseBody
    @RequestMapping("/download")
    @RateLimitAnnotation
    public CharityBaseResult<CharityPkgBatchDownResponse> batch(@RequestBody CharityPkgBatchDownRequest pkgBatchDownRequest) throws IOException {

        //先对文件名列表进行判空或者去重
        List<String> imgFileNameList = pkgBatchDownRequest.getImgFileNameList();
        if (CollectionUtils.isEmpty(imgFileNameList)){
            return CharityBaseResult.bizFail("download_list_null","下载列表为空");
        }
        List<String> imgNameList = imgFileNameList.stream().distinct().collect(Collectors.toList());
        String zipNameKey = UUID.randomUUID().toString();

        //先返回结果，再进行异步处理作业
        CompletableFuture.runAsync(() -> waybillCharityActivityPictureService.batchDownloadCharityFile(zipNameKey,imgNameList),threadPoolExecutor);

        CharityPkgBatchDownResponse batchDownResponse = new CharityPkgBatchDownResponse();
        charityCacheUtil.put(CACHE_PREFIX+zipNameKey,"",60 * 60 * 2);
        batchDownResponse.setStatus("init");
        batchDownResponse.setZipNameKey(zipNameKey);
        batchDownResponse.setDownloadUrl(charityCacheUtil.get(CACHE_PREFIX+zipNameKey).getValue().toString());
        return CharityBaseResult.success(batchDownResponse);
    }


    /**
     * 前端轮询下载任务是否完成,完成可以拿到url
     */
    @ResponseBody
    @RequestMapping("/ask")
    public CharityBaseResult<CharityPkgBatchDownResponse> ask(@RequestBody CharityActivityPictureAskRequest activityPictureAskRequest){
        BridgeValidator.validate(activityPictureAskRequest);
        return CharityBaseResult.success(waybillCharityActivityPictureService.isCompleteDownload(activityPictureAskRequest.getZipNameKey()));
    }

}
