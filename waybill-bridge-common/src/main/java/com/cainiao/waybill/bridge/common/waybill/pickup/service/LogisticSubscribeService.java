package com.cainiao.waybill.bridge.common.waybill.pickup.service;

import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.waybill.pickup.dto.LogisticSubscribeDTO;

/**
 * 物流详情订阅服务
 *
 * 一期我们主动为电商平台进行订阅物流详情
 * 后面会开放订阅能力给到外部平台
 *
 * <AUTHOR>
 * @date 2021-06-16
 */
public interface LogisticSubscribeService {


    /**
     * 订阅物流详情
     *
     * @param subscribeDTO 订阅物流详情信息
     * @return 成功或者失败
     */
    BaseResultDTO<Void> subscribe(LogisticSubscribeDTO subscribeDTO);
}
