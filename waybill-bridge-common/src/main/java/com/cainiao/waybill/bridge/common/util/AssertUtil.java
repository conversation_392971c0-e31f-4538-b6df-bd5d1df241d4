package com.cainiao.waybill.bridge.common.util;

import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;

/**
 * 断言工具类
 * <AUTHOR>
 */
public class AssertUtil {

    /**
     * 基础断言校验类  条件成立，打印异常消息
     * @param prediction
     * @param msg
     */
    public static void assertValidate(boolean prediction,String msg){
        if(prediction){
            throw new BridgeBusinessException("entry_conditions_not_match",msg);
        }
    }
}
