package com.cainiao.waybill.bridge.web.pickup.ticket.role;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;

import com.cainiao.cnlogin.api.context.CnUserInfo;
import com.cainiao.cnlogin.api.context.CnUserInfoUtil;
import com.cainiao.waybill.bridge.biz.common.user.WaybillBridgeUserInfoService;
import com.cainiao.waybill.bridge.biz.pickup.constants.BridgeUserFeatureKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketConstant.ExtraInfoKey;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.constants.UserInfoSceneEnum;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeUserInfoDTO;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.biz.ticket.dto.TicketRoleEnum;
import com.cainiao.waybill.common.util.FeatureUtils;
import com.cainiao.waybill.common.util.StringUtil;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @date 2022/4/22 上午11:56
 */
@Slf4j
public class CheckPickUpTicketRoleInterceptor implements HandlerInterceptor {

    @Setter
    private WaybillBridgeUserInfoService userInfoService;

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) throws Exception {
        //log.info("preHandleUrl:" + httpServletRequest.getRequestURI());
        CnUserInfo userInfo = CnUserInfoUtil.getLoginContext();
        if(userInfo == null || StringUtil.isBlank(userInfo.getAccount())){
            writeUserAuthErrorResponse(httpServletResponse);
            return false;
        }
        WaybillBridgeUserInfoDTO userInfoDTO = userInfoService.findCharityUser(userInfo.getAccount(), UserInfoSceneEnum.Ntb.name());
        if(userInfoDTO == null){
            writeUserAuthErrorResponse(httpServletResponse);
            return false;
        }
        PickUpLoginUserUtil.buildBaseReq(userInfoDTO.getId(), userInfoDTO.getPhone(),
            UserInfoSceneEnum.Ntb.name(), userInfoDTO.getRole(), userInfoDTO.getFeature());
        return true;
    }

    private void writeUserAuthErrorResponse(HttpServletResponse response) throws IOException {
        response.setContentType("text/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(BaseResult.bizFail("user_no_right", "用户无权限")));
        writer.flush();
        writer.close();
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }
}
