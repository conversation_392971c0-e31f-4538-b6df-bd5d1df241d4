package com.cainiao.waybill.bridge.enterprise.post.request;

import com.cainiao.waybill.bridge.enterprise.common.Paging;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseSourceFormEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/20
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class EnterprisePostRequest extends Paging {

    /**
     * 小邮局主键id
     */
    private String postId;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 小邮局名称
     */
    private String postName;

    /**
     * 场地id
     */
    private String locationId;

    /**
     * 小邮局管理员
     */
    private List<String> postAdminList;

    /**
     * 备注
     */
    private String remake;

    /**
     * 扩展字段
     */
    private String feature;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 来源
     * @see EnterpriseSourceFormEnum
     */
    private String sourceFrom;
}
