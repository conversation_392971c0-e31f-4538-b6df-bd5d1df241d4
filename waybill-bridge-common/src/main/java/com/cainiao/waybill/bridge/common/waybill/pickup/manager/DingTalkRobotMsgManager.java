package com.cainiao.waybill.bridge.common.waybill.pickup.manager;

import java.io.IOException;
import java.util.List;

/**
 * 钉钉机器人消息发送
 *
 * <AUTHOR>
 * @date 2021/9/29-上午11:18
 */
public interface DingTalkRobotMsgManager {

    /**
     * 发送text类型的消息
     *
     * @param hook    : 新建机器人时候的hook地址
     * @param content : 发送的内容
     * @return : 是否发送成功
     */
    boolean sendDingTextMsg(String hook, String content) throws IOException;

    boolean sendDingTextMsg(String hook, String content, String keyWord, List<String> atMobile) throws IOException;

    /**
     * 发送Markdown来类型消息
     *
     * @param hook      :
     * @param title     ：使用关键字的话，关键字需要放在这里
     * @param content   ：
     * @param atMobiles ：
     */
    Boolean sendDingMarkdownMsg(String hook, String title, String content, List<String> atMobiles,boolean atAll) throws Exception;

}
