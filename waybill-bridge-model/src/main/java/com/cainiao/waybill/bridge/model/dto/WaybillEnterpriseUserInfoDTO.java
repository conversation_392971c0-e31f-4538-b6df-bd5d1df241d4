package com.cainiao.waybill.bridge.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/16
 **/
@Data
public class WaybillEnterpriseUserInfoDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户类型
     * com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseUserTypeEnum
     */
    private String userType;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 来源平台
     * com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePlatformEnum
     */
    private String platform;

    /**
     * 手机号
     */
    private String phoneNum;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 手机号列表
     */
    private List<String> phoneNumList;

    /**
     * 场地主键ID
     */
    private String locationId;

    /**
     * 小邮局业务唯一ID
     */
    private String postId;

    /**
     * 部门列表
     */
    private List<UserDeptInfoDTO> deptList;
}
