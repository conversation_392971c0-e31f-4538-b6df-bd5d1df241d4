package com.cainiao.waybill.bridge.enterprise.administrators.controller;

import com.cainiao.waybill.bridge.enterprise.administrators.request.DeleteRequest;
import com.cainiao.waybill.bridge.model.mapper.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/6/11
 * @description 测试
 **/
@RestController
@RequestMapping(value = "/enterprise/test")
public class TestController {

    @Resource
    WaybillBridgeEnterprisePostMapper enterprisePostMapper;

    @Resource
    WaybillBridgeEnterprisePrinterMapper enterprisePrinterMapper;

    @Resource
    WaybillBridgeEnterpriseSettleAccountMapper enterpriseSettleAccountMapper;

    @Resource
    WaybillEnterpriseLocationMapper enterpriseLocationMapper;

    @Resource
    WaybillBridgeEnterpriseOrderMapper enterpriseOrderMapper;

    @Resource
    WaybillBridgeEnterpriseUserPhoneNumMapper enterpriseUserPhoneNumMapper;

    @Resource
    WaybillBridgeEnterprisePackInboundMapper enterprisePackInboundMapper;



    @PostMapping("/delete")
    public void deleteData(@RequestBody DeleteRequest deleteRequest) {
        if (CollectionUtils.isEmpty(deleteRequest.getId())) {
            return;
        }
        for (Long id : deleteRequest.getId()) {
            enterprisePostMapper.deleteByPrimaryKey(id);
            enterprisePrinterMapper.deleteByPrimaryKey(id);
            enterpriseSettleAccountMapper.deleteByPrimaryKey(id);
            enterpriseLocationMapper.deleteByPrimaryKey(id);
            enterpriseOrderMapper.deleteByPrimaryKey(id);
            enterpriseUserPhoneNumMapper.deleteByPrimaryKey(id);
            enterprisePackInboundMapper.deleteByPrimaryKey(id);
        }
    }

}
