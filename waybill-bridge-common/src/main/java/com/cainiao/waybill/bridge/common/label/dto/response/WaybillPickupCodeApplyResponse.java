package com.cainiao.waybill.bridge.common.label.dto.response;


import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/4/25.
 * 揽件码面单申请结果
 */
public class WaybillPickupCodeApplyResponse extends BaseDTO {

    private static final long serialVersionUID = -4881246894039377646L;

    /**
     * 电子面单号
     */
    private String waybillCode;

    /**
     * 揽件码
     */
    private String pickupCode;

    public String getWaybillCode() {
        return waybillCode;
    }

    public void setWaybillCode(String waybillCode) {
        this.waybillCode = waybillCode;
    }

    public String getPickupCode() {
        return pickupCode;
    }

    public void setPickupCode(String pickupCode) {
        this.pickupCode = pickupCode;
    }
}
