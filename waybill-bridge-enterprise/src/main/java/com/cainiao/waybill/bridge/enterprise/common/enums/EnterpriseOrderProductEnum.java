package com.cainiao.waybill.bridge.enterprise.common.enums;


import java.util.Objects;

public enum EnterpriseOrderProductEnum {
    /**
     * 顺丰标快产品
     */
    SF_EXPRESS(0, "SF_EXPRESS"),
    /**
     * 特快
     */
    EXPRESS(1, "EXPRESS"),

    /**
     * 同城
     */
    TC(2, "TC"),

    /**
     * 标快
     */
    STANDARD(3, "STANDARD");

    ;

    final Integer code;
    final String describe;

    EnterpriseOrderProductEnum(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }
    public Integer getCode() {
        return code;
    }
    public String getDescribe() {
        return describe;
    }


    public static Integer getCodeByDesc(String desc) {
        if (null == desc) {
            return null;
        }
        for (EnterpriseOrderProductEnum value : EnterpriseOrderProductEnum.values()) {
            if(Objects.equals(value.getDescribe(), desc)){
                return value.code;
            }
        }
        return null;
    }

    public static String getByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (EnterpriseOrderProductEnum value : EnterpriseOrderProductEnum.values()) {
            if(Objects.equals(value.getCode(), code)){
                return value.describe;
            }
        }
        return null;
    }
}
