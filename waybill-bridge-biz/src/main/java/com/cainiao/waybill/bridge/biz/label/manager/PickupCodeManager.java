package com.cainiao.waybill.bridge.biz.label.manager;

import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-27
 */
public interface PickupCodeManager {

    /**
     * 生成揽件码逻辑
     *
     * @param cpCode
     * @param branchCode 网点code
     * @return 揽件码，例如 "YT123456"
     * @throws BridgeBaseException
     */
    String generatePickupCode(String cpCode, String branchCode) throws BridgeBaseException;

}
