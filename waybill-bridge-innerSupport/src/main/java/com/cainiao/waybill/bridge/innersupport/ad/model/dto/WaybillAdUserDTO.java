package com.cainiao.waybill.bridge.innersupport.ad.model.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * WaybillAdUserDTO
 */
@Data
public class WaybillAdUserDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;
}