package com.cainiao.waybill.bridge.web.pickup.bill;

import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error.Bill;
import com.cainiao.waybill.bridge.biz.pickup.constants.bill.AdjustFileStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.bill.AdjustTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.bill.BillUserTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.dto.bill.BillAdjustApproveResponse;
import com.cainiao.waybill.bridge.biz.pickup.dto.bill.BillAdjustFileDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.bill.BillAdjustDetailRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.bill.BillAdjustInfoDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.bill.BillAdjustRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.bill.BillDetailInfoResponse;
import com.cainiao.waybill.bridge.biz.pickup.dto.bill.BillSummaryInfoDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.bill.BillSummaryInfoRequest;
import com.cainiao.waybill.bridge.biz.pickup.service.BillInfoService;
import com.cainiao.waybill.bridge.biz.ticket.dto.PagingResponse;
import com.cainiao.waybill.bridge.biz.ticket.dto.TicketRoleEnum;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.biz.utils.pickup.OssUtils;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.model.domain.BillAdjustFileInfoDO;
import com.cainiao.waybill.bridge.web.charity.controller.BaseController;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.pickup.constant.TicketResourceEnum;
import com.cainiao.waybill.bridge.web.pickup.ticket.role.PickUpResourceAuthority;
import com.cainiao.waybill.bridge.web.pickup.ticket.util.PickUpExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 账单相关web接口
 * <AUTHOR>
 * @date 2025/3/25 17:42
 **/
@RestController
@Slf4j(topic = "PICK_UP_BILL_INFO")
@RequestMapping(value = "/pickup/bill")
public class BillController  extends BaseController {

    @Resource
    private BillInfoService billInfoService;


    /**
     * 下载调账模板
     * @return
     */
    @PickUpResourceAuthority(TicketResourceEnum.BILL_MANAGER)
    @RequestMapping("/adjust/template/download")
    public BaseResult<String> downloadQuoteTemplate(@RequestBody BillAdjustRequest request){
        if (null == request || AdjustTypeEnum.getByType(request.getAdjustType()) == null) {
            return BaseResult.fail("模板类型无效");
        }
        if (StringUtils.equals(AdjustTypeEnum.WEIGHT.getType(), request.getAdjustType())) {
            return BaseResult.success(OssUtils.generateExpireUrl(BridgeSwitch.ACCOUNT_WEIGHT_ADJUST_TEMPLATE_URL, null));
        }else {
            return BaseResult.success(OssUtils.generateExpireUrl(BridgeSwitch.ACCOUNT_AMOUNT_ADJUST_TEMPLATE_URL, null));
        }
    }

    /**
     * 查询客户/CP账单列表
     * @param request
     * @return
     */
    @RequestMapping("/list")
    @PickUpResourceAuthority(TicketResourceEnum.BILL_MANAGER)
    public BaseResult<PagingResponse<BillSummaryInfoDTO>> queryCustomerOrderList(@RequestBody
    BillSummaryInfoRequest request) {
        try{
            if(null == request || BillUserTypeEnum.getByType(request.getBillUserType()) == null){
                throw new BridgeBusinessException(Bill.BILL_PARAM_ERROR);
            }
            PickUpLogUtil.info("查询账单列表.request:{}", JSONObject.toJSONString(request));
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            PagingResponse<BillSummaryInfoDTO> detailDTO = billInfoService.queryBillList(request);
            return BaseResult.success(detailDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("查询账单列表失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("查询账单列表失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }


    /**
     * 账单出账
     * @param request
     * @return
     */
    @RequestMapping("/issue/bill")
    @PickUpResourceAuthority(TicketResourceEnum.BILL_MANAGER)
    public BaseResult<BillSummaryInfoDTO> issueBill(@RequestBody BillSummaryInfoRequest request) {
        try{
            if(null == request || StringUtils.isBlank(request.getBillId())){
                throw new BridgeBusinessException(Bill.BILL_PARAM_ERROR);
            }
            PickUpLogUtil.info("账单出账.request:{}", JSONObject.toJSONString(request));
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            BillSummaryInfoDTO detailDTO = billInfoService.issueBill(request);
            PickUpLogUtil.info("账单出账成功.{}", JSONObject.toJSONString(detailDTO));
            return BaseResult.success(detailDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("账单出账失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("账单出账失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 账单下载
     * @param request
     * @return
     */
    @RequestMapping("/file/download")
    @PickUpResourceAuthority(TicketResourceEnum.BILL_MANAGER)
    public BaseResult<String> downBillFile(@RequestBody BillSummaryInfoRequest request) {

        try{
            if(null == request || null == request.getBillId()){
                return BaseResult.bizFail(Error.SYSTEM_PARAM_ERROR);
            }
            PickUpLogUtil.info("账单下载.request:{}", JSONObject.toJSONString(request));
            TicketRoleEnum role = getUserRole();
            if(TicketRoleEnum.admin != role){
                return BaseResult.bizFail(Error.NO_PERMISSION_ERROR);
            }
            String url  = PickUpExcelUtil.exportFile(request, "账单明细列表", BillDetailInfoResponse.class,
                request12 -> billInfoService.queryBillDetailList(request), BridgeSwitch.BILL_DETAIL_EXPORT_LIMIT);
            PickUpLogUtil.info("账单下载成功.url:{}", url);
            return BaseResult.success(url);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("账单下载失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("账单下载失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }

    }


    /**
     * 账单确认
     * @param request
     * @return
     */
    @RequestMapping("/confirm/bill")
    @PickUpResourceAuthority(TicketResourceEnum.BILL_MANAGER)
    public BaseResult<BillSummaryInfoDTO> confirmBill(@RequestBody BillSummaryInfoRequest request) {
        try{
            PickUpLogUtil.info("账单确认.request:{}", JSONObject.toJSONString(request));
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            BillSummaryInfoDTO detailDTO = billInfoService.confirmBill(request);
            PickUpLogUtil.info("账单确认成功.{}", JSONObject.toJSONString(detailDTO));
            return BaseResult.success(detailDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("账单出账失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("账单出账失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 查询历史调账文件列表
     * @param request
     * @return
     */
    @RequestMapping("/adjust/history/list")
    @PickUpResourceAuthority(TicketResourceEnum.BILL_MANAGER)
    public BaseResult<List<BillAdjustFileDTO>> adjustHistoryList(@RequestBody BillAdjustRequest request) {
        try{
            PickUpLogUtil.info("查询历史调账文件列表.request:{}", JSONObject.toJSONString(request));
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            List<BillAdjustFileDTO> list = billInfoService.queryAdjustHistoryList(request);
            PickUpLogUtil.info("查询历史调账文件列表成功.{}", JSONObject.toJSONString(list));
            return BaseResult.success(list);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("查询历史调账文件列表失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("查询历史调账文件列表失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 上传金额调账文件
     * @return
     */
    @PostMapping("/upload/adjust/amount")
    @PickUpResourceAuthority(TicketResourceEnum.BILL_MANAGER)
    public BaseResult<BillAdjustFileDTO> uploadBillAdjustAmountFile(@RequestParam("file") MultipartFile file, BillAdjustRequest request){
        try{
            PickUpLogUtil.info("上传金额调账文件.request:{}", JSONObject.toJSONString(request));
            checkMenuRoleValid(request.getQuerySource(), request.getRole());

            BillAdjustFileInfoDO billAdjustFileInfoDO
                = billInfoService.parseUploadAdjustFile(file, AdjustTypeEnum.AMOUNT.getType(), request.getLoginUserId());
            BillAdjustFileDTO billAdjustFileDTO = new BillAdjustFileDTO();
            BeanUtils.copyProperties(billAdjustFileInfoDO, billAdjustFileDTO);
            billAdjustFileInfoDO.setStatus(AdjustFileStatusEnum.UPLOAD.getStatus());
            int num = billInfoService.saveAdjustFile(billAdjustFileInfoDO);
            if(num <= 0){
                throw new BridgeBusinessException(Bill.BILL_ADJUST_FILE_SAVE_ERROR);
            }
            billAdjustFileDTO.setUploadUserName(getUserNick());
            billAdjustFileDTO.setAdjustTypeDesc(AdjustTypeEnum.AMOUNT.getDesc());

            PickUpLogUtil.info("上传金额调账文件成功.{}", JSONObject.toJSONString(billAdjustFileDTO));
            return BaseResult.success(billAdjustFileDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("上传金额调账文件失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("上传金额调账文件失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 上传重量调账文件
     * @return
     */
    @PostMapping("/upload/adjust/weight")
    @PickUpResourceAuthority(TicketResourceEnum.BILL_MANAGER)
    public BaseResult<BillAdjustFileDTO> uploadBillAdjustWeightFile(@RequestParam("file") MultipartFile file, BillAdjustRequest request){
        try{
            PickUpLogUtil.info("上传重量调账文件.request:{}", JSONObject.toJSONString(request));
            checkMenuRoleValid(request.getQuerySource(), request.getRole());

            BillAdjustFileInfoDO billAdjustFileInfoDO
                = billInfoService.parseUploadAdjustFile(file, AdjustTypeEnum.WEIGHT.getType(), request.getLoginUserId());
            BillAdjustFileDTO billAdjustFileDTO = new BillAdjustFileDTO();
            BeanUtils.copyProperties(billAdjustFileInfoDO, billAdjustFileDTO);
            billAdjustFileInfoDO.setStatus(AdjustFileStatusEnum.UPLOAD.getStatus());
            int num = billInfoService.saveAdjustFile(billAdjustFileInfoDO);
            if(num <= 0){
                throw new BridgeBusinessException(Bill.BILL_ADJUST_FILE_SAVE_ERROR);
            }

            billAdjustFileDTO.setUploadUserName(getUserNick());
            billAdjustFileDTO.setAdjustTypeDesc(AdjustTypeEnum.WEIGHT.getDesc());

            PickUpLogUtil.info("上传重量调账文件成功.{}", JSONObject.toJSONString(billAdjustFileDTO));
            return BaseResult.success(billAdjustFileDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("上传重量调账文件失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("上传重量调账文件失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 上传文件
     * @return
     */
    @PostMapping("/upload/demo")
    @PickUpResourceAuthority(TicketResourceEnum.BILL_MANAGER)
    public BaseResult<String> demoUpload(@RequestParam("file") MultipartFile file, BillAdjustRequest request){
        try{
            PickUpLogUtil.info("上传文件.request:{}", JSONObject.toJSONString(request));
            String url = billInfoService.uploadDemoFile(file);
            return BaseResult.success(url);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("上传文件", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("上传文件", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 调账调账明细保存(解析文件详情保存)
     * @param request
     * @return
     */
    @RequestMapping("/adjust/detail/save")
    @PickUpResourceAuthority(TicketResourceEnum.BILL_MANAGER)
    public BaseResult<String> adjustDetailSave(@RequestBody BillAdjustDetailRequest request) {
        try{
            PickUpLogUtil.info("账单调账明细保存.request:{}", JSONObject.toJSONString(request));
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            int count = billInfoService.adjustDetailSave(request);
            PickUpLogUtil.info("账单调账明细保存成功.count:{}", count);
            return BaseResult.success("保存成功");
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("账单调账明细保存失败", PickUpConstants.Action.BILL_ADJUST_DETAIL_SAVE_ERROR.name(), e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("账单调账明细保存失败", PickUpConstants.Action.BILL_ADJUST_DETAIL_SAVE_ERROR.name(), "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }


    /**
     * 发起调账审批
     * @param request
     * @return
     */
    @RequestMapping("/adjust/approve")
    @PickUpResourceAuthority(TicketResourceEnum.BILL_MANAGER)
    public BaseResult<BillAdjustApproveResponse> adjustApprove(@RequestBody BillAdjustRequest request) {
        try{
            PickUpLogUtil.info("发起调账审批.request:{}", JSONObject.toJSONString(request));
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            BillAdjustApproveResponse approveResponse = billInfoService.startAdjustApprove(request);
            if(null == approveResponse){
                return BaseResult.bizFail(Error.SYSTEM_ERROR);
            }
            PickUpLogUtil.info("发起调账审批成功.{}", JSONObject.toJSONString(approveResponse));
            return BaseResult.success(approveResponse);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("发起调账审批失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("发起调账审批失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request), e);
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }


    // 确认支付

    // 账单归档




}
