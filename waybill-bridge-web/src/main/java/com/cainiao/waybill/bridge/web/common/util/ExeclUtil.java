package com.cainiao.waybill.bridge.web.common.util;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;

import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.ObjectMetadata;
import com.cainiao.waybill.bridge.biz.utils.pickup.OssUtils;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.BridgeDateUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import static com.alibaba.common.lang.StringEscapeUtil.escapeURL;

/**
 * <AUTHOR>
 * @date 2022/1/29 下午8:06
 */
@Slf4j
public class ExeclUtil {
    @SneakyThrows
    public static List<String[]> loadData(InputStream in) {
        List<String[]> excelList = new ArrayList<>();
        try (Workbook workbook = new XSSFWorkbook(in)) {
            //创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回
            if (workbook == null) {
                return Collections.emptyList();
            }
            //获得当前sheet工作表
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                return Collections.emptyList();
            }
            //从第二行开始
            for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) {
                    continue;
                }
                String[] cells = new String[row.getLastCellNum()];
                //循环当前行
                for (int cellNum = row.getFirstCellNum(); cellNum < row.getLastCellNum(); cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    if (cell == null) {
                        continue;
                    }
                    cells[cellNum] = getCellValue(cell);
                }
                excelList.add(cells);
            }
        }
        return excelList;
    }

    private static String getCellValue(Cell cell) {
        cell.setCellType(CellType.STRING);
        return cell == null ? "" : cell.getStringCellValue();
    }

    public static String exportToOSS(Workbook workbook, String dir) {
        if (null == workbook) {
            throw new BridgeBusinessException("workbook is null", "导出文件失败");
        }

        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setObjectAcl(CannedAccessControlList.PublicRead);
        metadata.setExpirationTime(DateUtils.addMinutes(new Date(), 15));

        String date = BridgeDateUtil.dateToStr(new Date(), BridgeDateUtil.pattern24Format);

        String fileName = dir + "/" + workbook.getSheetName(0) + date + ".xlsx";
        String imgUrl = OssUtils.uploadToOSS(fileName, convert2InputStream(workbook), metadata);
        return imgUrl;
    }

    /**
     * workbook转为流
     *
     * @param wb
     * @return
     */
    private static InputStream convert2InputStream(Workbook wb) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            wb.write(bos);
            byte[] bytes = bos.toByteArray();
            return new ByteArrayInputStream(bytes);
        } catch (Throwable e) {
            log.error("WORKBOOK_CONVERT_STREAM_ERROR", e);
            return null;
        }
    }

}
