package com.cainiao.waybill.bridge.web.pickup.ticket.util;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> zouping.fzp
 * @Classname StrUtil
 * @Description
 * @Date 2022/8/18 8:33 下午
 * @Version 1.0
 */
public class StrUtil {



    public static List<String> split(String str){
        if(StringUtils.isBlank(str)){
            return null;
        }
        String[] strArray = str.split("[,;\\s]+");
        if(strArray.length == 0){
            return null;
        }
        return Arrays.asList(strArray);
    }

    public static void main(String[] args) {
        System.out.println(split("fdadfdafd"));
    }
}
