package com.cainiao.waybill.bridge.web.charity.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.view.InternalResourceViewResolver;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * <AUTHOR> zouping.fzp
 * @Classname SwaggerConfig
 * @Description
 * @Date 2022/8/29 9:55 上午
 * @Version 1.0
 */
public class SwaggerConfig {

    //@Bean
    //public InternalResourceViewResolver defaultViewResolver() {
    //    return new InternalResourceViewResolver();
    //}
    //
    //@Bean
    //public Docket createRestApi() {
    //    return new Docket(DocumentationType.SWAGGER_2)  // OAS_30
    //        .enable(true)  // 仅在开发环境开启Swagger
    //        .apiInfo(apiInfo())
    //        .host("https://pre-waybill.bridge.cainiao.com")  // Base URL
    //        .select()
    //        .apis(RequestHandlerSelectors.basePackage("com.cainiao.waybill.bridge.web.charity.controller"))
    //        .paths(PathSelectors.any())
    //        .build();
    //}
    //
    //private ApiInfo apiInfo() {
    //    return new ApiInfoBuilder()
    //        .title("API文档")
    //        .description("这是描述信息")
    //        .contact(new Contact("张三", null, null))
    //        .version("1.0")
    //        .build();
    //}
}
