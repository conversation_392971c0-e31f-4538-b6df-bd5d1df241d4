package com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.role;

import com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder.WaybillAdWorkOrderUpdateRequest;
import com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.type.ActionType;
import com.cainiao.waybill.bridge.innersupport.ad.model.dto.WaybillAdWorkOrderDTO;

import java.util.ArrayList;
import java.util.List;

public abstract class ActionRole {
    protected List<ActionType> actionTypes = new ArrayList<>();

    public void handleChanges(WaybillAdWorkOrderUpdateRequest request, WaybillAdWorkOrderDTO existingWorkOrder) {
        for (ActionType actionType : actionTypes) {
            if (actionType.hasChanged(request, existingWorkOrder)) {
                actionType.handleChange(request, existingWorkOrder);
            }
        }
    }
}
