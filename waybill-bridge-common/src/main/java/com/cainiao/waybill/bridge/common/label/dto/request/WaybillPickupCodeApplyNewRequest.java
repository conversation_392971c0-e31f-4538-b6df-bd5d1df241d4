package com.cainiao.waybill.bridge.common.label.dto.request;


import com.cainiao.waybill.bridge.common.dto.BaseDTO;
import com.cainiao.waybill.bridge.common.label.dto.MailOrderInfoDTO;

/**
 * 新增揽件码面单请求参数
 *
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/4/25.
 */
public class WaybillPickupCodeApplyNewRequest extends BaseDTO {

    private static final long serialVersionUID = -6219741461307360635L;

    /**
     * 发货订单信息
     */
    private MailOrderInfoDTO mailOrderInfoDTO;

    /**
     * 商家id
     */
    private Long sellerId;

    /**
     * 商家名称
     */
    private String sellerName;

    /**
     * 店铺名称(搜索展示)
     */
    private String shopName;

    /**
     * cp 编码
     */
    private String cpCode;

    /**
     * 网点编码
     */
    private String branchCode;

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public MailOrderInfoDTO getMailOrderInfoDTO() {
        return mailOrderInfoDTO;
    }

    public void setMailOrderInfoDTO(MailOrderInfoDTO mailOrderInfoDTO) {
        this.mailOrderInfoDTO = mailOrderInfoDTO;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }
}
