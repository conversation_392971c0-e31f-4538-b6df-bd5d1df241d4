package com.cainiao.waybill.bridge.biz.label.manager.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.common.mobileCode.service.SmsService;
import com.cainiao.waybill.bridge.biz.label.manager.AccountBalanceAlarmManager;
import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants.SMSSendInfo;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.util.Exceptions;
import com.cainiao.waybill.bridge.common.label.dto.AccountBalanceAlarmDTO;
import com.cainiao.waybill.bridge.common.label.dto.AccountBalanceAlarmQueryDTO;
import com.cainiao.waybill.bridge.model.dao.AccountBalanceAlarmDAO;
import com.cainiao.waybill.bridge.model.domain.AccountBalanceAlarmDO;
import com.cainiao.waybill.common.result.BaseResultDTO;
import com.cainiao.waybill.common.subscription.dto.request.CpAccountQueryDTO;
import com.cainiao.waybill.common.subscription.dto.response.CpAccountDTO;
import com.cainiao.waybill.common.subscription.service.CpAccountService;
import com.google.common.collect.Maps;
import com.taobao.cainiao.waybill.constants.WaybillConstant;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant.SystemError;
import com.taobao.common.dao.persistence.exception.DAOException;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2017/04/21
 */
@Service
public class AccountBalanceAlarmManagerImpl implements AccountBalanceAlarmManager {
    private static final Logger ACCOUNT_ALARM_LOGGER = LoggerFactory.getLogger(LogAppender.ACCOUNT_ALARM);

    private static final long ONE_HOUR = 60 * 60 * 1000;
    @Resource
    private AccountBalanceAlarmDAO accountBalanceAlarmDAO;
    @Resource
    private CpAccountService cpAccountService;
    @Resource
    private SmsService smsService;
    @Resource
    private LogManager logManager;
    @Override
    public void saveAccountBalanceAlarm(AccountBalanceAlarmDTO accountBalanceAlarmDTO)throws BridgeBaseException {
        try{
            AccountBalanceAlarmDO accountBalanceAlarm = accountBalanceAlarmDAO.getAccountBalanceAlarm(accountBalanceAlarmDTO.getCpId(),
                accountBalanceAlarmDTO.getBranchCode(), accountBalanceAlarmDTO.getAccountId(), accountBalanceAlarmDTO.getSegmentCode());
            if(accountBalanceAlarm == null){
                this.accountBalanceAlarmDAO.insert(new AccountBalanceAlarmDO(accountBalanceAlarmDTO.getAccountId(),accountBalanceAlarmDTO.getCpId(),
                    accountBalanceAlarmDTO.getBranchCode(),accountBalanceAlarmDTO.getAlarmQuantity(),accountBalanceAlarmDTO.getIntervalHour(),
                    accountBalanceAlarmDTO.getSegmentCode(),accountBalanceAlarmDTO.getPhone()));
            }else{
                accountBalanceAlarmDAO.updateRule(accountBalanceAlarmDTO.getCpId(), accountBalanceAlarmDTO.getBranchCode(), accountBalanceAlarmDTO.getAccountId(),
                    accountBalanceAlarmDTO.getSegmentCode(),accountBalanceAlarmDTO.getAlarmQuantity(), accountBalanceAlarmDTO.getIntervalHour(), accountBalanceAlarmDTO.getPhone());
            }
        } catch (DAOException e){
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION.getErrorCode(),
                SystemError.DAO_EXCEPTION.getErrorMsg(), e);
        }
    }

    @Override
    public void checkAndSendAlarm(AccountBalanceAlarmQueryDTO accountBalanceAlarmQueryDTO)throws BridgeBaseException {
        try{
            //step1 查询商家在当前网点是否有配置余额值
            AccountBalanceAlarmDO accountBalanceAlarm = accountBalanceAlarmDAO.getAccountBalanceAlarm(accountBalanceAlarmQueryDTO.getCpId(),
                accountBalanceAlarmQueryDTO.getBranchCode(), accountBalanceAlarmQueryDTO.getAccountId(), accountBalanceAlarmQueryDTO.getSegmentCode());
            if(accountBalanceAlarm == null) {
                return;
            }
            if(WaybillConstant.TPN_NOT_NOTIFY.equals(accountBalanceAlarm.getIntervalHour())){
                return;
            }
            //step2 查询账户余额
            Long remainQuantity = 0L;
            CpAccountQueryDTO cpAccountQueryDTO = new CpAccountQueryDTO();
            cpAccountQueryDTO.setCpId(accountBalanceAlarmQueryDTO.getCpId());
            cpAccountQueryDTO.setUserId(accountBalanceAlarmQueryDTO.getAccountId());
            BaseResultDTO<List<CpAccountDTO>> waybillAccountResult = cpAccountService.cpAccountQueryBySellerId(cpAccountQueryDTO);
            if(waybillAccountResult.isFailure()){
                logManager.logErrorMessage("WaybillAlarmManagerImpl#checkAndSendAlarm failure","errofInfo:{}",
                    new Object[]{waybillAccountResult.getOneErrorInfo()},ACCOUNT_ALARM_LOGGER);
                return;
            }else{
                List<CpAccountDTO> accountList = waybillAccountResult.getModule();
                if(CollectionUtils.isNotEmpty(accountList)){
                    List<CpAccountDTO> curBranchAccountList = accountList.stream().filter(
                        o -> o.getBranchCode().equals(accountBalanceAlarmQueryDTO.getBranchCode())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(curBranchAccountList)) {
                        return;
                    } else if (curBranchAccountList.size() > 1) {
                        logManager.logErrorMessage("WaybillAlarmManagerImpl#cpAccountQueryBySellerId failure,more than one record",
                                ",cpAccountQueryDTO={},accountBalanceAlarmQueryDTO={}",new Object[]{cpAccountQueryDTO,
                                accountBalanceAlarmQueryDTO},ACCOUNT_ALARM_LOGGER);
                        return;
                    }
                    remainQuantity = curBranchAccountList.get(0).getCurrentQuantity();
                }
            }
            if(accountBalanceAlarm.getAlarmQuantity() <= remainQuantity) {
                return;
            }
            Date lastTime = accountBalanceAlarm.getLastAlarmTime();
            if(lastTime != null && accountBalanceAlarm.getIntervalHour() * ONE_HOUR > System.currentTimeMillis() - lastTime.getTime()){
                return;
            }
            //step3 发送短信给网点相关负责人
            smsService.sendSms(accountBalanceAlarm.getPhone(), SMSSendInfo.ACCOUNT_BALANCE_REMIND_TEMPLATE, Maps.newHashMap());

            accountBalanceAlarmDAO.updateDate(accountBalanceAlarmQueryDTO.getCpId(), accountBalanceAlarmQueryDTO.getBranchCode(),
                    accountBalanceAlarmQueryDTO.getAccountId(), accountBalanceAlarmQueryDTO.getSegmentCode(), lastTime);
        }catch (DAOException e){
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION.getErrorCode(), SystemError.DAO_EXCEPTION.getErrorMsg(),e);
        }
    }

    @Override
    public AccountBalanceAlarmDO getAccountBalanceAlarm(AccountBalanceAlarmQueryDTO accountBalanceAlarmQueryDTO) throws BridgeBaseException {
        try {
            AccountBalanceAlarmDO accountBalanceAlarm = accountBalanceAlarmDAO.getAccountBalanceAlarm(accountBalanceAlarmQueryDTO
                    .getCpId(),
                accountBalanceAlarmQueryDTO.getBranchCode(), accountBalanceAlarmQueryDTO.getAccountId(),
                accountBalanceAlarmQueryDTO.getSegmentCode());
            if (accountBalanceAlarm == null) {
                return null;
            }
            return accountBalanceAlarm;
        }catch (DAOException e){
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION.getErrorCode(), SystemError.DAO_EXCEPTION.getErrorMsg(),e);
        }
    }
}
