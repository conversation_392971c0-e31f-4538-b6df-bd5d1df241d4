package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.util.NumberUtil;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.*;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.CommonConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Cp;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.TraceFeatureKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpEventConstants.ExtraInfoKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.exception.PickUpBusinessException;
import com.cainiao.waybill.bridge.biz.pickup.dto.order.OrderFee;
import com.cainiao.waybill.bridge.biz.pickup.dto.jdl.FeeInfoDetail;
import com.cainiao.waybill.bridge.biz.pickup.dto.jdl.WaybillPickUpInfoDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.waybill.WaybillPickUpRequest;
import com.cainiao.waybill.bridge.biz.pickup.manager.PickUpCpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.manager.PickUpCpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.manager.factory.PickUpCpOrderFactory;
import com.cainiao.waybill.bridge.biz.pickup.service.impl.WaybillPickUpEventMetaQSender;
import com.cainiao.waybill.bridge.biz.utils.MathUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.CpLogisticNoUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.CpWeightPushLimitUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpFeatureUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.base.ScenarioConstant;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.BaseError;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.common.util.LoggerMonitorUtil;
import com.cainiao.waybill.bridge.common.util.MapUtil;
import com.cainiao.waybill.bridge.common.util.monitor.Monitor;
import com.cainiao.waybill.bridge.common.waybill.constants.WaybillPickUpActionConstant;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseOrderDO;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseOrderMapper;
import com.cainiao.waybill.common.util.FeatureUtils;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.api.open.ReceiveService;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_ORDER_INFO_PUSH.FeeDetail;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_ORDER_INFO_PUSH.OrderInfo;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_ORDER_INFO_PUSH.PackageInfo;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_ORDER_INFO_PUSH.WaybillPickUpOrderInfoPushRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PICK_UP_ORDER_INFO_PUSH.WaybillPickUpOrderInfoPushResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;

/**
 * 订单状态回传link接口
 * 快递100等通过网关转化后推送
 * <AUTHOR> zouping.fzp
 * @Classname WaybillPickUpLogisticsDetailQueryServiceForLinkImpl
 * @Description
 * @Date 2023/8/15 8:29 下午
 * @Version 1.0
 */
public class WaybillPickUpOrderInfoPushServiceForLinkImpl implements ReceiveService<WaybillPickUpOrderInfoPushRequest, WaybillPickUpOrderInfoPushResponse> {

    @Resource
    private WaybillPickUpOrderManager waybillPickUpOrderManager;

    /**
     * 无花果换单类型key
     * trace 物流轨迹 replaceWaybill 更换面单
     */
    private static final String WHG_REPLACE_WAYBILL_KEY = "notifyType";

    //换单新单号标识
    private static final String NEW_WAYBILL_NO_KEY = "新单号:";

    //换单标识
    private static final String REPLACE_WAYBILL_KEY = "FORWARDING";

    @Resource
    private WaybillPickUpEventMetaQSender waybillPickUpEventMetaQSender;

    @Resource
    private WaybillBridgeEnterpriseOrderMapper enterpriseOrderMapper;

    @Override
    public WaybillPickUpOrderInfoPushResponse invoke(ReceiveParams<WaybillPickUpOrderInfoPushRequest> receiveParams) {
        WaybillPickUpOrderInfoPushResponse response = new WaybillPickUpOrderInfoPushResponse();
        try {
            String agentFromAppKey = receiveParams.getFromAppkey();

            LoggerMonitorUtil.start(ScenarioConstant.WAYBILL_PICK_UP_ORDER_INFO_PUSH, agentFromAppKey);

            WaybillPickUpOrderInfoPushRequest request = receiveParams.getRequestDataObject();

            //PickUpLogUtil.record("outer_accept_" + agentFromAppKey, JSON.toJSONString(request));
            PickUpLogUtil.info("gateway_push_request.appKey:{}, request:{}", agentFromAppKey, JSON.toJSONString(request));

            Monitor.stat(ScenarioConstant.WAYBILL_PICK_UP_ORDER_INFO_PUSH, agentFromAppKey,
                    request == null || request.getOrderInfo() == null ? "null" : request.getOrderInfo().getOrderStatus()).count();

            // 无花果换单回调也放在了轨迹回传接口 需要识别区分处理
            if(isWhgReplaceWaybill(request)){
                // 无花果换单
                return dealWhgReplaceWaybill(request);
            }
            WaybillPickUpDetailDO detailDO;
            if(StringUtils.isNotBlank(request.getBizOrderId()) && NumberUtil.isNumber(request.getBizOrderId())){
                detailDO = waybillPickUpOrderManager.getById(Long.parseLong(request.getBizOrderId()));
            } else if (StringUtils.isNotBlank(request.getBizOrderId()) && CpLogisticNoUtil.isV1(request.getBizOrderId())) {
                final Long orderId = CpLogisticNoUtil.parseOrderId(request.getBizOrderId());
                detailDO = waybillPickUpOrderManager.getById(orderId);
            } else if (StringUtils.isNotBlank(request.getBizOrderId()) && CpLogisticNoUtil.isEnt(request.getBizOrderId())) {
                final Long orderId = CpLogisticNoUtil.parseOrderId(request.getBizOrderId());
                return enterpriseOrderHandle(request, agentFromAppKey);
            } else if (StringUtils.isNotBlank(request.getBizOrderId())) {
                ImmutablePair<String, String> immutablePair = CpLogisticNoUtil.KD100.parseResCodeAndOuterOrderCode(request.getBizOrderId());
                detailDO = waybillPickUpOrderManager.get(immutablePair.getLeft(), immutablePair.getRight());
            } else {
                detailDO = waybillPickUpOrderManager.get(request.getMailNo());
            }
            if (detailDO == null) {
                response.setSuccess(false);
                response.setErrorCode("order_not_found");
                response.setErrorMsg("订单不存在");
                return response;
            }

            final Map<String, String> featureMap = FeatureUtils.parseFromString(detailDO.getFeature());
            final String agent = MapUtil.get(featureMap, PickUpConstants.TraceFeatureKey.AGENT);
            final PickUpCpOrderManager cpPickUpOrderManager = PickUpCpOrderFactory.getCpOrderManagerByAgent(agent, detailDO.getCpCode());
            // 前置处理报文。比如：京东需要主动查询重量
            if (cpPickUpOrderManager != null) {
                cpPickUpOrderManager.preHandlePushedRequest(request, detailDO);
            }

            OrderInfo orderInfo = request.getOrderInfo();
            PackageInfo packageInfo = request.getPackageInfo();

            String action = getAction(agentFromAppKey, orderInfo.getOrderStatus(), detailDO, packageInfo);

            //  action为空，即不是希望拿到的节点类型，不向电商平台下发
            if (StringUtil.isBlank(action)) {
                response.setSuccess(true);
                return response;
            }

            // 如果揽收没有重量告警
            if (WaybillPickUpActionConstant.GOT.equals(action)) {
                // 实际重量和计费重量都为空
                if (!StringUtils.isNumeric(packageInfo.getWeight()) && !StringUtils.isNumeric(packageInfo.getCalculateWeight())) {
                    LoggerMonitorUtil.monitor( Error.WEIGHT_IS_NULL.getErrorCode(), agentFromAppKey, detailDO.getMailNo() + ":包裹重量为空");
                }
            }

            //  判断CP回传重量的时间是否超过指定的时间限制
            if (!CpWeightPushLimitUtil.cpPushWeightInLimitRange(action, detailDO)) {
                PickUpLogUtil.errLog("", PickUpConstants.Action.KD100_RETURN_WEIGHT_LIMIT.name(),
                    Error.KD100_RETURN_WEIGHT_LIMIT.getErrorCode(),
                    Error.KD100_RETURN_WEIGHT_LIMIT.getErrorMsg() + ", mailNo:" + detailDO.getMailNo());
                response.setSuccess(false);
                response.setErrorCode("order_weight_call_back_over_time");
                response.setErrorMsg("订单重量回传超过时间");
                return response;
            }

            WaybillPickUpEvent pickUpEvent = new WaybillPickUpEvent();
            String cpCode = BridgeSwitch.SEND_TEMP_CP_APP_MAPPING.get(agentFromAppKey);
            if(StringUtils.equals(detailDO.getCpCode(), CommonConstants.TEMP_CP_CODE)
                && StringUtils.isNotBlank(cpCode)
                && StringUtils.isNotBlank(request.getMailNo())) {
                pickUpEvent.setCpCode(cpCode);
                pickUpEvent.setMailNo(request.getMailNo());
            }else {
                pickUpEvent.setCpCode(detailDO.getCpCode());
                pickUpEvent.setMailNo(detailDO.getMailNo());
            }
            pickUpEvent.setResCode(detailDO.getResCode());
            pickUpEvent.setPushedMailNo(request.getMailNo());
            pickUpEvent.setAction(action);
            pickUpEvent.setActionDesc(WaybillPickUpActionConstant.getActionDesc(action));
            if(null != orderInfo.getOperationTime()){
                pickUpEvent.setActionGmtModified(orderInfo.getOperationTime());
            }else {
                pickUpEvent.setActionGmtModified(System.currentTimeMillis());
            }
            pickUpEvent.setLinkCpCode(
                PickUpFeatureUtil.getFromMulString(detailDO, PickUpConstants.TraceFeatureKey.LINK_CP_CODE));
            pickUpEvent.setOuterOrderCode(detailDO.getOuterOrderCode());
            if (StringUtils.isNotBlank(orderInfo.getOrderStatusDes())) {
                pickUpEvent.setLastActionDetail(orderInfo.getOrderStatusDes());
            }

            setExtraInfo(pickUpEvent, orderInfo, packageInfo, request.getFeeList(), agentFromAppKey);
            //同步更新订单状态
            if (BridgeSwitch.UPDATE_ORDER_STATUS) {
                updateOrderStatus(action, detailDO.getId());
            }

            // 额外处理，比如：顺丰核重时需要调用他们支付
            if (cpPickUpOrderManager != null) {
                cpPickUpOrderManager.callbackWhenEventPushed(pickUpEvent, detailDO);
            }

            PickUpLogUtil.info("gateway_push_event.appKey:{}, event:{}", agentFromAppKey, JSON.toJSONString(pickUpEvent));
            waybillPickUpEventMetaQSender.send(pickUpEvent);

        } catch (PickUpBusinessException pickUpBusinessException) {
            response.setSuccess(false);
            response.setErrorCode(pickUpBusinessException.getErrorCode());
            response.setErrorMsg(pickUpBusinessException.getErrorMessage());
            PickUpLogUtil.errLog("", PickUpConstants.Action.ORDER_INFO_PUSH_LINK.name(),
                pickUpBusinessException.getErrorCode(), pickUpBusinessException.getErrorMessage(),
                pickUpBusinessException);
        } catch (Throwable e) {
            response.setSuccess(false);
            response.setErrorCode(BaseError.UNKNOWN.getErrorCode());
            response.setErrorMsg(BaseError.UNKNOWN.getErrorMsg());
            PickUpLogUtil.errLog("", PickUpConstants.Action.ORDER_INFO_PUSH_LINK.name(), "", e.getMessage(),
                ExceptionUtils.getFullStackTrace(e));
        } finally {
            LoggerMonitorUtil.end(response.isSuccess(), response.getErrorCode());
        }

        return response;
    }

    /**
     * 更新本地订单数据
     * @param request 订单数据
     * @param agentFromAppKey
     * @return
     */
    private WaybillPickUpOrderInfoPushResponse enterpriseOrderHandle(WaybillPickUpOrderInfoPushRequest request, String agentFromAppKey) {
        PickUpLogUtil.info("enterpriseOrderHandle request:{}, agentFromAppKey:{}", JSON.toJSONString(request), agentFromAppKey);
        WaybillPickUpOrderInfoPushResponse response = new WaybillPickUpOrderInfoPushResponse();
        WaybillBridgeEnterpriseOrderDO enterpriseOrderDO = enterpriseOrderMapper.get(request.getMailNo());
        if (enterpriseOrderDO == null){
            response.setSuccess(false);
            response.setErrorCode("order_not_found");
            response.setErrorMsg("订单不存在");
            return response;
        }
        final Map<String, String> featureMap = FeatureUtils.parseFromString(enterpriseOrderDO.getFeature());
        final String agent = MapUtil.get(featureMap, PickUpConstants.TraceFeatureKey.AGENT);

        final PickUpCpOrderManager cpPickUpOrderManager = PickUpCpOrderFactory.getCpOrderManagerByAgent(agent, enterpriseOrderDO.getCpCode());
        OrderInfo orderInfo = request.getOrderInfo();
        PackageInfo packageInfo = request.getPackageInfo();
        String orderStatus = orderInfo.getOrderStatus();
        String action = MapUtil.get(BridgeSwitch.agentActionMapping.get(agentFromAppKey), orderStatus);
        if (WaybillPickUpActionConstant.GOT.equals(action)) {
            String existWeightStr =  featureMap.get(PickUpConstants.TraceFeatureKey.CP_WEIGHT);
            if (StringUtil.isNotBlank(existWeightStr) && packageInfo != null && StringUtils.isNotBlank(packageInfo.getCalculateWeight())) {
                action = null;
            }
        }
        if (StringUtil.isBlank(action)) {
            response.setSuccess(true);
            return response;
        }

        // 如果揽收没有重量告警
        if (WaybillPickUpActionConstant.GOT.equals(action)) {
            // 实际重量和计费重量都为空
            if (!StringUtils.isNumeric(packageInfo.getWeight()) && !StringUtils.isNumeric(packageInfo.getCalculateWeight())) {
                LoggerMonitorUtil.monitor( Error.WEIGHT_IS_NULL.getErrorCode(), agentFromAppKey, enterpriseOrderDO.getWaybillCode() + ":包裹重量为空");
            }
        }

        //  判断CP回传重量的时间是否超过指定的时间限制
        WaybillPickUpDetailDO detailDO = new WaybillPickUpDetailDO();
        detailDO.setMailNo(enterpriseOrderDO.getWaybillCode());
        detailDO.setStatus(enterpriseOrderDO.getStatus());
        detailDO.setOuterOrderCode(enterpriseOrderDO.getOuterOrderCode());
        detailDO.setId(enterpriseOrderDO.getId());
        String feature = enterpriseOrderDO.getFeature();
        Map<String,String> existFeatureMap = FeatureUtils.parseFromString(feature);
        detailDO.setFeature(FeatureUtils.parseFromMap(existFeatureMap));
        if (!CpWeightPushLimitUtil.cpPushWeightInLimitRange(action, detailDO)) {
            response.setSuccess(false);
            response.setErrorCode("order_weight_call_back_over_time");
            response.setErrorMsg("订单重量回传超过时间");
            return response;
        }

        WaybillPickUpEvent pickUpEvent = new WaybillPickUpEvent();
        pickUpEvent.setResCode(enterpriseOrderDO.getCorpId());
        pickUpEvent.setMailNo(enterpriseOrderDO.getWaybillCode());
        pickUpEvent.setPushedMailNo(enterpriseOrderDO.getWaybillCode());
        pickUpEvent.setCpCode(enterpriseOrderDO.getCpCode());
        pickUpEvent.setAction(action);
        pickUpEvent.setActionDesc(WaybillPickUpActionConstant.getActionDesc(action));
        if(null != orderInfo.getOperationTime()){
            pickUpEvent.setActionGmtModified(orderInfo.getOperationTime());
        }else {
            pickUpEvent.setActionGmtModified(System.currentTimeMillis());
        }

        pickUpEvent.setOuterOrderCode(enterpriseOrderDO.getOuterOrderCode());
        if (StringUtils.isNotBlank(orderInfo.getOrderStatusDes())) {
            pickUpEvent.setLastActionDetail(orderInfo.getOrderStatusDes());
        }

        setExtraInfo(pickUpEvent, orderInfo, packageInfo, request.getFeeList(), agentFromAppKey);
        // 额外处理，顺丰核重时需要调用他们支付
        cpPickUpOrderManager.callbackWhenEventPushed(pickUpEvent, detailDO);
        waybillPickUpEventMetaQSender.send(pickUpEvent);

        response.setSuccess(true);
        return response;
    }

    /**
     * 判断是否为无花果换单
     * @param request
     * @return
     */
    private boolean isWhgReplaceWaybill(WaybillPickUpOrderInfoPushRequest request) {
        if (request == null) {
            return false;
        }
        if (request.getOrderInfo() != null) {
            String orderStatus = request.getOrderInfo().getOrderStatus();
            String orderStatusDes = request.getOrderInfo().getOrderStatusDes();

            if (REPLACE_WAYBILL_KEY.equals(orderStatus) && orderStatusDes.contains(NEW_WAYBILL_NO_KEY)) {
                return true;
            }
        }
        Map<String, String> extendMap = request.getExtendMap();
        return extendMap != null &&
                BridgeSwitch.WHG_REPLACE_TRACE_KEY_LIST.contains(extendMap.get(WHG_REPLACE_WAYBILL_KEY));
    }

    /**
     * 无花果换单请求
     * @param request
     * @return
     */
    private WaybillPickUpOrderInfoPushResponse dealWhgReplaceWaybill(WaybillPickUpOrderInfoPushRequest request) {
        WaybillPickUpOrderInfoPushResponse response = new WaybillPickUpOrderInfoPushResponse();
        try {
            final PickUpCpOrderManager cpPickUpOrderManager = PickUpCpOrderFactory.getCpOrderManagerByAgent(PickUpAgentEnum.WHG.getAgent(), PickUpConstants.Cp.YTO.name());
            if (cpPickUpOrderManager != null) {
                WaybillPickUpEvent pickUpEvent = new WaybillPickUpEvent();
                pickUpEvent.setAction(WaybillPickUpActionConstant.REPLACE_WAYBILL);
                pickUpEvent.setExtraInfo(JSONObject.toJSONString(request.getExtendMap()));
                pickUpEvent.setMailNo(request.getMailNo());
                pickUpEvent.setPushedMailNo(request.getMailNo());
                pickUpEvent.setCpCode(PickUpConstants.Cp.YTO.name());
                if (request.getOrderInfo() != null) {
                    pickUpEvent.setOrderStatusDes(request.getOrderInfo().getOrderStatusDes());
                }
                cpPickUpOrderManager.callbackWhenEventPushed(pickUpEvent, null);
            }
            response.setSuccess(true);
            return response;
        }catch (PickUpBusinessException e){
            response.setSuccess(false);
            response.setErrorCode(e.getErrorCode());
            response.setErrorMsg(e.getErrorMessage());
            return response;
        }

    }

    private String getAction(String agentFromAppKey, String orderStatus, WaybillPickUpDetailDO detailDO,
        PackageInfo packageInfo) {

        String action = MapUtil.get(BridgeSwitch.agentActionMapping.get(agentFromAppKey), orderStatus);
        if (StringUtil.isBlank(action)) {
            return null;
        }
        // 已经推送过则不处理
        if (WaybillPickUpActionConstant.GOT.equals(action)) {
            String existWeightStr = PickUpFeatureUtil.getFromMulString(detailDO,
                PickUpConstants.TraceFeatureKey.CP_WEIGHT);
            if (StringUtil.isNotBlank(existWeightStr) && packageInfo != null && StringUtils.isNotBlank(packageInfo.getCalculateWeight())) {

                if (BridgeSwitch.SUPPORT_WEIGHT_SECOND_TIME_PUSH.contains(agentFromAppKey)) {
                    if (StringUtils.isBlank(packageInfo.getWeight()) || !StringUtils.isNumeric(packageInfo.getWeight())) {
                        // 如果没有实际重量，
                        PickUpLogUtil.errLog("", PickUpConstants.Action.ORDER_INFO_PUSH_LINK.name(), "PACKAGE_WEIGHT_IS_NULL", "包裹重量为空");
                        // 返回null后，不继续处理
                        return null;
                    }
                    Integer weight = Integer.parseInt(packageInfo.getWeight());
                    if (weight > 0 && !Objects.equals(weight, Integer.parseInt(existWeightStr))) {
                        return WaybillPickUpActionConstant.MODIFY_WEIGHT;
                    }
                }
                return null;

            }
        }
        String agent = PickUpFeatureUtil.getFromMulString(detailDO, TraceFeatureKey.AGENT);
        if(StringUtils.equals(action, WaybillPickUpActionConstant.GOT )
            && detailDO.getStatus() == PickUpDetailStatusEnum.ACCEPT.getValue()
            && StringUtils.equals(detailDO.getCpCode(), Cp.YTO.getCpCode())
            && StringUtils.equals(agent, PickUpAgentEnum.WHG.getAgent())
            // 历史不能接收已上门节点客户或接收已上门节点后未做实操状态变更的客户继续使用原揽收节点推送
            && !BridgeSwitch.WHG_DROP_IN_ADAPT_CHANNELS.contains(detailDO.getOrderChannels())){
            // 无花果推送小件员上门揽收和网点称重揽收都是GOT状态且数据格式完全一样，这取第一次的推送作为小件员上们状态
            action = WaybillPickUpActionConstant.DROP_IN;
        }
        return action;
    }

    private void setExtraInfo(WaybillPickUpEvent pickUpEvent, OrderInfo orderInfo, PackageInfo packageInfo, List<FeeDetail> feeList, String agentFromAppKey) {
        //  订单扩展信息
        Map<String, String> extraInfo = Maps.newHashMap();

        if (WaybillPickUpActionConstant.GOT.equals(pickUpEvent.getAction()) || WaybillPickUpActionConstant.MODIFY_WEIGHT
            .equals(pickUpEvent.getAction())) {
            int weight = 0;
            if (BridgeSwitch.USE_REAL_WEIGHT.contains(agentFromAppKey)) {
                weight = Integer.parseInt(packageInfo.getWeight());
                PickUpLogUtil.info(String.format("whg weight %s, calculateWeight %s", packageInfo.getWeight(), packageInfo.getCalculateWeight()));
            } else {
                weight = Integer.parseInt(packageInfo.getCalculateWeight());
            }
            if (weight > 0) {
                extraInfo.put(PickUpEventConstants.ExtraInfoKey.WEIGHT, String.valueOf(weight));
            }

            if(CollectionUtils.isNotEmpty(feeList)){
                // 费用项去重
                Set<String> uniqueKeys = new HashSet<>();

                // 附加费用
                List<OrderFee> orderFeeList = Lists.newArrayList();
                for (FeeDetail feeDetail : feeList) {
                    String typeName = feeDetail.getTypeName();
                    String typeCode = feeDetail.getTypeCode();
                    String fee = feeDetail.getFee();
                    OrderFee orderFee = new OrderFee();
                    orderFee.setTypeName(typeName);
                    orderFee.setTypeCode(typeCode);
                    orderFee.setFee(Long.parseLong(fee));
                    String key = typeCode + "|" + typeName + "|" + fee;
                    if (uniqueKeys.add(key)) {
                        orderFeeList.add(orderFee);
                    }
                }
                extraInfo.put(ExtraInfoKey.FEE_LIST, JSON.toJSONString(orderFeeList));
            }

        }else if (WaybillPickUpActionConstant.DROP_IN.equals(pickUpEvent.getAction())
            && packageInfo != null
            && !StringUtils.isAllBlank(packageInfo.getWeight(), packageInfo.getCalculateWeight())){
            // 记录小件员上门揽收重量
            int weight = StringUtils.isBlank(packageInfo.getCalculateWeight()) ? Integer.parseInt(packageInfo.getWeight()) : Integer.parseInt(packageInfo.getCalculateWeight());
            extraInfo.put(PickUpEventConstants.ExtraInfoKey.DROP_IN_WEIGHT, String.valueOf(weight));
        }else if (WaybillPickUpActionConstant.ACCEPT.equals(pickUpEvent.getAction())) {
            extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_NAME, orderInfo.getCourierName());
            extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_MOBILE, orderInfo.getCourierPhone());
            long acceptTime = System.currentTimeMillis();
            extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_TIME, PickUpFeatureUtil.formatTime(acceptTime));


            if (orderInfo.getOnDoorStartTime() != null) {
                // 预约开始时间
                extraInfo.put(PickUpEventConstants.ExtraInfoKey.APPOINT_START_TIME, PickUpCommonUtil.formatDate(new Date(orderInfo.getOnDoorStartTime())));
            }
            if (orderInfo.getOnDoorEndTime() != null) {
                // 预约结束时间
                extraInfo.put(ExtraInfoKey.APPOINT_END_TIME, PickUpCommonUtil.formatDate(new Date(orderInfo.getOnDoorEndTime())));
            }

        }else if (WaybillPickUpActionConstant.FAIL.equals(pickUpEvent.getAction())) {
            extraInfo.put(PickUpConstants.TraceFeatureKey.FAIL_REASON, orderInfo.getOrderStatusDes());
            extraInfo.put(PickUpConstants.TraceFeatureKey.FAIL_CODE, CancelCodes.CP_CANCEL.getCode());
            extraInfo.put(PickUpConstants.TraceFeatureKey.CANCEL_TIME, PickUpFeatureUtil.formatTime(pickUpEvent.getActionGmtModified()));
        }
        if (MapUtils.isNotEmpty(extraInfo)) {
            pickUpEvent.setExtraInfo(JSON.toJSONString(extraInfo));
        }
    }

    private void updateOrderStatus(String action, Long orderId) {
        Integer code = PickUpLogisticsStatusEnum.logisticsStatusToCode(action);

        if (!PickUpLogisticsStatusEnum.logisticsCodeCheck(code)) {
            return;
        }
        Integer status = PickUpLogisticsStatusEnum.logisticsCodeConvert(code);
        if (status != null) {
            waybillPickUpOrderManager.updateStatusById(orderId, status);
        }
    }

}
