package com.cainiao.waybill.bridge.biz.label.service.impl;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
import com.cainiao.waybill.bridge.biz.label.manager.TpnMsgManager;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.SystemError;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.label.dto.request.TpnMsgRequest;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.util.BaseResultHelper;
import com.cainiao.waybill.bridge.common.label.dto.TpnMsgDTO;
import com.cainiao.waybill.bridge.common.label.service.TpnMsgService;
import com.cainiao.waybill.bridge.common.label.validator.TpnMsgValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 千牛消息service 实现
 * <AUTHOR>
 * @since 2017/04/21
 */
@HSFProvider(serviceInterface = TpnMsgService.class)
public class TpnMsgServiceImpl implements TpnMsgService {
    private final static Logger tpnLogger = LoggerFactory.getLogger(LogAppender.TPN_MSG);
    @Resource
    private TpnMsgManager tpnMsgManager;
    @Resource
    private LogManager logManager;

    @Override
    public BaseResultDTO<String> sendTpnMsg(TpnMsgDTO tpnMsgDTO, ClientInfoDTO clientInfoDTO) {
        try {
            TpnMsgValidator.validateTpnMsgDTO(tpnMsgDTO,clientInfoDTO);
            String messageId = tpnMsgManager.sendTpnMsg(tpnMsgDTO);
            return BaseResultHelper.newResult(messageId);
        }catch (BridgeValidationException e ){
            logManager.logExceptionMessage("TpnMsgServiceImpl#sendTpnMsg", "BridgeValidationException tpnMsgDTO:"+tpnMsgDTO, e, tpnLogger);
            return BaseResultHelper.errorResult(e.getErrorCode(),e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("TpnMsgServiceImpl#sendTpnMsg", "BridgeBaseException tpnMsgDTO:"+tpnMsgDTO, e, tpnLogger);
            return BaseResultHelper.errorResult(e.getErrorCode(),e.getErrorMessage());
        } catch (Throwable e) {
            logManager.logExceptionMessage("TpnMsgServiceImpl#sendTpnMsg", "Throwable tpnMsgDTO:"+tpnMsgDTO, e, tpnLogger);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        }
    }

    @Override
    public BaseResultDTO<Void> sendTpnMsg(TpnMsgRequest tpnMsgRequest, ClientInfoDTO clientInfoDTO) {
        try {
            TpnMsgValidator.validateTpnMsgDTO(tpnMsgRequest,clientInfoDTO);
            tpnMsgManager.sendTpnMsg(tpnMsgRequest);
            return BaseResultHelper.newResult();
        }catch (BridgeValidationException e ){
            logManager.logExceptionMessage("TpnMsgServiceImpl#sendTpnMsg", "BridgeValidationException tpnMsgRequest:"+tpnMsgRequest, e, tpnLogger);
            return BaseResultHelper.errorResult(e.getErrorCode(),e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("TpnMsgServiceImpl#sendTpnMsg", "BridgeBaseException tpnMsgRequest:"+tpnMsgRequest, e, tpnLogger);
            return BaseResultHelper.errorResult(e.getErrorCode(),e.getErrorMessage());
        } catch (Throwable e) {
            logManager.logExceptionMessage("TpnMsgServiceImpl#sendTpnMsg", "Throwable tpnMsgRequest:"+tpnMsgRequest, e, tpnLogger);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        }
    }

    public void setLogManager(LogManager logManager) {
        this.logManager = logManager;
    }
}
