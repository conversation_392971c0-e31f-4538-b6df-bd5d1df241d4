package com.cainiao.waybill.bridge.enterprise.administrators.request;

import com.cainiao.waybill.bridge.enterprise.common.BaseRequest;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePlatformEnum;
import lombok.Data;

import java.io.Serializable;


/**
 * 该类用于查询用户基本信息
 * <AUTHOR>
 * @date 2025-04-16 11:10:12
 */
@Data
public class UserInfoRequest extends BaseRequest implements Serializable {


    private static final long serialVersionUID = -8256430074584788986L;

    /**
     * 免登接口
     */
    private String code;

    /**
     * 免登接口
     */
    private String authCode;

    /**
     * 唯一ID
     */
    private String unionId;
}
