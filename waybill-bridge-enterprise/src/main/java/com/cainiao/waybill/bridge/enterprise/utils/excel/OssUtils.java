package com.cainiao.waybill.bridge.enterprise.utils.excel;

import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import org.apache.commons.lang3.StringUtils;

import java.io.InputStream;
import java.net.URL;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/4/17 下午9:59
 */
public class OssUtils {
    /**
     * birdge在oss上的bucket
     */
    private static final String ALI_SOCIAL_WORK_OSS_BUCKET = "waybill-bridge-ali-social-work-package";


    public static final String NAIL_PATTERN = "image/resize,m_fixed,w_100,h_100";

    /**
     * 生成oss原始url
     * @param fileName
     * @return
     */
//    public static String generateOssOriginalUrl(String fileName) {
//        if (StringUtils.isBlank(fileName)) {
//            return null;
//        }
//        return "https://" + ALI_SOCIAL_WORK_OSS_BUCKET + ".oss-cn-hangzhou.aliyuncs.com/" + fileName;
//    }

    /**
     * 上传到oss
     */
    public static String uploadToOSS(String fileName, InputStream inputStream, ObjectMetadata metadata) {
        OSSClient ossClient = EnterpriseOssClientFactory.getOssClient();
        ossClient.putObject(ALI_SOCIAL_WORK_OSS_BUCKET, fileName, inputStream, metadata);
        return "https://" + ALI_SOCIAL_WORK_OSS_BUCKET + ".oss-" + "cn-hangzhou.aliyuncs.com" + "/" + fileName;
    }

//    public static String generateOssUrl(String fileName, boolean nail, int expireDays) {
//        try {
//            if(StringUtils.isBlank(fileName)){
//                return null;
//            }
//            // 创建OSSClient实例。
//            OSSClient ossClient = EnterpriseOssClientFactory.getOssClient();
//
//            Date expiration = new Date((new Date()).getTime() + 1000L * 60 * 60 * 24 * expireDays);
//            GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(ALI_SOCIAL_WORK_OSS_BUCKET, fileName, HttpMethod.GET);
//            req.setExpiration(expiration);
//            if (nail) {
//                //?x-oss-process=image/resize,m_fixed,w_100,h_100
//                req.setProcess(NAIL_PATTERN);
//            }
//            URL signedUrl = ossClient.generatePresignedUrl(req);
//            return signedUrl.toString();
//        } catch (Exception e) {
//            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
//        }
//    }
//
//    public static String generateOssUrl(String fileName, boolean nail) {
//        String url =  generateOssUrl(fileName, nail, 1);
//        return url.replace("http://","https://");
//    }
//
//    public static InputStream getInputStream(String ossFileName){
//        // 创建OSSClient实例。
//        OSSClient ossClient = EnterpriseOssClientFactory.getOssClient();
//        OSSObject ossObject = ossClient.getObject(ALI_SOCIAL_WORK_OSS_BUCKET, ossFileName);
//        return ossObject.getObjectContent();
//    }
}
