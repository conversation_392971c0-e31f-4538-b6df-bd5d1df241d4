package com.cainiao.waybill.bridge.enterprise.authority.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 第三方企业授权。
 * 1.该类实现了`Serializable`接口，支持序列化操作。
 * <AUTHOR>
 * @date 2025-04-15 11:01:40
 */
@Data
public class AuthInfoResponse implements Serializable {

    private static final long serialVersionUID = 3004931058635470845L;
    /**
     * 小程序agentId
     */
    private Long agentId;
}
