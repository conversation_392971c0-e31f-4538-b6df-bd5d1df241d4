package com.cainiao.waybill.bridge.enterprise.route.convert;

import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePostDO;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseLocationDO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterprisePostDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillEnterpriseLocationMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/20
 **/
@Component
public class WaybillBridgeEnterprisePostConverter {

    @Autowired
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;

    @Autowired
    private WaybillEnterpriseLocationMapper enterpriseLocationMapper;

    /**
     * DTO模型转换成DO模型
     * @param waybillEnterprisePostDTO DTO模型
     */
    /*public WaybillBridgeEnterprisePostDO convertFromDTO(WaybillEnterprisePostDTO waybillEnterprisePostDTO) {
        if (waybillEnterprisePostDTO == null) {
            return null;
        }
        WaybillBridgeEnterprisePostDO waybillBridgeEnterprisePostDO = new WaybillBridgeEnterprisePostDO();
        BeanUtils.copyProperties(waybillEnterprisePostDTO,waybillBridgeEnterprisePostDO);
        return waybillBridgeEnterprisePostDO;
    }*/

    /**
     * DO模型转换成DTO模型
     * @param waybillBridgeEnterprisePostDO DO模型
     */
    public WaybillEnterprisePostDTO convertFromDO(WaybillBridgeEnterprisePostDO waybillBridgeEnterprisePostDO) {
        if (waybillBridgeEnterprisePostDO == null) {
            return null;
        }
        WaybillEnterprisePostDTO waybillEnterprisePostDTO = new WaybillEnterprisePostDTO();
        BeanUtils.copyProperties(waybillBridgeEnterprisePostDO,waybillEnterprisePostDTO);
        waybillEnterprisePostDTO.setAdminNameList(enterpriseUserInfoMapper.selectUserNameByPostId(waybillBridgeEnterprisePostDO.getId()));

        WaybillEnterpriseLocationDO waybillEnterpriseLocationDO = enterpriseLocationMapper.selectByPrimaryKey(waybillBridgeEnterprisePostDO.getLocationId());
        if (waybillEnterpriseLocationDO != null) {
            waybillEnterprisePostDTO.setLocationId(waybillEnterpriseLocationDO.getLocationId());
            waybillEnterprisePostDTO.setLocationName(waybillEnterpriseLocationDO.getLocationName());
        }
        return waybillEnterprisePostDTO;
    }


    /**
     * DO模型转换成DTO模型
     * @param waybillBridgeEnterprisePostDOList DO模型
     */
    public List<WaybillEnterprisePostDTO> convertFromDOList(List<WaybillBridgeEnterprisePostDO> waybillBridgeEnterprisePostDOList) {
        List<WaybillEnterprisePostDTO> waybillEnterprisePostDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(waybillBridgeEnterprisePostDOList)) {
            return waybillEnterprisePostDTOList;
        }
        waybillBridgeEnterprisePostDOList.forEach(waybillBridgeEnterpriseUserInfoDO -> {
            WaybillEnterprisePostDTO waybillEnterprisePostDTO = new WaybillEnterprisePostDTO();
            BeanUtils.copyProperties(waybillBridgeEnterpriseUserInfoDO,waybillEnterprisePostDTO);

            waybillEnterprisePostDTO.setAdminNameList(enterpriseUserInfoMapper.selectUserNameByPostId(waybillBridgeEnterpriseUserInfoDO.getId()));
            WaybillEnterpriseLocationDO waybillEnterpriseLocationDO = enterpriseLocationMapper.selectByPrimaryKey(waybillBridgeEnterpriseUserInfoDO.getLocationId());
            if (waybillEnterpriseLocationDO != null) {
                waybillEnterprisePostDTO.setLocationId(waybillEnterpriseLocationDO.getLocationId());
                waybillEnterprisePostDTO.setLocationName(waybillEnterpriseLocationDO.getLocationName());
            }
            waybillEnterprisePostDTOList.add(waybillEnterprisePostDTO);
        });
        return waybillEnterprisePostDTOList;
    }
}
