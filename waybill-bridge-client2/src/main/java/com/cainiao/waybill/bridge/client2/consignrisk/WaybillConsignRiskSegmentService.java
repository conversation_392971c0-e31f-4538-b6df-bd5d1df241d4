package com.cainiao.waybill.bridge.client2.consignrisk;

import com.cainiao.waybill.bridge.client2.PagingResult;
import com.cainiao.waybill.bridge.client2.Result;
import com.cainiao.waybill.bridge.client2.consignrisk.dto.WaybillConsignRiskSegmentConfigDTO;
import com.cainiao.waybill.bridge.client2.consignrisk.request.WaybillConsignRiskSegmentQueryRequest;
import com.cainiao.waybill.bridge.client2.consignrisk.request.WaybillConsignRiskSegmentSaveRequest;
import com.cainiao.waybill.bridge.client2.consignrisk.request.WaybillConsignRiskSegmentUpdateRequest;

import java.util.List;

public interface WaybillConsignRiskSegmentService {
    /**
     * 保存号段配置
     *
     * @param request
     * @return
     */
    Result<Void> save(WaybillConsignRiskSegmentSaveRequest request);

    /**
     * 更新号段配置
     *
     * @param request
     * @return
     */
    Result<Void> updateById(WaybillConsignRiskSegmentUpdateRequest request);


    /**
     * 批量查询号段配置
     *
     * @param request
     * @return
     */
    PagingResult<WaybillConsignRiskSegmentConfigDTO> list(WaybillConsignRiskSegmentQueryRequest request);


    /**
     * 根据id查询号段配置
     *
     * @param id
     * @return
     */
    Result<WaybillConsignRiskSegmentConfigDTO> get(Long id);



    /**
     * 查询生效的号段，包括灰度中和已发布
     *
     * @param request
     * @return
     */
    Result<List<WaybillConsignRiskSegmentConfigDTO>> listEffectSegments(WaybillConsignRiskSegmentQueryRequest request);

    /**
     * 删除号段配置
     *
     * @param id
     * @return
     */
    Result<Void> delete(Long id);


    /**
     * 批量灰度
     *
     * @param ids
     * @return
     */
    Result<Void> batchGray(List<Long> ids, Integer grayRate, String operator);

    /**
     * 批量下线
     *
     * @param ids
     * @return
     */
    Result<Void> batchDownline(List<Long> ids, String operator);
}
