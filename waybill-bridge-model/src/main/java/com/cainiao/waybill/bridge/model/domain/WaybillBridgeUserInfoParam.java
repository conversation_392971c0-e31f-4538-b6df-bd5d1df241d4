package com.cainiao.waybill.bridge.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class WaybillBridgeUserInfoParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public WaybillBridgeUserInfoParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public WaybillBridgeUserInfoParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public WaybillBridgeUserInfoParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public WaybillBridgeUserInfoParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public WaybillBridgeUserInfoParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public WaybillBridgeUserInfoParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andNickIsNull() {
            addCriterion("nick is null");
            return (Criteria) this;
        }

        public Criteria andNickIsNotNull() {
            addCriterion("nick is not null");
            return (Criteria) this;
        }

        public Criteria andNickEqualTo(String value) {
            addCriterion("nick =", value, "nick");
            return (Criteria) this;
        }

        public Criteria andNickNotEqualTo(String value) {
            addCriterion("nick <>", value, "nick");
            return (Criteria) this;
        }

        public Criteria andNickGreaterThan(String value) {
            addCriterion("nick >", value, "nick");
            return (Criteria) this;
        }

        public Criteria andNickGreaterThanOrEqualTo(String value) {
            addCriterion("nick >=", value, "nick");
            return (Criteria) this;
        }

        public Criteria andNickLessThan(String value) {
            addCriterion("nick <", value, "nick");
            return (Criteria) this;
        }

        public Criteria andNickLessThanOrEqualTo(String value) {
            addCriterion("nick <=", value, "nick");
            return (Criteria) this;
        }

        public Criteria andNickLike(String value) {
            addCriterion("nick like", value, "nick");
            return (Criteria) this;
        }

        public Criteria andNickNotLike(String value) {
            addCriterion("nick not like", value, "nick");
            return (Criteria) this;
        }

        public Criteria andNickIn(List<String> values) {
            addCriterion("nick in", values, "nick");
            return (Criteria) this;
        }

        public Criteria andNickNotIn(List<String> values) {
            addCriterion("nick not in", values, "nick");
            return (Criteria) this;
        }

        public Criteria andNickBetween(String value1, String value2) {
            addCriterion("nick between", value1, value2, "nick");
            return (Criteria) this;
        }

        public Criteria andNickNotBetween(String value1, String value2) {
            addCriterion("nick not between", value1, value2, "nick");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("phone not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdIsNull() {
            addCriterion("cainiao_user_id is null");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdIsNotNull() {
            addCriterion("cainiao_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdEqualTo(String value) {
            addCriterion("cainiao_user_id =", value, "cainiaoUserId");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdNotEqualTo(String value) {
            addCriterion("cainiao_user_id <>", value, "cainiaoUserId");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdGreaterThan(String value) {
            addCriterion("cainiao_user_id >", value, "cainiaoUserId");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("cainiao_user_id >=", value, "cainiaoUserId");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdLessThan(String value) {
            addCriterion("cainiao_user_id <", value, "cainiaoUserId");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdLessThanOrEqualTo(String value) {
            addCriterion("cainiao_user_id <=", value, "cainiaoUserId");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdLike(String value) {
            addCriterion("cainiao_user_id like", value, "cainiaoUserId");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdNotLike(String value) {
            addCriterion("cainiao_user_id not like", value, "cainiaoUserId");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdIn(List<String> values) {
            addCriterion("cainiao_user_id in", values, "cainiaoUserId");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdNotIn(List<String> values) {
            addCriterion("cainiao_user_id not in", values, "cainiaoUserId");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdBetween(String value1, String value2) {
            addCriterion("cainiao_user_id between", value1, value2, "cainiaoUserId");
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdNotBetween(String value1, String value2) {
            addCriterion("cainiao_user_id not between", value1, value2, "cainiaoUserId");
            return (Criteria) this;
        }

        public Criteria andRoleIsNull() {
            addCriterion("role is null");
            return (Criteria) this;
        }

        public Criteria andRoleIsNotNull() {
            addCriterion("role is not null");
            return (Criteria) this;
        }

        public Criteria andRoleEqualTo(String value) {
            addCriterion("role =", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotEqualTo(String value) {
            addCriterion("role <>", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleGreaterThan(String value) {
            addCriterion("role >", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleGreaterThanOrEqualTo(String value) {
            addCriterion("role >=", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleLessThan(String value) {
            addCriterion("role <", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleLessThanOrEqualTo(String value) {
            addCriterion("role <=", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleLike(String value) {
            addCriterion("role like", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotLike(String value) {
            addCriterion("role not like", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleIn(List<String> values) {
            addCriterion("role in", values, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotIn(List<String> values) {
            addCriterion("role not in", values, "role");
            return (Criteria) this;
        }

        public Criteria andRoleBetween(String value1, String value2) {
            addCriterion("role between", value1, value2, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotBetween(String value1, String value2) {
            addCriterion("role not between", value1, value2, "role");
            return (Criteria) this;
        }

        public Criteria andSceneIsNull() {
            addCriterion("scene is null");
            return (Criteria) this;
        }

        public Criteria andSceneIsNotNull() {
            addCriterion("scene is not null");
            return (Criteria) this;
        }

        public Criteria andSceneEqualTo(String value) {
            addCriterion("scene =", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneNotEqualTo(String value) {
            addCriterion("scene <>", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneGreaterThan(String value) {
            addCriterion("scene >", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneGreaterThanOrEqualTo(String value) {
            addCriterion("scene >=", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneLessThan(String value) {
            addCriterion("scene <", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneLessThanOrEqualTo(String value) {
            addCriterion("scene <=", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneLike(String value) {
            addCriterion("scene like", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneNotLike(String value) {
            addCriterion("scene not like", value, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneIn(List<String> values) {
            addCriterion("scene in", values, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneNotIn(List<String> values) {
            addCriterion("scene not in", values, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneBetween(String value1, String value2) {
            addCriterion("scene between", value1, value2, "scene");
            return (Criteria) this;
        }

        public Criteria andSceneNotBetween(String value1, String value2) {
            addCriterion("scene not between", value1, value2, "scene");
            return (Criteria) this;
        }

        public Criteria andFeatureIsNull() {
            addCriterion("feature is null");
            return (Criteria) this;
        }

        public Criteria andFeatureIsNotNull() {
            addCriterion("feature is not null");
            return (Criteria) this;
        }

        public Criteria andFeatureEqualTo(String value) {
            addCriterion("feature =", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotEqualTo(String value) {
            addCriterion("feature <>", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThan(String value) {
            addCriterion("feature >", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanOrEqualTo(String value) {
            addCriterion("feature >=", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLessThan(String value) {
            addCriterion("feature <", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanOrEqualTo(String value) {
            addCriterion("feature <=", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLike(String value) {
            addCriterion("feature like", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotLike(String value) {
            addCriterion("feature not like", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureIn(List<String> values) {
            addCriterion("feature in", values, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotIn(List<String> values) {
            addCriterion("feature not in", values, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureBetween(String value1, String value2) {
            addCriterion("feature between", value1, value2, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotBetween(String value1, String value2) {
            addCriterion("feature not between", value1, value2, "feature");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andNickEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("nick =", value, "nick");
            }
            return (Criteria) this;
        }

        public Criteria andNickNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("nick <>", value, "nick");
            }
            return (Criteria) this;
        }

        public Criteria andNickGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("nick >", value, "nick");
            }
            return (Criteria) this;
        }

        public Criteria andNickGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("nick >=", value, "nick");
            }
            return (Criteria) this;
        }

        public Criteria andNickLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("nick <", value, "nick");
            }
            return (Criteria) this;
        }

        public Criteria andNickLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("nick <=", value, "nick");
            }
            return (Criteria) this;
        }

        public Criteria andNickLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("nick like", value, "nick");
            }
            return (Criteria) this;
        }

        public Criteria andNickNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("nick not like", value, "nick");
            }
            return (Criteria) this;
        }

        public Criteria andNickInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("nick in", values, "nick");
            }
            return (Criteria) this;
        }

        public Criteria andNickNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("nick not in", values, "nick");
            }
            return (Criteria) this;
        }

        public Criteria andNickBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("nick between", value1, value2, "nick");
            }
            return (Criteria) this;
        }

        public Criteria andNickNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("nick not between", value1, value2, "nick");
            }
            return (Criteria) this;
        }

        public Criteria andPhoneEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("phone =", value, "phone");
            }
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("phone <>", value, "phone");
            }
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("phone >", value, "phone");
            }
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("phone >=", value, "phone");
            }
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("phone <", value, "phone");
            }
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("phone <=", value, "phone");
            }
            return (Criteria) this;
        }

        public Criteria andPhoneLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("phone like", value, "phone");
            }
            return (Criteria) this;
        }

        public Criteria andPhoneNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("phone not like", value, "phone");
            }
            return (Criteria) this;
        }

        public Criteria andPhoneInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("phone in", values, "phone");
            }
            return (Criteria) this;
        }

        public Criteria andPhoneNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("phone not in", values, "phone");
            }
            return (Criteria) this;
        }

        public Criteria andPhoneBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("phone between", value1, value2, "phone");
            }
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("phone not between", value1, value2, "phone");
            }
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("cainiao_user_id =", value, "cainiaoUserId");
            }
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("cainiao_user_id <>", value, "cainiaoUserId");
            }
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("cainiao_user_id >", value, "cainiaoUserId");
            }
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("cainiao_user_id >=", value, "cainiaoUserId");
            }
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("cainiao_user_id <", value, "cainiaoUserId");
            }
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("cainiao_user_id <=", value, "cainiaoUserId");
            }
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("cainiao_user_id like", value, "cainiaoUserId");
            }
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("cainiao_user_id not like", value, "cainiaoUserId");
            }
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("cainiao_user_id in", values, "cainiaoUserId");
            }
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("cainiao_user_id not in", values, "cainiaoUserId");
            }
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("cainiao_user_id between", value1, value2, "cainiaoUserId");
            }
            return (Criteria) this;
        }

        public Criteria andCainiaoUserIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("cainiao_user_id not between", value1, value2, "cainiaoUserId");
            }
            return (Criteria) this;
        }

        public Criteria andRoleEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("role =", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("role <>", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("role >", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("role >=", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("role <", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("role <=", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("role like", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("role not like", value, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("role in", values, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("role not in", values, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("role between", value1, value2, "role");
            }
            return (Criteria) this;
        }

        public Criteria andRoleNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("role not between", value1, value2, "role");
            }
            return (Criteria) this;
        }

        public Criteria andSceneEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("scene =", value, "scene");
            }
            return (Criteria) this;
        }

        public Criteria andSceneNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("scene <>", value, "scene");
            }
            return (Criteria) this;
        }

        public Criteria andSceneGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("scene >", value, "scene");
            }
            return (Criteria) this;
        }

        public Criteria andSceneGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("scene >=", value, "scene");
            }
            return (Criteria) this;
        }

        public Criteria andSceneLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("scene <", value, "scene");
            }
            return (Criteria) this;
        }

        public Criteria andSceneLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("scene <=", value, "scene");
            }
            return (Criteria) this;
        }

        public Criteria andSceneLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("scene like", value, "scene");
            }
            return (Criteria) this;
        }

        public Criteria andSceneNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("scene not like", value, "scene");
            }
            return (Criteria) this;
        }

        public Criteria andSceneInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("scene in", values, "scene");
            }
            return (Criteria) this;
        }

        public Criteria andSceneNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("scene not in", values, "scene");
            }
            return (Criteria) this;
        }

        public Criteria andSceneBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("scene between", value1, value2, "scene");
            }
            return (Criteria) this;
        }

        public Criteria andSceneNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("scene not between", value1, value2, "scene");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature =", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature <>", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature >", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature >=", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature <", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature <=", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature like", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature not like", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("feature in", values, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("feature not in", values, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("feature between", value1, value2, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("feature not between", value1, value2, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andStatusEqualToWhenPresent(Byte value) {
            if(value != null) {
                addCriterion("status =", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualToWhenPresent(Byte value) {
            if(value != null) {
                addCriterion("status <>", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanWhenPresent(Byte value) {
            if(value != null) {
                addCriterion("status >", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualToWhenPresent(Byte value) {
            if(value != null) {
                addCriterion("status >=", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLessThanWhenPresent(Byte value) {
            if(value != null) {
                addCriterion("status <", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualToWhenPresent(Byte value) {
            if(value != null) {
                addCriterion("status <=", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusInWhenPresent(List<Byte> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("status in", values, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotInWhenPresent(List<Byte> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("status not in", values, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusBetweenWhenPresent(Byte value1, Byte value2) {
            if(value1 != null && value2 != null){
                addCriterion("status between", value1, value2, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotBetweenWhenPresent(Byte value1, Byte value2) {
            if(value1 != null && value2 != null){
                addCriterion("status not between", value1, value2, "status");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *账户昵称
         */
        NICK("nick"),
        /**
         *账户名称
         */
        PHONE("phone"),
        /**
         *菜鸟会员id
         */
        CAINIAOUSERID("cainiao_user_id"),
        /**
         *角色
         */
        ROLE("role"),
        /**
         *业务场景编码：Ntb淘外，OneFound壹基金
         */
        SCENE("scene"),
        /**
         *扩展字段
         */
        FEATURE("feature"),
        /**
         *状态，1在用，-1停用
         */
        STATUS("status");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}
