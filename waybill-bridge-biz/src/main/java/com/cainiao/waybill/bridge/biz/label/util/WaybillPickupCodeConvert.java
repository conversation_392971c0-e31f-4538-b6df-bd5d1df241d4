package com.cainiao.waybill.bridge.biz.label.util;

import java.util.List;

import com.cainiao.waybill.bridge.common.label.dto.Item;
import com.cainiao.waybill.bridge.common.label.dto.MailOrderInfoDTO;
import com.cainiao.waybill.bridge.common.label.dto.UserInfoDTO;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeApplyNewRequest;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import com.cainiao.waybill.common.seller.dto.PackageItem;
import com.cainiao.waybill.common.seller.dto.TradeOrderInfoDTO;
import com.cainiao.waybill.common.seller.dto.WaybillApplyNewRequest;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

/**
 * Description: "揽件码"电子面单转换器
 *
 * <AUTHOR>
 * @Date 2017-04-27
 */
public class WaybillPickupCodeConvert {

    public static WaybillApplyNewRequest constructApplyNewRequest(WaybillPickupCodeApplyNewRequest src) {
        WaybillApplyNewRequest applyNewRequest = new WaybillApplyNewRequest();

        applyNewRequest.setTradeOrderInfoDTOCols(constructTradeOrderInfo(src.getMailOrderInfoDTO()));
        applyNewRequest.setCpCode(src.getCpCode());
        applyNewRequest.setShippingAddress(bridgeAddress2WaybillAddress(src.getMailOrderInfoDTO().getSender().getAddress()));
        applyNewRequest.setShippingBranchCode(src.getBranchCode());
        applyNewRequest.setMultiPackagesShipment(false);
        return applyNewRequest;
    }

    /**
     * 构造订单信息
     * @param src
     * @return
     */
    static List<TradeOrderInfoDTO> constructTradeOrderInfo(MailOrderInfoDTO src) {
        List<TradeOrderInfoDTO> des = Lists.newArrayList();
        TradeOrderInfoDTO tradeInfo = new TradeOrderInfoDTO();

        tradeInfo.setOrderChannelsType(src.getOrderInfoDTO().getOrderChannelsType());
        tradeInfo.setTradeOrderList(src.getOrderInfoDTO().getTradeOrderList());
        // 补齐 objectId, waybill 校验
        tradeInfo.setObjectId("1");
        if (src.getPackageInfo().getVolume() == null) {
            tradeInfo.setVolume(0);
        } else {
            tradeInfo.setVolume(src.getPackageInfo().getVolume());
        }
        if (src.getPackageInfo().getWeight() == null) {
            tradeInfo.setWeight(0);
        } else {
            tradeInfo.setWeight(src.getPackageInfo().getWeight());
        }

        tradeInfo.setPackageId(src.getPackageInfo().getId());
        tradeInfo.setPackageItems(bridgeItem2WaybillItem(src.getPackageInfo().getItems()));

        tradeInfo.setConsigneeMobile(src.getRecipient().getMobile());
        tradeInfo.setConsigneeName(src.getRecipient().getName());
        tradeInfo.setConsigneePhone(src.getRecipient().getPhone());
        tradeInfo.setConsigneeAddress(bridgeAddress2WaybillAddress(src.getRecipient().getAddress()));
        tradeInfo.setConsigneeZipCode(src.getRecipient().getAddress().getZipCode());

        // tradeInfo.setTemplateURL(src.getTemplateURL());
        UserInfoDTO sender = src.getSender();
        tradeInfo.setSendName(sender.getName());
        tradeInfo.setSendPhone(sender.getPhone());
        tradeInfo.setSendMobile(sender.getMobile());

        des.add(tradeInfo);
        return des;
    }

    /**
     * 构造 packageItem
     * <note>电子面单 applyNew 要求必须有一个 packageItem，并且 name 不为空，所以给一个默认值</note>
     */
    static List<PackageItem> bridgeItem2WaybillItem(List<Item> src) {
        List<PackageItem> des = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(src)) {
            for (Item item : src) {
                PackageItem packageItem = new PackageItem();
                packageItem.setItemName(item.getName());
                packageItem.setCount(item.getCount());
                des.add(packageItem);
            }
        } else {
            // 构造一个默认的 packageItem，电子面单生成必需
            PackageItem packageItem = new PackageItem();
            packageItem.setItemName("默认商品");
            packageItem.setCount(1);
            des.add(packageItem);
        }
        return des;
    }

    static AddressDTO bridgeAddress2WaybillAddress(com.cainiao.waybill.bridge.common.label.dto.AddressDTO src) {
        AddressDTO des = new AddressDTO();
        if (src == null) {
            return null;
        }
        des.setProvinceName(src.getProvince());
        des.setCityName(src.getCity());
        des.setAreaName(src.getDistrict());
        des.setTownName(src.getTown());
        des.setAddressDetail(src.getDetail());
        return des;
    }
}
