package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.biz.pickup.constants.InsuranceConstants;
import com.cainiao.waybill.bridge.biz.pickup.dto.PolicyLogisticsInfoRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.PolicyProductInfoRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.WaybillInsurancePolicyRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.WaybillInsurancePolicyResponse;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillInsuranceManager;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.google.common.collect.Lists;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_ADD_POLICY.WaybillAddPolicyRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_ADD_POLICY.WaybillAddPolicyResponse;
import com.taobao.pac.client.sdk.receiveservice.WaybillAddPolicyService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 投保Link接口
 * @date 2021/7/22-上午11:34
 */
public class TmsWaybillAddPolicyServiceImpl implements WaybillAddPolicyService {

    @Resource
    private WaybillInsuranceManager waybillInsuranceManager;

    @Override
    public WaybillAddPolicyResponse invoke(ReceiveParams<WaybillAddPolicyRequest> receiveParams) {
        PickUpLogUtil.info("AddPolicy receiveParams : " + JSON.toJSONString(receiveParams));

        //  调用WaybillInsuranceManager的请求参数
        WaybillInsurancePolicyRequest insurancePolicyRequest = new WaybillInsurancePolicyRequest();
        WaybillAddPolicyRequest linkRequest = receiveParams.getRequestDataObject();
        BeanUtils.copyProperties(linkRequest.getRequest(), insurancePolicyRequest);

        PolicyLogisticsInfoRequest logisticsInfo = new PolicyLogisticsInfoRequest();
        BeanUtils.copyProperties(linkRequest.getRequest().getLogisticsInfo(), logisticsInfo);
        insurancePolicyRequest.setLogisticsInfo(logisticsInfo);

        if (CollectionUtils.isNotEmpty(linkRequest.getRequest().getProductInfos())) {
            List<PolicyProductInfoRequest> productInfos = Lists.newArrayList();
            for (com.taobao.pac.client.sdk.dataobject.request.WAYBILL_ADD_POLICY.PolicyProductInfoRequest linkProductInfo : linkRequest.getRequest().getProductInfos()) {
                PolicyProductInfoRequest productInfo = new PolicyProductInfoRequest();
                BeanUtils.copyProperties(linkProductInfo, productInfo);
                productInfos.add(productInfo);
            }
            insurancePolicyRequest.setProductInfos(productInfos);
        }

        PickUpLogUtil.info("AddPolicy insurancePolicyRequest : " + JSON.toJSONString(insurancePolicyRequest));

        WaybillInsurancePolicyResponse policyResponse = waybillInsuranceManager.addPolicy(insurancePolicyRequest);

        com.taobao.pac.client.sdk.dataobject.response.WAYBILL_ADD_POLICY.WaybillInsurancePolicyResponse result = new com.taobao.pac.client.sdk.dataobject.response.WAYBILL_ADD_POLICY.WaybillInsurancePolicyResponse();
        BeanUtils.copyProperties(policyResponse, result);
        WaybillAddPolicyResponse linkResponse = new WaybillAddPolicyResponse();
        linkResponse.setResult(result);

        if (InsuranceConstants.ZhonganResult.SUCESS_RESULT_CODE.equals(policyResponse.getResultCode())) {
            linkResponse.setSuccess(true);
        } else {
            linkResponse.setSuccess(false);
            linkResponse.setErrorCode(policyResponse.getResultCode());
            linkResponse.setErrorMsg(policyResponse.getResultMsg());
        }
        PickUpLogUtil.info("AddPolicy linkResponse : " + JSON.toJSONString(linkResponse));

        return linkResponse;
    }
}
