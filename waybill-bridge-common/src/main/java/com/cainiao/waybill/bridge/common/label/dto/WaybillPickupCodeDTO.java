package com.cainiao.waybill.bridge.common.label.dto;


import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/4/25.
 *
 * 揽件码面单 dto
 */
public class WaybillPickupCodeDTO extends BaseDTO {
    private static final long serialVersionUID = -391487885509815022L;

    /**
     * cp 编码
     */
    private String cpCode;

    /**
     * 电子面单编码
     */
    private String waybillCode;

    /**
     * 揽件码
     */
    private String pickupCode;

    /**
     * 小件员id
     */
    private Long courierId;

    /**
     * 商家名称(冗余)
     */
    private String sellerName;

    /**
     * 店铺名称(冗余)
     */
    private String shopName;

    /**
     * 订单创建时间
     *   "yyyy-MM-dd"
     */
    private String createDate;

    /**
     * 结算类型
     */
    private String settlement ;

    /**
     * 包裹重量
     */
    private Integer weight;

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getWaybillCode() {
        return waybillCode;
    }

    public void setWaybillCode(String waybillCode) {
        this.waybillCode = waybillCode;
    }

    public String getPickupCode() {
        return pickupCode;
    }

    public void setPickupCode(String pickupCode) {
        this.pickupCode = pickupCode;
    }

    public Long getCourierId() {
        return courierId;
    }

    public void setCourierId(Long courierId) {
        this.courierId = courierId;
    }

    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    public String getSettlement() {
        return settlement;
    }

    public void setSettlement(String settlement) {
        this.settlement = settlement;
    }
}
