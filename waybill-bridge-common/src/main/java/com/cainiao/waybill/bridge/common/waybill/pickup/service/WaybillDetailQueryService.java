package com.cainiao.waybill.bridge.common.waybill.pickup.service;

import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.common.seller.dto.WaybillDetailInfo;

import java.util.List;

/**
 * 查询菜鸟电子面单详情
 *
 * @author: yexin
 * @date: 2022-04-26 21:04
 **/
public interface WaybillDetailQueryService {
    /**
     * 根据面单号+cpCode查询面单信息集合
     *
     * @param mailNo :
     * @param cpCode :
     * @return :
     */
    List<WaybillDetailInfo> queryWithMailNoAndCpCode(String mailNo, String cpCode) throws BridgeBaseException;

    /**
     * 根据面单号查询面单信息集合
     *
     * @param mailNo :
     * @return :
     */
    List<WaybillDetailInfo> queryWithMailNo(String mailNo) throws BridgeBaseException;

    /**
     * 根据面单号+cpCode查询面单信息，只取集合中的一个即可。一个取号动作会产生多个面单详情，除了外部订单号外，其它信息都一致
     *
     * @param mailNo :
     * @param cpCode :
     * @return :
     */
    WaybillDetailInfo queryOneWithMailNoAndCpCode(String mailNo, String cpCode) throws BridgeBaseException;

    /**
     * 根据面单号查询面单信息，只取集合中的一个即可。一个取号动作会产生多个面单详情，除了外部订单号外，其它信息都一致
     *
     * @param mailNo :
     * @return :
     */
    WaybillDetailInfo queryOneWithMailNo(String mailNo) throws BridgeBaseException;

    /**
     * 查询面单号对应的cpCode
     *
     * @param mailNo :
     * @return :
     */
    String queryCpCodeWithMailNo(String mailNo) throws BridgeBaseException;
}
