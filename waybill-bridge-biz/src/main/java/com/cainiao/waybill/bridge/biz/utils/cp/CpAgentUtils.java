package com.cainiao.waybill.bridge.biz.utils.cp;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpAgentEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpCpBizTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpCpEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * cp工具
 * <AUTHOR>
 * @date 2024/12/13 11:52
 **/
public class CpAgentUtils {

    private static final String DEFAULT_SPLIT_CHAR = "-";


    private static final String CP_BIZ_TYPE_NX_DESC = "逆向";
    private static final String CP_BIZ_TYPE_ZX_DESC = "正向";
    private static final String CP_BIZ_TYPE_SD_DESC = "散单";

    /**
     * 可用运力转换为中文名称
     * @param cpList
     * @return
     */
    public static  List<String> covertAvailableCpNameList(List<String> cpList) {
        List<String> cpAgentNameList = new ArrayList<>();
        for(String cpAgentCode : cpList){
            String cpAgentName = parseCpAgentName(cpAgentCode);
            if(StringUtils.isNotBlank(cpAgentName)){
                cpAgentNameList.add(cpAgentName);
            }
        }
        return cpAgentNameList;
    }

    /**
     * 可用运力转换为英文编码
     * @param cpList
     * @return
     */
    public static  List<String> covertAvailableCpCodeList(List<String> cpList) {
        List<String> cpAgentCodeList = new ArrayList<>();
        for(String cpAgentName : cpList){
            String cpAgentCode = parseCpAgentCode(cpAgentName);
            if(StringUtils.isNotBlank(cpAgentCode)){
                cpAgentCodeList.add(cpAgentCode);
            }
        }
        return cpAgentCodeList;
    }


    /**
     * 解析运力服务商名称
     *
     * @param cpCodeAgent 格式：cp-agent
     * @return
     */
    public static String parseCpAgentName(String cpCodeAgent){
        if(StringUtils.isBlank(cpCodeAgent)){
            return "";
        }
        String cpCode;
        String agentCode;
        if(!cpCodeAgent.contains(DEFAULT_SPLIT_CHAR)){
            return "";
        }
        String cpBizType = CP_BIZ_TYPE_NX_DESC;
        cpCode = cpCodeAgent.split(DEFAULT_SPLIT_CHAR)[0];
        agentCode = cpCodeAgent.split(DEFAULT_SPLIT_CHAR)[1];
        if(cpCodeAgent.split(DEFAULT_SPLIT_CHAR).length == 3 ){
            if(cpCodeAgent.split(DEFAULT_SPLIT_CHAR)[2].equals(PickUpCpBizTypeEnum.E_COMMERCE_SHIPPING.getBizType()+"")){
                cpBizType = CP_BIZ_TYPE_ZX_DESC;
            }else if(cpCodeAgent.split(DEFAULT_SPLIT_CHAR)[2].equals(PickUpCpBizTypeEnum.C2C.getBizType()+"")){
                cpBizType = CP_BIZ_TYPE_SD_DESC;
            }
        }
        String cpName = PickUpCpEnum.getNameByCode(cpCode);

        String agentName = PickUpAgentEnum.nameByAgent(agentCode);

        if(StringUtils.isBlank(cpName) || StringUtils.equals(agentName, PickUpAgentEnum.NONE.getName())){
            return "";
        }
        return cpName+ DEFAULT_SPLIT_CHAR + agentName + DEFAULT_SPLIT_CHAR + cpBizType;

    }

    /**
     * 解析运力服务商code
     *
     * @param cpCodeAgentName 格式：圆通快递-直连
     * @return
     */
    public static String parseCpAgentCode(String cpCodeAgentName){
        if(StringUtils.isBlank(cpCodeAgentName)){
            return "";
        }
        String cpCodeName;
        String agentCodeName;
        // 默认逆向
        String cpBizType = "1";
        if(!cpCodeAgentName.contains(DEFAULT_SPLIT_CHAR)){
            return "";
        }
        cpCodeName = cpCodeAgentName.split(DEFAULT_SPLIT_CHAR)[0];
        agentCodeName = cpCodeAgentName.split(DEFAULT_SPLIT_CHAR)[1];
        if(cpCodeAgentName.split(DEFAULT_SPLIT_CHAR).length == 3){
            if(cpCodeAgentName.split(DEFAULT_SPLIT_CHAR)[2].equals(CP_BIZ_TYPE_ZX_DESC)){
                cpBizType = PickUpCpBizTypeEnum.E_COMMERCE_SHIPPING.getBizType()+"";
            }else if(cpCodeAgentName.split(DEFAULT_SPLIT_CHAR)[2].equals(CP_BIZ_TYPE_SD_DESC)){
                cpBizType = PickUpCpBizTypeEnum.C2C.getBizType()+"";
            }
        }

        String cpCode = PickUpCpEnum.getCodeByName(cpCodeName);

        String agentCode = PickUpAgentEnum.getAgentByName(agentCodeName);

        if(StringUtils.isBlank(cpCode) || StringUtils.equals(agentCode, PickUpAgentEnum.NONE.getName())){
            return "";
        }
        return cpCode+ DEFAULT_SPLIT_CHAR + agentCode + DEFAULT_SPLIT_CHAR + cpBizType;

    }

}
