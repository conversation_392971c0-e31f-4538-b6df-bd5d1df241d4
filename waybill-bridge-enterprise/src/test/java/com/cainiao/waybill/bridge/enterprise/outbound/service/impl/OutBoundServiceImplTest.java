package com.cainiao.waybill.bridge.enterprise.outbound.service.impl;

import com.base.BaseTest;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePlatformEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.outbound.request.OrderPickUpPushRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.Logger;

import static org.junit.Assert.assertFalse;
import static org.mockito.Mockito.*;
import static org.testng.AssertJUnit.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class OutBoundServiceImplTest extends BaseTest {
    @InjectMocks
    private OutBoundServiceImpl outBoundServiceImpl;
    @Mock
    private DingTalkAuthService dingTalkAuthService;
    @Mock
    private Logger LOGGER;

    /**
     * 尝试获取corpAccessToken，如果为null或空则记录错误日志并返回false
     */
    @Test(timeout = 5000)
    public void testPushOrderPickUpMessageCorpAccessTokenIsNull() {
        // Mock DingTalkAuthService返回null或者空字符串
        Mockito.when(dingTalkAuthService.getCorpAccessToken(eq(EnterprisePlatformEnum.DingTalk.name()), anyString()))
                .thenReturn(null);
        // 准备测试数据
        OrderPickUpPushRequest request = new OrderPickUpPushRequest();
        request.setCorpId("testCorpId");
        // 调用测试方法
        Boolean result = outBoundServiceImpl.pushOrderPickUpMessage(request);
        // 验证结果
        assertFalse(result); // 验证返回值为false
        verify(LOGGER).error("corpId get corpAccessToken failed"); // 验证错误日志记录
    }

    /**
     * 处理异常情况，记录错误日志并抛出EnterpriseException
     */
    @Test(timeout = 5000)
    public void testPushOrderPickUpMessageExceptionThrown() {
        // 模拟方法抛出异常
        Mockito.doThrow(new RuntimeException("error"))
                .when(dingTalkAuthService)
                .getCorpAccessToken(eq(EnterprisePlatformEnum.DingTalk.name()), anyString());
        // 准备测试数据
        OrderPickUpPushRequest request = new OrderPickUpPushRequest();
        request.setCorpId("testCorpId");
        // 捕获异常实现验证
        try {
            outBoundServiceImpl.pushOrderPickUpMessage(request);
        } catch (EnterpriseException e) {
            assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getErrorCode());
            assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.describe(), e.getMessage());
        }
        // 额外的验证和行为检查（不执行）
        // verify(LOGGER).error(anyString(), anyString(), any(Throwable.class));
    }
}