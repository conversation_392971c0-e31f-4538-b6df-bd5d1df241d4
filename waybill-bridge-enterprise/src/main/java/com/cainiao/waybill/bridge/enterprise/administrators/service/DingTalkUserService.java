package com.cainiao.waybill.bridge.enterprise.administrators.service;

import com.cainiao.waybill.bridge.enterprise.administrators.response.*;
import com.cainiao.waybill.bridge.enterprise.administrators.request.EnterpriseUserRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.GetEnterpriseUserInfoRequest;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoDO;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserInfoDTO;

import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
public interface DingTalkUserService {

    /**
     * 查询管理员列表
     * @param corpId 企业ID
     * https://open.dingtalk.com/document/isvapp/query-the-administrator-list
     * @return
     */
    List<ListAdminResponse> getAdminUserIdList(String corpId);


    /**
     * 钉钉企业管理员是否已同步检查
     * @param corpId 企业ID
     */
    void dingTalkAdminSync(String corpId);


    /**
     * 钉钉企业管理员是否已同步检查
     * @param corpId 企业ID
     * @param start 开始
     * @param end 结束
     */
    void dingTalkAdminSync(String corpId, Integer start, Integer end);

    /**
     * 钉钉企业管理员是否已同步检查
     * @param corpId 企业ID
     * @param userId 用户ID
     * @param isAdmin 是否激活管理员
     */
    void dingTalkAdminSync(String corpId, String userId, Boolean isAdmin);

    /**
     * 获取用户信息
     *
     * @param request request
     * @return 用户信息
     */
    BridgePagingDTO<WaybillEnterpriseUserInfoDTO> pageList(GetEnterpriseUserInfoRequest request);

    /**
     * 根据userId获取用户信息
     *
     * @param request request
     * @return 用户信息
     */
    WaybillBridgeEnterpriseUserInfoDO getUserInfoFromDB(EnterpriseUserRequest request);

    /**
     * 添加管理员
     * @param request request
     * @return 是否成功
     */
    int addUser(EnterpriseUserRequest request);

    /**
     * 删除管理员
     * @param id id
     * @return 是否成功
     */
    int deleteUserById(Long id);

    /**
     * 删除管理员
     * @param id id
     * @return 是否成功
     */
    int removeAdminById(Long id);


    /**
     * 根据id获取用户信息
     * @param id id
     * @return 管理员
     */
    WaybillEnterpriseUserInfoDTO getById(Long id);

    /**
     * 使用免登陆接口查询用户信息
     * @param code 免登陆码
     * @param corpId 企业ID
     * https://open.dingtalk.com/document/isvapp/obtain-the-userid-of-a-user-by-using-the-log-free
     * @return
     */
    UserInfoResponse getUserInfoByCode(String corpId, String code);

    /**
     * 使用免登陆接口查询用户信息-小程序移动端
     * @param authCode 免登陆码令牌权限码
     * @param corpId 企业ID
     * @return
     */
    UserTokenResponse fetchUserInfoAuthCodeFromMiniApp(String corpId, String authCode);

    /**
     * 使用免登陆接口查询用户信息
     * @param userId 用户ID
     * @param corpId 企业ID
     * @return
     * https://open.dingtalk.com/document/isvapp/query-user-details
     */
    UserInfoResponse getUserInfoToDingTalkByUserId(String corpId, String userId);

    /**
     * 使用免登陆接口查询用户信息
     * @param unionId 用户 唯一ID
     * @param corpId 企业ID
     * @return
     * https://open.dingtalk.com/document/isvapp/query-a-user-by-the-union-id
     */
    UserInfoResponse getUserInfoByUnionId(String corpId, String unionId);


    /**
     * 根据参数查询用户信息
     * @param userRequest 参数信息
     * @return 用户信息
     */
    BridgePagingDTO<WaybillEnterpriseUserInfoDTO> getUserBySearchValue(EnterpriseUserRequest userRequest);

    /**
     * 获取钉钉下该企业用户信息
     * @param request 查询参数
     * @return 用户信息
     * https://open.dingtalk.com/document/isvapp/address-book-search-user-id
     */
    BridgePagingDTO<UserInfoResponse> getDingTalkUserInfo(GetEnterpriseUserInfoRequest request);

    /**
     * 获取该钉钉企业下部门信息
     * @param corpId 企业ID
     * https://open.dingtalk.com/document/isvapp/get-department-list
     */
    List<CorpDeptResponse> getDingTalkCorpDept(String accessToken, String corpId, Long deptId);

    /**
     * 获取该钉钉用户所有父部门ID列表
     * @param corpId 企业ID
     * https://oapi.dingtalk.com/topapi/v2/department/listparentbyuser?access_token=ACCESS_TOKEN
     */
    List<List<UserDeptInfoResponse>> getDingTalkDeptListByUserId(String corpId, String userId);

    /**
     * 获取部门下所有用户
     * @param deptId 部门ID
     * https://open.dingtalk.com/document/isvapp/query-the-list-of-department-userids
     */
    List<String> getDingTalkDeptUserId(String accessToken, Long deptId);



    /**
     * 钉钉用户同步到DB
     * 这个方法只能同步当前增量的员工，对于已经离职的员工没法识别到，需要使用定时任务进行处理
     * @param corpId 企业ID
     * @param start 开始
     * @param end 结束
     */
    void dingTalkUserSync(String corpId, Integer start, Integer end);

    /**
     * 钉钉企业员工同步
     * @param corpId 企业ID
     * @param userId 用户ID
     * @param isActive 是否激活
     */
    void dingTalkUserSync(String corpId, String userId, Boolean isActive);

    /**
     * 使用部门ID陆接口查询部门信息
     * @param deptId 部门ID
     * @param corpId 企业ID
     * @return
     * https://open.dingtalk.com/document/isvapp/query-department-details0-v2
     */
    UserDeptInfoResponse getUserDeptInfoById(String corpId, Long deptId);


    /**
     * 根据id获取用户信息
     * @param userId userId
     * @return 用户信息
     */
    WaybillEnterpriseUserInfoDTO getByUserId(String userId, String corpId);

    /**
     * 根据用户ID删除用户信息
     * @param userId 用户ID
     */
    void removeUserByUserId(String userId, String corpId);

    /**
     * 根据userId删除管理员
     * @param userId
     * @return 是否成功
     */
    int removeAdminByUserId(String userId);

}
