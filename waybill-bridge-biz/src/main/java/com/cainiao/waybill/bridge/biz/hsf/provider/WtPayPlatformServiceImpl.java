package com.cainiao.waybill.bridge.biz.hsf.provider;

import java.math.BigDecimal;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.cainiao.waybill.bridge.biz.hsf.WtPayPlatformService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.wrapper.WtPayPlatformWrapper;
import com.cainiao.waybill.bridge.common.dto.WtAccountDTO;

/**
 * 淘外结算平台
 * <AUTHOR>
 * @date 2024/11/11 10:50
 **/
@HSFProvider(serviceInterface = WtPayPlatformService.class)
public class WtPayPlatformServiceImpl implements WtPayPlatformService {

    @Resource
    private WtPayPlatformWrapper wtPayPlatformWrapper;

    @Override
    public void wtPayOrder(String flowId, String amount, long accountId, String ownerId) {
        PickUpLogUtil.info("淘外结算平台扣款入参.flowId:{},amount:{},accountId:{},ownerId:{}", flowId, amount, accountId, ownerId);
        BigDecimal price = new BigDecimal(amount);
        wtPayPlatformWrapper.wtPayOrder(flowId, price, accountId, ownerId);
    }

    @Override
    public void wtRecharge(String flowId, String amount, long accountId, String ownerId) {
        PickUpLogUtil.info("淘外结算平台调账入参.flowId:{},amount:{},accountId:{},ownerId:{}", flowId, amount, accountId, ownerId);
        BigDecimal price = new BigDecimal(amount);
        wtPayPlatformWrapper.wtRecharge(flowId, price, accountId, ownerId);
    }

    @Override
    public WtAccountDTO queryAccount(String accountId) {
        return wtPayPlatformWrapper.queryAccount(accountId);
    }
}
