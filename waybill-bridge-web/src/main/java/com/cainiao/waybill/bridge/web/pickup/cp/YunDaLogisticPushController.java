package com.cainiao.waybill.bridge.web.pickup.cp;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.*;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Cp;
import com.cainiao.waybill.bridge.biz.pickup.dto.GoodsInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.yunda.YunDaLogisticRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.yunda.YunDaLogisticRequestData;
import com.cainiao.waybill.bridge.biz.pickup.dto.yunda.YunDaLogisticResponse;
import com.cainiao.waybill.bridge.biz.pickup.dto.yunda.YunDaLogisticResponseData;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpAttributeManager;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.service.impl.WaybillPickUpEventMetaQSender;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCategoryWeightUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpFeatureUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.YunDaOpenApiHttpUtils;
import com.cainiao.waybill.bridge.common.constants.LogisticStatusEnum;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEventBuilder;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpAttributeDO;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 接受韵达物流轨迹回传：韵达无法直接将轨迹回传给详情团队，这里单独对接并做转换
 *
 * @author: yexin
 * @date: 2022-04-21 15:36
 **/
@RestController
public class YunDaLogisticPushController {

    @Resource
    private WaybillPickUpOrderManager pickUpOrderManager;

    @Resource
    private WaybillPickUpAttributeManager pickUpAttributeManager;

    @Resource
    private WaybillPickUpEventMetaQSender waybillPickUpEventMetaQSender;

    @PostMapping("/logistic/yunda")
    public YunDaLogisticResponse logistic(@RequestBody YunDaLogisticRequest logisticRequest) {
        PickUpLogUtil.info("/logistic/yunda : " + JSON.toJSONString(logisticRequest));
        YunDaLogisticResponse logisticResponse = new YunDaLogisticResponse();
        try {
            //  请求校验
            if (logisticRequest == null || StringUtils.isBlank(logisticRequest.getData())) {
                throw new BridgeBaseException(PickUpConstants.Error.YunDa.YUNDA_TRACE_REQUEST_EMPTY.getErrorCode(), PickUpConstants.Error.YunDa.YUNDA_TRACE_REQUEST_EMPTY.getErrorCode());
            }
            YunDaLogisticRequestData requestData = JSON.parseObject(logisticRequest.getData(), YunDaLogisticRequestData.class);
            if (requestData == null) {
                throw new BridgeBaseException(PickUpConstants.Error.YunDa.YUNDA_TRACE_REQUEST_EMPTY.getErrorCode(), PickUpConstants.Error.YunDa.YUNDA_TRACE_REQUEST_EMPTY.getErrorCode());
            }
            String exeCheckDigest = BridgeSwitch.yundaPickUpConfig.get(PickUpConstants.YunDa.ConfigKey.lcCheckDigestSwitch.name());
            if ("true".equals(exeCheckDigest)) {
                String appKey = BridgeSwitch.yundaPickUpConfig.get(PickUpConstants.YunDa.ConfigKey.lcAppKey.name());
                String appSecret = BridgeSwitch.yundaPickUpConfig.get(PickUpConstants.YunDa.ConfigKey.lcAppSecret.name());
                String sign = YunDaOpenApiHttpUtils.MD5(logisticRequest.getData() + "_" + appSecret);
                if (!Objects.equals(appKey, logisticRequest.getAppKey()) || !Objects.equals(sign, logisticRequest.getSign())) {
                    throw new BridgeBaseException(PickUpConstants.Error.YunDa.YUNDA_LOGISTIC_SIGN_CHECK_FAIL.getErrorCode(), PickUpConstants.Error.YunDa.YUNDA_LOGISTIC_SIGN_CHECK_FAIL.getErrorCode());
                }
            }

            String mailNo = requestData.getMailno();
            WaybillPickUpDetailDO existDetailDO = pickUpOrderManager.get(mailNo);
            if (existDetailDO == null || !StringUtils.equals(existDetailDO.getCpCode(), Cp.YUNDA.name())) {
                logisticResponse.setResult(false);
                logisticResponse.setCode(PickUpConstants.Error.YunDa.ORDER_NOT_EXIST.getErrorCode());
                logisticResponse.setMessage(PickUpConstants.Error.YunDa.ORDER_NOT_EXIST.getErrorCode());
                return logisticResponse;
            }

            //  揽收节点当做状态回传的揽收节点处理。其它节点直接转发
            if (Objects.equals(YunDaLogisticActionEnum.GOT.name(), requestData.getAction())) {
                dealGotAction(existDetailDO, requestData);
            } else {
                dealNormalAction(existDetailDO, requestData);
            }

            logisticResponse.setResult(true);
            logisticResponse.setCode("0000");
            logisticResponse.setMessage("接收成功");
            YunDaLogisticResponseData data = new YunDaLogisticResponseData();
            data.setOrderid(requestData.getOrderid());
            logisticResponse.setData(data);
            return logisticResponse;
        } catch (BridgeBaseException e) {
            PickUpLogUtil.errLog("", PickUpConstants.Action.YUN_LOGISTIC_ERROR.name(), e.getErrorCode(), JSON.toJSONString(logisticRequest));
            logisticResponse.setResult(false);
            logisticResponse.setCode(e.getErrorCode());
            logisticResponse.setMessage(e.getErrorMessage());
            return logisticResponse;
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpConstants.Action.YUN_LOGISTIC_ERROR.name(), e.getMessage(), JSON.toJSONString(logisticRequest), e);
            logisticResponse.setResult(false);
            logisticResponse.setCode(PickUpConstants.Error.YunDa.YUNDA_LOGISTIC_CAINIAO_SYS_EXECEPTION.getErrorCode());
            logisticResponse.setMessage(PickUpConstants.Error.YunDa.YUNDA_LOGISTIC_CAINIAO_SYS_EXECEPTION.getErrorCode());
            return logisticResponse;
        }
    }

    private void dealGotAction(WaybillPickUpDetailDO existDetailDO, YunDaLogisticRequestData requestData) throws Exception {
        if (!Objects.equals(existDetailDO.getStatus(), PickUpDetailStatusEnum.ACCEPT.getValue())) {
            return;
        }
        //  处理重量
        String weight = null;
        String checkWeight = PickUpFeatureUtil.getFromMulString(existDetailDO, PickUpConstants.TraceFeatureKey.CP_WEIGHT);
        //  揽收节点前已接收到核价节点，DB有重量，直接获取。之前未收到核价节点，根据类目得到一个默认重量，无类目默认重量(1kg)
        if (StringUtils.isNotBlank(checkWeight)) {
            weight = checkWeight;
        } else {
            List<GoodsInfo> goodsInfoList = pickUpAttributeManager.selectGoodsInfo(existDetailDO.getId(), existDetailDO.getMailNo());
            //  根据商品信息中的类目配置的重量信息，获取默认重量
            ImmutablePair<String, String> defaultAndMaxWeight = PickUpCategoryWeightUtil.getCategoryWeightInfo(goodsInfoList);
            weight = defaultAndMaxWeight.getLeft();
        }
        //  构建物流事件消息
        WaybillPickUpEvent waybillPickUpEvent = convertToEvent(requestData, existDetailDO, requestData.getAction());
        waybillPickUpEvent.setActionDesc("已揽收");
        Map<String, String> extraInfo = Maps.newHashMap();
        extraInfo.put(PickUpEventConstants.ExtraInfoKey.WEIGHT, weight);
        waybillPickUpEvent.setExtraInfo(JSON.toJSONString(extraInfo));
        waybillPickUpEventMetaQSender.send(waybillPickUpEvent);
    }

    /**
     * 处理一个带商品信息的订单：
     * 1.商品信息无类目属性，取默认重量；
     * 2.有类目信息，且只有一个类目，取该类目的默认重量
     * 3.有类目信息，且有多个类目，取重量最大的
     */
    private String dealHasGoodsInfoOrder(List<WaybillPickUpAttributeDO> goodsInfoAttrList, Map<String, Map<String, String>> categoryWeightConfig) {
        List<GoodsInfo> hasCategoryGoods = Lists.newArrayListWithCapacity(goodsInfoAttrList.size());
        Set<String> categorySet = Sets.newHashSet();
        for (WaybillPickUpAttributeDO attributeDO : goodsInfoAttrList) {
            if (StringUtils.isBlank(attributeDO.getAttributeValue())) {
                continue;
            }
            GoodsInfo goodsInfo = JSON.parseObject(attributeDO.getAttributeValue(), GoodsInfo.class);
            if (StringUtils.isNotBlank(goodsInfo.getCategory())) {
                hasCategoryGoods.add(goodsInfo);
                categorySet.add(goodsInfo.getCategory());
            }
        }
        String defaultCategoryKey = PickUpCreateOrderParamConstants.Category.DEFAULT_CATEGORY;
        String defaultWeightKey = PickUpCreateOrderParamConstants.Category.DEFAULT_WEIGHT;
        if (CollectionUtils.isEmpty(hasCategoryGoods)) {
            return categoryWeightConfig.get(defaultCategoryKey).get(defaultWeightKey);
        }
        int maxDefaultWeight = 0;
        for (String category : categorySet) {
            Map<String, String> weightConfig = categoryWeightConfig.get(category);
            if (MapUtils.isEmpty(weightConfig) || StringUtils.isBlank(weightConfig.get(defaultWeightKey))) {
                continue;
            }
            int curCategoryDefaultWeight = Integer.parseInt(weightConfig.get(defaultWeightKey));
            if (curCategoryDefaultWeight > maxDefaultWeight) {
                maxDefaultWeight = curCategoryDefaultWeight;
            }
        }
        return String.valueOf(maxDefaultWeight);
    }


    private void dealNormalAction(WaybillPickUpDetailDO existDetailDO, YunDaLogisticRequestData requestData) throws Exception {
        String action = BridgeSwitch.yundaPickUpLogisticsAction.get(requestData.getAction());
        if (StringUtils.isBlank(action)) {
            return;
        }
        WaybillPickUpEvent waybillPickUpEvent = convertToEvent(requestData, existDetailDO, action);
        LogisticStatusEnum logisticStatusEnum = LogisticStatusEnum.getWithEnumName(action);
        if (logisticStatusEnum != null) {
            waybillPickUpEvent.setActionDesc(logisticStatusEnum.getStatusDesc());
        }
        waybillPickUpEventMetaQSender.send(waybillPickUpEvent);
    }

    private WaybillPickUpEvent convertToEvent(YunDaLogisticRequestData requestData, WaybillPickUpDetailDO existDetailDO, String action) throws Exception {
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        WaybillPickUpEventBuilder ansBuilder = WaybillPickUpEventBuilder.builder()
                .cpCode(existDetailDO.getCpCode())
                .mailNo(existDetailDO.getMailNo())
                .outerOrderCode(existDetailDO.getOuterOrderCode())
                .resCode(existDetailDO.getResCode())
                .linkCpCode(PickUpFeatureUtil.getFromMulString(existDetailDO, PickUpConstants.TraceFeatureKey.LINK_CP_CODE))
                .action(action)
                //.actionDesc(LogisticStatusEnum.getWithEnumName(action).getStatusDesc())  状态说明韵达回传中没有，暂不设置，具体看哪个场景再判断
                .lastActionDetail(requestData.getDescription())
                .actionGmtModified(format.parse(requestData.getTime()).getTime())
                .actionCity(requestData.getCity());
        return ansBuilder.build();
    }


}
