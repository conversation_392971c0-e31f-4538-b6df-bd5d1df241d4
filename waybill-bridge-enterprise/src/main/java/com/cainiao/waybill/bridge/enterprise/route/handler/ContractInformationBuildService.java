package com.cainiao.waybill.bridge.enterprise.route.handler;


import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/7
 **/
@Service
public class ContractInformationBuildService implements ApplicationContextAware {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    private ApplicationContext applicationContext;

    private Map<String, ContractInformationBuild> methodHandlerMap = Maps.newHashMap();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    public void init() {
        Map<String, ContractInformationBuild> beanMap = applicationContext.getBeansOfType(ContractInformationBuild.class);
        beanMap.values().forEach(informationMapBuild -> {
            ContractInformationBuildMethod buildMethod = informationMapBuild.getClass().getAnnotation(ContractInformationBuildMethod.class);
            if (buildMethod != null) {
                methodHandlerMap.put(buildMethod.expressType().getExpressCode(), informationMapBuild);
            }
        });
    }

    public ContractInformationBuild route(String expressCode) {
        ContractInformationBuild contractInformationMapBuild = methodHandlerMap.get(expressCode);
        if (contractInformationMapBuild == null) {
            LOGGER.error("未找到对应约定的处理器, expressCode: {}", expressCode);
            return null;
        }
        return contractInformationMapBuild;
    }
}
