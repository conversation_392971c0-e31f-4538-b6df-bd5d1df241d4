package com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.role;

import com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.type.*;
import com.cainiao.waybill.bridge.innersupport.ad.constant.emums.AdUserRoleEnum;

public class CainiaoCustomerServiceActionRole extends ActionRole {
    public CainiaoCustomerServiceActionRole() {
        actionTypes.add(new RemarkUpdateActionType(AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue()));
        actionTypes.add(new AdvertiserUpdateActionType(AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue()));
        actionTypes.add(new ExpeditedUpdateActionType(AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue()));
        actionTypes.add(new RefundCompletedUpdateActionType(AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue()));
        actionTypes.add(new NoRefundCompletedUpdateActionType(AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue()));
        actionTypes.add(new AbnormallyCompletedUpdateActionType(AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue()));
        actionTypes.add(new PendingUpdateActionType(AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue()));
    }
}

