package com.cainiao.waybill.bridge.biz.fast.response;

import com.cainiao.waybill.bridge.biz.utils.excel.ExcelHeader;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @Date 2023/9/6
 */
@Data
public class FastShipmentRefundExportResponse implements Serializable {

    private static final long serialVersionUID = -5545059692679126353L;
    /**
     * 运单号
     */
    @ExcelHeader("运单号")
    private String waybillCode;

    /**
     * 打印机id
     */
    @ExcelHeader("打印机编码")
    private String printerId;
    /**
     * 运力模式
     */
    @ExcelHeader("运力类型")
    private String transportCapacity;

    /**
     * 运力来源
     */
    @ExcelHeader("运力来源")
    private String applyType;

    /**
     * 面单所属的CpCode
     */
    @ExcelHeader("CP")
    private String cpCode;

    /**
     * 网点名称
     */
    @ExcelHeader("取号网点")
    private String cpBranchName;
    /**
     * 小件员名称
     */
    @ExcelHeader("小件员姓名")
    private String courierName;

    /**
     * 小件员号码
     */
    @ExcelHeader("小件员联系方式")
    private String courierPhone;
    /**
     * 寄件人姓名
     */
    @ExcelHeader("寄件人姓名")
    private String sendName;
    /**
     * 寄件人手机号
     */
    @ExcelHeader("寄件人电话")
    private String sendPhone;

    /**
     * 寄件省份
     */
    @ExcelHeader("寄件省份")
    private String sourceProvince;

    /**
     * 寄件城市
     */
    @ExcelHeader("寄件城市")
    private String sourceCity;
    /**
     * 收件省份
     */
    @ExcelHeader("收件省份")
    private String destinationProvince;

    /**
     * 收件城市
     */
    @ExcelHeader("收件城市")
    private String destinationCity;


    /**
     * 下单时间
     */
    @ExcelHeader("下单时间")
    private String gmtCreate;

    /**
     * 揽收时间
     */
    @ExcelHeader("揽收时间")
    private String gotTime;
    /**
     * 支付时间
     */
    @ExcelHeader("结算时间")
    private String paidTime;

    /**
     * 订单状态
     */
    @ExcelHeader("订单状态")
    private String status;

    /**
     * 结算状态
     */
    @ExcelHeader("结算状态")
    private String payStatus;
    /**
     * 重量
     */
    @ExcelHeader("重量")
    private BigDecimal weight;

    /**
     * 运费金额
     */
    @ExcelHeader("运费金额")
    private Double totalPrice;

    /**
     * 操作人
     */
    @ExcelHeader("操作人")
    private String loginName;

    /**
     * 操作时间
     */
    @ExcelHeader("操作时间")
    private String gmtCreateRefund;

    /**
     * 退款金额
     */
    @ExcelHeader("退款金额")
    private Double refundAmount;
    /**
     * 退款原因
     */
    @ExcelHeader("退款原因")
    private String refundReason;
    /**
     * 审批状态 待审批、同意、拒绝
     */
    @ExcelHeader("审批状态")
    private String approvalStatus;
    /**
     * 退款状态 成功、失败
     */
    @ExcelHeader("退款状态")
    private String refundStatus;
    /**
     * 支付时间(退款成功时间)
     */
    @ExcelHeader("支付时间")
    private String payTime;


}
