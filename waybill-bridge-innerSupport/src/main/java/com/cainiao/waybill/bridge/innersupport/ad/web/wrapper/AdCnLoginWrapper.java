package com.cainiao.waybill.bridge.innersupport.ad.web.wrapper;

import com.cainiao.cnuser.client.ResultInfo;
import com.cainiao.cnuser.client.domain.CnUserInfoDO;
import com.cainiao.cnuser.client.service.CnUserInfoQueryService;
import com.cainiao.waybill.bridge.innersupport.ad.constant.AdCommonConstant;
import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.AdErrorConstants;
import com.cainiao.waybill.bridge.innersupport.ad.exception.AdBusinessException;
import com.cainiao.waybill.bridge.innersupport.ad.util.MethodRetryUtils;
import com.taobao.hsf.exception.HSFTimeOutException;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AdCnLoginWrapper {
    private final static Logger log = LoggerFactory.getLogger(AdCommonConstant.LOG_NAME);

    @Resource
    private CnUserInfoQueryService cnUserInfoQueryService;

    /**
     * 获取菜鸟登录会员的手机号
     *
     * @param loginUserId
     * @return
     */
    public String getCnLoginUserMobile(Long loginUserId) {
        ResultInfo<CnUserInfoDO> resultInfo;
        try {
            resultInfo = MethodRetryUtils.retryIfException(1, 0L, HSFTimeOutException.class, () -> cnUserInfoQueryService.getCnUserInfoDO(loginUserId));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new AdBusinessException(AdErrorConstants.QUERY_CN_USERINFO_ERROR.getErrorCode(), e.getMessage());
        }
        if (resultInfo == null || !resultInfo.isSuccess() || resultInfo.getData() == null) {
            throw new AdBusinessException(AdErrorConstants.QUERY_CN_USERINFO_ERROR);
        }
        String mobile = resultInfo.getData().getMobile();
        if (StringUtils.isBlank(mobile)) {
            throw new AdBusinessException(AdErrorConstants.CN_USERINFO_MOBILE_IS_NULL);
        }
        return mobile;
    }

    public ResultInfo<CnUserInfoDO> getCnLoginUserInfo(Long loginUserId) {
        ResultInfo<CnUserInfoDO> resultInfo;
        try {
            resultInfo = MethodRetryUtils.retryIfException(1, 0L, HSFTimeOutException.class, () -> cnUserInfoQueryService.getCnUserInfoDO(loginUserId));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new AdBusinessException(AdErrorConstants.QUERY_CN_USERINFO_ERROR.getErrorCode(), e.getMessage());
        }
        if (resultInfo == null || !resultInfo.isSuccess() || resultInfo.getData() == null) {
            throw new AdBusinessException(AdErrorConstants.QUERY_CN_USERINFO_ERROR);
        }
        String mobile = resultInfo.getData().getMobile();
        if (StringUtils.isBlank(mobile)) {
            throw new AdBusinessException(AdErrorConstants.CN_USERINFO_MOBILE_IS_NULL);
        }
        return resultInfo;
    }
}