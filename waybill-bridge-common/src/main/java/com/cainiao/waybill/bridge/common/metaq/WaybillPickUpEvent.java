package com.cainiao.waybill.bridge.common.metaq;

import com.cainiao.waybill.bridge.common.waybill.constants.WaybillPickUpActionConstant;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 逆向物流生命周期消息
 *
 * <AUTHOR>
 * @date 2021/6/16
 */
@Data
@Accessors(chain = true)
public class WaybillPickUpEvent {

    /**
     * cpCode，物流公司编号
     */
    private String cpCode;

    /**
     * 物流单号
     */
    private String mailNo;

    /**
     * 外部电商平台资源code
     */
    private String resCode;

    /**
     * 外部电商平台link传入的cpCode
     */
    private String linkCpCode;

    /**
     * 外部订单号
     */
    private String outerOrderCode;

    /**
     * 物流详情节点类型
     * {@link WaybillPickUpActionConstant}
     */
    private String action;

    /**
     * 物流详情节点类型描述
     */
    private String actionDesc;

    /**
     * 最后一条物流详情信息
     */
    private String lastActionDetail;

    /**
     * 物流详情节点动作发生时间
     */
    private Long actionGmtModified;

    /**
     * 物流详情节点行为发生的城市
     */
    private String actionCity;

    /**
     * 扩展信息，json
     */
    private String extraInfo;

    /**
     * 是否是详情订阅case
     */
    private boolean ldSubCase = false;

    private String orderChannel;

    /**
     * 接口推过来的物流单号，可能会和库里的MailNo 不一致
     */
    private String pushedMailNo;

    /**
     * 订单换单信息
     */
    private String orderStatusDes;
}
