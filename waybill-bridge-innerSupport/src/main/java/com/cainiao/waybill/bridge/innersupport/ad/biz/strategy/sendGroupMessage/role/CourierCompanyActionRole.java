package com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.role;

import com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.type.ExpeditedUpdateActionType;
import com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.type.RemarkUpdateActionType;
import com.cainiao.waybill.bridge.innersupport.ad.constant.emums.AdUserRoleEnum;

public class CourierCompanyActionRole extends ActionRole {
    public CourierCompanyActionRole() {
        actionTypes.add(new RemarkUpdateActionType(AdUserRoleEnum.COURIER_COMPANY_ROLE.getValue()));
        actionTypes.add(new ExpeditedUpdateActionType(AdUserRoleEnum.COURIER_COMPANY_ROLE.getValue()));
    }
}

