package com.cainiao.waybill.bridge.web.charity.userfilter;

import com.alibaba.fastjson.JSON;
import com.cainiao.cnlogin.api.context.CnUserInfo;
import com.cainiao.cnlogin.api.context.CnUserInfoUtil;
import com.cainiao.cnuser.client.ResultInfo;
import com.cainiao.cnuser.client.domain.CnUserInfoDO;
import com.cainiao.cnuser.client.service.CnUserInfoQueryService;
import com.cainiao.waybill.bridge.biz.common.user.WaybillBridgeUserInfoService;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.common.config.diamond.WaybillCharityDiamondConfig;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.administrators.request.User;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import com.cainiao.waybill.bridge.enterprise.common.BaseRequest;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseSwitchHolder;
import com.cainiao.waybill.bridge.enterprise.common.UserContext;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.utils.SessionUtil;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeUserInfoDTO;
import com.cainiao.waybill.bridge.web.charity.CharityLoginUserUtil;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/22 上午11:56
 */

@Setter
public class EnterpriseUserInterceptor implements HandlerInterceptor {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    private DingTalkAuthService dingTalkAuthService;

    private static final ThreadLocal<BaseRequest> context = new ThreadLocal<>();

    public static void setBaseRequest(BaseRequest baseRequest) {
        context.set(baseRequest);
    }


    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) throws Exception {
        try {

            // 从header中提取Id
            String Id = httpServletRequest.getHeader("user-id");
            // 从header中提取Id
            String sessionId = httpServletRequest.getHeader("X-Session-Id");
            if (StringUtils.isBlank(Id)) {
                writeUserAuthErrorResponse(httpServletResponse, EnterpriseErrorEnum.LOGIN_USER_ID_MISSING.code(), EnterpriseErrorEnum.LOGIN_USER_ID_MISSING.describe());
                return false;
            }
            // 从header中提取Id
            String corpId = httpServletRequest.getHeader("corp-id");
            if (StringUtils.isBlank(corpId)) {
                writeUserAuthErrorResponse(httpServletResponse, EnterpriseErrorEnum.LOGIN_CORP_ID_MISSING.code(), EnterpriseErrorEnum.LOGIN_CORP_ID_MISSING.describe());
                return false;
            }
            // 测试用户跳过授权
            if (EnterpriseSwitchHolder.ENTERPRISE_TEST_USER.contains(Id)) {
                User user = new User();
                user.setUserId(Id);
                user.setCorpId(corpId);
                UserContext.setUser(user);
                return true;
            }

            if (StringUtils.isBlank(sessionId)) {
                writeUserAuthErrorResponse(httpServletResponse, EnterpriseErrorEnum.LOGIN_SESSION_ID_MISSING.code(), EnterpriseErrorEnum.LOGIN_SESSION_ID_MISSING.describe());
            }

            // 进行权限校验
            Boolean result = dingTalkAuthService.checkSession(Id, sessionId);
            if (BooleanUtils.isFalse(result)) {
                writeNoAuthResponse(httpServletResponse);
                return false;
            }
            User user = SessionUtil.getUserFromToken(sessionId);
            if(user == null) {
//                dingTalkAuthService.delSession(Id);
                writeUserAuthErrorResponse(httpServletResponse, EnterpriseErrorEnum.LOGIN_USER_NOT_RIGHT.code(), EnterpriseErrorEnum.LOGIN_USER_NOT_RIGHT.describe());
                return false;
            }
            // 带入上下文
            user.setCorpId(corpId);
            UserContext.setUser(user);

        } catch (Throwable throwable) {
            LOGGER.error("权限校验异常", throwable);
            writeUserAuthErrorResponse(httpServletResponse, EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
            return false;
        }
        return true;
    }

    private void writeNoAuthResponse(HttpServletResponse response) throws IOException {
        response.setContentType("text/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(BaseResult.bizFail(EnterpriseErrorEnum.NO_AUTH.code(), EnterpriseErrorEnum.NO_AUTH.describe())));
        writer.flush();
        writer.close();
    }

    private void writeUserAuthErrorResponse(HttpServletResponse response, String errorCode, String errorMsg) throws IOException {
        response.setContentType("text/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        PrintWriter writer = response.getWriter();
        writer.write(JSON.toJSONString(BaseResult.bizFail(errorCode, errorMsg)));
        writer.flush();
        writer.close();
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o,
        ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
        Object o, Exception e) throws Exception {
        UserContext.clear(); // 确保请求结束后清理，避免内存泄漏
    }

    public static String getUserId(HttpServletRequest httpServletRequest) {
        return httpServletRequest.getHeader("user-id");
    }
}
