package com.cainiao.waybill.bridge.enterprise.administrators.controller;

import com.cainiao.waybill.bridge.enterprise.administrators.service.EnterpriseSettleAccountService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.UserContext;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EnterpriseSettleAccountControllerTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(EnterpriseSettleAccountControllerTest.class);
    @InjectMocks
    private EnterpriseSettleAccountController enterpriseSettleAccountController;
    @Mock
    private EnterpriseSettleAccountService settleAccountService;
    @Mock
    private UserContext userContext;

    @Before
    public void setUp() {
        // 初始化用户上下文中Mock获取到的用户
    }

    /**
     * 测试当调用settleAccountService的remove方法并返回结果为1时，结果为真
     */
    @Test(expected = Exception.class)
    public void testRemoveByWaybillAccountIdSuccess() {
        // Mock settleAccountService的removeByWaybillAccountId方法，使其返回1
        when(settleAccountService.removeByWaybillAccountId(anyString(), anyString())).thenReturn(1);
        // 调用要测试的controller方法
        EnterpriseBaseResult<Boolean> result = enterpriseSettleAccountController.removeByWaybillAccountId("validWaybillAccountId");
        // 验证返回的结果是成功的，且返回值为true
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
    }
}