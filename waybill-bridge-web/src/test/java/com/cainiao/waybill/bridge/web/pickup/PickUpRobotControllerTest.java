package com.cainiao.waybill.bridge.web.pickup;

import static org.mockito.Mockito.*;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.base.BaseTest;
import com.cainiao.waybill.bridge.biz.common.user.WaybillBridgeUserInfoService;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.model.dto.WaybillPickUpReportDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpDetailMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpOnlineStatisticsMapper;
import com.cainiao.waybill.bridge.web.pickup.request.PickUpOnlineReportRequest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.mockito.Mockito.any;

/**
 * <a href="https://alidocs.dingtalk.com/i/nodes/dpYLaezmVNRMGX56C1aKjlBOVrMqPxX6">单元测试编写&生成FAQ</a>
 * Created By Thub IDEA插件
 */
public class PickUpRobotControllerTest extends BaseTest {

    @InjectMocks
    private PickUpOnlineReportController pickUpRobotController;
    @Mock
    private WaybillPickUpDetailMapper waybillPickUpDetailMapper;
    @Mock
    private WaybillPickUpOnlineStatisticsMapper waybillPickUpOnlineStatisticsMapper;
    @Mock
    private WaybillBridgeUserInfoService userInfoService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testYishouCustomerIndexData() {
        WaybillPickUpReportDTO waybillPickUpReportDTO = new WaybillPickUpReportDTO();
        waybillPickUpReportDTO.setCustomer("customer");
        waybillPickUpReportDTO.setCancelNum(1);
        waybillPickUpReportDTO.setOrderNum(10);
        waybillPickUpReportDTO.setAcceptIn10mNum(3);
        waybillPickUpReportDTO.setCustomerGotNum(1);
        waybillPickUpReportDTO.setShouldGotInTimeGotNum(1);
        waybillPickUpReportDTO.setShouldGotNum(1);

        // Setup, prepare mock data
        Mockito.when(waybillPickUpDetailMapper.queryReportCustomer(any()))
                .thenReturn(Arrays.<WaybillPickUpReportDTO>asList(waybillPickUpReportDTO));

        // Prepare method arguments
        PickUpOnlineReportRequest pickUpOnlineReportRequest = new PickUpOnlineReportRequest();
        pickUpOnlineReportRequest.setAccessToken(BridgeSwitch.reportInterfaceAccessToken);
        // Run the method to be tested
        JSONObject result = pickUpRobotController.yishouCustomerIndexData(pickUpOnlineReportRequest);
        // Verify the result
        Assert.notNull(result);
    }

    @Test
    public void testAlipayCustomerIndexData(){
        // Setup, prepare mock data
        WaybillPickUpReportDTO waybillPickUpReportDTO = new WaybillPickUpReportDTO();
        waybillPickUpReportDTO.setCustomer("customer");
        waybillPickUpReportDTO.setCancelNum(1);
        waybillPickUpReportDTO.setOrderNum(10);
        waybillPickUpReportDTO.setAcceptIn10mNum(3);
        waybillPickUpReportDTO.setShouldGotInTimeGotNum(1);
        waybillPickUpReportDTO.setShouldGotNum(1);
        waybillPickUpReportDTO.setCustomerGotNum(2);
        Mockito.when(waybillPickUpDetailMapper.queryReportCustomer(any()))
                .thenReturn(Arrays.<WaybillPickUpReportDTO>asList(waybillPickUpReportDTO));

        // Prepare method arguments
        PickUpOnlineReportRequest pickUpOnlineReportRequest = new PickUpOnlineReportRequest();
        pickUpOnlineReportRequest.setAccessToken(BridgeSwitch.reportInterfaceAccessToken);
        // Run the method to be tested
        JSONObject result = pickUpRobotController.alipayCustomerIndexData(pickUpOnlineReportRequest);
        // Verify the result
        Assert.notNull(result);
    }
}