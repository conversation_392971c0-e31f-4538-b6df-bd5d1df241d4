<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta http-equiv="x-ua-compatible" content="ie=edge,chrome=1" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/png" href="https://img.alicdn.com/tfs/TB1CZ0ARpXXXXX7XXXXXXXXXXXX-32-32.png">
    <link rel="stylesheet" href="${resourceVersion}/css/index.css" >
    <title>淘外解决方案-小二控制台</title>
    <script>

        if(!window.__cone_config__) window.__cone_config__ = {};
        window.__cone_config__.microConfig = '$!{microConfig}';
    </script>
</head>

<body>
<div id="icestark-container">
    <div style="display:flex;align-items:center;justify-content:center;width:100vw;height:90vh;font-size:13px;color:#999;">
        加载中...
    </div>
    <div id="loading"></div>
</div>
<script src="//g.alicdn.com/code/lib/babel-polyfill/6.26.0/polyfill.min.js"></script>
<script src="https://g.alicdn.com/code/lib/react/16.10.2/umd/react.production.min.js"></script>
<script src="https://g.alicdn.com/code/lib/react-dom/16.10.2/umd/react-dom.production.min.js"></script>

<script src="https://g.alicdn.com/code/lib/prop-types/15.7.2/prop-types.min.js"></script>
<script>
    if (window.React && window.PropTypes) {
        React.PropTypes = PropTypes;
    }
    window.VERSION = '';
</script>
<script src="https://g.alicdn.com/vision/render-engine/7.0.7/render-engine.js"></script>

<!-- 版本号可以由diamond控制 -->
<script src="${resourceVersion}/js/index.js"></script>

</body>
</html>