package com.cainiao.waybill.bridge.common.label.validator;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.label.dto.AddressDTO;
import com.cainiao.waybill.bridge.common.label.dto.request.UpdateWeightRequest;
import com.cainiao.waybill.bridge.common.validator.BridgeBaseValidator;
import com.cainiao.waybill.bridge.common.label.dto.MailOrderInfoDTO;
import com.cainiao.waybill.bridge.common.label.dto.OrderInfoDTO;
import com.cainiao.waybill.bridge.common.label.dto.UserInfoDTO;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeApplyNewRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeQueryRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeUpdateRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-27
 */
public class WaybillPickupCodeValidator extends BridgeBaseValidator {

    public static void validateApplyNewRequest(WaybillPickupCodeApplyNewRequest request, Long courierId, ClientInfoDTO clientInfoDTO)
            throws BridgeValidationException {
        // request - mailOrderInfoDTO 校验
        validate(request != null,"request 不能为空");

        MailOrderInfoDTO mailOrderInfoDTO = request.getMailOrderInfoDTO();
        validate(mailOrderInfoDTO != null, "mailOrderInfoDTO 不能为空");
        UserInfoDTO sender = mailOrderInfoDTO.getSender();
        UserInfoDTO recipient = mailOrderInfoDTO.getRecipient();
        validate(sender != null, "sender 不能为空");
        validate(recipient != null, "recipient 不能为空");
        validate(sender.getAddress() != null, "sender.address 不能为空");
        validate(recipient.getAddress() != null, "recipient.address 不能为空");
        validateAddress(sender.getAddress());
        validateAddress(recipient.getAddress());
        // 手机号和电话不能同时为空
        validate(stringHasChar(sender.getMobile()) || stringHasChar(sender.getPhone()), "发货人电话和手机不能同时为空");
        validate(stringHasChar(recipient.getMobile()) || stringHasChar(recipient.getPhone()), "收件人电话和手机不能同时为空");

        OrderInfoDTO orderInfo = mailOrderInfoDTO.getOrderInfoDTO();
        validate(orderInfo != null, "orderInfo 不能为空");
        validate(StringUtils.isNotBlank(orderInfo.getOrderChannelsType()), "orderInfo.orderChannelsType 不能为空");
        validate(CollectionUtils.isNotEmpty(orderInfo.getTradeOrderList()), "orderInfo.tradeOrderList 不能为空");

        validate(mailOrderInfoDTO.getPackageInfo() != null, "packageInfo 不能为空");

        // request - 其他 校验
        validate(request.getSellerId() != null, "sellerId 不能为空");
        validate(StringUtils.isNotBlank(request.getSellerName()), "sellerName 不能为空");
        validate(StringUtils.isNotBlank(request.getCpCode()), "cpCode 不能为空");
        validate(StringUtils.isNotBlank(request.getBranchCode()), "branchCode 不能为空");

        // clientInfoDTO 校验
        validate(clientInfoDTO != null,"clientInfoDTO不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getAppName()), "clientInfoDTO.appName不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getOperator()), "clientInfoDTO.operator不能为空");

        validate(courierId != null,"courierId 不能为空");
    }

    public static void validateAddress(AddressDTO addressDTO) throws BridgeValidationException {
        validate(stringHasChar(addressDTO.getProvince()), "省份不能为空");
        validate(stringHasChar(addressDTO.getDetail()), "地址详情不能为空");


    }

    public static void validateQueryRequest(WaybillPickupCodeQueryRequest request, Long courierId, ClientInfoDTO clientInfoDTO)
            throws BridgeValidationException {
        validate(request != null,"request 不能为空");
        validate(request.getCurrentPageIndex() >= 0, "currentPageIndex 需要不小于0");
        validate(request.getSize() >= 0, "size 需要不小于0");
        validate(StringUtils.isNotBlank(request.getCpCode()), "cpCode 不能为空");
        validate(StringUtils.isNotBlank(request.getBranchCode()), "branchCode 不能为空");

        // clientInfoDTO 校验
        validate(clientInfoDTO != null,"clientInfoDTO不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getAppName()), "clientInfoDTO.appName不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getOperator()), "clientInfoDTO.operator不能为空");

        validate(courierId != null,"courierId 不能为空");
    }

    public static void validateUpdateRequest(WaybillPickupCodeUpdateRequest request, Long courierId, ClientInfoDTO clientInfoDTO)
            throws BridgeValidationException {
        validate(request != null,"request 不能为空");

        validate(StringUtils.isNotBlank(request.getCpCode()), "cpCode 不能为空");
        validate(StringUtils.isNotBlank(request.getWaybillCode()), "waybillCode 不能为空");

        validateClient(clientInfoDTO);

        validate(courierId != null,"courierId 不能为空");
    }

    public static void validateUpdateWeightRequest(UpdateWeightRequest request, Long courierId, ClientInfoDTO clientInfoDTO)
        throws BridgeValidationException {
        validate(request != null,"request 不能为空");

        validate(StringUtils.isNotBlank(request.getCpCode()), "cpCode 不能为空");
        validate(StringUtils.isNotBlank(request.getWaybillCode()), "waybillCode 不能为空");
        validate(courierId != null,"courierId 不能为空");

        validateClient(clientInfoDTO);
    }

    private static void validateClient(ClientInfoDTO clientInfoDTO) throws BridgeValidationException {
        // clientInfoDTO 校验
        validate(clientInfoDTO != null,"clientInfoDTO不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getAppName()), "clientInfoDTO.appName不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getOperator()), "clientInfoDTO.operator不能为空");
    }
}
