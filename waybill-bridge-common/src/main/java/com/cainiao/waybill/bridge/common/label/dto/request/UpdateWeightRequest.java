package com.cainiao.waybill.bridge.common.label.dto.request;

import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * 更新包裹重量 request
 * <AUTHOR>
 * @date 2017/06/08
 */
public class UpdateWeightRequest extends BaseDTO {
    private static final long serialVersionUID = 8373739529446290701L;

    private String cpCode;

    private String waybillCode;

    /**
     * 需要更新的包裹重量，以 g 为单位
     */
    private Integer weight;

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getWaybillCode() {
        return waybillCode;
    }

    public void setWaybillCode(String waybillCode) {
        this.waybillCode = waybillCode;
    }

    public Integer getWeight() {
        return weight;
    }

    public void setWeight(Integer weight) {
        this.weight = weight;
    }
}
