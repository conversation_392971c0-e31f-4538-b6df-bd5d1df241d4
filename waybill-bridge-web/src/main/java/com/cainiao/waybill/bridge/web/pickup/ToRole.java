package com.cainiao.waybill.bridge.web.pickup;

import java.io.File;
import java.io.IOException;
import java.util.List;

import com.alibaba.fastjson.JSON;

import com.cainiao.waybill.bridge.web.pickup.response.TicketRole;
import com.google.common.collect.Lists;
import org.apache.commons.io.FileUtils;

/**
 * <AUTHOR> zouping.fzp
 * @Classname ToRole
 * @Description
 * @Date 2022/7/25 11:32 上午
 * @Version 1.0
 */
public class ToRole {

    public static void main(String[] args) throws IOException {

        List<String> xiaoers = Lists.newArrayList("15869112514", "15052511436");
        List<String> ytoers = Lists.newArrayList();
        List<String> yundaers = Lists.newArrayList();
        List<String> JL = Lists.newArrayList();
        List<String> WIEDIAN = Lists.newArrayList();
        List<String> fhd = Lists.newArrayList();

        List<String> lines = FileUtils.readLines(new File("/Users/<USER>/Desktop/工作文件/临时文件/新增淘外管理端账号.txt"));
        for (String line : lines) {
            String[] cols = line.split(",");
            String phone = cols[0];
            String source = cols[1];
            if(source.equals("圆通")){
                ytoers.add(phone);
            }else if(source.equals("韵达")){
                yundaers.add(phone);
            }else if(source.equals("微点")){
                WIEDIAN.add(phone);
            }else if(source.equals("鲸灵")){
                JL.add(phone);
            }else if(source.equals("风火递")){
                fhd.add(phone);
            }
        }

        List<TicketRole> roles = Lists.newArrayList();
        for (String xiaoer : xiaoers) {
            TicketRole roleYq = new TicketRole();
            roleYq.setAccount(xiaoer);
            roleYq.setRoleType("cainiao");
            roles.add(roleYq);
        }

        for (String cper : ytoers) {
            TicketRole roleYq = new TicketRole();
            roleYq.setAccount(cper);
            roleYq.setRoleType("cp");
            roleYq.setSource("YTO");
            roles.add(roleYq);
        }

        for (String cper : yundaers) {
            TicketRole roleYq = new TicketRole();
            roleYq.setAccount(cper);
            roleYq.setRoleType("cp");
            roleYq.setSource("YUNDA");
            roles.add(roleYq);
        }

        for (String user : WIEDIAN) {
            TicketRole roleYq = new TicketRole();
            roleYq.setAccount(user);
            roleYq.setRoleType("platform");
            roleYq.setSource("********");
            roles.add(roleYq);
        }

        for (String user : JL) {
            TicketRole roleYq = new TicketRole();
            roleYq.setAccount(user);
            roleYq.setRoleType("platform");
            roleYq.setSource("JL");
            roles.add(roleYq);
        }

        for (String user : fhd) {
            TicketRole roleYq = new TicketRole();
            roleYq.setAccount(user);
            roleYq.setRoleType("platform");
            roleYq.setSource("FHD");
            roles.add(roleYq);
        }
        System.out.println(JSON.toJSONString(roles));
    }
}
