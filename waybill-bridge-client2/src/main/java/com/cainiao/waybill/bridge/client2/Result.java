package com.cainiao.waybill.bridge.client2;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/11/10-上午11:23
 */
@Data
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    private String errorCode;
    private String errorMsg;
    private boolean success = true;
    private T data;
    /**
     * 分页信息
     */
    private PagingInfo pagingInfo;

    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<T>();
        result.setData(data);
        result.setSuccess(true);
        return result;
    }

    public static <T> Result<T> success() {
        Result<T> result = new Result<T>();
        result.setSuccess(true);
        return result;
    }

    public static <T> Result<T> fail(T data) {
        Result<T> result = new Result<T>();
        result.setData(data);
        result.setSuccess(false);
        return result;
    }

    public static <T> Result<T> bizFail(String errorCode, String errorMsg) {
        Result<T> result = new Result<T>();
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setErrorMsg(errorMsg);
        return result;
    }

    public static <T> Result<T> systemFail() {
        Result<T> result = new Result<T>();
        result.setSuccess(false);
        result.setErrorCode("-1");
        result.setErrorMsg("后台系统异常");
        return result;
    }
}

