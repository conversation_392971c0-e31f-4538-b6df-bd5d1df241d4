package com.cainiao.waybill.bridge.common.util;

import com.cainiao.waybill.bridge.common.dto.BaseDTO;

import java.io.Serializable;
import java.util.List;

/**
 * 分页对象
 * 
 * <AUTHOR>
 * @since 12/17/14
 */
public class Page<E> extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -164919719591056526L;

    public static final int DEFAULT_PAGE_SIZE = 10;

    public Page() {
    }

    public Page(List<E> data, Integer start, Integer size, Integer totalRecord) {
        if (size == null || size <= 0) {
            size = DEFAULT_PAGE_SIZE;
        }
        this.totalPage = totalRecord / size + (totalRecord % size == 0 ? 0 : 1);
        this.start = start;
        this.currentPageIndex = start / size + 1;
        this.size = size;
        this.totalRecord = totalRecord;
        this.data = data;
    }

    private int     start            = 0;

    private int     currentPageIndex = 0;

    private int     size             = 10;

    private int     totalPage        = 0;

    private int     totalRecord      = 0;

    private List<E> data;

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getCurrentPageIndex() {
        return currentPageIndex;
    }

    public void setCurrentPageIndex(int currentPageIndex) {
        this.currentPageIndex = currentPageIndex;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(int totalPage) {
        this.totalPage = totalPage;
    }

    public int getTotalRecord() {
        return totalRecord;
    }

    public void setTotalRecord(int totalRecord) {
        this.totalRecord = totalRecord;
    }

    public List<E> getData() {
        return data;
    }

    public void setData(List<E> data) {
        this.data = data;
    }

}
