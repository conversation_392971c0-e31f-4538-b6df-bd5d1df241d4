package com.cainiao.waybill.bridge.web.pickup.common;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.ali.com.google.common.collect.Lists;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpCpEnum;
import com.cainiao.waybill.bridge.biz.pickup.dto.TextValueInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpOrderSourceDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpPairDTO;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpBaseRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpCommonQueryConstantRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.TicketRoleEnum;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.common.constants.LogisticStatusEnum;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpCommonController
 * @Description
 * @Date 2023/5/9 4:59 下午
 * @Version 1.0
 */
@Slf4j(topic = "PICK_UP_MANAGER_INFO")
@RestController
public class PickUpCommonController {

    @RequestMapping("/pickup/common/constant")
    public BaseResult<Map<String, List<PickUpPairDTO>>> queryConstants(
        @RequestBody PickUpCommonQueryConstantRequest request) {
        BridgeValidator.validate(request);
        Map<String, List<PickUpPairDTO>> map = Maps.newHashMap();
        for (String type : request.getConstantsType()) {
            List<PickUpPairDTO> list = Lists.newArrayList();
            // 订单来源类型
            if ("orderSourceType".equals(type)) {
                for (PickUpOrderSourceDTO pickUpOrderSourceDTO : BridgeSwitch.pickupOrderSourceMapping) {
                    if (isAdminOrCainiaoService(request) || checkRight(request, pickUpOrderSourceDTO.getFromAppKey(),
                        pickUpOrderSourceDTO.getOrderChannel())) {
                        PickUpPairDTO pickUpPairDTO = new PickUpPairDTO();
                        pickUpPairDTO.setText(pickUpOrderSourceDTO.getSourceName());
                        pickUpPairDTO.setValue(pickUpOrderSourceDTO.getOrderChannel());
                        list.add(pickUpPairDTO);
                    }
                }
            }
            // 工单类型
            if ("ticketType".equals(type)) {
                for (TextValueInfo textValueInfo : BridgeSwitch.ticketTypes) {
                    PickUpPairDTO pickUpPairDTO = new PickUpPairDTO();
                    pickUpPairDTO.setText(textValueInfo.getText());
                    pickUpPairDTO.setValue(textValueInfo.getValue());
                    list.add(pickUpPairDTO);
                }
            }

            // 工单类型
            if ("cpType".equals(type)) {
                for (Entry<String, String> entry : BridgeSwitch.cpInfo.entrySet()) {
                    PickUpPairDTO pickUpPairDTO = new PickUpPairDTO();
                    pickUpPairDTO.setValue(entry.getKey());
                    pickUpPairDTO.setText(entry.getValue());
                    list.add(pickUpPairDTO);
                }
            }
            // 工单类型
            if ("logisticsStatusType".equals(type)) {
                for (LogisticStatusEnum logisticStatusEnum : LogisticStatusEnum.values()) {
                    PickUpPairDTO pickUpPairDTO = new PickUpPairDTO();
                    pickUpPairDTO.setValue(String.valueOf(logisticStatusEnum.getStatusValue()));
                    pickUpPairDTO.setText(logisticStatusEnum.getStatusDesc());
                    list.add(pickUpPairDTO);
                }
            }
            map.put(type, list);
        }

        return BaseResult.success(map);
    }

    private boolean checkRight(PickUpBaseRequest request, String fromAppKey, String orderChannel) {
        return ListUtil.non(request.getUserSource()).contains(orderChannel);
    }

    private boolean isAdminOrCainiaoService(PickUpBaseRequest request){
        return TicketRoleEnum.admin.name().equals(request.getRole())
            || TicketRoleEnum.cnCs.name().equals(request.getRole());
    }

}
