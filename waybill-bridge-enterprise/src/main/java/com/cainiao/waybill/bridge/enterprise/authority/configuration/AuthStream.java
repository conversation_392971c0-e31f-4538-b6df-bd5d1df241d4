package com.cainiao.waybill.bridge.enterprise.authority.configuration;

import com.alibaba.fastjson2.JSONObject;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.administrators.service.DingTalkUserService;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePlatformEnum;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseAuthDO;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseAuthParam;
import com.cainiao.waybill.bridge.model.mapper.WaybillEnterpriseAuthMapper;
import com.dingtalk.open.app.api.GenericEventListener;
import com.dingtalk.open.app.api.OpenDingTalkStreamClientBuilder;
import com.dingtalk.open.app.api.message.GenericOpenDingTalkEvent;
import com.dingtalk.open.app.api.security.AuthClientCredential;
import com.dingtalk.open.app.stream.protocol.event.EventAckStatus;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import com.cainiao.waybill.bridge.enterprise.common.enums.DingTalkEvenTypeEnum;

import javax.annotation.PostConstruct;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;

@Configuration
public class AuthStream {
    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Value("${enterprise.dingtalk.clientId}")
    String clientId;

    @Value("${enterprise.dingtalk.clientSecret}")
    String clientSecret;

    @Autowired
    private WaybillEnterpriseAuthMapper waybillEnterpriseAuthMapper;

    @Autowired
    private DingTalkUserService dingTalkUserService;

    @PostConstruct
    public void init() throws Exception {


        OpenDingTalkStreamClientBuilder
                .custom()
                .credential(new AuthClientCredential(clientId, clientSecret))
                //注册事件监听
                .registerAllEventListener(new GenericEventListener() {
                    @Override
                    public EventAckStatus onEvent(GenericOpenDingTalkEvent event) {
                        try {
                            LOGGER.info("get stream message from DingTalk， msgType:{} data:{}", event.getEventType(), event.getData());
                            //事件唯一Id
                            String eventId = event.getEventId();
                            //事件类型
                            String eventType = event.getEventType();
                            //事件产生时间
                            Long bornTime = event.getEventBornTime();
                            //事件产生企业
                            String corpId = event.getEventCorpId();
                            // 检查bizData是否为空
                            if (StringUtils.isEmpty(corpId)) {
                                LOGGER.error("get stream corpId is null eventId:{} bornTime:{}", eventId, bornTime);
                                return EventAckStatus.SUCCESS;
                            }
                            //获取事件体
                            JSONObject bizData = event.getData();
                            // 检查bizData是否为空
                            if (bizData == null) {
                                LOGGER.error("get stream bizData is null eventId:{} bornTime:{}", eventId, bornTime);
                                return EventAckStatus.SUCCESS;
                            }
                            //处理事件
                            if(StringUtils.equals(DingTalkEvenTypeEnum.SUITE_TICKET.getCode(), eventType)){
                                orgSuiteTicketAuthProcess(corpId, bizData);
                            } else if (StringUtils.equals(DingTalkEvenTypeEnum.ORG_SUITE_AUTH.getCode(), eventType)) {
                                LOGGER.info("企业授权开通应用事件 cropId:{} eventId:{}", corpId, eventId);
                                orgSuiteAuthProcess(bizData);
                            }else if(StringUtils.equals(DingTalkEvenTypeEnum.USER_ROLE_CHANGE.getCode(), eventType)){
                                LOGGER.info("企业管理员信息变动 cropId:{} eventId:{}", corpId, eventId);
                                userRoleChangeProcess(corpId, bizData);
                            }
                            //消费成功
                            return EventAckStatus.SUCCESS;
                        } catch (Exception e) {
                            //消费失败
                            return EventAckStatus.LATER;
                        }
                    }
                })
                .build().start();
    }

    private void orgSuiteAuthProcess(JSONObject data){

        // 提取 authCropInfo
        JSONObject authCropInfo = data.getJSONObject("authCorpInfo");
        // 检查suitTicket是否为空
        if (null == authCropInfo) {
            LOGGER.info("企业授权开通应用事件失败 authCropInfo 不为空");
            return;
        }

        // 提取 authCropInfo
        String authCropId = data.getString("authCorpInfo");
        // 检查authCropId是否为空
        if (StringUtils.isBlank(authCropId)) {
            LOGGER.info("企业授权开通应用事件失败 cropId 不为空");
            return;
        }

        // 根据corpId查询记录
        WaybillEnterpriseAuthParam waybillEnterpriseAuthParam = new WaybillEnterpriseAuthParam();
        waybillEnterpriseAuthParam.createCriteria().andCorpIdEqualTo(authCropId);
        WaybillEnterpriseAuthDO enterpriseAuthDO = waybillEnterpriseAuthMapper.selectOneByParam(waybillEnterpriseAuthParam);
        // format 时间
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        ZonedDateTime zonedDateTime = now.atZone(ZoneId.systemDefault());
        Instant instant = zonedDateTime.toInstant();
        Date date = Date.from(instant);
        // 存在当前的cropId-重复授权
        if(null != enterpriseAuthDO){
            enterpriseAuthDO.setRefreshTime(date);
            enterpriseAuthDO.setGmtModified(date);
            waybillEnterpriseAuthMapper.updateByPrimaryKey(enterpriseAuthDO);
            return;
        }
        //  当前cropId不存在
        WaybillEnterpriseAuthDO authDO = new WaybillEnterpriseAuthDO();
        authDO.setCorpId(authCropId);
        authDO.setPlatform(EnterprisePlatformEnum.DingTalk.name());
        authDO.setRefreshTime(date);
        authDO.setGmtCreate(date);
        authDO.setGmtModified(date);
        waybillEnterpriseAuthMapper.insert(authDO);
        return;
    }

    private void orgSuiteTicketAuthProcess(String corpId, JSONObject data){

        // 提取 corpId
        String suitTicket = data.getString("suiteTicket");
        // 检查suitTicket是否为空
        if (StringUtils.isBlank(suitTicket)) {
            return;
        }

        // 根据corpId查询记录
        WaybillEnterpriseAuthParam waybillEnterpriseAuthParam = new WaybillEnterpriseAuthParam();
        waybillEnterpriseAuthParam.createCriteria().andCorpIdEqualTo(corpId);
        WaybillEnterpriseAuthDO enterpriseAuthDO = waybillEnterpriseAuthMapper.selectOneByParam(waybillEnterpriseAuthParam);
        // format 时间
        LocalDateTime now = LocalDateTime.now(ZoneId.systemDefault());
        ZonedDateTime zonedDateTime = now.atZone(ZoneId.systemDefault());
        Instant instant = zonedDateTime.toInstant();
        Date date = Date.from(instant);
        // 存在当前的cropId-重复授权
        if(null != enterpriseAuthDO){
            enterpriseAuthDO.setSuiteTicket(suitTicket);
            enterpriseAuthDO.setRefreshTime(date);
            enterpriseAuthDO.setGmtModified(date);
            waybillEnterpriseAuthMapper.updateByPrimaryKey(enterpriseAuthDO);
            return;
        }
        //  当前cropId不存在
        WaybillEnterpriseAuthDO authDO = new WaybillEnterpriseAuthDO();
        authDO.setCorpId(corpId);
        authDO.setSuiteTicket(suitTicket);
        authDO.setPlatform(EnterprisePlatformEnum.DingTalk.name());
        authDO.setRefreshTime(date);
        authDO.setGmtCreate(date);
        authDO.setGmtModified(date);
        waybillEnterpriseAuthMapper.insert(authDO);
        return;
    }

    /**
     * 用户角色变更处理
     *
     * <p>处理用户角色变更的逻辑。
     *
     * @param corpId 企业的唯一标识符
     * @param data 包含变更信息的JSON对象
     * @return 无返回值
     */
    private void userRoleChangeProcess(String corpId, JSONObject data){

        // 提取 是否是管理员
        Boolean isAdmin = data.getBoolean("isAdmin");
        // 提取 是否激活
        Boolean isActive = data.getBoolean("active");
        // 提取 是否是管理员
        String userId = data.getString("userId");
        // 检查suitTicket是否为空
        if (StringUtils.isBlank(userId)) {
            return;
        }
        // 同步一次当前企业管理员
        dingTalkUserService.dingTalkAdminSync(corpId, userId, isAdmin);
        // 同步一次当前企业员工
        dingTalkUserService.dingTalkUserSync(corpId, userId, isActive);
    }

}
