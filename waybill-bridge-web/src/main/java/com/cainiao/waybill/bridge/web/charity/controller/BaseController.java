package com.cainiao.waybill.bridge.web.charity.controller;

import javax.annotation.Resource;

import com.cainiao.cnlogin.api.context.CnUserInfo;
import com.cainiao.cnlogin.api.context.CnUserInfoUtil;
import com.cainiao.waybill.bridge.biz.common.user.WaybillBridgeUserInfoService;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error;
import com.cainiao.waybill.bridge.biz.ticket.dto.TicketRoleEnum;
import com.cainiao.waybill.bridge.common.constants.UserInfoSceneEnum;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeUserInfoDTO;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.ExceptionHandler;

/**
 * <AUTHOR> zouping.fzp
 * @Classname BaseController
 * @Description
 * @Date 2022/8/29 5:42 下午
 * @Version 1.0
 */
@Slf4j(topic = "CHARITY_INFO")
public class BaseController {

    @Resource
    private WaybillBridgeUserInfoService userInfoService;

    @ExceptionHandler(value = BridgeBusinessException.class)
    public BaseResult<?> handleBizException(BridgeBusinessException e) {
        log.error("business_exception, errorCode:{}, errorMsg:{}", e.getErrorCode(), e.getErrorMessage(), e);
        return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
    }

    @ExceptionHandler(value = BridgeBaseException.class)
    public BaseResult<?> handleBizException(BridgeBaseException e) {
        log.error("business_exception, errorCode:{}, errorMsg:{}", e.getErrorCode(), e.getErrorMessage(), e);
        return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
    }

    @ExceptionHandler(value = Exception.class)
    public BaseResult<?> handleBizException(Exception e) {
        log.error("system_exception, errorMsg:{}", e.getMessage(), e);
        return BaseResult.bizFail("system_exception", "系统异常");
    }

    /**
     * 校验登录用户角色是否有该菜单权限
     * @param primarySource
     * @param role
     * @return
     */
    public void checkMenuRoleValid(String primarySource, String role){
        // 用户所属角色合法的菜单来源
        String roleSource = TicketRoleEnum.sourceFromRole(role);

        // 一致性校验
        boolean consistency = StringUtils.equals(role, TicketRoleEnum.admin.name())
            || StringUtils.equals(primarySource, roleSource);
        if(!consistency){
            throw new BridgeBusinessException(Error.NO_PERMISSION_ERROR);
        }
    }

    /**
     * 获取登录人信息
     * @return
     */
    public WaybillBridgeUserInfoDTO getLoginUserInfo(){
        return userInfoService.getLoginUser();
    }

    /**
     * 获取登录人角色
     * @return
     */
    public TicketRoleEnum getUserRole(){
        CnUserInfo userInfo = CnUserInfoUtil.getLoginContext();
        if(userInfo == null || StringUtil.isBlank(userInfo.getAccount())){
            return null;
        }
        WaybillBridgeUserInfoDTO userInfoDTO = userInfoService.findCharityUser(userInfo.getAccount(), UserInfoSceneEnum.Ntb.name());
        if(null == userInfoDTO){
            return null;
        }
        return TicketRoleEnum.get(userInfoDTO.getRole());
    }

    /**
     * 获取登录人昵称
     * @return
     */
    public String getUserNick(){
        WaybillBridgeUserInfoDTO userInfo = userInfoService.getLoginUser();
        if(null == userInfo){
            return null;
        }
        return StringUtils.isBlank(userInfo.getNick()) ? userInfo.getPhone() : userInfo.getNick();
    }

}
