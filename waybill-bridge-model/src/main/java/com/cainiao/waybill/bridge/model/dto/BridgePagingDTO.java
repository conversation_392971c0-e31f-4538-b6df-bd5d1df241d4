package com.cainiao.waybill.bridge.model.dto;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname CharityPagingResponse
 * @Description
 * @Date 2022/8/29 3:31 下午
 * @Version 1.0
 */
@Data
public class BridgePagingDTO<T>{

    private List<T> tableData;

    private BridgePaging paging;

    public static <T> BridgePagingDTO<T> build(List<T> tableData, long totalCount, Integer currentPage, Integer pageSize){
        BridgePagingDTO<T> response = new BridgePagingDTO<>();
        response.setTableData(tableData);
        BridgePaging bridgePaging = new BridgePaging();
        bridgePaging.setCurrentPage(currentPage);
        bridgePaging.setPageSize(pageSize);
        bridgePaging.setTotalCount(Math.toIntExact(totalCount));
        response.setPaging(bridgePaging);
        return response;
    }
}
