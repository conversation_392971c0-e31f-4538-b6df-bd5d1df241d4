package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class QuoteConfigInfoDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   报价名称
     *
     *
     * @mbg.generated
     */
    private String quoteName;

    /**
     * Database Column Remarks:
     *   归属主体名称
     *
     *
     * @mbg.generated
     */
    private String ownerName;

    /**
     * Database Column Remarks:
     *   归属主体ID
     *
     *
     * @mbg.generated
     */
    private Long ownerId;

    /**
     * Database Column Remarks:
     *   报价类型
     *
     *
     * @mbg.generated
     */
    private String quoteType;

    /**
     * Database Column Remarks:
     *   CP编码
     *
     *
     * @mbg.generated
     */
    private String cpCode;

    /**
     * Database Column Remarks:
     *   产品编码
     *
     *
     * @mbg.generated
     */
    private String productCode;

    /**
     * Database Column Remarks:
     *   结算方案类型
     *
     *
     * @mbg.generated
     */
    private String settlementType;

    /**
     * Database Column Remarks:
     *   费用项
     *
     *
     * @mbg.generated
     */
    private String feeType;

    /**
     * Database Column Remarks:
     *   生效状态
     *
     *
     * @mbg.generated
     */
    private String status;

    /**
     * Database Column Remarks:
     *   审批状态
     *
     *
     * @mbg.generated
     */
    private String approveStatus;

    /**
     * Database Column Remarks:
     *   生效起始时间
     *
     *
     * @mbg.generated
     */
    private Date effectiveTimeStart;

    /**
     * Database Column Remarks:
     *   生效截止时间
     *
     *
     * @mbg.generated
     */
    private Date effectiveTimeEnd;

    /**
     * Database Column Remarks:
     *   报价文件URL
     *
     *
     * @mbg.generated
     */
    private String fileUrl;

    /**
     * Database Column Remarks:
     *   扩展字段
     *
     *
     * @mbg.generated
     */
    private String feature;

    /**
     * Database Column Remarks:
     *   是否删除
     *
     *
     * @mbg.generated
     */
    private String isDelete;

    /**
     * Database Column Remarks:
     *   报价配置key
     *
     *
     * @mbg.generated
     */
    private Long quoteKey;

    /**
     *
     * @return the value of quote_config_info.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for quote_config_info.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of quote_config_info.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for quote_config_info.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of quote_config_info.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for quote_config_info.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of quote_config_info.quote_name
     *
     * @mbg.generated
     */
    public String getQuoteName() {
        return quoteName;
    }

    /**
     *
     * @param quoteName the value for quote_config_info.quote_name
     *
     * @mbg.generated
     */
    public void setQuoteName(String quoteName) {
        this.quoteName = quoteName;
    }

    /**
     *
     * @return the value of quote_config_info.owner_name
     *
     * @mbg.generated
     */
    public String getOwnerName() {
        return ownerName;
    }

    /**
     *
     * @param ownerName the value for quote_config_info.owner_name
     *
     * @mbg.generated
     */
    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    /**
     *
     * @return the value of quote_config_info.owner_id
     *
     * @mbg.generated
     */
    public Long getOwnerId() {
        return ownerId;
    }

    /**
     *
     * @param ownerId the value for quote_config_info.owner_id
     *
     * @mbg.generated
     */
    public void setOwnerId(Long ownerId) {
        this.ownerId = ownerId;
    }

    /**
     *
     * @return the value of quote_config_info.quote_type
     *
     * @mbg.generated
     */
    public String getQuoteType() {
        return quoteType;
    }

    /**
     *
     * @param quoteType the value for quote_config_info.quote_type
     *
     * @mbg.generated
     */
    public void setQuoteType(String quoteType) {
        this.quoteType = quoteType;
    }

    /**
     *
     * @return the value of quote_config_info.cp_code
     *
     * @mbg.generated
     */
    public String getCpCode() {
        return cpCode;
    }

    /**
     *
     * @param cpCode the value for quote_config_info.cp_code
     *
     * @mbg.generated
     */
    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    /**
     *
     * @return the value of quote_config_info.product_code
     *
     * @mbg.generated
     */
    public String getProductCode() {
        return productCode;
    }

    /**
     *
     * @param productCode the value for quote_config_info.product_code
     *
     * @mbg.generated
     */
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    /**
     *
     * @return the value of quote_config_info.settlement_type
     *
     * @mbg.generated
     */
    public String getSettlementType() {
        return settlementType;
    }

    /**
     *
     * @param settlementType the value for quote_config_info.settlement_type
     *
     * @mbg.generated
     */
    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType;
    }

    /**
     *
     * @return the value of quote_config_info.fee_type
     *
     * @mbg.generated
     */
    public String getFeeType() {
        return feeType;
    }

    /**
     *
     * @param feeType the value for quote_config_info.fee_type
     *
     * @mbg.generated
     */
    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    /**
     *
     * @return the value of quote_config_info.status
     *
     * @mbg.generated
     */
    public String getStatus() {
        return status;
    }

    /**
     *
     * @param status the value for quote_config_info.status
     *
     * @mbg.generated
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     *
     * @return the value of quote_config_info.approve_status
     *
     * @mbg.generated
     */
    public String getApproveStatus() {
        return approveStatus;
    }

    /**
     *
     * @param approveStatus the value for quote_config_info.approve_status
     *
     * @mbg.generated
     */
    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    /**
     *
     * @return the value of quote_config_info.effective_time_start
     *
     * @mbg.generated
     */
    public Date getEffectiveTimeStart() {
        return effectiveTimeStart;
    }

    /**
     *
     * @param effectiveTimeStart the value for quote_config_info.effective_time_start
     *
     * @mbg.generated
     */
    public void setEffectiveTimeStart(Date effectiveTimeStart) {
        this.effectiveTimeStart = effectiveTimeStart;
    }

    /**
     *
     * @return the value of quote_config_info.effective_time_end
     *
     * @mbg.generated
     */
    public Date getEffectiveTimeEnd() {
        return effectiveTimeEnd;
    }

    /**
     *
     * @param effectiveTimeEnd the value for quote_config_info.effective_time_end
     *
     * @mbg.generated
     */
    public void setEffectiveTimeEnd(Date effectiveTimeEnd) {
        this.effectiveTimeEnd = effectiveTimeEnd;
    }

    /**
     *
     * @return the value of quote_config_info.file_url
     *
     * @mbg.generated
     */
    public String getFileUrl() {
        return fileUrl;
    }

    /**
     *
     * @param fileUrl the value for quote_config_info.file_url
     *
     * @mbg.generated
     */
    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    /**
     *
     * @return the value of quote_config_info.feature
     *
     * @mbg.generated
     */
    public String getFeature() {
        return feature;
    }

    /**
     *
     * @param feature the value for quote_config_info.feature
     *
     * @mbg.generated
     */
    public void setFeature(String feature) {
        this.feature = feature;
    }

    /**
     *
     * @return the value of quote_config_info.is_delete
     *
     * @mbg.generated
     */
    public String getIsDelete() {
        return isDelete;
    }

    /**
     *
     * @param isDelete the value for quote_config_info.is_delete
     *
     * @mbg.generated
     */
    public void setIsDelete(String isDelete) {
        this.isDelete = isDelete;
    }

    /**
     *
     * @return the value of quote_config_info.quote_key
     *
     * @mbg.generated
     */
    public Long getQuoteKey() {
        return quoteKey;
    }

    /**
     *
     * @param quoteKey the value for quote_config_info.quote_key
     *
     * @mbg.generated
     */
    public void setQuoteKey(Long quoteKey) {
        this.quoteKey = quoteKey;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", quoteName=").append(quoteName);
        sb.append(", ownerName=").append(ownerName);
        sb.append(", ownerId=").append(ownerId);
        sb.append(", quoteType=").append(quoteType);
        sb.append(", cpCode=").append(cpCode);
        sb.append(", productCode=").append(productCode);
        sb.append(", settlementType=").append(settlementType);
        sb.append(", feeType=").append(feeType);
        sb.append(", status=").append(status);
        sb.append(", approveStatus=").append(approveStatus);
        sb.append(", effectiveTimeStart=").append(effectiveTimeStart);
        sb.append(", effectiveTimeEnd=").append(effectiveTimeEnd);
        sb.append(", fileUrl=").append(fileUrl);
        sb.append(", feature=").append(feature);
        sb.append(", isDelete=").append(isDelete);
        sb.append(", quoteKey=").append(quoteKey);
        sb.append("]");
        return sb.toString();
    }
}