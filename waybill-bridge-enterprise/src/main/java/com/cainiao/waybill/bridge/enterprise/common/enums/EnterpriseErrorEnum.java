package com.cainiao.waybill.bridge.enterprise.common.enums;

public enum EnterpriseErrorEnum implements CodeDescribe {

    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功"),

    /**
     * 没有权限
     */
    NO_AUTH("NO_AUTH", "没有权限"),

    /**
     * 登陆态用户ID缺失
     */
    LOGIN_USER_ID_MISSING("LOGIN_USER_ID_MISSING", "登陆态用户ID缺失"),

    /**
     * 登陆态用户异常
     */
    LOGIN_USER_NOT_RIGHT("LOGIN_USER_NOT_RIGHT", "登陆态用户异常"),

    /**
     * 登陆态用户SESSION缺失
     */
    LOGIN_SESSION_ID_MISSING("LOGIN_SESSION_ID_MISSING", "登陆态用户SESSION缺失"),

    /**
     * 参数异常
     */
    PARAM_ERROR("PARAM_ERROR", "参数异常"),

    /**
     * 场地已存在
     */
    LOCATION_HAS_EXIST("LOCATION_HAS_EXIST", "场地已存在"),

    /**
     * 场地不存在
     */
    LOCATION_NOT_EXIST("LOCATION_NOT_EXIST", "场地不存在"),

    /**
     * 获取权限Token失败
     */
    QUERY_ACCESSTOKEN_FAILED("QUERY_ACCESSTOKEN_FAILED", "获取权限Token失败"),


    /**
     * 获取权限Token失败
     */
    QUERY_USERTOKEN_FAILED("QUERY_USERTOKEN_FAILED", "获取用户权限Token失败"),

    /**
     * 使用免登码查询用户信息失败
     */
    QUERY_USER_INFO_BY_CODE_FAILED("QUERY_USER_INFO_BY_CODE_FAILED", "使用免登码查询用户信息失败"),

    /**
     * 使用免登码查询用户信息失败
     */
    QUERY_USER_INFO_BY_AUTH_CODE_FAILED("QUERY_USER_INFO_BY_AUTH_CODE_FAILED", "使用免登码查询用户信息失败"),

    /**
     * 使用USERID查询用户信息失败
     */
    QUERY_USER_INFO_BY_USERID_FAILED("QUERY_USER_INFO_BY_USERID_FAILED", "使用USERID查询用户信息失败"),

    /**
     * 查询系统管理员列表失败
     */
    QUERY_ADMIN_USER_ID_FAILED("QUERY_ADMIN_USER_ID_FAILED", "查询系统管理员列表失败"),

    /**
     * 登陆态失效
     */
    LOGIN_SESSION_EXPIRED("LOGIN_SESSION_EXPIRED", "登陆态失效"),

    /**
     * 登陆userID不存在
     */
    LOGIN_USERID_NOT_EXIST("LOGIN_USERID_NOT_EXIST", "登陆userID不存在"),

    /**
     * 登陆态异常
     */
    LOGIN_SESSION_EXPIRED_OR_NOT_RIGHT("LOGIN_SESSION_EXPIRED_OR_NOT_RIGHT", "登陆态异常"),
    /**
     * 系统异常
     */
    SYSTEM_ERROR("SYSTEM_ERROR", "系统异常"),

    /**
     * 月结账号已存在
     */
    ACCOUNT_HAS_EXIST("ACCOUNT_HAS_EXIST", "月结账号已存在"),

    /**
     * 月结账号不存在
     */
    ACCOUNT_NOT_EXIST("ACCOUNT_NOT_EXIST", "月结账号不存在"),

    /**
     * 打印机绑定异常
     */
    PRINTER_BIND_ERROR("PRINTER_BIND_ERROR", "打印机绑定异常"),

    /**
     * 打印机已存在
     */
    PRINTER_ALREADY_EXIST("PRINTER_ALREADY_EXIST", "打印机已存在"),

    /**
     * 打印机已被其它企业绑定
     */
    PRINTER_ALREADY_BIND("PRINTER_ALREADY_BIND", "打印机已被其它企业绑定"),

    /**
     * 打印机已绑定场地
     */
    PRINTER_ALREADY_BIND_LOCATION("PRINTER_ALREADY_BIND_LOCATION", "打印机已绑定场地"),

    /**
     *风控校验失败
     */
    RISK_CHECK_FAILED("RISK_CHECK_FAILED", "风控校验失败"),

    /**
     * 企业id为空
     */
    CORP_ID_IS_NULL("CORP_ID_IS_NULL", "企业id为空"),
    /**
     * 场地id为空
     */
    LOCATION_ID_IS_NULL("LOCATION_ID_IS_NULL", "场地id为空"),

    /**
     * cpCode为空
     */
    CP_CODE_IS_NULL("CP_CODE_IS_NULL", "cpCode为空"),

    /**
     * 月结账号为空
     */
    WAYBILL_ACCOUNT_NO_IS_NULL("WAYBILL_ACCOUNT_NO_IS_NULL", "月结账号为空"),
    /**
     * 快递产品类型为空
     */
    PRODUCT_IS_NULL("PRODUCT_IS_NULL", "产品类型为空"),

    /**
     * 物品类型为空
     */
    ITEM_IS_NULL("ITEM_IS_NULL", "物品类型为空"),

    /**
     * 结算类型
     */
    BUSINESS_TYPE_IS_NULL("BUSINESS_TYPE_IS_NULL", "结算类型为空"),
    /**
     * 收货人手机号或电话为空
     */
    CONSIGNEE_PHONE_OR_MOBILE_IS_NULL("CONSIGNEE_PHONE_OR_MOBILE_IS_NULL", "收货人手机号或电话为空"),

    /**
     * 收件人地址为空
     */
    CONSIGNEE_ADDRESS_IS_NULL("CONSIGNEE_ADDRESS_IS_NULL", "收件人地址为空"),

    /**
     * 收件人地址code为空
     */
    CONSIGNEE_ADDRESS_CODE_IS_NULL("CONSIGNEE_ADDRESS_CODE_IS_NULL", "收件人地址code为空"),

    /**
     * 寄件人手机和电话都为空
     */
    SENDER_PHONE_OR_MOBILE_IS_NULL("SENDER_PHONE_OR_MOBILE_IS_NULL", "寄件人手机和电话都为空"),

    /**
     * 寄件人地址为空
     */
    SENDER_ADDRESS_IS_NULL("SENDER_ADDRESS_IS_NULL", "寄件人地址为空"),

    /**
     * 寄件人地址code为空
     */
    SENDER_ADDRESS_CODE_IS_NULL("SENDER_ADDRESS_CODE_IS_NULL", "寄件人地址code为空"),

    /**
     * 下单人不可为空
     */
    OPERATOR_ID_IS_NULL("OPERATOR_ID_IS_NULL", "下单人不可为空"),

    /**
     * 下单人不存在
     */
    OPERATOR_NOT_EXIT("OPERATOR_NOT_EXIT", "下单人不存在"),

    /**
     * 地址详情乱码或者包含中文字符过少
     */
    ADDRESS_DETAIL_ERROR("ADDRESS_DETAIL_ERROR", "地址详情乱码或者包含中文字符过少"),

    /**
     * 详情地址校验异常
     */
    ADDRESS_DETAIL_CHECK_ERROR("ADDRESS_DETAIL_CHECK_ERROR", "详情地址校验异常"),

    /**
     * 当前地址快递无法正常揽件或者派件
     */
    ORDER_UNREACHABLE("ORDER_UNREACHABLE", "当前地址快递无法正常揽件或者派件"),

    /**
     * 订单不存在
     */
    ORDER_NOT_EXIST("ORDER_NOT_EXIST", "订单不存在"),

    /**
     * 模版推送agentId不存在
     */
    QUERY_AGENTID_FAILED("QUERY_AGENTID_FAILED", "模版推送agentId不存在"),

    /**
     * 该小邮局已存在
     */
    POST_ALREADY_EXIST("POST_ALREADY_EXIST", "该小邮局已存在"),

    /**
     * 该小邮局不存在
     */
    POST_NOT_EXIST("POST_NOT_EXIST", "该小邮局不存在"),

    /**
     * 小邮局绑定错误
     */
    POST_BIND_ERROR("POST_BIND_ERROR", "小邮局绑定错误"),

    /**
     * 该小邮局不存在
     */
    DEL_USER_ERROR("DEL_USER_ERROR", "删除用户异常"),

    /**
     * 打印面单异常
     */
    PRINT_EXPRESS_ERROR("PRINT_EXPRESS_ERROR", "打印面单异常"),

    /**
     * 用户不存在
     */
    USER_NOT_EXIST("USER_NOT_EXIST", "用户不存在"),

    /**
     * 登陆态用户ID缺失
     */
    LOGIN_CORP_ID_MISSING("LOGIN_CORP_ID_MISSING", "登陆态企业ID缺失"),

    /**
     * 场地不存在
     */
    PRINTER_NOT_EXIST("PRINTER_NOT_EXIST", "打印机不存在"),
    ;


    final String code;
    final String describe;

    EnterpriseErrorEnum(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    @Override
    public String code() {
        return code;
    }

    @Override
    public String describe() {
        return describe;
    }


}
