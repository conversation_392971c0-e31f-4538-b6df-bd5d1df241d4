<?xml version="1.0" encoding="UTF-8"?>
<config>
    <group name="tddl">
        <property name="tddl.app" defaultValue="CAINIAO_WAYBILL_MAGNETO_APP" description="tddl中配置的应用名"/>
        <property name="tddl.sharding" defaultValue="false" description="TDataSource是否拆分"/>
    </group>
    <group name="hsf">
        <property name="hsf.group" defaultValue="HSF" description="hsf服务所属的group"/>
        <property name="hsf.version" defaultValue="1.0.0.daily" description="hsf服务的version"/>
        <property name="ad.spring.hsf.version" defaultValue="1.0.0.DAILY" description="广告hsf服务的version"/>
        <property name="hsf.timeout" defaultValue="2000" description="hsf服务的超时时间，单位是ms"/>
        <property name="hsf.print.version" defaultValue="1.0.0.daily" description="hsf print版本"/>
        <property name="hsf.RemoteWriteService.version" defaultValue="1.0.0.xforest" description="hsf RemoteWriteService版本号"/>
    </group>
    <group name="tair">
        <property name="rcResourceTairManager.config.id" defaultValue="market1-daily" description="rc tair配置的id"/>
        <property name="waybill.tair.configid" defaultValue="mdbcomm-daily" description="waybil tair配置的id"/>
    </group>
    <group name="app">
        <property name="app.name" defaultValue="waybill-bridge" description="应用名"/>
        <property name="env" defaultValue="daily" description="应用环境"/>
    </group>

    <group name="aksk">
        <property name="app.tea.flag" defaultValue="fb157f1fa9b8482bbc2d850363862047" description="阿里云flag" />
    </group>

    <group name="enterprise">
        <property name="enterprise.dingtalk.clientId" defaultValue="default" description="企业寄件钉钉客户端ID" />
        <property name="enterprise.dingtalk.clientSecret" defaultValue="default" description="企业寄件钉钉客户端密钥" />
    </group>

    <script>
        <generate template="application.properties"/>
        <generate template="app-config.properties"/>
    </script>
</config>
