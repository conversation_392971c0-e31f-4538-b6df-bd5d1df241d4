package com.cainiao.waybill.bridge.biz.utils.pickup;

import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.aliyun.oss.model.ObjectMetadata;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CharityExceptionEnum;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.middleware.oss.pickup.PickUpOssClientFactory;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.CommonConstants;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/4/17 下午9:59
 */
public class OssUtils {
    /**
     * birdge在oss上的bucket
     */
    private static final String BRIDGE_OSS_BUCKET = "waybill-bridge";

    /**
     * OSS文件基础url
     */
    public static final String OSS_FILE_BASE_URL = "https://" + BRIDGE_OSS_BUCKET + ".oss-cn-hangzhou.aliyuncs.com";


    public static final String NAIL_PATTERN = "image/resize,m_fixed,w_100,h_100";

    private static final String XLSX = ".xlsx";

    private static final String XLS = ".xls";

    /**
     * 默认过期时间 30分钟
     */
    public static final Long DEFAULT_EXPIRE_TIME = 30 * 60 * 1000L;

    /**
     * 上传文件到oss
     * @param file 文件
     * @param businessDir 业务文件夹
     * @param userId 用户自有文件夹
     * @return
     */
    public static String uploadBillFile(MultipartFile file,String businessDir, String userId){
        if(null == file){
            return null;
        }
        String fileOrigionName = file.getOriginalFilename();
        try {
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setObjectAcl(CannedAccessControlList.PublicRead);
            int expireDays = BridgeSwitch.BILL_FILE_EXPIRE_DAYS;
            metadata.setExpirationTime(DateUtils.addDays(new Date(), expireDays));

            InputStream in = file.getInputStream();
            //if(org.apache.commons.lang3.StringUtils.isNotBlank(userId)){
            //    fileName = userId + "_" + fileName;
            //}

            String date = com.cainiao.waybill.bridge.common.util.DateUtils.dateToStr(new Date(), com.cainiao.waybill.bridge.common.util.DateUtils.patternSimple24hh);

            String suffix = XLSX;
            if(fileOrigionName.endsWith(XLSX)){
                fileOrigionName = fileOrigionName.substring(0, fileOrigionName.length() - 5);
                suffix = XLSX;
            }else if(fileOrigionName.endsWith(XLS)){
                fileOrigionName = fileOrigionName.substring(0, fileOrigionName.length() - 4);
                suffix = XLS;
            }

            String fileName = fileOrigionName +"_" + date + suffix;
            if(StringUtils.isNotBlank(userId)){
                fileName = userId + "_" + fileName;
            }

            return OssUtils.uploadToOSS(businessDir, fileName, in, metadata);

        }catch (Exception e){
            PickUpLogUtil.error("上传文件失败:{}", fileOrigionName, e);
        }
        return null;
    }

    /**
     * 上传到oss
     * @param dir oss目录
     * @param fileName oss文件名
     * @param inputStream 文件流
     * @param metadata oss元数据
     * @return
     */
    public static String uploadToOSS(String dir, String fileName, InputStream inputStream, ObjectMetadata metadata) {
        OSSClient ossClient = PickUpOssClientFactory.getOssClient();
        if(StringUtils.isNotBlank(dir)){
            fileName = dir + "/" + fileName;
        }
        ossClient.putObject(BRIDGE_OSS_BUCKET, fileName, inputStream, metadata);
        String url = OSS_FILE_BASE_URL + "/" + fileName;
        PickUpLogUtil.info("上传文件成功:{}", url);
        return url;
    }

    /**
     * 上传到oss
     */
    public static String uploadToOSS(String fileName, InputStream inputStream, ObjectMetadata metadata) {
        return uploadToOSS(null, fileName, inputStream, metadata);
    }

    /**
     * 构建oss文件名
     * @param fileOriginalName
     * @param loginUserId
     * @return
     */
    public static String buildFileName(String fileOriginalName, Long loginUserId) {
        String date = com.cainiao.waybill.bridge.common.util.DateUtils.dateToStr(new Date(), com.cainiao.waybill.bridge.common.util.DateUtils.patternSimple24hh);

        String suffix = XLSX;
        if(fileOriginalName.endsWith(XLSX)){
            fileOriginalName = fileOriginalName.substring(0, fileOriginalName.length() - 5);
            suffix = XLSX;
        }else if(fileOriginalName.endsWith(XLS)){
            fileOriginalName = fileOriginalName.substring(0, fileOriginalName.length() - 4);
            suffix = XLS;
        }
        if(null != loginUserId){
            return loginUserId + "_" + fileOriginalName +"_" + date + suffix;
        }

        return fileOriginalName +"_" + date + suffix;
    }

    /**
     * 生成oss图片链接
     * @param fileName
     * @param nail
     * @param expireDays
     * @return
     */
    public static String generateOssPic(String fileName, boolean nail, int expireDays) {
        try {
            if(StringUtils.isBlank(fileName)){
                return null;
            }

            // 创建OSSClient实例。
            OSSClient ossClient = PickUpOssClientFactory.getOssClient();

            Date expiration = new Date((new Date()).getTime() + 1000L * 60 * 60 * 24 * expireDays);
            GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(BRIDGE_OSS_BUCKET, fileName, HttpMethod.GET);
            req.setExpiration(expiration);
            if (nail) {
                //?x-oss-process=image/resize,m_fixed,w_100,h_100
                req.setProcess(NAIL_PATTERN);
            }
            URL signedUrl = ossClient.generatePresignedUrl(req);
            return signedUrl.toString();

        } catch (OSSException e) {
            throw new BridgeBusinessException(CharityExceptionEnum.GENERATE_PKG_IMAGE_URL_FAIL.getErrorCode(),
                e.getMessage());
        } catch (Exception e) {
            throw new BridgeBusinessException(CharityExceptionEnum.SYSTEM_ERROR.getErrorCode(), e.getMessage());
        }
    }

    public static String generateOssPic(String fileName, boolean nail) {
        return generateOssPic(fileName, nail, 1);
    }

    /**
     * 生成带过期时间的文件下载链接
     * @param fileName 文件名，不含域名的文件路径
     * @param expireTime 下载过期时间，单位毫秒，默认30分钟
     * @return
     * @throws BridgeBusinessException
     */
    public static String generateExpireUrl(String fileName, Long expireTime) throws BridgeBusinessException {

        try {

            if(StringUtils.isBlank(fileName)){
                throw new BridgeBusinessException("FILE_NAME_IS_NULL", "文件名为空");
            }
            if(fileName.startsWith(OSS_FILE_BASE_URL)){
                fileName = fileName.substring(OSS_FILE_BASE_URL.length());
            }
            if(fileName.startsWith(CommonConstants.XG)){
                fileName = fileName.substring(1);
            }
            if(expireTime == null || expireTime <= 0){
                expireTime = DEFAULT_EXPIRE_TIME;
            }
            OSSClient ossClient = PickUpOssClientFactory.getOssClient();
            Date expiration = new Date((new Date()).getTime() + expireTime);
            URL url = ossClient.generatePresignedUrl(BRIDGE_OSS_BUCKET, fileName, expiration);
            if (url != null) {
                return url.toString();
            }
        } catch (OSSException e) {
            PickUpLogUtil.error("generateExpireUrlOssError", e);
            throw new BridgeBusinessException("OSS_FILE_ERROR", e.getMessage());
        } catch (Exception e) {
            PickUpLogUtil.error("generateExpireUrlError", e);
            throw new BridgeBusinessException("SYSTEM_ERROR", e.getMessage());
        }
        throw new BridgeBusinessException("GENERATE_URL_ERROR", "生成下载链接错误");
    }

    /**
     * 下载文件
     * @param fileUrl 文件下载完整路径 包含域名
     * @return
     * @throws IOException
     */
    public static InputStream downloadFileIs(String fileUrl) throws BridgeBusinessException{
        try {
            URL url = new URL(fileUrl);
            HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
            httpConn.setRequestMethod("GET");

            // 检查响应代码
            int responseCode = httpConn.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 输入流
                return new BufferedInputStream(httpConn.getInputStream());
            }
            httpConn.disconnect();
            return null;

        }catch (IOException e){
            PickUpLogUtil.error("downloadFileIsError", e);
            throw new BridgeBusinessException("DOWN_LOAD_ERROR", e.getMessage());
        }
    }

}
