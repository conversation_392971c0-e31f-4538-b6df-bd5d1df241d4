package com.cainiao.waybill.bridge.web.common.util;

import com.alibaba.buc.acl.api.input.check.CheckPermissionsParam;
import com.alibaba.buc.acl.api.output.check.CheckPermissionsResult;
import com.alibaba.buc.acl.api.service.AccessControlService;
import com.alibaba.buc.api.model.SimpleUser;
import com.alibaba.buc.api.ucweb.model.ResultAsRpc;
import com.alibaba.buc.api.unit.HavanaQueryReadService;
import com.alibaba.buc.sso.client.util.SimpleUserUtil;
import com.alibaba.buc.sso.client.vo.BucSSOUser;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.google.common.collect.Lists;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/23-下午8:56
 */
@Component
public class AclPermissionHelper {

    private static final String ACL_ACCESS_KEY = "waybill-bridge-2Vz02A155Evymyy";
    @Resource
    private AccessControlService accessControlService;

    @Resource
    private HavanaQueryReadService havanaQueryReadService;

    public boolean judgeOperatorPermission(String permissionName, HttpServletRequest httpServletRequest) {
        try {
            BucSSOUser user = SimpleUserUtil.getBucSSOUser(httpServletRequest);
            if (user == null || StringUtil.isBlank(user.getEmpId())) {
                return false;
            }
            ResultAsRpc<SimpleUser> simpleUserResult = havanaQueryReadService.getSimpleUserByEmpId(user.getEmpId());
            PickUpLogUtil.info("simpleUserResult : " + JSON.toJSONString(simpleUserResult));
            if (simpleUserResult == null || !simpleUserResult.isSuccess() || simpleUserResult.getContent() == null || simpleUserResult.getContent().getUserId() == null) {
                return false;
            }
            Integer userId = simpleUserResult.getContent().getUserId();
            CheckPermissionsParam checkPermissionsParam = new CheckPermissionsParam();
            checkPermissionsParam.setAccessKey(ACL_ACCESS_KEY);
            checkPermissionsParam.setUserId(userId);
            checkPermissionsParam.setPermissionNames(Lists.newArrayList(permissionName));
            CheckPermissionsResult checkPermissionsResult = accessControlService.checkPermissions(checkPermissionsParam);
            PickUpLogUtil.info("checkPermissions : " + JSON.toJSONString(checkPermissionsResult));
            if (checkPermissionsResult == null || !checkPermissionsResult.isSuccess()) {
                return false;
            }
            List<CheckPermissionsResult.CheckPermissionResultInner> checkPermissionResults = checkPermissionsResult.getCheckPermissionResults();
            for (CheckPermissionsResult.CheckPermissionResultInner checkPermissionResult : checkPermissionResults) {
                if (permissionName.equals(checkPermissionResult.getPermissionName())) {
                    return checkPermissionResult.isAccessible();
                }
            }
            return false;
        } catch (Throwable e) {
            PickUpLogUtil.info("judgeOperatorPermission error : " + ExceptionUtils.getStackTrace(e));
            return false;
        }
    }
}
