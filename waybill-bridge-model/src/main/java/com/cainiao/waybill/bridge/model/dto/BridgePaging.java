package com.cainiao.waybill.bridge.model.dto;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> zouping.fzp
 * @Classname CharityPagingDTO
 * @Description
 * @Date 2022/8/29 3:57 下午
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BridgePaging implements Serializable {

    private static final long serialVersionUID = -1122375449568956393L;

    /**
     * 第几页
     */
    private Integer currentPage;
    /**
     * 每页大小
     */
    private Integer pageSize;
    /**
     * 总条数
     */
    private Integer totalCount;

    public int getTotalPage() {
        return totalCount % pageSize == 0 ? totalCount / pageSize : (totalCount / pageSize + 1);
    }
}
