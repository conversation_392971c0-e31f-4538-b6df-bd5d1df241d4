package com.cainiao.waybill.bridge.client2;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 分页信息
 * <AUTHOR>
 * @Date 2024/3/19 14:43
 */
@Data
public class PagingInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 第几页
     */
    private Integer currentPage;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总条数
     */
    private Integer totalCount;

    public int getTotalPage() {
        return totalCount % pageSize == 0 ? totalCount / pageSize : (totalCount / pageSize + 1);
    }
}
