package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
/**
 *
 * <AUTHOR>
 */
public class WaybillInsurancePolicyDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   保险产品编号
     *
     *
     * @mbg.generated
     */
    private String productCode;

    /**
     * Database Column Remarks:
     *   平台编号
     *
     *
     * @mbg.generated
     */
    private String platformCode;

    /**
     * Database Column Remarks:
     *   电商平台交易ID
     *
     *
     * @mbg.generated
     */
    private String orderId;

    /**
     * Database Column Remarks:
     *   电商平台卖家ID
     *
     *
     * @mbg.generated
     */
    private String sellerId;

    /**
     * Database Column Remarks:
     *   电商平台买家ID
     *
     *
     * @mbg.generated
     */
    private String buyerId;

    /**
     * Database Column Remarks:
     *   卖家证件号
     *
     *
     * @mbg.generated
     */
    private String sellerCertNo;

    /**
     * Database Column Remarks:
     *   买家证件号
     *
     *
     * @mbg.generated
     */
    private String buyerCertNo;

    /**
     * Database Column Remarks:
     *   下单时间
     *
     *
     * @mbg.generated
     */
    private Date orderTime;

    /**
     * Database Column Remarks:
     *   支付时间
     *
     *
     * @mbg.generated
     */
    private Date payTime;

    /**
     * Database Column Remarks:
     *   支付金额，单位分
     *
     *
     * @mbg.generated
     */
    private Integer amount;

    /**
     * Database Column Remarks:
     *   实际支付金额，单位分
     *
     *
     * @mbg.generated
     */
    private Integer payAmount;

    /**
     * Database Column Remarks:
     *   物流信息，Json格式
     *
     *
     * @mbg.generated
     */
    private String logisticsInfo;

    /**
     * Database Column Remarks:
     *   商品信息列表，Json格式 list
     *
     *
     * @mbg.generated
     */
    private String productInfos;

    /**
     保单号
     */
    private String policyNo;

    /**
     生效时间
     */
    private Date effectiveDate;

    /**
     过期时间
     */
    private Date expiryDate;

    /**
     投保时间
     */
    private Date  insureDate;

    /**
     保费
     */
    private Integer premium;

    /**
     保额
     */
    private Integer sumInsured;

    /**
     * 投保受理时间
     */
    private Date insureAcceptDate;

    public Date getInsureAcceptDate() {
        return insureAcceptDate;
    }

    public void setInsureAcceptDate(Date insureAcceptDate) {
        this.insureAcceptDate = insureAcceptDate;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public Date getInsureDate() {
        return insureDate;
    }

    public void setInsureDate(Date insureDate) {
        this.insureDate = insureDate;
    }

    public Integer getPremium() {
        return premium;
    }

    public void setPremium(Integer premium) {
        this.premium = premium;
    }

    public Integer getSumInsured() {
        return sumInsured;
    }

    public void setSumInsured(Integer sumInsured) {
        this.sumInsured = sumInsured;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_insurance_policy.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_insurance_policy.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_insurance_policy.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.product_code
     *
     * @mbg.generated
     */
    public String getProductCode() {
        return productCode;
    }

    /**
     *
     * @param productCode the value for waybill_insurance_policy.product_code
     *
     * @mbg.generated
     */
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.platform_code
     *
     * @mbg.generated
     */
    public String getPlatformCode() {
        return platformCode;
    }

    /**
     *
     * @param platformCode the value for waybill_insurance_policy.platform_code
     *
     * @mbg.generated
     */
    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.order_id
     *
     * @mbg.generated
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @param orderId the value for waybill_insurance_policy.order_id
     *
     * @mbg.generated
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.seller_id
     *
     * @mbg.generated
     */
    public String getSellerId() {
        return sellerId;
    }

    /**
     *
     * @param sellerId the value for waybill_insurance_policy.seller_id
     *
     * @mbg.generated
     */
    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.buyer_id
     *
     * @mbg.generated
     */
    public String getBuyerId() {
        return buyerId;
    }

    /**
     *
     * @param buyerId the value for waybill_insurance_policy.buyer_id
     *
     * @mbg.generated
     */
    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.seller_cert_no
     *
     * @mbg.generated
     */
    public String getSellerCertNo() {
        return sellerCertNo;
    }

    /**
     *
     * @param sellerCertNo the value for waybill_insurance_policy.seller_cert_no
     *
     * @mbg.generated
     */
    public void setSellerCertNo(String sellerCertNo) {
        this.sellerCertNo = sellerCertNo;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.buyer_cert_no
     *
     * @mbg.generated
     */
    public String getBuyerCertNo() {
        return buyerCertNo;
    }

    /**
     *
     * @param buyerCertNo the value for waybill_insurance_policy.buyer_cert_no
     *
     * @mbg.generated
     */
    public void setBuyerCertNo(String buyerCertNo) {
        this.buyerCertNo = buyerCertNo;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.order_time
     *
     * @mbg.generated
     */
    public Date getOrderTime() {
        return orderTime;
    }

    /**
     *
     * @param orderTime the value for waybill_insurance_policy.order_time
     *
     * @mbg.generated
     */
    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.pay_time
     *
     * @mbg.generated
     */
    public Date getPayTime() {
        return payTime;
    }

    /**
     *
     * @param payTime the value for waybill_insurance_policy.pay_time
     *
     * @mbg.generated
     */
    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.amount
     *
     * @mbg.generated
     */
    public Integer getAmount() {
        return amount;
    }

    /**
     *
     * @param amount the value for waybill_insurance_policy.amount
     *
     * @mbg.generated
     */
    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.pay_amount
     *
     * @mbg.generated
     */
    public Integer getPayAmount() {
        return payAmount;
    }

    /**
     *
     * @param payAmount the value for waybill_insurance_policy.pay_amount
     *
     * @mbg.generated
     */
    public void setPayAmount(Integer payAmount) {
        this.payAmount = payAmount;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.logistics_info
     *
     * @mbg.generated
     */
    public String getLogisticsInfo() {
        return logisticsInfo;
    }

    /**
     *
     * @param logisticsInfo the value for waybill_insurance_policy.logistics_info
     *
     * @mbg.generated
     */
    public void setLogisticsInfo(String logisticsInfo) {
        this.logisticsInfo = logisticsInfo;
    }

    /**
     *
     * @return the value of waybill_insurance_policy.product_infos
     *
     * @mbg.generated
     */
    public String getProductInfos() {
        return productInfos;
    }

    /**
     *
     * @param productInfos the value for waybill_insurance_policy.product_infos
     *
     * @mbg.generated
     */
    public void setProductInfos(String productInfos) {
        this.productInfos = productInfos;
    }

    @Override
    public String toString() {
        return "WaybillInsurancePolicyDO{" +
                "id=" + id +
                ", gmtCreate=" + gmtCreate +
                ", gmtModified=" + gmtModified +
                ", productCode='" + productCode + '\'' +
                ", platformCode='" + platformCode + '\'' +
                ", orderId='" + orderId + '\'' +
                ", sellerId='" + sellerId + '\'' +
                ", buyerId='" + buyerId + '\'' +
                ", sellerCertNo='" + sellerCertNo + '\'' +
                ", buyerCertNo='" + buyerCertNo + '\'' +
                ", orderTime=" + orderTime +
                ", payTime=" + payTime +
                ", amount=" + amount +
                ", payAmount=" + payAmount +
                ", logisticsInfo='" + logisticsInfo + '\'' +
                ", productInfos='" + productInfos + '\'' +
                ", policyNo='" + policyNo + '\'' +
                ", effectiveDate=" + effectiveDate +
                ", expiryDate=" + expiryDate +
                ", insureDate=" + insureDate +
                ", premium=" + premium +
                ", sumInsured=" + sumInsured +
                ", insureAcceptDate=" + insureAcceptDate +
                '}';
    }
}