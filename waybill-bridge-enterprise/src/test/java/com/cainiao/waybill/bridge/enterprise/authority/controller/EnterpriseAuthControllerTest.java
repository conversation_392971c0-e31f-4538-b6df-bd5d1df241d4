package com.cainiao.waybill.bridge.enterprise.authority.controller;

import com.cainiao.waybill.bridge.enterprise.authority.request.LoginRequest;
import com.cainiao.waybill.bridge.enterprise.authority.response.LoginResponse;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.google.gson.Gson;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EnterpriseAuthControllerTest {
    @InjectMocks
    private EnterpriseAuthController enterpriseAuthController;
    @Mock
    private DingTalkAuthService dingTalkAuthService;
    @Mock
    private HttpServletResponse response;
    @Mock
    private PrintWriter printWriter;
    private LoginRequest validRequest;
    private LoginRequest errorRequest;

    @Before
    public void setUp() throws Exception {
        validRequest = new LoginRequest();
        validRequest.setUserId("user123");
        validRequest.setCorpId("corp456");
        validRequest.setSalt("salt789");
        errorRequest = new LoginRequest();
        errorRequest.setUserId("errorUser");
        errorRequest.setCorpId("errorCorp");
        errorRequest.setSalt("errorSalt");
        when(response.getWriter()).thenReturn(printWriter);
    }

    /**
     * 正常情况下的登录操作
     */
    @Test
    public void testLoginSuccess() throws Exception {
        String sessionId = "mockSessionId";
        when(dingTalkAuthService.createSession(anyString(), anyString(), anyString())).thenReturn(sessionId);
        enterpriseAuthController.login(validRequest, response);
        verify(response).setStatus(HttpServletResponse.SC_OK);
        verify(printWriter).write(new Gson().toJson(EnterpriseBaseResult.success(any(LoginResponse.class))));
    }

    /**
     * 当出现业务异常时的处理
     */
    @Test
    public void testLoginBusinessException() throws Exception {
        doThrow(new EnterpriseException("Error Code", "Business Error")).when(dingTalkAuthService).createSession(anyString(), anyString(), anyString());
        enterpriseAuthController.login(errorRequest, response);
        verify(printWriter).write(new Gson().toJson(EnterpriseBaseResult.bizFail("Error Code", "Business Error")));
    }

    /**
     * 当系统抛出异常时的处理
     */
    @Test
    public void testLoginSystemException() throws Exception {
        doThrow(new RuntimeException("System error")).when(dingTalkAuthService).createSession(anyString(), anyString(), anyString());
        enterpriseAuthController.login(errorRequest, response);
        verify(printWriter).write(new Gson().toJson(EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "System error")));
    }
}