package com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 平台维度监控报表中一行主记录
 *
 * <AUTHOR>
 * @date 2021/11/17-下午8:08
 */
@Data
public class PlatformMainItem implements Serializable {
    private static final long serialVersionUID = 1L;

    private String dateRange;
    private String platformName;
    private ReportItem platformReport;
    List<PlatformSubCpItem> cpItemList;
}
