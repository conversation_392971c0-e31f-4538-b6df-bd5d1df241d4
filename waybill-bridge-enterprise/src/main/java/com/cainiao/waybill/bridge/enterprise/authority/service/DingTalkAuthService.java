package com.cainiao.waybill.bridge.enterprise.authority.service;

import com.cainiao.waybill.bridge.enterprise.authority.response.AuthInfoResponse;

/**
 * 权限服务
 *
 * <AUTHOR>
 */
public interface DingTalkAuthService {

    /**
     * 生成权限Code
     *
     * @param platform
     * @param authCorpId
     * @return
     */
    String getAccessToken(String platform, String authCorpId);

    /**
     * 生成权限Code
     *
     * @param platform
     * @param authCorpId
     * @return
     */
    String getAccessTokenAppKey(String platform, String authCorpId);

    /**
     * 生成权限Code-新版本根据suitTicket
     *
     * @param platform
     * @param authCorpId
     * @return
     */
    String getCorpAccessToken(String platform, String authCorpId);

    /**
     * 生成权限Code
     *
     * @param authCode
     * @return
     */
    String getUserToken(String authCode, String corpId);

    /**
     * 获取钉钉URL
     *
     * <p>根据请求类型获取相应的钉钉URL。
     *
     * @param requestType 请求类型
     * @return 相应的钉钉URL
     */
    public String getDingDIngUrl(String requestType);


    /**
     *
     * 创建会话
     *
     * @param userId 请求类型
     * @param salt  盐
     * @param corpId  企业ID
     * @return 会话ID
     */
    public String createSession(String corpId, String userId, String salt);

    /**
     *
     * 创建会话
     *
     * @param userId 请求类型
     * @return 会话ID
     */
    public String getSession(String userId);

    /**
     *
     *  删除缓存
     *
     * @param userId 请求类型
     * @return 会话ID
     */
    public String delSession(String userId);

    /**
     *
     * 创建会话
     *
     * @param userId 请求类型
     * @return 会话ID
     */
    public Boolean cleanSession(String userId);

    /**
     *
     * 检查回话
     *
     * @param userId 请求类型
     * @param sessionId 会话ID
     * @return 会话ID
     */
    public Boolean checkSession(String userId, String sessionId);


    /**
     *
     * 获取企业授权信息
     *
     * @param corpId 企业Id
     * @return 企业授权信息
     */
    public AuthInfoResponse getEnterpriseAuth(String platform, String corpId);

    /**
     *
     * 新增企业入驻
     *
     * @param corpId 企业Id
     * @return 企业授权信息
     */
    public boolean addEnterpriseAuth(String platform, String corpId);

}
