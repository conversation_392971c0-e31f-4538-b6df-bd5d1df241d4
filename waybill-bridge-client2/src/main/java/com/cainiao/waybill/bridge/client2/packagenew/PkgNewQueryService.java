package com.cainiao.waybill.bridge.client2.packagenew;


import com.cainiao.waybill.bridge.client2.packagenew.response.WeiXinPkgLogisticAccordResponse;

import java.util.List;

/**
 * 包裹新发现微信小程序查询接口
 *
 * <AUTHOR>
 */
public interface PkgNewQueryService {

    /**
     * 包裹新发现  按照物流状态对包裹进行分类筛选 返回符合节点要求的列表
     *
     * @param mailNoCpCodeList   包裹单号和cpCode列表，格式YT6123456738990:YTO，cpCode可以不填写只填运单号YT6123456738990
     * @param logisticStatusDesc 物流状态描述 可传递枚举类LogisticStatusDescEnums.xxx.getStatusDesc() 当该字段不传时默认查询全部的物流节点
     * @return
     */
    List<WeiXinPkgLogisticAccordResponse> batchQueryWaybillLogisticsDetailList(List<String> mailNoCpCodeList, String logisticStatusDesc);

}
