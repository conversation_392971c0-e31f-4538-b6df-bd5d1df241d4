package com.cainiao.waybill.bridge.web.pickup.ticket.role;

import java.util.Arrays;
import java.util.Map;

import com.cainiao.waybill.bridge.biz.pickup.constants.BridgeUserFeatureKey;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpBaseRequest;
import com.cainiao.waybill.common.util.FeatureUtils;
import org.apache.commons.lang3.StringUtils;
import org.testng.collections.Lists;

/**
 * <AUTHOR> zouping.fzp
 * @Classname LoginUserUtil
 * @Description
 * @Date 2022/8/29 11:43 上午
 * @Version 1.0
 */
public class PickUpLoginUserUtil {


    private static final ThreadLocal<PickUpBaseRequest> LOGIN_REQ = new ThreadLocal<>();

    public static void buildBaseReq(Long loginUserId, String account, String scene, String role, String feature) {
        PickUpBaseRequest request = new PickUpBaseRequest();
        request.setLoginUserId(loginUserId);
        request.setScene(scene);
        request.setRole(role);
        request.setAccount(account);
        request.setFeature(feature);
        Map<String, String> map = FeatureUtils.parseFromString(feature);
        String userSource = map.get(BridgeUserFeatureKey.userSource);
        String agent = map.get(BridgeUserFeatureKey.agent);
        if(StringUtils.isNotBlank(userSource)){
            request.setUserSource(Arrays.asList(StringUtils.split(userSource, ",")));
        }
        if(StringUtils.isNotBlank(agent)){
            request.setUserAgent(agent);
        }
        LOGIN_REQ.set(request);
    }

    private static PickUpBaseRequest getBaseReq(){
        return LOGIN_REQ.get();
    }

    public static void clear(){
        LOGIN_REQ.remove();
    }

    public static <T extends PickUpBaseRequest> void popLoginInfo(T params){
        PickUpBaseRequest baseRequest = getBaseReq();
        params.setScene(baseRequest.getScene());
        params.setLoginUserId(baseRequest.getLoginUserId());
        params.setRole(baseRequest.getRole());
        params.setAccount(baseRequest.getAccount());
        params.setFeature(baseRequest.getFeature());
        params.setUserSource(baseRequest.getUserSource());
        params.setUserAgent(baseRequest.getUserAgent());
    }
}
