<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>com.taobao</groupId>
        <artifactId>parent</artifactId>
        <version>2.0.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.cainiao.waybill</groupId>
    <artifactId>waybill-bridge</artifactId>
    <packaging>pom</packaging>
    <version>${waybill-bridge.version}</version>
    <name>waybill-bridge</name>

    <properties>
        <waybill-bridge.version>3.0.9</waybill-bridge.version>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <mockito-all.version>1.10.19</mockito-all.version>
        <mybatis-starter.version>1.1.1</mybatis-starter.version>
        <spring-boot.version>1.5.19.RELEASE</spring-boot.version>
        <pandora-boot.version>2021-07-release</pandora-boot.version>
        <spring-test.version>4.3.2.RELEASE</spring-test.version>
        <junit.version>4.12</junit.version>
        <maven-antrun.version>1.8</maven-antrun.version>
        <pandora-boot-maven-plugin.version>10.0.1</pandora-boot-maven-plugin.version>
        <autoconfig-maven-plugin.version>1.2-fixcompress-SNAPSHOT</autoconfig-maven-plugin.version>
        <waybill-shared.version>2.14.86-privacy-SNAPSHOT</waybill-shared.version>
        <bearlake.version>1.2.9</bearlake.version>
        <data-service-client.version>1.1.3</data-service-client.version>
        <dipan-engine-api.version>2.0.6</dipan-engine-api.version>
        <hsf-app-spring-version>*******.2</hsf-app-spring-version>
        <hsf-feature-context-version>3.0.9</hsf-feature-context-version>
        <diamond-client-version>3.8.18</diamond-client-version>
        <poi.version>3.17</poi.version>
        <oss.version>3.17.4</oss.version>
        <pac.client.version>1.0.43-400849</pac.client.version>
        <pac.api.version>1.7.8-link</pac.api.version>
        <tdtradeplatform.version>20240402-RELEASE</tdtradeplatform.version>
        <tdtradeplatform2.client.version>20240125-RELEASE</tdtradeplatform2.client.version>
        <tdtradeplatform.open.client>20240125-RELEASE</tdtradeplatform.open.client>
        <nbp.client.version>1.0.5</nbp.client.version>
        <lombok.version>1.16.20</lombok.version>
        <org.mapstruct.version>1.2.0.Final</org.mapstruct.version>
        <log4j.version>2.17.1</log4j.version>
        <cnmember.client.version>3.2.66</cnmember.client.version>
    </properties>

    <modules>
        <module>waybill-bridge-client2</module>
        <module>waybill-bridge-client</module>
        <module>waybill-bridge-common</module>
        <module>waybill-bridge-model</module>
        <module>waybill-bridge-biz</module>
        <module>waybill-bridge-start</module>
        <module>waybill-bridge-web</module>
        <module>waybill-bridge-aliSocialWork</module>
        <module>waybill-bridge-innerSupport</module>
        <module>waybill-bridge-enterprise</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <!--FBI报表-->
            <dependency>
                <groupId>com.alibaba.da</groupId>
                <artifactId>da-infra-api</artifactId>
                <version>1.2.33</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${oss.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aliyun</groupId>
                        <artifactId>aliyun-java-sdk-kms</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>servlet-api</artifactId>
                <version>99.0-does-not-exist</version>
            </dependency>

            <dependency>
                <groupId>servlet-api</groupId>
                <artifactId>servlet-api</artifactId>
                <version>999-no-exist-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>999-not-exist-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>servlet-api</artifactId>
                <version>999-not-exist-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.mortbay.jetty</groupId>
                <artifactId>servlet-api-2.5</artifactId>
                <version>999-not-exist</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.dataworks</groupId>
                <artifactId>data-service-client</artifactId>
                <version>${data-service-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.deepclone</groupId>
                        <artifactId>deep-clone</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.common.division</groupId>
                <artifactId>common-division-plus</artifactId>
                <version>4.2.7</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.tair</groupId>
                        <artifactId>tair-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-bridge-biz</artifactId>
                <version>${waybill-bridge.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-bridge-client</artifactId>
                <version>${waybill-bridge.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-bridge-client2</artifactId>
                <version>${waybill-bridge.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-bridge-common</artifactId>
                <version>${waybill-bridge.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-bridge-model</artifactId>
                <version>${waybill-bridge.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-bridge-start</artifactId>
                <version>${waybill-bridge.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-bridge-web</artifactId>
                <version>${waybill-bridge.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-bridge-aliSocialWork</artifactId>
                <version>${waybill-bridge.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-bridge-innerSupport</artifactId>
                <version>${waybill-bridge.version}</version>
            </dependency>

<!--            企业寄件-->
            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-bridge-enterprise</artifactId>
                <version>${waybill-bridge.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.schedulerx</groupId>
                <artifactId>schedulerx-worker</artifactId>
                <version>1.2.8.1</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.taobao.pandora</groupId>
                <artifactId>pandora-boot-starter-bom</artifactId>
                <version>2024-04-release-fix-fastjson-v2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>pandora-switchcenter-spring-boot-starter</artifactId>
                <version>${pandora-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring-test.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.security</groupId>
                <artifactId>security-spring-dependencies</artifactId>
                <version>1.2.5-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!-- <dependency>
                 <groupId>org.mybatis.spring.boot</groupId>
                 <artifactId>mybatis-spring-boot-starter</artifactId>
                 <version>${mybatis-starter.version}</version>
             </dependency>-->


            <dependency>
                <groupId>com.alibaba.fastvalidator</groupId>
                <artifactId>fastvalidator-spring-boot-starter</artifactId>
                <version>2.6.2.8</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.cnlogin</groupId>
                <artifactId>cnlogin-client</artifactId>
                <version>1.2.5</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.cnuser</groupId>
                <artifactId>cnuser-client</artifactId>
                <version>2.2.7</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>1.1.0.Final</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.common.dao</groupId>
                <artifactId>common-dao</artifactId>
                <version>1.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.common.lang</groupId>
                <artifactId>toolkit-common-lang</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.common.logging</groupId>
                <artifactId>toolkit-common-logging</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.external</groupId>
                <artifactId>jakarta.commons.logging</artifactId>
                <version>0.0.0</version>
            </dependency>
            <!--  apache   -->
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>1.7.22</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.tpn</groupId>
                <artifactId>tpn-client</artifactId>
                <version>1.4.7</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.test</groupId>
                <artifactId>itest</artifactId>
                <version>1.4.2-SPRING4-SNAPSHOT</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>31.1-jre</version>
            </dependency>
            <dependency>
                <artifactId>bearlake-client</artifactId>
                <groupId>com.cainiao.bearlake</groupId>
                <version>${bearlake.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cainiao.bearlake</groupId>
                <artifactId>bearlake-api</artifactId>
                <version>1.4.7</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>1.1.7</version>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-number-client</artifactId>
                <version>1.0.8</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>1.1.7</version>
            </dependency>
            <!-- 排除掉各种logger -->
            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>999-not-exist</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>999-not-exist-v3</version>
            </dependency>
            <!-- 菜鸟会员账号 -->
            <dependency>
                <groupId>com.cainiao.member.shared</groupId>
                <artifactId>cnmember-account-shared</artifactId>
                <version>${cnmember.client.version}</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>log4j-over-slf4j</artifactId>
                <version>999-not-exist-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-nop</artifactId>
                <version>999-not-exist-v3</version>
            </dependency>

            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>1.4.11</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.external</groupId>
                <artifactId>jakarta.commons.logging</artifactId>
                <version>999-not-exist-v3</version>
            </dependency>
            <!-- 引入执行python脚本依赖 start -->
            <dependency>
                <groupId>org.python</groupId>
                <artifactId>jython-standalone</artifactId>
                <version>2.7.4</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>org.jpype</groupId>-->
<!--                <artifactId>jpype</artifactId>-->
<!--                <version>0.7.5</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>net.sf.py4j</groupId>
                <artifactId>py4j</artifactId>
                <version>0.10.9.7</version>
            </dependency>
            <!-- 引入执行python脚本依赖 end-->

            <dependency>
                <groupId>com.taobao.common.division</groupId>
                <artifactId>common-division</artifactId>
                <version>3.0.28</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.cloudprint</groupId>
                <artifactId>cloudprint-client</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.cainiao.waybillprint</groupId>
                <artifactId>waybill-print-client</artifactId>
                <version>0.1.6-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-extend-privacy</artifactId>
                <version>1.1-bridge-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.mdm</groupId>
                <artifactId>cn-mdm-client</artifactId>
                <version>1.3.3</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.vipserver</groupId>
                <artifactId>vipserver-client</artifactId>
                <version>4.8.4</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.middleware.liaoyuan</groupId>
                <artifactId>liaoyuan-client</artifactId>
                <version>2.7.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.vipserver</groupId>
                        <artifactId>vipserver-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.wlb</groupId>
                <artifactId>resourcecenter-pony</artifactId>
                <version>*******</version>
                <exclusions>
                    <exclusion>
                        <artifactId>logback-classic</artifactId>
                        <groupId>ch.qos.logback</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- dts -->
            <dependency>
                <groupId>com.alibaba.schedulerx</groupId>
                <artifactId>schedulerx-client</artifactId>
                <version>2.0.14</version>
            </dependency>
            <!-- ISS -->
<!--            <dependency>-->
<!--                <groupId>com.cainiao</groupId>-->
<!--                <artifactId>ISS-client</artifactId>-->
<!--                <version>1.0.5</version>-->
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <artifactId>eagleeye-core</artifactId>-->
<!--                        <groupId>com.taobao.eagleeye</groupId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
<!--            </dependency>-->
            <!-- link -->
            <dependency>
                <groupId>com.taobao.partneraccesscenter</groupId>
                <artifactId>pac.client</artifactId>
                <version>${pac.client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>eagleeye-core</artifactId>
                        <groupId>com.taobao.eagleeye</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.metaq.final</groupId>
                        <artifactId>metaq-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.taobao.partneraccesscenter</groupId>
                <artifactId>pac.api</artifactId>
                <version>${pac.api.version}</version>
            </dependency>
            <!-- cainiao-lego -->
            <dependency>
                <groupId>com.cainiao.lego</groupId>
                <artifactId>cainiao-lego</artifactId>
                <version>1.0.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>eagleeye-core</artifactId>
                        <groupId>com.taobao.eagleeye</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 地址清洗服务 -->
            <dependency>
                <groupId>com.cainiao.address</groupId>
                <artifactId>address-api</artifactId>
                <version>1.0.9</version>
            </dependency>
            <!-- 物流订单服务 -->
            <dependency>
                <groupId>com.taobao.logistics</groupId>
                <artifactId>logistics-common-base</artifactId>
                <version>*******</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.taobao.trade.platform</groupId>
                        <artifactId>tp-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.hbase</groupId>
                        <artifactId>hbase</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hbase</groupId>
                        <artifactId>hbase-extender</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.notify</groupId>
                        <artifactId>notify-tr-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hsf</groupId>
                        <artifactId>hsf.notify.spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-fileupload</groupId>
                        <artifactId>commons-fileupload</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.common.catserver</groupId>
                        <artifactId>catserver-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.codehaus.castor</groupId>
                        <artifactId>castor</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>itemcenter-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.itemcenter</groupId>
                        <artifactId>itemcenter-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.refund</groupId>
                        <artifactId>refund-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.refund</groupId>
                        <artifactId>refund-domain</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.ump</groupId>
                        <artifactId>ump-common-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.tmall.cloud.integration</groupId>
                        <artifactId>data-mapping</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.scsku</groupId>
                        <artifactId>scsku-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.scsku</groupId>
                        <artifactId>scsku-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.tmall.pyramid</groupId>
                        <artifactId>pyramid-domain</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.tmall.pyramid</groupId>
                        <artifactId>pyramid-domain</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.ump</groupId>
                        <artifactId>ump-core-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.tair</groupId>
                        <artifactId>tair-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.tair</groupId>
                        <artifactId>tair-mc-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.mina</groupId>
                        <artifactId>mina-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.common.uic.usertag-client</groupId>
                        <artifactId>common-usertag-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.wlb</groupId>
                        <artifactId>wlb-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.diamond</groupId>
                        <artifactId>diamond-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.thoughtworks.xstream</groupId>
                        <artifactId>xstream</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>asm</groupId>
                        <artifactId>asm-commons</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>asm</groupId>
                        <artifactId>asm</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.htmlparser</groupId>
                        <artifactId>htmlparser</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>hessian</groupId>
                        <artifactId>hessian</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>joda-time</groupId>
                        <artifactId>joda-time</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.common.dao</groupId>
                        <artifactId>common-dao</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.util</groupId>
                        <artifactId>util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.toolkit.common.lang</groupId>
                        <artifactId>lang</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.common.logging</groupId>
                        <artifactId>toolkit-common-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>aspectj</groupId>
                        <artifactId>aspectjlib</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>aspectj</groupId>
                        <artifactId>aspectjrt</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>aspectj</groupId>
                        <artifactId>aspectjweaver</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--查询商品类目-->
            <dependency>
                <groupId>com.taobao.forest</groupId>
                <artifactId>forest-client</artifactId>
                <version>6.2.2.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba.platform.shared</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.cainiao.cnmd.opplatform</groupId>
                <artifactId>cnmd-op-platform-scenario-temp</artifactId>
                <version>1.1.6</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.tair</groupId>
                <artifactId>tair-mc-client</artifactId>
                <version>2.2.29</version>
            </dependency>

            <!-- test -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>${mockito-all.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.16.18</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty</artifactId>
                <version>3.9.2.Final</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.etao.igraph</groupId>
                <artifactId>igraph-client</artifactId>
                <version>0.10.36</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.cm2</groupId>
                <artifactId>cm2.subscriber</artifactId>
                <version>0.2.6</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.3</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus</artifactId>
                <version>3.4.0</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.routingservice</groupId>
                <artifactId>routingservice-client</artifactId>
                <version>1.9.64</version>
            </dependency>

            <!-- JD运力接入start -->
            <dependency>
                <groupId>com.jd.opensdk</groupId>
                <artifactId>jd.opensdk</artifactId>
                <version>1.0.28</version>
            </dependency>
            <dependency>
                <groupId>com.jd.opensdk</groupId>
                <artifactId>jd.ecapsdk</artifactId>
                <version>1.0.0</version>
            </dependency>
            <!-- JD运力接入end -->

            <!-- 裹裹24小时运力接入start -->
            <dependency>
                <groupId>com.cainiao.tdtradeplatform2</groupId>
                <artifactId>tdtradeplatform2-client</artifactId>
                <version>********-RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aliyun</groupId>
                        <artifactId>tea-openapi</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.aliyun</groupId>
                        <artifactId>tea-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.aliyun</groupId>
                        <artifactId>tea</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 裹裹24小时运力接入end -->

            <dependency>
                <groupId>com.cainiao.waybill.trade</groupId>
                <artifactId>trade-center-api</artifactId>
                <version>1.2.2</version>
            </dependency>

            <dependency>
                <groupId>net.sf.dozer</groupId>
                <artifactId>dozer</artifactId>
                <version>5.5.1</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.logisticsdetail</groupId>
                <artifactId>logisticsdetail-basic-common</artifactId>
                <version>1.0.22</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>3.13.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>3.13.1</version>
            </dependency>

            <!--物流云短信依赖-->
            <dependency>
                <groupId>com.cainiao.middleware</groupId>
                <artifactId>cn-sms-user-client</artifactId>
                <version>1.9.5</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.metaq.final</groupId>
                <artifactId>metaq-client</artifactId>
                <version>4.2.1.Final</version>
            </dependency>
            <dependency>
                <groupId>com.taobao.itemcenter</groupId>
                <artifactId>itemcenter-client</artifactId>
                <version>99.0-does-not-exist</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-common</artifactId>
                <version>${waybill-shared.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.finance</groupId>
                <artifactId>cf-acquirer-gateway-client</artifactId>
                <version>2.0.5</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.cainiao.finance</groupId>
                <artifactId>cf-acquirer-common</artifactId>
                <version>1.8.48</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.cainiao.finance</groupId>
                <artifactId>cf-acquirer-client</artifactId>
                <version>1.8.48</version>
                <exclusions>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mcms-spring-boot-starter</artifactId>
                        <groupId>com.alibaba.intl.sourcing.shared</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.cainiao.fundwork</groupId>
                        <artifactId>cf-fundwork-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.cainiao.fund</groupId>
                        <artifactId>cf-constants</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.cainiao.finance</groupId>
                        <artifactId>cf-charge-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.cainiao</groupId>
                        <artifactId>ISS-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.cainiao.snowland</groupId>
                        <artifactId>snowland-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-test</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.common.division</groupId>
                        <artifactId>common-division-plus</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-test</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.hsf</groupId>
                        <artifactId>hsf-standalone</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.wlb.businesscenter</groupId>
                        <artifactId>businesscenter-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.taobao.wlb.businesscenter</groupId>
                        <artifactId>businesscenter-cache</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.cainiao.fund</groupId>
                <artifactId>cf-constants</artifactId>
                <version>0.1.15</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.csp</groupId>
                <artifactId>sentinel</artifactId>
                <version>3.9.24</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.0.5</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.geography</groupId>
                <artifactId>dipan-engine-api</artifactId>
                <version>${dipan-engine-api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.finance</groupId>
                <artifactId>cf-charge-engine-facade</artifactId>
                <version>2.0.37</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.hsf</groupId>
                <artifactId>hsf.app.spring</artifactId>
                <version>${hsf-app-spring-version}</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.hsf</groupId>
                <artifactId>hsf-feature-context</artifactId>
                <version>${hsf-feature-context-version}</version>
            </dependency>

            <dependency>
                <groupId>com.taobao.diamond</groupId>
                <artifactId>diamond-client</artifactId>
                <version>${diamond-client-version}</version>
            </dependency>


            <!--            odps tunnel start-->
            <dependency>
                <groupId>com.aliyun.odps</groupId>
                <artifactId>odps-sdk-core-internal</artifactId>
                <version>0.44.0</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.odps</groupId>
                <artifactId>odps-sdk-core</artifactId>
                <version>0.44.0</version>
            </dependency>
            <!--            odps tunnel end-->

            <!--buc start-->
            <dependency>
                <groupId>com.alibaba.platform.shared</groupId>
                <artifactId>buc.sso.client</artifactId>
                <version>1.8.1</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>buc-spring-boot-starter</artifactId>
                <version>1.8.1</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.platform.shared</groupId>
                <artifactId>buc.uc.api</artifactId>
                <version>1.0.96</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.middleware</groupId>
                <artifactId>buc.sso.client.plugin-sdk</artifactId>
                <version>999-not-exist</version>
            </dependency>
            <!--buc end-->

            <!--poi start-->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <!--poi end-->

            <dependency>
                <groupId>com.alibaba.platform.shared</groupId>
                <artifactId>acl.api</artifactId>
                <version>2.3.13</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>velocity-spring-boot-starter</artifactId>
                <version>1.0.4.RELEASE</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.waybill.galaxy</groupId>
                <artifactId>order-api</artifactId>
                <version>1.0.36</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill.galaxy</groupId>
                <artifactId>common</artifactId>
                <version>1.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill.galaxy</groupId>
                <artifactId>payment-api</artifactId>
                <version>1.0.31</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill.galaxy</groupId>
                <artifactId>user-api</artifactId>
                <version>1.31.18</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.cainiao.waybill.galaxy</groupId>
                <artifactId>galaxy-api</artifactId>
                <version>2.0.20</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.dingtalk.open</groupId>-->
<!--                <artifactId>dingtalk-openapi-sdk</artifactId>-->
<!--                <version>20190323</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.github.rholder</groupId>
                <artifactId>guava-retrying</artifactId>
                <version>2.0.0</version>
            </dependency>

            <!-- 裹裹 -->
            <dependency>
                <groupId>com.cainiao.tdtradeplatform</groupId>
                <artifactId>tdtradeplatform-open-client</artifactId>
                <version>${tdtradeplatform.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.cainiao.crowd</groupId>
                        <artifactId>cainiao-crowd-client</artifactId>
                </exclusion>
                    <exclusion>
                        <groupId>com.cainiao.tdcqf</groupId>
                        <artifactId>tdcqf-core</artifactId>
                </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.cainiao.tdtradeplatform2</groupId>
                <artifactId>tdtradeplatform2-client</artifactId>
                <version>${tdtradeplatform2.client.version}</version>
            </dependency>


            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>2.9.2</version>
            </dependency>

            <!--https://mvnrepository.com/artifact/io.springfox/springfox-swagger-ui -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>2.9.2</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.openservices.eas</groupId>
                <artifactId>eas-sdk</artifactId>
                <version>1.1.7</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>ocr_api20210707</artifactId>
                <version>1.1.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.aliyun</groupId>
                        <artifactId>tea-openapi</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.aliyun</groupId>
                        <artifactId>tea-util</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.aliyun</groupId>
                        <artifactId>tea</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-security-client-teaclient</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-kms</artifactId>
                <version>2.14.0</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-security-client-common</artifactId>
                <version>1.2.14</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-codec</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.cainiao.arch</groupId>
                <artifactId>nbp-client-spring</artifactId>
                <version>${nbp.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.cne.sns</groupId>
                <artifactId>cne-sns-client</artifactId>
                <version>1.0.2.10</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.cne.sns</groupId>
                <artifactId>cne-sns-common</artifactId>
                <version>1.0.2.10</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.cainiao.waybill</groupId>-->
<!--                <artifactId>waybill-client</artifactId>-->
<!--                <version>3.0.27</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.alibaba.security</groupId>
                <artifactId>tenant-common</artifactId>
                <version>1.6.5</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.hyena</groupId>
                <artifactId>hyena-alibaba-common</artifactId>
                <version>7.2.45</version>
            </dependency>

            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>1.3.5</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.work.alipmc</groupId>
                <artifactId>alipmc-api</artifactId>
                <version>3.3.8</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.work.alipmc</groupId>
                <artifactId>ngalipmc-bpm</artifactId>
                <version>2.1.41</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.work.alipmc</groupId>
                <artifactId>ngalipmc-bpm-core</artifactId>
                <version>2.1.41</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <!-- 结算服务 -->
            <dependency>
                <groupId>com.cainiao.finance</groupId>
                <artifactId>cf-fee-client</artifactId>
                <version>1.1.12</version>
            </dependency>
            <dependency>
                <groupId>com.cainiao.finance</groupId>
                <artifactId>cf-prerun-client</artifactId>
                <version>1.0.56</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-advertisement-common</artifactId>
                <version>1.0.53</version>
            </dependency>
            <!-- kfc内容敏感词校验 -->
            <dependency>
                <groupId>com.taobao.kfc</groupId>
                <artifactId>kfc-client</artifactId>
                <version>4.1.9</version>
            </dependency>

            <dependency>
                <groupId>com.dingtalk.open</groupId>
                <artifactId>app-stream-client</artifactId>
                <version>1.3.2</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dingtalk</artifactId>
                <version>2.2.12</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibaba-dingtalk-service-sdk</artifactId>
                <version>2.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao.waybill</groupId>
                <artifactId>waybill-number-client</artifactId>
                <version>1.0.8</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>0.9.1</version>
            </dependency>

            <dependency>
                <groupId>com.cainiao</groupId>
                <artifactId>spring-startup-starter</artifactId>
                <version>1.0.0</version>
            </dependency>

        </dependencies>

    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.taobao.pandora</groupId>
                    <artifactId>pandora-boot-maven-plugin</artifactId>
                    <version>${pandora-boot-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.alibaba.citrus.tool</groupId>
                    <artifactId>autoconfig-maven-plugin</artifactId>
                    <version>${autoconfig-maven-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.2.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>1.8</source> <!-- depending on your project -->
                        <target>1.8</target> <!-- depending on your project -->
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${org.mapstruct.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>1.18.16</version>
                            </path>
                            <!-- This is needed when using Lombok 1.18.16 and above -->
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok-mapstruct-binding</artifactId>
                                <version>0.2.0</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>com.alibaba.maven.plugins</groupId>
                    <artifactId>nts-maven-plugin</artifactId>
                    <version>1.1.7</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>com.sap.prd.mobile.ios.maven.plugins</groupId>
                <artifactId>resolve-pom-maven-plugin</artifactId>
                <version>1.0</version>
                <executions>
                    <execution>
                        <id>resolve-pom-props</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>resolve-pom-props</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>com.alibaba.dsm</groupId>
            <artifactId>dsm-gdp-client-springboot</artifactId>
            <version>1.0.11</version>
        </dependency>
    </dependencies>
</project>
