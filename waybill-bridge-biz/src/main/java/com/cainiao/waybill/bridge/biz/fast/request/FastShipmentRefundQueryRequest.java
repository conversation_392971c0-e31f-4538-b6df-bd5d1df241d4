package com.cainiao.waybill.bridge.biz.fast.request;

import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpPagingBaseRequest;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/26 12:14
 */
@Data
public class FastShipmentRefundQueryRequest extends PickUpPagingBaseRequest {
    /**
     * 运单号
     */
    private List<String> waybillCodes;

    /**
     * 下单日期起始
     */
    private String payTimeStart;
    /**
     * 下单日期截止
     */
    private String payTimeEnd;
    /**
     * 审批状态
     */
    private Integer approvalStatus;
    /**
     * 退款状态
     */
    private Integer refundStatus;
    /**
     * 姓名
     */
    private String sendName;

    /**
     * 电话
     */
    private String sendPhone;
}
