package com.cainiao.waybill.bridge.common.util;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.google.common.collect.Lists;

/**
 * <AUTHOR> zouping.fzp
 * @Classname ListUtil
 * @Description
 * @Date 2022/8/3 5:03 下午
 * @Version 1.0
 */
public class ListUtil {

    public static <T> List<T> non(List<T> list) {
        if (list == null) {
            return Lists.newArrayList();
        }
        return list;
    }

    public static <T> Stream<T> stream(List<T> list) {
        if (list == null) {
            return (Stream<T>)Lists.newArrayList().stream();
        }
        return list.stream();
    }

    public static <T, R> List<R> map(List<T> list, Function<? super T, ? extends R> mapper) {
        if (list == null) {
            return Lists.newArrayList();
        }
        return list.stream().map(mapper).collect(Collectors.toList());
    }

    public static <T> void add(List<T> list, T t) {
        if (list == null || t == null) {
            return;
        }
        list.add(t);
    }
}
