package com.cainiao.waybill.bridge.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
public class WaybillEnterpriseLocationDTO {
    /**
     *   业务id
     */
    private String locationId;

    /**
     *   企业唯一id
     */
    private String corpId;

    /**
     *   办公地点自定义名称
     *
     */
    private String locationName;


    /**
     *   外键-关联月结账号配置
     *
     */
    private String waybillAccountVid;

    /**
     *   备注
     *
     */
    private String remark;

    /**
     *   创建时间
     */
    private Date gmtCreate;

    /**
     *   修改时间
     */
    private Date gmtModified;

    /**
     *   办公场地详细地址Code
     *
     */
    private AddressInfo locationAddressCode;

    /**
     *   办公场地详细地址
     *
     */
    private AddressInfo locationAddress;

    /**
     * 月结账号 外键
     */
    private List<WaybillAccount> waybillAccountsList;
}