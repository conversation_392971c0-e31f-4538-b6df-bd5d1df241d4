package com.cainiao.waybill.bridge.biz.fast.request;

import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpBaseRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/26 12:14
 */
@Data
public class FastShipmentOrderRequest extends PickUpBaseRequest {
    /**
     * 运单号,可批量
     */
    private List<String> waybillCode;
    /**
     * 打印机编码
     */
    private List<String> printerId;
    /**
     * 运力类型
     */
    private Integer transportCapacity;
    /**
     * cp
     */
    private String cpCode;
    /**
     * 网点编码
     */
    private List<String> cpBranchCode;
    /**
     * 状态
     */
    private List<Integer> status;
    /**
     * 揽收状态
     */
    private List<Integer> action;
    /**
     * 创建起始时间
     */
    private String gmtCreateStart;
    /**
     * 创建截止时间
     */
    private String gmtCreateEnd;
    /**
     * 负责人信息
     */
    private List<String> acceptOrderMobile;
    private Integer pageSize;
    /**
     * 寄件省
     */
    private String sourceProvince;
    /**
     * 寄件城市
     */
    private String sourceCity;
    /**
     * 支付起始时间
     */
    private String paidTimeStart;
    /**
     * 支付截止时间
     */
    private String paidTimeEnd;
    /**
     * 寄件人手机号
     */
    private String sendPhone;
    /**
     * 支付状态
     */
    private Integer payStatus;
    private Integer currentPage;

    /**
     * 寄件人姓名
     */
    private String sendName;
    /**
     * 运力来源
     */
    private Integer applyType;
}
