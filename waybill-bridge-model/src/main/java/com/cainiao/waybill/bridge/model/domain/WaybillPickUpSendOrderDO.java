package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
/**
 *
 * <AUTHOR>
 */
public class WaybillPickUpSendOrderDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   网点code
     *
     *
     * @mbg.generated
     */
    private String branchCode;

    /**
     * Database Column Remarks:
     *   三段码
     *
     *
     * @mbg.generated
     */
    private String threeSegmentCode;

    /**
     * Database Column Remarks:
     *   四段码
     *
     *
     * @mbg.generated
     */
    private String blockCode;

    /**
     * Database Column Remarks:
     *   实际揽件的小件员工号
     *
     *
     * @mbg.generated
     */
    private String opEmployeeNo;

    /**
     * Database Column Remarks:
     *   运单号
     *
     *
     * @mbg.generated
     */
    private String mailNo;

    /**
     * Database Column Remarks:
     *   分配的小件员工号
     *
     *
     * @mbg.generated
     */
    private String assignEmployeeNo;

    /**
     * Database Column Remarks:
     *   日期，yyyyMMdd格式
     *
     *
     * @mbg.generated
     */
    private String date;

    /**
     * Database Column Remarks:
     *   扩展属性字段
     *
     *
     * @mbg.generated
     */
    private String feature;

    /**
     * Database Column Remarks:
     *   cpcode
     *
     *
     * @mbg.generated
     */
    private String cpCode;

    /**
     *
     * @return the value of waybill_pick_up_send_order.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_pick_up_send_order.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_pick_up_send_order.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_pick_up_send_order.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_pick_up_send_order.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_pick_up_send_order.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_pick_up_send_order.branch_code
     *
     * @mbg.generated
     */
    public String getBranchCode() {
        return branchCode;
    }

    /**
     *
     * @param branchCode the value for waybill_pick_up_send_order.branch_code
     *
     * @mbg.generated
     */
    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    /**
     *
     * @return the value of waybill_pick_up_send_order.three_segment_code
     *
     * @mbg.generated
     */
    public String getThreeSegmentCode() {
        return threeSegmentCode;
    }

    /**
     *
     * @param threeSegmentCode the value for waybill_pick_up_send_order.three_segment_code
     *
     * @mbg.generated
     */
    public void setThreeSegmentCode(String threeSegmentCode) {
        this.threeSegmentCode = threeSegmentCode;
    }

    /**
     *
     * @return the value of waybill_pick_up_send_order.block_code
     *
     * @mbg.generated
     */
    public String getBlockCode() {
        return blockCode;
    }

    /**
     *
     * @param blockCode the value for waybill_pick_up_send_order.block_code
     *
     * @mbg.generated
     */
    public void setBlockCode(String blockCode) {
        this.blockCode = blockCode;
    }

    /**
     *
     * @return the value of waybill_pick_up_send_order.op_employee_no
     *
     * @mbg.generated
     */
    public String getOpEmployeeNo() {
        return opEmployeeNo;
    }

    /**
     *
     * @param opEmployeeNo the value for waybill_pick_up_send_order.op_employee_no
     *
     * @mbg.generated
     */
    public void setOpEmployeeNo(String opEmployeeNo) {
        this.opEmployeeNo = opEmployeeNo;
    }

    /**
     *
     * @return the value of waybill_pick_up_send_order.mail_no
     *
     * @mbg.generated
     */
    public String getMailNo() {
        return mailNo;
    }

    /**
     *
     * @param mailNo the value for waybill_pick_up_send_order.mail_no
     *
     * @mbg.generated
     */
    public void setMailNo(String mailNo) {
        this.mailNo = mailNo;
    }

    /**
     *
     * @return the value of waybill_pick_up_send_order.assign_employee_no
     *
     * @mbg.generated
     */
    public String getAssignEmployeeNo() {
        return assignEmployeeNo;
    }

    /**
     *
     * @param assignEmployeeNo the value for waybill_pick_up_send_order.assign_employee_no
     *
     * @mbg.generated
     */
    public void setAssignEmployeeNo(String assignEmployeeNo) {
        this.assignEmployeeNo = assignEmployeeNo;
    }

    /**
     *
     * @return the value of waybill_pick_up_send_order.date
     *
     * @mbg.generated
     */
    public String getDate() {
        return date;
    }

    /**
     *
     * @param date the value for waybill_pick_up_send_order.date
     *
     * @mbg.generated
     */
    public void setDate(String date) {
        this.date = date;
    }

    /**
     *
     * @return the value of waybill_pick_up_send_order.feature
     *
     * @mbg.generated
     */
    public String getFeature() {
        return feature;
    }

    /**
     *
     * @param feature the value for waybill_pick_up_send_order.feature
     *
     * @mbg.generated
     */
    public void setFeature(String feature) {
        this.feature = feature;
    }

    /**
     *
     * @return the value of waybill_pick_up_send_order.cp_code
     *
     * @mbg.generated
     */
    public String getCpCode() {
        return cpCode;
    }

    /**
     *
     * @param cpCode the value for waybill_pick_up_send_order.cp_code
     *
     * @mbg.generated
     */
    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", branchCode=").append(branchCode);
        sb.append(", threeSegmentCode=").append(threeSegmentCode);
        sb.append(", blockCode=").append(blockCode);
        sb.append(", opEmployeeNo=").append(opEmployeeNo);
        sb.append(", mailNo=").append(mailNo);
        sb.append(", assignEmployeeNo=").append(assignEmployeeNo);
        sb.append(", date=").append(date);
        sb.append(", feature=").append(feature);
        sb.append(", cpCode=").append(cpCode);
        sb.append("]");
        return sb.toString();
    }
}