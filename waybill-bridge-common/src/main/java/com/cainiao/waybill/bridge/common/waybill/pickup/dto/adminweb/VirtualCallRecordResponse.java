package com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/21-下午5:55
 */
@Data
public class VirtualCallRecordResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订阅关系ID
     */
    private String subId;
    /**
     * 物理云通话Id
     */
    private String callId;

    /**
     * 物理云响铃时间
     */
    private String ringTime;

    /**
     * 物理云接通时间，当为未接通状态时，该值与释放时间相同
     */
    private String startTime;

    /**
     * 物理云释放时间
     */
    private String releaseTime;

    /**
     * 拨打小号的小件员号码
     */
    private String callMobile;

    /**
     * 是否通话成功：0：失败 1：成功
     */
    private String isSuccess;

    /**
     * 释放方向： 0：平台释放 1：主叫释放， 2：被叫释放
     */
    private String releaseDir;

    /**
     * 小号电联录音文件下载链接
     */
    private String recordFileDownLoadUrl = "";
}
