package com.cainiao.waybill.bridge.client2;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/10-上午11:23
 */
@Data
public class PagingResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    private String errorCode;
    private String errorMsg;
    private boolean success = true;
    private List<T> data;
    /**
     * 分页信息
     */
    private PagingInfo pagingInfo;

    public static <T> PagingResult<T> buildError(String errorMsg) {
        PagingResult<T> response = new PagingResult<>();
        response.setSuccess(false);
        response.setErrorMsg(errorMsg);
        return response;
    }

    public static <T> PagingResult<T> build(List<T> tableData, long totalCount, Integer currentPage, Integer pageSize) {
        PagingResult<T> response = new PagingResult<>();
        response.setSuccess(true);
        response.setData(tableData);
        PagingInfo pageInfo = new PagingInfo();
        pageInfo.setCurrentPage(currentPage);
        pageInfo.setPageSize(pageSize);
        pageInfo.setTotalCount(Math.toIntExact(totalCount));
        response.setPagingInfo(pageInfo);
        return response;
    }

    public static <T> PagingResult<T> build(List<T> tableData, PagingInfo pagingInfo) {
        PagingResult<T> response = new PagingResult<>();
        response.setSuccess(true);
        response.setData(tableData);
        PagingInfo paging = new PagingInfo();
        paging.setCurrentPage(pagingInfo.getCurrentPage());
        paging.setPageSize(pagingInfo.getPageSize());
        paging.setTotalCount(pagingInfo.getTotalCount());
        response.setPagingInfo(paging);
        return response;
    }
}

