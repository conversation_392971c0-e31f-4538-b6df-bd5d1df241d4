package com.cainiao.waybill.bridge.common.label.service;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;

/**
 * 商家协议服务
 * <AUTHOR>
 * @date 2017/06/12
 */
public interface SellerAgreementService {

    /**
     * 商家协议是否有效中
     * @param sellerId
     * @param clientInfoDTO
     * @return
     */
    BaseResultDTO<Boolean> isSellerAgreementValid(Long sellerId, ClientInfoDTO clientInfoDTO);

    /**
     * 签署商家协议
     * @param sellerId
     * @param clientInfoDTO
     * @return
     */
    BaseResultDTO<Void> signSellerAgreement(Long sellerId, ClientInfoDTO clientInfoDTO);
}
