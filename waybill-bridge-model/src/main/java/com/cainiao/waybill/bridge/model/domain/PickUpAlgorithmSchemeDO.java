package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class PickUpAlgorithmSchemeDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   方案名称
     *
     *
     * @mbg.generated
     */
    private String name;

    /**
     * Database Column Remarks:
     *   平台名称
     *
     *
     * @mbg.generated
     */
    private String platform;

    /**
     * Database Column Remarks:
     *   平台渠道
     *
     *
     * @mbg.generated
     */
    private String orderChannels;

    /**
     * Database Column Remarks:
     *   可用运力列表
     *
     *
     * @mbg.generated
     */
    private String availableCpList;

    /**
     * Database Column Remarks:
     *   目标指标项
     *
     *
     * @mbg.generated
     */
    private String targetDimension;

    /**
     * Database Column Remarks:
     *   目标方向值
     *
     *
     * @mbg.generated
     */
    private String targetDonstraint;

    /**
     * Database Column Remarks:
     *   方案状态
     *
     *
     * @mbg.generated
     */
    private String status;

    /**
     * Database Column Remarks:
     *   算法执行进度
     *
     *
     * @mbg.generated
     */
    private String algorithmProgress;

    /**
     * Database Column Remarks:
     *   算法测算结果
     *
     *
     * @mbg.generated
     */
    private String algorithmCalcResult;

    /**
     * Database Column Remarks:
     *   算法测算结果摘要
     *
     *
     * @mbg.generated
     */
    private String algorithmDigest;

    /**
     * Database Column Remarks:
     *   生效时间
     *
     *
     * @mbg.generated
     */
    private Date effectiveTime;

    /**
     * Database Column Remarks:
     *   创建人
     *
     *
     * @mbg.generated
     */
    private String creator;

    /**
     * Database Column Remarks:
     *   发布人
     *
     *
     * @mbg.generated
     */
    private String publisher;

    /**
     * Database Column Remarks:
     *   测算方案key
     *
     *
     * @mbg.generated
     */
    private String calcKey;

    /**
     * Database Column Remarks:
     *   审批流key
     *
     *
     * @mbg.generated
     */
    private String approveKey;

    /**
     * Database Column Remarks:
     *   审批状态
     *
     *
     * @mbg.generated
     */
    private String approveStatus;

    /**
     * Database Column Remarks:
     *   扩展字段
     *
     *
     * @mbg.generated
     */
    private String extInfo;

    /**
     *
     * @return the value of pick_up_algorithm_scheme.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for pick_up_algorithm_scheme.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for pick_up_algorithm_scheme.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for pick_up_algorithm_scheme.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.name
     *
     * @mbg.generated
     */
    public String getName() {
        return name;
    }

    /**
     *
     * @param name the value for pick_up_algorithm_scheme.name
     *
     * @mbg.generated
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.platform
     *
     * @mbg.generated
     */
    public String getPlatform() {
        return platform;
    }

    /**
     *
     * @param platform the value for pick_up_algorithm_scheme.platform
     *
     * @mbg.generated
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.order_channels
     *
     * @mbg.generated
     */
    public String getOrderChannels() {
        return orderChannels;
    }

    /**
     *
     * @param orderChannels the value for pick_up_algorithm_scheme.order_channels
     *
     * @mbg.generated
     */
    public void setOrderChannels(String orderChannels) {
        this.orderChannels = orderChannels;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.available_cp_list
     *
     * @mbg.generated
     */
    public String getAvailableCpList() {
        return availableCpList;
    }

    /**
     *
     * @param availableCpList the value for pick_up_algorithm_scheme.available_cp_list
     *
     * @mbg.generated
     */
    public void setAvailableCpList(String availableCpList) {
        this.availableCpList = availableCpList;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.target_dimension
     *
     * @mbg.generated
     */
    public String getTargetDimension() {
        return targetDimension;
    }

    /**
     *
     * @param targetDimension the value for pick_up_algorithm_scheme.target_dimension
     *
     * @mbg.generated
     */
    public void setTargetDimension(String targetDimension) {
        this.targetDimension = targetDimension;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.target_donstraint
     *
     * @mbg.generated
     */
    public String getTargetDonstraint() {
        return targetDonstraint;
    }

    /**
     *
     * @param targetDonstraint the value for pick_up_algorithm_scheme.target_donstraint
     *
     * @mbg.generated
     */
    public void setTargetDonstraint(String targetDonstraint) {
        this.targetDonstraint = targetDonstraint;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.status
     *
     * @mbg.generated
     */
    public String getStatus() {
        return status;
    }

    /**
     *
     * @param status the value for pick_up_algorithm_scheme.status
     *
     * @mbg.generated
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.algorithm_progress
     *
     * @mbg.generated
     */
    public String getAlgorithmProgress() {
        return algorithmProgress;
    }

    /**
     *
     * @param algorithmProgress the value for pick_up_algorithm_scheme.algorithm_progress
     *
     * @mbg.generated
     */
    public void setAlgorithmProgress(String algorithmProgress) {
        this.algorithmProgress = algorithmProgress;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.algorithm_calc_result
     *
     * @mbg.generated
     */
    public String getAlgorithmCalcResult() {
        return algorithmCalcResult;
    }

    /**
     *
     * @param algorithmCalcResult the value for pick_up_algorithm_scheme.algorithm_calc_result
     *
     * @mbg.generated
     */
    public void setAlgorithmCalcResult(String algorithmCalcResult) {
        this.algorithmCalcResult = algorithmCalcResult;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.algorithm_digest
     *
     * @mbg.generated
     */
    public String getAlgorithmDigest() {
        return algorithmDigest;
    }

    /**
     *
     * @param algorithmDigest the value for pick_up_algorithm_scheme.algorithm_digest
     *
     * @mbg.generated
     */
    public void setAlgorithmDigest(String algorithmDigest) {
        this.algorithmDigest = algorithmDigest;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.effective_time
     *
     * @mbg.generated
     */
    public Date getEffectiveTime() {
        return effectiveTime;
    }

    /**
     *
     * @param effectiveTime the value for pick_up_algorithm_scheme.effective_time
     *
     * @mbg.generated
     */
    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     *
     * @param creator the value for pick_up_algorithm_scheme.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.publisher
     *
     * @mbg.generated
     */
    public String getPublisher() {
        return publisher;
    }

    /**
     *
     * @param publisher the value for pick_up_algorithm_scheme.publisher
     *
     * @mbg.generated
     */
    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.calc_key
     *
     * @mbg.generated
     */
    public String getCalcKey() {
        return calcKey;
    }

    /**
     *
     * @param calcKey the value for pick_up_algorithm_scheme.calc_key
     *
     * @mbg.generated
     */
    public void setCalcKey(String calcKey) {
        this.calcKey = calcKey;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.approve_key
     *
     * @mbg.generated
     */
    public String getApproveKey() {
        return approveKey;
    }

    /**
     *
     * @param approveKey the value for pick_up_algorithm_scheme.approve_key
     *
     * @mbg.generated
     */
    public void setApproveKey(String approveKey) {
        this.approveKey = approveKey;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.approve_status
     *
     * @mbg.generated
     */
    public String getApproveStatus() {
        return approveStatus;
    }

    /**
     *
     * @param approveStatus the value for pick_up_algorithm_scheme.approve_status
     *
     * @mbg.generated
     */
    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    /**
     *
     * @return the value of pick_up_algorithm_scheme.ext_info
     *
     * @mbg.generated
     */
    public String getExtInfo() {
        return extInfo;
    }

    /**
     *
     * @param extInfo the value for pick_up_algorithm_scheme.ext_info
     *
     * @mbg.generated
     */
    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", name=").append(name);
        sb.append(", platform=").append(platform);
        sb.append(", orderChannels=").append(orderChannels);
        sb.append(", availableCpList=").append(availableCpList);
        sb.append(", targetDimension=").append(targetDimension);
        sb.append(", targetDonstraint=").append(targetDonstraint);
        sb.append(", status=").append(status);
        sb.append(", algorithmProgress=").append(algorithmProgress);
        sb.append(", algorithmCalcResult=").append(algorithmCalcResult);
        sb.append(", algorithmDigest=").append(algorithmDigest);
        sb.append(", effectiveTime=").append(effectiveTime);
        sb.append(", creator=").append(creator);
        sb.append(", publisher=").append(publisher);
        sb.append(", calcKey=").append(calcKey);
        sb.append(", approveKey=").append(approveKey);
        sb.append(", approveStatus=").append(approveStatus);
        sb.append(", extInfo=").append(extInfo);
        sb.append("]");
        return sb.toString();
    }
}