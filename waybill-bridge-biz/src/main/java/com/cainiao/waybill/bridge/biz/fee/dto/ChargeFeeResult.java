package com.cainiao.waybill.bridge.biz.fee.dto;

import lombok.Data;

/**
 * 结算收费结果
 * <AUTHOR>
 * @date 2024/7/31 20:52
 **/
@Data
public class ChargeFeeResult {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 来源系统编码
     */
    private String sourceSystem;

    /**
     * 业务单据号
     */
    private String serviceOrderCode;

    /**
     * 外部订单号，幂等、对账用
     */
    private String externalOrderId;

    /**
     * 费用项编码，结算定义，业务系统选用
     */
    private String feeCode;

    /**
     * 收费单ID
     */
    private Long cfFeeOrderId;

    /**
     * 结算ID
     */
    private Long cfOrderId;

    /**
     * 结算单ID
     */
    private Long cfChargeOrderId;
}
