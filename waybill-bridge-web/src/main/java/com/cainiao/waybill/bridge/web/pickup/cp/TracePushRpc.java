package com.cainiao.waybill.bridge.web.pickup.cp;

/**
 * <AUTHOR>
 * @date 2021/7/9 下午5:52
 */

import com.alibaba.common.lang.ExceptionUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.security.SecurityUtil;

import cn.hutool.core.util.NumberUtil;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.CancelCodes;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Cp;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpDetailStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpEventConstants;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.service.impl.WaybillPickUpEventMetaQSender;
import com.cainiao.waybill.bridge.biz.utils.pickup.*;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.util.WeightUnitUtil;
import com.cainiao.waybill.bridge.common.waybill.constants.WaybillPickUpActionConstant;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.google.common.collect.Maps;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.xml.sax.InputSource;

import javax.annotation.Resource;
import java.io.StringReader;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

@RestController
@RequestMapping("/tracepush")
public class TracePushRpc {

    private static final String RESP_FIELD_SUCCESS = "success";
    private static final String RESP_FIELD_LOGISTIC_PROVIDER_ID = "logisticProviderID";
    private static final String RESP_FIELD_TX_LOGISTIC_ID = "txLogisticID";
    private static final String RESP_FIELD_REASON = "reason";

    @Resource
    private WaybillPickUpOrderManager waybillPickUpOrderManager;

    @Resource
    private WaybillPickUpEventMetaQSender waybillPickUpEventMetaQSender;

    @PostMapping("/yto")
    public Map<String, Object> tracePush(@RequestParam(required = false) String logistics_interface
            , @RequestParam(required = false) String data_digest
            , @RequestParam(required = false) String clientId
            , @RequestParam(required = false) String type) throws Exception {

        PickUpLogUtil.record("outer_accept_yto", logistics_interface);

        PickUpLogUtil.info("logistics_interface:{},traceXml:{}", data_digest, logistics_interface);

        Map<String, Object> result = Maps.newHashMap();
        String txLogisticID = null;
        String logisticProviderID = null;
        try {
            //  监控发现有时会有空的，或者不是正确格式的请求过来，这里过滤掉
            Map<String, Object> errResult = Maps.newHashMap();
            errResult.put(RESP_FIELD_SUCCESS, false);
            String errMsg = "非发请求参数, logistics_interface: ";
            if (StringUtil.isBlank(logistics_interface)) {
                PickUpLogUtil.errLog("", PickUpConstants.Action.TRACE_PUSH_RPC_INVALID_PARAM.name(), PickUpConstants.Error.TRACE_PUSH_RPC_ERROR_YTO.getErrorCode(), errMsg + logistics_interface);
                errResult.put(RESP_FIELD_REASON, errMsg + logistics_interface);
                return errResult;
            }
            if (logistics_interface.length() < 10) {
                PickUpLogUtil.errLog("", PickUpConstants.Action.TRACE_PUSH_RPC_INVALID_PARAM.name(), PickUpConstants.Error.TRACE_PUSH_RPC_ERROR_YTO.getErrorCode(), errMsg + logistics_interface);
                errResult.put(RESP_FIELD_REASON, errMsg + logistics_interface);
                return errResult;
            }

            String xmlData = URLDecoder.decode(logistics_interface, StandardCharsets.UTF_8.name());

            SAXReader reader = new SAXReader();

            //安全校验要求加的
            reader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            reader.setFeature("http://xml.org/sax/features/external-general-entities", false);
            reader.setFeature("http://xml.org/sax/features/external-parameter-entities", false);

            InputSource source = new InputSource(new StringReader(xmlData));
            Document document = reader.read(source);
            Element root = document.getRootElement();

            txLogisticID = root.element("txLogisticID").getText();
            logisticProviderID = SecurityUtil.escapeHtml(root.element("logisticProviderID").getText());
            result.put(RESP_FIELD_SUCCESS, true);
            result.put(RESP_FIELD_TX_LOGISTIC_ID, SecurityUtil.escapeHtml(txLogisticID));
            result.put(RESP_FIELD_LOGISTIC_PROVIDER_ID, logisticProviderID);

            if (BridgeSwitch.ytoDigestCheckGrepSwitch) {
                //  验证摘要内容
                String expectedDigest = encryptSign(xmlData, BridgeSwitch.ytoPickUpConfig.get(PickUpConstants.Yto.ConfigKey.partnerId.name()));
                if (!data_digest.equals(expectedDigest)) {
                    result.put(RESP_FIELD_REASON, "报文验证失败");
                    result.put(RESP_FIELD_SUCCESS, false);
                    return result;
                }
            }

            String infoContent = root.element("infoContent").getText();

            WaybillPickUpDetailDO detailDO;
            if(NumberUtil.isNumber(txLogisticID)){
                detailDO = waybillPickUpOrderManager.getById(Long.valueOf(txLogisticID));
            }else{
                ImmutablePair<String, String> pair = CpLogisticNoUtil.Yto.parseResCodeAndOuterOrderCode(txLogisticID);
                //根据回传的resCode和outOrderCode查询订单信息
                detailDO = waybillPickUpOrderManager.get(pair.left, pair.right);
            }
            // 订单不存在或者已经转单
            if (detailDO == null || !StringUtils.equals(detailDO.getCpCode(), Cp.YTO.name())) {
                result.put(RESP_FIELD_REASON, "订单不存在");
                result.put(RESP_FIELD_SUCCESS, false);
                return result;
            }

            String action = getAction(infoContent, detailDO, root);

            //  action为空，即不是希望拿到的节点类型，不向电商平台下发
            if (StringUtil.isBlank(action)) {
                return result;
            }

            //  判断CP回传重量的时间是否超过指定的时间限制
            if (!CpWeightPushLimitUtil.cpPushWeightInLimitRange(action, detailDO)) {
                result.put(RESP_FIELD_SUCCESS, false);
                result.put(RESP_FIELD_REASON, PickUpConstants.Error.TRACE_PUSH_GOT_LIMIT.getErrorMsg() + "T+" + BridgeSwitch.cpReturnWeightDayLimit);
                PickUpLogUtil.errLog("", PickUpConstants.Action.YTO_RETURN_WEIGHT_LIMIT.name(), PickUpConstants.Error.YTO_RETURN_WEIGHT_LIMIT.getErrorCode(),
                        PickUpConstants.Error.YTO_RETURN_WEIGHT_LIMIT.getErrorMsg() + "existDetailDO:" + JSON.toJSONString(detailDO));
                return result;
            }

            WaybillPickUpEvent pickUpEvent = new WaybillPickUpEvent();
            pickUpEvent.setResCode(detailDO.getResCode());
            pickUpEvent.setMailNo(detailDO.getMailNo());
            pickUpEvent.setCpCode(PickUpConstants.Cp.YTO.name());
            pickUpEvent.setAction(action);
            pickUpEvent.setActionDesc(WaybillPickUpActionConstant.getActionDesc(action));
            pickUpEvent.setActionGmtModified(System.currentTimeMillis());
            pickUpEvent.setLinkCpCode(PickUpFeatureUtil.getFromMulString(detailDO, PickUpConstants.TraceFeatureKey.LINK_CP_CODE));
            pickUpEvent.setOuterOrderCode(detailDO.getOuterOrderCode());
            if (root.element("city") != null) {
                pickUpEvent.setActionCity(root.element("city").getText());
            }
            if (root.element("remark") != null) {
                pickUpEvent.setLastActionDetail(root.element("remark").getText());
            }
            setExtraInfo(pickUpEvent, root, detailDO);
            PickUpLogUtil.info("yto tracePushRpc pickUpEvent:{}", JSON.toJSONString(pickUpEvent));

            waybillPickUpEventMetaQSender.send(pickUpEvent);
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpConstants.Action.TRACE_PUSH_RPC.name(), PickUpConstants.Error.TRACE_PUSH_RPC_ERROR_YTO.getErrorCode(), PickUpConstants.Error.TRACE_PUSH_RPC_ERROR_YTO.getErrorMsg() + ExceptionUtil.getStackTrace(e));
            result.put(RESP_FIELD_SUCCESS, false);
            result.put(RESP_FIELD_REASON, PickUpConstants.Error.TRACE_PUSH_SYS_ERROR.getErrorMsg());
            result.put(RESP_FIELD_TX_LOGISTIC_ID, SecurityUtil.escapeHtml(txLogisticID));
            result.put(RESP_FIELD_LOGISTIC_PROVIDER_ID, logisticProviderID);
        }
        return result;
    }

    private String encryptSign(String xml, String partnerId) throws Exception {
        MessageDigest messageDigest = MessageDigest.getInstance("MD5");
        byte[] signByte = messageDigest.digest((xml + partnerId).getBytes(StandardCharsets.UTF_8));
        String base64String = new String(Base64.encodeBase64(signByte));
        PickUpLogUtil.info("except sign : " + base64String);
        return base64String;
    }

    private void setExtraInfo(WaybillPickUpEvent pickUpEvent, Element root, WaybillPickUpDetailDO existDetailDO) {
        //  订单扩展信息
        Map<String, String> extraInfo = Maps.newHashMap();
        if (WaybillPickUpActionConstant.GOT.equals(pickUpEvent.getAction()) || WaybillPickUpActionConstant.MODIFY_WEIGHT.equals(pickUpEvent.getAction())) {
            int weight = getWeight(root);
            if (weight > 0) {
                extraInfo.put(PickUpEventConstants.ExtraInfoKey.WEIGHT, String.valueOf(weight));
            }
        }
        if (WaybillPickUpActionConstant.ACCEPT.equals(pickUpEvent.getAction())) {
            extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_NAME, PickUpCommonUtil.getXmlText(root, "empName"));
            extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_ORG_CODE, PickUpCommonUtil.getXmlText(root, "orgCode"));
            extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_ORG_NAME, PickUpCommonUtil.getXmlText(root, "orgName"));
            Date parseYtoDate = parseYtoDate(PickUpCommonUtil.getXmlText(root, "acceptTime"));
            long acceptTime = parseYtoDate != null ? parseYtoDate.getTime() : pickUpEvent.getActionGmtModified();
            extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_TIME, PickUpFeatureUtil.formatTime(acceptTime));
            //  预约件回传：与接单节点内容基本一致，新增了预约开始、结束时间，没有小件员手机号(线上验证下，目前先按照圆通给的来分开处理)
            if (StringUtils.isNotBlank(PickUpCommonUtil.getXmlText(root, "bookingStartTime"))) {
                extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_MOBILE, PickUpFeatureUtil.getFromMulString(existDetailDO, PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_MOBILE));
                extraInfo.put(PickUpEventConstants.ExtraInfoKey.APPOINT_START_TIME, PickUpCommonUtil.formatDate(parseYtoDate(PickUpCommonUtil.getXmlText(root, "bookingStartTime"))));
                extraInfo.put(PickUpEventConstants.ExtraInfoKey.APPOINT_END_TIME, PickUpCommonUtil.formatDate(parseYtoDate(PickUpCommonUtil.getXmlText(root, "bookingEndTime"))));
            } else {
                //  接单节点
                String phone = PickUpCommonUtil.getXmlText(root, "contactInfo");
                extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_MOBILE, StringUtil.isNotBlank(phone) ? phone : getPhone(pickUpEvent.getLastActionDetail()));
            }
        }
        if (WaybillPickUpActionConstant.FAIL.equals(pickUpEvent.getAction())) {
            extraInfo.put(PickUpConstants.TraceFeatureKey.FAIL_REASON, PickUpFeatureUtil.dealFailReason(PickUpCommonUtil.getXmlText(root, "remark")));
            extraInfo.put(PickUpConstants.TraceFeatureKey.FAIL_CODE, CancelCodes.CP_CANCEL.getCode());
            extraInfo.put(PickUpConstants.TraceFeatureKey.CANCEL_TIME, PickUpFeatureUtil.formatTime(pickUpEvent.getActionGmtModified()));
        }
        if (MapUtils.isNotEmpty(extraInfo)) {
            pickUpEvent.setExtraInfo(JSON.toJSONString(extraInfo));
        }
    }

    private Date parseYtoDate(String timeStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        try {
            return sdf.parse(timeStr);
        } catch (ParseException e) {
            return null;
        }
    }

    private String getPhone(String text) {
        //  避免匹配到网点电话等情况
        String pre = "业务员电话：";
        String subText = text.substring(text.indexOf(pre) + pre.length());

        return PickUpCommonUtil.getPhone(subText);
    }


    private String getAction(String infoContent, WaybillPickUpDetailDO detailDO, Element root) {
        String action = BridgeSwitch.ytoPickUpAction.get(infoContent);
        if (StringUtil.isBlank(action)) {
            return null;
        }
        if (WaybillPickUpActionConstant.GOT.equals(action)) {
            String existWeightStr = PickUpFeatureUtil.getFromMulString(detailDO, PickUpConstants.TraceFeatureKey.CP_WEIGHT);
            // if (detailDO.getStatus().equals(PickUpDetailStatusEnum.GOT.getValue())) {
            if (StringUtil.isNotBlank(existWeightStr)) {
                int existWeight = Integer.parseInt(existWeightStr);
                int newWeight = getWeight(root);
                if (newWeight > 0 && newWeight != existWeight) {
                    return WaybillPickUpActionConstant.MODIFY_WEIGHT;
                } else {
                    return null;
                }
            }
        }
        return action;
    }

    private int getWeight(Element root) {
        if (root.element("weight") == null) {
            return 0;
        }
        String weightStr = root.element("weight").getText();
        return WeightUnitUtil.kg2g(weightStr);
    }
}
