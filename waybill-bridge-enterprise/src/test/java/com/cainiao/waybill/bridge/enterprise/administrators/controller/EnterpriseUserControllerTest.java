package com.cainiao.waybill.bridge.enterprise.administrators.controller;

import com.cainiao.waybill.bridge.enterprise.administrators.request.EnterpriseUserRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.ListAdminRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.UserDeptInfoRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.UserInfoRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.response.ListAdminResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.response.UserDeptInfoResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.response.UserInfoResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.response.UserTokenResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.service.DingTalkUserService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserInfoDTO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Matchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

public class EnterpriseUserControllerTest {
    private static final Long VALID_USER_ID = 1L;
    private static final Long INVALID_USER_ID = -1L;
    private static final Long ADMIN_ID = 1L;
    private static final Long NON_EXISTING_ADMIN_ID = -1L;
    @InjectMocks
    private EnterpriseUserController enterpriseUserController;
    @Mock
    private DingTalkUserService dingTalkUserService;
    private ListAdminRequest validRequest;
    private ListAdminRequest errorRequest;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        
        // 初始化请求对象
        validRequest = mock(ListAdminRequest.class);
        when(validRequest.getCorpId()).thenReturn("validCorpId");
        
        errorRequest = mock(ListAdminRequest.class);
        when(errorRequest.getCorpId()).thenReturn("errorCorpId");

    }

    /**
     * 模拟请求成功并返回管理员列表
     */
    @Test
    public void testGetAdminUserIdListSuccess() {
        List<ListAdminResponse> responseList = Collections.singletonList(new ListAdminResponse());
        when(dingTalkUserService.getAdminUserIdList(anyString())).thenReturn(responseList);
        EnterpriseBaseResult<List<ListAdminResponse>> result = enterpriseUserController.getAdminUserIdList(validRequest);
        assertTrue(result.isSuccess());
        assertEquals(responseList, result.getData());
    }

    /**
     * 模拟请求出现异常并返回错误信息
     */
    @Test
    public void testGetAdminUserIdListSystemError() {
        when(dingTalkUserService.getAdminUserIdList(anyString())).thenThrow(new RuntimeException("System error"));
        EnterpriseBaseResult<List<ListAdminResponse>> result = enterpriseUserController.getAdminUserIdList(errorRequest);
        assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), result.getErrorCode());
    }

    /**
     * 通过私有方法反射来调用getUserInfoByUnionId
     */
    private EnterpriseBaseResult<UserInfoResponse> invokePrivateGetUserInfoByUnionId(UserInfoRequest request) throws Exception {
        Method method = EnterpriseUserController.class.getDeclaredMethod("getUserInfoByUnionId", UserInfoRequest.class);
        method.setAccessible(true);
        return (EnterpriseBaseResult<UserInfoResponse>) method.invoke(enterpriseUserController, request);
    }

    // 设置反射方式调用私有方法
    private EnterpriseBaseResult<UserTokenResponse> invokePrivateGetUserTokenInfoByAuthCode(UserInfoRequest request) throws Exception {
        Method method = EnterpriseUserController.class.getDeclaredMethod("getUserTokenInfoByAuthCode", UserInfoRequest.class);
        method.setAccessible(true);
        return (EnterpriseBaseResult<UserTokenResponse>) method.invoke(enterpriseUserController, request);
    }


    // 使用反射调用私有方法addUser
    private EnterpriseBaseResult<Boolean> invokePrivateAddUser(EnterpriseUserRequest request) throws Exception {
        Method method = EnterpriseUserController.class.getDeclaredMethod("addUser", EnterpriseUserRequest.class);
        method.setAccessible(true);
        return (EnterpriseBaseResult<Boolean>) method.invoke(enterpriseUserController, request);
    }


    /**
     * 测试成功获取用户信息并返回成功结果
     */
    @Test(timeout = 5000)
    public void testGetUserBySearchValueSuccess() throws Exception {
        BridgePagingDTO<WaybillEnterpriseUserInfoDTO> userInfoDTOList = BridgePagingDTO.build(Collections.singletonList(new WaybillEnterpriseUserInfoDTO()), 1, 1, 10);
        when(dingTalkUserService.getUserBySearchValue(any())).thenReturn(userInfoDTOList);
        EnterpriseUserRequest enterpriseUserRequest = mock(EnterpriseUserRequest.class);
        when(enterpriseUserRequest.getSearchValue()).thenReturn("validSearchValue");
        when(enterpriseUserRequest.getCorpId()).thenReturn("validUserType");
        EnterpriseBaseResult<List<WaybillEnterpriseUserInfoDTO>> result = enterpriseUserController.getUserBySearchValue(enterpriseUserRequest);
        assertTrue(result.isSuccess());
        assertEquals(userInfoDTOList.getTableData(), result.getData());
    }

    /**
     * 测试获取用户信息过程中发生异常并记录日志后返回错误结果
     */
    public void testGetUserBySearchValueSystemError() {
        when(dingTalkUserService.getUserBySearchValue(any())).thenThrow(new RuntimeException("System error"));
        EnterpriseUserRequest enterpriseUserRequest = mock(EnterpriseUserRequest.class);
        when(enterpriseUserRequest.getSearchValue()).thenReturn("errorSearchValue");
        when(enterpriseUserRequest.getUserType()).thenReturn("errorUserType");
        EnterpriseBaseResult<List<WaybillEnterpriseUserInfoDTO>> result = enterpriseUserController.getUserBySearchValue(enterpriseUserRequest);
        assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), result.getErrorCode());
    }

    public void testGetUserBySearchValueValidateError() {
        when(dingTalkUserService.getUserBySearchValue(any())).thenThrow(new RuntimeException("System error"));
        EnterpriseUserRequest enterpriseUserRequest = mock(EnterpriseUserRequest.class);
        EnterpriseBaseResult<List<WaybillEnterpriseUserInfoDTO>> result = enterpriseUserController.getUserBySearchValue(enterpriseUserRequest);
        assertTrue(result.isSuccess());
        assertTrue(result.getData().isEmpty());
    }

    /**
     * 测试成功获取用户详情并返回
     */
    @Test
    public void testGetUserByIdSuccess() {
        WaybillEnterpriseUserInfoDTO userInfoDTO = new WaybillEnterpriseUserInfoDTO();
        when(dingTalkUserService.getById(VALID_USER_ID)).thenReturn(userInfoDTO);
        EnterpriseBaseResult<WaybillEnterpriseUserInfoDTO> result = enterpriseUserController.getUserById(VALID_USER_ID);
        assertTrue(result.isSuccess());
        assertEquals(userInfoDTO, result.getData());
    }

    /**
     * 测试获取用户详情失败，返回系统错误
     */
    @Test
    public void testGetUserByIdSystemError() {
        when(dingTalkUserService.getById(INVALID_USER_ID)).thenThrow(new RuntimeException("System error"));
        EnterpriseBaseResult<WaybillEnterpriseUserInfoDTO> result = enterpriseUserController.getUserById(INVALID_USER_ID);
        assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), result.getErrorCode());
    }

    /**
     * 测试：检查请求中是否包含有效的userId参数
     */
    @Test
    public void testGetUserByUserIdWithValidUserId() {
        WaybillEnterpriseUserInfoDTO validUserInfo = mock(WaybillEnterpriseUserInfoDTO.class);
        // 模拟方法返回有效用户信息
        when(dingTalkUserService.getByUserId(anyString(), anyString())).thenReturn(validUserInfo);
        // 调用getUserByUserId方法
        EnterpriseBaseResult<WaybillEnterpriseUserInfoDTO> result = enterpriseUserController.getUserByUserId(anyString(), anyString());
        // 验证返回结构正确且成功
        assertTrue(result.isSuccess());
        assertEquals(validUserInfo, result.getData());
    }

    /**
     * 测试：捕获异常并记录错误信息，返回错误结果
     */
    @Test
    public void testGetUserByUserIdHandleException() {
        // 模拟方法抛出异常
        when(dingTalkUserService.getByUserId(anyString(), anyString())).thenThrow(new RuntimeException("Exception occurred"));
        EnterpriseBaseResult<WaybillEnterpriseUserInfoDTO> result = enterpriseUserController.getUserByUserId(anyString(), anyString());
        // 验证捕获系统异常错误码返回
        assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), result.getErrorCode());
    }

    /**
     * 测试当移除管理员成功时，返回布尔值true
     */
    @Test
    public void testRemoveAdminByIdSuccess() {
        when(dingTalkUserService.removeAdminById(anyLong())).thenReturn(1);
        EnterpriseBaseResult<Boolean> result = enterpriseUserController.removeAdminById(ADMIN_ID);
        assertTrue(result.isSuccess());
        assertEquals(Boolean.TRUE, result.getData());
    }

    /**
     * 测试当移除管理员失败或抛出异常时，记录日志并返回企业错误
     */
    @Test
    public void testRemoveAdminByIdError() {
        when(dingTalkUserService.removeAdminById(anyLong())).thenThrow(new RuntimeException("System error"));
        EnterpriseBaseResult<Boolean> result = enterpriseUserController.removeAdminById(NON_EXISTING_ADMIN_ID);
        assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), result.getErrorCode());
    }

    /**
     * 通过反射调用私有方法removeAdminById
     */
    private EnterpriseBaseResult<Boolean> invokePrivateRemoveAdminById(Long id) throws Exception {
        Method method = EnterpriseUserController.class.getDeclaredMethod("removeAdminById", Long.class);
        method.setAccessible(true);
        return (EnterpriseBaseResult<Boolean>) method.invoke(enterpriseUserController, id);
    }

    /**
     * 测试根据userId移除管理员成功时，返回成功结果
     */
    @Test
    public void testRemoveAdminByUserIdSuccess() throws Exception {
        // 准备 mock 的返回结果
        when(dingTalkUserService.removeAdminByUserId(anyString())).thenReturn(1);
        EnterpriseBaseResult<Boolean> validUserId = enterpriseUserController.removeAdminByUserId("VALID_USER_ID");
    }

    /**
     * 测试根据userId移除管理员失败，异常捕获并返回错误结果
     */
    @Test(timeout = 5000) // 设置超时以防止可能的死循环
    public void testRemoveAdminByUserIdError() throws Exception {
        // 准备 mock 的异常抛出
        when(dingTalkUserService.removeAdminByUserId(anyString())).thenThrow(new RuntimeException("System error"));
        // 调用私有方法并获取返回结果
        EnterpriseBaseResult<Boolean> validUserId = enterpriseUserController.removeAdminByUserId("VALID_USER_ID");

    }
}