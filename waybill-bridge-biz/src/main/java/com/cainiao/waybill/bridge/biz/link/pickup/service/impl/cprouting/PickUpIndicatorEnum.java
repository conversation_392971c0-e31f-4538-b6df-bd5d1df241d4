package com.cainiao.waybill.bridge.biz.link.pickup.service.impl.cprouting;

import lombok.Getter;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpIndicatorEnum
 * @Description
 * @Date 2022/10/25 9:50 下午
 * @Version 1.0
 */
public enum PickUpIndicatorEnum {

    /**
     * 网点指标
     */
    ORG_INDICATOR("org"),

    /**
     * 寄件人手机号指标
     */
    SEND_MOBILE_INDICATOR("sendMobile"),

    /**
     * 四级地址指标
     */
    ADDR_TOWN_INDICATOR("addrTown"),

    ;

    @Getter
    private final String name;

    PickUpIndicatorEnum(String name) {
        this.name = name;
    }
}
