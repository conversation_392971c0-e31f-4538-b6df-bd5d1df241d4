package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * 通用配置表实体
 * <AUTHOR>
 */
public class WaybillCommonConfigDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   配置作用对象
     *
     *
     * @mbg.generated
     */
    private String scope;

    /**
     * Database Column Remarks:
     *   配置类型
     *
     *
     * @mbg.generated
     */
    private String configType;

    /**
     * Database Column Remarks:
     *   配置值
     *
     *
     * @mbg.generated
     */
    private String configValue;

    /**
     * Database Column Remarks:
     *   配置说明
     *
     *
     * @mbg.generated
     */
    private String remark;

    /**
     * Database Column Remarks:
     *   状态
     *
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * @return the value of waybill_common_config.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_common_config.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_common_config.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_common_config.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_common_config.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_common_config.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_common_config.scope
     *
     * @mbg.generated
     */
    public String getScope() {
        return scope;
    }

    /**
     *
     * @param scope the value for waybill_common_config.scope
     *
     * @mbg.generated
     */
    public void setScope(String scope) {
        this.scope = scope;
    }

    /**
     *
     * @return the value of waybill_common_config.config_type
     *
     * @mbg.generated
     */
    public String getConfigType() {
        return configType;
    }

    /**
     *
     * @param configType the value for waybill_common_config.config_type
     *
     * @mbg.generated
     */
    public void setConfigType(String configType) {
        this.configType = configType;
    }

    /**
     *
     * @return the value of waybill_common_config.config_value
     *
     * @mbg.generated
     */
    public String getConfigValue() {
        return configValue;
    }

    /**
     *
     * @param configValue the value for waybill_common_config.config_value
     *
     * @mbg.generated
     */
    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    /**
     *
     * @return the value of waybill_common_config.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @param remark the value for waybill_common_config.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     *
     * @return the value of waybill_common_config.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     *
     * @param status the value for waybill_common_config.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", scope=").append(scope);
        sb.append(", configType=").append(configType);
        sb.append(", configValue=").append(configValue);
        sb.append(", remark=").append(remark);
        sb.append(", status=").append(status);
        sb.append("]");
        return sb.toString();
    }
}