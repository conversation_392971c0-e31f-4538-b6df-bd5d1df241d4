package com.cainiao.waybill.bridge.model.dao;

import com.cainiao.waybill.bridge.model.domain.BranchSequenceDO;
import com.taobao.common.dao.persistence.exception.DAOException;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-26
 */
public interface BranchPickupSequenceDAO {

    /**
     * 通过 cpCode branchCode 查询 sequenceDO
     * @param cpCode
     * @param branchCode
     * @throws DAOException
     * @return
     */
    BranchSequenceDO queryByCpAndBranchCode(String cpCode, String branchCode) throws DAOException;

    /**
     * 插入网点序列值
     *
     * @param branchSequenceDO
     * @return
     * @throws DAOException
     */
    Integer insert(BranchSequenceDO branchSequenceDO) throws DAOException;

    /**
     * 网点序列值自增 1，并返回最新结果
     * @param cpCode
     * @param branchCode
     * @return
     * @throws DAOException
     */
    Long incrementAndGet(String cpCode, String branchCode) throws DAOException;

}
