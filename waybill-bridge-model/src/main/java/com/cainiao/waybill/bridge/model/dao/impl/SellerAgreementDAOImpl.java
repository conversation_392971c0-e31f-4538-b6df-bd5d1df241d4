package com.cainiao.waybill.bridge.model.dao.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.model.common.AbstractDAO;
import com.cainiao.waybill.bridge.model.dao.SellerAgreementDAO;
import com.cainiao.waybill.bridge.model.domain.SellerAgreementDO;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant;
import com.taobao.common.dao.persistence.exception.DAOException;
import com.taobao.tddl.client.sequence.exception.SequenceException;
import com.taobao.tddl.client.sequence.impl.GroupSequence;

/**
 * <AUTHOR>
 * @date 2017/06/12
 */
public class SellerAgreementDAOImpl extends AbstractDAO implements SellerAgreementDAO {

    @Resource
    private GroupSequence sellerAgreementSequence;

    @Override
    public SellerAgreementDO queryBySellerId(Long sellerId) throws DAOException {
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put("sellerId", sellerId);
        return (SellerAgreementDO)this.executeQueryForObject("sellerAgreement.selectBySellerId", paramMap, getDBRoute());
    }

    @Override
    public Integer insert(SellerAgreementDO sellerAgreementDO) throws DAOException {
        sellerAgreementDO.setId(nextId());
        return this.executeUpdate("sellerAgreement.insert", sellerAgreementDO, getDBRoute());
    }

    //通过TDDL获取唯一主键
    private Long nextId() throws DAOException {
        try {
            return sellerAgreementSequence.nextValue();
        } catch (SequenceException e) {
            throw new DAOException(WaybillErrorConstant.SystemError.TDDL_SEQUENCE_ERROR.getErrorMsg(), e);
        } catch (Throwable e) {
            throw new DAOException(e);
        }
    }
}
