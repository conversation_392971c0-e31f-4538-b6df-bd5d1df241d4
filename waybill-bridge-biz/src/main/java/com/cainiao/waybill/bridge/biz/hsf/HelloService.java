package com.cainiao.waybill.bridge.biz.hsf;

import com.alibaba.cainiao.stationplatform.client.domain.StationOrderDTO;
import com.alibaba.cainiao.stationplatform.client.domain.StationPageResultDTO;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.tunnel.TunnelException;
import com.cainiao.middleware.cnsms.client.Result;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpUserCreateRequest;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpQualityIndicatorDO;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import com.cainiao.waybill.common.privacy.dto.PrivacyEndBaseInfoDTO;
import com.taobao.logisticsdetail.common.domain.basic.LogisticsDetailDO;
import com.taobao.logisticsdetail.common.domain.result.SingleResult;
import org.apache.ibatis.annotations.Param;

import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * HSF服务接口
 *
 * <AUTHOR>
 */
public interface HelloService {

    /**
     * HSF服务的方法
     *
     * @param name
     * @return 调用HSF服务返回的结果
     */
    String sayHello(String name);

    List<Record> testOdps(String ds) throws IOException, TunnelException;

    void testLogs(String bizKey) throws Exception;

    void logReplay(List<String> bizKeyList) throws Exception;

    void ossBucketCreateTest(String bucketName);

    void ossPutTest(String bucketName, String fileName, String content);

    String ossGetTest(String bucketName, String fileName) throws IOException;

    void subLd(String mailNo, String cpCode, boolean cpCodeSwitch, boolean sendNameSw, boolean sendPhoneSw, boolean sendAddrSw,
               boolean reNameSw, boolean rePhoneSw, boolean reAddrSw);

    SingleResult<List<LogisticsDetailDO>> queryLd(String mailNo, String cpCode);

    boolean sendDingTextMsg(String hook, String content) throws IOException;

    void sendDingMarkdownMsg(String hook, String title, String content, List<String> atMobiles);

    void delayMsgTest();

    List<AddressDTO> cleanAddr(Date start, Date end) throws BridgeBaseException;

    PrivacyEndBaseInfoDTO queryEndName(String mailNo, String cpCode);

    Result<String> innerGetRecordFileDownloadUrl(String userId, String channelCode, String subId, String callId);

    StationPageResultDTO<List<StationOrderDTO>> queryStationOrderByMailNo(String mailNo);

    void insertIndicator(List<WaybillPickUpQualityIndicatorDO> list);

    void deleteIndicator(String bizDate, String cpCode, String indicatorName, String indicatorKey);

    void createBridgeUser(List<PickUpUserCreateRequest> request);

    void updateBridgeUser(PickUpUserCreateRequest request);

    void updateBridgeUser(Long id, PickUpUserCreateRequest request);

    void deleteBridgeUser(Long id);

    String packageNoTest();

    int batchModifyStrategyStatus(List<String> ids, String status, Long userId);

}
