package com.cainiao.waybill.bridge.model.dao;

import java.util.Date;
import java.util.List;

import com.cainiao.waybill.bridge.common.util.Page;
import com.cainiao.waybill.bridge.model.dao.bean.BranchSellerAddressRelationQuery;
import com.cainiao.waybill.bridge.model.domain.BranchSellerAddressDO;
import com.taobao.common.dao.persistence.exception.DAOException;

/**
 * Created by zhangsaiyong on 2017/4/18.
 */
public interface BranchSellerAddressDAO {

    /**
     * 查询商家已经绑定的生效的小件员列表
     * @param sellerId
     * @return
     * @throws DAOException
     */
    List<BranchSellerAddressDO> queryBySellerId(Long sellerId) throws DAOException;

    /**
     * 网点查询商家小件员绑定关系列表
     *
     * @param query
     * @param pageNo
     * @param pageSize
     * @return
     * @throws DAOException
     */
    Page<BranchSellerAddressDO> queryByBranch(BranchSellerAddressRelationQuery query, int pageNo, int pageSize) throws DAOException;

    /**
     * 根据CP+商家+地址进行查询（uk唯一约束条件）
     * @param cpCode
     * @param sellerId
     * @param divisionId
     * @param addressDetail
     * @return
     * @throws DAOException
     */
    BranchSellerAddressDO queryByCpSellerAddress(String cpCode, Long sellerId, Long divisionId, String addressDetail) throws DAOException;

    /**
     * 主键查询
     * @param id
     * @return
     * @throws DAOException
     */
    BranchSellerAddressDO queryById(Long id) throws DAOException;

    /**
     * 基本插入函数
     * @param branchSellerAddressDO
     * @return
     * @throws DAOException
     */
    Integer insert(BranchSellerAddressDO branchSellerAddressDO) throws DAOException;

    /**
     * 基本更新函数
     * @param branchSellerAddressDO
     * @return
     * @throws DAOException
     */
    Integer update(BranchSellerAddressDO branchSellerAddressDO) throws DAOException;


    /**
     * 分页查询商家小件员绑定关系列表
     * 条件1：大于给定的更新时间
     * 条件2：合作中的
     *
     * @param start
     * @param pageSize
     * @return
     * @throws DAOException
     */
    List<BranchSellerAddressDO> queryByGtModifyDateAndCooperation(Date modifyDate, int start, int pageSize) throws DAOException;

    /**
     * 统计查询商家小件员绑定关系的总数
     * 条件1：大于给定的更新时间
     * 条件2：合作中的
     *
     * @return
     * @throws DAOException
     */
    Integer countByGtModifyDateAndCooperation(Date modifyDate) throws DAOException ;

}
