package com.cainiao.waybill.bridge.common.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> zouping.fzp
 * @Classname HttpUtil
 * @Description
 * @Date 2023/7/31 4:15 下午
 * @Version 1.0
 */
public class HttpUtil {

    private static final Pattern PATTERN = Pattern.compile("^(http|https):\\/\\/.*");

    public static boolean isHttpAddress(String str){
        return PATTERN.matcher(str).matches();
    }

    public static void main(String[] args) {
        Long startTime = System.currentTimeMillis();
        try {
            // Flask 服务的 URL
            String urlString = "http://***********:8050/optimize";

            // 创建 URL 对象
            URL url = new URL(urlString);
            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "application/json");

            // 设置连接超时时间 (单位: 毫秒)
            connection.setConnectTimeout(5000);
            // 设置读取超时时间 (单位: 毫秒)
            connection.setReadTimeout(20000);

            // 构造请求内容
            String jsonInputString = "{\"ak\":\"LTAIukIV0YjuVEBa\",\"sk\":\"GeDLaHTCSZSXZeOlLU303ByC132xhJ\",\"order_channels\":\"JL\",\"input_table\":\"nature_day\",\"avail_cp\":[\"YTO-OPEN\",\"GUOGUO-OPEN\",\"YTO-WHG\",\"YTO-KD100\",\"STO-CP\"],\"model_version\":\"v1\",\"end_bizdate\":\"2024-10-16\",\"bizdate_span\":7,\"objective\":{\"item\":\"利润\",\"target\":\"max\"},\"constraint\":[{\"item\":\"进线率\",\"target\":\"max\",\"value\":1,\"scope\":\"global\"},{\"item\":\"进线率\",\"target\":\"max\",\"value\":1,\"scope\":\"route\"},{\"item\":\"揽收率\",\"target\":\"min\",\"value\":0.0,\"scope\":\"global\"}]}";
            System.out.println("开始执行");
            // 发送请求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonInputString.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            // 读取响应
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                System.out.println("Response: " + response.toString());
            }

        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            System.out.println("执行结束:"+(System.currentTimeMillis()-startTime));
        }
    }


    public String testAlgorithm(String request, String urlPath, int connectTimeout, int readTimeout){
        Long startTime = System.currentTimeMillis();
        try {
            // Flask 服务的 URL
            String urlString;
            if(StringUtils.isBlank(urlPath)){
                urlString = "http://localhost:8050/optimize";
            }else {
                urlString = urlPath;
            }

            // 创建 URL 对象
            URL url = new URL(urlString);
            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setDoOutput(true);
            connection.setRequestProperty("Content-Type", "application/json");

            if (connectTimeout == 0){
                // 设置连接超时时间 (单位: 毫秒)
                connection.setConnectTimeout(5000);
            }else {
                connection.setConnectTimeout(connectTimeout);
            }
            if(readTimeout == 0){
                // 设置读取超时时间 (单位: 毫秒)
                connection.setReadTimeout(20000);
            }else {
                connection.setReadTimeout(readTimeout);
            }

            String jsonInputString;
            // 构造请求内容
            if(StringUtils.isNotBlank(request)){
                jsonInputString = request;
            }else {
                jsonInputString = "{\"ak\":\"LTAIukIV0YjuVEBa\",\"sk\":\"GeDLaHTCSZSXZeOlLU303ByC132xhJ\",\"order_channels\":\"JL\",\"input_table\":\"nature_day\",\"avail_cp\":[\"YTO-OPEN\",\"GUOGUO-OPEN\",\"YTO-WHG\",\"YTO-KD100\",\"STO-CP\"],\"model_version\":\"v1\",\"end_bizdate\":\"2024-10-16\",\"bizdate_span\":7,\"objective\":{\"item\":\"利润\",\"target\":\"max\"},\"constraint\":[{\"item\":\"进线率\",\"target\":\"max\",\"value\":1,\"scope\":\"global\"},{\"item\":\"进线率\",\"target\":\"max\",\"value\":1,\"scope\":\"route\"},{\"item\":\"揽收率\",\"target\":\"min\",\"value\":0.0,\"scope\":\"global\"}]}";
            }
            System.out.println("开始执行");
            // 发送请求
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonInputString.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            // 读取响应
            try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), "utf-8"))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                System.out.println("Response: " + response.toString());
                return response.toString();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            System.out.println("执行结束:"+(System.currentTimeMillis()-startTime));
        }
        return "无响应";
    }


}
