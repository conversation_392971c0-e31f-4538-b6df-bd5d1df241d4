package com.cainiao.waybill.bridge.common.label.dto;


import com.cainiao.waybill.bridge.common.dto.BaseDTO;

import java.util.List;

/**
 * Created by guochao.tgc on 2016/6/3.
 */
public class PackageInfoDTO extends BaseDTO {
    private static final long serialVersionUID = -6707576969667513126L;

    /**
     * 包裹Id
     */
    private String            Id;
    /**
     * 包裹商品信息
     */
    private List<Item> Items;
    /**
     * 包裹重量
     */
    private Long              weight;
    /**
     * 包裹体积
     */
    private Long              volume;


    public PackageInfoDTO() {}

    public PackageInfoDTO(String id, List<Item> items, Long weight, Long volume) {
        Id = id;
        Items = items;
        this.weight = weight;
        this.volume = volume;
    }

    public String getId() {
        return Id;
    }

    public void setId(String id) {
        Id = id;
    }

    public List<Item> getItems() {
        return Items;
    }

    public void setItems(List<Item> items) {
        Items = items;
    }

    public Long getWeight() {
        return weight;
    }

    public void setWeight(Long weight) {
        this.weight = weight;
    }

    public Long getVolume() {
        return volume;
    }

    public void setVolume(Long volume) {
        this.volume = volume;
    }
}
