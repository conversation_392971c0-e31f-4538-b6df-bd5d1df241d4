package com.cainiao.waybill.bridge.client2.consignrisk.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/16 下午8:34
 */
@Data
public class WaybillConsignRiskSegmentQueryRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 第几页，从1开始
     * 默认第一页
     */
    private Integer currentPage = 1;
    /**
     * 每页大小
     * 默认20条
     */
    private Integer pageSize = 20;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * cpCode
     */
    private String cpCode;

    /**
     * 号段渠道
     */
    private String channel;

    /**
     * 状态
     */
    private String status;
}
