package com.cainiao.waybill.bridge.biz.label.manager;

import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;

/**
 * <AUTHOR>
 * @date 2017/06/12
 */
public interface SellerAgreementManager {

    /**
     * 检查商家协议是否有效期内
     * @param sellerId
     * @return
     * @throws BridgeBaseException
     */
    Boolean isSellerAgreementValid(Long sellerId) throws BridgeBaseException;

    /**
     * 签署商家协议
     *
     * @param sellerId
     * @return
     * @throws BridgeBaseException
     */
    void signSellerAgreement(Long sellerId) throws BridgeBaseException;
}
