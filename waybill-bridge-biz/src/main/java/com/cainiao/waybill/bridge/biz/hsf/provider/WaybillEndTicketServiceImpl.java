package com.cainiao.waybill.bridge.biz.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastvalidator.core.FastValidatorUtils;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeTicketDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeTicketParam;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeTicketMapper;
import com.cainiao.waybill.bridge.ticket.api.WaybillEndTicketService;
import com.cainiao.waybill.bridge.ticket.api.enums.TicketStatusEnum;
import com.cainiao.waybill.bridge.ticket.api.enums.TicketOperatorTypeEnum;
import com.cainiao.waybill.bridge.ticket.api.enums.TicketTypeEnum;
import com.cainiao.waybill.bridge.ticket.api.request.WaybillTicketCreateRequest;
import com.cainiao.waybill.bridge.ticket.api.request.WaybillTicketHurryRequest;
import com.cainiao.waybill.bridge.ticket.api.request.WaybillTicketReplyRequest;
import com.cainiao.waybill.bridge.ticket.api.response.WaybillTicketResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.ConstraintViolation;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 对接末端工单信息 保存在内部数据库
 * <AUTHOR>
 */
@HSFProvider(serviceInterface = WaybillEndTicketService.class)
public class WaybillEndTicketServiceImpl implements WaybillEndTicketService {

    @Autowired
    private WaybillBridgeTicketMapper waybillBridgeTicketMapper;

    private static final String SECRET = "waybill_bridge_ticket_test";

    @Override
    public String saveCreateTicket(WaybillTicketCreateRequest createRequest) {

        try{

            //参数校验
            Set<ConstraintViolation<WaybillTicketCreateRequest>> validationSet = FastValidatorUtils.validate(createRequest);
            if (CollectionUtils.isNotEmpty(validationSet)){
                return "saveCreateTicket: 参数校验未通过";
            }

            //如果同一个运单号,有相同类型的工单在进行,不让创建新的工单
            WaybillBridgeTicketParam param = new WaybillBridgeTicketParam();
            WaybillBridgeTicketParam.Criteria criteria = param.createCriteria();
            List<String> ticketStatusList = Arrays.asList(TicketStatusEnum.STATUS_1.getContent(),TicketStatusEnum.STATUS_2.getContent());
            criteria.andMailNoEqualTo(createRequest.getMailNo())
                    .andTicketTypeEqualTo(TicketTypeEnum.getContentByCode(createRequest.getTicketType()))
                    .andTicketStatusIn(ticketStatusList);
            List<WaybillBridgeTicketDO> ticketList = waybillBridgeTicketMapper.selectByExample(param);
            if (CollectionUtils.isNotEmpty(ticketList)){
                return "saveCreateTicket: 运单号"+createRequest.getMailNo()+"有同类型工单还未完结";
            }

            WaybillBridgeTicketDO ticketDO = new WaybillBridgeTicketDO();
            ticketDO.setMailNo(createRequest.getMailNo());
            ticketDO.setTicketType(TicketTypeEnum.getContentByCode(createRequest.getTicketType()));
            ticketDO.setOperatorType(TicketOperatorTypeEnum.getContentByCode(createRequest.getOperatorType()));
            ticketDO.setIntoBoxTime(createRequest.getIntoBoxTime());
            ticketDO.setTicketCreateContent(createRequest.getTicketContent());
            if(CollectionUtils.isNotEmpty(createRequest.getTicketFileNames())){
                ticketDO.setTicketFiles(createRequest.getTicketFileNames().toString());
            }
            ticketDO.setFeature(createRequest.getFeature());
            ticketDO.setTicketStatus(TicketStatusEnum.STATUS_1.getContent());
            ticketDO.setHurryMark(Byte.valueOf("0"));
            ticketDO.setGmtCreate(new Date());
            ticketDO.setGmtModified(new Date());
            return (waybillBridgeTicketMapper.insert(ticketDO) < 1) ? "saveCreateTicket: 未更新工单信息至数据库":"";

        }catch (Throwable e){
            return "saveCreateTicket: "+e.getMessage();
        }

    }

    @Override
    public String saveReplyTicket(WaybillTicketReplyRequest replyRequest) {

        try{
            String ticketId = replyRequest.getTicketId();
            String mailNo = replyRequest.getMailNo();
            if (StringUtils.isBlank(ticketId) || StringUtils.isBlank(mailNo)){
                return "saveReplyTicket: 工单id或运单号为空";
            }

            WaybillBridgeTicketParam param = new WaybillBridgeTicketParam();
            WaybillBridgeTicketParam.Criteria criteria = param.createCriteria();
            criteria.andMailNoEqualTo(mailNo)
                    .andIdEqualTo(Long.valueOf(ticketId));
            List<WaybillBridgeTicketDO> ticketList = waybillBridgeTicketMapper.selectByExample(param);
            if(CollectionUtils.isEmpty(ticketList)){
                return "saveReplyTicket: 运单号"+mailNo+"对应的工单不存在";
            }

            List<WaybillBridgeTicketDO> ticketDOList = ticketList.stream()
                    .filter(ticket -> TicketStatusEnum.STATUS_1.getContent().equals(ticket.getTicketStatus())
                            || TicketStatusEnum.STATUS_2.getContent().equals(ticket.getTicketStatus()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(ticketDOList)){
                return "saveReplyTicket: 运单号"+mailNo+"对应的工单已取消或已完结";
            }

            WaybillBridgeTicketDO ticketDO = ticketDOList.get(0);
            ticketDO.setTicketStatus(TicketStatusEnum.getContentByStatus(replyRequest.getTicketStatus()));
            ticketDO.setTicketRespContent(replyRequest.getTicketRespContent());
            ticketDO.setFeature(replyRequest.getFeature());
            ticketDO.setGmtModified(new Date());
            return (waybillBridgeTicketMapper.updateByPrimaryKeySelective(ticketDO) < 1) ? "saveReplyTicket: 未更新工单信息至数据库":"";

        }catch (Throwable e){
            return "saveReplyTicket: "+e.getMessage();
        }


    }

    @Override
    public String hurryTicket(WaybillTicketHurryRequest hurryRequest) {

        try{

            //参数校验
            Set<ConstraintViolation<WaybillTicketHurryRequest>> validationSet = FastValidatorUtils.validate(hurryRequest);
            if (CollectionUtils.isNotEmpty(validationSet)){
                return "hurryTicket: 参数校验未通过";
            }

            WaybillBridgeTicketParam param = new WaybillBridgeTicketParam();
            WaybillBridgeTicketParam.Criteria criteria = param.createCriteria();
            criteria.andMailNoEqualTo(hurryRequest.getMailNo());
            List<WaybillBridgeTicketDO> ticketList = waybillBridgeTicketMapper.selectByExample(param);
            if (CollectionUtils.isEmpty(ticketList)){
                return "hurryTicket: 运单号"+hurryRequest.getMailNo()+"对应的工单不存在";
            }
            List<WaybillBridgeTicketDO> ticketDOList = ticketList.stream()
                    .filter(ticket -> ticket.getOperatorType().equals(TicketOperatorTypeEnum.getContentByCode(hurryRequest.getOperatorType())))
                    .filter(ticket -> ticket.getTicketType().equals(TicketTypeEnum.getContentByCode(hurryRequest.getTicketType())))
                    .filter(ticket -> TicketStatusEnum.STATUS_1.getContent().equals(ticket.getTicketStatus())
                            || TicketStatusEnum.STATUS_2.getContent().equals(ticket.getTicketStatus()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(ticketDOList)){
                return "hurryTicket: 运单号"+hurryRequest.getMailNo()+"对应的工单已取消或已完结";
            }

            WaybillBridgeTicketDO ticketDO = ticketDOList.get(0);
            if (ticketDO.getHurryMark() != null && "1".equals(String.valueOf(ticketDO.getHurryMark()))){
                return "hurryTicket: 运单号"+hurryRequest.getMailNo()+"重复催单";
            }
            ticketDO.setHurryMark(Byte.valueOf("1"));
            ticketDO.setGmtModified(new Date());
            return (waybillBridgeTicketMapper.updateByPrimaryKeySelective(ticketDO) < 1) ? "hurryTicket: 未更新工单信息至数据库":"";

        }catch (Throwable e){
            return "hurryTicket: "+e.getMessage();
        }
    }

    @Override
    public List<WaybillTicketResponse> selectTicket(String mailNo) {

        List<WaybillTicketResponse> responseList = new ArrayList<>();
        if (StringUtils.isBlank(mailNo)){
            return responseList;
        }

        WaybillBridgeTicketParam param = new WaybillBridgeTicketParam();
        WaybillBridgeTicketParam.Criteria criteria = param.createCriteria();
        criteria.andMailNoEqualTo(mailNo)
                .andTicketStatusNotEqualTo(TicketStatusEnum.STATUS_0.getContent());
        List<WaybillBridgeTicketDO> ticketList = waybillBridgeTicketMapper.selectByExample(param);
        for (WaybillBridgeTicketDO ticketDO : ticketList) {
            WaybillTicketResponse ticketResponse = new WaybillTicketResponse();
            BeanUtils.copyProperties(ticketDO,ticketResponse);
            responseList.add(ticketResponse);
        }
        return responseList;

    }

    @Override
    public void clearTestTicket(String authSecret) {

        if (StringUtils.isBlank(authSecret) || !SECRET.equals(authSecret)){
            return;
        }

        //清除单号或者内容上带测试字样的数据
        WaybillBridgeTicketParam param1 = new WaybillBridgeTicketParam();
        WaybillBridgeTicketParam.Criteria criteria1 = param1.createCriteria();
        criteria1.andMailNoLike("test%");
        waybillBridgeTicketMapper.deleteByExample(param1);

        WaybillBridgeTicketParam param2 = new WaybillBridgeTicketParam();
        WaybillBridgeTicketParam.Criteria criteria2 = param2.createCriteria();
        criteria2.andTicketCreateContentLike("%测试%");
        waybillBridgeTicketMapper.deleteByExample(param2);


    }
}
