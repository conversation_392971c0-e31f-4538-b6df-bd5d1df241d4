package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
/**
 *
 * <AUTHOR>
 */
public class WaybillPickUpAttributeDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   cpCode
     *
     *
     * @mbg.generated
     */
    private String cpCode;

    /**
     * Database Column Remarks:
     *   运单号
     *
     *
     * @mbg.generated
     */
    private String mailNo;

    /**
     * Database Column Remarks:
     *   属性key，枚举字段：服务、商品
     *
     *
     * @mbg.generated
     */
    private String attributeName;

    /**
     * Database Column Remarks:
     *   属性value (DTO json格式)
     *
     *
     * @mbg.generated
     */
    private String attributeValue;

    /**
     *
     * @return the value of waybill_pick_up_attribute.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_pick_up_attribute.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_pick_up_attribute.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_pick_up_attribute.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_pick_up_attribute.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_pick_up_attribute.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_pick_up_attribute.cp_code
     *
     * @mbg.generated
     */
    public String getCpCode() {
        return cpCode;
    }

    /**
     *
     * @param cpCode the value for waybill_pick_up_attribute.cp_code
     *
     * @mbg.generated
     */
    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    /**
     *
     * @return the value of waybill_pick_up_attribute.mail_no
     *
     * @mbg.generated
     */
    public String getMailNo() {
        return mailNo;
    }

    /**
     *
     * @param mailNo the value for waybill_pick_up_attribute.mail_no
     *
     * @mbg.generated
     */
    public void setMailNo(String mailNo) {
        this.mailNo = mailNo;
    }

    /**
     *
     * @return the value of waybill_pick_up_attribute.attribute_name
     *
     * @mbg.generated
     */
    public String getAttributeName() {
        return attributeName;
    }

    /**
     *
     * @param attributeName the value for waybill_pick_up_attribute.attribute_name
     *
     * @mbg.generated
     */
    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    /**
     *
     * @return the value of waybill_pick_up_attribute.attribute_value
     *
     * @mbg.generated
     */
    public String getAttributeValue() {
        return attributeValue;
    }

    /**
     *
     * @param attributeValue the value for waybill_pick_up_attribute.attribute_value
     *
     * @mbg.generated
     */
    public void setAttributeValue(String attributeValue) {
        this.attributeValue = attributeValue;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", cpCode=").append(cpCode);
        sb.append(", mailNo=").append(mailNo);
        sb.append(", attributeName=").append(attributeName);
        sb.append(", attributeValue=").append(attributeValue);
        sb.append("]");
        return sb.toString();
    }
}
