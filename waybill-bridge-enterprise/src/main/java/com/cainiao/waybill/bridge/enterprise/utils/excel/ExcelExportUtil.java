package com.cainiao.waybill.bridge.enterprise.utils.excel;

import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.google.common.collect.Lists;
import com.taobao.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ReflectionUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class ExcelExportUtil {

    /**
     * 组装list对象为excel数据，
     * 注：依赖"T"对象属性的定义顺序。作为excel的列
     */
    public static <T> Workbook build(Collection<T> dataList, String fileName, Class<T> tClass) {
        if (CollectionUtil.isEmpty(dataList)) {
            dataList = Lists.newArrayList();
        }
        Workbook wb = new XSSFWorkbook();
        Sheet s = wb.createSheet(fileName);
        s.setDefaultColumnWidth(16);

        //头的字体格式
        CellStyle cs = wb.createCellStyle();
        DataFormat df = wb.createDataFormat();
        Font f = wb.createFont();
        f.setFontHeightInPoints((short)12);
        f.setColor(IndexedColors.BLACK.getIndex());
        f.setBold(true);
        cs.setFont(f);
        cs.setBorderBottom(BorderStyle.THIN);
        cs.setDataFormat(df.getFormat("text"));

        //文本的格式
        CellStyle textCs = wb.createCellStyle();
        Font f1 = wb.createFont();
        f1.setFontHeightInPoints((short)12);
        f1.setColor(IndexedColors.BLACK.getIndex());
        textCs.setFont(f1);
        textCs.setDataFormat(df.getFormat("text"));

        //浮点数格式
        CellStyle doubleCs = wb.createCellStyle();
        Font f3 = wb.createFont();
        f3.setFontHeightInPoints((short)12);
        f3.setColor(IndexedColors.BLACK.getIndex());
        doubleCs.setFont(f3);
        doubleCs.setDataFormat(df.getFormat("0.0"));
        //加上头
        int rowNum = 0;
        //第一行头
        Row headRow = s.createRow(rowNum++);
        List<Field> fields = Lists.newArrayList();
        //fields的顺序为，属性的定义顺序
        ReflectionUtils.doWithFields(tClass, fields::add,
            field -> field.isAnnotationPresent(ExcelHeader.class));
        for (int i = 0; i < fields.size(); i++) {
            Field field = fields.get(i);
            Cell c = headRow.createCell(i);
            c.setCellStyle(cs);
            ExcelHeader excelHeader = field.getAnnotation(ExcelHeader.class);
            if (excelHeader != null) {
                c.setCellValue(excelHeader.value());
            }
        }
        for (T item : dataList) {
            Row row = s.createRow(rowNum++);
            for (int i = 0; i < fields.size(); i++) {
                Field field = fields.get(i);
                PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(tClass, field.getName());
                try {
                    Object obj = propertyDescriptor.getReadMethod().invoke(item);
                    if (obj == null || StringUtils.isBlank(obj.toString())) {
                        obj = "-";
                    }
                    row.createCell(i).setCellValue(obj.toString());
                } catch (IllegalAccessException | InvocationTargetException e) {
                    log.error("excel_export_error, class:{}, method:{}",
                        item.getClass(), field.getName(), e);
                    throw new BridgeBusinessException("excel_export_error", "excel导出失败");
                }
            }
        }
        return wb;
    }

}
