package com.cainiao.waybill.bridge.web.pickup.vo;

import com.cainiao.waybill.bridge.biz.utils.excel.ExcelHeader;
import lombok.Data;

/**
 * cp揽收数据报告
 * <AUTHOR>
 * @date 2025/2/21 17:42
 **/
@Data
public class PickUpCpCollectRobotVO {

    /**
     * CP产品
     */
    @ExcelHeader("CP产品")
    private String cpProduct;

    /**
     * 应揽收量
     */
    @ExcelHeader("应揽收量")
    private Integer shouldGotNum;

    /**
     * 已揽量
     */
    @ExcelHeader("已揽量")
    private Integer gotedNum;

    /**
     * 应揽揽收率
     */
    @ExcelHeader("应揽收率")
    private String shouldGotRate;

    /**
     * 应揽及时揽收率
     */
    @ExcelHeader("应揽及时揽率")
    private String shouldGotInTimeGotRate;


}
