package com.cainiao.waybill.bridge.enterprise.administrators.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.administrators.request.EnterpriseSettleAccountRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.service.EnterpriseSettleAccountService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.UserContext;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseSettleAccountDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 月结账号管理
 *
 * <AUTHOR>
 * @date 2025-04-23 17:23:47
 */
@RestController
@RequestMapping(value = "/enterprise/settleAccount")
public class EnterpriseSettleAccountController {
    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private EnterpriseSettleAccountService settleAccountService;


    /**
     * 列表查询（分页）
     */
    @PostMapping("/page")
    public EnterpriseBaseResult<BridgePagingDTO<WaybillEnterpriseSettleAccountDTO>> page(@RequestBody EnterpriseSettleAccountRequest request) {
        try {
            LOGGER.info("page request:{}", JSONObject.toJSONString(request));
            BridgePagingDTO<WaybillEnterpriseSettleAccountDTO> result = settleAccountService.pageList(request);
            return EnterpriseBaseResult.success(result);
        } catch (EnterpriseException e) {
            LOGGER.error("page enterprise settle account error, request:{}", JSON.toJSONString(request), e);
            return EnterpriseBaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }
    }

    /**
     * 列表查询（分页）
     */
    @PostMapping("/list")
    public EnterpriseBaseResult<List<WaybillEnterpriseSettleAccountDTO>> list(@RequestBody EnterpriseSettleAccountRequest request) {
        try {
            LOGGER.info("list request:{}", JSONObject.toJSONString(request));
            List<WaybillEnterpriseSettleAccountDTO> list = settleAccountService.list(request);
            return EnterpriseBaseResult.success(list);
        } catch (EnterpriseException e) {
            LOGGER.error("list enterprise settle account error, request:{}", JSON.toJSONString(request), e);
            return EnterpriseBaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }
    }

    /**
     * 创建
     */
    @RequestMapping("/add")
    public EnterpriseBaseResult<Boolean> add(@RequestBody EnterpriseSettleAccountRequest request) {
        try {
            LOGGER.info("add request :{}", JSONObject.toJSONString(request));
            boolean isSuccess = settleAccountService.addSettleAccount(request) == 1;
            return EnterpriseBaseResult.success(isSuccess);
        } catch (EnterpriseException e) {
            LOGGER.error("add enterprise settle account error, request:{}", JSON.toJSONString(request), e);
            return EnterpriseBaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }

    }

    /**
     * 修改
     */
    @RequestMapping("/edit")
    public EnterpriseBaseResult<Boolean> edit(@RequestBody EnterpriseSettleAccountRequest request) {
        LOGGER.info("edit request:{}", JSONObject.toJSONString(request));
        try {
            request.setCorpId(UserContext.getUser().getCorpId());
            boolean isSuccess = settleAccountService.editSettleAccount(request) == 1;
            return EnterpriseBaseResult.success(isSuccess);
        } catch (EnterpriseException e) {
            LOGGER.error("edi enterprise settle account error, request:{}", JSON.toJSONString(request), e);
            return EnterpriseBaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }

    }
    /**
     * 删除
     */
    @RequestMapping("/remove")
    @Deprecated
    public EnterpriseBaseResult<Boolean> remove(@RequestParam("id") Long id) {
        LOGGER.info("remove request:{}", id);
        boolean isSuccess = settleAccountService.deleteById(id) == 1;
        return EnterpriseBaseResult.success(isSuccess);
    }

    /**
     * 删除
     */
    @GetMapping("/removeByWaybillAccountId")
    public EnterpriseBaseResult<Boolean> removeByWaybillAccountId(@RequestParam("waybillAccountId") String waybillAccountId) {
        LOGGER.info("remove request:{}", waybillAccountId);
        if (StringUtils.isEmpty(waybillAccountId)) {
            return EnterpriseBaseResult.success(false);
        }
        int removeResult = settleAccountService.removeByWaybillAccountId(waybillAccountId, UserContext.getUser().getCorpId());
        return EnterpriseBaseResult.success(removeResult == 1);
    }
}