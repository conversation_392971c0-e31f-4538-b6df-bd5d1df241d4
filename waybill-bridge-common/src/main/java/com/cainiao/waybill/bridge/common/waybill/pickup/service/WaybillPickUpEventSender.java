package com.cainiao.waybill.bridge.common.waybill.pickup.service;

import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;

/**
 * 生命周期事件发送类
 * 向上层逻辑封装消息发送逻辑
 *
 * <AUTHOR>
 * @date 2021-06-16
 */
public interface WaybillPickUpEventSender {

    /**
     * 发送事件
     *
     * @param event 事件
     * @return 发送成功或者失败
     */
    BaseResultDTO<Void> send(WaybillPickUpEvent event);
}
