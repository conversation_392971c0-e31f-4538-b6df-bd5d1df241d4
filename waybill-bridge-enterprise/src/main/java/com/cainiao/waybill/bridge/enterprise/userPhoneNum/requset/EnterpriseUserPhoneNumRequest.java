package com.cainiao.waybill.bridge.enterprise.userPhoneNum.requset;

import com.cainiao.waybill.bridge.enterprise.utils.excel.ExcelHeader;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/14
 **/
@Data
public class EnterpriseUserPhoneNumRequest {

    /**
     * 用户id
     */
    @ExcelHeader(value = "员工ID")
    private String userId;

    /**
     * 用户名称
     */
    @ExcelHeader(value = "姓名")
    private String userName;

    /**
     * 手机号
     */
    @ExcelHeader(value = "手机号码")
    private String phoneNum;

    /**
     * 错误原因
     */
    @ExcelHeader(value = "错误原因", check = false)
    private String errorInfo;

    /**
     * 企业ID
     */
    private String corpId;
}
