package com.cainiao.waybill.bridge.enterprise.administrators.controller;

import com.cainiao.waybill.bridge.enterprise.administrators.request.User;
import com.cainiao.waybill.bridge.enterprise.administrators.service.DingTalkLocationService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.UserContext;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseLocationDTO;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Mockito;
import org.mockito.runners.MockitoJUnitRunner;
import org.testng.collections.Lists;

import static org.junit.Assert.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

public class EnterpriseLocationControllerTest {

    @InjectMocks
    private EnterpriseLocationController enterpriseLocationController;
    @Mock
    private DingTalkLocationService dingTalkLocationService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 初始化测试数据
        validRequest = new LocationRequest();
        validRequest.setCurrentPage(1);
        validRequest.setPageSize(10);
        validRequest.setCorpId("test-corp-id");
        
        errorRequest = new LocationRequest();
        errorRequest.setCurrentPage(1);
        errorRequest.setPageSize(10);
        errorRequest.setCorpId("error-corp-id");
    }

    /**
     * 模拟正常情况下分页查询场地的方法
     */
    @Test
    public void testPageSuccess() {
        BridgePagingDTO<WaybillEnterpriseLocationDTO> pagingResult = new BridgePagingDTO<>();
        pagingResult.setTableData(Lists.newArrayList());
        when(dingTalkLocationService.pageList(any())).thenReturn(pagingResult);
        EnterpriseBaseResult<BridgePagingDTO<WaybillEnterpriseLocationDTO>> result = enterpriseLocationController.page(any());
        assertTrue(result.isSuccess());
        assertEquals(pagingResult, result.getData());
    }

    /**
     * 模拟当服务层抛出异常的情况
     */
    @Test
    public void testPageSystemError() throws Exception {
        when(dingTalkLocationService.pageList(any(LocationRequest.class))).thenThrow(new RuntimeException("System error"));
        EnterpriseBaseResult<BridgePagingDTO<WaybillEnterpriseLocationDTO>> result = invokePrivatePage(errorRequest);
        assertFalse(result.isSuccess());
        when(dingTalkLocationService.pageList(any())).thenThrow(new RuntimeException("SYSTEM_ERROR"));
        EnterpriseBaseResult<BridgePagingDTO<WaybillEnterpriseLocationDTO>> result = enterpriseLocationController.page(any());
        assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), result.getErrorCode());
        assertEquals("System error", result.getErrorMsg());
        // No need to verify log messages as they do not return values
    }

    /**
     * 测试locationId为空导致参数错误的情况
     */
    @Test
    public void testDeleteByLocationIdParamError() throws Exception {
        String locationId = null;
        EnterpriseBaseResult<Boolean> result = enterpriseLocationController.deleteByLocationId(locationId);
        // 验证参数错误返回
        assertFalse(result.isSuccess());
        assertEquals(EnterpriseErrorEnum.PARAM_ERROR.code(), result.getErrorCode());
        assertEquals("locationId不能为空", result.getErrorMsg());
    }

    /**
     * 测试删除操作成功返回成功状态的情况
     */
    @Test
    public void testDeleteByLocationIdSuccess() {
        String locationId = "locationId";
        // Mock UserContext静态方法返回corpId
        User testUser = Mockito.mock(User.class);
        when(testUser.getCorpId()).thenReturn("testCorpId");
        UserContext.setUser(testUser);
        // 假设成功删除，返回1
        when(dingTalkLocationService.deleteByLocationId(locationId, "testCorpId")).thenReturn(1);
        EnterpriseBaseResult<Boolean> result = enterpriseLocationController.deleteByLocationId(locationId);
        // 验证删除成功
        assertTrue(result.isSuccess());
        assertEquals(true, result.getData());
    }

    /**
     * 测试locationId为空导致参数错误的情况
     */
    @Test
    public void testGetLocationByLocationIdParamError() throws Exception {
        String locationId = null;
        EnterpriseBaseResult<WaybillEnterpriseLocationDTO> result = enterpriseLocationController.getLocationByLocationId(locationId);
        // 验证参数错误返回
        assertFalse(result.isSuccess());
        assertEquals(EnterpriseErrorEnum.PARAM_ERROR.code(), result.getErrorCode());
        assertEquals("locationId不能为空", result.getErrorMsg());
    }

    /**
     * 模拟服务层抛出异常的情况
     * 注意：根据Controller实现，deleteById方法并没有try-catch，所以异常会直接抛出
     * 使用反射时，异常会被包装在InvocationTargetException中
     * 测试删除操作成功返回成功状态的情况
     */
    @Test
    public void testDeleteByIdException() throws Exception {
        // 模拟服务层抛出异常
        when(dingTalkLocationService.deleteLocationById(VALID_ID)).thenThrow(new RuntimeException("System error"));

        try {
            // 调用私有方法，期望抛出异常
            invokePrivateDeleteById(VALID_ID);
            fail("Expected exception to be thrown");
        } catch (Exception e) {
            // 反射调用会包装异常在InvocationTargetException中
            assertTrue("Exception should be InvocationTargetException or RuntimeException", 
                e instanceof java.lang.reflect.InvocationTargetException || e instanceof RuntimeException);
            
            if (e instanceof java.lang.reflect.InvocationTargetException) {
                Throwable cause = e.getCause();
                assertTrue("Cause should be RuntimeException", cause instanceof RuntimeException);
                assertEquals("System error", cause.getMessage());
            } else if (e instanceof RuntimeException) {
                assertEquals("System error", e.getMessage());
            }
        }
    @Test
    public void testGetLocationByLocationIdSuccess() {
        String locationId = "locationId";
        // Mock UserContext静态方法返回corpId
        User testUser = Mockito.mock(User.class);
        when(testUser.getCorpId()).thenReturn("testCorpId");
        UserContext.setUser(testUser);
        // 假设成功删除，返回1
        when(dingTalkLocationService.getLocationByLocationId(locationId, "testCorpId")).thenReturn(new WaybillEnterpriseLocationDTO());
        EnterpriseBaseResult<WaybillEnterpriseLocationDTO> result = enterpriseLocationController.getLocationByLocationId(locationId);
        // 验证删除成功
        assertTrue(result.isSuccess());
    }
}