package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class WaybillBridgeEnterpriseOrderDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   企业id
     *
     *
     * @mbg.generated
     */
    private String corpId;

    /**
     * Database Column Remarks:
     *   订单业务号（业务唯一）
     *
     *
     * @mbg.generated
     */
    private String outerOrderCode;

    /**
     * Database Column Remarks:
     *   打印机编码
     *
     *
     * @mbg.generated
     */
    private String printerId;

    /**
     * Database Column Remarks:
     *   运单号
     *
     *
     * @mbg.generated
     */
    private String waybillCode;

    /**
     * Database Column Remarks:
     *   运力服务提供方
     *
     *
     * @mbg.generated
     */
    private String cpCode;

    /**
     * Database Column Remarks:
     *   场地唯一ID 外键
     *
     *
     * @mbg.generated
     */
    private Long locationId;

    /**
     * Database Column Remarks:
     *   小邮局唯一ID 外键
     *
     *
     * @mbg.generated
     */
    private Long postId;

    /**
     * Database Column Remarks:
     *   月结账号
     *
     *
     * @mbg.generated
     */
    private String waybillAccountNo;

    /**
     * Database Column Remarks:
     *   寄件人姓名
     *
     *
     * @mbg.generated
     */
    private String senderName;

    /**
     * Database Column Remarks:
     *   寄件人电话
     *
     *
     * @mbg.generated
     */
    private String senderPhone;

    /**
     * Database Column Remarks:
     *   寄件人手机
     *
     *
     * @mbg.generated
     */
    private String senderMobile;

    /**
     * Database Column Remarks:
     *   寄件人地址
     *
     *
     * @mbg.generated
     */
    private String senderAddress;

    /**
     * Database Column Remarks:
     *   收件人姓名
     *
     *
     * @mbg.generated
     */
    private String consigneeName;

    /**
     * Database Column Remarks:
     *   收件人电话
     *
     *
     * @mbg.generated
     */
    private String consigneePhone;

    /**
     * Database Column Remarks:
     *   收件人手机
     *
     *
     * @mbg.generated
     */
    private String consigneeMobile;

    /**
     * Database Column Remarks:
     *   收件人地址
     *
     *
     * @mbg.generated
     */
    private String consigneeAddress;

    /**
     * Database Column Remarks:
     *   订单状态
     *
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     * Database Column Remarks:
     *   物流状态
     *
     *
     * @mbg.generated
     */
    private Integer action;

    /**
     * Database Column Remarks:
     *   物品类型
     *
     *
     * @mbg.generated
     */
    private Integer item;

    /**
     * Database Column Remarks:
     *   重量
     *
     *
     * @mbg.generated
     */
    private Integer weight;

    /**
     * Database Column Remarks:
     *   寄件结算类型
     *
     *
     * @mbg.generated
     */
    private Integer product;

    /**
     * Database Column Remarks:
     *   用户id
     *
     *
     * @mbg.generated
     */
    private String userId;

    /**
     * Database Column Remarks:
     *   备注
     *
     *
     * @mbg.generated
     */
    private String remark;

    /**
     * Database Column Remarks:
     *   扩展字段
     *
     *
     * @mbg.generated
     */
    private String feature;

    /**
     * Database Column Remarks:
     *   揽收时间
     *
     *
     * @mbg.generated
     */
    private Date gotTime;

    /**
     * Database Column Remarks:
     *   称重时间
     *
     *
     * @mbg.generated
     */
    private Date weighingTime;

    /**
     * Database Column Remarks:
     *   取消时间
     *
     *
     * @mbg.generated
     */
    private Date cancelTime;

    /**
     * Database Column Remarks:
     *   支付时间
     *
     *
     * @mbg.generated
     */
    private Date payTime;

    /**
     * Database Column Remarks:
     *   业务类型 因公 因私
     *
     *
     * @mbg.generated
     */
    private Byte businessType;

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_bridge_enterprise_order.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_bridge_enterprise_order.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_bridge_enterprise_order.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.corp_id
     *
     * @mbg.generated
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     *
     * @param corpId the value for waybill_bridge_enterprise_order.corp_id
     *
     * @mbg.generated
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.outer_order_code
     *
     * @mbg.generated
     */
    public String getOuterOrderCode() {
        return outerOrderCode;
    }

    /**
     *
     * @param outerOrderCode the value for waybill_bridge_enterprise_order.outer_order_code
     *
     * @mbg.generated
     */
    public void setOuterOrderCode(String outerOrderCode) {
        this.outerOrderCode = outerOrderCode;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.printer_id
     *
     * @mbg.generated
     */
    public String getPrinterId() {
        return printerId;
    }

    /**
     *
     * @param printerId the value for waybill_bridge_enterprise_order.printer_id
     *
     * @mbg.generated
     */
    public void setPrinterId(String printerId) {
        this.printerId = printerId;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.waybill_code
     *
     * @mbg.generated
     */
    public String getWaybillCode() {
        return waybillCode;
    }

    /**
     *
     * @param waybillCode the value for waybill_bridge_enterprise_order.waybill_code
     *
     * @mbg.generated
     */
    public void setWaybillCode(String waybillCode) {
        this.waybillCode = waybillCode;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.cp_code
     *
     * @mbg.generated
     */
    public String getCpCode() {
        return cpCode;
    }

    /**
     *
     * @param cpCode the value for waybill_bridge_enterprise_order.cp_code
     *
     * @mbg.generated
     */
    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.location_id
     *
     * @mbg.generated
     */
    public Long getLocationId() {
        return locationId;
    }

    /**
     *
     * @param locationId the value for waybill_bridge_enterprise_order.location_id
     *
     * @mbg.generated
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.post_id
     *
     * @mbg.generated
     */
    public Long getPostId() {
        return postId;
    }

    /**
     *
     * @param postId the value for waybill_bridge_enterprise_order.post_id
     *
     * @mbg.generated
     */
    public void setPostId(Long postId) {
        this.postId = postId;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.waybill_account_no
     *
     * @mbg.generated
     */
    public String getWaybillAccountNo() {
        return waybillAccountNo;
    }

    /**
     *
     * @param waybillAccountNo the value for waybill_bridge_enterprise_order.waybill_account_no
     *
     * @mbg.generated
     */
    public void setWaybillAccountNo(String waybillAccountNo) {
        this.waybillAccountNo = waybillAccountNo;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.sender_name
     *
     * @mbg.generated
     */
    public String getSenderName() {
        return senderName;
    }

    /**
     *
     * @param senderName the value for waybill_bridge_enterprise_order.sender_name
     *
     * @mbg.generated
     */
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.sender_phone
     *
     * @mbg.generated
     */
    public String getSenderPhone() {
        return senderPhone;
    }

    /**
     *
     * @param senderPhone the value for waybill_bridge_enterprise_order.sender_phone
     *
     * @mbg.generated
     */
    public void setSenderPhone(String senderPhone) {
        this.senderPhone = senderPhone;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.sender_mobile
     *
     * @mbg.generated
     */
    public String getSenderMobile() {
        return senderMobile;
    }

    /**
     *
     * @param senderMobile the value for waybill_bridge_enterprise_order.sender_mobile
     *
     * @mbg.generated
     */
    public void setSenderMobile(String senderMobile) {
        this.senderMobile = senderMobile;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.sender_address
     *
     * @mbg.generated
     */
    public String getSenderAddress() {
        return senderAddress;
    }

    /**
     *
     * @param senderAddress the value for waybill_bridge_enterprise_order.sender_address
     *
     * @mbg.generated
     */
    public void setSenderAddress(String senderAddress) {
        this.senderAddress = senderAddress;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.consignee_name
     *
     * @mbg.generated
     */
    public String getConsigneeName() {
        return consigneeName;
    }

    /**
     *
     * @param consigneeName the value for waybill_bridge_enterprise_order.consignee_name
     *
     * @mbg.generated
     */
    public void setConsigneeName(String consigneeName) {
        this.consigneeName = consigneeName;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.consignee_phone
     *
     * @mbg.generated
     */
    public String getConsigneePhone() {
        return consigneePhone;
    }

    /**
     *
     * @param consigneePhone the value for waybill_bridge_enterprise_order.consignee_phone
     *
     * @mbg.generated
     */
    public void setConsigneePhone(String consigneePhone) {
        this.consigneePhone = consigneePhone;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.consignee_mobile
     *
     * @mbg.generated
     */
    public String getConsigneeMobile() {
        return consigneeMobile;
    }

    /**
     *
     * @param consigneeMobile the value for waybill_bridge_enterprise_order.consignee_mobile
     *
     * @mbg.generated
     */
    public void setConsigneeMobile(String consigneeMobile) {
        this.consigneeMobile = consigneeMobile;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.consignee_address
     *
     * @mbg.generated
     */
    public String getConsigneeAddress() {
        return consigneeAddress;
    }

    /**
     *
     * @param consigneeAddress the value for waybill_bridge_enterprise_order.consignee_address
     *
     * @mbg.generated
     */
    public void setConsigneeAddress(String consigneeAddress) {
        this.consigneeAddress = consigneeAddress;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.status
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     *
     * @param status the value for waybill_bridge_enterprise_order.status
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.action
     *
     * @mbg.generated
     */
    public Integer getAction() {
        return action;
    }

    /**
     *
     * @param action the value for waybill_bridge_enterprise_order.action
     *
     * @mbg.generated
     */
    public void setAction(Integer action) {
        this.action = action;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.item
     *
     * @mbg.generated
     */
    public Integer getItem() {
        return item;
    }

    /**
     *
     * @param item the value for waybill_bridge_enterprise_order.item
     *
     * @mbg.generated
     */
    public void setItem(Integer item) {
        this.item = item;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.weight
     *
     * @mbg.generated
     */
    public Integer getWeight() {
        return weight;
    }

    /**
     *
     * @param weight the value for waybill_bridge_enterprise_order.weight
     *
     * @mbg.generated
     */
    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.product
     *
     * @mbg.generated
     */
    public Integer getProduct() {
        return product;
    }

    /**
     *
     * @param product the value for waybill_bridge_enterprise_order.product
     *
     * @mbg.generated
     */
    public void setProduct(Integer product) {
        this.product = product;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.user_id
     *
     * @mbg.generated
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @param userId the value for waybill_bridge_enterprise_order.user_id
     *
     * @mbg.generated
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @param remark the value for waybill_bridge_enterprise_order.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.feature
     *
     * @mbg.generated
     */
    public String getFeature() {
        return feature;
    }

    /**
     *
     * @param feature the value for waybill_bridge_enterprise_order.feature
     *
     * @mbg.generated
     */
    public void setFeature(String feature) {
        this.feature = feature;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.got_time
     *
     * @mbg.generated
     */
    public Date getGotTime() {
        return gotTime;
    }

    /**
     *
     * @param gotTime the value for waybill_bridge_enterprise_order.got_time
     *
     * @mbg.generated
     */
    public void setGotTime(Date gotTime) {
        this.gotTime = gotTime;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.weighing_time
     *
     * @mbg.generated
     */
    public Date getWeighingTime() {
        return weighingTime;
    }

    /**
     *
     * @param weighingTime the value for waybill_bridge_enterprise_order.weighing_time
     *
     * @mbg.generated
     */
    public void setWeighingTime(Date weighingTime) {
        this.weighingTime = weighingTime;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.cancel_time
     *
     * @mbg.generated
     */
    public Date getCancelTime() {
        return cancelTime;
    }

    /**
     *
     * @param cancelTime the value for waybill_bridge_enterprise_order.cancel_time
     *
     * @mbg.generated
     */
    public void setCancelTime(Date cancelTime) {
        this.cancelTime = cancelTime;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.pay_time
     *
     * @mbg.generated
     */
    public Date getPayTime() {
        return payTime;
    }

    /**
     *
     * @param payTime the value for waybill_bridge_enterprise_order.pay_time
     *
     * @mbg.generated
     */
    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_order.business_type
     *
     * @mbg.generated
     */
    public Byte getBusinessType() {
        return businessType;
    }

    /**
     *
     * @param businessType the value for waybill_bridge_enterprise_order.business_type
     *
     * @mbg.generated
     */
    public void setBusinessType(Byte businessType) {
        this.businessType = businessType;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", corpId=").append(corpId);
        sb.append(", outerOrderCode=").append(outerOrderCode);
        sb.append(", printerId=").append(printerId);
        sb.append(", waybillCode=").append(waybillCode);
        sb.append(", cpCode=").append(cpCode);
        sb.append(", locationId=").append(locationId);
        sb.append(", postId=").append(postId);
        sb.append(", waybillAccountNo=").append(waybillAccountNo);
        sb.append(", senderName=").append(senderName);
        sb.append(", senderPhone=").append(senderPhone);
        sb.append(", senderMobile=").append(senderMobile);
        sb.append(", senderAddress=").append(senderAddress);
        sb.append(", consigneeName=").append(consigneeName);
        sb.append(", consigneePhone=").append(consigneePhone);
        sb.append(", consigneeMobile=").append(consigneeMobile);
        sb.append(", consigneeAddress=").append(consigneeAddress);
        sb.append(", status=").append(status);
        sb.append(", action=").append(action);
        sb.append(", item=").append(item);
        sb.append(", weight=").append(weight);
        sb.append(", product=").append(product);
        sb.append(", userId=").append(userId);
        sb.append(", remark=").append(remark);
        sb.append(", feature=").append(feature);
        sb.append(", gotTime=").append(gotTime);
        sb.append(", weighingTime=").append(weighingTime);
        sb.append(", cancelTime=").append(cancelTime);
        sb.append(", payTime=").append(payTime);
        sb.append(", businessType=").append(businessType);
        sb.append("]");
        return sb.toString();
    }
}