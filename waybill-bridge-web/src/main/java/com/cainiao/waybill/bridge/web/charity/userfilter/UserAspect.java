package com.cainiao.waybill.bridge.web.charity.userfilter;

import com.cainiao.waybill.bridge.biz.charity.request.CharityBaseRequest;
import com.cainiao.waybill.bridge.web.charity.CharityLoginUserUtil;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zouping.fzp
 * @Classname UserAspect
 * @Description
 * @Date 2022/9/15 7:32 下午
 * @Version 1.0
 */
@Component
@Aspect
public class UserAspect {

    @Around("execution(* com.cainiao.waybill.bridge.web.charity.controller..*.*(..))")
    public Object userPop(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        if (args != null) {
            for (Object arg : args) {
                if (arg instanceof CharityBaseRequest) {
                    CharityLoginUserUtil.popLoginInfo((CharityBaseRequest)arg);
                }
            }
        }
        return joinPoint.proceed();
    }
}
