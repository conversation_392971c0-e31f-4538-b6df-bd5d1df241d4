package com.cainiao.waybill.bridge.enterprise.administrators.request;

import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePlatformEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class User implements Serializable {

    private static final long serialVersionUID = 1602862287191719664L;
    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户ID
     */
    private String userType;

    /**
     * 企业唯一id
     */
    private String corpId;

    /**
     * 平台
     * @see EnterprisePlatformEnum
     */
    private String platform;
}
