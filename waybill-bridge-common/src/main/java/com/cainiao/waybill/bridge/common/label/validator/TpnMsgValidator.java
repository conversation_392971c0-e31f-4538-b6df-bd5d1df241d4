package com.cainiao.waybill.bridge.common.label.validator;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.label.dto.request.TpnMsgRequest;
import com.cainiao.waybill.bridge.common.validator.BridgeBaseValidator;
import com.cainiao.waybill.bridge.common.label.dto.TpnMsgDTO;
import org.apache.commons.lang.StringUtils;

/**
 * 发送千牛消息入参 validator
 *
 * <AUTHOR>
 * @since 2017/04/21
 */
public class TpnMsgValidator extends BridgeBaseValidator {
    /**
     * 校验查询条件是否合法
     *
     * @throws BridgeValidationException
     */
    public static void validateTpnMsgDTO(TpnMsgDTO tpnMsgDTO, ClientInfoDTO clientInfoDTO) throws
        BridgeValidationException {
        validate(clientInfoDTO != null, "clientInfoDTO不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getAppName()), "clientInfoDTO.appName不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getOperator()), "clientInfoDTO.operator不能为空");
        validate(tpnMsgDTO != null, "tpnMsgDTO不能为空");
        validate(tpnMsgDTO.getSellerId() != null, "tpnMsgDTO.sellerId不能为空");
        validate(StringUtils.isNotBlank(tpnMsgDTO.getTitle()), "tpnMsgDTO.title不能为空");
        validate(StringUtils.isNotBlank(tpnMsgDTO.getContent()), "tpnMsgDTO.content不能为空");
        validate(StringUtils.isNotBlank(tpnMsgDTO.getSecondMsgType()), "tpnMsgDTO.secondMsgType不能为空");
    }

    /**
     * 校验查询条件是否合法
     *
     * @throws BridgeValidationException
     */
    public static void validateTpnMsgDTO(TpnMsgRequest tpnMsgRequest, ClientInfoDTO clientInfoDTO) throws
        BridgeValidationException {
        validate(clientInfoDTO != null, "clientInfoDTO不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getAppName()), "clientInfoDTO.appName不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getOperator()), "clientInfoDTO.operator不能为空");
        validate(tpnMsgRequest != null, "tpnMsgRequest不能为空");
        validate(tpnMsgRequest.getSellerId() != null, "tpnMsgRequest.sellerId不能为空");
        validate(tpnMsgRequest.getBizId() != null, "tpnMsgRequest.bizId不能为空");
        validate(tpnMsgRequest.getTitleParams() != null, "tpnMsgRequest.titleParams不能为空");
        validate(tpnMsgRequest.getContentParams() != null, "tpnMsgRequest.contentParams不能为空");
        validate(tpnMsgRequest.getTemplateType() != null, "tpnMsgRequest.templateType不能为空");
    }
}
