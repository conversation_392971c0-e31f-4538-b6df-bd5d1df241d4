package com.cainiao.waybill.bridge.web.pickup.vo;

import com.cainiao.waybill.bridge.biz.utils.excel.ExcelHeader;
import lombok.Data;

import java.io.Serializable;

/**
 * 支付宝指标数据
 * <AUTHOR>
 * @date 2025/3/19 16:24
 **/
@Data
public class PickUpAlipayRobotVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    @ExcelHeader("日期")
    private String timeRange;

    /**
     * 订单量
     */
    @ExcelHeader("订单量")
    private Integer orderNum;

    /**
     * 10m接单率
     */
    @ExcelHeader("接单率")
    private String accept10mRate;

    /**
     * 应揽已揽收率
     */
    @ExcelHeader("应揽已揽收率")
    private String gotRate;

    /**
     * 应揽及时揽收率
     */
    @ExcelHeader("应揽及时揽收率")
    private String gotInTimeRate;

}
