package com.cainiao.waybill.bridge.enterprise.administrators.service;

import com.cainiao.waybill.bridge.enterprise.administrators.request.EnterpriseUserRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.GetEnterpriseUserInfoRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.LocationRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.response.ListAdminResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.response.SyncAdminResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.response.UserInfoResponse;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseLocationDO;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseLocationDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserInfoDTO;

import java.util.List;

/**
 * 办公场地配置服务
 *
 * <AUTHOR>
 */
public interface DingTalkLocationService {

    /**
     * 获取办公场地列表
     *
     * @param request request
     * @return 用户信息
     */
    BridgePagingDTO<WaybillEnterpriseLocationDTO> pageList(LocationRequest request);

    /**
     * 查询办公场地
     * @param request request
     * @return 是否成功
     */
    WaybillEnterpriseLocationDO getLocationByName(LocationRequest request);

    /**
     * 添加办公场地
     * @param request request
     * @return 是否成功
     */
    String addLocation(LocationRequest request);

    /**
     * 更新办公场地
     * @param request
     * @return 是否成功
     */
    int updateLocation(LocationRequest request);

    /**
     * 删除办公场地
     * @param id id
     * @return 是否成功
     */
    int deleteLocationById(Long id);


    /**
     * 根据id获取办公场地
     * @param id id
     * @return 管理员
     */
    WaybillEnterpriseLocationDTO getById(Long id);


    /**
     * 删除办公场地
     * @param locationId locationId
     * @return 是否成功
     */
    int deleteByLocationId(String locationId, String corpId);


    /**
     * 获取办公场地
     * @param locationId locationId
     * @return 管理员
     */
    WaybillEnterpriseLocationDTO getLocationByLocationId(String locationId, String corpId);


}
