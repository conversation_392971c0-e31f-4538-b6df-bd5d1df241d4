package com.cainiao.waybill.bridge.web.charity.controller;

import java.util.List;

import com.cainiao.cnlogin.api.context.CnUserInfo;
import com.cainiao.cnlogin.api.context.CnUserInfoUtil;
import com.cainiao.waybill.bridge.biz.charity.request.CharityBaseRequest;
import com.cainiao.waybill.bridge.biz.charity.response.CharityProjectResponse;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityProjectService;
import com.cainiao.waybill.bridge.model.charity.DTO.WaybillCharityProjectDTO;
import com.cainiao.waybill.bridge.web.charity.CharityLoginUserUtil;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> zouping.fzp
 * @Classname WaybillCharityProjectController
 * @Description
 * @Date 2022/8/23 5:15 下午
 * @Version 1.0
 */
@Api(tags = "项目列表")
@RestController
@RequestMapping(value = "/charity/project")
public class WaybillCharityProjectController extends BaseController {

    @Autowired
    private WaybillCharityProjectService waybillCharityProjectService;

    @RequestMapping("/list")
    public BaseResult<List<WaybillCharityProjectDTO>> listProject(CharityBaseRequest charityBaseRequest) {
        List<WaybillCharityProjectDTO> response = waybillCharityProjectService.queryProjectList(charityBaseRequest);
        return BaseResult.success(response);
    }
}
