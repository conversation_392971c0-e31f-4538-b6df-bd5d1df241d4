package com.cainiao.waybill.bridge.common.label.dto;

import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 *  千牛消息 DTO
 * <AUTHOR>
 * @since 2017/04/20
 */
public class TpnMsgDTO extends BaseDTO {
    private static final long serialVersionUID = 6366851501994524443L;
    /**
     * 商家账户id 必填
     */
    private Long sellerId;
    /**
     * 消息标题 必填
     */
    private String title;
    /**
     * 消息内容 必填
     */
    private String content;
    /**
     * 消息类型 必填 （对应千牛二级类目，目前电子面单共有3个二级类目：INSUFFICIENT_QUANTITY,ACCOUNT_RECHARGE,SYSTEM_MESSAGE)
     */
    private String secondMsgType;

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSecondMsgType() {
        return secondMsgType;
    }

    public void setSecondMsgType(String secondMsgType) {
        this.secondMsgType = secondMsgType;
    }
}
