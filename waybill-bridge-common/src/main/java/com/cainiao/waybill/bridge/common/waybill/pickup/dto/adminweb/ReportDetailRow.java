package com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/11/18-下午8:11
 */
@Data
public class ReportDetailRow implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    private String outerOrderCode;
    /**
     * 运单号
     */
    private String mailNo;
    /**
     * 下单时间
     */
    private String createTime;
    /**
     * 业务时间
     */
    private String bizDate;
    /**
     * 状态
     */
    private String status;
    /**
     * 快递公司
     */
    private String cpCodeName;
    /**
     * 订单渠道
     */
    private String channelName;

    /**
     * 接单网点名称
     */
    private String acceptBranchName;
    /**
     * 接单网点code
     */
    private String acceptBranchCode;
    /**
     * 接单小件员名称
     */
    private String acceptCourierName;
    /**
     * 接单小件员手机号
     */
    private String acceptCourierMobile;

    /**
     * 发件人姓名
     */
    private String sendName;
    /**
     * 发件人手机号
     */
    private String sendMobile;
    /**
     * 发件人省份
     */
    private String sendPro;
    /**
     * 发件人城市
     */
    private String sendCity;
    /**
     * 发件人地址
     */
    private String sendAddress;
    /**
     * 收件人姓名
     */
    private String consigneeName;
    /**
     * 收件人手机号
     */
    private String consigneeMobile;
    /**
     * 收件人省份
     */
    private String consigneePro;
    /**
     * 收件人地址
     */
    private String consigneeAddress;

    /**
     * 接单时间
     */
    private String acceptTime;
    /**
     * 揽收时间
     */
    private String gotTime;
    /**
     * 签收时间
     */
    private String signTime;
    /**
     * 取消时间
     */
    private String cancelTime;
    /**
     * 取消原因
     */
    private String cancelDesc;

    /**
     * 最新重量
     */
    private String weight;
    /**
     * 最新重量设置时间
     */
    private String weightTime;
    /**
     * 首次重量
     */
    private String oldWeight;
    /**
     * 首次设置重量时间
     */
    private String oldWeightTime;

    /**
     * 小号
     */
    private String virtualNum;
    /**
     * 电联状态
     */
    private String virtualStatus;
    /**
     * 首次有效电联时间
     */
    private String firstVirtualTime;

    /**
     * 扩展信息
     */
    private String extendInfo;

    /**
     * 是否是预约订单：值范围["是","否"]
     */
    private String isAppointOrder;
    /**
     * 消费者指定的预约开始时间
     */
    private String cusAppointStartTime;
    /**
     * 消费者指定的预约结束时间
     */
    private String cusAppointEndTime;
    /**
     * 最终确定的预约开始时间
     */
    private String appointStartTime;
    /**
     * 最终确定的预约结束时间
     */
    private String appointEndTime;

    /**
     * 取件码
     */
    private String gotCode;

    /**
     * 服务商订单号
     */
    private String cpOrderId;
}
