package com.cainiao.waybill.bridge.common.dto;

import java.io.Serializable;

/**
 * 最基础的数据传输对象，所有的dto对象都会继承它。
 * 
 * <AUTHOR>
 * @since Jan 27, 2016 2:41:36 PM
 */
public class BaseDTO extends AutomaticToString implements Serializable {

    /**
     * 
     */
    private static final long  serialVersionUID   = -4615606359388601164L;
    public static final String DEFAULT_REQUEST_ID = "DEFAULT_REQUEST_ID";
    // 接口参数请求id，由调用方自己设置，只要保证请求参数list里面的objectId不重复即可
    protected String           objectId;

    public String getObjectId() {
        return objectId;
    }

    /**
     * 接口参数数据对象id，由调用方自己设置，只要保证请求参数list里面的objectId不重复即可。生成方式可参考
     * {@link com.cainiao.waybill.common.util.StringUtil#getUUID()}
     * 
     * @param objectId
     */
    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

}
