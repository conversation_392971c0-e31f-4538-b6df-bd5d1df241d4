package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class CustomerSettlementInfoDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   客户名
     *
     *
     * @mbg.generated
     */
    private String userName;

    /**
     * Database Column Remarks:
     *   客户来源，CRM/淘外系统
     *
     *
     * @mbg.generated
     */
    private String userSource;

    /**
     * Database Column Remarks:
     *   用户类型，CUSTOMER:客户/CP:运力
     *
     *
     * @mbg.generated
     */
    private String userType;

    /**
     * Database Column Remarks:
     *   用户外键ID
     *
     *
     * @mbg.generated
     */
    private Long outUserId;

    /**
     * Database Column Remarks:
     *   菜鸟会员ID
     *
     *
     * @mbg.generated
     */
    private String cnUserId;

    /**
     * Database Column Remarks:
     *   淘宝会员ID
     *
     *
     * @mbg.generated
     */
    private String tbUserId;

    /**
     * Database Column Remarks:
     *   结算方案类型，预充值/月结/代扣
     *
     *
     * @mbg.generated
     */
    private String settlementType;

    /**
     * Database Column Remarks:
     *   费用项
     *
     *
     * @mbg.generated
     */
    private String feeType;

    /**
     * Database Column Remarks:
     *   客户状态
     *
     *
     * @mbg.generated
     */
    private String status;

    /**
     * Database Column Remarks:
     *   扩展信息
     *
     *
     * @mbg.generated
     */
    private String feature;

    /**
     * Database Column Remarks:
     *   是否删除,Y/N
     *
     *
     * @mbg.generated
     */
    private String isDelete;

    /**
     * Database Column Remarks:
     *   客户结算配置key
     *
     *
     * @mbg.generated
     */
    private Long userKey;

    /**
     *
     * @return the value of customer_settlement_info.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for customer_settlement_info.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of customer_settlement_info.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for customer_settlement_info.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of customer_settlement_info.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for customer_settlement_info.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of customer_settlement_info.user_name
     *
     * @mbg.generated
     */
    public String getUserName() {
        return userName;
    }

    /**
     *
     * @param userName the value for customer_settlement_info.user_name
     *
     * @mbg.generated
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     *
     * @return the value of customer_settlement_info.user_source
     *
     * @mbg.generated
     */
    public String getUserSource() {
        return userSource;
    }

    /**
     *
     * @param userSource the value for customer_settlement_info.user_source
     *
     * @mbg.generated
     */
    public void setUserSource(String userSource) {
        this.userSource = userSource;
    }

    /**
     *
     * @return the value of customer_settlement_info.user_type
     *
     * @mbg.generated
     */
    public String getUserType() {
        return userType;
    }

    /**
     *
     * @param userType the value for customer_settlement_info.user_type
     *
     * @mbg.generated
     */
    public void setUserType(String userType) {
        this.userType = userType;
    }

    /**
     *
     * @return the value of customer_settlement_info.out_user_id
     *
     * @mbg.generated
     */
    public Long getOutUserId() {
        return outUserId;
    }

    /**
     *
     * @param outUserId the value for customer_settlement_info.out_user_id
     *
     * @mbg.generated
     */
    public void setOutUserId(Long outUserId) {
        this.outUserId = outUserId;
    }

    /**
     *
     * @return the value of customer_settlement_info.cn_user_id
     *
     * @mbg.generated
     */
    public String getCnUserId() {
        return cnUserId;
    }

    /**
     *
     * @param cnUserId the value for customer_settlement_info.cn_user_id
     *
     * @mbg.generated
     */
    public void setCnUserId(String cnUserId) {
        this.cnUserId = cnUserId;
    }

    /**
     *
     * @return the value of customer_settlement_info.tb_user_id
     *
     * @mbg.generated
     */
    public String getTbUserId() {
        return tbUserId;
    }

    /**
     *
     * @param tbUserId the value for customer_settlement_info.tb_user_id
     *
     * @mbg.generated
     */
    public void setTbUserId(String tbUserId) {
        this.tbUserId = tbUserId;
    }

    /**
     *
     * @return the value of customer_settlement_info.settlement_type
     *
     * @mbg.generated
     */
    public String getSettlementType() {
        return settlementType;
    }

    /**
     *
     * @param settlementType the value for customer_settlement_info.settlement_type
     *
     * @mbg.generated
     */
    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType;
    }

    /**
     *
     * @return the value of customer_settlement_info.fee_type
     *
     * @mbg.generated
     */
    public String getFeeType() {
        return feeType;
    }

    /**
     *
     * @param feeType the value for customer_settlement_info.fee_type
     *
     * @mbg.generated
     */
    public void setFeeType(String feeType) {
        this.feeType = feeType;
    }

    /**
     *
     * @return the value of customer_settlement_info.status
     *
     * @mbg.generated
     */
    public String getStatus() {
        return status;
    }

    /**
     *
     * @param status the value for customer_settlement_info.status
     *
     * @mbg.generated
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     *
     * @return the value of customer_settlement_info.feature
     *
     * @mbg.generated
     */
    public String getFeature() {
        return feature;
    }

    /**
     *
     * @param feature the value for customer_settlement_info.feature
     *
     * @mbg.generated
     */
    public void setFeature(String feature) {
        this.feature = feature;
    }

    /**
     *
     * @return the value of customer_settlement_info.is_delete
     *
     * @mbg.generated
     */
    public String getIsDelete() {
        return isDelete;
    }

    /**
     *
     * @param isDelete the value for customer_settlement_info.is_delete
     *
     * @mbg.generated
     */
    public void setIsDelete(String isDelete) {
        this.isDelete = isDelete;
    }

    /**
     *
     * @return the value of customer_settlement_info.user_key
     *
     * @mbg.generated
     */
    public Long getUserKey() {
        return userKey;
    }

    /**
     *
     * @param userKey the value for customer_settlement_info.user_key
     *
     * @mbg.generated
     */
    public void setUserKey(Long userKey) {
        this.userKey = userKey;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", userName=").append(userName);
        sb.append(", userSource=").append(userSource);
        sb.append(", userType=").append(userType);
        sb.append(", outUserId=").append(outUserId);
        sb.append(", cnUserId=").append(cnUserId);
        sb.append(", tbUserId=").append(tbUserId);
        sb.append(", settlementType=").append(settlementType);
        sb.append(", feeType=").append(feeType);
        sb.append(", status=").append(status);
        sb.append(", feature=").append(feature);
        sb.append(", isDelete=").append(isDelete);
        sb.append(", userKey=").append(userKey);
        sb.append("]");
        return sb.toString();
    }
}