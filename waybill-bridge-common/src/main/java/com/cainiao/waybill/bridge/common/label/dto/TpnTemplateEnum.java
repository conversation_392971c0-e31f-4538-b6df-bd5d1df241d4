package com.cainiao.waybill.bridge.common.label.dto;

/**
 * 千牛消息模板信息枚举
 *
 * <AUTHOR>
 * @date 2018-04-17 4:30 PM
 */
public enum TpnTemplateEnum {

    /**
     * 解绑商家和小件员的绑定关系
     */
    DELETE_RELATION(1000189L, "0C18B61E8D82460B5C084200FFD90350"),
    /**
     * 换绑商家和小件员的绑定关系
     */
    CHANGE_RELATION(1000192L, "9F74E97E98B573BB7B598784497046E7");

    private Long templateId;

    private String templateKey;

    TpnTemplateEnum(Long templateId, String templateKey) {
        this.templateId = templateId;
        this.templateKey = templateKey;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getTemplateKey() {
        return templateKey;
    }

    public void setTemplateKey(String templateKey) {
        this.templateKey = templateKey;
    }
}
