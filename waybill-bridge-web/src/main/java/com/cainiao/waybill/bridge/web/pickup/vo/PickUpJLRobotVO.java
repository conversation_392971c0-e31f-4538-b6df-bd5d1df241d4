package com.cainiao.waybill.bridge.web.pickup.vo;

import java.io.Serializable;

import com.cainiao.waybill.bridge.biz.utils.excel.ExcelHeader;
import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpCpRobotVO
 * @Description
 * @Date 2023/11/28 13:43
 * @Version 1.0
 */
@Data
public class PickUpJLRobotVO implements Serializable {

    private static final long serialVersionUID = -8752654347854533230L;

    /**
     * 服务商信息
     */
    @ExcelHeader("服务商")
    private String cpInfo;

    @ExcelHeader("单量")
    private int num;

    @ExcelHeader("工单量")
    private int ticketNum;

    //@ExcelHeader("去重工单量")
    private int distinctTicketNum;

    //@ExcelHeader("进线率")
    private String ticketRate;

    @ExcelHeader("去重进线率")
    private String distinctTicketRate;

}
