package com.cainiao.waybill.bridge.common.verificationCode.dto.request;


import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 *
 * 发送短信验证码的request dto
 *
 * Created by shouyuan.lzl on 2017-04-25 2:07 PM.
 */
public class SendVerificationCodeRequest extends BaseDTO {

    private static final long serialVersionUID = -5486416160261121265L;
    /**
     * 商家id, 必填
     */
    private Long sellerId ;

    /**
     * 商家昵称，必填
     */
    private String sellerNick ;

    /**
     * 快递员id，必填
     */
    private Long courierId ;

    /**
     * 快递员电话，必填
     */
    private String courierMobile ;

    /**
     * 验证码类型 1 商家和快递员绑定申请的验证码 ，必填
     */
    private Integer verificationCodeType ;

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public String getSellerNick() {
        return sellerNick;
    }

    public void setSellerNick(String sellerNick) {
        this.sellerNick = sellerNick;
    }

    public Long getCourierId() {
        return courierId;
    }

    public void setCourierId(Long courierId) {
        this.courierId = courierId;
    }

    public String getCourierMobile() {
        return courierMobile;
    }

    public void setCourierMobile(String courierMobile) {
        this.courierMobile = courierMobile;
    }

    public Integer getVerificationCodeType() {
        return verificationCodeType;
    }

    public void setVerificationCodeType(Integer verificationCodeType) {
        this.verificationCodeType = verificationCodeType;
    }
}
