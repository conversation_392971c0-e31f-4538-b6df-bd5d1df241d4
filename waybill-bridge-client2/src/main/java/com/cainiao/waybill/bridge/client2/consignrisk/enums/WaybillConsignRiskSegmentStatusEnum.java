package com.cainiao.waybill.bridge.client2.consignrisk.enums;

/**
 * <AUTHOR>
 */
public enum WaybillConsignRiskSegmentStatusEnum {
    newStatus("新建", "new"),
    grayPublish("灰度中", "grayPublish"),
    published("已发布", "published"),
    downline("已下线", "downline"),
    ;

    private final String name;
    private final String value;

    WaybillConsignRiskSegmentStatusEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

    public static WaybillConsignRiskSegmentStatusEnum fromValue(String value) {
        for (WaybillConsignRiskSegmentStatusEnum status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with value " + value);
    }

    public static WaybillConsignRiskSegmentStatusEnum fromName(String name) {
        for (WaybillConsignRiskSegmentStatusEnum status : WaybillConsignRiskSegmentStatusEnum.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        throw new IllegalArgumentException(name + "不在状态列表中");
    }
}

