package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.ld.dto.WaybillLdSubRequest;
import com.cainiao.waybill.bridge.biz.pickup.ld.service.WaybillLdSubService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PACK_SUB.WaybillPackSubRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PACK_SUB.WaybillPackSubResponse;
import com.taobao.pac.client.sdk.receiveservice.WaybillPackSubService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/1/18-下午8:57
 */
public class TmsWaybillPackSubServiceImpl implements WaybillPackSubService {

    @Resource
    private WaybillLdSubService waybillLdSubService;

    @Override
    public WaybillPackSubResponse invoke(ReceiveParams<WaybillPackSubRequest> receiveParams) {
        PickUpLogUtil.info("TmsWaybillPackSubServiceImpl param : " + JSON.toJSONString(receiveParams));

        WaybillPackSubRequest linkSubRequest = receiveParams.getRequestDataObject();

        WaybillPackSubResponse linkSubResponse = new WaybillPackSubResponse();

        try {
            //  调用方订阅权限总开关
            if (BridgeSwitch.ldSubSwitch.get(receiveParams.getFromAppkey()) == null || !BridgeSwitch.ldSubSwitch.get(receiveParams.getFromAppkey())) {
                linkSubResponse.setErrorCode(PickUpConstants.Error.LD_SUB_NOT_NO_PERMISSION.getErrorCode());
                linkSubResponse.setErrorMsg(PickUpConstants.Error.LD_SUB_NOT_NO_PERMISSION.getErrorMsg());
                linkSubResponse.setResult(false);
                linkSubResponse.setSuccess(false);
                return linkSubResponse;
            }

            if (StringUtil.isBlank(linkSubRequest.getMailNo()) || StringUtil.isBlank(linkSubRequest.getOrderChannel())) {
                linkSubResponse.setErrorCode(PickUpConstants.Error.LD_SUB_PARAM_EMPTY.getErrorCode());
                linkSubResponse.setErrorMsg(PickUpConstants.Error.LD_SUB_PARAM_EMPTY.getErrorMsg());
                linkSubResponse.setResult(false);
                linkSubResponse.setSuccess(false);
                return linkSubResponse;
            }
            //  至少保证有一个可以关联上运单号的校验参数
            //  都为空，直接返回
            if (StringUtil.isBlank(linkSubRequest.getConsigneeMobile()) && StringUtil.isBlank(linkSubRequest.getExtentFeature())) {
                linkSubResponse.setErrorCode(PickUpConstants.Error.LD_SUB_CHECK_PARAM_EMPTY.getErrorCode());
                linkSubResponse.setErrorMsg(PickUpConstants.Error.LD_SUB_CHECK_PARAM_EMPTY.getErrorMsg());
                linkSubResponse.setResult(false);
                linkSubResponse.setSuccess(false);
                return linkSubResponse;
            }
//            //  收件人手机号为空，feature不为空，则需要校验feature JSON内容是否为空
//            if (StringUtil.isBlank(linkSubRequest.getConsigneeMobile()) && !mailNoRelationParamEmptyCheck(linkSubRequest.getExtentFeature())) {
//                linkSubResponse.setErrorCode(PickUpConstants.Error.LD_SUB_CHECK_PARAM_EMPTY.getErrorCode());
//                linkSubResponse.setErrorMsg(PickUpConstants.Error.LD_SUB_CHECK_PARAM_EMPTY.getErrorMsg());
//                linkSubResponse.setResult(false);
//                linkSubResponse.setSuccess(false);
//                return linkSubResponse;
//            }

            WaybillLdSubRequest waybillLdSubRequest = new WaybillLdSubRequest();
            BeanUtils.copyProperties(linkSubRequest, waybillLdSubRequest);
            waybillLdSubRequest.setLinkFromAppKey(receiveParams.getFromAppkey());
            waybillLdSubRequest.setLinkFromResCode(receiveParams.getCpCode());
            waybillLdSubService.subscribe(waybillLdSubRequest);
        } catch (BridgeBaseException bridgeBaseException) {
            PickUpLogUtil.errLog(JSON.toJSONString(receiveParams), PickUpConstants.Action.LINK_LD_SUB_BIZ_ERROR.name(),
                    bridgeBaseException.getErrorCode(), bridgeBaseException.getErrorMessage());
            linkSubResponse.setErrorCode(bridgeBaseException.getErrorCode());
            linkSubResponse.setErrorMsg(bridgeBaseException.getErrorMessage());
            linkSubResponse.setResult(false);
            linkSubResponse.setSuccess(false);
            return linkSubResponse;
        } catch (Throwable e) {
            PickUpLogUtil.errLog(JSON.toJSONString(receiveParams), PickUpConstants.Action.LINK_LD_SUB_ERROR.name(),
                    PickUpConstants.Error.LD_SUB_SYSTEM_ERROR.getErrorCode(), PickUpConstants.Error.LD_SUB_SYSTEM_ERROR.getErrorMsg(), e);
            linkSubResponse.setErrorCode(PickUpConstants.Error.LD_SUB_SYSTEM_ERROR.getErrorCode());
            linkSubResponse.setErrorMsg(PickUpConstants.Error.LD_SUB_SYSTEM_ERROR.getErrorMsg());
            linkSubResponse.setResult(false);
            linkSubResponse.setSuccess(false);
            return linkSubResponse;
        }
        linkSubResponse.setResult(true);
        PickUpLogUtil.info("ld sub suc : " + linkSubRequest.getMailNo());
        return linkSubResponse;
    }

    private boolean mailNoRelationParamEmptyCheck(String extentFeature) throws BridgeBaseException {
        try {
            JSONObject jsonObject = JSONObject.parseObject(extentFeature);
            if (jsonObject == null) {
                return false;
            }
            String orderId = jsonObject.getString(PickUpConstants.Ld.MailNoRelationParamKey.ORDER_ID);
            String tid = jsonObject.getString(PickUpConstants.Ld.MailNoRelationParamKey.TID);
            Boolean cloudPrintTag = jsonObject.getBoolean(PickUpConstants.Ld.MailNoRelationParamKey.CLOUD_PRINT_TAG);
            if (StringUtil.isBlank(orderId) && StringUtil.isBlank(tid) && cloudPrintTag == null) {
                return false;
            }
            return true;
        } catch (Throwable e) {
            throw new BridgeBaseException(PickUpConstants.Error.LD_SUB_CHECK_PARAM_EMPTY.getErrorCode(), PickUpConstants.Error.LD_SUB_CHECK_PARAM_EMPTY.getErrorMsg());
        }
    }
}
