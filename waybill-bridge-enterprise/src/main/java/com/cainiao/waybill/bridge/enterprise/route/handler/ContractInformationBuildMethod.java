package com.cainiao.waybill.bridge.enterprise.route.handler;

import com.cainiao.waybill.bridge.enterprise.common.enums.ExpressTypeEnum;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2025/5/7
 **/
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface ContractInformationBuildMethod {

    ExpressTypeEnum expressType();
}
