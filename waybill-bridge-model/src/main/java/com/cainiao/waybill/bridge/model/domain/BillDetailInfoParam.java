package com.cainiao.waybill.bridge.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class BillDetailInfoParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public BillDetailInfoParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public BillDetailInfoParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public BillDetailInfoParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public BillDetailInfoParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public BillDetailInfoParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public BillDetailInfoParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdIsNull() {
            addCriterion("bill_summary_id is null");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdIsNotNull() {
            addCriterion("bill_summary_id is not null");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdEqualTo(String value) {
            addCriterion("bill_summary_id =", value, "billSummaryId");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdNotEqualTo(String value) {
            addCriterion("bill_summary_id <>", value, "billSummaryId");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdGreaterThan(String value) {
            addCriterion("bill_summary_id >", value, "billSummaryId");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdGreaterThanOrEqualTo(String value) {
            addCriterion("bill_summary_id >=", value, "billSummaryId");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdLessThan(String value) {
            addCriterion("bill_summary_id <", value, "billSummaryId");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdLessThanOrEqualTo(String value) {
            addCriterion("bill_summary_id <=", value, "billSummaryId");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdLike(String value) {
            addCriterion("bill_summary_id like", value, "billSummaryId");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdNotLike(String value) {
            addCriterion("bill_summary_id not like", value, "billSummaryId");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdIn(List<String> values) {
            addCriterion("bill_summary_id in", values, "billSummaryId");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdNotIn(List<String> values) {
            addCriterion("bill_summary_id not in", values, "billSummaryId");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdBetween(String value1, String value2) {
            addCriterion("bill_summary_id between", value1, value2, "billSummaryId");
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdNotBetween(String value1, String value2) {
            addCriterion("bill_summary_id not between", value1, value2, "billSummaryId");
            return (Criteria) this;
        }

        public Criteria andBillMonthIsNull() {
            addCriterion("bill_month is null");
            return (Criteria) this;
        }

        public Criteria andBillMonthIsNotNull() {
            addCriterion("bill_month is not null");
            return (Criteria) this;
        }

        public Criteria andBillMonthEqualTo(String value) {
            addCriterion("bill_month =", value, "billMonth");
            return (Criteria) this;
        }

        public Criteria andBillMonthNotEqualTo(String value) {
            addCriterion("bill_month <>", value, "billMonth");
            return (Criteria) this;
        }

        public Criteria andBillMonthGreaterThan(String value) {
            addCriterion("bill_month >", value, "billMonth");
            return (Criteria) this;
        }

        public Criteria andBillMonthGreaterThanOrEqualTo(String value) {
            addCriterion("bill_month >=", value, "billMonth");
            return (Criteria) this;
        }

        public Criteria andBillMonthLessThan(String value) {
            addCriterion("bill_month <", value, "billMonth");
            return (Criteria) this;
        }

        public Criteria andBillMonthLessThanOrEqualTo(String value) {
            addCriterion("bill_month <=", value, "billMonth");
            return (Criteria) this;
        }

        public Criteria andBillMonthLike(String value) {
            addCriterion("bill_month like", value, "billMonth");
            return (Criteria) this;
        }

        public Criteria andBillMonthNotLike(String value) {
            addCriterion("bill_month not like", value, "billMonth");
            return (Criteria) this;
        }

        public Criteria andBillMonthIn(List<String> values) {
            addCriterion("bill_month in", values, "billMonth");
            return (Criteria) this;
        }

        public Criteria andBillMonthNotIn(List<String> values) {
            addCriterion("bill_month not in", values, "billMonth");
            return (Criteria) this;
        }

        public Criteria andBillMonthBetween(String value1, String value2) {
            addCriterion("bill_month between", value1, value2, "billMonth");
            return (Criteria) this;
        }

        public Criteria andBillMonthNotBetween(String value1, String value2) {
            addCriterion("bill_month not between", value1, value2, "billMonth");
            return (Criteria) this;
        }

        public Criteria andMailNoIsNull() {
            addCriterion("mail_no is null");
            return (Criteria) this;
        }

        public Criteria andMailNoIsNotNull() {
            addCriterion("mail_no is not null");
            return (Criteria) this;
        }

        public Criteria andMailNoEqualTo(String value) {
            addCriterion("mail_no =", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoNotEqualTo(String value) {
            addCriterion("mail_no <>", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoGreaterThan(String value) {
            addCriterion("mail_no >", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoGreaterThanOrEqualTo(String value) {
            addCriterion("mail_no >=", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoLessThan(String value) {
            addCriterion("mail_no <", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoLessThanOrEqualTo(String value) {
            addCriterion("mail_no <=", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoLike(String value) {
            addCriterion("mail_no like", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoNotLike(String value) {
            addCriterion("mail_no not like", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoIn(List<String> values) {
            addCriterion("mail_no in", values, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoNotIn(List<String> values) {
            addCriterion("mail_no not in", values, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoBetween(String value1, String value2) {
            addCriterion("mail_no between", value1, value2, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoNotBetween(String value1, String value2) {
            addCriterion("mail_no not between", value1, value2, "mailNo");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeIsNull() {
            addCriterion("out_order_code is null");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeIsNotNull() {
            addCriterion("out_order_code is not null");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeEqualTo(String value) {
            addCriterion("out_order_code =", value, "outOrderCode");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeNotEqualTo(String value) {
            addCriterion("out_order_code <>", value, "outOrderCode");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeGreaterThan(String value) {
            addCriterion("out_order_code >", value, "outOrderCode");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeGreaterThanOrEqualTo(String value) {
            addCriterion("out_order_code >=", value, "outOrderCode");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeLessThan(String value) {
            addCriterion("out_order_code <", value, "outOrderCode");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeLessThanOrEqualTo(String value) {
            addCriterion("out_order_code <=", value, "outOrderCode");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeLike(String value) {
            addCriterion("out_order_code like", value, "outOrderCode");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeNotLike(String value) {
            addCriterion("out_order_code not like", value, "outOrderCode");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeIn(List<String> values) {
            addCriterion("out_order_code in", values, "outOrderCode");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeNotIn(List<String> values) {
            addCriterion("out_order_code not in", values, "outOrderCode");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeBetween(String value1, String value2) {
            addCriterion("out_order_code between", value1, value2, "outOrderCode");
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeNotBetween(String value1, String value2) {
            addCriterion("out_order_code not between", value1, value2, "outOrderCode");
            return (Criteria) this;
        }

        public Criteria andBillTypeIsNull() {
            addCriterion("bill_type is null");
            return (Criteria) this;
        }

        public Criteria andBillTypeIsNotNull() {
            addCriterion("bill_type is not null");
            return (Criteria) this;
        }

        public Criteria andBillTypeEqualTo(String value) {
            addCriterion("bill_type =", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotEqualTo(String value) {
            addCriterion("bill_type <>", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeGreaterThan(String value) {
            addCriterion("bill_type >", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeGreaterThanOrEqualTo(String value) {
            addCriterion("bill_type >=", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeLessThan(String value) {
            addCriterion("bill_type <", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeLessThanOrEqualTo(String value) {
            addCriterion("bill_type <=", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeLike(String value) {
            addCriterion("bill_type like", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotLike(String value) {
            addCriterion("bill_type not like", value, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeIn(List<String> values) {
            addCriterion("bill_type in", values, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotIn(List<String> values) {
            addCriterion("bill_type not in", values, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeBetween(String value1, String value2) {
            addCriterion("bill_type between", value1, value2, "billType");
            return (Criteria) this;
        }

        public Criteria andBillTypeNotBetween(String value1, String value2) {
            addCriterion("bill_type not between", value1, value2, "billType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeIsNull() {
            addCriterion("settlement_type is null");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeIsNotNull() {
            addCriterion("settlement_type is not null");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeEqualTo(String value) {
            addCriterion("settlement_type =", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotEqualTo(String value) {
            addCriterion("settlement_type <>", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeGreaterThan(String value) {
            addCriterion("settlement_type >", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeGreaterThanOrEqualTo(String value) {
            addCriterion("settlement_type >=", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeLessThan(String value) {
            addCriterion("settlement_type <", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeLessThanOrEqualTo(String value) {
            addCriterion("settlement_type <=", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeLike(String value) {
            addCriterion("settlement_type like", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotLike(String value) {
            addCriterion("settlement_type not like", value, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeIn(List<String> values) {
            addCriterion("settlement_type in", values, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotIn(List<String> values) {
            addCriterion("settlement_type not in", values, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeBetween(String value1, String value2) {
            addCriterion("settlement_type between", value1, value2, "settlementType");
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotBetween(String value1, String value2) {
            addCriterion("settlement_type not between", value1, value2, "settlementType");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeIsNull() {
            addCriterion("order_create_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeIsNotNull() {
            addCriterion("order_create_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeEqualTo(Date value) {
            addCriterion("order_create_time =", value, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeNotEqualTo(Date value) {
            addCriterion("order_create_time <>", value, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeGreaterThan(Date value) {
            addCriterion("order_create_time >", value, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_create_time >=", value, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeLessThan(Date value) {
            addCriterion("order_create_time <", value, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_create_time <=", value, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeIn(List<Date> values) {
            addCriterion("order_create_time in", values, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeNotIn(List<Date> values) {
            addCriterion("order_create_time not in", values, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeBetween(Date value1, Date value2) {
            addCriterion("order_create_time between", value1, value2, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_create_time not between", value1, value2, "orderCreateTime");
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeIsNull() {
            addCriterion("order_got_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeIsNotNull() {
            addCriterion("order_got_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeEqualTo(Date value) {
            addCriterion("order_got_time =", value, "orderGotTime");
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeNotEqualTo(Date value) {
            addCriterion("order_got_time <>", value, "orderGotTime");
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeGreaterThan(Date value) {
            addCriterion("order_got_time >", value, "orderGotTime");
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_got_time >=", value, "orderGotTime");
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeLessThan(Date value) {
            addCriterion("order_got_time <", value, "orderGotTime");
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_got_time <=", value, "orderGotTime");
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeIn(List<Date> values) {
            addCriterion("order_got_time in", values, "orderGotTime");
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeNotIn(List<Date> values) {
            addCriterion("order_got_time not in", values, "orderGotTime");
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeBetween(Date value1, Date value2) {
            addCriterion("order_got_time between", value1, value2, "orderGotTime");
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_got_time not between", value1, value2, "orderGotTime");
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeIsNull() {
            addCriterion("order_charge_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeIsNotNull() {
            addCriterion("order_charge_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeEqualTo(Date value) {
            addCriterion("order_charge_time =", value, "orderChargeTime");
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeNotEqualTo(Date value) {
            addCriterion("order_charge_time <>", value, "orderChargeTime");
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeGreaterThan(Date value) {
            addCriterion("order_charge_time >", value, "orderChargeTime");
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_charge_time >=", value, "orderChargeTime");
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeLessThan(Date value) {
            addCriterion("order_charge_time <", value, "orderChargeTime");
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_charge_time <=", value, "orderChargeTime");
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeIn(List<Date> values) {
            addCriterion("order_charge_time in", values, "orderChargeTime");
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeNotIn(List<Date> values) {
            addCriterion("order_charge_time not in", values, "orderChargeTime");
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeBetween(Date value1, Date value2) {
            addCriterion("order_charge_time between", value1, value2, "orderChargeTime");
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_charge_time not between", value1, value2, "orderChargeTime");
            return (Criteria) this;
        }

        public Criteria andSendProvIsNull() {
            addCriterion("send_prov is null");
            return (Criteria) this;
        }

        public Criteria andSendProvIsNotNull() {
            addCriterion("send_prov is not null");
            return (Criteria) this;
        }

        public Criteria andSendProvEqualTo(String value) {
            addCriterion("send_prov =", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvNotEqualTo(String value) {
            addCriterion("send_prov <>", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvGreaterThan(String value) {
            addCriterion("send_prov >", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvGreaterThanOrEqualTo(String value) {
            addCriterion("send_prov >=", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvLessThan(String value) {
            addCriterion("send_prov <", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvLessThanOrEqualTo(String value) {
            addCriterion("send_prov <=", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvLike(String value) {
            addCriterion("send_prov like", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvNotLike(String value) {
            addCriterion("send_prov not like", value, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvIn(List<String> values) {
            addCriterion("send_prov in", values, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvNotIn(List<String> values) {
            addCriterion("send_prov not in", values, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvBetween(String value1, String value2) {
            addCriterion("send_prov between", value1, value2, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendProvNotBetween(String value1, String value2) {
            addCriterion("send_prov not between", value1, value2, "sendProv");
            return (Criteria) this;
        }

        public Criteria andSendCityIsNull() {
            addCriterion("send_city is null");
            return (Criteria) this;
        }

        public Criteria andSendCityIsNotNull() {
            addCriterion("send_city is not null");
            return (Criteria) this;
        }

        public Criteria andSendCityEqualTo(String value) {
            addCriterion("send_city =", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityNotEqualTo(String value) {
            addCriterion("send_city <>", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityGreaterThan(String value) {
            addCriterion("send_city >", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityGreaterThanOrEqualTo(String value) {
            addCriterion("send_city >=", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityLessThan(String value) {
            addCriterion("send_city <", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityLessThanOrEqualTo(String value) {
            addCriterion("send_city <=", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityLike(String value) {
            addCriterion("send_city like", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityNotLike(String value) {
            addCriterion("send_city not like", value, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityIn(List<String> values) {
            addCriterion("send_city in", values, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityNotIn(List<String> values) {
            addCriterion("send_city not in", values, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityBetween(String value1, String value2) {
            addCriterion("send_city between", value1, value2, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendCityNotBetween(String value1, String value2) {
            addCriterion("send_city not between", value1, value2, "sendCity");
            return (Criteria) this;
        }

        public Criteria andSendAreaIsNull() {
            addCriterion("send_area is null");
            return (Criteria) this;
        }

        public Criteria andSendAreaIsNotNull() {
            addCriterion("send_area is not null");
            return (Criteria) this;
        }

        public Criteria andSendAreaEqualTo(String value) {
            addCriterion("send_area =", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaNotEqualTo(String value) {
            addCriterion("send_area <>", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaGreaterThan(String value) {
            addCriterion("send_area >", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaGreaterThanOrEqualTo(String value) {
            addCriterion("send_area >=", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaLessThan(String value) {
            addCriterion("send_area <", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaLessThanOrEqualTo(String value) {
            addCriterion("send_area <=", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaLike(String value) {
            addCriterion("send_area like", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaNotLike(String value) {
            addCriterion("send_area not like", value, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaIn(List<String> values) {
            addCriterion("send_area in", values, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaNotIn(List<String> values) {
            addCriterion("send_area not in", values, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaBetween(String value1, String value2) {
            addCriterion("send_area between", value1, value2, "sendArea");
            return (Criteria) this;
        }

        public Criteria andSendAreaNotBetween(String value1, String value2) {
            addCriterion("send_area not between", value1, value2, "sendArea");
            return (Criteria) this;
        }

        public Criteria andReceiveProvIsNull() {
            addCriterion("receive_prov is null");
            return (Criteria) this;
        }

        public Criteria andReceiveProvIsNotNull() {
            addCriterion("receive_prov is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveProvEqualTo(String value) {
            addCriterion("receive_prov =", value, "receiveProv");
            return (Criteria) this;
        }

        public Criteria andReceiveProvNotEqualTo(String value) {
            addCriterion("receive_prov <>", value, "receiveProv");
            return (Criteria) this;
        }

        public Criteria andReceiveProvGreaterThan(String value) {
            addCriterion("receive_prov >", value, "receiveProv");
            return (Criteria) this;
        }

        public Criteria andReceiveProvGreaterThanOrEqualTo(String value) {
            addCriterion("receive_prov >=", value, "receiveProv");
            return (Criteria) this;
        }

        public Criteria andReceiveProvLessThan(String value) {
            addCriterion("receive_prov <", value, "receiveProv");
            return (Criteria) this;
        }

        public Criteria andReceiveProvLessThanOrEqualTo(String value) {
            addCriterion("receive_prov <=", value, "receiveProv");
            return (Criteria) this;
        }

        public Criteria andReceiveProvLike(String value) {
            addCriterion("receive_prov like", value, "receiveProv");
            return (Criteria) this;
        }

        public Criteria andReceiveProvNotLike(String value) {
            addCriterion("receive_prov not like", value, "receiveProv");
            return (Criteria) this;
        }

        public Criteria andReceiveProvIn(List<String> values) {
            addCriterion("receive_prov in", values, "receiveProv");
            return (Criteria) this;
        }

        public Criteria andReceiveProvNotIn(List<String> values) {
            addCriterion("receive_prov not in", values, "receiveProv");
            return (Criteria) this;
        }

        public Criteria andReceiveProvBetween(String value1, String value2) {
            addCriterion("receive_prov between", value1, value2, "receiveProv");
            return (Criteria) this;
        }

        public Criteria andReceiveProvNotBetween(String value1, String value2) {
            addCriterion("receive_prov not between", value1, value2, "receiveProv");
            return (Criteria) this;
        }

        public Criteria andReceiveCityIsNull() {
            addCriterion("receive_city is null");
            return (Criteria) this;
        }

        public Criteria andReceiveCityIsNotNull() {
            addCriterion("receive_city is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveCityEqualTo(String value) {
            addCriterion("receive_city =", value, "receiveCity");
            return (Criteria) this;
        }

        public Criteria andReceiveCityNotEqualTo(String value) {
            addCriterion("receive_city <>", value, "receiveCity");
            return (Criteria) this;
        }

        public Criteria andReceiveCityGreaterThan(String value) {
            addCriterion("receive_city >", value, "receiveCity");
            return (Criteria) this;
        }

        public Criteria andReceiveCityGreaterThanOrEqualTo(String value) {
            addCriterion("receive_city >=", value, "receiveCity");
            return (Criteria) this;
        }

        public Criteria andReceiveCityLessThan(String value) {
            addCriterion("receive_city <", value, "receiveCity");
            return (Criteria) this;
        }

        public Criteria andReceiveCityLessThanOrEqualTo(String value) {
            addCriterion("receive_city <=", value, "receiveCity");
            return (Criteria) this;
        }

        public Criteria andReceiveCityLike(String value) {
            addCriterion("receive_city like", value, "receiveCity");
            return (Criteria) this;
        }

        public Criteria andReceiveCityNotLike(String value) {
            addCriterion("receive_city not like", value, "receiveCity");
            return (Criteria) this;
        }

        public Criteria andReceiveCityIn(List<String> values) {
            addCriterion("receive_city in", values, "receiveCity");
            return (Criteria) this;
        }

        public Criteria andReceiveCityNotIn(List<String> values) {
            addCriterion("receive_city not in", values, "receiveCity");
            return (Criteria) this;
        }

        public Criteria andReceiveCityBetween(String value1, String value2) {
            addCriterion("receive_city between", value1, value2, "receiveCity");
            return (Criteria) this;
        }

        public Criteria andReceiveCityNotBetween(String value1, String value2) {
            addCriterion("receive_city not between", value1, value2, "receiveCity");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaIsNull() {
            addCriterion("receive_area is null");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaIsNotNull() {
            addCriterion("receive_area is not null");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaEqualTo(String value) {
            addCriterion("receive_area =", value, "receiveArea");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaNotEqualTo(String value) {
            addCriterion("receive_area <>", value, "receiveArea");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaGreaterThan(String value) {
            addCriterion("receive_area >", value, "receiveArea");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaGreaterThanOrEqualTo(String value) {
            addCriterion("receive_area >=", value, "receiveArea");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaLessThan(String value) {
            addCriterion("receive_area <", value, "receiveArea");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaLessThanOrEqualTo(String value) {
            addCriterion("receive_area <=", value, "receiveArea");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaLike(String value) {
            addCriterion("receive_area like", value, "receiveArea");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaNotLike(String value) {
            addCriterion("receive_area not like", value, "receiveArea");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaIn(List<String> values) {
            addCriterion("receive_area in", values, "receiveArea");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaNotIn(List<String> values) {
            addCriterion("receive_area not in", values, "receiveArea");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaBetween(String value1, String value2) {
            addCriterion("receive_area between", value1, value2, "receiveArea");
            return (Criteria) this;
        }

        public Criteria andReceiveAreaNotBetween(String value1, String value2) {
            addCriterion("receive_area not between", value1, value2, "receiveArea");
            return (Criteria) this;
        }

        public Criteria andFirstWeightIsNull() {
            addCriterion("first_weight is null");
            return (Criteria) this;
        }

        public Criteria andFirstWeightIsNotNull() {
            addCriterion("first_weight is not null");
            return (Criteria) this;
        }

        public Criteria andFirstWeightEqualTo(Integer value) {
            addCriterion("first_weight =", value, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightNotEqualTo(Integer value) {
            addCriterion("first_weight <>", value, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightGreaterThan(Integer value) {
            addCriterion("first_weight >", value, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("first_weight >=", value, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightLessThan(Integer value) {
            addCriterion("first_weight <", value, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightLessThanOrEqualTo(Integer value) {
            addCriterion("first_weight <=", value, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightIn(List<Integer> values) {
            addCriterion("first_weight in", values, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightNotIn(List<Integer> values) {
            addCriterion("first_weight not in", values, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightBetween(Integer value1, Integer value2) {
            addCriterion("first_weight between", value1, value2, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andFirstWeightNotBetween(Integer value1, Integer value2) {
            addCriterion("first_weight not between", value1, value2, "firstWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightIsNull() {
            addCriterion("step_weight is null");
            return (Criteria) this;
        }

        public Criteria andStepWeightIsNotNull() {
            addCriterion("step_weight is not null");
            return (Criteria) this;
        }

        public Criteria andStepWeightEqualTo(Integer value) {
            addCriterion("step_weight =", value, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightNotEqualTo(Integer value) {
            addCriterion("step_weight <>", value, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightGreaterThan(Integer value) {
            addCriterion("step_weight >", value, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("step_weight >=", value, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightLessThan(Integer value) {
            addCriterion("step_weight <", value, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightLessThanOrEqualTo(Integer value) {
            addCriterion("step_weight <=", value, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightIn(List<Integer> values) {
            addCriterion("step_weight in", values, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightNotIn(List<Integer> values) {
            addCriterion("step_weight not in", values, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightBetween(Integer value1, Integer value2) {
            addCriterion("step_weight between", value1, value2, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andStepWeightNotBetween(Integer value1, Integer value2) {
            addCriterion("step_weight not between", value1, value2, "stepWeight");
            return (Criteria) this;
        }

        public Criteria andWeightIsNull() {
            addCriterion("weight is null");
            return (Criteria) this;
        }

        public Criteria andWeightIsNotNull() {
            addCriterion("weight is not null");
            return (Criteria) this;
        }

        public Criteria andWeightEqualTo(Integer value) {
            addCriterion("weight =", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotEqualTo(Integer value) {
            addCriterion("weight <>", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThan(Integer value) {
            addCriterion("weight >", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("weight >=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThan(Integer value) {
            addCriterion("weight <", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightLessThanOrEqualTo(Integer value) {
            addCriterion("weight <=", value, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightIn(List<Integer> values) {
            addCriterion("weight in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotIn(List<Integer> values) {
            addCriterion("weight not in", values, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightBetween(Integer value1, Integer value2) {
            addCriterion("weight between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andWeightNotBetween(Integer value1, Integer value2) {
            addCriterion("weight not between", value1, value2, "weight");
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeIsNull() {
            addCriterion("cp_service_fee is null");
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeIsNotNull() {
            addCriterion("cp_service_fee is not null");
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeEqualTo(Integer value) {
            addCriterion("cp_service_fee =", value, "cpServiceFee");
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeNotEqualTo(Integer value) {
            addCriterion("cp_service_fee <>", value, "cpServiceFee");
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeGreaterThan(Integer value) {
            addCriterion("cp_service_fee >", value, "cpServiceFee");
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeGreaterThanOrEqualTo(Integer value) {
            addCriterion("cp_service_fee >=", value, "cpServiceFee");
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeLessThan(Integer value) {
            addCriterion("cp_service_fee <", value, "cpServiceFee");
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeLessThanOrEqualTo(Integer value) {
            addCriterion("cp_service_fee <=", value, "cpServiceFee");
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeIn(List<Integer> values) {
            addCriterion("cp_service_fee in", values, "cpServiceFee");
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeNotIn(List<Integer> values) {
            addCriterion("cp_service_fee not in", values, "cpServiceFee");
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeBetween(Integer value1, Integer value2) {
            addCriterion("cp_service_fee between", value1, value2, "cpServiceFee");
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeNotBetween(Integer value1, Integer value2) {
            addCriterion("cp_service_fee not between", value1, value2, "cpServiceFee");
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeIsNull() {
            addCriterion("freight_reward_fee is null");
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeIsNotNull() {
            addCriterion("freight_reward_fee is not null");
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeEqualTo(Integer value) {
            addCriterion("freight_reward_fee =", value, "freightRewardFee");
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeNotEqualTo(Integer value) {
            addCriterion("freight_reward_fee <>", value, "freightRewardFee");
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeGreaterThan(Integer value) {
            addCriterion("freight_reward_fee >", value, "freightRewardFee");
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeGreaterThanOrEqualTo(Integer value) {
            addCriterion("freight_reward_fee >=", value, "freightRewardFee");
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeLessThan(Integer value) {
            addCriterion("freight_reward_fee <", value, "freightRewardFee");
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeLessThanOrEqualTo(Integer value) {
            addCriterion("freight_reward_fee <=", value, "freightRewardFee");
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeIn(List<Integer> values) {
            addCriterion("freight_reward_fee in", values, "freightRewardFee");
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeNotIn(List<Integer> values) {
            addCriterion("freight_reward_fee not in", values, "freightRewardFee");
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeBetween(Integer value1, Integer value2) {
            addCriterion("freight_reward_fee between", value1, value2, "freightRewardFee");
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeNotBetween(Integer value1, Integer value2) {
            addCriterion("freight_reward_fee not between", value1, value2, "freightRewardFee");
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeIsNull() {
            addCriterion("abnormal_claim_fee is null");
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeIsNotNull() {
            addCriterion("abnormal_claim_fee is not null");
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeEqualTo(Integer value) {
            addCriterion("abnormal_claim_fee =", value, "abnormalClaimFee");
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeNotEqualTo(Integer value) {
            addCriterion("abnormal_claim_fee <>", value, "abnormalClaimFee");
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeGreaterThan(Integer value) {
            addCriterion("abnormal_claim_fee >", value, "abnormalClaimFee");
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeGreaterThanOrEqualTo(Integer value) {
            addCriterion("abnormal_claim_fee >=", value, "abnormalClaimFee");
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeLessThan(Integer value) {
            addCriterion("abnormal_claim_fee <", value, "abnormalClaimFee");
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeLessThanOrEqualTo(Integer value) {
            addCriterion("abnormal_claim_fee <=", value, "abnormalClaimFee");
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeIn(List<Integer> values) {
            addCriterion("abnormal_claim_fee in", values, "abnormalClaimFee");
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeNotIn(List<Integer> values) {
            addCriterion("abnormal_claim_fee not in", values, "abnormalClaimFee");
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeBetween(Integer value1, Integer value2) {
            addCriterion("abnormal_claim_fee between", value1, value2, "abnormalClaimFee");
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeNotBetween(Integer value1, Integer value2) {
            addCriterion("abnormal_claim_fee not between", value1, value2, "abnormalClaimFee");
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeIsNull() {
            addCriterion("user_rights_fee is null");
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeIsNotNull() {
            addCriterion("user_rights_fee is not null");
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeEqualTo(Integer value) {
            addCriterion("user_rights_fee =", value, "userRightsFee");
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeNotEqualTo(Integer value) {
            addCriterion("user_rights_fee <>", value, "userRightsFee");
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeGreaterThan(Integer value) {
            addCriterion("user_rights_fee >", value, "userRightsFee");
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_rights_fee >=", value, "userRightsFee");
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeLessThan(Integer value) {
            addCriterion("user_rights_fee <", value, "userRightsFee");
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeLessThanOrEqualTo(Integer value) {
            addCriterion("user_rights_fee <=", value, "userRightsFee");
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeIn(List<Integer> values) {
            addCriterion("user_rights_fee in", values, "userRightsFee");
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeNotIn(List<Integer> values) {
            addCriterion("user_rights_fee not in", values, "userRightsFee");
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeBetween(Integer value1, Integer value2) {
            addCriterion("user_rights_fee between", value1, value2, "userRightsFee");
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeNotBetween(Integer value1, Integer value2) {
            addCriterion("user_rights_fee not between", value1, value2, "userRightsFee");
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeIsNull() {
            addCriterion("pl3_user_rights_fee is null");
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeIsNotNull() {
            addCriterion("pl3_user_rights_fee is not null");
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeEqualTo(Integer value) {
            addCriterion("pl3_user_rights_fee =", value, "pl3UserRightsFee");
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeNotEqualTo(Integer value) {
            addCriterion("pl3_user_rights_fee <>", value, "pl3UserRightsFee");
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeGreaterThan(Integer value) {
            addCriterion("pl3_user_rights_fee >", value, "pl3UserRightsFee");
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeGreaterThanOrEqualTo(Integer value) {
            addCriterion("pl3_user_rights_fee >=", value, "pl3UserRightsFee");
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeLessThan(Integer value) {
            addCriterion("pl3_user_rights_fee <", value, "pl3UserRightsFee");
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeLessThanOrEqualTo(Integer value) {
            addCriterion("pl3_user_rights_fee <=", value, "pl3UserRightsFee");
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeIn(List<Integer> values) {
            addCriterion("pl3_user_rights_fee in", values, "pl3UserRightsFee");
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeNotIn(List<Integer> values) {
            addCriterion("pl3_user_rights_fee not in", values, "pl3UserRightsFee");
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeBetween(Integer value1, Integer value2) {
            addCriterion("pl3_user_rights_fee between", value1, value2, "pl3UserRightsFee");
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeNotBetween(Integer value1, Integer value2) {
            addCriterion("pl3_user_rights_fee not between", value1, value2, "pl3UserRightsFee");
            return (Criteria) this;
        }

        public Criteria andFeatureIsNull() {
            addCriterion("feature is null");
            return (Criteria) this;
        }

        public Criteria andFeatureIsNotNull() {
            addCriterion("feature is not null");
            return (Criteria) this;
        }

        public Criteria andFeatureEqualTo(String value) {
            addCriterion("feature =", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotEqualTo(String value) {
            addCriterion("feature <>", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThan(String value) {
            addCriterion("feature >", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanOrEqualTo(String value) {
            addCriterion("feature >=", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLessThan(String value) {
            addCriterion("feature <", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanOrEqualTo(String value) {
            addCriterion("feature <=", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLike(String value) {
            addCriterion("feature like", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotLike(String value) {
            addCriterion("feature not like", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureIn(List<String> values) {
            addCriterion("feature in", values, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotIn(List<String> values) {
            addCriterion("feature not in", values, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureBetween(String value1, String value2) {
            addCriterion("feature between", value1, value2, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotBetween(String value1, String value2) {
            addCriterion("feature not between", value1, value2, "feature");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_summary_id =", value, "billSummaryId");
            }
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_summary_id <>", value, "billSummaryId");
            }
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_summary_id >", value, "billSummaryId");
            }
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_summary_id >=", value, "billSummaryId");
            }
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_summary_id <", value, "billSummaryId");
            }
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_summary_id <=", value, "billSummaryId");
            }
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_summary_id like", value, "billSummaryId");
            }
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_summary_id not like", value, "billSummaryId");
            }
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("bill_summary_id in", values, "billSummaryId");
            }
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("bill_summary_id not in", values, "billSummaryId");
            }
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("bill_summary_id between", value1, value2, "billSummaryId");
            }
            return (Criteria) this;
        }

        public Criteria andBillSummaryIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("bill_summary_id not between", value1, value2, "billSummaryId");
            }
            return (Criteria) this;
        }

        public Criteria andBillMonthEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_month =", value, "billMonth");
            }
            return (Criteria) this;
        }

        public Criteria andBillMonthNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_month <>", value, "billMonth");
            }
            return (Criteria) this;
        }

        public Criteria andBillMonthGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_month >", value, "billMonth");
            }
            return (Criteria) this;
        }

        public Criteria andBillMonthGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_month >=", value, "billMonth");
            }
            return (Criteria) this;
        }

        public Criteria andBillMonthLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_month <", value, "billMonth");
            }
            return (Criteria) this;
        }

        public Criteria andBillMonthLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_month <=", value, "billMonth");
            }
            return (Criteria) this;
        }

        public Criteria andBillMonthLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_month like", value, "billMonth");
            }
            return (Criteria) this;
        }

        public Criteria andBillMonthNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_month not like", value, "billMonth");
            }
            return (Criteria) this;
        }

        public Criteria andBillMonthInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("bill_month in", values, "billMonth");
            }
            return (Criteria) this;
        }

        public Criteria andBillMonthNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("bill_month not in", values, "billMonth");
            }
            return (Criteria) this;
        }

        public Criteria andBillMonthBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("bill_month between", value1, value2, "billMonth");
            }
            return (Criteria) this;
        }

        public Criteria andBillMonthNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("bill_month not between", value1, value2, "billMonth");
            }
            return (Criteria) this;
        }

        public Criteria andMailNoEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("mail_no =", value, "mailNo");
            }
            return (Criteria) this;
        }

        public Criteria andMailNoNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("mail_no <>", value, "mailNo");
            }
            return (Criteria) this;
        }

        public Criteria andMailNoGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("mail_no >", value, "mailNo");
            }
            return (Criteria) this;
        }

        public Criteria andMailNoGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("mail_no >=", value, "mailNo");
            }
            return (Criteria) this;
        }

        public Criteria andMailNoLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("mail_no <", value, "mailNo");
            }
            return (Criteria) this;
        }

        public Criteria andMailNoLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("mail_no <=", value, "mailNo");
            }
            return (Criteria) this;
        }

        public Criteria andMailNoLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("mail_no like", value, "mailNo");
            }
            return (Criteria) this;
        }

        public Criteria andMailNoNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("mail_no not like", value, "mailNo");
            }
            return (Criteria) this;
        }

        public Criteria andMailNoInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("mail_no in", values, "mailNo");
            }
            return (Criteria) this;
        }

        public Criteria andMailNoNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("mail_no not in", values, "mailNo");
            }
            return (Criteria) this;
        }

        public Criteria andMailNoBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("mail_no between", value1, value2, "mailNo");
            }
            return (Criteria) this;
        }

        public Criteria andMailNoNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("mail_no not between", value1, value2, "mailNo");
            }
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("out_order_code =", value, "outOrderCode");
            }
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("out_order_code <>", value, "outOrderCode");
            }
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("out_order_code >", value, "outOrderCode");
            }
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("out_order_code >=", value, "outOrderCode");
            }
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("out_order_code <", value, "outOrderCode");
            }
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("out_order_code <=", value, "outOrderCode");
            }
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("out_order_code like", value, "outOrderCode");
            }
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("out_order_code not like", value, "outOrderCode");
            }
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("out_order_code in", values, "outOrderCode");
            }
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("out_order_code not in", values, "outOrderCode");
            }
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("out_order_code between", value1, value2, "outOrderCode");
            }
            return (Criteria) this;
        }

        public Criteria andOutOrderCodeNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("out_order_code not between", value1, value2, "outOrderCode");
            }
            return (Criteria) this;
        }

        public Criteria andBillTypeEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_type =", value, "billType");
            }
            return (Criteria) this;
        }

        public Criteria andBillTypeNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_type <>", value, "billType");
            }
            return (Criteria) this;
        }

        public Criteria andBillTypeGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_type >", value, "billType");
            }
            return (Criteria) this;
        }

        public Criteria andBillTypeGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_type >=", value, "billType");
            }
            return (Criteria) this;
        }

        public Criteria andBillTypeLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_type <", value, "billType");
            }
            return (Criteria) this;
        }

        public Criteria andBillTypeLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_type <=", value, "billType");
            }
            return (Criteria) this;
        }

        public Criteria andBillTypeLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_type like", value, "billType");
            }
            return (Criteria) this;
        }

        public Criteria andBillTypeNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_type not like", value, "billType");
            }
            return (Criteria) this;
        }

        public Criteria andBillTypeInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("bill_type in", values, "billType");
            }
            return (Criteria) this;
        }

        public Criteria andBillTypeNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("bill_type not in", values, "billType");
            }
            return (Criteria) this;
        }

        public Criteria andBillTypeBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("bill_type between", value1, value2, "billType");
            }
            return (Criteria) this;
        }

        public Criteria andBillTypeNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("bill_type not between", value1, value2, "billType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type =", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type <>", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type >", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type >=", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type <", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type <=", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type like", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("settlement_type not like", value, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("settlement_type in", values, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("settlement_type not in", values, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("settlement_type between", value1, value2, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andSettlementTypeNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("settlement_type not between", value1, value2, "settlementType");
            }
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_create_time =", value, "orderCreateTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_create_time <>", value, "orderCreateTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_create_time >", value, "orderCreateTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_create_time >=", value, "orderCreateTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_create_time <", value, "orderCreateTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_create_time <=", value, "orderCreateTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("order_create_time in", values, "orderCreateTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("order_create_time not in", values, "orderCreateTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("order_create_time between", value1, value2, "orderCreateTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderCreateTimeNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("order_create_time not between", value1, value2, "orderCreateTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_got_time =", value, "orderGotTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_got_time <>", value, "orderGotTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_got_time >", value, "orderGotTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_got_time >=", value, "orderGotTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_got_time <", value, "orderGotTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_got_time <=", value, "orderGotTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("order_got_time in", values, "orderGotTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("order_got_time not in", values, "orderGotTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("order_got_time between", value1, value2, "orderGotTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderGotTimeNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("order_got_time not between", value1, value2, "orderGotTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_charge_time =", value, "orderChargeTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_charge_time <>", value, "orderChargeTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_charge_time >", value, "orderChargeTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_charge_time >=", value, "orderChargeTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_charge_time <", value, "orderChargeTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("order_charge_time <=", value, "orderChargeTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("order_charge_time in", values, "orderChargeTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("order_charge_time not in", values, "orderChargeTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("order_charge_time between", value1, value2, "orderChargeTime");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChargeTimeNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("order_charge_time not between", value1, value2, "orderChargeTime");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov =", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov <>", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov >", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov >=", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov <", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov <=", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov like", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_prov not like", value, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("send_prov in", values, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("send_prov not in", values, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("send_prov between", value1, value2, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendProvNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("send_prov not between", value1, value2, "sendProv");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city =", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city <>", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city >", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city >=", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city <", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city <=", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city like", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_city not like", value, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("send_city in", values, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("send_city not in", values, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("send_city between", value1, value2, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendCityNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("send_city not between", value1, value2, "sendCity");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area =", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area <>", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area >", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area >=", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area <", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area <=", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area like", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("send_area not like", value, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("send_area in", values, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("send_area not in", values, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("send_area between", value1, value2, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andSendAreaNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("send_area not between", value1, value2, "sendArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveProvEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_prov =", value, "receiveProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveProvNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_prov <>", value, "receiveProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveProvGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_prov >", value, "receiveProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveProvGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_prov >=", value, "receiveProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveProvLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_prov <", value, "receiveProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveProvLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_prov <=", value, "receiveProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveProvLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_prov like", value, "receiveProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveProvNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_prov not like", value, "receiveProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveProvInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("receive_prov in", values, "receiveProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveProvNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("receive_prov not in", values, "receiveProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveProvBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("receive_prov between", value1, value2, "receiveProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveProvNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("receive_prov not between", value1, value2, "receiveProv");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveCityEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_city =", value, "receiveCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveCityNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_city <>", value, "receiveCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveCityGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_city >", value, "receiveCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveCityGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_city >=", value, "receiveCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveCityLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_city <", value, "receiveCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveCityLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_city <=", value, "receiveCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveCityLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_city like", value, "receiveCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveCityNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_city not like", value, "receiveCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveCityInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("receive_city in", values, "receiveCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveCityNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("receive_city not in", values, "receiveCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveCityBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("receive_city between", value1, value2, "receiveCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveCityNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("receive_city not between", value1, value2, "receiveCity");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveAreaEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_area =", value, "receiveArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveAreaNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_area <>", value, "receiveArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveAreaGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_area >", value, "receiveArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveAreaGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_area >=", value, "receiveArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveAreaLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_area <", value, "receiveArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveAreaLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_area <=", value, "receiveArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveAreaLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_area like", value, "receiveArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveAreaNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("receive_area not like", value, "receiveArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveAreaInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("receive_area in", values, "receiveArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveAreaNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("receive_area not in", values, "receiveArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveAreaBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("receive_area between", value1, value2, "receiveArea");
            }
            return (Criteria) this;
        }

        public Criteria andReceiveAreaNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("receive_area not between", value1, value2, "receiveArea");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight =", value, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight <>", value, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight >", value, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight >=", value, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight <", value, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("first_weight <=", value, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("first_weight in", values, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("first_weight not in", values, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("first_weight between", value1, value2, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andFirstWeightNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("first_weight not between", value1, value2, "firstWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight =", value, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight <>", value, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight >", value, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight >=", value, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight <", value, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("step_weight <=", value, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("step_weight in", values, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("step_weight not in", values, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("step_weight between", value1, value2, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andStepWeightNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("step_weight not between", value1, value2, "stepWeight");
            }
            return (Criteria) this;
        }

        public Criteria andWeightEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("weight =", value, "weight");
            }
            return (Criteria) this;
        }

        public Criteria andWeightNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("weight <>", value, "weight");
            }
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("weight >", value, "weight");
            }
            return (Criteria) this;
        }

        public Criteria andWeightGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("weight >=", value, "weight");
            }
            return (Criteria) this;
        }

        public Criteria andWeightLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("weight <", value, "weight");
            }
            return (Criteria) this;
        }

        public Criteria andWeightLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("weight <=", value, "weight");
            }
            return (Criteria) this;
        }

        public Criteria andWeightInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("weight in", values, "weight");
            }
            return (Criteria) this;
        }

        public Criteria andWeightNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("weight not in", values, "weight");
            }
            return (Criteria) this;
        }

        public Criteria andWeightBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("weight between", value1, value2, "weight");
            }
            return (Criteria) this;
        }

        public Criteria andWeightNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("weight not between", value1, value2, "weight");
            }
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("cp_service_fee =", value, "cpServiceFee");
            }
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("cp_service_fee <>", value, "cpServiceFee");
            }
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("cp_service_fee >", value, "cpServiceFee");
            }
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("cp_service_fee >=", value, "cpServiceFee");
            }
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("cp_service_fee <", value, "cpServiceFee");
            }
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("cp_service_fee <=", value, "cpServiceFee");
            }
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("cp_service_fee in", values, "cpServiceFee");
            }
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("cp_service_fee not in", values, "cpServiceFee");
            }
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("cp_service_fee between", value1, value2, "cpServiceFee");
            }
            return (Criteria) this;
        }

        public Criteria andCpServiceFeeNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("cp_service_fee not between", value1, value2, "cpServiceFee");
            }
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("freight_reward_fee =", value, "freightRewardFee");
            }
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("freight_reward_fee <>", value, "freightRewardFee");
            }
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("freight_reward_fee >", value, "freightRewardFee");
            }
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("freight_reward_fee >=", value, "freightRewardFee");
            }
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("freight_reward_fee <", value, "freightRewardFee");
            }
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("freight_reward_fee <=", value, "freightRewardFee");
            }
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("freight_reward_fee in", values, "freightRewardFee");
            }
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("freight_reward_fee not in", values, "freightRewardFee");
            }
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("freight_reward_fee between", value1, value2, "freightRewardFee");
            }
            return (Criteria) this;
        }

        public Criteria andFreightRewardFeeNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("freight_reward_fee not between", value1, value2, "freightRewardFee");
            }
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("abnormal_claim_fee =", value, "abnormalClaimFee");
            }
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("abnormal_claim_fee <>", value, "abnormalClaimFee");
            }
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("abnormal_claim_fee >", value, "abnormalClaimFee");
            }
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("abnormal_claim_fee >=", value, "abnormalClaimFee");
            }
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("abnormal_claim_fee <", value, "abnormalClaimFee");
            }
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("abnormal_claim_fee <=", value, "abnormalClaimFee");
            }
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("abnormal_claim_fee in", values, "abnormalClaimFee");
            }
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("abnormal_claim_fee not in", values, "abnormalClaimFee");
            }
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("abnormal_claim_fee between", value1, value2, "abnormalClaimFee");
            }
            return (Criteria) this;
        }

        public Criteria andAbnormalClaimFeeNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("abnormal_claim_fee not between", value1, value2, "abnormalClaimFee");
            }
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_rights_fee =", value, "userRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_rights_fee <>", value, "userRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_rights_fee >", value, "userRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_rights_fee >=", value, "userRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_rights_fee <", value, "userRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("user_rights_fee <=", value, "userRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_rights_fee in", values, "userRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_rights_fee not in", values, "userRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_rights_fee between", value1, value2, "userRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andUserRightsFeeNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_rights_fee not between", value1, value2, "userRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("pl3_user_rights_fee =", value, "pl3UserRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("pl3_user_rights_fee <>", value, "pl3UserRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("pl3_user_rights_fee >", value, "pl3UserRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("pl3_user_rights_fee >=", value, "pl3UserRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("pl3_user_rights_fee <", value, "pl3UserRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("pl3_user_rights_fee <=", value, "pl3UserRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("pl3_user_rights_fee in", values, "pl3UserRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("pl3_user_rights_fee not in", values, "pl3UserRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("pl3_user_rights_fee between", value1, value2, "pl3UserRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andPl3UserRightsFeeNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("pl3_user_rights_fee not between", value1, value2, "pl3UserRightsFee");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature =", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature <>", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature >", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature >=", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature <", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature <=", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature like", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature not like", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("feature in", values, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("feature not in", values, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("feature between", value1, value2, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("feature not between", value1, value2, "feature");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *汇总账单ID
         */
        BILLSUMMARYID("bill_summary_id"),
        /**
         *账单月份
         */
        BILLMONTH("bill_month"),
        /**
         *运单号
         */
        MAILNO("mail_no"),
        /**
         *订单号
         */
        OUTORDERCODE("out_order_code"),
        /**
         *账单类型
         */
        BILLTYPE("bill_type"),
        /**
         *结算类型
         */
        SETTLEMENTTYPE("settlement_type"),
        /**
         *订单创建时间
         */
        ORDERCREATETIME("order_create_time"),
        /**
         *订单揽收时间
         */
        ORDERGOTTIME("order_got_time"),
        /**
         *订单计费时间
         */
        ORDERCHARGETIME("order_charge_time"),
        /**
         *出发省份
         */
        SENDPROV("send_prov"),
        /**
         *出发城市
         */
        SENDCITY("send_city"),
        /**
         *出发区县
         */
        SENDAREA("send_area"),
        /**
         *到达省份
         */
        RECEIVEPROV("receive_prov"),
        /**
         *到达城市
         */
        RECEIVECITY("receive_city"),
        /**
         *到达区县
         */
        RECEIVEAREA("receive_area"),
        /**
         *首重，单位克
         */
        FIRSTWEIGHT("first_weight"),
        /**
         *续重，单位克
         */
        STEPWEIGHT("step_weight"),
        /**
         *包裹重量，单位克
         */
        WEIGHT("weight"),
        /**
         *供应链服务费，单位分
         */
        CPSERVICEFEE("cp_service_fee"),
        /**
         *运费奖励费用，单位分
         */
        FREIGHTREWARDFEE("freight_reward_fee"),
        /**
         *异常单理赔费用，单位分
         */
        ABNORMALCLAIMFEE("abnormal_claim_fee"),
        /**
         *用户权益费用，单位分
         */
        USERRIGHTSFEE("user_rights_fee"),
        /**
         *3PL用户权益费用，单位分
         */
        PL3USERRIGHTSFEE("pl3_user_rights_fee"),
        /**
         *扩展字段
         */
        FEATURE("feature");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}