package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * 电子面单账户余额提醒规则配置表
 * <AUTHOR>
 * @since 2017/05/05
 */
public class AccountBalanceAlarmDO {

    /**
     * 主键
     */
    private Long    id;

    /**
     * 创建时间
     */
    private Date    gmtCreate;

    /**
     * 修改时间
     */
    private Date    gmtModified;

    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 物流商ID
     */
    private Long    cpId;

    /**
     * 网点编码
     */
    private String  branchCode;

    /**
     * 余额告警值
     */
    private Integer alarmQuantity;

    /**
     * 上一次发送余额告警消息的时间
     */
    private Date    lastAlarmTime;
    
    /**
     * 发送间隔时间（小时）
     */
    private Integer intervalHour;
    
    /**
     * 号段用途编码,用于区分一个CP下的不同号段
     */
    private String segmentCode;
    

    /**
     * 用于短信提醒的电话号码
     */
    private String phone;

    /**
     * setter for column 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * getter for column 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * setter for column 创建时间
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * getter for column 创建时间
     */
    public Date getGmtCreate() {
        return this.gmtCreate;
    }

    /**
     * setter for column 修改时间
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * getter for column 修改时间
     */
    public Date getGmtModified() {
        return this.gmtModified;
    }

    /**
     * setter for column 商家ID
     */
    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    /**
     * getter for column 商家ID
     */
    public Long getAccountId() {
        return this.accountId;
    }

    /**
     * setter for column 物流商ID
     */
    public void setCpId(Long cpId) {
        this.cpId = cpId;
    }

    /**
     * getter for column 物流商ID
     */
    public Long getCpId() {
        return this.cpId;
    }

    /**
     * setter for column 网点编码
     */
    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    /**
     * getter for column 网点编码
     */
    public String getBranchCode() {
        return this.branchCode;
    }

    /**
     * setter for column 余额告警值
     */
    public void setAlarmQuantity(Integer alarmQuantity) {
        this.alarmQuantity = alarmQuantity;
    }

    /**
     * getter for column 余额告警值
     */
    public Integer getAlarmQuantity() {
        return this.alarmQuantity;
    }

    /**
     * setter for column 上一次发送余额告警消息的时间
     */
    public void setLastAlarmTime(Date lastAlarmTime) {
        this.lastAlarmTime = lastAlarmTime;
    }

    /**
     * getter for column 上一次发送余额告警消息的时间
     */
    public Date getLastAlarmTime() {
        return this.lastAlarmTime;
    }

    /**
     * setter for column 发送间隔时间（小时）
     */
    public void setIntervalHour(Integer intervalHour) {
        this.intervalHour = intervalHour;
    }

    /**
     * getter for column 发送间隔时间（小时）
     */
    public Integer getIntervalHour() {
        return this.intervalHour;
    }

    public String getSegmentCode() {
		return segmentCode;
	}

	public void setSegmentCode(String segmentCode) {
		this.segmentCode = segmentCode;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

    public AccountBalanceAlarmDO(Long accountId, Long cpId, String branchCode, Integer alarmQuantity, Integer intervalHour, String segmentCode, String phone) {
        this.accountId = accountId;
        this.cpId = cpId;
        this.branchCode = branchCode;
        this.alarmQuantity = alarmQuantity;
        this.intervalHour = intervalHour;
        this.segmentCode = segmentCode;
        this.phone = phone;
    }

    public AccountBalanceAlarmDO(){};
}
