package com.cainiao.waybill.bridge.common.waybill.pickup.manager;

import com.taobao.biz.common.division.newera.exception.DivisionException;
import com.taobao.biz.common.division.newera.vo.DivisionParse;

/**
 * <AUTHOR>
 * @date 2021/8/18-下午2:12
 */
public interface DivisionManager {

    /**
     * 四级地址解析
     *
     * @param fullAddress：传入下级，获得下级及其上级的名称及id；也可以传入详细地址
     * @return ：
     */
    DivisionParse parseDivision(String fullAddress) throws DivisionException;
}
