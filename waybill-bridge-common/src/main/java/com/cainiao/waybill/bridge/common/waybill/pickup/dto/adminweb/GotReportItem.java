package com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: yexin
 * @date: 2022-05-16 15:04
 **/
@Data
public class GotReportItem implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 应揽收订单总量
     */
    private Integer shouldAllNum;
    /**
     * 应揽收订单揽收量
     */
    private Integer shouldGotNum;
    /**
     * 应揽收订单未及时揽收量
     */
    private Integer shouldNotGotNum;
    /**
     * 应揽收订单揽收率
     */
    private String shouldGotRate;
    /**
     * 应揽收订单揽收率值
     */
    private double shouldGotRateValue;

    /**
     * 实时订单总量
     */
    private Integer realAllNum;
    /**
     * 实时订单揽收量
     */
    private Integer realGotNum;
    /**
     * 实时订单未及时揽收量
     */
    private Integer realNotGotNum;
    /**
     * 实时订单揽收率
     */
    private String realGotRate;
    /**
     * 实时订单揽收率值
     */
    private double realGotRateValue;

    /**
     * 预约订单总量
     */
    private Integer appointAllNum;
    /**
     * 预约订单揽收量
     */
    private Integer appointGotNum;
    /**
     * 预约订单未及时揽收量
     */
    private Integer appointNotGotNum;
    /**
     * 预约订单揽收率
     */
    private String appointGotRate;
    /**
     * 预约订单揽收率值
     */
    private double appointGotRateValue;
}
