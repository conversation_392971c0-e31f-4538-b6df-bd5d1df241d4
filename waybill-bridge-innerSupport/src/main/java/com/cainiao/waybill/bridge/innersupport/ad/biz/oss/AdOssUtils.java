package com.cainiao.waybill.bridge.innersupport.ad.biz.oss;

import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.AdErrorConstants;
import com.cainiao.waybill.bridge.innersupport.ad.exception.AdBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import java.net.URL;
import java.util.Date;

public class AdOssUtils {
    /**
     * oss bucket name
     */
    private static final String IMAGE_BUCKET_NAME = AdOssClientFactory.AD_WORK_ORDER_BUCKET_NAME;

    /**
     * 根据给定的URL生成预签名的OSS URL
     * @param url 待生成预签名URL的原始URL
     * @param expiration 过期时间
     * @return 生成的预签名OSS URL的HTTPS协议的URL字符串，若生成失败则返回null
     * @throws AdBusinessException 当生成预签名URL失败时抛出该异常
     */
    public static String generateOssPresignedUrl(String url, Date expiration) {
        try {
            if (StringUtils.isBlank(url)) {
                return null;
            }
            URL uri = new URL(url);
            String path = uri.getPath();
            String[] segments = path.split("/");
            String fileName = segments.length > 0 ? segments[segments.length - 1] : null;
            if (StringUtils.isBlank(fileName)) {
                return null;
            }
            // 创建OSSClient实例。
            OSSClient ossClient = AdOssClientFactory.getOssClient();
            // 创建预签名URL请求
            GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(
                    IMAGE_BUCKET_NAME,
                    AdOssClientFactory.OSS_IMG_FILE_DIR + "/" + fileName,
                    HttpMethod.GET
            );
            req.setExpiration(expiration);
            // oss client 生成的签名除了替换换成https外，不要进行其他的处理
            URL signedUrl = ossClient.generatePresignedUrl(req);
            return convertToHttpsUrl(signedUrl);
        } catch (OSSException e) {
            throw new AdBusinessException(AdErrorConstants.GET_IMAGE_URL_FAIL.getErrorCode(),
                    e.getMessage());
        } catch (Exception e) {
            throw new AdBusinessException(AdErrorConstants.SYSTEM_ERROR.getErrorCode(), e.getMessage());
        }
    }

    /**
     * 根据给定的URL生成预签名的OSS URL
     * @param url 待生成预签名URL的原始URL
     * @return 生成的预签名OSS URL的HTTPS协议的URL字符串，若生成失败则返回null
     * @throws AdBusinessException 当生成预签名URL失败时抛出该异常
     */
    public static String generateOssPresignedUrl(String url) {
        // 指定天数后的过期日期
        int expireDays = 1;
        Date expiration = DateUtils.addDays(new Date(), expireDays);
        return generateOssPresignedUrl(url, expiration);
    }

    /**
     * 将URL转换为HTTPS协议
     * @param url 原始URL
     * @return HTTPS协议的URL字符串
     */
    private static String convertToHttpsUrl(URL url) {
        return url.toString().replace("http://", "https://");
    }
}
