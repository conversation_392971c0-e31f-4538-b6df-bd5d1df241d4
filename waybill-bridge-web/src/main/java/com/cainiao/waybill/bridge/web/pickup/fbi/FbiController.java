package com.cainiao.waybill.bridge.web.pickup.fbi;

import com.alibaba.da.common.bean.Result;
import com.alibaba.da.infra.client.resource.ResourceAPI;
import com.alibaba.da.infra.client.resource.constant.ResourceConstants;
import com.alibaba.da.infra.client.resource.po.UrlParam;
import com.alibaba.da.infra.client.resource.service.UrlService;
import com.alibaba.fastjson.JSON;
import com.cainiao.cnlogin.api.context.CnUserInfo;
import com.cainiao.cnlogin.api.context.CnUserInfoUtil;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.exception.PickUpBusinessException;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpBaseRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.TicketRoleEnum;
import com.cainiao.waybill.bridge.web.charity.controller.BaseController;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.pickup.constant.TicketResourceEnum;
import com.cainiao.waybill.bridge.web.pickup.ticket.role.PickUpResourceAuthority;
import com.cainiao.waybill.common.util.StringUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2024/12/2.
 */
@Slf4j(topic = "PICK_UP_MANAGER_INFO")
@RestController
public class FbiController extends BaseController {


    @RequestMapping("/pickup/fbi/logistics")
    @PickUpResourceAuthority(TicketResourceEnum.LOGISTIC_FBI)
    public BaseResult<String> logistics(PickUpBaseRequest request) {
        try {
            final UrlParam param = buildBaseParams(BridgeSwitch.LOGISTICS_FBI_ID);

            //添加其他业务的加密参数
            final Map<String, String> resourceParamMap = Maps.newHashMap();

            if (CollectionUtils.isEmpty(request.getUserSource())) {
                return BaseResult.bizFail(PickUpConstants.Error.SYSTEM_PARAM_ERROR.getErrorCode(), "用户没有权限");
            }

            // 菜鸟的都可以看
            if (!StringUtils.equals(TicketRoleEnum.admin.name(), request.getRole())) {
                // 限制用户看到的行数据
                final List<String> appkeys = request.getUserSource().stream()
                        .map(s -> BridgeSwitch.APPKEY_OF_USER_SOURCE.get(s)).filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(appkeys)) {
                    return BaseResult.bizFail(PickUpConstants.Error.SYSTEM_PARAM_ERROR.getErrorCode(), "用户没有权限");
                }
                resourceParamMap.put("appkey", String.join(",", appkeys));
            }
            // 下载
            resourceParamMap.put("down", "1");
            param.setResourceParamMap(resourceParamMap);

            final UrlService urlService = ResourceAPI.getUrlService();
            final Result<String> result = urlService.getUrl(param);

            if (result != null && StringUtils.isNotEmpty(result.getReturnValue())) {
                return BaseResult.success(result.getReturnValue());
            }
            if (result == null) {
                return BaseResult.bizFail(PickUpConstants.Error.SYSTEM_ERROR.getErrorCode(), "获取报表链接失败");
            }
            return BaseResult.bizFail(PickUpConstants.Error.SYSTEM_ERROR.getErrorCode(), result.getReturnMessage());
        } catch (Exception e) {
            log.error("logistics fbi error {}, e ", JSON.toJSONString(request), e);
            return BaseResult.bizFail(PickUpConstants.Error.SYSTEM_ERROR.getErrorCode(), e.getMessage());
        }
    }

    @NotNull
    private static UrlParam buildBaseParams(String resourceId) {
        final UrlParam param = new UrlParam();
        //你的应用名，和申请的时候保持一致
        param.setAppName("waybill-bridge");
        //应用秘钥，申请时管理员分配的
        param.setAppSecret(BridgeSwitch.FBI_SECRET);
        //当前登录人，填写登录账号

        // 菜鸟账号
        final CnUserInfo userInfo = CnUserInfoUtil.getLoginContext();
        if(userInfo == null || StringUtil.isBlank(userInfo.getAccount())){
            throw new PickUpBusinessException(PickUpConstants.Error.SYSTEM_PARAM_ERROR.getErrorCode(), "用户没有权限");
        }
        param.setLoginId(String.valueOf(userInfo.getEmployeeId() != null && userInfo.getEmployeeId() > 0 ? userInfo.getEmployeeId() : userInfo.getAccountId()));
        //设置当前账号体系  LOGIN_MODE_DA  为开放登录
        param.setLoginMode(ResourceConstants.LOGIN_MODE_CAINIAO);
        //报表ID,报表发布后会有一个报表ID，可以在报表列表页面查看
        param.setResourceId(resourceId);
        //RESOURCE_TYPE_REPORT 固定格式，不要改变
        param.setResourceType(ResourceConstants.RESOURCE_TYPE_REPORT);

        //添加其他URL参数，非加密参数
        final Map<String, String> urlParamMap = Maps.newHashMap();
        urlParamMap.put("hiddenOwner", "true");
        urlParamMap.put("hiddenTitle", "true");
        urlParamMap.put("copyright", "false");
        urlParamMap.put("hideTools", "true");
        urlParamMap.put("hideNav", "true");
        param.setUrlParams(urlParamMap);
        return param;
    }

}
