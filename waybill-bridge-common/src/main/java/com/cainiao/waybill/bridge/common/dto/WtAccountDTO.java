package com.cainiao.waybill.bridge.common.dto;

import java.util.Date;
import java.util.Map;

import lombok.Data;

/**
 * 淘外结算平台账号模型
 * <AUTHOR>
 * @date 2024/8/28 19:42
 **/
@Data
public class WtAccountDTO extends BaseDTO{

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 归属人Id
     */
    private String ownerId;

    /**
     * id类型: 5-网点ID
     */
    private Integer ownerIdType;

    /**
     * 可用余额 单位:分
     */
    private Long balance;

    /**
     * 已使用数量
     */
    private Long used;

    /**
     * 账户类型: common-通用账户类型
     */
    private String accountType;

    /**
     * 扩展信息
     */
    private Map<String, String> featuresMap;

}
