package com.cainiao.waybill.bridge.enterprise.administrators.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.dingtalkcontact_1_0.Client;
import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponse;
import com.aliyun.dingtalkcontact_1_0.models.SearchUserResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.cainiao.waybill.bridge.biz.utils.MobilePhoneUtil;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.enterprise.administrators.request.User;
import com.cainiao.waybill.bridge.enterprise.administrators.response.*;
import com.cainiao.waybill.bridge.enterprise.administrators.service.DingTalkUserService;
import com.cainiao.waybill.bridge.enterprise.administrators.request.EnterpriseUserRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.GetEnterpriseUserInfoRequest;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseSwitchHolder;
import com.cainiao.waybill.bridge.enterprise.common.UserContext;
import com.cainiao.waybill.bridge.enterprise.common.constant.DingTalkApiUrlConstant;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePlatformEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseUserStatusEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseUserTypeEnum;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterpriseUserInfoConverter;
import com.cainiao.waybill.bridge.enterprise.utils.PhoneNumberValidator;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoParam;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserPhoneNumDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserPhoneNumParam;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.UserDeptInfoDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserInfoDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePostMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserPhoneNumMapper;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 该类用于提供钉钉用户服务的实现，包括一些用户相关的操作。
 * <AUTHOR>
 * @date 2025-04-15 20:30:43
 */
@Service
public class DingTalkUserServiceImpl implements DingTalkUserService {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private DingTalkAuthService dingTalkAuthService;

    @Autowired
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;

    @Autowired
    private WaybillBridgeEnterpriseUserInfoConverter waybillBridgeEnterpriseUserInfoConverter;

    @Autowired
    private WaybillBridgeEnterpriseUserPhoneNumMapper enterpriseUserPhoneNumMapper;

    @Autowired
    private WaybillBridgeEnterprisePostMapper enterprisePostMapper;

    @Autowired
    Client dingTalkContractClient;

    private static final Executor threadPoolExecutor =
            new ThreadPoolExecutor(2, 5,1, TimeUnit.HOURS,
                    new ArrayBlockingQueue<>(128),new ThreadPoolExecutor.AbortPolicy());
    private static final ScheduledExecutorService scheduled = new ScheduledThreadPoolExecutor(1);
    private static final int BATCH_SIZE = 1000;


    /**
     * 查询管理员列表
     *
     * @return
     */
    @Override
    public List<ListAdminResponse> getAdminUserIdList(String corpId) {
        String corpAccessToken = dingTalkAuthService.getCorpAccessToken(EnterprisePlatformEnum.DingTalk.name(), corpId);
        if(StringUtils.isEmpty(corpAccessToken)) {
            LOGGER.error("corpId get corpAccessToken failed");
            throw new EnterpriseException(EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.code(), EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.describe());
        }
        List<ListAdminResponse> adminResponseList = new ArrayList<>();
        try {
            DingTalkClient client = new DefaultDingTalkClient(DingTalkApiUrlConstant.GET_ADMIN_USERID_LIST);
            OapiUserListadminRequest req = new OapiUserListadminRequest();
            OapiUserListadminResponse rsp = client.execute(req, corpAccessToken);
            if(null == rsp.getResult()) {
                throw new EnterpriseException(EnterpriseErrorEnum.QUERY_ADMIN_USER_ID_FAILED.code(), EnterpriseErrorEnum.QUERY_ADMIN_USER_ID_FAILED.describe());
            }
            rsp.getResult().forEach(o -> {
                ListAdminResponse res = new ListAdminResponse();
                res.setUserId(o.getUserid());
                res.setSysLevel(o.getSysLevel());
                adminResponseList.add(res);
            });
            return adminResponseList;
        } catch (EnterpriseException e) {
            LOGGER.error("corpId : {} get admin userId list failed, occur business error", corpId, e);
            throw new EnterpriseException(e.getErrorCode(), e.getErrorMessage());
        } catch (Exception e) {
            LOGGER.error("corpId : {} get admin userId list failed, occur system error", corpId, e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public UserInfoResponse getUserInfoByCode(String corpId, String code) {
        String accessToken = dingTalkAuthService.getAccessTokenAppKey(EnterprisePlatformEnum.DingTalk.name(), corpId);
        if(StringUtils.isEmpty(accessToken)) {
            LOGGER.info("corpId get appkey accessToken failed");
            throw new EnterpriseException(EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.code(), EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.describe());
        }
        try {
            // 通过钉钉开放平台免登码查询
            DingTalkClient client = new DefaultDingTalkClient(DingTalkApiUrlConstant.GET_USER_INFO_BY_CODE);
            OapiV2UserGetuserinfoRequest req = new OapiV2UserGetuserinfoRequest();
            req.setCode(code);
            OapiV2UserGetuserinfoResponse rsp = client.execute(req, accessToken);
            if(null == rsp.getResult()) {
                throw new EnterpriseException(EnterpriseErrorEnum.QUERY_USER_INFO_BY_CODE_FAILED.code(), EnterpriseErrorEnum.QUERY_USER_INFO_BY_CODE_FAILED.describe());
            }
            // 通过数据库查询用户信息
            // 逻辑：数据库中查询到用户类型，以数据库为主
            // 数据库中不存在，则看钉钉側返回值是不是admin，如果是则以钉钉为主
            // 不是管理员的话，默认最低权限
            EnterpriseUserRequest userRequest = new EnterpriseUserRequest();
            userRequest.setCorpId(corpId);
            userRequest.setUserId(rsp.getResult().getUserid());
            WaybillBridgeEnterpriseUserInfoDO userInfoDO = getUserInfoFromDB(userRequest);
            UserInfoResponse userInfoResponse = new UserInfoResponse();
            userInfoResponse.setSys(rsp.getResult().getSys());
            userInfoResponse.setUserId(rsp.getResult().getUserid());
            userInfoResponse.setSysLevel(rsp.getResult().getSysLevel());
            userInfoResponse.setUserName(rsp.getResult().getName());
            userInfoResponse.setSalt(code);

            // 查询数据库用户信息
            WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
            enterpriseUserInfoParam.createCriteria()
                    .andCorpIdEqualTo(corpId)
                    .andUserIdEqualTo(rsp.getResult().getUserid());
            WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectOneByParam(enterpriseUserInfoParam);
            String userType = EnterpriseUserTypeEnum.EMPLOYEE.name();
            if(null != waybillBridgeEnterpriseUserInfoDO){
                userType = waybillBridgeEnterpriseUserInfoDO.getUserType();
            }
            userInfoResponse.setUserType(userType);
            if(null != userInfoDO) {
                userInfoResponse.setUserType(userInfoDO.getUserType());
            }
            return userInfoResponse;

        } catch (EnterpriseException e) {
            LOGGER.error("corpId : {} get userInfo failed, occur business error", corpId, e);
            throw new EnterpriseException(e.getErrorCode(), e.getErrorMessage());
        } catch (Exception e) {
            LOGGER.error("corpId : {} get userInfo failed, occur system error", corpId, e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public UserTokenResponse fetchUserInfoAuthCodeFromMiniApp(String corpId, String authCode) {
        String userToken = dingTalkAuthService.getUserToken(authCode, corpId);
        if(StringUtils.isEmpty(userToken)) {
            LOGGER.error("corpId {} authCode {} get accessToken failed", corpId, authCode);
            throw new EnterpriseException(EnterpriseErrorEnum.QUERY_USERTOKEN_FAILED.code(), EnterpriseErrorEnum.QUERY_USERTOKEN_FAILED.describe());
        }
        GetUserHeaders getUserHeaders = new GetUserHeaders();
        getUserHeaders.xAcsDingtalkAccessToken = userToken;
        try {
            //如需获取当前授权人的信息，unionId参数可以传me。
            GetUserResponse rsp = dingTalkContractClient.getUserWithOptions("me", getUserHeaders, new RuntimeOptions());
            if(null == rsp || null == rsp.getBody()) {
                throw new EnterpriseException(EnterpriseErrorEnum.QUERY_USER_INFO_BY_AUTH_CODE_FAILED.code(), EnterpriseErrorEnum.QUERY_USER_INFO_BY_AUTH_CODE_FAILED.describe());
            }
            UserTokenResponse userTokenResponse = new UserTokenResponse();
            BeanUtils.copyProperties(rsp.getBody(), userTokenResponse);
            return userTokenResponse;

        } catch (EnterpriseException e) {
            LOGGER.error("corpId : {} get userInfo failed, occur business error", corpId, e);
            throw new EnterpriseException(e.getErrorCode(), e.getErrorMessage());
        } catch (Exception e) {
            LOGGER.error("corpId : {} get userInfo failed, occur system error", corpId, e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public void dingTalkAdminSync(String corpId) {
        // 钉钉系统管理员
        List<ListAdminResponse> adminUserIdList = this.getAdminUserIdList(corpId);
        LOGGER.info("corpId : {} get dingAdminUserIdList is {}", corpId, JSON.toJSONString(adminUserIdList));
        if (CollectionUtils.isEmpty(adminUserIdList)) {
            return;
        }

        // 本地管理员
        WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
        enterpriseUserInfoParam.createCriteria()
                .andCorpIdEqualTo(corpId)
                .andUserTypeEqualTo(EnterpriseUserTypeEnum.ADMIN.name());
        // 当前的管理员列表
        List<WaybillBridgeEnterpriseUserInfoDO> enterpriseUserInfoDOList = enterpriseUserInfoMapper.selectByParam(enterpriseUserInfoParam);

        List<String> dingAdminUserIdList = adminUserIdList.stream().map(ListAdminResponse::getUserId).collect(Collectors.toList());
        List<String> currentAdminUserIdList = enterpriseUserInfoDOList.stream().map(WaybillBridgeEnterpriseUserInfoDO::getUserId).collect(Collectors.toList());
        // 需要同步新增的管理员
        List<String> differenceAdd = dingAdminUserIdList.stream().filter(userId -> !currentAdminUserIdList.contains(userId)).collect(Collectors.toList());

        // 需要同步删除的管理员
        List<String> differenceReduce = currentAdminUserIdList.stream().filter(userId -> !dingAdminUserIdList.contains(userId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(differenceAdd) && CollectionUtils.isEmpty(differenceReduce)) {
            return;
        }

        try {
            for (String adminUserId : differenceAdd) {
                List<String> whiteUserIdList = EnterpriseSwitchHolder.CORP_ADMIN_USER_LIST.get(corpId);
                // 如果白名单不为空，那么就是用白名单配置的userId
                if(CollectionUtils.isNotEmpty(whiteUserIdList) && !whiteUserIdList.contains(adminUserId)) {
                    continue;
                }
                EnterpriseUserRequest userRequest = new EnterpriseUserRequest();
                userRequest.setUserId(adminUserId);

                // 获取用户信息
                UserInfoResponse userInfoByUserId = this.getUserInfoToDingTalkByUserId(corpId, adminUserId);
                if (userInfoByUserId == null) {
                    LOGGER.error("corpId: {} get userInfoByUserId is null for userId: {}", corpId, adminUserId);
                    continue;
                }
                userRequest.setUserId(userInfoByUserId.getUserId());
                userRequest.setUserName(userInfoByUserId.getUserName());
                userRequest.setCorpId(corpId);
                userRequest.setUserType(EnterpriseUserTypeEnum.ADMIN.name());
                userRequest.setFrom(EnterprisePlatformEnum.DingTalk.name());
                userRequest.setPhoneNum(userInfoByUserId.getPhoneNum());
                userRequest.setJobNumber(userInfoByUserId.getJobNumber());
                this.addUser(userRequest);
            }

            for (String adminUserId : differenceReduce){
                this.removeUserByUserId(adminUserId, corpId);
            }

        } catch (Exception e) {
            LOGGER.error("corpId: {} adminSyncCheck error", corpId, e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public void dingTalkAdminSync(String corpId, Integer start, Integer end) {
        // 钉钉系统管理员
        List<ListAdminResponse> adminUserIdList = this.getAdminUserIdList(corpId);
        LOGGER.info("corpId : {} get dingAdminUserIdList is {}", corpId, JSON.toJSONString(adminUserIdList));
        if (CollectionUtils.isEmpty(adminUserIdList)) {
            return;
        }
        // 钉钉接口返回的当前管理员
        List<String> dingAdminUserIdList = adminUserIdList.stream().map(ListAdminResponse::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dingAdminUserIdList)) {
            return;
        }

        if(null != start && null != end){
            dingAdminUserIdList = dingAdminUserIdList.subList(start, end);
        }

        for(String userId : dingAdminUserIdList) {
            // 从switch中查询当前企业所配置的管理员白名单
            List<String> whiteUserIdList = EnterpriseSwitchHolder.CORP_ADMIN_USER_LIST.get(corpId);
            if(CollectionUtils.isNotEmpty(whiteUserIdList) && !whiteUserIdList.contains(userId)) {
                return;
            }
            // 通过DB查询本地管理员
            WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
            enterpriseUserInfoParam.createCriteria()
                    .andCorpIdEqualTo(corpId)
                    .andUserIdEqualTo(userId);
            // 当前的管理员
            EnterpriseUserRequest userRequest = new EnterpriseUserRequest();
            userRequest.setUserId(userId);
            // 获取用户信息
            UserInfoResponse userInfoByUserId = this.getUserInfoToDingTalkByUserId(corpId, userId);
            if (userInfoByUserId == null) {
                LOGGER.error("corpId: {} get userInfoByUserId is null for userId: {}", corpId, userId);
                continue;
            }
            userRequest.setUserId(userInfoByUserId.getUserId());
            userRequest.setUserName(userInfoByUserId.getUserName());
            userRequest.setCorpId(corpId);
            userRequest.setUserType(EnterpriseUserTypeEnum.ADMIN.name());
            userRequest.setFrom(EnterprisePlatformEnum.DingTalk.name());
            userRequest.setPhoneNum(userInfoByUserId.getPhoneNum());
            userRequest.setJobNumber(userInfoByUserId.getJobNumber());
            this.addUser(userRequest);
        }

    }

    @Override
    public void dingTalkAdminSync(String corpId, String userId, Boolean isAdmin) {
        // 钉钉系统管理员
        List<ListAdminResponse> adminUserIdList = this.getAdminUserIdList(corpId);
        LOGGER.info("corpId : {} get dingAdminUserIdList is {}", corpId, JSON.toJSONString(adminUserIdList));
        if (CollectionUtils.isEmpty(adminUserIdList)) {
            return;
        }
        // 本地管理员
        WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
        enterpriseUserInfoParam.createCriteria()
                .andCorpIdEqualTo(corpId)
                .andUserIdEqualTo(userId)
                .andUserTypeEqualTo(EnterpriseUserTypeEnum.ADMIN.name());
        // 当前的管理员列表
        WaybillBridgeEnterpriseUserInfoDO enterpriseUserInfoDO = enterpriseUserInfoMapper.selectOneByParam(enterpriseUserInfoParam);
        if(null != enterpriseUserInfoDO && !isAdmin) {
            // 用户离职且用户存在-删除管理员类型
            enterpriseUserInfoDO.setUserType(EnterpriseUserTypeEnum.EMPLOYEE.name());
            enterpriseUserInfoDO.setGmtModified(new Date());
            enterpriseUserInfoMapper.updateByPrimaryKey(enterpriseUserInfoDO);

        }else if(null == enterpriseUserInfoDO && isAdmin){
            List<String> whiteUserIdList = EnterpriseSwitchHolder.CORP_ADMIN_USER_LIST.get(corpId);
            // 如果白名单不为空，那么就是用白名单配置的userId
            if(CollectionUtils.isNotEmpty(whiteUserIdList) && !whiteUserIdList.contains(userId)) {
                return;
            }
            EnterpriseUserRequest userRequest = new EnterpriseUserRequest();
            userRequest.setUserId(userId);

            // 获取用户信息
            UserInfoResponse userInfoByUserId = this.getUserInfoToDingTalkByUserId(corpId, userId);
            if (userInfoByUserId == null) {
                LOGGER.error("corpId: {} get userInfoByUserId is null for userId: {}", corpId, userId);
                return;
            }
            userRequest.setUserId(userInfoByUserId.getUserId());
            userRequest.setUserName(userInfoByUserId.getUserName());
            userRequest.setCorpId(corpId);
            userRequest.setUserType(EnterpriseUserTypeEnum.ADMIN.name());
            userRequest.setFrom(EnterprisePlatformEnum.DingTalk.name());
            userRequest.setPhoneNum(userInfoByUserId.getPhoneNum());
            userRequest.setJobNumber(userInfoByUserId.getJobNumber());
            this.addUser(userRequest);
        }else {
            LOGGER.error("corpId: {} userId: {} syncAdmin info error not found from DB", corpId, userId);
        }

    }

    @Override
    public BridgePagingDTO<WaybillEnterpriseUserInfoDTO> pageList(GetEnterpriseUserInfoRequest request) {

        try {
            WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
            enterpriseUserInfoParam.setPage(true);
            enterpriseUserInfoParam.setPagination(request.getCurrentPage(), request.getPageSize());
            enterpriseUserInfoParam.appendOrderByClause(WaybillBridgeEnterpriseUserInfoParam.OrderCondition.GMTMODIFIED, WaybillBridgeEnterpriseUserInfoParam.SortType.DESC);

            WaybillBridgeEnterpriseUserInfoParam.Criteria userInfoParamCriteria = enterpriseUserInfoParam.createCriteria();
            if (request.getSelectAdmin()) {
                // 同步钉钉管理员-这里不用同步
//                adminSyncCheck(request.getCorpId());
                userInfoParamCriteria.andUserTypeEqualTo(EnterpriseUserTypeEnum.ADMIN.name());
            }

            userInfoParamCriteria.andCorpIdEqualTo(request.getCorpId());
//            if (!request.getSelectAdmin()) {
//                // 同步钉钉员工
//                this.dingTalkUserSync(request.getCorpId());
//            }

            if (StringUtils.isNotEmpty(request.getUserType())) {
                userInfoParamCriteria.andUserTypeEqualTo(request.getUserType());
            }

            long totalCount = enterpriseUserInfoMapper.countByParam(enterpriseUserInfoParam);
            List<WaybillBridgeEnterpriseUserInfoDO> enterpriseUserInfoDOList = enterpriseUserInfoMapper.selectByParam(enterpriseUserInfoParam);
            return BridgePagingDTO.build(waybillBridgeEnterpriseUserInfoConverter.convertFromDOList(enterpriseUserInfoDOList),
                    totalCount, request.getCurrentPage(), request.getPageSize());
        } catch (Exception e) {
            LOGGER.error("corpId: {} pageList error", request.getCorpId(), e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public WaybillBridgeEnterpriseUserInfoDO getUserInfoFromDB(EnterpriseUserRequest request) {
        WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
        enterpriseUserInfoParam.createCriteria()
                .andCorpIdEqualTo(request.getCorpId())
                .andUserIdEqualTo(request.getUserId());
        return enterpriseUserInfoMapper.selectOneByParam(enterpriseUserInfoParam);
    }

    /**
     * 添加企业用户信息
     *
     * <p>该方法用于处理企业用户的新增或更新操作。首先将请求中的参数映射到对应的实体对象
     * WaybillBridgeEnterpriseUserInfoDO 中，并根据是否存在相同的用户记录决定执行更新或插入操作。
     * 如果请求中包含手机号，则会同步更新电话表中的数据；同时，在更新用户信息时，会根据权限规则
     * 判断是否允许修改用户类型。</p>
     *
     * @param request 包含企业用户信息的请求对象，其中包含 userId、userName、jobNumber、corpId、platform、remark、status、phoneNum、post_id、locationId 等字段
     * @return 返回受影响的行数，表示插入或更新操作的结果；若发生异常，则可能抛出异常
     * @throws EnterpriseException 当系统出现未知错误时抛出，具体错误码为 SYSTEM_ERROR
     */
    @Override
    public int addUser(EnterpriseUserRequest request) {
        WaybillBridgeEnterpriseUserInfoDO enterpriseUserInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        enterpriseUserInfoDO.setUserId(request.getUserId());
        enterpriseUserInfoDO.setUserName(request.getUserName());
        enterpriseUserInfoDO.setJobNumber(request.getJobNumber());
        enterpriseUserInfoDO.setCorpId(request.getCorpId());
        enterpriseUserInfoDO.setPlatform(request.getFrom());
        enterpriseUserInfoDO.setRemark(request.getRemake());
        enterpriseUserInfoDO.setStatus(EnterpriseUserStatusEnum.ENABLE.getStatus());
        enterpriseUserInfoDO.setPhoneNum(request.getPhoneNum());
        enterpriseUserInfoDO.setPostId(request.getPostId());
        enterpriseUserInfoDO.setLocationId(request.getLocationId());

        try {
            // 更新手机号通讯录
            if (request.getPhoneNum() != null) {
                // 同步电话表
                WaybillBridgeEnterpriseUserPhoneNumDO existUserPhoneNumDO = enterpriseUserPhoneNumMapper.selectOneByUserIdAndPhoneNum(request.getUserId(), request.getPhoneNum());
                if (existUserPhoneNumDO == null) { {
                    WaybillBridgeEnterpriseUserPhoneNumDO waybillBridgeEnterpriseUserPhoneNumDO = new WaybillBridgeEnterpriseUserPhoneNumDO();
                    waybillBridgeEnterpriseUserPhoneNumDO.setGmtCreate(new Date());
                    waybillBridgeEnterpriseUserPhoneNumDO.setGmtModified(new Date());
                    waybillBridgeEnterpriseUserPhoneNumDO.setUserId(request.getUserId());
                    waybillBridgeEnterpriseUserPhoneNumDO.setUserName(request.getUserName());
                    waybillBridgeEnterpriseUserPhoneNumDO.setPhoneNum(request.getPhoneNum());
                    waybillBridgeEnterpriseUserPhoneNumDO.setCorpId(request.getCorpId());
                    enterpriseUserPhoneNumMapper.insert(waybillBridgeEnterpriseUserPhoneNumDO);
                }}
            }

            // 存在则更新
            // 不存在则插入
            WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
            enterpriseUserInfoParam.createCriteria()
                    .andCorpIdEqualTo(request.getCorpId())
                    .andUserIdEqualTo(request.getUserId());
            WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectOneByParam(enterpriseUserInfoParam);
            if (waybillBridgeEnterpriseUserInfoDO != null) {
                enterpriseUserInfoDO.setId(waybillBridgeEnterpriseUserInfoDO.getId());
                enterpriseUserInfoDO.setGmtCreate(waybillBridgeEnterpriseUserInfoDO.getGmtCreate());
                enterpriseUserInfoDO.setGmtModified(new Date());
                Integer dbUserType = EnterpriseUserTypeEnum.getStatus(waybillBridgeEnterpriseUserInfoDO.getUserType());
                if (dbUserType != null) {
                    if (StringUtils.isNotEmpty(request.getUserType())) {
                        Integer requestUserType = EnterpriseUserTypeEnum.getStatus(request.getUserType());
                        // 权限递进,按下兼容
                        if (requestUserType != null && dbUserType > requestUserType) {
                            enterpriseUserInfoDO.setUserType(request.getUserType());
                        } else {
                            enterpriseUserInfoDO.setUserType(waybillBridgeEnterpriseUserInfoDO.getUserType());
                        }
                    }
                } else {
                    enterpriseUserInfoDO.setUserType(request.getUserType());
                }
                enterpriseUserInfoDO.setPostId(waybillBridgeEnterpriseUserInfoDO.getPostId());
                return enterpriseUserInfoMapper.updateByPrimaryKey(enterpriseUserInfoDO);
            } else {
                enterpriseUserInfoDO.setUserType(request.getUserType());
            }


            enterpriseUserInfoDO.setGmtCreate(new Date());
            enterpriseUserInfoDO.setGmtModified(new Date());
            return enterpriseUserInfoMapper.insert(enterpriseUserInfoDO);
        } catch (Exception e) {
            LOGGER.error("insert enterprise user info error, request:{}", JSON.toJSONString(request), e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public int deleteUserById(Long id) {
        WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectByPrimaryKey(id);
        if (waybillBridgeEnterpriseUserInfoDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.DEL_USER_ERROR.code(), "用户不存在");
        }
//        if (StringUtils.equals(EnterprisePlatformEnum.DingTalk.name(), waybillBridgeEnterpriseUserInfoDO.getPlatform())&&
//                StringUtils.equals(EnterpriseUserTypeEnum.ADMIN.name(), waybillBridgeEnterpriseUserInfoDO.getUserType())) {
//            throw new EnterpriseException(EnterpriseErrorEnum.DEL_USER_ERROR.code() ,"钉钉后台系统管理员不可删除");
//        }
        WaybillBridgeEnterpriseUserPhoneNumParam enterpriseUserPhoneNumParam = new WaybillBridgeEnterpriseUserPhoneNumParam();
        enterpriseUserPhoneNumParam.createCriteria().andUserIdEqualTo(waybillBridgeEnterpriseUserInfoDO.getUserId());
        List<WaybillBridgeEnterpriseUserPhoneNumDO> userPhoneNumDOList = enterpriseUserPhoneNumMapper.selectByParam(enterpriseUserPhoneNumParam);
        if (CollectionUtils.isNotEmpty(userPhoneNumDOList)) {
            userPhoneNumDOList.forEach(userPhoneNumDO -> {
                enterpriseUserPhoneNumMapper.deleteByPrimaryKey(userPhoneNumDO.getId());
            });
        }
        return enterpriseUserInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int removeAdminById(Long id) {
        WaybillBridgeEnterpriseUserInfoDO userInfoDO = enterpriseUserInfoMapper.selectByPrimaryKey(id);
        if (userInfoDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.DEL_USER_ERROR.code(), "用户不存在");
        }
        if (userInfoDO.getPostId() != null) {
            userInfoDO.setUserType(EnterpriseUserTypeEnum.POST_ADMIN.name());
        } else {
            userInfoDO.setUserType(EnterpriseUserTypeEnum.EMPLOYEE.name());
        }
        userInfoDO.setGmtModified(new Date());
        return enterpriseUserInfoMapper.updateByPrimaryKeySelective(userInfoDO);
    }

    @Override
    public WaybillEnterpriseUserInfoDTO getById(Long id) {
        WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectByPrimaryKey(id);
        return waybillBridgeEnterpriseUserInfoConverter.convertFromDO(waybillBridgeEnterpriseUserInfoDO);
    }

    @Override
    public UserInfoResponse getUserInfoToDingTalkByUserId(String corpId, String userId) {
        String accessToken = dingTalkAuthService.getAccessToken(EnterprisePlatformEnum.DingTalk.name(), corpId);
        if(StringUtils.isEmpty(accessToken)) {
            LOGGER.info("corpId get accessToken failed");
            throw new EnterpriseException(EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.code(), EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.describe());
        }
        try {
            return this.getDingTalkUserInfoByUserId(accessToken, userId);
        } catch (EnterpriseException e) {
            LOGGER.error("corpId : {} get userInfo by userID failed, occur business error", corpId, e);
            throw new EnterpriseException(e.getErrorCode(), e.getErrorMessage());
        } catch (Exception e) {
            LOGGER.error("corpId : {} get userInfo by userID failed, occur system error", corpId, e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public UserInfoResponse getUserInfoByUnionId(String corpId, String unionId) {
        String accessToken = dingTalkAuthService.getAccessToken(EnterprisePlatformEnum.DingTalk.name(), corpId);
        if(StringUtils.isEmpty(accessToken)) {
            LOGGER.info("corpId {} get accessToken failed", corpId);
            throw new EnterpriseException(EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.code(), EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.describe());
        }
        try {
            DingTalkClient client = new DefaultDingTalkClient(DingTalkApiUrlConstant.GET_USER_INFO_BY_UNION_ID);
            OapiUserGetbyunionidRequest req = new OapiUserGetbyunionidRequest();
            req.setUnionid(unionId);
            OapiUserGetbyunionidResponse rsp = client.execute(req, accessToken);
            if(rsp.isSuccess() && null != rsp.getResult() && StringUtils.isNotEmpty(rsp.getResult().getUserid())){
                String userId = rsp.getResult().getUserid();
                return this.getDingTalkUserInfoByUserId(accessToken, userId);
            }
        } catch (EnterpriseException e) {
            LOGGER.error("corpId : {} get userInfo by unionId failed, occur business error", corpId, e);
            throw new EnterpriseException(e.getErrorCode(), e.getErrorMessage());
        } catch (Exception e) {
            LOGGER.error("corpId : {} get userInfo by unionId failed, occur system error", corpId, e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
        return null;
    }

    @Override
    public BridgePagingDTO<WaybillEnterpriseUserInfoDTO> getUserBySearchValue(EnterpriseUserRequest request) {
        WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
        enterpriseUserInfoParam.setPage(true);
        enterpriseUserInfoParam.setPagination(request.getCurrentPage(), request.getPageSize());
        enterpriseUserInfoParam.appendOrderByClause(WaybillBridgeEnterpriseUserInfoParam.OrderCondition.ID, WaybillBridgeEnterpriseUserInfoParam.SortType.DESC);

        String searchValue = request.getSearchValue();
        if (PhoneNumberValidator.isValidNumber(searchValue)) {
            if(!PhoneNumberValidator.isFourDigitNumber(searchValue)){
                return BridgePagingDTO.build(Lists.newArrayList(),0, request.getCurrentPage(),
                        request.getPageSize());
            }
            WaybillBridgeEnterpriseUserPhoneNumParam enterpriseUserPhoneNumParam = new WaybillBridgeEnterpriseUserPhoneNumParam();
            WaybillBridgeEnterpriseUserPhoneNumParam.Criteria criteria = enterpriseUserPhoneNumParam.createCriteria();
            criteria.andPhoneNumLike("%" + searchValue);
            criteria.andCorpIdEqualTo(request.getCorpId());
            List<WaybillBridgeEnterpriseUserPhoneNumDO> enterpriseUserPhoneNumDOS = enterpriseUserPhoneNumMapper.selectByParam(enterpriseUserPhoneNumParam);
            List<String> collect = ListUtil.non(enterpriseUserPhoneNumDOS).stream().map(WaybillBridgeEnterpriseUserPhoneNumDO::getUserId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                return BridgePagingDTO.build(Lists.newArrayList(),0, request.getCurrentPage(),
                        request.getPageSize());
            }
            enterpriseUserInfoParam.createCriteria()
                    .andUserIdIn(collect)
                    .andCorpIdEqualTo(request.getCorpId());
            long total = enterpriseUserInfoMapper.countByParam(enterpriseUserInfoParam);
            List<WaybillBridgeEnterpriseUserInfoDO> waybillBridgeEnterpriseUserInfoDOS = enterpriseUserInfoMapper.selectByParam(enterpriseUserInfoParam);
            return BridgePagingDTO.build(waybillBridgeEnterpriseUserInfoConverter.convertFromDOList(waybillBridgeEnterpriseUserInfoDOS),total,
                    request.getCurrentPage(), request.getPageSize());
        }
        enterpriseUserInfoParam.createCriteria()
                .andUserNameLike("%" + searchValue + "%")
                .andCorpIdEqualTo(request.getCorpId());;
        long total = enterpriseUserInfoMapper.countByParam(enterpriseUserInfoParam);
        List<WaybillBridgeEnterpriseUserInfoDO> enterpriseUserInfoDOList = enterpriseUserInfoMapper.selectByParam(enterpriseUserInfoParam);
        return BridgePagingDTO.build(waybillBridgeEnterpriseUserInfoConverter.convertFromDOList(enterpriseUserInfoDOList), total,
                request.getCurrentPage(), request.getPageSize());
    }

    @Override
    public BridgePagingDTO<UserInfoResponse> getDingTalkUserInfo(GetEnterpriseUserInfoRequest request) {
        if (StringUtils.isEmpty(request.getCorpId()) || StringUtils.isEmpty(request.getUserName())) {
            return BridgePagingDTO.build(Lists.newArrayList(), 0, request.getCurrentPage(), request.getPageSize());
        }
        String accessToken = dingTalkAuthService.getAccessToken(EnterprisePlatformEnum.DingTalk.name(), request.getCorpId());
        if(StringUtils.isEmpty(accessToken)) {
            LOGGER.info("corpId get accessToken failed");
            throw new EnterpriseException(EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.code(), EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.describe());
        }
        com.aliyun.dingtalkcontact_1_0.models.SearchUserHeaders searchUserHeaders = new com.aliyun.dingtalkcontact_1_0.models.SearchUserHeaders();
        searchUserHeaders.xAcsDingtalkAccessToken = accessToken;
        com.aliyun.dingtalkcontact_1_0.models.SearchUserRequest searchUserRequest = new com.aliyun.dingtalkcontact_1_0.models.SearchUserRequest()
                .setQueryWord(request.getUserName())
                .setOffset(request.getCurrentPage())
                .setSize(request.getPageSize());

        try {
            List<UserInfoResponse> userInfoList = Lists.newArrayList();
            SearchUserResponse searchUserResponse = dingTalkContractClient.searchUserWithOptions(searchUserRequest, searchUserHeaders, new RuntimeOptions());
            LOGGER.info("searchUserResponse:{}", JSON.toJSONString(searchUserResponse));
            List<String> userIdList = searchUserResponse.getBody().getList();
            userIdList.forEach(userId -> {
                UserInfoResponse userInfoByUserId = this.getUserInfoToDingTalkByUserId(request.getCorpId(), userId);
                if(null != userInfoByUserId){
                    userInfoList.add(userInfoByUserId);
                }
            });
            return BridgePagingDTO.build(userInfoList, searchUserResponse.getBody().getTotalCount(), request.getCurrentPage(), request.getPageSize());
        } catch (Exception e) {
            LOGGER.error("getDingTalkUserInfo failed, error", e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public List<CorpDeptResponse> getDingTalkCorpDept(String accessToken, String corpId, Long deptId) {
        DingTalkClient client = new DefaultDingTalkClient(DingTalkApiUrlConstant.GET_CORP_DEPARTMENT);
        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
        // 默认从根部门开始获取所有部门
        req.setDeptId(deptId);
        req.setLanguage("zh_CN");

        try {
            OapiV2DepartmentListsubResponse rsp = client.execute(req, accessToken);
            LOGGER.info("getCorpDept rsp:{}", JSON.toJSONString(rsp));
            List<OapiV2DepartmentListsubResponse.DeptBaseResponse> result = rsp.getResult();
            if (CollectionUtils.isEmpty(result)) {
                return Lists.newArrayList();
            }
            List<CorpDeptResponse> corpDeptResponseList = Lists.newArrayList();
            result.forEach(deptBaseResponse -> {
                CorpDeptResponse corpDeptResponse = new CorpDeptResponse();
                BeanUtils.copyProperties(deptBaseResponse, corpDeptResponse);
                corpDeptResponseList.add(corpDeptResponse);
            });
            return corpDeptResponseList;
        } catch (Exception e) {
            LOGGER.error("getDingTalkUserInfo failed, error: {}", ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public List<List<UserDeptInfoResponse>> getDingTalkDeptListByUserId(String corpId, String userId) {
        String accessToken = dingTalkAuthService.getAccessToken(EnterprisePlatformEnum.DingTalk.name(), corpId);
        if(StringUtils.isEmpty(accessToken)) {
            LOGGER.info("corpId get accessToken failed");
            throw new EnterpriseException(EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.code(), EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.describe());
        }
        DingTalkClient client = new DefaultDingTalkClient(DingTalkApiUrlConstant.GET_USER_ALL_DEPARTMENT);
        OapiV2DepartmentListparentbyuserRequest req = new OapiV2DepartmentListparentbyuserRequest();
        req.setUserid(userId);
        try {
            OapiV2DepartmentListparentbyuserResponse rsp = client.execute(req, accessToken);
            LOGGER.info("getDingTalkDeptListByUserId rsp:{}", JSON.toJSONString(rsp));
            if (!rsp.isSuccess() || null == rsp.getResult()) {
                return Lists.newArrayList();
            }
            OapiV2DepartmentListparentbyuserResponse.DeptListParentByUserResponse result = rsp.getResult();
            List<List<UserDeptInfoResponse>> responseList = new ArrayList<>();
            for(int i = 0; i < result.getParentList().size(); i++) {
                List<UserDeptInfoResponse> deptDetailRspList = new ArrayList<>();
                for (int j = 0; j < result.getParentList().get(i).getParentDeptIdList().size(); j++) {
                    UserDeptInfoResponse deptDetailRsp = this.getUserDeptInfoById(corpId, result.getParentList().get(i).getParentDeptIdList().get(j));
                    if(null != deptDetailRsp){
                        deptDetailRspList.add(deptDetailRsp);
                    }
                }
                if(CollectionUtils.isNotEmpty(deptDetailRspList)){
                    responseList.add(deptDetailRspList);
                }
            }
            return responseList;
        } catch (Exception e) {
            LOGGER.error("getDingTalkDeptListByUserId failed, error: {}", ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public List<String> getDingTalkDeptUserId(String accessToken, Long deptId) {
        DingTalkClient client = new DefaultDingTalkClient(DingTalkApiUrlConstant.GET_DEPARTMENT_USER);
        OapiUserListidRequest req = new OapiUserListidRequest();
        req.setDeptId(deptId);

        try {
            OapiUserListidResponse rsp = client.execute(req, accessToken);
            LOGGER.info("getDingTalkDeptUserInfo rsp:{}", JSON.toJSONString(rsp));
            if (!rsp.isSuccess() || null == rsp.getResult()) {
                return Lists.newArrayList();
            }
            OapiUserListidResponse.ListUserByDeptResponse result = rsp.getResult();
            return result.getUseridList();
        } catch (Exception e) {
            LOGGER.error("getDingTalkUserInfo failed, error: {}", ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @Override
    public void dingTalkUserSync(String corpId, Integer start, Integer end) {
        String accessToken = dingTalkAuthService.getAccessToken(EnterprisePlatformEnum.DingTalk.name(), corpId);
        if(StringUtils.isEmpty(accessToken)) {
            LOGGER.info("corpId get accessToken failed");
            throw new EnterpriseException(EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.code(), EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.describe());
        }
        // 默认获取根部门下一层级的部门
        List<CorpDeptResponse> dingTalkCorpDept = this.getDingTalkCorpDept(accessToken, corpId, 1L);
        // 用户可能没有部门,默认获取根部门
        List<Long> deptIdList = Lists.newArrayList(1L);
        if (CollectionUtils.isNotEmpty(dingTalkCorpDept)) {
            deptIdList.addAll(dingTalkCorpDept.stream().map(CorpDeptResponse::getDeptId).collect(Collectors.toList()));
        }
        LOGGER.info("deptIdList:{}", JSON.toJSONString(deptIdList));
        // 遍历企业部门,获取所有部门下的用户
        List<String> dingTalkUserIdList = new ArrayList<>();
        deptIdList.forEach(deptId -> {
            List<String> dingTalkDeptUserId = this.getDingTalkDeptUserId(accessToken, deptId);
            dingTalkUserIdList.addAll(dingTalkDeptUserId);
        });
        List<String> dingTalkUserIdSubList = dingTalkUserIdList;
        if(null != start && null != end){
            dingTalkUserIdSubList = dingTalkUserIdList.subList(start, end);
        }
        if(CollectionUtils.isNotEmpty(dingTalkUserIdSubList)){
            dingTalkUserIdList.forEach(userId -> {
                //  查询本地DB员工
                WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
                enterpriseUserInfoParam.createCriteria()
                        .andCorpIdEqualTo(corpId)
                        .andUserIdEqualTo(userId);
                // 当前的管理员列表
                UserInfoResponse userInfoResponse = this.getDingTalkUserInfoByUserId(accessToken, userId);
                if(null != userInfoResponse){
                    this.syncDingTalkUserToDB(userInfoResponse, corpId);
                }
            });
        }

    }



    @Override
    public void dingTalkUserSync(String corpId, String userId, Boolean isActive) {
        String accessToken = dingTalkAuthService.getAccessToken(EnterprisePlatformEnum.DingTalk.name(), corpId);
        if(StringUtils.isEmpty(accessToken)) {
            LOGGER.info("corpId get accessToken failed");
            throw new EnterpriseException(EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.code(), EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.describe());
        }

        // 本地员工
        WaybillBridgeEnterpriseUserInfoParam enterpriseUserInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
        enterpriseUserInfoParam.createCriteria()
                .andCorpIdEqualTo(corpId)
                .andUserIdEqualTo(userId);
        // 当前的管理员列表
        WaybillBridgeEnterpriseUserInfoDO enterpriseUserInfoDO = enterpriseUserInfoMapper.selectOneByParam(enterpriseUserInfoParam);
        // 如果离职，并且人员存在
        if (!isActive && null != enterpriseUserInfoDO) {
            enterpriseUserInfoMapper.deleteByPrimaryKey(enterpriseUserInfoDO.getId());
        }else {
            // 如果在职，人员不存在则插入新员工数据
            UserInfoResponse userInfoResponse = this.getDingTalkUserInfoByUserId(accessToken, userId);
            if(null != userInfoResponse){
                this.syncDingTalkUserToDB(userInfoResponse, corpId);
            }
        }
    }

    @Override
    public UserDeptInfoResponse getUserDeptInfoById(String corpId, Long deptId) {
        String accessToken = dingTalkAuthService.getAccessToken(EnterprisePlatformEnum.DingTalk.name(), corpId);
        if(StringUtils.isEmpty(accessToken)) {
            LOGGER.info("corpId {} get accessToken failed", corpId);
            throw new EnterpriseException(EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.code(), EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.describe());
        }
        try {
            DingTalkClient client = new DefaultDingTalkClient(DingTalkApiUrlConstant.GET_USER_DEPT_BY_DEPT_ID);
            OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
            req.setDeptId(deptId);
            req.setLanguage("zh_CN");
            OapiV2DepartmentGetResponse rsp = client.execute(req, accessToken);
            LOGGER.info("getUserDeptInfoById deptId:{} rsp:{}", deptId, JSON.toJSONString(rsp));
            if(rsp.isSuccess() && null != rsp.getResult()){
                UserDeptInfoResponse userDeptInfoResponse = new UserDeptInfoResponse();
                userDeptInfoResponse.setDeptId(rsp.getResult().getDeptId());
                userDeptInfoResponse.setDeptName(rsp.getResult().getName());
                userDeptInfoResponse.setParentDeptId(rsp.getResult().getParentId());
                userDeptInfoResponse.setDeptManagerUseridList(rsp.getResult().getDeptManagerUseridList());
                return userDeptInfoResponse;
            }
        } catch (EnterpriseException e) {
            LOGGER.error("corpId : {} get userDeptIdById by unionId failed, occur business error", corpId, e);
            throw new EnterpriseException(e.getErrorCode(), e.getErrorMessage());
        } catch (Exception e) {
            LOGGER.error("corpId : {} get userDeptIdById by unionId failed, occur system error", corpId, e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
        return null;
    }

    @Override
    public WaybillEnterpriseUserInfoDTO getByUserId(String userId, String corpId) {
        WaybillBridgeEnterpriseUserInfoParam param = new WaybillBridgeEnterpriseUserInfoParam();
        param.createCriteria()
                .andUserIdEqualTo(userId)
                .andCorpIdEqualTo(corpId);
        WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectOneByParam(param);
        return waybillBridgeEnterpriseUserInfoConverter.convertFromDO(waybillBridgeEnterpriseUserInfoDO);
    }

    @Override
    public void removeUserByUserId(String userId, String corpId) {
        WaybillBridgeEnterpriseUserInfoParam param = new WaybillBridgeEnterpriseUserInfoParam();
        param.createCriteria().andUserIdEqualTo(userId)
                .andCorpIdEqualTo(corpId);
        WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectOneByParam(param);
        if(null == waybillBridgeEnterpriseUserInfoDO){
            LOGGER.info("userId: {} not found", userId);
            return;
        }
        // 本地管理员不参与同步
        if (EnterprisePlatformEnum.CaiNiao.name().equals(waybillBridgeEnterpriseUserInfoDO.getPlatform())) {
            return;
        }
        enterpriseUserInfoMapper.deleteByPrimaryKey(waybillBridgeEnterpriseUserInfoDO.getId());
    }

    @Override
    public int removeAdminByUserId(String userId) {
        LOGGER.info("removeAdminByUserId userId: {} ", userId);
        User user = UserContext.getUser();
        if (Objects.equals(user.getUserId(), userId)) {
            throw new EnterpriseException(EnterpriseErrorEnum.DEL_USER_ERROR.code(), "不支持账户自删除操作");
        }
        WaybillBridgeEnterpriseUserInfoParam param = new WaybillBridgeEnterpriseUserInfoParam();
        param.createCriteria().andUserIdEqualTo(userId)
                .andCorpIdEqualTo(user.getCorpId());
        WaybillBridgeEnterpriseUserInfoDO userInfoDO = enterpriseUserInfoMapper.selectOneByParam(param);
        if (userInfoDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.USER_NOT_EXIST.code(), EnterpriseErrorEnum.USER_NOT_EXIST.describe());
        }
        if (userInfoDO.getPostId() != null) {
            userInfoDO.setUserType(EnterpriseUserTypeEnum.POST_ADMIN.name());
        } else {
            userInfoDO.setUserType(EnterpriseUserTypeEnum.EMPLOYEE.name());
        }
        userInfoDO.setGmtModified(new Date());
        return enterpriseUserInfoMapper.updateByPrimaryKeySelective(userInfoDO);
    }


    /**
     * 根据userId获取用户信息
     * @param accessToken accessToken
     * @param userId userId
     * @return 用户信息
     */
    private UserInfoResponse getDingTalkUserInfoByUserId(String accessToken, String userId) {
        DingTalkClient client = new DefaultDingTalkClient(DingTalkApiUrlConstant.GET_USER_INFO_BY_USERID);
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userId);
        req.setLanguage("zh_CN");

        try {
            OapiV2UserGetResponse rsp = client.execute(req, accessToken);
            if(null == rsp.getResult()) {
                // 当查询不出不报错，直接返回 null
                LOGGER.error("userId : {} get userInfo by userID failed, occur business error", userId);
                return null;
//                throw new EnterpriseException(EnterpriseErrorEnum.QUERY_USER_INFO_BY_USERID_FAILED.code(), EnterpriseErrorEnum.QUERY_USER_INFO_BY_USERID_FAILED.describe());
            }
            LOGGER.info("getDingTalkUserInfoByUserId userid:{}, rsp:{}", userId, JSON.toJSONString(rsp));
            UserInfoResponse userInfoResponse = new UserInfoResponse();
            userInfoResponse.setUserId(rsp.getResult().getUserid());
            userInfoResponse.setUserName(rsp.getResult().getName());
            userInfoResponse.setPhoneNum(rsp.getResult().getMobile());
            userInfoResponse.setEmail(rsp.getResult().getEmail());
            userInfoResponse.setJobNumber(rsp.getResult().getJobNumber());
            userInfoResponse.setDeptIdList(rsp.getResult().getDeptIdList());
            return userInfoResponse;
        } catch (EnterpriseException e) {
            LOGGER.error("userId : {} get userInfo by userID failed, business error: {}", userId, ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(e.getErrorCode(), e.getErrorMessage());
        } catch (Exception e) {
            LOGGER.error("userId : {} get userInfo by userID failed, system error: {}", userId, ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    /**
     * 同步钉钉用户到DB
     * @param userInfoResponse 从钉钉获取的用户信息
     * @param corpId 用户所属企业
     */
    private void syncDingTalkUserToDB(UserInfoResponse userInfoResponse, String corpId) {
        EnterpriseUserRequest enterpriseUserRequest = new EnterpriseUserRequest();
        enterpriseUserRequest.setUserId(userInfoResponse.getUserId());
        enterpriseUserRequest.setUserName(userInfoResponse.getUserName());
        enterpriseUserRequest.setCorpId(corpId);
        enterpriseUserRequest.setUserType(EnterpriseUserTypeEnum.EMPLOYEE.name());
        enterpriseUserRequest.setFrom(EnterprisePlatformEnum.DingTalk.name());
        enterpriseUserRequest.setPhoneNum(userInfoResponse.getPhoneNum());
        enterpriseUserRequest.setJobNumber(userInfoResponse.getJobNumber());
        this.addUser(enterpriseUserRequest);
    }
}
