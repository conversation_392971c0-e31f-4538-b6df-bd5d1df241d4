package com.cainiao.waybill.bridge.biz.label.manager.impl;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.biz.label.manager.TpnMsgManager;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants.TpnMsg;
import com.cainiao.waybill.bridge.common.constants.TpnConstants;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.util.Exceptions;
import com.cainiao.waybill.bridge.common.label.dto.TpnMsgDTO;
import com.cainiao.waybill.bridge.common.label.dto.request.TpnMsgRequest;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.taobao.notify.remotingclient.SendResult;
import com.taobao.tpn.api.AuthorizeService;
import com.taobao.tpn.api.domain.AuthorizeInfo;
import com.taobao.tpn.client.domain.NotifyMessage;
import com.taobao.tpn.client.domain.TpnSendResult;
import com.taobao.tpn.client.domain.TpnTemplateMessage;
import com.taobao.tpn.client.notify.service.SendNotifyMessage;
import com.taobao.tpn.client.template.SendTpnMessage;
import com.taobao.tpn.domain.ResultSet;
import org.springframework.stereotype.Service;

/**
 * 千牛消息 manager 实现
 *
 * <AUTHOR>
 * @since 2017/04/21
 */
@Service
public class TpnMsgManagerImpl implements TpnMsgManager {
    @Resource
    private SendNotifyMessage sendNotifyMessage;
    @Resource
    private AuthorizeService authorizeService;
    @Resource
    private SendTpnMessage sendTpnMessage;

    @Override
    public String sendTpnMsg(TpnMsgDTO tpnMsgDTO) throws BridgeBaseException {
        NotifyMessage notifyMessage = new NotifyMessage();
        notifyMessage.setSellerUserId(tpnMsgDTO.getSellerId());
        notifyMessage.setTitle(tpnMsgDTO.getTitle());
        notifyMessage.setBroadcast(true);
        notifyMessage.setCustomerViewData(Sets.newHashSet(tpnMsgDTO.getContent()));
        notifyMessage.setModified(System.currentTimeMillis());
        notifyMessage.setTopic(TpnMsg.TOPIC);
        notifyMessage.setMsgStatus(tpnMsgDTO.getSecondMsgType());
        try {
            SendResult sendResult = sendNotifyMessage.sendMessage(notifyMessage);
            if (sendResult.isSuccess()) {
                return sendResult.getMessageId();
            } else {
                throw new BridgeBaseException(TpnMsg.TPN_MSG_SEND_ERROR, sendResult.getErrorMessage());
            }
        } catch (BridgeBaseException e) {
            throw e;
        } catch (Exception e) {
            throw Exceptions.newBridgeBaseException(TpnMsg.TPN_MSG_SEND_ERROR, TpnMsg.TPN_MSG_SEND_ERROR, e);
        }
    }

    @Override
    public boolean sendTpnMsg(TpnMsgRequest request) throws BridgeBaseException {
        try {
            TpnTemplateMessage message = new TpnTemplateMessage();
            message.setTemplateId(request.getTemplateType().getTemplateId());
            // 模板分配的key
            message.setTemplateKey(request.getTemplateType().getTemplateKey());
            // 主账号userId
            message.setUserId(request.getSellerId());
            // 业务方业务id， 可拿该id排查发送情况， 如果没有可用时间戳
            message.setBizId(request.getBizId());
            // 标题参数
            message.setTitleParam(request.getTitleParams());
            // 内容参数
            message.setContentParam(request.getContentParams());

            TpnSendResult sendResult = sendTpnMessage.sendMessage(message);

            if (sendResult.isSuccess()) {
                return true;
            } else {
                throw new BridgeBaseException(TpnMsg.TPN_MSG_SEND_ERROR, sendResult.getErrorMsg());
            }
        } catch (BridgeBaseException e) {
            throw e;
        } catch (Throwable e) {
            throw Exceptions.newBridgeBaseException(TpnMsg.TPN_MSG_SEND_ERROR, TpnMsg.TPN_MSG_SEND_ERROR, e);
        }
    }

    @Override
    public void authorizeTpn(Long sellerId) throws BridgeBaseException {

        if (sellerId == null) {
            throw new BridgeBaseException("SellerId can not be null", "SellerId can not be null");
        }

        try {
            AuthorizeInfo authorizeInfo = new AuthorizeInfo();
            authorizeInfo.setUserId(sellerId);
            authorizeInfo.setSubUserId(0L);

            JSONObject jo = new JSONObject();
            jo.put(TpnMsg.TOPIC, Lists.newArrayList(TpnConstants.SELLER_BIND_MSG_TYPE));

            authorizeInfo.setOpenSubInfo(jo.toJSONString());

            ResultSet result = authorizeService.authorizePermitV2(authorizeInfo);

            if (result.isError()) {
                throw new BridgeBaseException(result.getErrorCode(), result.getErrorMsg());
            }
        } catch (Throwable e) {
            throw new BridgeBaseException("TpnService authorizeTpn error", "TpnService authorizeTpn error", e);
        }
    }

}
