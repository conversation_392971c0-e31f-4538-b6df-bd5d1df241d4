package com.cainiao.waybill.bridge.enterprise.route.convert;

import com.cainiao.waybill.bridge.enterprise.common.EnterpriseSwitchHolder;
import com.cainiao.waybill.bridge.enterprise.common.constant.EnterpriseOrderFeatureConstant;
import com.cainiao.waybill.bridge.enterprise.common.enums.ExpressTypeEnum;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePackInboundDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePostDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoDO;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeEnterprisePackBoundDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePostMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.cainiao.waybill.common.util.FeatureUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@Component
public class WaybillBridgeEnterprisePackInboundConverter {

    @Autowired
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;

    @Autowired
    private WaybillBridgeEnterprisePostMapper enterprisePostMapper;

    /**
     * DTO模型转换成DO模型
     * @param waybillBridgeEnterprisePackBoundDTO DTO模型
     */
    public WaybillBridgeEnterprisePackInboundDO convertFromDTO(WaybillBridgeEnterprisePackBoundDTO waybillBridgeEnterprisePackBoundDTO) {
        WaybillBridgeEnterprisePackInboundDO waybillBridgeEnterprisePackInboundDO = new WaybillBridgeEnterprisePackInboundDO();
        BeanUtils.copyProperties(waybillBridgeEnterprisePackBoundDTO,waybillBridgeEnterprisePackInboundDO);
        return waybillBridgeEnterprisePackInboundDO;
    }

    /**
     * DO模型转换成DTO模型
     * @param waybillBridgeEnterprisePackInboundDO DO模型
     */
    public WaybillBridgeEnterprisePackBoundDTO convertFromDO(WaybillBridgeEnterprisePackInboundDO waybillBridgeEnterprisePackInboundDO) {
        WaybillBridgeEnterprisePackBoundDTO waybillBridgeEnterprisePackBoundDTO = new WaybillBridgeEnterprisePackBoundDTO();
        BeanUtils.copyProperties(waybillBridgeEnterprisePackInboundDO,waybillBridgeEnterprisePackBoundDTO);
        WaybillBridgeEnterpriseUserInfoDO pickUpUserInfo = enterpriseUserInfoMapper.selectByUserId(waybillBridgeEnterprisePackBoundDTO.getPickUpUserId(), waybillBridgeEnterprisePackInboundDO.getCorpId());
        if (pickUpUserInfo != null) {
            waybillBridgeEnterprisePackBoundDTO.setPickUpUserJobNumber(pickUpUserInfo.getJobNumber());
        }
        WaybillBridgeEnterpriseUserInfoDO operatorUserInfo = enterpriseUserInfoMapper.selectByUserId(waybillBridgeEnterprisePackBoundDTO.getOperatorUserId(), waybillBridgeEnterprisePackInboundDO.getCorpId());
        if (operatorUserInfo != null) {
            waybillBridgeEnterprisePackBoundDTO.setOperatorJobNumber(operatorUserInfo.getJobNumber());
        }
        if (StringUtils.isNotEmpty(waybillBridgeEnterprisePackInboundDO.getCpCode())) {
            waybillBridgeEnterprisePackBoundDTO.setCpName(ExpressTypeEnum.getByExpressCode(waybillBridgeEnterprisePackInboundDO.getCpCode()));
        }
        WaybillBridgeEnterprisePostDO waybillBridgeEnterprisePostDO = enterprisePostMapper.selectByPrimaryKey(waybillBridgeEnterprisePackInboundDO.getPostId());
        if (waybillBridgeEnterprisePostDO != null) {
            waybillBridgeEnterprisePackBoundDTO.setPostId(waybillBridgeEnterprisePostDO.getPostId());
            waybillBridgeEnterprisePackBoundDTO.setPostName(waybillBridgeEnterprisePostDO.getPostName());
        }
        return waybillBridgeEnterprisePackBoundDTO;
    }

    /**
     * DO模型转换成DTO模型
     * @param waybillBridgeEnterprisePackInboundDOList DO模型
     */
    public List<WaybillBridgeEnterprisePackBoundDTO> convertFromDOList(List<WaybillBridgeEnterprisePackInboundDO> waybillBridgeEnterprisePackInboundDOList) {
        List<WaybillBridgeEnterprisePackBoundDTO> waybillEnterprisePackInboundDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(waybillBridgeEnterprisePackInboundDOList)) {
            return waybillEnterprisePackInboundDTOList;
        }
        waybillBridgeEnterprisePackInboundDOList.forEach(waybillBridgeEnterprisePackInboundDO -> {
            WaybillBridgeEnterprisePackBoundDTO waybillBridgeEnterprisePackBoundDTO = new WaybillBridgeEnterprisePackBoundDTO();
            BeanUtils.copyProperties(waybillBridgeEnterprisePackInboundDO,waybillBridgeEnterprisePackBoundDTO);
            WaybillBridgeEnterpriseUserInfoDO pickUpUserInfo = enterpriseUserInfoMapper.selectByUserId(waybillBridgeEnterprisePackBoundDTO.getPickUpUserId(), waybillBridgeEnterprisePackInboundDO.getCorpId());
            if (pickUpUserInfo != null) {
                waybillBridgeEnterprisePackBoundDTO.setPickUpUserJobNumber(pickUpUserInfo.getJobNumber());
                waybillBridgeEnterprisePackBoundDTO.setPickUpUserName(pickUpUserInfo.getUserName());
            }
            WaybillBridgeEnterpriseUserInfoDO operatorUserInfo = enterpriseUserInfoMapper.selectByUserId(waybillBridgeEnterprisePackBoundDTO.getOperatorUserId(), waybillBridgeEnterprisePackInboundDO.getCorpId());
            if (operatorUserInfo != null) {
                waybillBridgeEnterprisePackBoundDTO.setOperatorUserName(operatorUserInfo.getUserName());
                waybillBridgeEnterprisePackBoundDTO.setOperatorJobNumber(operatorUserInfo.getJobNumber());
            }
            if (StringUtils.isNotEmpty(waybillBridgeEnterprisePackInboundDO.getCpCode())) {
                waybillBridgeEnterprisePackBoundDTO.setCpName(EnterpriseSwitchHolder.CP_CODE_MAPPING.get(waybillBridgeEnterprisePackInboundDO.getCpCode()));
            }
            WaybillBridgeEnterprisePostDO waybillBridgeEnterprisePostDO = enterprisePostMapper.selectByPrimaryKey(waybillBridgeEnterprisePackInboundDO.getPostId());
            if (waybillBridgeEnterprisePostDO != null) {
                waybillBridgeEnterprisePackBoundDTO.setPostId(waybillBridgeEnterprisePostDO.getPostId());
                waybillBridgeEnterprisePackBoundDTO.setPostName(waybillBridgeEnterprisePostDO.getPostName());
            }
            if (StringUtils.isNotBlank(waybillBridgeEnterprisePackInboundDO.getFeature())) {
                Map<String, String> map = FeatureUtils.parseFromString(waybillBridgeEnterprisePackInboundDO.getFeature());
                String delivery = map.get(EnterpriseOrderFeatureConstant.DELIVERY);
                if (StringUtils.isNotEmpty(delivery)) {
                    waybillBridgeEnterprisePackBoundDTO.setDelivery(Integer.valueOf(delivery));
                }
            }
            waybillEnterprisePackInboundDTOList.add(waybillBridgeEnterprisePackBoundDTO);
        });
        return waybillEnterprisePackInboundDTOList;
    }
}