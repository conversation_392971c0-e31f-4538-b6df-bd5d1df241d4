package com.cainiao.waybill.bridge.web.charity.controller;

import java.util.List;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.biz.charity.constant.CharityConstant;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CharityOrgTypeEnum;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CharityRoleEnum;
import com.cainiao.waybill.bridge.biz.charity.request.CharityGiveOutQueryRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityGiveOutUpdateRequest;
import com.cainiao.waybill.bridge.biz.charity.response.*;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityGiveOutService;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityOrgService;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.model.charity.DTO.WaybillCharityOrgDTO;
import com.cainiao.waybill.bridge.web.charity.CharityBaseResult;
import com.cainiao.waybill.bridge.web.charity.CharityExcelUtil;
import com.cainiao.waybill.bridge.web.charity.CharityLoginUserUtil;
import io.swagger.annotations.Api;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * v2.0 新增包裹发放界面 提供查询、导出等功能
 * <AUTHOR>
 */
@Api(tags = "包裹发放界面管理")
@RestController
@RequestMapping(value = "/charity/giveOut/")
public class WaybillCharityGiveOutController extends BaseController{

    @Autowired
    private WaybillCharityGiveOutService waybillCharityGiveOutService;

    @Autowired
    private WaybillCharityOrgService charityOrgService;


    /**
     * 包裹发放列表查询
     * 支持多条件
     */
    @ResponseBody
    @RequestMapping("/list")
    public CharityBaseResult<CharityPagingResponse<CharityGiveOutQueryResponse>> list(@RequestBody CharityGiveOutQueryRequest giveOutQueryRequest){
        BridgeValidator.validate(giveOutQueryRequest);
        return CharityBaseResult.success(waybillCharityGiveOutService.queryGiveOutList(giveOutQueryRequest));
    }

    /**
     * 包裹发放列表内容导出
     * @param giveOutQueryRequest
     * @return
     */
    @ResponseBody
    @RequestMapping("/list/export")
    public CharityBaseResult<CharityExcelResponse> exportList(@RequestBody CharityGiveOutQueryRequest giveOutQueryRequest) {
        CharityLoginUserUtil.popLoginInfo(giveOutQueryRequest);
        String url = CharityExcelUtil.exportFile(giveOutQueryRequest, "包裹发放统计列表", CharityGiveOutQueryResponse.class,
                request1 -> waybillCharityGiveOutService.queryGiveOutList(request1));
        return CharityBaseResult.success(new CharityExcelResponse(url));
    }

    /**
     * 包裹发放单号明细查询
     */
    @ResponseBody
    @RequestMapping("/detail")
    public CharityBaseResult<CharityPagingResponse<CharityGiveOutDetailQueryResponse>> detail(@RequestBody CharityGiveOutQueryRequest giveOutQueryRequest){
        BridgeValidator.validate(giveOutQueryRequest);
        return CharityBaseResult.success(waybillCharityGiveOutService.queryGiveOutDetail(giveOutQueryRequest));
    }

    /**
     * 包裹发放单号明细修改
     */
    @ResponseBody
    @RequestMapping("/detail/update")
    public CharityBaseResult<String> detailUpdate(@RequestBody CharityGiveOutUpdateRequest giveOutUpdateRequest){
        BridgeValidator.validate(giveOutUpdateRequest);
        // 鉴权 管理员可修改所有记录；非管理员，机构类型为省级中心，仅能修改本省的记录；非管理员，机构类型为非省级中心，仅能修改本机构的记录。
        boolean isAdmin = CharityRoleEnum.admin.name().equals(giveOutUpdateRequest.getRole());
        if(!isAdmin){
            // 查询所属机构类型是否有省级中心机构
            List<WaybillCharityOrgDTO> belongOrgList = charityOrgService.listOrgByUserId(CharityConstant.WARM_PROJECT, giveOutUpdateRequest.getLoginUserId());

            List<WaybillCharityOrgDTO> provinceOrgList = belongOrgList.stream().filter(
                orgInfo -> CharityOrgTypeEnum.provinceCenter.getCode().equals(orgInfo.getType())).collect(
                Collectors.toList());
            // 查询所选的机构是否属于当前省份
            CharityOrgResponse orgResponse = charityOrgService.findByName(CharityConstant.WARM_PROJECT, giveOutUpdateRequest.getSenderOrgName());
            if(null == orgResponse ){
                return CharityBaseResult.bizFail("ORG_NOT_FOUND","机构信息不存在");
            }
            // 省级机构仅判定省份一致即可
            List<WaybillCharityOrgDTO> filterList = provinceOrgList.stream().filter(
                item -> StringUtils.equals(orgResponse.getProvince(), item.getProvince())).collect(
                Collectors.toList());
            // 其他非省级中心机构需判定是同一机构
            List<WaybillCharityOrgDTO> filterSelfList = belongOrgList.stream().filter(
                item -> StringUtils.equals(orgResponse.getOrgCode(), item.getOrgCode())).collect(
                Collectors.toList());
            PickUpLogUtil.info("用户归属机构列表.giveOutUpdateRequest:{},belongOrgList:{},filterList:{},filterSelfList:{}",
                JSONObject.toJSONString(giveOutUpdateRequest),
                JSONObject.toJSONString(belongOrgList),
                JSONObject.toJSONString(filterList),
                JSONObject.toJSONString(filterSelfList));
            if(CollectionUtils.isEmpty(filterList) && CollectionUtils.isEmpty(filterSelfList)){
                return CharityBaseResult.bizFail("PERMISSION_ERROR","您无该机构权限无法修改包裹详细信息");
            }
        }

        return CharityBaseResult.success(waybillCharityGiveOutService.updateGiveOutDetail(giveOutUpdateRequest));
    }

    /**
     * 包裹发放单号明细内容导出
     * @param giveOutQueryRequest
     * @return
     */
    @ResponseBody
    @RequestMapping("/detail/export")
    public CharityBaseResult<CharityExcelResponse> exportDetail(@RequestBody CharityGiveOutQueryRequest giveOutQueryRequest) {
        CharityLoginUserUtil.popLoginInfo(giveOutQueryRequest);
        String url = CharityExcelUtil.exportFile(giveOutQueryRequest, "包裹发放单号明细列表", CharityGiveOutDetailQueryResponse.class,
                request1 -> waybillCharityGiveOutService.queryGiveOutDetail(request1));
        return CharityBaseResult.success(new CharityExcelResponse(url));
    }


}
