package com.cainiao.waybill.bridge.enterprise.outbound.service.impl;

import com.base.BaseTest;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseUserTypeEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.ExpressTypeEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.outbound.request.EnterprisePackBoundRequest;
import com.cainiao.waybill.bridge.enterprise.outbound.response.PackCpInfo;
import com.cainiao.waybill.bridge.enterprise.outbound.service.OutBoundService;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterprisePackInboundConverter;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePackInboundDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePostDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoDO;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseLocationDO;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeEnterprisePackBoundDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePackInboundMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePostMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillEnterpriseLocationMapper;
import com.cainiao.waybill.number.client.WaybillNumberCommonService;
import com.cainiao.waybill.number.client.dto.PossibleCpCodesByWaybillCodeRequest;
import com.cainiao.waybill.number.client.dto.PossibleCpCodesByWaybillCodeResponse;
import com.cainiao.waybill.number.common.ClientInfoDTO;
import com.cainiao.waybill.number.common.Result;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * <a href="https://alidocs.dingtalk.com/i/nodes/dpYLaezmVNRMGX56C1aKjlBOVrMqPxX6">单元测试编写&生成FAQ</a>
 * Created By Thub IDEA插件
 */
@RunWith(MockitoJUnitRunner.class)
public class WaybillBridgeEnterprisePackBoundServiceImplTest extends BaseTest {

    @InjectMocks
    private WaybillBridgeEnterprisePackBoundServiceImpl waybillBridgeEnterprisePackBoundServiceImpl;
    @Mock
    private DingTalkAuthService dingTalkAuthService;
    @Mock
    private WaybillNumberCommonService waybillNumberCommonService;
    @Mock
    private WaybillBridgeEnterprisePackInboundConverter enterprisePackInboundConverter;
    @Mock
    private WaybillBridgeEnterprisePackInboundMapper enterprisePackInboundMapper;
    @Mock
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;
    @Mock
    private OutBoundService outBoundService;
    @Mock
    private WaybillBridgeEnterprisePostMapper enterprisePostMapper;
    @Mock
    private WaybillEnterpriseLocationMapper enterpriseLocationMapper;
    @Mock
    private List<String> adminList;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testOutboundPage() {
        // Setup, prepare mock data
        Mockito.when(enterprisePackInboundConverter.convertFromDOList(anyList()))
                .thenReturn(Arrays.<WaybillBridgeEnterprisePackBoundDTO>asList(new WaybillBridgeEnterprisePackBoundDTO()));
        Mockito.when(enterprisePackInboundMapper.countByParam(any()))
                .thenReturn(0L);
        WaybillBridgeEnterprisePackInboundDO waybillBridgeEnterprisePackInboundDO = new WaybillBridgeEnterprisePackInboundDO();
        waybillBridgeEnterprisePackInboundDO.setStatus((byte) 0);
        Mockito.when(enterprisePackInboundMapper.selectByParam(any()))
                .thenReturn(null);
        WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        waybillBridgeEnterpriseUserInfoDO.setUserId("userId");
        waybillBridgeEnterpriseUserInfoDO.setUserType("ADMIN");
        Mockito.when(enterpriseUserInfoMapper.selectByUserId(anyString(), anyString()))
                .thenReturn(waybillBridgeEnterpriseUserInfoDO);

        // Prepare method arguments
        EnterprisePackBoundRequest enterprisePackBoundRequest = new EnterprisePackBoundRequest();
        enterprisePackBoundRequest.setCorpId("corpId");
        enterprisePackBoundRequest.setUserId("userId");
        enterprisePackBoundRequest.setSourceFrom("USER");
        enterprisePackBoundRequest.setPageSize(1);
        enterprisePackBoundRequest.setCurrentPage(1);
        enterprisePackBoundRequest.setConsigneeUserId("consigneeUserId");
        // Run the method to be tested
        BridgePagingDTO<WaybillBridgeEnterprisePackBoundDTO> result = waybillBridgeEnterprisePackBoundServiceImpl.outboundPage(enterprisePackBoundRequest);

        // Verify the result
    }

    /**
     * 测试有员工信息时包裹状态和信息设置的正确性
     */
    @Test
    public void testExpressInStoreWithEmployee() {
        // Mock依赖方法
        WaybillBridgeEnterprisePackInboundDO mockInboundDO = null;
        Mockito.when(enterprisePackInboundMapper.selectOneByParam(Mockito.any())).thenReturn(mockInboundDO);
        WaybillBridgeEnterpriseUserInfoDO mockUserInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        Mockito.when(enterpriseUserInfoMapper.selectByUserId(Mockito.anyString(), Mockito.anyString())).thenReturn(mockUserInfoDO);
        Mockito.when(enterprisePackInboundMapper.insert(Mockito.any())).thenReturn(1);
        Mockito.when(outBoundService.pushOrderPickUpMessage(Mockito.any())).thenReturn(true);
        // 准备测试数据
        EnterprisePackBoundRequest request = new EnterprisePackBoundRequest();
        request.setWaybillCode("testWaybillCode");
        request.setOperatorUserId("testOperatorUserId");
        request.setCorpId("testCorpId");
        request.setEmployee(true);
        request.setConsigneeUserId("testConsigneeUserId");
        request.setConsigneeName("testConsigneeName");
        request.setConsigneePhone("testConsigneePhone");
        request.setBizType("1");
        request.setCpCode("testCpCode");
        // 调用测试方法
        int result = waybillBridgeEnterprisePackBoundServiceImpl.expressInStore(request);
    }

    /**
     * 测试插入新数据并发送取件消息
     */
    @Test
    public void testInsertNewDataAndSendPickUpMessage() {
        // Mock依赖方法
        WaybillBridgeEnterprisePackInboundDO mockInboundDO = null;
        Mockito.when(enterprisePackInboundMapper.selectOneByParam(Mockito.any())).thenReturn(mockInboundDO);
        WaybillBridgeEnterpriseUserInfoDO mockUserInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        Mockito.when(enterpriseUserInfoMapper.selectByUserId(Mockito.anyString(), Mockito.anyString())).thenReturn(mockUserInfoDO);
        Mockito.when(enterprisePackInboundMapper.insert(Mockito.any())).thenReturn(1);
        Mockito.when(outBoundService.pushOrderPickUpMessage(Mockito.any())).thenReturn(true);
        // 准备测试数据
        EnterprisePackBoundRequest request = new EnterprisePackBoundRequest();
        request.setWaybillCode("testWaybillCode");
        request.setOperatorUserId("testOperatorUserId");
        request.setCorpId("testCorpId");
        request.setEmployee(false);
        request.setConsigneeName("testConsigneeName");
        request.setConsigneePhone("testConsigneePhone");
        request.setBizType("1");
        request.setCpCode("testCpCode");
        // 调用测试方法
        int result = waybillBridgeEnterprisePackBoundServiceImpl.expressInStore(request);
        // 验证方法调用结果
    }

    @Test(expected = EnterpriseException.class)
    public void testOutboundPageWhenPostIdError() {
        EnterprisePackBoundRequest packBoundRequest = new EnterprisePackBoundRequest();
        packBoundRequest.setSourceFrom(EnterpriseUserTypeEnum.ADMIN.name());
        packBoundRequest.setCurrentPage(1);
        packBoundRequest.setPageSize(10);
        packBoundRequest.setOperatorUserId("123");
        packBoundRequest.setCorpId("123");

        WaybillBridgeEnterpriseUserInfoDO userInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        userInfoDO.setUserId("123");
        userInfoDO.setUserType(EnterpriseUserTypeEnum.ADMIN.name());
        when(enterpriseUserInfoMapper.selectByUserId("123", "123")).thenReturn(userInfoDO);

        packBoundRequest.setPostId("123123");
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(null);

        waybillBridgeEnterprisePackBoundServiceImpl.outboundPage(packBoundRequest);
    }

    @Test(expected = EnterpriseException.class)
    public void testOutboundPageWhenLocationIdError() {
        EnterprisePackBoundRequest packBoundRequest = new EnterprisePackBoundRequest();
        packBoundRequest.setSourceFrom(EnterpriseUserTypeEnum.ADMIN.name());
        packBoundRequest.setCurrentPage(1);
        packBoundRequest.setPageSize(10);
        packBoundRequest.setOperatorUserId("123");
        packBoundRequest.setCorpId("123");

        WaybillBridgeEnterpriseUserInfoDO userInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        userInfoDO.setUserId("123");
        userInfoDO.setUserType(EnterpriseUserTypeEnum.ADMIN.name());
        when(enterpriseUserInfoMapper.selectByUserId("123", "123")).thenReturn(userInfoDO);

        packBoundRequest.setPostId("123123");
        WaybillBridgeEnterprisePostDO postDO = new WaybillBridgeEnterprisePostDO();
        postDO.setId(123L);
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(postDO);

        packBoundRequest.setLocationId("123123");
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(null);
        waybillBridgeEnterprisePackBoundServiceImpl.outboundPage(packBoundRequest);
    }

    @Test(expected = EnterpriseException.class)
    public void testExpressInStoreWhenLocationIdError() {
        EnterprisePackBoundRequest packBoundRequest = new EnterprisePackBoundRequest();
        packBoundRequest.setSourceFrom(EnterpriseUserTypeEnum.ADMIN.name());
        packBoundRequest.setCurrentPage(1);
        packBoundRequest.setPageSize(10);
        packBoundRequest.setOperatorUserId("123");
        packBoundRequest.setCorpId("123");
        packBoundRequest.setWaybillCode("123123");
        packBoundRequest.setBizType("0");
        when(enterprisePackInboundMapper.selectOneByParam(any())).thenReturn(null);

        WaybillBridgeEnterpriseUserInfoDO userInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        userInfoDO.setUserId("123");
        userInfoDO.setUserType(EnterpriseUserTypeEnum.ADMIN.name());
        when(enterpriseUserInfoMapper.selectByUserId("123", "123")).thenReturn(userInfoDO);

        packBoundRequest.setLocationId("123123");
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(null);

        waybillBridgeEnterprisePackBoundServiceImpl.expressInStore(packBoundRequest);
    }

    @Test(expected = EnterpriseException.class)
    public void testExpressInStoreWhenPostIdError() {
        EnterprisePackBoundRequest packBoundRequest = new EnterprisePackBoundRequest();
        packBoundRequest.setSourceFrom(EnterpriseUserTypeEnum.ADMIN.name());
        packBoundRequest.setCurrentPage(1);
        packBoundRequest.setPageSize(10);
        packBoundRequest.setOperatorUserId("123");
        packBoundRequest.setCorpId("123");
        packBoundRequest.setWaybillCode("123123");
        packBoundRequest.setBizType("0");
        when(enterprisePackInboundMapper.selectOneByParam(any())).thenReturn(null);

        WaybillBridgeEnterpriseUserInfoDO userInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        userInfoDO.setUserId("123");
        userInfoDO.setUserType(EnterpriseUserTypeEnum.ADMIN.name());
        when(enterpriseUserInfoMapper.selectByUserId("123", "123")).thenReturn(userInfoDO);

        packBoundRequest.setLocationId("123123");
        WaybillEnterpriseLocationDO locationDO = new WaybillEnterpriseLocationDO();
        locationDO.setId(123L);
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(locationDO);

        packBoundRequest.setPostId("123123");
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(null);

        waybillBridgeEnterprisePackBoundServiceImpl.expressInStore(packBoundRequest);
    }

    @Test(expected = EnterpriseException.class)
    public void testExpressInStoreSuccess() {
        EnterprisePackBoundRequest packBoundRequest = new EnterprisePackBoundRequest();
        packBoundRequest.setSourceFrom(EnterpriseUserTypeEnum.ADMIN.name());
        packBoundRequest.setCurrentPage(1);
        packBoundRequest.setPageSize(10);
        packBoundRequest.setOperatorUserId("123");
        packBoundRequest.setCorpId("123");
        packBoundRequest.setWaybillCode("123123");
        packBoundRequest.setBizType("0");
        packBoundRequest.setDelivery(1);
        packBoundRequest.setCpCode("SF");
        when(enterprisePackInboundMapper.selectOneByParam(any())).thenReturn(null);

        WaybillBridgeEnterpriseUserInfoDO userInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        userInfoDO.setUserId("123");
        userInfoDO.setUserType(EnterpriseUserTypeEnum.ADMIN.name());
        when(enterpriseUserInfoMapper.selectByUserId("123", "123")).thenReturn(userInfoDO);

        packBoundRequest.setLocationId("123123");
        WaybillEnterpriseLocationDO locationDO = new WaybillEnterpriseLocationDO();
        locationDO.setId(123L);
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(locationDO);

        packBoundRequest.setPostId("123123");
        WaybillBridgeEnterprisePostDO postDO = new WaybillBridgeEnterprisePostDO();
        postDO.setId(123L);
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(postDO);

        when(enterprisePackInboundMapper.insert(any())).thenReturn(1);

        when(outBoundService.pushOrderPickUpMessage(any())).thenReturn(true);
        int result = waybillBridgeEnterprisePackBoundServiceImpl.expressInStore(packBoundRequest);
        Assert.assertEquals(1, result);
    }
}