// 右键菜单，可以一键生成哦。
// 配置格式参见 https://yuque.antfin-inc.com/xd6pvi/dr962p 
{
	"tableName":"ali_social_work_goods_detail",
	"domainObjectName":"AliSocialWorkGoodsDetail",
	"mapperName":"AliSocialWorkGoodsDetailDAO",
	"appName":"CAINIAO_WAYBILL_BRIDGE_APP",
	"groupKey":"",
	"split":false,
	"pattern":"_[0-9]+$",
	"anno":true,
	"antZdal":false,
	"appList":[
		"CAINIAO_WAYBILL_BRIDGE_APP$"
	],
	"blobEnable":false,
	"businessPackageName":"",
	"businessTargetProject":"waybill-bridge-client/src/main/java",
	"catlog":"cainiao_waybill_bridge",
	"columnName":"id",
	"connectionURL":"",
	"createBaseType":false,
	"disableNamespace":false,
	"driverClass":"",
	"enableDeleteMethod":true,
	"fileEncode":"UTF-8",
	"generateBusiness":false,
	"generateKey":true,
	"ignoreColumn":false,
	"ignoreColumnValue":"id",
	"javaClientGeneratorTargetPackage":"com.cainiao.waybill.bridge.alisocial.model.mapper",
	"javaClientGeneratorTargetProject":"waybill-bridge-aliSocialWork/src/main/java",
	"javaClientGeneratorType":"XMLMAPPER",
	"javaModelGeneratorTargetPackage":"com.cainiao.waybill.bridge.alisocial.model.domain",
	"javaModelGeneratorTargetProject":"waybill-bridge-aliSocialWork/src/main/java",
	"lastBizPackage":"",
	"lastBizProjectPath":"waybill-bridge-client/src/main/java",
	"lastTypeHadler":"com.alibaba.schedulerx.shade.scala.collection.immutable.$colon$colon$",
	"lastTypeJava":"",
	"mergePolicy":1,
	"modelType":"flat",
	"myDataBase":null,
	"password":"",
	"replaceIs":false,
	"saveParameter":true,
	"saveParameterPath":".aligenerator",
	"security":false,
	"serializable":false,
	"simpleMethod":true,
	"sqlMapGeneratorTargetPackage":"com.cainiao.waybill.bridge.alisocial.model.mapper",
	"sqlMapGeneratorTargetProject":"waybill-bridge-aliSocialWork/src/main/resources",
	"sqlStatement":"JDBC",
	"tableSchema":"",
	"targetRuntime":"MyBatis3",
	"tddl":true,
	"tddlMap":{
		"CAINIAO_WAYBILL_BRIDGE_APP":""
	},
	"typeHandlerMap":{},
	"typeMap":{},
	"useBaseDao":false,
	"useDBTime":false,
	"useDao":true,
	"useLombok":false,
	"useSqlCreate":false,
	"userId":"",
	"whenPresent":true,
	"generatedBy":"Aligenerator",
	"schemaVersion":"1.0"
}