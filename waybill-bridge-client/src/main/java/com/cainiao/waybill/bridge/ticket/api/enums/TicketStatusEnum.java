package com.cainiao.waybill.bridge.ticket.api.enums;

import java.util.Objects;

/**
 * 工单状态枚举值
 * <AUTHOR>
 */
public enum TicketStatusEnum {

    STATUS_0(0,"取消"),
    STATUS_1(1,"待处理"),
    STATUS_2(2,"处理中"),
    STATUS_3(3,"完结"),

    ;

    private Integer status;
    private String content;

    TicketStatusEnum(Integer status, String content){
        this.status = status;
        this.content = content;
    }

    public Integer getStatus(){
        return status;
    }

    public String getContent(){
        return content;
    }

    public static String getContentByStatus(Integer inputStatus){
        if (inputStatus == null){
            return null;
        }
        for (TicketStatusEnum ticketStatusEnum : TicketStatusEnum.values()) {
            if (Objects.equals(ticketStatusEnum.getStatus(),inputStatus)){
                return ticketStatusEnum.getContent();
            }
        }
        return null;
    }

    public static Integer getStatusByContent(String inputContent){
        if (inputContent == null){
            return null;
        }
        for (TicketStatusEnum ticketStatusEnum : TicketStatusEnum.values()) {
            if (Objects.equals(ticketStatusEnum.getContent(),inputContent)){
                return ticketStatusEnum.getStatus();
            }
            if (Objects.equals(String.valueOf(ticketStatusEnum.getStatus()),inputContent)){
                return ticketStatusEnum.getStatus();
            }
        }
        return 2;
    }

    public static void main(String[] args) {
        System.out.println(getContentByStatus(1));
    }
}
