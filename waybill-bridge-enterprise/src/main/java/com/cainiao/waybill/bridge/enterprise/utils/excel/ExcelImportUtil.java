package com.cainiao.waybill.bridge.enterprise.utils.excel;

import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ReflectionUtils;

import java.beans.PropertyDescriptor;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/3/20 16:19
 */
@Slf4j
public class ExcelImportUtil {


    @SneakyThrows
    public static <T> List<T> loadData(InputStream file, String name, Class<T> tClass, int maxNum) {
        if (file == null) {
            return Collections.emptyList();
        }
        List<T> excelList = new ArrayList<>();
        try (Workbook workbook = StringUtils.isNotBlank(name) && name.endsWith("xls") ? new HSSFWorkbook(file): new XSSFWorkbook(file)) {
            //创建返回对象，把每行中的值作为一个数组，所有行作为一个集合返回

            //获得当前sheet工作表
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                return Collections.emptyList();
            }
            List<Field> fields = Lists.newArrayList();
            //fields的顺序为，属性的定义顺序
            ReflectionUtils.doWithFields(tClass, fields::add,
                field -> field.isAnnotationPresent(ExcelHeader.class)
                    && field.getAnnotation(ExcelHeader.class).check());
            if (sheet.getLastRowNum() > maxNum) {
                throw new EnterpriseException("row_num_over_limit", String.format("文件行数超过最大%s限制", maxNum));
            }
            // 校验表头
            Map<Integer, Field> fieldMap = checkExcelFirstRow(sheet, fields);

            //从第二行开始
            for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
                Row row = sheet.getRow(rowNum);
                if (row == null) {
                    continue;
                }
                T t = tClass.newInstance();
                //循环当前行
                for (int cellNum = row.getFirstCellNum(); cellNum < row.getLastCellNum(); cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    if (cell == null) {
                        continue;
                    }
                    String val = getCellValue(cell).trim();
                    Field field = fieldMap.get(cellNum);
                    if(field == null){
                        continue;
                    }
                    PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(tClass, field.getName());
                    propertyDescriptor.getWriteMethod().invoke(t, val);
                }
                excelList.add(t);
            }
        }
        return excelList;
    }

    @SneakyThrows
    public static <T> List<T> loadDataFromInputStream(InputStream in, String fileName, Class<T> tClass, int maxNum) {
        return loadData(in, fileName, tClass, maxNum);
    }

    private static Map<Integer, Field> checkExcelFirstRow(Sheet sheet, List<Field> fields) {
        // check表头
        Row firstRow = sheet.getRow(0);
        // 属性列的最后一行为错误信息，所以这里排除最后一行
        //if (firstRow.getLastCellNum() != (fields.size() - 1)) {
        //    throw new BridgeBusinessException("template_not_match", "模板不匹配");
        //}

        Map<Integer, Field> map = new HashMap<>();

        for (int cellNum = firstRow.getFirstCellNum(); cellNum < firstRow.getLastCellNum(); cellNum++) {
            Cell cell = firstRow.getCell(cellNum);
            if (cell == null) {
                continue;
            }
            String val = getCellValue(cell);

            for (Field field : ListUtil.non(fields)) {
                if(StringUtils.contains(val, field.getAnnotation(ExcelHeader.class).value())){
                    map.put(cellNum, field);
                }
            }
        }
        if(map.size() != fields.size()){
            throw new EnterpriseException("template_cell_not_match", "模板列不匹配，请更换模板");
        }
        return map;
    }

    private static String getCellValue(Cell cell) {
        cell.setCellType(CellType.STRING);
        return cell.getStringCellValue();
    }


}
