package com.cainiao.waybill.bridge.client.request;

import java.io.Serializable;
import java.util.List;

import com.cainiao.waybill.bridge.client.entity.GoodsInfo;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * HSF下单请求
 * <AUTHOR>
 * @date 2024/7/10 21:35
 **/
@Data
public class PickUpHsfCreateOrderRequest extends PickUpCreateOrderBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 来源系统编码
     */
    @NotBlank(message = "param error|来源系统编码为空")
    @Length(max = 64, message = "来源系统编码最大长度64")
    private String resourceCode;

    /**
     * 商品信息
     */
    private List<GoodsInfo> goodsInfos;

}
