package com.cainiao.waybill.bridge.web.pickup.cp;

import com.alibaba.common.lang.ExceptionUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.security.SecurityUtil;

import cn.hutool.core.util.NumberUtil;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.CancelCodes;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Cp;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpEventConstants;
import com.cainiao.waybill.bridge.biz.pickup.dto.deppon.TracePushResponse;
import com.cainiao.waybill.bridge.biz.pickup.dto.deppon.UpdateStatusRequest;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.service.impl.WaybillPickUpEventMetaQSender;
import com.cainiao.waybill.bridge.biz.utils.pickup.CpLogisticNoUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpFeatureUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.util.WeightUnitUtil;
import com.cainiao.waybill.bridge.common.waybill.constants.WaybillPickUpActionConstant;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;

/**
 * 德邦物流详情推送
 *
 * <AUTHOR>
 * @date 2021/9/4-下午5:58
 */
@Controller
public class DepponTracePushController {

    private static final String RESP_SUCCESS = "true";

    private static final String RESP_FAIL = "false";


    @Resource
    private WaybillPickUpOrderManager waybillPickUpOrderManager;

    @Resource
    private WaybillPickUpEventMetaQSender waybillPickUpEventMetaQSender;


    /**
     * 德邦物流详情推送接口
     *
     * @param req  :
     * @param resp :
     */
    @PostMapping("/tracepush/deppon")
    public void depponTracePush(HttpServletRequest req, HttpServletResponse resp) {
        //时间戳
        String timestamp = req.getParameter("timestamp");
        //摘要
        String digest = req.getParameter("digest");
        //参数
        String params = req.getParameter("params");

        PickUpLogUtil.info("timestamp:" + timestamp);
        PickUpLogUtil.info("digest:" + digest);
        PickUpLogUtil.info("params:" + params);
        UpdateStatusRequest request = null;
        try {
            //时间戳验证使用，判断请求是否在有效的时间戳范围内，时间戳范围根据情况自行定义
            long sendTime = Long.parseLong(timestamp);
            long now = System.currentTimeMillis();
            long effectiveTimestamp = Long.parseLong(BridgeSwitch.depponPickUpConfig.get(PickUpConstants.Deppon.ConfigKey.effectiveTimestamp.name()));

            //摘要验证使用 appkey为双方约定
            String appkey = BridgeSwitch.depponPickUpConfig.get(PickUpConstants.Deppon.ConfigKey.appKey.name());
            String expectDigest = PickUpCommonUtil.getDigest(params + appkey + timestamp);

            //  获取推送的状态内容
            request = JSON.parseObject(params, UpdateStatusRequest.class);

            TracePushResponse resultInfo = new TracePushResponse();
            resultInfo.setResultCode("1000");
            resultInfo.setResult(RESP_SUCCESS);
            resultInfo.setReason("成功");
            resultInfo.setLogisticCompanyID(SecurityUtil.escapeHtml(request.getLogisticCompanyID()));
            resultInfo.setLogisticID(SecurityUtil.escapeHtml(request.getLogisticID()));

            if (Math.abs(now - sendTime) > effectiveTimestamp) {
                resultInfo.setResultCode("2003");
                resultInfo.setResult(RESP_FAIL);
                resultInfo.setReason("时间戳验证失败");
            } else if (!expectDigest.equals(digest.trim())) {
                resultInfo.setResultCode("2002");
                resultInfo.setResult(RESP_FAIL);
                resultInfo.setReason("摘要验证失败");
            }

            if (RESP_SUCCESS.equals(resultInfo.getResult())) {
                dealBiz(request);
            }

            dealResponse(resp, resultInfo);
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpConstants.Action.DEPPON_TRACE_PUSH_RPC.name(), PickUpConstants.Error.TRACE_PUSH_RPC_ERROR_DEPPON.getErrorCode(), PickUpConstants.Error.TRACE_PUSH_RPC_ERROR_DEPPON.getErrorMsg() + ExceptionUtil.getStackTrace(e));
            TracePushResponse resultInfo = new TracePushResponse();
            resultInfo.setResult(RESP_FAIL);
            resultInfo.setReason(PickUpConstants.Error.TRACE_PUSH_SYS_ERROR.getErrorMsg());
            resultInfo.setLogisticID(request != null ? SecurityUtil.escapeHtml(request.getLogisticID()) : "");
            resultInfo.setLogisticCompanyID(request != null ? SecurityUtil.escapeHtml(request.getLogisticCompanyID()) : PickUpConstants.Deppon.LOGISTIC_COMPANY_ID);
            dealResponse(resp, resultInfo);
        }
    }

    /**
     * 按照德邦开放文档的要求：以HttpServletResponse向德邦反馈结果
     */
    private void dealResponse(HttpServletResponse resp, TracePushResponse resultInfo) {
        // 给德邦响应结果
        String response = JSON.toJSONString(resultInfo);
        resp.setCharacterEncoding("UTF-8");
        try {
            try (PrintWriter pw = resp.getWriter()) {
                pw.println(response);
                pw.flush();
            }
        } catch (IOException e) {
            PickUpLogUtil.errLog("", PickUpConstants.Action.DEPPON_TRACE_PUSH_RPC.name(), PickUpConstants.Error.TRACE_PUSH_RPC_ERROR_DEPPON.getErrorCode(), PickUpConstants.Error.TRACE_PUSH_RPC_ERROR_DEPPON.getErrorMsg() + ExceptionUtil.getStackTrace(e));
        }
    }

    /**
     * 根据德邦回传的状态信息，封装成内部消息进行发送
     */
    private void dealBiz(UpdateStatusRequest request) {
        WaybillPickUpDetailDO detailDO;
        if(NumberUtil.isNumber(request.getLogisticID())){
            detailDO = waybillPickUpOrderManager.getById(Long.parseLong(request.getLogisticID()));
        }else{
            ImmutablePair<String, String> pair = CpLogisticNoUtil.Deppon.parseResCodeAndOuterOrderCode(request.getLogisticID());
            //  根据resCode、outerOrderCode，查出对应的唯一一条订单
            detailDO = waybillPickUpOrderManager.get(pair.left, pair.right);
        }
        if(detailDO == null || !StringUtil.equals(detailDO.getCpCode(), Cp.DBKD.name())){
            return;
        }
        String action = getAction(request, detailDO);
        if (StringUtil.isBlank(action)) {
            return;
        }

        WaybillPickUpEvent pickUpEvent = new WaybillPickUpEvent();
        pickUpEvent.setAction(action);
        pickUpEvent.setActionDesc(WaybillPickUpActionConstant.getActionDesc(action));
        pickUpEvent.setMailNo(detailDO.getMailNo());
        pickUpEvent.setCpCode(PickUpConstants.Cp.DBKD.name());
        pickUpEvent.setResCode(detailDO.getResCode());
        pickUpEvent.setOuterOrderCode(detailDO.getOuterOrderCode());
        pickUpEvent.setLinkCpCode(PickUpFeatureUtil.getFromMulString(detailDO, PickUpConstants.TraceFeatureKey.LINK_CP_CODE));
        pickUpEvent.setActionGmtModified(request.getGmtUpdated().getTime());
        pickUpEvent.setLastActionDetail(request.getComments());
        setExtraInfo(pickUpEvent, request);

        waybillPickUpEventMetaQSender.send(pickUpEvent);
    }

    /**
     * 获取状态信息中的额外信息
     */
    private void setExtraInfo(WaybillPickUpEvent pickUpEvent, UpdateStatusRequest request) {
        Map<String, String> extraInfo = Maps.newHashMap();
        if (WaybillPickUpActionConstant.GOT.equals(pickUpEvent.getAction()) || WaybillPickUpActionConstant.MODIFY_WEIGHT.equals(pickUpEvent.getAction())) {
            int weight = WeightUnitUtil.kg2g(String.valueOf(request.getTotalWeight()));
            if (weight > 0) {
                extraInfo.put(PickUpEventConstants.ExtraInfoKey.WEIGHT, String.valueOf(weight));
            }
        }
        if (WaybillPickUpActionConstant.ACCEPT.equals(pickUpEvent.getAction())) {
            String comments = request.getComments();
            String courierMobile = PickUpCommonUtil.getPhone(comments);
            if (StringUtil.isBlank(courierMobile)) {
                courierMobile = getPhone(comments);
            }
            extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_MOBILE, courierMobile);
            extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_NAME, getCourierName(comments));
            long acceptTime = pickUpEvent.getActionGmtModified();
            extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_TIME, PickUpFeatureUtil.formatTime(acceptTime));
        }
        if (WaybillPickUpActionConstant.FAIL.equals(pickUpEvent.getAction())) {
            extraInfo.put(PickUpConstants.TraceFeatureKey.FAIL_REASON, PickUpFeatureUtil.dealFailReason(request.getComments()));
            extraInfo.put(PickUpConstants.TraceFeatureKey.FAIL_CODE, CancelCodes.CP_CANCEL.getCode());
            extraInfo.put(PickUpConstants.TraceFeatureKey.CANCEL_TIME, PickUpFeatureUtil.formatTime(pickUpEvent.getActionGmtModified()));
        }

        if (MapUtils.isNotEmpty(extraInfo)) {
            pickUpEvent.setExtraInfo(JSON.toJSONString(extraInfo));
        }
    }

    private String getPhone(String comments) {
        int index = -1;
        for (int i = 0; i < comments.length(); i++) {
            if (comments.charAt(i) >= '0' && comments.charAt(i) <= '9') {
                index = i;
                break;
            }
        }
        if (index > -1) {
            return comments.substring(index);
        }
        throw new RuntimeException("接单信息中无小件员手机号");
    }

    /**
     * "comments":"接货中段建亮17708848786"
     */
    private String getCourierName(String comments) {
        int numIndex = -1;
        for (int i = 0; i < comments.length(); i++) {
            if ('0' <= comments.charAt(i) && comments.charAt(i) <= '9') {
                numIndex = i;
                break;
            }
        }
        return comments.substring(3, numIndex);
    }

    private String getAction(UpdateStatusRequest request, WaybillPickUpDetailDO detailDO) {
        String action = BridgeSwitch.depponPickUpAction.get(request.getStatusType());
        if (StringUtil.isBlank(action)) {
            return null;
        }
        //  与德邦确定了：一切根据揽收时传入的第一次重量为准。但是，为了避免特殊意外情况发生，这里还是加上"重量变化"的处理，后续在使用重量时还是取首次重量即可
        if (WaybillPickUpActionConstant.GOT.equals(action)) {
            String existWeightStr = PickUpFeatureUtil.getFromMulString(detailDO, PickUpConstants.TraceFeatureKey.CP_WEIGHT);
            if (StringUtil.isNotBlank(existWeightStr)) {
                int existWeight = Integer.parseInt(existWeightStr);
                int newWeight = WeightUnitUtil.kg2g(String.valueOf(request.getTotalWeight()));
                if (newWeight > 0 && newWeight != existWeight) {
                    return WaybillPickUpActionConstant.MODIFY_WEIGHT;
                } else {
                    return null;
                }
            }
        }
        return action;
    }
}
