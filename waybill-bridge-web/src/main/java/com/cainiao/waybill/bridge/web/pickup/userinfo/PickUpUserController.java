package com.cainiao.waybill.bridge.web.pickup.userinfo;

import java.util.List;
import java.util.stream.Collectors;

import com.cainiao.waybill.bridge.biz.common.user.WaybillBridgeUserInfoService;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpBaseRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpUserCreateRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpUserRemoveRequest;
import com.cainiao.waybill.bridge.common.constants.UserInfoSceneEnum;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeUserInfoDTO;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.pickup.constant.TicketResourceEnum;
import com.cainiao.waybill.bridge.biz.ticket.dto.TicketRoleEnum;
import com.cainiao.waybill.bridge.web.pickup.response.PickUpRightResource;
import com.cainiao.waybill.bridge.web.pickup.response.PickUpUserInfoResponse;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpTicketController
 * @Description
 * @Date 2022/11/29 10:46 上午
 * @Version 1.0
 */
@RestController
public class PickUpUserController {

    @Autowired
    private WaybillBridgeUserInfoService waybillBridgeUserInfoService;

    @RequestMapping("/pickup/user/info")
    public BaseResult<PickUpUserInfoResponse> queryUser(PickUpBaseRequest pickUpBaseRequest) {
        WaybillBridgeUserInfoDTO waybillBridgeUserInfoDTO = waybillBridgeUserInfoService.findCharityUser(
            pickUpBaseRequest.getAccount(), UserInfoSceneEnum.Ntb.name());

        return BaseResult.success(convert(waybillBridgeUserInfoDTO));
    }

    @RequestMapping("/pickup/user/right")
    public BaseResult<List<PickUpRightResource>> userRight(PickUpBaseRequest request) {
        List<TicketResourceEnum> resourceEnumList = TicketResourceEnum.roleResourceList(TicketRoleEnum.get(request.getRole()));
        List<PickUpRightResource> list = resourceEnumList.stream().map(x -> {
            PickUpRightResource resource = new PickUpRightResource();
            resource.setResourceId(x.getResourceId());
            resource.setResourceName(x.getResourceName());
            return resource;
        }).collect(Collectors.toList());

        return BaseResult.success(list);
    }

    @RequestMapping("/pickup/user/list")
    public BaseResult<List<PickUpUserInfoResponse>> userList(PickUpBaseRequest request) {
        List<WaybillBridgeUserInfoDTO> userInfoDTOList = waybillBridgeUserInfoService.queryUserList(
            UserInfoSceneEnum.Ntb.name());
        List<PickUpUserInfoResponse> list = ListUtil.stream(userInfoDTOList).map(this::convert).collect(
            Collectors.toList());
        return BaseResult.success(list);
    }

    @RequestMapping("/pickup/user/add")
    public BaseResult<Void> add(@RequestBody PickUpUserCreateRequest request) {
        waybillBridgeUserInfoService.createUser(request.getNick(), request.getPhone(),
            UserInfoSceneEnum.Ntb.name(), request.getRole());
        return BaseResult.success();
    }

    @RequestMapping("/pickup/user/remove")
    public BaseResult<Void> remove(@RequestBody PickUpUserRemoveRequest request) {
        waybillBridgeUserInfoService.remove(request.getId());
        return BaseResult.success();
    }

    private PickUpUserInfoResponse convert(WaybillBridgeUserInfoDTO user) {
        if (user == null) {
            return null;
        }
        PickUpUserInfoResponse response = new PickUpUserInfoResponse();
        response.setId(user.getId());
        response.setNick(user.getNick());
        response.setPhone(user.getPhone());
        response.setRole(user.getRole());
        return response;
    }
}
