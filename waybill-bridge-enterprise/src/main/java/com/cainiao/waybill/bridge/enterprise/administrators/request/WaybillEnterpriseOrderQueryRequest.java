package com.cainiao.waybill.bridge.enterprise.administrators.request;

import com.cainiao.waybill.bridge.enterprise.common.Paging;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseOrderStatusEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseSourceFormEnum;
import lombok.Data;

/**
 * 寄件列表请求
 * <AUTHOR>
 * @date 2025/4/28 10:55
 */
@Data
public class WaybillEnterpriseOrderQueryRequest extends Paging {

    private static final long serialVersionUID = -2120771705325493088L;
    /**
     * 订单起始时间
     */

    private String startTime;

    /**
     * 订单结束时间
     */
    private String endTime;
    /**
     * 场地id
     */
    private String locationId;

    /**
     * 运力服务商
     */
    private String cpCode;

    /**
     * 下单人
     */
    private String userName;

    /**
     * 下单人 id
     */
    private String userId;

    /**
     * 订单状态
     * @see EnterpriseOrderStatusEnum
     */
    private Integer orderStatus;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 来源
     * @see EnterpriseSourceFormEnum
     */
    private String sourceFrom;

    /**
     * 业务类型
     */
    private Long businessType;

    /**
     * 小邮局id
     */
    private String postId;


}