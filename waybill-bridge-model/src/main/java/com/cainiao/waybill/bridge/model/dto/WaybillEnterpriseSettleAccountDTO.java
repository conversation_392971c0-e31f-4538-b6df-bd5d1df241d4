package com.cainiao.waybill.bridge.model.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 月结账号DTO
 * <AUTHOR>
 */
@Data
public class WaybillEnterpriseSettleAccountDTO implements Serializable {
    private static final long serialVersionUID = 567564321465765443L;

    /**
     *   主键
     */
    private String waybillAccountId;

    /**
     *   创建时间
     */
    private Date gmtCreate;

    /**
     *   修改时间
     */
    private Date gmtModified;

    /**
     *   企业id
     */
    private String corpId;

    /**
     *   月结账号
     */
    private String waybillAccountNo;

    /**
     *   运力服务商
     */
    private String cpCode;

    /**
     *   运力服务商
     */
    private String cpName;

    /**
     *   产品类型
     * com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseProductTypeEnum
     *
     */
    private String product;

    /**
     *   产品类型list
     */
    private List<JSONObject> productList;

    /**
     *   备注
     */
    private String remark;

    /**
     *   签名
     */
    private String sign;

    /**
     *   结算类型
     */
    private Integer businessType;


}