package com.cainiao.waybill.bridge.biz.link.pickup.service.impl.cprouting;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.cainiao.waybill.bridge.biz.pickup.dto.create.pre.PickUpCreatePreTdAppointTimeSlotDTO;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

/**
 * <AUTHOR> zouping.fzp
 * @Classname CpRountingContext
 * @Description
 * @Date 2022/8/2 2:03 下午
 * @Version 1.0
 */
@Data
public class CpRoutingContext implements Serializable {

    private static final long serialVersionUID = -1936623361300428353L;

    /**
     * 优先使用的cp
     */
    private String firstCp;

    private List<String> availableCpList = Lists.newArrayList();

    private List<String> usedCpList;

    private Integer itemVersion;

    private PickUpCreatePreTdAppointTimeSlotDTO appointTimeSlotDTO;

    private String acceptBranchCode;

    private String routerStrategyId;

    // 缓存cp可达性校验结果
    Map<String, Boolean> cpReachResult = Maps.newHashMap();

    public String availableCp() {
        if (CollectionUtils.isEmpty(availableCpList)) {
            return null;
        }
        // 过滤使用过的cpCode
        for (String cpCode : availableCpList) {
            if (usedCpList != null && usedCpList.contains(cpCode)) {
                continue;
            }
            return cpCode;
        }
        return null;
    }

    public void addUsedCp(String cpCode){
        if(usedCpList == null){
            usedCpList = Lists.newArrayList();
        }
        usedCpList.add(cpCode);
    }
}
