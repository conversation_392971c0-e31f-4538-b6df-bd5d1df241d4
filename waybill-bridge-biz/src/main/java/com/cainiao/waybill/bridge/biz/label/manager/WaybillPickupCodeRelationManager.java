package com.cainiao.waybill.bridge.biz.label.manager;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.label.dto.request.UpdateWeightRequest;
import com.cainiao.waybill.bridge.common.util.Page;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeApplyNewRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeQueryRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeUpdateRequest;
import com.cainiao.waybill.bridge.model.domain.WaybillPickupCodeRelationDO;

/**
 * 面单-揽件码关系 manager
 * <AUTHOR>
 * @date 2017-04-27
 */
public interface WaybillPickupCodeRelationManager {

    /**
     * 创建揽件码电子面单
     * @param request
     * @param courierId
     * @param clientInfoDTO
     * @return
     * @throws BridgeBaseException
     */
    WaybillPickupCodeRelationDO createPickupCodeWaybill(WaybillPickupCodeApplyNewRequest request, Long courierId, ClientInfoDTO clientInfoDTO)throws BridgeBaseException;

    /**
     * 网点查询揽件码面单列表
     * @param request cpCode branchCode 不能为空， courierId 不能为空(分表字段)
     * @param courierId
     * @param clientInfoDTO
     * @return
     * @throws BridgeBaseException
     */
    Page<WaybillPickupCodeRelationDO> queryRelations(WaybillPickupCodeQueryRequest request, Long courierId, ClientInfoDTO clientInfoDTO) throws BridgeBaseException;

    /**
     * 更新打印状态
     *
     * @param request
     * @param courierId
     * @param clientInfoDTO
     * @throws BridgeBaseException
     */
    void updatePrintStatus(WaybillPickupCodeUpdateRequest request, Long courierId, ClientInfoDTO clientInfoDTO) throws BridgeBaseException;

    /**
     * 更新包裹重量
     *
     * @param request
     * @param courierId
     * @throws BridgeBaseException
     */
    void updateWeight(UpdateWeightRequest request, Long courierId) throws BridgeBaseException;


    /**
     * 根据 waybillCode 查询
     *
     * @param courierId
     * @param cpCode
     * @param waybillCode
     * @return
     * @throws BridgeBaseException
     */
    WaybillPickupCodeRelationDO queryByWaybillCode(Long courierId, String cpCode, String waybillCode) throws BridgeBaseException ;
}
