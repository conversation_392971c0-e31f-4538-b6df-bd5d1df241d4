package com.cainiao.waybill.bridge.enterprise.authority.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 登陆响应
 * 1.该类实现了`Serializable`接口，支持序列化操作。
 * <AUTHOR>
 * @date 2025-04-15 11:01:40
 */
@Data
public class LoginResponse implements Serializable {

    private static final long serialVersionUID = 2128184907234069130L;
    /**
     * 权限code
     */
    private String resultCode;

    /**
     * session鉴权
     */
    private String session;

    /**
     * csrfToken鉴权
     */
    private String csrfToken;
}
