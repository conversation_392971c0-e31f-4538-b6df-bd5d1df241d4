package com.cainiao.waybill.bridge.web.pickup.uploadfile;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PolicyConditions;
import com.cainiao.waybill.bridge.biz.middleware.oss.pickup.CharityOssClientFactory;
import com.cainiao.waybill.bridge.biz.middleware.oss.pickup.PickUpOssClientFactory;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketConstant;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpUserRemoveRequest;
import com.cainiao.waybill.bridge.biz.utils.pickup.OssUtils;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpFileUploadController
 * @Description
 * @Date 2022/11/30 10:19 上午
 * @Version 1.0
 */
@Slf4j(topic = "PICK_UP_MANAGER_INFO")
@RestController
public class PickUpFileUploadController {

    @RequestMapping("/pickup/file/upload")
    public BaseResult<Map<String, String>> upload(@RequestParam("file") MultipartFile file) {
        if(file.getOriginalFilename().contains("\\.")){
            return BaseResult.bizFail("file_format_error", "文件格式错误");
        }
        String[] fileName = file.getOriginalFilename().split("\\.");
        String ossFileName = PickUpTicketConstant.TICKET_IMG_OSS_DIR + "/" + System.currentTimeMillis() + "." + fileName[fileName.length - 1];
        try (InputStream in = file.getInputStream()) {
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setObjectAcl(CannedAccessControlList.PublicRead);
            metadata.setExpirationTime(DateUtils.addDays(new Date(), 365));
            String imgUrl = OssUtils.uploadToOSS(ossFileName, in, metadata);
            Map<String, String> uploadMap = Maps.newHashMap();
            uploadMap.put("url", imgUrl);
            uploadMap.put("fileName", ossFileName);
            return BaseResult.success(uploadMap);
        } catch (Throwable th) {
            log.error(th.getMessage(), th);
            return BaseResult.bizFail("", th.getMessage());
        }
    }
}
