package com.cainiao.waybill.bridge.common.util.monitor;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * <AUTHOR> on 2021-06-15.
 */
class StatInner implements Stat {
    private final LongAdder count = new LongAdder();
    private final LongAdder value = new LongAdder();

    private final AtomicLong min = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong max = new AtomicLong(Long.MIN_VALUE);

    /**
     * 通常用于计算 qps
     * count +1
     */
    @Override
    public void count() {
        count.add(1);
    }

    /**
     * 通常用于计算 qps
     * count + count
     */
    @Override
    public void count(long count) {
        this.count.add(count);
    }

    /**
     * 通常用于计算 rt
     * count + count,
     * value + value
     */
    @Override
    public void countAndSum(long count, long value) {
        this.count.add(count);
        this.value.add(value);
    }

    /**
     * 通常用于计算 rt
     * value + value
     */
    @Override
    public void countAndSum(long value) {
        countAndSum(1, value);
    }

    /**
     * 通常用于计算 平均rt，最长rt，最短rt
     * value
     */
    @Override
    public void countAndSumAndMinMax(long value) {
        countAndSum(1, value);
        minMax(value);
    }

    /**
     * 记录一段时间内的最大值和最小值
     * @param value 要记录的值
     */
    @Override
    public void minMax(long value) {
        long oldMax = max.get();
        if (oldMax <= value) {
            while (!max.compareAndSet(oldMax, value) && (oldMax = max.get()) <= value) {
            }
        }
        long oldMin = min.get();
        if (oldMin >= value) {
            while (!min.compareAndSet(oldMin, value) && (oldMin = min.get()) >= value) {
            }
        }
    }

    @Override
    public String toString() {
        final long sum = count.sum();
        final long time = value.sum();
        return String.format("%s;%s;%.2f;%s;%s;", sum, time, time == 0 ? 0 : time * 1.0 / sum,
            min.get() == Long.MAX_VALUE ? 0 : min.get(),
            max.get() == Long.MIN_VALUE ? 0 : max.get());
    }

    /**
     * 是否记录数据
     */
    public boolean hasStated() {
        return count.sum() > 0 || value.sum() > 0 || min.get() != Long.MAX_VALUE || max.get() != Long.MIN_VALUE;
    }
}
