package com.cainiao.waybill.bridge.model.common;

import com.alibaba.common.lang.diagnostic.Profiler;
import com.cainiao.waybill.bridge.common.util.Page;
import com.taobao.common.dao.persistence.DBRoute;
import com.taobao.common.dao.persistence.exception.DAOException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lanbo <br>
 * @version: 1.0 <br>
 * @date: 2012-6-1
 */
public class SqlMapBaseDAO extends com.taobao.common.dao.persistence.SqlMapBaseDAO {

    @Override
    protected Object executeQueryForObject(String statementName, Object parameterObject, DBRoute dr, boolean isExistsExit) throws DAOException {
        Profiler.enter("executeQueryForObject #" + statementName + "#");
        try {
            return super.executeQueryForObject(statementName, parameterObject, dr, isExistsExit);
        } finally {
            Profiler.release();
        }
    }

    @Override
    @SuppressWarnings("rawtypes")

    protected List executeQueryForList(String statementName, Object parameterObject, DBRoute dr) throws DAOException {
        Profiler.enter("executeQueryForList #" + statementName + "#");
        try {
            return super.executeQueryForList(statementName, parameterObject, dr);
        } finally {
            Profiler.release();
        }
    }

    @Override
    @SuppressWarnings("rawtypes")

    protected Map executeQueryForMap(String statementName, Object parameterObject, DBRoute dr, String key) throws DAOException {
        Profiler.enter("executeQueryForMap #" + statementName + "#");
        try {
            return super.executeQueryForMap(statementName, parameterObject, dr, key);
        } finally {
            Profiler.release();
            final String detail = Profiler.dump("Detail: ", "           ");
        }
    }


    @Override
    protected int executeUpdate(String statementName, Object parameterObject, DBRoute dr) throws DAOException {
        Profiler.enter("executeUpdate #" + statementName + "#");
        try {
            return super.executeUpdate(statementName, parameterObject, dr);
        } finally {
            Profiler.release();
            final String detail = Profiler.dump("Detail: ", "           ");
        }
    }


    @Override
    protected Object executeInsert(String statementName, Object parameterObject, DBRoute dr) throws DAOException {
        Profiler.enter("executeInsert #" + statementName + "#");
        try {
            return super.executeInsert(statementName, parameterObject, dr);
        } finally {
            Profiler.release();
            final String detail = Profiler.dump("Detail: ", "           ");
        }
    }

    private static Integer getStartIndex(Integer currentPage, Integer pageSize) {
        if (currentPage == null || currentPage <= 0) {
            currentPage = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = PAGE_SIZE;
        }
        return (currentPage - 1) * pageSize;
    }

    private static Map<String, Object> getPageMap(Integer currentPage, Integer pageSize) {
        Integer start = getStartIndex(currentPage, pageSize); // 用正确的pageSize拿到 正确的start（起始位置）
        Map<String, Object> param = new HashMap<String, Object>();
        if (start == null || start < 0) {
            start = 0;
        }
        if (pageSize == null || pageSize < 0) {
            pageSize = PAGE_SIZE;
        }
        param.put("_start_", start);
        param.put("_size_", pageSize + 1);  // 这里用pageSize+1 ， 查出pageSize+1条， 主要为了满足判断是否有下一页
        return param;
    }

    protected <E> Page<E> executeQueryForPage(String statementName, String countStatementName, Map<String, Object> parameterObject, Integer pageSize, Integer pageIndex, DBRoute dr)
            throws DAOException {
        Map<String, Object> param = getPageMap(pageIndex, pageSize);
        if (parameterObject != null && !parameterObject.isEmpty()) {
            param.putAll(parameterObject);
        }
        try {
            Integer start = getStartIndex(pageIndex, pageSize);// 这里要使用正常的start
            List<E> data = this.executeQueryForList(statementName, param, dr);
            // 不查询count，性能扛不住
            // Integer count = (Integer) this.executeQueryForObject(countStatementName, param, dr);
            return new Page<E>(data, start, pageSize, 0);// caount位置直接给0
        } catch (Exception e) {
            throw new DAOException(e);
        }
    }

    private static Integer getStartIndexWithCount(Integer currentPage, Integer pageSize) {
        if (currentPage == null || currentPage <= 0) {
            currentPage = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = PAGE_SIZE;
        }
        return (currentPage - 1) * pageSize;
    }

    private static Map<String, Object> getPageMapWithCount(Integer currentPage, Integer pageSize) {
        Integer start = getStartIndex(currentPage, pageSize);
        Map<String, Object> param = new HashMap<String, Object>();
        if (start == null || start < 0) {
            start = 0;
        }
        if (pageSize == null || pageSize < 0) {
            pageSize = PAGE_SIZE;
        }
        param.put("_start_", start);
        param.put("_size_", pageSize);
        return param;
    }

    protected <E> Page<E> executeQueryForPageWithCount(String statementName, String countStatementName, Map<String, Object> parameterObject, Integer pageSize, Integer pageIndex,
            DBRoute dr) throws DAOException {
        Map<String, Object> param = getPageMapWithCount(pageIndex, pageSize);
        if (parameterObject != null && !parameterObject.isEmpty()) {
            param.putAll(parameterObject);
        }
        try {
            Integer start = getStartIndexWithCount(pageIndex, pageSize);
            List<E> data = this.executeQueryForList(statementName, param, dr);
            Integer count = (Integer) this.executeQueryForObject(countStatementName, param, dr);
            return new Page<E>(data, start, pageSize, count);
        } catch (Exception e) {
            throw new DAOException(e);
        }
    }
}
