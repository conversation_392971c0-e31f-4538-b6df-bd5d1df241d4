package com.cainiao.waybill.bridge.model.dto;

import lombok.Data;

/**
 * 指标统计报告
 * <AUTHOR>
 * @date 2025/2/18 18:18
 **/
@Data
public class WaybillPickUpReportDTO {

    /**
     * 客户渠道
     */
    private String orderChannels;

    /**
     * 客户名
     */
    private String customer;

    /**
     * cpCode
     */
    private String cpCode;

    /**
     * 代理商
     */
    private String agent;

    /**
     * 订单数
     */
    private Integer orderNum;

    /**
     * 工单去重进线数
     */
    private Integer distinctTicketNum;


    /**
     * 应揽收数
     */
    private Integer shouldGotNum;

    /**
     * 实时单应揽收数
     */
    private Integer currShouldDropInNum;

    /**
     * 预约单应揽收数
     */
    private Integer appoShouldDropInNum;

    /**
     * CP已揽量
     */
    private Integer gotedNum;

    /**
     * 应揽及时揽收数
     */
    private Integer shouldGotInTimeGotNum;

    /**
     * CP产品类目
     */
    private String cpProduct;

    /**
     * CP应揽揽收量
     */
    private Integer shouldGotedNum;

    /**
     * 30分钟接单量
     */
    private Integer acceptIn30mNum;

    /**
     * 客户预约单应揽收及时揽收量
     */
    private Integer appoInTimeDropInNum;

    /**
     * 客户实时单应揽收及时揽收量
     */
    private Integer currInTimeDropInNum;

    /**
     * 客户应揽收及时揽收量
     */
    private Integer customerGotInTimeNum;

    /**
     * 10分钟接单量
     */
    private Integer acceptIn10mNum;

    /**
     * 客户揽收数量
     */
    private Integer customerGotNum;

    // 实时单客户揽收数量
    private Integer currCustomerGotNum;

    // 预约单客户揽收数量
    private Integer appoCustomerGotNum;

    /**
     * 客户取消数量
     */
    private Integer cancelNum;



}
