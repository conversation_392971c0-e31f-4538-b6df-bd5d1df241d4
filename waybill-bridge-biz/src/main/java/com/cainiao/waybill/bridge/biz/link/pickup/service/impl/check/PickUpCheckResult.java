package com.cainiao.waybill.bridge.biz.link.pickup.service.impl.check;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpCpConfigInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.RouteReachableResult;
import com.google.common.collect.Lists;
import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpCheckResult
 * @Description
 * @Date 2023/8/11 10:28 上午
 * @Version 1.0
 */
@Data
public class PickUpCheckResult implements Serializable {

    private static final long serialVersionUID = -1667124393262652559L;

    /**
     * 可用的cp类别(经过可达判断之后)
     */
    private List<PickUpCpConfigInfo> availableCpList = Lists.newArrayList();

    /**
     * 是否可用
     */
    private Boolean available;

    /**
     * 不可用原因
     */
    private String unavailableReason;

    /**
     * 可达结果
     */
    private Map<PickUpCpConfigInfo, RouteReachableResult> routeReachableResult = new HashMap<>();
}
