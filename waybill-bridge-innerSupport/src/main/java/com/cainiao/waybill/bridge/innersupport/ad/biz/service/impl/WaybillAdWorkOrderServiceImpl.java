package com.cainiao.waybill.bridge.innersupport.ad.biz.service.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.common.waybill.pickup.service.WaybillDetailQueryService;
import com.cainiao.waybill.bridge.innersupport.ad.biz.manager.WaybillAdWorkOrderManager;
import com.cainiao.waybill.bridge.innersupport.ad.biz.oss.AdOssUtils;
import com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder.*;
import com.cainiao.waybill.bridge.innersupport.ad.biz.response.common.AdPagingResponse;
import com.cainiao.waybill.bridge.innersupport.ad.biz.service.WaybillAdWorkOrderService;
import com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.role.ActionRole;
import com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.role.AdvertiserActionRole;
import com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.role.CainiaoCustomerServiceActionRole;
import com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.role.CourierCompanyActionRole;
import com.cainiao.waybill.bridge.innersupport.ad.constant.AdCommonConstant;
import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.AdErrorConstants;
import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.config.diamond.WaybillAdWorkOrderDiamondConfig;
import com.cainiao.waybill.bridge.innersupport.ad.constant.emums.AdCustomRobotGroupTypeEnum;
import com.cainiao.waybill.bridge.innersupport.ad.constant.emums.AdUserRoleEnum;
import com.cainiao.waybill.bridge.innersupport.ad.exception.AdBusinessException;
import com.cainiao.waybill.bridge.innersupport.ad.model.convert.WaybillAdWorkOrderConverter;
import com.cainiao.waybill.bridge.innersupport.ad.model.domain.WaybillAdWorkOrderParam;
import com.cainiao.waybill.bridge.innersupport.ad.model.dto.WaybillAdUserDTO;
import com.cainiao.waybill.bridge.innersupport.ad.model.dto.WaybillAdWorkOrderDTO;
import com.cainiao.waybill.bridge.innersupport.ad.util.CheckRole;
import com.cainiao.waybill.bridge.innersupport.ad.util.CustomRobotGroupMessage;
import com.cainiao.waybill.bridge.innersupport.ad.util.ValidatorUtils;
import com.cainiao.waybill.bridge.innersupport.ad.web.wrapper.WorkOrderCallWrapper;
import com.cainiao.waybill.common.seller.dto.WaybillDetailInfo;
import com.google.common.collect.Lists;
import com.taobao.cainiao.waybill.constants.WaybillConstant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.support.TransactionTemplate;
import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;

@HSFProvider(serviceInterface = WaybillAdWorkOrderService.class)
public class WaybillAdWorkOrderServiceImpl implements WaybillAdWorkOrderService {
    private final static Logger log = LoggerFactory.getLogger(AdCommonConstant.LOG_NAME);

    @Resource
    private WaybillAdWorkOrderManager waybillAdWorkOrderManager;

    @Resource
    private TransactionTemplate bridgeTransactionTemplate;

    @Resource
    private WaybillDetailQueryService waybillDetailQueryService;

    @Resource
    private WorkOrderCallWrapper workOrderCallWrapper;

    @Override
    public Boolean create(WaybillAdWorkOrderCreateRequest request) {
        // 参数校验
        String errorMsg = ValidatorUtils.validateAndReturnSingleErrorMsg(request);
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new AdBusinessException(AdErrorConstants.PARAM_ERROR.getErrorCode(), errorMsg);
        }
        WaybillAdWorkOrderDTO waybillAdWorkOrderDTO = WaybillAdWorkOrderConverter.INSTANCE.convertRequest2DTO(request);
        WaybillAdUserDTO waybillAdUserDTO = request.getLoginUserInfo();
        // 操作人
        String operatorName = waybillAdUserDTO.getName();
        waybillAdWorkOrderDTO.setOperator(operatorName);
        Map<String, Object> userInfo = CheckRole.findRoleByMobile(request);
        String userRole = String.valueOf(userInfo.get("userRole"));
        Map<String, Object> advertiserNameMap = (Map<String, Object>) userInfo.get("advertiserNameMap");
        Map<String, Object> cpNameMap = (Map<String, Object>) userInfo.get("cpNameMap");
        if (Objects.equals(userRole, AdUserRoleEnum.ADVERTISER_ROLE.getValue())) {
            // 广告主角色
            List<Map<String, Object>> advertisers = (List<Map<String, Object>>) userInfo.get("advertiserList");
            Map<String, Object> item = advertisers.get(0);
            waybillAdWorkOrderDTO.setAdvertiserName(String.valueOf(item.get("advertiserName")));
            waybillAdWorkOrderDTO.setAdvertiserId(String.valueOf(item.get("advertiserId")));
            waybillAdWorkOrderDTO.setCpName(null);
            waybillAdWorkOrderDTO.setCpCode(null);
            waybillAdWorkOrderDTO.setWorkOrderSource(null);
        } else if (Objects.equals(userRole, AdUserRoleEnum.COURIER_COMPANY_ROLE.getValue())) {
            // 快递公司
            List<Map<String, Object>> cps = (List<Map<String, Object>>) userInfo.get("cpList");
            Map<String, Object> item = cps.get(0);
            waybillAdWorkOrderDTO.setCpName(String.valueOf(item.get("cpName")));
            waybillAdWorkOrderDTO.setCpCode(String.valueOf(item.get("cpCode")));
            waybillAdWorkOrderDTO.setAdvertiserName(null);
            waybillAdWorkOrderDTO.setAdvertiserId(null);
            waybillAdWorkOrderDTO.setWorkOrderSource(null);
        } else if (Objects.equals(userRole, AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue())) {
            // 菜鸟客服，广告主Id、cpCode不能为空校验
            if (StringUtils.isBlank(request.getAdvertiserId())) {
                throw new AdBusinessException(AdErrorConstants.ADVERTISER_ID_IS_NULL_ERROR.getErrorCode(), AdErrorConstants.ADVERTISER_ID_IS_NULL_ERROR.getErrorMsg());
            }
            if (StringUtils.isBlank(request.getCpCode())) {
                throw new AdBusinessException(AdErrorConstants.CP_CODE_IS_NULL_ERROR.getErrorCode(), AdErrorConstants.CP_CODE_IS_NULL_ERROR.getErrorMsg());
            }
            waybillAdWorkOrderDTO.setAdvertiserName(advertiserNameMap.get(request.getAdvertiserId()).toString());
            waybillAdWorkOrderDTO.setCpName(cpNameMap.get(request.getCpCode()).toString());
        } else if (Objects.equals(userRole, "UNKNOWN_ROLE")) {
            // 未知角色
            throw new AdBusinessException(AdErrorConstants.UNKNOWN_ROLE_ERROR.getErrorCode(), AdErrorConstants.UNKNOWN_ROLE_ERROR.getErrorMsg());
        }
        // 运单号重复校验
        if (request.getMailNo() != null) {
            WaybillAdWorkOrderParam duplicateParam = new WaybillAdWorkOrderParam();
            duplicateParam.setMailNo(request.getMailNo());
            Long count = waybillAdWorkOrderManager.countByParam(duplicateParam);
            if (count > 0) {
                throw new AdBusinessException(AdErrorConstants.MAIL_NO_DUPLICATE_ERROR.getErrorCode(), AdErrorConstants.MAIL_NO_DUPLICATE_ERROR.getErrorMsg());
            }
        }
        // 根据运单号查询面单详情
        WaybillDetailInfo waybillDetailInfo;
        try {
            waybillDetailInfo = waybillDetailQueryService.queryOneWithMailNo(request.getMailNo());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            waybillDetailInfo = null;
        }
        if (waybillDetailInfo != null && waybillDetailInfo.getCreateTime() != null) {
            // 设置面单打印时间
            waybillAdWorkOrderDTO.setWaybillPrintTime(waybillDetailInfo.getCreateTime());
        }
        // 设置广告创意id
        if (waybillDetailInfo != null && waybillDetailInfo.getFeatureMap() != null) {
            if (StringUtils.isNotBlank(waybillDetailInfo.getFeatureMap().get(WaybillConstant.FeatureKey.AD_ID.name()))) {
                waybillAdWorkOrderDTO.setAdId(Long.valueOf(waybillDetailInfo.getFeatureMap().get(WaybillConstant.FeatureKey.AD_ID.name())));
            }
        }
        // 处理截图链接
        decoderScreenshotLinks(waybillAdWorkOrderDTO);
        // 创建工单
        waybillAdWorkOrderManager.create(waybillAdWorkOrderDTO);
        // 菜鸟客服创建工单（钉钉群发送消息）
        if (Objects.equals(userRole, AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue())) {
            // 菜鸟客服 & 广告主群里，@广告主
            if (StringUtils.isNotBlank(request.getAdvertiserId())) {
                String sendContent = "【客诉提醒】，" + request.getMailNo() + "，广告客诉工单已更新，请查收！";
                CustomRobotGroupMessage.sendGroupMessage(AdCustomRobotGroupTypeEnum.CAINIAO_AND_ADVERTISER_DING_TALK_GROUP.getValue(), request.getAdvertiserId(), AdUserRoleEnum.ADVERTISER_ROLE.getValue(), sendContent);
            }
        }
        // 快递公司创建工单（钉钉群发送消息）
        if (Objects.equals(userRole, AdUserRoleEnum.COURIER_COMPANY_ROLE.getValue())) {
            // 菜鸟客服 & 快递公司群里，@菜鸟客服
            List<Map<String, Object>> cps = (List<Map<String, Object>>) userInfo.get("cpList");
            Map<String, Object> item = cps.get(0);
            String currentCpCode = String.valueOf(item.get("cpCode"));
            if (StringUtils.isNotBlank(currentCpCode)) {
                String sendContent = "【客诉提醒】，" + request.getMailNo() + "，广告客诉工单已更新，请查收！";
                CustomRobotGroupMessage.sendGroupMessage(AdCustomRobotGroupTypeEnum.CAINIAO_AND_COURIER_COMPANY_DING_TALK_GROUP.getValue(), currentCpCode, AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue(), sendContent);
            }
        }
        // 联系客户（通过智能语音外呼）
        WaybillAdWorkOrderDiamondConfig.WaybillAdWorkOrderConfig config = WaybillAdWorkOrderDiamondConfig.getConfig();
        if(config != null) {
            Boolean callCustomerEnableSwitch = false;
            callCustomerEnableSwitch = config.getCallCustomerEnableSwitch();
            if(callCustomerEnableSwitch != null && callCustomerEnableSwitch) {
                try {
                    log.error("联系客户（通过智能语音外呼）start");
                    workOrderCallWrapper.callCustomer(request.getMailNo(), request.getMobile());
                    log.error("联系客户（通过智能语音外呼）end");
                } catch (Exception e) {
                    log.error("联系客户（通过智能语音外呼）失败", e);
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public AdPagingResponse<WaybillAdWorkOrderDTO> listPage(WaybillAdWorkOrderQueryRequest queryRequest) {
        // 参数校验
        String errorMsg = ValidatorUtils.validateAndReturnSingleErrorMsg(queryRequest);
        if (StringUtils.isNotBlank(errorMsg)) {
            return AdPagingResponse.buildError(errorMsg);
        }
        // 构建查询参数
        WaybillAdWorkOrderParam param = buildQueryParamFromRequest(queryRequest);

        // 角色校验
        Map<String, Object> userInfo = CheckRole.findRoleByMobile(queryRequest);
        String userRole = String.valueOf(userInfo.get("userRole"));
        if (Objects.equals(userRole, AdUserRoleEnum.ADVERTISER_ROLE.getValue())) {
            // 广告主角色
            List<Map<String, Object>> advertisers = (List<Map<String, Object>>) userInfo.get("advertiserList");
            Map<String, Object> item = advertisers.get(0);
            param.setAdvertiserId(String.valueOf(item.get("advertiserId")));
            param.setCpCode(null);
            param.setWorkOrderSource(null);
        } else if (Objects.equals(userRole, AdUserRoleEnum.COURIER_COMPANY_ROLE.getValue())) {
            // 快递公司
            List<Map<String, Object>> cps = (List<Map<String, Object>>) userInfo.get("cpList");
            Map<String, Object> item = cps.get(0);
            param.setCpCode(String.valueOf(item.get("cpCode")));
            param.setAdvertiserId(null);
            param.setWorkOrderSource(null);
        } else if (Objects.equals(userRole, AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue())) {
            // 菜鸟客服，不限制
        } else if (Objects.equals(userRole, "UNKNOWN_ROLE")) {
            // 未知角色
            throw new AdBusinessException(AdErrorConstants.UNKNOWN_ROLE_ERROR.getErrorCode(), AdErrorConstants.UNKNOWN_ROLE_ERROR.getErrorMsg());
        }
        Integer currentPage;
        if (queryRequest.getCurrentPage() != null) {
            currentPage = queryRequest.getCurrentPage();
        } else {
            currentPage = 1;
        }
        Integer pageSize;
        if (queryRequest.getPageSize() != null) {
            pageSize = queryRequest.getPageSize();
        } else {
            pageSize = 20;
        }
        Integer offset = (currentPage - 1) * pageSize;
        Integer limit = pageSize;
        param.setCurrentPage(offset);
        param.setPageSize(limit);
        param.setStartTime(queryRequest.getStartTime());
        param.setEndTime(queryRequest.getEndTime());
        Long count = waybillAdWorkOrderManager.countByParam(param);
        if (null == count || count == 0) {
            return AdPagingResponse.build(Lists.newArrayList(), 0L, queryRequest.getCurrentPage(), queryRequest.getPageSize());
        }
        List<WaybillAdWorkOrderDTO> waybillAdWorkOrderDTOS = waybillAdWorkOrderManager.queryByParam(param);
        List<WaybillAdWorkOrderDTO> resultWaybillAdWorkOrderDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(waybillAdWorkOrderDTOS)) {
            for (WaybillAdWorkOrderDTO waybillAdWorkOrderDTO : waybillAdWorkOrderDTOS) {
                // 处理截图链接
                processScreenshotLinks(waybillAdWorkOrderDTO);
                resultWaybillAdWorkOrderDTOS.add(waybillAdWorkOrderDTO);
            }
        }
        return AdPagingResponse.build(resultWaybillAdWorkOrderDTOS, count, queryRequest.getCurrentPage(), queryRequest.getPageSize());
    }

    /**
     * 处理截图链接
     * <p>根据传入的广告工单DTO，处理其中的截图链接，并更新到对应的字段中
     * @param waybillAdWorkOrderDTO 广告工单DTO
     * @return 无特殊情况，返回处理后的广告工单DTO
     */
    private void processScreenshotLinks(WaybillAdWorkOrderDTO waybillAdWorkOrderDTO) {
        waybillAdWorkOrderDTO.setScanCodeScreenshotLinks(
                handleScreenshotLinks(waybillAdWorkOrderDTO.getScanCodeScreenshotLinks()));
        waybillAdWorkOrderDTO.setPaymentScreenshotLinks(
                handleScreenshotLinks(waybillAdWorkOrderDTO.getPaymentScreenshotLinks()));
        waybillAdWorkOrderDTO.setRefundScreenshotLinks(
                handleScreenshotLinks(waybillAdWorkOrderDTO.getRefundScreenshotLinks()));
    }

    /**
     * 使用OSS工具类，生成预签名URL
     * @param links
     * @return
     */
    private String handleScreenshotLinks(String links) {
        if (StringUtils.isNotBlank(links)) {
            List<String> linkList = JSON.parseArray(links, String.class);
            List<String> presignedLinkList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(linkList)) {
                for (String link : linkList) {
                    presignedLinkList.add(AdOssUtils.generateOssPresignedUrl(link));
                }
            }
            return JSON.toJSONString(presignedLinkList);
        }
        return links;
    }

    /**
     * decoder 截图链接
     * <p>根据传入的广告工单DTO，处理其中的截图链接，并更新到对应的字段中
     * @param waybillAdWorkOrderDTO 广告工单DTO
     * @return 无特殊情况，返回处理后的广告工单DTO
     */
    private void decoderScreenshotLinks(WaybillAdWorkOrderDTO waybillAdWorkOrderDTO) {
        waybillAdWorkOrderDTO.setScanCodeScreenshotLinks(
                handleDecoderScreenshotLinks(waybillAdWorkOrderDTO.getScanCodeScreenshotLinks()));
        waybillAdWorkOrderDTO.setPaymentScreenshotLinks(
                handleDecoderScreenshotLinks(waybillAdWorkOrderDTO.getPaymentScreenshotLinks()));
        waybillAdWorkOrderDTO.setRefundScreenshotLinks(
                handleDecoderScreenshotLinks(waybillAdWorkOrderDTO.getRefundScreenshotLinks()));
    }

    /**
     * 具体执行 decoder 处理截图链接
     * @param links
     * @return
     */
    private String handleDecoderScreenshotLinks(String links) {
        if (StringUtils.isNotBlank(links)) {
            List<String> linkList = JSON.parseArray(links, String.class);
            List<String> decoderLinkList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(linkList)) {
                for (String link : linkList) {
                    String decodeUrl = link;
                    try {
                        decodeUrl = URLDecoder.decode(link, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        log.error(e.getMessage(), e);
                    }
                    decoderLinkList.add(decodeUrl);
                }
            }
            return JSON.toJSONString(decoderLinkList);
        }
        return links;
    }

    @Override
    public Boolean modified(WaybillAdWorkOrderUpdateRequest request) {
        // 参数校验
        String errorMsg = ValidatorUtils.validateAndReturnSingleErrorMsg(request);
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new AdBusinessException(AdErrorConstants.PARAM_ERROR.getErrorCode(), errorMsg);
        }
        WaybillAdWorkOrderDTO updatedWaybillAdWorkOrderDTO = WaybillAdWorkOrderConverter.INSTANCE.convertRequest2DTO(request);
        WaybillAdUserDTO waybillAdUserDTO = request.getLoginUserInfo();
        // 操作人
        String operatorName = waybillAdUserDTO.getName();
        updatedWaybillAdWorkOrderDTO.setOperator(operatorName);
        WaybillAdWorkOrderDTO existingWorkOrder = waybillAdWorkOrderManager.queryById(request.getId());
        // 只有菜鸟客服可以更新（快递公司名称、快递公司代码，广告主名称，广告主id）
        // 运单号重复校验
        if (request.getMailNo() != null && !Objects.equals(request.getMailNo(), existingWorkOrder.getMailNo())) {
            WaybillAdWorkOrderParam duplicateParam = new WaybillAdWorkOrderParam();
            duplicateParam.setMailNo(request.getMailNo());
            Long count = waybillAdWorkOrderManager.countByParam(duplicateParam);
            if (count > 0) {
                throw new AdBusinessException(AdErrorConstants.MAIL_NO_DUPLICATE_ERROR.getErrorCode(), AdErrorConstants.MAIL_NO_DUPLICATE_ERROR.getErrorMsg());
            }
        }
        // 完结工单标记
        Boolean completionFlag = false;
        if (existingWorkOrder != null) {
            if (request.getDealProgress() != null && existingWorkOrder.getDealProgress().equals(1) && (request.getDealProgress().equals(2) || request.getDealProgress().equals(3) || request.getDealProgress().equals(4))) {
                completionFlag = true;
            }
            if (request.getDealProgress() != null && existingWorkOrder.getDealProgress().equals(5) && (request.getDealProgress().equals(2) || request.getDealProgress().equals(3) || request.getDealProgress().equals(4))) {
                completionFlag = true;
            }
        }
        // 角色校验
        Map<String, Object> userInfo = CheckRole.findRoleByMobile(request);
        String userRole = String.valueOf(userInfo.get("userRole"));
        Map<String, Object> advertiserNameMap = (Map<String, Object>) userInfo.get("advertiserNameMap");
        Map<String, Object> cpNameMap = (Map<String, Object>) userInfo.get("cpNameMap");
        if (Objects.equals(userRole, AdUserRoleEnum.ADVERTISER_ROLE.getValue())) {
            // 广告主角色
            List<Map<String, Object>> advertisers = (List<Map<String, Object>>) userInfo.get("advertiserList");
            Map<String, Object> item = advertisers.get(0);
            if (!existingWorkOrder.getAdvertiserId().equals(item.get("advertiserId"))) {
                // 当前广告主不能更新其他广告主的数据
                throw new AdBusinessException(AdErrorConstants.USER_ROLE_NOT_MATCH.getErrorCode(), AdErrorConstants.USER_ROLE_NOT_MATCH.getErrorMsg());
            }
            updatedWaybillAdWorkOrderDTO.setAdvertiserId(null);
            updatedWaybillAdWorkOrderDTO.setAdvertiserName(null);
            updatedWaybillAdWorkOrderDTO.setCpCode(null);
            updatedWaybillAdWorkOrderDTO.setCpName(null);
            updatedWaybillAdWorkOrderDTO.setWorkOrderSource(null);
        } else if (Objects.equals(userRole, AdUserRoleEnum.COURIER_COMPANY_ROLE.getValue())) {
            // 快递公司
            List<Map<String, Object>> cps = (List<Map<String, Object>>) userInfo.get("cpList");
            Map<String, Object> item = cps.get(0);
            if (!existingWorkOrder.getCpCode().equals(item.get("cpCode"))) {
                // 当前CP不能更新其他CP的数据
                throw new AdBusinessException(AdErrorConstants.USER_ROLE_NOT_MATCH.getErrorCode(), AdErrorConstants.USER_ROLE_NOT_MATCH.getErrorMsg());
            }
            updatedWaybillAdWorkOrderDTO.setAdvertiserId(null);
            updatedWaybillAdWorkOrderDTO.setAdvertiserName(null);
            updatedWaybillAdWorkOrderDTO.setCpCode(null);
            updatedWaybillAdWorkOrderDTO.setCpName(null);
            updatedWaybillAdWorkOrderDTO.setWorkOrderSource(null);
        } else if (Objects.equals(userRole, AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue())) {
            // 菜鸟客服
            // 只有菜鸟客服，可以修改广告主、快递公司
            if (StringUtils.isNotBlank(request.getAdvertiserId())) {
                updatedWaybillAdWorkOrderDTO.setAdvertiserName(advertiserNameMap.get(request.getAdvertiserId()).toString());
            }
            if (StringUtils.isNotBlank(request.getCpCode())) {
                updatedWaybillAdWorkOrderDTO.setCpName(cpNameMap.get(request.getCpCode()).toString());
            }
        } else if (Objects.equals(userRole, "UNKNOWN_ROLE")) {
            // 未知角色
            throw new AdBusinessException(AdErrorConstants.UNKNOWN_ROLE_ERROR.getErrorCode(), AdErrorConstants.UNKNOWN_ROLE_ERROR.getErrorMsg());
        }
        // 工单完结时间
        if (completionFlag) {
            Date completionTime = new Date();
            updatedWaybillAdWorkOrderDTO.setCompletionTime(completionTime);
        }
        // 处理截图链接
        decoderScreenshotLinks(updatedWaybillAdWorkOrderDTO);
        waybillAdWorkOrderManager.update(updatedWaybillAdWorkOrderDTO);
        // 发送钉钉群消息
        sendDingTalkGroupMessages(userRole, request, existingWorkOrder);
        return Boolean.TRUE;
    }

    @Override
    public Boolean deleteById(WaybillAdWorkOrderDeleteRequest waybillAdWorkOrderDeleteRequest) {
        // 参数校验
        String errorMsg = ValidatorUtils.validateAndReturnSingleErrorMsg(waybillAdWorkOrderDeleteRequest);
        if (StringUtils.isNotBlank(errorMsg)) {
            throw new AdBusinessException(AdErrorConstants.PARAM_ERROR.getErrorCode(), errorMsg);
        }
        // 角色校验
        Map<String, Object> userInfo = CheckRole.findRoleByMobile(waybillAdWorkOrderDeleteRequest);
        String userRole = String.valueOf(userInfo.get("userRole"));
        if (userRole != AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue()) {
            throw new AdBusinessException(AdErrorConstants.USER_ROLE_NOT_MATCH.getErrorCode(), AdErrorConstants.USER_ROLE_NOT_MATCH.getErrorMsg());
        }
        if (waybillAdWorkOrderDeleteRequest.getId() != null) {
            waybillAdWorkOrderManager.deleteById(waybillAdWorkOrderDeleteRequest.getId());
        }
        return true;
    }

    @Override
    public Boolean updateWaybillPrintTimeBatch(WaybillAdWorkOrderUpdateWaybillPrintTimeRequest request) {
        List<Long> ids = request.getIds();
        if(ids != null && ids.size() > 0) {
            for (Long id : ids) {
                try {
                    WaybillAdWorkOrderDTO waybillAdWorkOrderDTO = waybillAdWorkOrderManager.queryById(id);
                    if (waybillAdWorkOrderDTO != null) {
                        // 根据运单号查询面单详情
                        WaybillDetailInfo waybillDetailInfo;
                        try {
                            waybillDetailInfo = waybillDetailQueryService.queryOneWithMailNo(waybillAdWorkOrderDTO.getMailNo());
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                            waybillDetailInfo = null;
                        }
                        if (waybillDetailInfo != null && waybillDetailInfo.getCreateTime() != null) {
                            // 设置面单打印时间
                            waybillAdWorkOrderDTO.setWaybillPrintTime(waybillDetailInfo.getCreateTime());
                        }
                        waybillAdWorkOrderManager.update(waybillAdWorkOrderDTO);
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean updateAdIdBatch(WaybillAdWorkOrderUpdateWaybillPrintTimeRequest request) {
        List<Long> ids = request.getIds();
        if (CollectionUtils.isNotEmpty(ids)) {
            for (Long id : ids) {
                WaybillAdWorkOrderDTO waybillAdWorkOrderDTO = waybillAdWorkOrderManager.queryById(id);
                if (waybillAdWorkOrderDTO != null) {
                    // 根据运单号查询面单详情
                    WaybillDetailInfo waybillDetailInfo;
                    try {
                        waybillDetailInfo = waybillDetailQueryService.queryOneWithMailNo(waybillAdWorkOrderDTO.getMailNo());
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        waybillDetailInfo = null;
                    }
                    // 设置广告创意id
                    if (waybillDetailInfo != null && waybillDetailInfo.getFeatureMap() != null) {
                        if (StringUtils.isNotBlank(waybillDetailInfo.getFeatureMap().get(WaybillConstant.FeatureKey.AD_ID.name()))) {
                            waybillAdWorkOrderDTO.setAdId(Long.valueOf(waybillDetailInfo.getFeatureMap().get(WaybillConstant.FeatureKey.AD_ID.name())));
                        }
                    }
                    waybillAdWorkOrderManager.update(waybillAdWorkOrderDTO);
                }
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 根据请求构建查询参数
     */
    private WaybillAdWorkOrderParam buildQueryParamFromRequest(WaybillAdWorkOrderQueryRequest request) {
        WaybillAdWorkOrderParam param = new WaybillAdWorkOrderParam();
        // 处理进度
        if (request.getDealProgress() != null) {
            param.setDealProgress(request.getDealProgress());
        }
        // 快递公司Code（菜鸟客服才可以从请求中获取）
        if (request.getCpCode() != null) {
            param.setCpCode(request.getCpCode());
        }
        // 广告主id（菜鸟客服才可以从请求中获取）
        if (request.getAdvertiserId() != null) {
            param.setAdvertiserId(request.getAdvertiserId());
        }
        // 工单来源
        if (request.getWorkOrderSource() != null) {
            param.setWorkOrderSource(request.getWorkOrderSource());
        }
        // 运单号
        if (request.getMailNo() != null && request.getMailNo() != "") {
            param.setMailNo(request.getMailNo());
        }
        // 电话
        if (request.getMobile() != null && request.getMobile() != "") {
            param.setMobile(request.getMobile());
        }
        // 当前页
        if (request.getCurrentPage() != null) {
            param.setCurrentPage(request.getCurrentPage());
        }
        // 页码
        if (request.getPageSize() != null) {
            param.setPageSize(request.getPageSize());
        }
        return param;
    }


    /**
     * 向钉钉群组发送消息
     * <p>根据用户角色发送不同类型的工单更新请求
     * @param userRole 用户角色
     * @param request  工单更新请求
     * @param existingWorkOrder 已存在的工单
     * @return <无特殊情况，无返回值>
     * #throw <无>
     */
    private void sendDingTalkGroupMessages(String userRole, WaybillAdWorkOrderUpdateRequest request, WaybillAdWorkOrderDTO existingWorkOrder) {
        ActionRole role;
        switch (userRole) {
            case "CAINIAO_CUSTOMER_SERVICE_ROLE":
                role = new CainiaoCustomerServiceActionRole();
                break;
            case "ADVERTISER_ROLE":
                role = new AdvertiserActionRole();
                break;
            case "COURIER_COMPANY_ROLE":
                role = new CourierCompanyActionRole();
                break;
            default:
                role = null;
        }
        if (role != null) {
            // 执行角色对应的变更处理逻辑
            role.handleChanges(request, existingWorkOrder);
        }
    }
}
