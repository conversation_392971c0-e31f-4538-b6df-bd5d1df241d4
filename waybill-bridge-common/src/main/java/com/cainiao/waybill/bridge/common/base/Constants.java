package com.cainiao.waybill.bridge.common.base;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * 常量工具
 * 
 * <AUTHOR>
 * @since 9/15/14
 */
public final class Constants {

    public static class ErrorConstant {
        protected String errorCode;
        protected String errorMsg;

        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

        public void setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMsg() {
            return errorMsg;
        }

        public ErrorConstant set(String errorCode, String errorMsg) {
            this.errorCode = errorCode;
            this.errorMsg = errorMsg;
            return this;
        }
    }

    public static class ConstantCollection {

        protected static Map<String, String> codeMsgMap = Maps.newHashMap();

        public static String getErrorMsg(String errorCode) {
            if (codeMsgMap.containsKey(errorCode)) {
                return codeMsgMap.get(errorCode);
            }
            return errorCode;
        }

        public static ErrorConstant newConstant(String errorCode, String errorMsg) {
            codeMsgMap.put(errorCode, errorMsg);
            return new ErrorConstant().set(errorCode, errorMsg);
        }
    }
}
