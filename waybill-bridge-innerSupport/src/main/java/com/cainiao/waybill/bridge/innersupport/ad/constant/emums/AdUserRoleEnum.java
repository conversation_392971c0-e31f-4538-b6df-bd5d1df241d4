package com.cainiao.waybill.bridge.innersupport.ad.constant.emums;

public enum AdUserRoleEnum {
    // 菜鸟客服
    CAINIAO_CUSTOMER_SERVICE_ROLE("菜鸟客服", "CAINIAO_CUSTOMER_SERVICE_ROLE"),

    // 广告主
    ADVERTISER_ROLE("广告主", "ADVERTISER_ROLE"),

    // 快递公司
    COURIER_COMPANY_ROLE("快递公司", "COURIER_COMPANY_ROLE");

    private final String name;
    private final String value;

    AdUserRoleEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }
}

