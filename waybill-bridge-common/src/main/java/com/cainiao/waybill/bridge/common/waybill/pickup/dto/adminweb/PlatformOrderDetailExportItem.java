package com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb;

import lombok.Data;

import java.io.Serializable;

/**
 * 平台数据详情导出对象字段格式
 *
 * <AUTHOR>
 * @date 2021/11/22-上午11:19
 */
@Data
public class PlatformOrderDetailExportItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 快递公司
     */
    private String cpCodeName;
    /**
     * 订单编号
     */
    private String outerOrderCode;
    /**
     * 运单号
     */
    private String mailNo;
    /**
     * 下单时间
     */
    private String createTime;
    /**
     * 状态
     */
    private String status;

    /**
     * 接单时间
     */
    private String acceptTime;
    /**
     * 接单网点
     */
    private String acceptBranchName;
    /**
     * 接单小件员名称
     */
    private String acceptCourierName;


    /**
     * 揽收时间
     */
    private String gotTime;
    /**
     * 签收时间
     */
    private String signTime;
    /**
     * 取消时间
     */
    private String cancelTime;
    /**
     * 取消原因
     */
    private String cancelDesc;

}
