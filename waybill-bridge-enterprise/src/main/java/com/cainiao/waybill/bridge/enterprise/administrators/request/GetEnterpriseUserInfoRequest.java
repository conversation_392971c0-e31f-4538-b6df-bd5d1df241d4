package com.cainiao.waybill.bridge.enterprise.administrators.request;

import com.cainiao.waybill.bridge.enterprise.common.Paging;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/4/16
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class GetEnterpriseUserInfoRequest extends Paging {

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 是否只查询管理员
     */
    private Boolean selectAdmin;

    /**
     * 用户类型
     */
    private String userType;

}
