package com.cainiao.waybill.bridge.common.constants;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 物流详情状态枚举
 * 会把不同物流公司不同物流节点(action)，转换为固定的状态(status)
 *
 * @author: yexin
 * @date: 2022-04-18 14:45
 **/
@Getter
public enum LogisticStatusEnum {

    CREATE("已下单", 0),
    // 仓段状态枚举
    WAREHOUSE_ACCEPT("仓库已接单", 100),
    WAREHOUSE_PROCESS("仓库处理中", 150),
    WAREHOUSE_CONFIRMED("已出库", 200),
    CONSIGN("已发货", 300),
    // 配送段状态枚举
    ACCEPT("已揽件", 400),
    LH_HO("干线运输中", 430),
    // 进口 海外作业段状态枚举 JK_HW_ACCEPT("本地已揽件",470), JK_HWC("仓作业中",471), JK_BSC("保税仓作业中",472), JK_GFC("GFC仓作业中",473), JK_GJGX("干线运输中",474), CC_HO("清关中",475),
    // 配送段状态枚举
    TRANSPORT("运输中", 500),
    DELIVERING("派送中", 600),
    FAILED("物流异常提醒", 700),
    REJECT("拒签", 800),
    WAITTING_DELIVERY("待提货", 800),
    AGENT_SIGN("待取件", 900),
    STA_DELIVERING("驿站派送中", 901),
    OTHER_SIGN("他人代收", 950),
    SIGN("已签收", 1000),
    ORDER_TRANSER("已转单", 1100),
    REVERSE_RETURN("退货返回", 1200);


    private static final Map<String, LogisticStatusEnum> LOGISTIC_STATUS_ENUM_MAP;

    static {
        LOGISTIC_STATUS_ENUM_MAP = Maps.newHashMapWithExpectedSize(LogisticStatusEnum.values().length);
        for (LogisticStatusEnum logisticStatusEnum : LogisticStatusEnum.values()) {
            LOGISTIC_STATUS_ENUM_MAP.put(logisticStatusEnum.name(), logisticStatusEnum);
        }
    }

    public static LogisticStatusEnum getWithEnumName(String enumName) {
        return LOGISTIC_STATUS_ENUM_MAP.get(enumName);
    }

    private final String statusDesc;

    private final int statusValue;

    LogisticStatusEnum(String statusDesc, int statusValue) {
        this.statusDesc = statusDesc;
        this.statusValue = statusValue;
    }

    public static String getDescByStatusValue(Integer statusValue) {
        if(statusValue == null){
            return null;
        }
        for (LogisticStatusEnum logisticStatusEnum : LogisticStatusEnum.values()) {
            if (logisticStatusEnum.getStatusValue() == statusValue) {
                return logisticStatusEnum.getStatusDesc();
            }
        }
        return null;
    }

}
