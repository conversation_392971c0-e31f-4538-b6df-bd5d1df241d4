package com.cainiao.waybill.bridge.biz.label.manager;

import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.label.dto.AccountBalanceAlarmDTO;
import com.cainiao.waybill.bridge.common.label.dto.AccountBalanceAlarmQueryDTO;
import com.cainiao.waybill.bridge.model.domain.AccountBalanceAlarmDO;

/**
 * 账户余额告警 manager
 * <AUTHOR>
 * @since 2017/04/21
 */
public interface AccountBalanceAlarmManager {
    /**
     * 配置余额告警值
     * @param accountBalanceAlarmDTO
     */
    void saveAccountBalanceAlarm(AccountBalanceAlarmDTO accountBalanceAlarmDTO)throws BridgeBaseException;

    /**
     * check当前账号余额是否触发设置值。如果触发则发送短信
     * @param accountBalanceAlarmQueryDTO
     */
    void checkAndSendAlarm(AccountBalanceAlarmQueryDTO accountBalanceAlarmQueryDTO)throws BridgeBaseException;

    AccountBalanceAlarmDO getAccountBalanceAlarm(AccountBalanceAlarmQueryDTO accountBalanceAlarmQueryDTO) throws BridgeBaseException;
}
