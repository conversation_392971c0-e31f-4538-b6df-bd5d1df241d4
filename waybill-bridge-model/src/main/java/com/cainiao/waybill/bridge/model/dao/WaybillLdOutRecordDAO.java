package com.cainiao.waybill.bridge.model.dao;

import com.cainiao.waybill.bridge.common.waybill.pickup.dto.WaybillLdSubRecordCreateTimeQueryDTO;
import com.cainiao.waybill.bridge.common.waybill.pickup.dto.WaybillLdSubRecordQueryDTO;
import com.cainiao.waybill.bridge.model.domain.WaybillLdOutRecordDO;
import com.cainiao.waybill.bridge.model.dto.WaybillLdOutRecordUpdateDTO;
import com.taobao.common.dao.persistence.exception.DAOException;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/17-下午4:14
 */
public interface WaybillLdOutRecordDAO {

    /**
     * 基本插入
     *
     * @param ldOutRecordDO
     * @return :
     * @throws DAOException :
     */
    Integer insert(WaybillLdOutRecordDO ldOutRecordDO) throws DAOException;

    Integer updateId(WaybillLdOutRecordUpdateDTO updateDTO) throws DAOException;

    /**
     * 根据面单号查询
     *
     * @param mailNo ：
     * @return ：
     * @throws DAOException :
     */
    List<WaybillLdOutRecordDO> queryWithMailNo(String mailNo) throws DAOException;

    /**
     *
     * @param queryDTO :
     * @return :
     * @throws DAOException :
     */
    List<WaybillLdOutRecordDO> queryWithCreateTime(WaybillLdSubRecordCreateTimeQueryDTO queryDTO) throws DAOException;


    /**
     * 综合查询
     *
     * @param ldSubRecordQueryDTO :
     * @return :
     * @throws DAOException :
     */
    List<WaybillLdOutRecordDO> query(WaybillLdSubRecordQueryDTO ldSubRecordQueryDTO) throws DAOException;

    /**
     * 统计主键id=#{id}的行数
     *
     * @param id :
     * @return :
     */
    Integer countWithId(Long id) throws DAOException;

    Long getId() throws DAOException;
}
