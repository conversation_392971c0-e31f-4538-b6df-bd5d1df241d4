package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.api.open.ReceiveService;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_TICKET_REPLY.WaybillPickUpTicketPushRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PICK_UP_TICKET_REPLY.WaybillPickUpTicketPushResponse;

/**
 * @author: yexin
 * @date: 2022-05-06 11:08
 **/
public class TmsWaybillPickUpTicketReplyServiceImpl implements ReceiveService<WaybillPickUpTicketPushRequest, WaybillPickUpTicketPushResponse> {

    @Override
    public WaybillPickUpTicketPushResponse invoke(ReceiveParams<WaybillPickUpTicketPushRequest> receiveParams) {
        PickUpLogUtil.info("WaybillPickUpTicketPushRequest : "+receiveParams);
        WaybillPickUpTicketPushResponse ticketPushResponse = new WaybillPickUpTicketPushResponse();
        ticketPushResponse.setSuccess(true);
        return ticketPushResponse;
    }
}
