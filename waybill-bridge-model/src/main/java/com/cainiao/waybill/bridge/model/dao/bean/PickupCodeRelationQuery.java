package com.cainiao.waybill.bridge.model.dao.bean;

import java.util.Date;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-28
 */
public class PickupCodeRelationQuery {

    private Long courierId;
    private Date createDateStart;
    private Date createDateEnd;
    private Byte printStatus;
    private String cpCode;
    private String branchCode;
    private String pickupCode;
    private String shopName;

    public Long getCourierId() {
        return courierId;
    }

    public PickupCodeRelationQuery setCourierId(Long courierId) {
        this.courierId = courierId;
        return this;
    }

    public Date getCreateDateStart() {
        return createDateStart;
    }

    public PickupCodeRelationQuery setCreateDateStart(Date createDateStart) {
        this.createDateStart = createDateStart;
        return this;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public PickupCodeRelationQuery setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
        return this;
    }

    public Byte getPrintStatus() {
        return printStatus;
    }

    public PickupCodeRelationQuery setPrintStatus(Byte printStatus) {
        this.printStatus = printStatus;
        return this;
    }

    public String getCpCode() {
        return cpCode;
    }

    public PickupCodeRelationQuery setCpCode(String cpCode) {
        this.cpCode = cpCode;
        return this;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public PickupCodeRelationQuery setBranchCode(String branchCode) {
        this.branchCode = branchCode;
        return this;
    }

    public String getPickupCode() {
        return pickupCode;
    }

    public PickupCodeRelationQuery setPickupCode(String pickupCode) {
        this.pickupCode = pickupCode;
        return this;
    }

    public String getShopName() {
        return shopName;
    }

    public PickupCodeRelationQuery setShopName(String shopName) {
        this.shopName = shopName;
        return this;
    }
}
