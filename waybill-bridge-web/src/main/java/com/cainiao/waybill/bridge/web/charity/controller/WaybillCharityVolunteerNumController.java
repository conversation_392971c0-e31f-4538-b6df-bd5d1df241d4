package com.cainiao.waybill.bridge.web.charity.controller;

import com.cainiao.waybill.bridge.biz.charity.request.CharityVolunteerCountRequest;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityVolunteerService;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.web.charity.CharityBaseResult;
import com.cainiao.waybill.bridge.web.charity.CharityLoginUserUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@Api(tags = "自愿者管理")
@RestController
@RequestMapping(value = "/charity/volunteer")
public class WaybillCharityVolunteerNumController extends BaseController {

    @Autowired
    private WaybillCharityVolunteerService waybillCharityVolunteerService;

    /**
     * 统计志愿者数量 相同执行单位进行数量累加
     */
    @ResponseBody
    @RequestMapping("/count")
    public CharityBaseResult<Long> countVolunteerNum(@RequestBody CharityVolunteerCountRequest volunteerCountRequest){
        BridgeValidator.validate(volunteerCountRequest);
        return CharityBaseResult.success(waybillCharityVolunteerService.countVolunteerNumByOrg(volunteerCountRequest));
    }
}
