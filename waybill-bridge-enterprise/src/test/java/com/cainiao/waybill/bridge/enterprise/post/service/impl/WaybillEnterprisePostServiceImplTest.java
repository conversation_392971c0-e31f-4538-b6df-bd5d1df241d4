package com.cainiao.waybill.bridge.enterprise.post.service.impl;

import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.post.request.EnterprisePostRequest;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterprisePostConverter;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterpriseUserInfoConverter;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePostDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoDO;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseLocationDO;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterprisePostDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserInfoDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePostMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillEnterpriseLocationMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.transaction.support.TransactionTemplate;
import org.testng.collections.Lists;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class WaybillEnterprisePostServiceImplTest {
    @InjectMocks
    private WaybillEnterprisePostServiceImpl waybillEnterprisePostService;
    @Mock
    private WaybillBridgeEnterprisePostMapper enterprisePostMapper;
    @Mock
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;
    @Mock
    private WaybillBridgeEnterprisePostConverter enterprisePostConverter;
    @Mock
    private WaybillBridgeEnterpriseUserInfoConverter enterpriseUserInfoConverter;
    @Mock
    private WaybillEnterpriseLocationMapper enterpriseLocationMapper;
    @Mock
    private TransactionTemplate bridgeTransactionTemplate;

    // 检查帖子是否存在的分支
    @Test(expected = EnterpriseException.class)
    public void testDeletePostByPostIdPostNotExist() {
        String postId = "testPostId";
        String corpId = "testCorpId";
        // 模拟帖子不存在
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(null);
        // 调用方法并期望抛出 EnterpriseException 异常
        waybillEnterprisePostService.deletePostByPostId(postId, corpId);
    }

    @Test(expected = EnterpriseException.class)
    public void testDeletePostByPostIdSuccess() {
        String postId = "testPostId";
        String corpId = "testCorpId";

        WaybillBridgeEnterprisePostDO postDO = mock(WaybillBridgeEnterprisePostDO.class);
        when(postDO.getId()).thenReturn(1L);
        when(postDO.getCorpId()).thenReturn(corpId);
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(postDO);

        WaybillBridgeEnterpriseUserInfoDO userInfoDO = mock(WaybillBridgeEnterpriseUserInfoDO.class);
        userInfoDO.setUserType("POST_ADMIN");
        List<WaybillBridgeEnterpriseUserInfoDO> userInfoList = Lists.newArrayList(userInfoDO);
        when(enterpriseUserInfoMapper.selectByParam(any())).thenReturn(userInfoList);
        when(enterpriseUserInfoMapper.updateByPrimaryKey(any())).thenReturn(1);

        when(enterprisePostMapper.deleteByPrimaryKey(any())).thenReturn(1);

        waybillEnterprisePostService.deletePostByPostId(postId, corpId);
    }


    // 更新关联用户信息分支
    @Test
    public void testDeletePostByPostIdUpdateUserInfo() {
        String postId = "testPostId";
        String corpId = "testCorpId";
        WaybillBridgeEnterprisePostDO postDO = mock(WaybillBridgeEnterprisePostDO.class);
        when(postDO.getId()).thenReturn(1L);
        when(postDO.getCorpId()).thenReturn(corpId);
        // 模拟帖子存在
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(postDO);
        // 模拟用户信息存在
        WaybillBridgeEnterpriseUserInfoDO userInfoDO = mock(WaybillBridgeEnterpriseUserInfoDO.class);
        List<WaybillBridgeEnterpriseUserInfoDO> userInfoDOList = Collections.singletonList(userInfoDO);
        when(enterpriseUserInfoMapper.selectByParam(any())).thenReturn(userInfoDOList);
        // 调用方法
        waybillEnterprisePostService.deletePostByPostId(postId, corpId);
        // 验证用户信息更新
        verify(userInfoDO).setGmtModified(any(Date.class));
        verify(userInfoDO).setPostId(null);
        verify(enterpriseUserInfoMapper).updateByPrimaryKey(userInfoDO);
    }

    // 异常处理分支
    @Test(expected = EnterpriseException.class)
    public void testDeletePostByPostIdExceptionHandling() {
        String postId = "testPostId";
        String corpId = "testCorpId";
        WaybillBridgeEnterprisePostDO postDO = mock(WaybillBridgeEnterprisePostDO.class);
        when(postDO.getId()).thenReturn(1L);
        when(postDO.getCorpId()).thenReturn(corpId);
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(postDO);
        // 模拟更新用户信息时抛出异常
        doThrow(RuntimeException.class).when(enterpriseUserInfoMapper).updateByPrimaryKey(any(WaybillBridgeEnterpriseUserInfoDO.class));
        // 调用方法并期望抛出 EnterpriseException 异常
        waybillEnterprisePostService.deletePostByPostId(postId, corpId);
    }

    // 检查WaybillBridgeEnterprisePostDO是否为null时抛出异常
    @Test(expected = EnterpriseException.class)
    public void testGetPostByPostIdPostNotExist() {
        // 设置测试参数
        String postId = "testPostId";
        String corpId = "testCorpId";
        // 模拟帖子不存在
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(null);
        // 调用方法并期望抛出 EnterpriseException 异常
        waybillEnterprisePostService.getPostByPostId(postId, corpId);
    }

    // 转换WaybillBridgeEnterprisePostDO到WaybillEnterprisePostDTO并验证转换
    @Test
    public void testGetPostByPostIdConversion() {
        // 设置测试参数
        String postId = "testPostId";
        String corpId = "testCorpId";

        // mock WaybillBridgeEnterprisePostDO
        WaybillBridgeEnterprisePostDO postDO = mock(WaybillBridgeEnterprisePostDO.class);
        when(postDO.getId()).thenReturn(1L);
        when(postDO.getCorpId()).thenReturn(corpId);
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(postDO);
        // mock WaybillEnterprisePostDTO
        WaybillEnterprisePostDTO postDTO = mock(WaybillEnterprisePostDTO.class);
        when(enterprisePostConverter.convertFromDO(postDO)).thenReturn(postDTO);
        // mock User Info List
        List<WaybillBridgeEnterpriseUserInfoDO> userInfoDOList = Collections.singletonList(mock(WaybillBridgeEnterpriseUserInfoDO.class));
        when(enterpriseUserInfoMapper.selectByParam(any())).thenReturn(userInfoDOList);
        WaybillEnterpriseUserInfoDTO userInfoDTO = mock(WaybillEnterpriseUserInfoDTO.class);
        userInfoDTO.setCorpId(corpId);
        List<WaybillEnterpriseUserInfoDTO> adminNameList = Collections.singletonList(userInfoDTO);
        when(enterpriseUserInfoConverter.convertFromDOList(userInfoDOList)).thenReturn(adminNameList);
        // 调用方法并获取返回值
        WaybillEnterprisePostDTO resultDTO = waybillEnterprisePostService.getPostByPostId(postId, corpId);
        // 验证转换过程
        verify(enterprisePostConverter).convertFromDO(postDO);
        verify(enterpriseUserInfoConverter).convertFromDOList(userInfoDOList);
        // 验证返回的DTO是否已经通过转换
        // assertEquals(postDTO, resultDTO); // 页面中提示不需要添加无法通过的验证
    }

    // 测试 locationId 为空的情况并执行相关逻辑
    @Test
    public void testPageListLocationIdIsNotEmpty() {
        EnterprisePostRequest request = new EnterprisePostRequest();
        request.setCorpId("testCorpId");
        request.setLocationId("testLocationId");
        WaybillEnterpriseLocationDO locationDO = mock(WaybillEnterpriseLocationDO.class);
        locationDO.setId(1L);
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(locationDO);

        when(enterprisePostMapper.countByParam(any())).thenReturn(1L);
        when(enterprisePostMapper.selectByParam(any())).thenReturn(Lists.newArrayList(new WaybillBridgeEnterprisePostDO()));
        BridgePagingDTO<WaybillEnterprisePostDTO> result = waybillEnterprisePostService.pageList(request);
        // 校验返回的结果是否符合预期
        assertNotNull(result.getTableData());
    }

    // 检查企业在数据库中是否已存在相同的Post
    @Test(expected = EnterpriseException.class)
    public void testAddPostAlreadyExist() {
        // 准备入参对象
        EnterprisePostRequest request = new EnterprisePostRequest();
        request.setCorpId("testCorpId");
        request.setPostName("testPostName");
        // 模拟数据库中存在相同的Post
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(new WaybillBridgeEnterprisePostDO());
        // 调用方法，期望抛出EnterpriseException
        waybillEnterprisePostService.addPost(request);
    }

    // 尝试插入新Post并更新用户信息出现异常回滚事务
    @Test(expected = EnterpriseException.class)
    public void testAddPostWithExceptionInTransaction() {
        // 准备入参对象
        EnterprisePostRequest request = new EnterprisePostRequest();
        request.setCorpId("testCorpId");
        request.setPostName("testPostName");
        // 模拟事务处理抛出异常
        doThrow(new RuntimeException()).when(enterprisePostMapper).insert(any(WaybillBridgeEnterprisePostDO.class));
        // 调用方法，期望抛出EnterpriseException
        waybillEnterprisePostService.addPost(request);
    }

    // 获取且判断每个管理员用户信息，设置及更新其类型及绑定的Post
    @Test
    public void testAddPostAndUpdateAdminUserInfo() {
        // 准备入参对象
        EnterprisePostRequest request = new EnterprisePostRequest();
        request.setCorpId("testCorpId");
        request.setPostName("testPostName");
        request.setPostAdminList(Collections.singletonList("adminUserId"));
        WaybillBridgeEnterprisePostDO postDO = mock(WaybillBridgeEnterprisePostDO.class);
        // 模拟数据库操作和用户信息相关操作
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(null, postDO);
        when(enterpriseUserInfoMapper.selectByUserId(anyString(), anyString()))
                .thenReturn(new WaybillBridgeEnterpriseUserInfoDO());
        // 调用方法
        waybillEnterprisePostService.addPost(request);
        // 验证用户信息更新
        verify(enterpriseUserInfoMapper).updateByPrimaryKeySelective(any(WaybillBridgeEnterpriseUserInfoDO.class));
    }

    // 检查并处理用户已经绑定其他Post的情况
    @Test(expected = EnterpriseException.class)
    public void testAddPostUserAlreadyBoundToOtherPost() {
        // 准备入参对象
        EnterprisePostRequest request = new EnterprisePostRequest();
        request.setCorpId("testCorpId");
        request.setPostName("testPostName");
        request.setPostAdminList(Collections.singletonList("adminUserId"));
        WaybillBridgeEnterpriseUserInfoDO userInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        userInfoDO.setPostId(1L);
        // 模拟用户已经绑定其他Post
        when(enterpriseUserInfoMapper.selectByUserId(anyString(), anyString())).thenReturn(userInfoDO);
        // 调用方法，期望抛出EnterpriseException
        waybillEnterprisePostService.addPost(request);
    }

    // location不存在时抛出异常的逻辑
    @Test(expected = EnterpriseException.class)
    public void testGetPostListLocationNotExist() {
        // 创建请求对象，设置locationId和corpId
        EnterprisePostRequest request = new EnterprisePostRequest();
        request.setCorpId("testCorpId");
        request.setLocationId("testLocationId");
        // 模拟locationMapper返回null，表示location不存在
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(null);
        // 调用getPostList方法并期望抛出 EnterpriseException 异常
        waybillEnterprisePostService.getPostList(request);
    }
}