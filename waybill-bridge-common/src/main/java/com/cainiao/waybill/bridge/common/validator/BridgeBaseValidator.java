package com.cainiao.waybill.bridge.common.validator;

import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.BaseError;
import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import org.apache.commons.lang.StringUtils;

import com.cainiao.waybill.bridge.common.exception.util.Exceptions;

/**
 * 打印工具基础校验类
 * Created by lichen.ll on 2017/1/5.
 */
public class BridgeBaseValidator {

    public static void validate ( boolean prediction , String msg ) throws BridgeValidationException {
        if(!prediction){
            throw Exceptions.newBridgeValidatorException(BaseError.PARAMETER_ILLEGAL.getErrorCode(),msg);
        }
    }

    /**
     * 校验clientInfoDTO
     * @param clientInfoDTO
     * @throws BridgeValidationException
     */
    public static void validateClientInfoDTO(ClientInfoDTO clientInfoDTO) throws BridgeValidationException {
        BridgeBaseValidator.validate(clientInfoDTO != null,"clientInfoDTO不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getAppName()), "clientInfoDTO.appName不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getOperator()), "clientInfoDTO.operator不能为空");
    }

    /**
     * 参数不为null，且大于0
     * @param parameter
     * @return
     */
    public static boolean longGtZero(Long parameter) {
        return objectNotNull(parameter) && parameter > 0 ;
    }

    /**
     * 参数不为null，且大于0
     * @param parameter
     * @return
     */
    public static boolean integerGtZero(Integer parameter) {
        return objectNotNull(parameter) && parameter > 0 ;
    }

    /**
     * 参数不为null，且有值
     * @param parameter
     * @return
     */
    public static boolean stringHasChar(String parameter) {
        return objectNotNull(parameter) && parameter.length() > 0 ;
    }

    /**
     * 参数不为null
     * @param parameter
     * @return
     */
    public static boolean objectNotNull(Object parameter) {
        return parameter != null ;
    }

}
