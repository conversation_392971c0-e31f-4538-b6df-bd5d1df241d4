package com.cainiao.waybill.bridge.enterprise;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cainiao.hyena.common.util.Maps2;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpCpConfigInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpFinanceSettleCpConfig;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpFinanceSettleCustomerAndCpConfig;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpPlatConfig;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpPlatConfigHelp;
import org.apache.commons.io.Charsets;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.nio.file.Files;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 单测基础类，勿删
 * Created By Thub IDEA插件
 *
 * <AUTHOR> zouping.fzp
 * @Classname BaseTest
 * @Description
 * @Date 2023/10/26 20:09
 * @Version 1.0
 */
public class BaseTest {
    protected static String readJson(String filePath) {
        try {
            File file = new File(BaseTest.class.getClassLoader()
                .getResource(filePath).getFile());
            return String.join("", Files.readAllLines(file.toPath()));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    protected static <T> void initSwitch(Class<T> tClass, String filePath)
        throws IllegalAccessException, NoSuchFieldException {
        String config = readJson(filePath);
        JSONObject jsonObject = JSONObject.parseObject(config);
        for (String key : jsonObject.keySet()) {
            Field field = tClass.getDeclaredField(key);
            Type genericType = field.getGenericType();

            Object t = jsonObject.getObject(key, genericType);
            field.setAccessible(true);
            field.set(BridgeSwitch.class, t);
        }
    }

//    @Test
    public void t() throws IOException {
        final Map<String, String> agent = Maps2.newHashMap("OPEN", "开放平台直连 OPEN", "CP", "电子面单对接 CP", "FHD", "风火递 FHD");
        agent.put("KD100", "快递100");
        agent.put("GG24H", "裹裹24小时 GG24H");
        agent.put("WHG", "无花果 WHG");

        final Map<String, String> cp = Maps2.newHashMap("YTO", "圆通 YTO", "SF", "顺丰 SF", "JDL", "京东 JDL");
        cp.put("STO", "申通 STO");
        cp.put("YUNDA", "韵达 YUNDA");
        cp.put("HTKY", "极兔 HTKY");
        cp.put("DBKD", "德邦 DBKD");
        cp.put("GUOGUO", "裹裹 GUOGUO");
        cp.put("ZTO", "中通 ZTO");

        final Map<Long, String> user = Maps2.newHashMap(183744L, "食人鱼 183744", 2529677229L, "大森hala 2529677229", 516377603L, "申通网络配送专家 516377603");
        user.put(65329090L, "韵达在线客服 65329090");
        user.put(920678872L, "上海龙邦速递 920678872");
        user.put(624768359L, "曹寅飞0310 624768359");
        user.put(2215760232955L, "风火递加 2215760232955");
        user.put(115872958L, "lunawang_520 115872958");
        user.put(2213106744056L, "无花果文化发展有限公司 2213106744056");
        final Map<String, PickUpPlatConfig> pickUpPlatConfigMap = JSONObject.parseObject(
                com.google.common.io.Files.readFirstLine(new File("/tmp/aa"), Charsets.UTF_8),
                new TypeReference<Map<String, PickUpPlatConfig>>(){}
        );

        final List<PickUpFinanceSettleCustomerAndCpConfig> financeSettleCustomerAndCpConfigs = JSONObject.parseObject(
                com.google.common.io.Files.readFirstLine(new File("/tmp/bb"), Charsets.UTF_8),
                new TypeReference<List<PickUpFinanceSettleCustomerAndCpConfig>>() {
                }
        );

        final Map<String, PickUpFinanceSettleCustomerAndCpConfig> collect = financeSettleCustomerAndCpConfigs.stream()
                .filter(e -> !e.getAppKey().equals("403145"))
//                .collect(Collectors.toMap(e -> e.getAppKey() + '-' + e.getOrderChannels(), e -> e));
                .collect(Collectors.toMap(e -> e.getAppKey(), e -> e));

        for (Map.Entry<String, PickUpPlatConfig> entry : pickUpPlatConfigMap.entrySet()) {
            final PickUpFinanceSettleCustomerAndCpConfig financeSettleCustomerAndCpConfig = collect.get(entry.getKey());
            if (financeSettleCustomerAndCpConfig == null) {
//                System.out.println("config null " + entry.getKey());
                continue;
            }

            for (PickUpCpConfigInfo pickUpCpConfigInfo : entry.getValue().getCpConfigList()) {
                PickUpFinanceSettleCpConfig settleCpConfig = PickUpPlatConfigHelp.settleCpConfig(pickUpCpConfigInfo.getAgent(), pickUpCpConfigInfo.getCpCode(), String.valueOf(pickUpCpConfigInfo.getBizType()), financeSettleCustomerAndCpConfig.getSettleCpConfigList());
                if(settleCpConfig == null){
                    settleCpConfig = PickUpPlatConfigHelp.settleCpConfig("*", pickUpCpConfigInfo.getCpCode(), String.valueOf(pickUpCpConfigInfo.getBizType()), financeSettleCustomerAndCpConfig.getSettleCpConfigList());
                }
                if(settleCpConfig == null){
                    settleCpConfig = PickUpPlatConfigHelp.settleCpConfig(pickUpCpConfigInfo.getAgent(), pickUpCpConfigInfo.getCpCode(), "*", financeSettleCustomerAndCpConfig.getSettleCpConfigList());
                }
                if(settleCpConfig == null){
                    settleCpConfig = PickUpPlatConfigHelp.settleCpConfig("*", pickUpCpConfigInfo.getCpCode(), "*", financeSettleCustomerAndCpConfig.getSettleCpConfigList());
                }
                if (settleCpConfig == null) {
//                    System.out.println("bill null " +
//                            entry.getKey() + " : " +
//                            entry.getValue().getName() + " : " +
//                            pickUpCpConfigInfo.getAgent() + " : " +
//                            pickUpCpConfigInfo.getCpCode() + " : " +
//                            pickUpCpConfigInfo.getBizType()
//                    );
                    continue;
                }
                System.out.println(entry.getKey() + ":" + entry.getValue().getName() + "|" +
                        agent.get(pickUpCpConfigInfo.getAgent()) + "|" +
                        cp.get(pickUpCpConfigInfo.getCpCode()) + "|" +
//                        pickUpCpConfigInfo.getBizType() + "|" +
//                        settleCpConfig.getAgent() + " : " +
//                        settleCpConfig.getCpCode() + " : " +
                        settleCpConfig.getCpBizType() + "|" +
                        user.getOrDefault(settleCpConfig.getUserId(), String.valueOf(settleCpConfig.getUserId())) + "|" +
                        settleCpConfig.getServiceNode()
                );
            }
//            System.out.println("------------------------------------------");
        }
    }

//    @Test
    public void tt() throws IOException {
        final Map<String, String> agent = Maps2.newHashMap("OPEN", "开放平台直连 OPEN", "CP", "电子面单对接 CP", "FHD", "风火递 FHD");
        agent.put("KD100", "快递100");
        agent.put("GG24H", "裹裹24小时 GG24H");
        agent.put("WHG", "无花果 WHG");

        final Map<String, String> cp = Maps2.newHashMap("YTO", "圆通 YTO", "SF", "顺丰 SF", "JDL", "京东 JDL");
        cp.put("STO", "申通 STO");
        cp.put("YUNDA", "韵达 YUNDA");
        cp.put("HTKY", "极兔 HTKY");
        cp.put("DBKD", "德邦 DBKD");
        cp.put("GUOGUO", "裹裹 GUOGUO");
        cp.put("ZTO", "中通 ZTO");

        final Map<Long, String> user = Maps2.newHashMap(183744L, "食人鱼 183744", 2529677229L, "大森hala 2529677229", 516377603L, "申通网络配送专家 516377603");
        user.put(65329090L, "韵达在线客服 65329090");
        user.put(920678872L, "上海龙邦速递 920678872");
        user.put(624768359L, "曹寅飞0310 624768359");
        user.put(2215760232955L, "风火递加 2215760232955");
        user.put(115872958L, "lunawang_520 115872958");
        user.put(2213106744056L, "无花果文化发展有限公司 2213106744056");
        final Map<String, PickUpPlatConfig> pickUpPlatConfigMap = JSONObject.parseObject(
                com.google.common.io.Files.readFirstLine(new File("/tmp/aa"), Charsets.UTF_8),
                new TypeReference<Map<String, PickUpPlatConfig>>(){}
        );

        final List<PickUpFinanceSettleCustomerAndCpConfig> financeSettleCustomerAndCpConfigs = JSONObject.parseObject(
                com.google.common.io.Files.readFirstLine(new File("/tmp/bb"), Charsets.UTF_8),
                new TypeReference<List<PickUpFinanceSettleCustomerAndCpConfig>>() {
                }
        );

        final Map<String, PickUpFinanceSettleCustomerAndCpConfig> collect = financeSettleCustomerAndCpConfigs.stream()
                .filter(e -> e.getAppKey().equals("403145"))
                .collect(Collectors.toMap(e -> e.getAppKey() + '-' + e.getOrderChannels(), e -> e));

        for (Map.Entry<String, PickUpPlatConfig> entry : pickUpPlatConfigMap.entrySet()) {
            if (!entry.getKey().startsWith("403145-")) {
                continue;
            }

            final PickUpFinanceSettleCustomerAndCpConfig financeSettleCustomerAndCpConfig = collect.get(entry.getKey());
            if (financeSettleCustomerAndCpConfig == null) {
//                System.out.println("config null " + entry.getKey());
                continue;
            }

            for (PickUpCpConfigInfo pickUpCpConfigInfo : entry.getValue().getCpConfigList()) {
                PickUpFinanceSettleCpConfig settleCpConfig = PickUpPlatConfigHelp.settleCpConfig(pickUpCpConfigInfo.getAgent(), pickUpCpConfigInfo.getCpCode(), String.valueOf(pickUpCpConfigInfo.getBizType()), financeSettleCustomerAndCpConfig.getSettleCpConfigList());
                if(settleCpConfig == null){
                    settleCpConfig = PickUpPlatConfigHelp.settleCpConfig("*", pickUpCpConfigInfo.getCpCode(), String.valueOf(pickUpCpConfigInfo.getBizType()), financeSettleCustomerAndCpConfig.getSettleCpConfigList());
                }
                if(settleCpConfig == null){
                    settleCpConfig = PickUpPlatConfigHelp.settleCpConfig(pickUpCpConfigInfo.getAgent(), pickUpCpConfigInfo.getCpCode(), "*", financeSettleCustomerAndCpConfig.getSettleCpConfigList());
                }
                if(settleCpConfig == null){
                    settleCpConfig = PickUpPlatConfigHelp.settleCpConfig("*", pickUpCpConfigInfo.getCpCode(), "*", financeSettleCustomerAndCpConfig.getSettleCpConfigList());
                }
                if (settleCpConfig == null) {
//                    System.out.println("bill null " +
//                            entry.getKey() + " : " +
//                            entry.getValue().getName() + " : " +
//                            pickUpCpConfigInfo.getAgent() + " : " +
//                            pickUpCpConfigInfo.getCpCode() + " : " +
//                            pickUpCpConfigInfo.getBizType()
//                    );
                    continue;
                }
                System.out.println(entry.getKey() + ":" + entry.getValue().getName() + "|" +
                                agent.get(pickUpCpConfigInfo.getAgent()) + "|" +
                                cp.get(pickUpCpConfigInfo.getCpCode()) + "|" +
//                        pickUpCpConfigInfo.getBizType() + "|" +
//                        settleCpConfig.getAgent() + " : " +
//                        settleCpConfig.getCpCode() + " : " +
                                settleCpConfig.getCpBizType() + "|" +
                                user.getOrDefault(settleCpConfig.getUserId(), String.valueOf(settleCpConfig.getUserId())) + "|" +
                                settleCpConfig.getServiceNode()
                );
            }
//            System.out.println("------------------------------------------");
        }
    }


}
