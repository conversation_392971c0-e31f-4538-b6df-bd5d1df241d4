package com.cainiao.waybill.bridge.innersupport.ad.util;

import com.cainiao.waybill.bridge.innersupport.ad.exception.AdBusinessException;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 事务处理
 */
public class TransactionExecutor {

    public interface ExecutorAction<T> {
        /**
         * 事务处理
         *
         * @param status
         * @return
         */
        T doExecute(TransactionStatus status) throws AdBusinessException;
    }

    /**
     * 统一处理manager层对dao调用的异常处理
     *
     * @param transactionTemplate 如果这个参会为null，则表示不用事务，否则使用事务
     * @param action 回调接口
     * @return
     * @throws Exception
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public static <T> T execute(TransactionTemplate transactionTemplate, final ExecutorAction<T> action) throws AdBusinessException {
        T result = null;
        if (transactionTemplate != null) {
            result = (T) transactionTemplate.execute(new TransactionCallback() {

                @Override
                public Object doInTransaction(TransactionStatus status) {
                    try {
                        return action.doExecute(status);
                    } catch (Throwable t) {
                        return t;
                    }
                }
            });
            if (result instanceof AdBusinessException) {
                throw (AdBusinessException) result;
            }
            if (result instanceof Throwable) {
                throw new AdBusinessException("UNKNOWN_EXCEPTION", "unknown exception caught", (Throwable) result);
            }
            return result;
        } else {
            try {
                return action.doExecute(null);
            } catch (AdBusinessException e) {
                throw e;
            } catch (Throwable t) {
                throw new AdBusinessException("UNKNOWN_EXCEPTION", "unknown exception caught", t);
            }
        }
    }

    /**
     * 执行事物,如果抛异常回滚事物
     *
     * @param transactionTemplate
     * @param action
     * @param <T>
     * @return
     */
    public static <T> T executeWithRollback(TransactionTemplate transactionTemplate, final ExecutorAction<T> action) throws AdBusinessException  {
        T result;
        if (transactionTemplate!= null) {
            result = (T) transactionTemplate.execute(new TransactionCallback() {

                @Override
                public Object doInTransaction(TransactionStatus status) {
                    try {
                        return action.doExecute(status);
                    } catch (Throwable t) {
                        status.setRollbackOnly();
                        return t;
                    }
                }
            });
            if (result instanceof AdBusinessException) {
                throw (AdBusinessException) result;
            }
            if (result instanceof Throwable) {
                throw new AdBusinessException("UNKNOWN_EXCEPTION", "unknown exception caught", (Throwable) result);
            }
            return result;
        } else {
            try {
                return action.doExecute(null);
            } catch (AdBusinessException e) {
                throw e;
            } catch (Throwable t) {
                throw new AdBusinessException("UNKNOWN_EXCEPTION", "unknown exception caught", t);
            }
        }
    }
}
