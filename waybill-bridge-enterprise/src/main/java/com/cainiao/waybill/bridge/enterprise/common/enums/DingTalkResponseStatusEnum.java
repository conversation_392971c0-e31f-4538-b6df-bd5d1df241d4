package com.cainiao.waybill.bridge.enterprise.common.enums;

public enum DingTalkResponseStatusEnum implements CodeDescribe {

    /**
     * 成功
     */
    invalidSuiteKey("invalidSuiteKey", "suitekey不合法"),
    /**
     * 参数非法
     */
    invalidSuiteTicket("invalidSuiteTicket", "suiteTicket无效"),
    /**
     * 参数非法
     */
    invalidAuthInfo("invalidAuthInfo", "授权关系不存在");


    final String code;
    final String describe;

    DingTalkResponseStatusEnum(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    @Override
    public String code() {
        return code;
    }

    @Override
    public String describe() {
        return describe;
    }


}
