package com.cainiao.waybill.bridge.enterprise.administrators.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/5/9
 **/
@Data
public class CorpDeptResponse {

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 父部门id
     */
    private Long parentId;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门群已经创建后，有新人加入部门是否会自动加入该群：
     * true：会自动入群
     * false：不会
     */
    private Boolean autoAddUser;

    /**
     * 是否同步创建一个关联此部门的企业群：
     * true：创建
     * false：不创建
     */
    private Boolean createDeptGroup;
}
