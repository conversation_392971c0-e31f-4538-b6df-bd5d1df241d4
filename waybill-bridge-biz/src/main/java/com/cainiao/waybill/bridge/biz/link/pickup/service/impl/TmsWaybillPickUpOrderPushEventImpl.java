package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.api.open.ReceiveService;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PUSH_ORDER_EVENT.WaybillPushOrderEventRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PUSH_ORDER_EVENT.PushOrderEventResponse;

/**
 * 订单事件空实现，用于测试
 *
 * @author: zouping.fzp
 * @date: 2022-05-06 10:41
 **/
public class TmsWaybillPickUpOrderPushEventImpl implements ReceiveService<WaybillPushOrderEventRequest, PushOrderEventResponse> {


    @Override
    public PushOrderEventResponse invoke(ReceiveParams<WaybillPushOrderEventRequest> receiveParams) {

        PushOrderEventResponse response = new PushOrderEventResponse();
        response.setSuccess(true);
        return response;
    }
}
