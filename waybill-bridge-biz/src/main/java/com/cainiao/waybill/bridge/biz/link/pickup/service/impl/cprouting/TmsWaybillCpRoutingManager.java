package com.cainiao.waybill.bridge.biz.link.pickup.service.impl.cprouting;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;

import com.cainiao.waybill.bridge.biz.middleware.BridgeRouterSwitch;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.middleware.EagleEyeCallbackTask;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Cp;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpCpEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpDetailBizTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.dto.CpRouteRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.create.pre.PickUpCreatePreTdAppointTimeSlotDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.RouteReachableResult;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.RoutingInfo;
import com.cainiao.waybill.bridge.biz.pickup.routing.dto.RouteBaseRequest;
import com.cainiao.waybill.bridge.biz.pickup.routing.manager.RoutingManager;
import com.cainiao.waybill.bridge.biz.pickup.routing.manager.RoutingReachableManager;
import com.cainiao.waybill.bridge.biz.router.DTO.BridgeRouterRequest;
import com.cainiao.waybill.bridge.biz.router.DTO.BridgeRouterResult;
import com.cainiao.waybill.bridge.biz.router.DTO.CpInfo;
import com.cainiao.waybill.bridge.biz.router.core.BridgeRouterCoreService;
import com.cainiao.waybill.bridge.biz.utils.AddressUtil;
import com.cainiao.waybill.bridge.biz.utils.BridgeAddressUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpPlatConfigHelp;
import com.cainiao.waybill.bridge.common.config.diamond.CpRoutingDiamondConfig;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.util.BridgeDateUtil;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.common.util.MapUtil;
import com.cainiao.waybill.bridge.common.util.StrUtil;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpDetailMapper;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.eagleeye.EagleEye;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zouping.fzp
 * @Classname TmsWaybillCpRoutingManager
 * @Description
 * @Date 2022/8/2 2:00 下午
 * @Version 1.0
 */
@Component
public class TmsWaybillCpRoutingManager {

    @Resource
    private RoutingReachableManager routingReachableManager;

    @Resource
    private RoutingManager routingManager;

    @Resource
    private WaybillPickUpCpRoutingCalculationHelper routingCalculationHelper;

    @Resource
    private BridgeRouterCoreService routerCoreService;

    @Resource
    WaybillPickUpDetailMapper waybillPickUpDetailMapper;

    ExecutorService executorService = new ThreadPoolExecutor(4, 150,
        60L, TimeUnit.SECONDS,
        new SynchronousQueue<Runnable>());

    public CpRoutingContext routing(CpRouteRequest cpRouteRequest) throws Throwable {

        // 黑名单地址直接拦截
        blackAddressIntercept(cpRouteRequest.getSendAddress());

        // 构造可用cp列表
        CpRoutingContext context = buildContext(cpRouteRequest.getCpCode(), cpRouteRequest.getFromAppKey(),
            cpRouteRequest.getBizType());

        // 可用cp列表排序
        context.getAvailableCpList().sort(Comparator.comparingInt(PickUpCpEnum::orderOf));

        // 计算揽件网点，用于黑名单等判断
        try {
            String branchCode = getBranchCode(Cp.YTO.name(), cpRouteRequest.getSendAddress());
            cpRouteRequest.setOrgCode(branchCode);
            cpRouteRequest.setCpCodeForOrgCode(Cp.YTO.name());
            context.setAcceptBranchCode(branchCode);
        } catch (Throwable throwable) {
            PickUpLogUtil.errLog("", "query_yto_branch_code_error", "query_yto_branch_code_error", "查询圆通网点失败",
                throwable);
        }

        String routingCpCode = null;

        boolean hitRouter = false;

        if(BridgeRouterSwitch.grayResCodeSenderAddressList.containsKey(cpRouteRequest.getResCode())){
            List<String> grayAddressList = BridgeRouterSwitch.grayResCodeSenderAddressList.get(cpRouteRequest.getResCode());
            for (String address : ListUtil.non(grayAddressList)) {
                if(AddressUtil.address(cpRouteRequest.getSendAddress()).startsWith(address)){
                    hitRouter = true;
                    break;
                }
            }
        }

        if(StringUtils.isNotBlank(cpRouteRequest.getCpCode())) {
            // 指定cp
            routingCpCode = cpRouteRequest.getCpCode();
        } else if(StringUtils.isBlank(cpRouteRequest.getCpCode()) && hitRouter){
            BridgeRouterRequest routerRequest =  BridgeRouterRequest.builder()
                .availableCpList(buildRouterAvailableCpList(cpRouteRequest, context))
                .bizType(cpRouteRequest.getBizType())
                .fromAppKey(cpRouteRequest.getFromAppKey())
                .sendName(cpRouteRequest.getSendName())
                .sendMobile(cpRouteRequest.getSendMobile())
                .sendAddress(cpRouteRequest.getSendAddress())
                .consignName(cpRouteRequest.getConsignName())
                .consignMobile(cpRouteRequest.getConsignMobile())
                .consignAddress(cpRouteRequest.getConsignAddress())
                .appointGotStartTime(cpRouteRequest.getAppointGotStartTime())
                .appointGotEndTime(cpRouteRequest.getAppointGotEndTime())
                .sendMobile(cpRouteRequest.getSendMobile())
                .resCode(cpRouteRequest.getResCode())
                .orgCode(context.getAcceptBranchCode())
                .outerOrderCode(cpRouteRequest.getOuterOrderCode())
                .build();

            BridgeRouterResult routerResult = new BridgeRouterResult();
            try{
                routerResult = routerCoreService.router(routerRequest);
            }catch (Throwable throwable){
                PickUpLogUtil.errLog("", "router_exception", "router_exception",
                    "路由异常: " + JSON.toJSONString(cpRouteRequest), throwable);
            }
            if(CollectionUtils.isNotEmpty(routerResult.getAvailableCpList())){
                routingCpCode = routerResult.getAvailableCpList().get(0).getCpCode();
                context.setRouterStrategyId(routerResult.getRouterStrategyId());
            }
            // 未命中策略 则继续按照之前的逻辑路由
            if(StringUtils.isBlank(routerResult.getRouterStrategyId())){
                // 按优先级计算路由cp
                routingCpCode = routing(context, cpRouteRequest);
            }else{
                context.setAvailableCpList(ListUtil.non(routerResult.getAvailableCpList()).stream().map(CpInfo::getCpCode).collect(Collectors.toList()));
            }
        }else{
            // 按优先级计算路由cp
            routingCpCode = routing(context, cpRouteRequest);
        }

        // 验证可达性
        checkReachableBuildResult(cpRouteRequest, context, routingCpCode);
        if (StringUtils.isNotBlank(context.getFirstCp())) {
            return context;
        }

        // 如果优先cp不可达，则兜底
        if (RandomUtils.nextInt(0, 100) > BridgeSwitch.guoguoCantReachUseDefaultGray) {
            for (String cpCode : context.getAvailableCpList()) {
                if (StringUtils.isBlank(cpCode) || cpCode.equals(routingCpCode)) {
                    continue;
                }

                PickUpLogUtil.info(
                    "check_cp cpCode: " + cpCode + "," + BridgeSwitch.orderCantReachTryfGuoguo + ", " + routingCpCode);

                // 订单不可达时
                if (!BridgeSwitch.orderCantReachTryfGuoguo && Cp.GUOGUO.name().equals(cpCode)) {
                    continue;
                }

                // 验证可达性
                checkReachableBuildResult(cpRouteRequest, context, cpCode);
                if (StringUtils.isNotBlank(context.getFirstCp())) {
                    return context;
                }
            }
        }

        return context;
    }

    private List<CpInfo> buildRouterAvailableCpList(CpRouteRequest cpRouteRequest, CpRoutingContext context){
        List<CpInfo> list = new ArrayList<>();
        for (String cpCode : ListUtil.non(context.getAvailableCpList())) {

            CpInfo cpInfo = new CpInfo();
            cpInfo.setCpCode(cpCode);
            String agent = PickUpPlatConfigHelp.getAgentByAppKeyAndCpCode(cpRouteRequest.getFromAppKey(), cpCode);
            cpInfo.setAgent(agent);
            list.add(cpInfo);
        }
        return list;
    }

    private void checkReachableBuildResult(CpRouteRequest cpRouteRequest, CpRoutingContext context,
        String routingCpCode) throws Exception {
        if (StringUtils.isBlank(routingCpCode)) {
            return;
        }

        // 判断路由cp是否可达
        RouteReachableResult routeReachableResult = checkReach(cpRouteRequest, routingCpCode);
        context.getCpReachResult().put(routingCpCode, routeReachableResult.isReachable());

        // 是否命中需要判断其他cp可达
        boolean hit = checkOtherCpJudge(cpRouteRequest, routeReachableResult, routingCpCode);
        if (!hit) {
            buildResult(cpRouteRequest, routeReachableResult, context, routingCpCode, true);
            return;
        }

        // 额外判断其余cp是否可达，达到比例才认为可达
        boolean reachableRateMatch = checkOtherCpReachable(cpRouteRequest, context, routingCpCode);

        buildResult(cpRouteRequest, routeReachableResult, context, routingCpCode, reachableRateMatch);
    }

    private boolean checkOtherCpJudge(CpRouteRequest cpRouteRequest, RouteReachableResult routeReachableResult,
        String routingCpCode) {
        if (MapUtils.isEmpty(BridgeSwitch.cpReachableCheckList)
            || CollectionUtils.isEmpty(BridgeSwitch.cpReachableCheckList.get(routingCpCode))
            || routeReachableResult == null) {
            return false;
        }

        if (MapUtils.isEmpty(BridgeSwitch.cpReachableCheckGrayRate)) {
            return false;
        }

        // 判断是否命中灰度
        boolean hit = false;
        String sendAddress = BridgeAddressUtil.buildAddress(cpRouteRequest.getSendAddress());
        for (Entry<String, Integer> stringIntegerEntry : BridgeSwitch.cpReachableCheckGrayRate.entrySet()) {
            boolean match = BridgeAddressUtil.judgeHit(sendAddress, stringIntegerEntry.getKey());
            if (match && RandomUtils.nextInt(0, 100) <= stringIntegerEntry.getValue()) {
                hit = true;
            }
        }
        return hit;
    }

    private void buildResult(CpRouteRequest cpRouteRequest, RouteReachableResult routeReachableResult, CpRoutingContext context, String routingCpCode,
        boolean reachableRateMatch) {

        // itemVersion为裹裹必须参数，为空则代表不可达
        if(Cp.GUOGUO.name().equals(routingCpCode) && routeReachableResult.getItemVersion() == null){
            PickUpLogUtil.info("cp_guoguo_item_version_empty, request: " + JSON.toJSONString(cpRouteRequest));
            return;
        }

        if ((!BridgeSwitch.notCheckPreQueryResultAppKey.contains(cpRouteRequest.getFromAppKey()))
            && (routeReachableResult == null || !routeReachableResult.isReachable() || !reachableRateMatch)) {
            return;
        }
        context.setFirstCp(routingCpCode);
        context.setItemVersion(routeReachableResult.getItemVersion());
        context.setAppointTimeSlotDTO(routeReachableResult.getAppointTime());
        context.setAcceptBranchCode(routeReachableResult.getBranchCode());
    }

    public void blackAddressIntercept(AddressDTO addressDTO) throws BridgeBaseException {
        String addressDetail = StrUtil.non(addressDTO.getProvinceName()) + StrUtil.non(addressDTO.getCityName())
            + StrUtil.non(addressDTO.getAreaName()) + StrUtil.non(addressDTO.getTownName()) + StrUtil.non(
            addressDTO.getAddressDetail());
        if (StringUtils.isBlank(addressDetail)) {
            return;
        }
        List<String> addressList = BridgeSwitch.blackAddressNotAccept;
        if (CollectionUtils.isNotEmpty(addressList)) {
            for (String address : addressList) {
                if (StringUtils.isNotBlank(address) && addressDetail.startsWith(address)) {
                    // 黑名单网点
                    PickUpLogUtil.errLog("", "routing_address_black", "routing_address_black", "黑名单地址不接单");
                    throw new BridgeBaseException(PickUpConstants.Error.ORDER_UNREACHABLE.getErrorCode(),
                        PickUpConstants.Error.ORDER_UNREACHABLE.getErrorMsg());
                }
            }
        }
    }

    private String routing(CpRoutingContext context, CpRouteRequest cpRouteRequest) throws Throwable {

        // 计算当天有没有下过订单，如果有下过则使用之前的cp
        String cpCode = queryTodayUsedCpCodeByMobile(cpRouteRequest.getSendMobile(),
            cpRouteRequest.getAppointGotStartTime(), cpRouteRequest.getBizType());
        // 如果没有可用的cp则清空，换其它cp
        if(!context.getAvailableCpList().contains(cpCode)){
            cpCode = null;
        }

        // 计算收发件线路路由到cp
        if (StringUtils.isBlank(cpCode)) {
            cpCode = routingCpWithPath(AddressUtil.addressFromFormat(cpRouteRequest.getSendAddress().getAddressFormat()),
                AddressUtil.addressFromFormat(cpRouteRequest.getConsignAddress().getAddressFormat()));
        }

        // 计算收件地址开头
        if (StringUtils.isBlank(cpCode)) {
            cpCode = routingCpWithReceiveAddressStart(cpRouteRequest.getConsignAddress().getAddressFormat());
        }

        // 优先计算省到省
        if (StringUtils.isBlank(cpCode)) {
            cpCode = computeCpWithProvinceToProvince(cpRouteRequest.getSendAddress().getProvinceName(),
                cpRouteRequest.getConsignAddress().getProvinceName());
        }

        // 计算发件地址开头转到裹裹
        if (StringUtils.isBlank(cpCode)) {
            cpCode = computeCpWithSendAddressStart(cpRouteRequest.getSendAddress().getAddressFormat());
        }

        // 计算发件地址开头
        if (StringUtils.isBlank(cpCode)) {
            cpCode = routingCpWithSendAddressStart(cpRouteRequest.getSendAddress().getAddressFormat());
        }

        // 计算发件省
        if (StringUtils.isBlank(cpCode)) {
            cpCode = computeCpWithPro(cpRouteRequest.getSendAddress().getProvinceName());
        }

        // 黑名单手机号,直接返回对应cp
        if (StringUtils.isBlank(cpCode)) {
            cpCode = cpRoutingBlackMobile(cpRouteRequest.getSendMobile());
        }

        // 黑名单手机号，未配置指标计算规则后的黑名单手机号
        if (StringUtils.isBlank(cpCode)) {
            cpCode = computeCpWithSendMobile(cpRouteRequest.getSendMobile());
        }

        // 黑名单网点，未配置指标计算规则后根据黑名单网点
        if (StringUtils.isBlank(cpCode) && StringUtils.isNotBlank(cpRouteRequest.getOrgCode())) {

            if (CpRoutingDiamondConfig.isBlockBranch(cpRouteRequest.getCpCodeForOrgCode(), cpRouteRequest.getOrgCode(),
                cpRouteRequest.getSendAddress().getProvinceName(),
                cpRouteRequest.getConsignAddress().getProvinceName())) {
                // 黑名单网点
                PickUpLogUtil.errLog("", "routing_cp_black_branch", "routing_cp_black_branch", "黑名单网点");
                throw new BridgeBaseException(PickUpConstants.Error.ORDER_UNREACHABLE.getErrorCode(),
                    PickUpConstants.Error.ORDER_UNREACHABLE.getErrorMsg());
            }

            // 根据网点和路线获取可切换的cp，如果没有配置则认为可达
            List<String> switchCpList = CpRoutingDiamondConfig.isBlackBranchAndPath(
                cpRouteRequest.getCpCodeForOrgCode(), cpRouteRequest.getOrgCode(),
                cpRouteRequest.getSendAddress().getProvinceName(),
                cpRouteRequest.getConsignAddress().getProvinceName());
            if (CollectionUtils.isNotEmpty(switchCpList)) {
                cpCode = switchCpList.get(0);
            }
        }

        // 根据指标计算
        if (StringUtils.isBlank(cpCode)) {
            try {
                cpCode = routingCalculationHelper.calculation(cpRouteRequest, context.getAvailableCpList());
            } catch (Throwable throwable) {
                PickUpLogUtil.errLog("", "routing_cp_indicator_error", "routing_cp_indicator_error", "cp根据指标路由异常" + JSON
                    .toJSONString(cpRouteRequest), throwable);
            }
        }

        // cp为空或者该cp不可用，则走默认逻辑
        if (StringUtils.isBlank(cpCode) || !context.getAvailableCpList().contains(cpCode)) {
            return null;
        }
        return cpCode;
    }

    private boolean checkOtherCpReachable(CpRouteRequest cpRouteRequest, CpRoutingContext context, String cpCode)
        throws InterruptedException, java.util.concurrent.ExecutionException {
        List<String> cpList = BridgeSwitch.cpReachableCheckList.get(cpCode);

        Map<String, Future<RouteReachableResult>> futureMap = Maps.newHashMap();
        for (String reachCheckCpCode : cpList) {
            if (context.getCpReachResult().get(reachCheckCpCode) != null) {
                continue;
            }
            final Object rpcContext = EagleEye.currentRpcContext();
            Future<RouteReachableResult> future = executorService.submit(
                new EagleEyeCallbackTask<RouteReachableResult>(rpcContext) {
                    @Override
                    public RouteReachableResult callback() {
                        return checkReach(cpRouteRequest, reachCheckCpCode);
                    }
                });
            //Future<RouteReachableResult> future = executorService.submit(() -> checkReach(cpRouteRequest,
            // reachCheckCpCode));
            futureMap.put(reachCheckCpCode, future);
        }

        int count = 0;
        for (String reachCheckCpCode : cpList) {
            if (Boolean.TRUE.equals(context.getCpReachResult().get(reachCheckCpCode))) {
                count++;
                continue;
            }
            boolean reachResult = false;

            Future<RouteReachableResult> future = futureMap.get(reachCheckCpCode);
            if (future == null) {
                continue;
            }
            try {
                reachResult = future.get().isReachable();
            } catch (Throwable throwable) {
                PickUpLogUtil.errLog("", "future_get_exception", "future_get_exception", "可达查询异常", throwable);
            }

            // 记录可达结果
            context.getCpReachResult().put(reachCheckCpCode, reachResult);
            if (reachResult) {
                count++;
            }
        }

        // 验证可达数量是否满足
        return count >= BridgeSwitch.otherCpReachableNum.getOrDefault(cpCode, 1);
    }

    private RouteReachableResult checkReach(CpRouteRequest cpRouteRequest, String cpCode) {
        RouteBaseRequest routeBaseRequest = new RouteBaseRequest();
        routeBaseRequest.setSendName(cpRouteRequest.getSendName());
        routeBaseRequest.setSendMobile(cpRouteRequest.getSendMobile());
        routeBaseRequest.setSendAddress(cpRouteRequest.getSendAddress());
        routeBaseRequest.setReceiveName(cpRouteRequest.getConsignName());
        routeBaseRequest.setReceiveMobile(cpRouteRequest.getConsignMobile());
        routeBaseRequest.setReceiveAddress(cpRouteRequest.getConsignAddress());
        routeBaseRequest.setCpCode(cpCode);
        routeBaseRequest.setBizType(cpRouteRequest.getBizType());
        routeBaseRequest.setAppointGotStartTime(cpRouteRequest.getAppointGotStartTime());
        routeBaseRequest.setAppointGotEndTime(cpRouteRequest.getAppointGotEndTime());
        routeBaseRequest.setResCode(cpRouteRequest.getResCode());
        routeBaseRequest.setOuterOrderCode(cpRouteRequest.getOuterOrderCode());

        RouteReachableResult routeReachableResult = new RouteReachableResult();
        try {
            routeReachableResult = routingReachableManager.routingReachable(routeBaseRequest);
        } catch (Exception exception) {
            PickUpLogUtil.errLog("", "check_reach_exception", "check_reach_exception: " + cpCode,
                "校验可达异常", exception);
        }

        return routeReachableResult;
    }

    public CpRoutingContext buildContext(String cpCode, String fromAppKey, Integer bizType) {
        CpRoutingContext cpRoutingContext = new CpRoutingContext();
        List<String> cpList = Lists.newArrayList();
        if (StringUtils.isNotBlank(cpCode)) {

            //  判断菜鸟本身是否支持
            boolean available = BridgeSwitch.onlineAvailableCp.contains(cpCode);

            //  判断平台是否支持
            List<String> supportCpCodeList = BridgeSwitch.fromAppKeySupportCp.get(fromAppKey);
            boolean platSupport = CollectionUtils.isNotEmpty(supportCpCodeList) && supportCpCodeList.contains(cpCode);
            if (available && platSupport) {
                cpList.add(cpCode);
            }

        } else {
            Set<String> supportCpCodeList = BridgeSwitch.appKeySpecialCpList.get(fromAppKey);
            if (CollectionUtils.isEmpty(supportCpCodeList)) {
                supportCpCodeList = BridgeSwitch.onlineAvailableCp;
            }
            cpList.addAll(supportCpCodeList);
        }

        // 过滤支持按时配送的cp
        cpList = filterAppoint(cpList, bizType);

        cpRoutingContext.setAvailableCpList(cpList);
        return cpRoutingContext;
    }

    public List<String> filterAppoint(List<String> availableCpList, Integer bizType) {
        if (BridgeSwitch.openAppointOrderSingleRoute) {
            //  预约件模式：目前只有韵达可完全支持预约件，但不确定韵达在有预约件订单时能否使用。所以还是需要用圆通兜底
            if (Objects.equals(bizType, PickUpDetailBizTypeEnum.APPOINT.getValue())) {
                //  线上环境可完全支持预约件的CP（即正向下单与逆向修改预约时间都可以满足）
                List<String> cpList = availableCpList.stream().filter(
                    BridgeSwitch.onlineAppointAvailableCp::contains).collect(
                    Collectors.toList());
                return cpList;
            }
        }
        return availableCpList;
    }

    private String queryTodayUsedCpCodeByMobile(String mobile, Date appointDate, Integer bizType) {
        Date date = new Date();
        if (appointDate != null && Objects.equals(PickUpDetailBizTypeEnum.APPOINT.getValue(), bizType)) {
            date = appointDate;
        }
        Date startDate = BridgeDateUtil.dateToStartTime(date);
        Date endDate = BridgeDateUtil.dateToEndTime(date);

        return waybillPickUpDetailMapper.selectMobileDateUsedCp(startDate, endDate, mobile);
    }

    @Data
    static class ReachableResult {

        private boolean available = false;

        private boolean blackBranch = false;

        private boolean switchCp = false;

        private List<String> switchToCpList = Lists.newArrayList();

        /**
         * 裹裹预查询版本号
         */
        private Integer itemVersion;

        /**
         * 预约时间段
         */
        private PickUpCreatePreTdAppointTimeSlotDTO appointTimeSlotDTO;

        /**
         * 接单网点
         */
        private String acceptBranchCode;

    }

    /**
     * 获取接单的网点code
     */
    private String getBranchCode(String cpCode, AddressDTO senderAddress) throws Throwable {
        // 裹裹无法获取网点
        if (Cp.GUOGUO.name().equals(cpCode)) {
            return null;
        }
        //  调用分拣码接口，获取接单网点
        RouteBaseRequest routeBaseRequest = new RouteBaseRequest();
        routeBaseRequest.setCpCode(cpCode);
        //  都用寄件地址去算
        routeBaseRequest.setReceiveAddress(senderAddress);
        routeBaseRequest.setSendAddress(senderAddress);

        RoutingInfo routingInfo = routingManager.calculateRoutingInfo(routeBaseRequest);
        return routingInfo.getReceiveBranchCode();
    }

    /**
     * 根据BridgeSwitch.orderCpRouteInfo配置的各省的比例，计算出当前订单给哪一个CP
     */
    private String computeCpWithPro(String provinceName) {
        Map<String, Map<String, Integer>> orderCpRouteInfo = BridgeSwitch.newOrderCpRouteInfo;
        Map<String, Integer> cpAndScale = orderCpRouteInfo.get(provinceName);
        if (org.springframework.util.CollectionUtils.isEmpty(cpAndScale)) {
            return null;
        }

        //  将cpCode与概率值包装为一个实体，再根据实体的概率值升序排序
        List<CpScale> cpScaleList = new ArrayList<>(cpAndScale.size());
        for (Map.Entry<String, Integer> entry : cpAndScale.entrySet()) {
            CpScale item = new CpScale(entry.getKey(), entry.getValue());
            cpScaleList.add(item);
        }
        cpScaleList.sort(Comparator.comparing(o -> o.scale));

        Random random = new Random();
        int randomScale = random.nextInt(100);

        //  计算应该使用哪一个CP
        int sum = 0;
        String cpCode = null;
        for (CpScale cpScale : cpScaleList) {
            sum += cpScale.scale;
            if (sum >= randomScale) {
                cpCode = cpScale.getCpCode();
                break;
            }
        }

        return cpCode;
    }

    /**
     * 根据BridgeSwitch.orderCpRouteInfo配置的各省的比例，计算出当前订单给哪一个CP
     */
    private String computeCpWithProvinceToProvince(String senderProvince, String receiveProvince) {
        Map<String, String> cityToCityCpRouteInfo = BridgeSwitch.provinceToProvinceCpRouteInfo;
        return cityToCityCpRouteInfo.get(senderProvince + "-" + receiveProvince);
    }

    private String computeCpWithSendMobile(String sendMobile) {
        if (StringUtils.isBlank(sendMobile)) {
            return null;
        }
        List<String> list = BridgeSwitch.mobileSwitchToGuoguo;
        if (ListUtil.non(list).contains(sendMobile)) {
            return Cp.GUOGUO.name();
        }
        return null;
    }

    private String cpRoutingBlackMobile(String sendMobile) {
        if (StringUtils.isBlank(sendMobile)) {
            return null;
        }
        Map<String, String> map = BridgeSwitch.cpRoutingBlackMobile;
        if (MapUtils.isNotEmpty(map) && map.containsKey(sendMobile)) {
            return map.get(sendMobile);
        }
        return null;
    }

    private String computeCpWithSendAddressStart(String sendAddress) {
        if (StringUtils.isBlank(sendAddress)) {
            return null;
        }
        List<String> list = BridgeSwitch.addressSwitchToGuoguo;
        for (String str : ListUtil.non(list)) {
            if (StringUtils.isNotBlank(str) && sendAddress.startsWith(str)) {
                return Cp.GUOGUO.name();
            }
        }
        return null;
    }

    private String routingCpWithSendAddressStart(String sendAddress) {
        if (StringUtils.isBlank(sendAddress)) {
            return null;
        }
        for (Entry<String, String> entry : MapUtil.entrySet(BridgeSwitch.senderAddressRoutingToCp)) {
            if (StringUtils.isNotBlank(entry.getKey()) && sendAddress.startsWith(entry.getKey())) {
                return entry.getValue();
            }
        }
        return null;
    }

    private String routingCpWithPath(String sendAddress, String receiverAddress) {
        if (StringUtils.isBlank(sendAddress) || StringUtils.isBlank(receiverAddress)) {
            return null;
        }
        for (Entry<String, Map<String, String>> entry : MapUtil.entrySet(BridgeSwitch.senderToReceiverPathRoutingCp)) {
            if (StringUtils.isBlank(entry.getKey())){
                continue;
            }
            if (!sendAddress.startsWith(entry.getKey()) && !StringUtils.equals(entry.getKey(), "全国")) {
                continue;
            }
            for (Entry<String, String> subEntry : MapUtil.entrySet(entry.getValue())) {
                if (StringUtils.isBlank(subEntry.getKey())){
                    continue;
                }
                if (!receiverAddress.startsWith(subEntry.getKey()) && !StringUtils.equals(subEntry.getKey(), "全国")) {
                    continue;
                }
                return  subEntry.getValue();
            }
        }
        return null;
    }

    private String routingCpWithReceiveAddressStart(String address) {
        if (StringUtils.isBlank(address)) {
            return null;
        }
        for (Entry<String, String> entry : MapUtil.entrySet(BridgeSwitch.receiveAddressRoutingToCp)) {
            if (StringUtils.isNotBlank(entry.getKey()) && address.startsWith(entry.getKey())) {
                return entry.getValue();
            }
        }
        return null;
    }

    /**
     * 辅助实体，用于通过scale排序
     */
    static class CpScale {
        private String cpCode;
        private Integer scale;

        public CpScale(String cpCode, Integer scale) {
            this.cpCode = cpCode;
            this.scale = scale;
        }

        @Override
        public String toString() {
            return "CpScale{" +
                "cpCode='" + cpCode + '\'' +
                ", scale=" + scale +
                '}';
        }

        public CpScale() {
        }

        public String getCpCode() {
            return cpCode;
        }

        public void setCpCode(String cpCode) {
            this.cpCode = cpCode;
        }

        public Integer getScale() {
            return scale;
        }

        public void setScale(Integer scale) {
            this.scale = scale;
        }
    }

}
