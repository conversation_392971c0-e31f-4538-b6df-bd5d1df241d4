package com.cainiao.waybill.bridge.web.pickup.quote;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import com.cainiao.cnlogin.api.context.CnUserInfo;
import com.cainiao.cnlogin.api.context.CnUserInfoUtil;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CommonConfigScope;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.config.PickUpPlatformConfigDiamond;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.CommonConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error.Quote;
import com.cainiao.waybill.bridge.biz.pickup.constants.ProductCodeEnums;
import com.cainiao.waybill.bridge.biz.pickup.constants.quote.UserSettlementTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.dto.product.PickUpProductDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.quote.CustomerProductDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.quote.QuoteCommonRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.quote.QuoteConfigRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.quote.UserSettlementInfoDTO;
import com.cainiao.waybill.bridge.biz.pickup.service.QuoteInfoService;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillCommonConfigService;
import com.cainiao.waybill.bridge.biz.ticket.dto.PagingResponse;
import com.cainiao.waybill.bridge.biz.pickup.dto.quote.CustomerSettlementInfoDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.quote.CustomerSettlementInfoRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.quote.QuoteConfigDTO;
import com.cainiao.waybill.bridge.biz.ticket.dto.TicketRoleEnum;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.biz.utils.pickup.OssUtils;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.model.domain.WaybillCommonConfigDO;
import com.cainiao.waybill.bridge.web.charity.controller.BaseController;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.pickup.constant.TicketResourceEnum;
import com.cainiao.waybill.bridge.web.pickup.ticket.role.PickUpResourceAuthority;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 报价相关接口
 * <AUTHOR>
 * @date 2025/3/17 15:57
 **/
@RestController
@Slf4j(topic = "PICK_UP_QUOTE_INFO")
@RequestMapping(value = "/pickup/quote")
public class QuoteController extends BaseController {

    @Resource
    private QuoteInfoService quoteInfoService;

    @Resource
    private WaybillCommonConfigService waybillCommonConfigService;

    /**
     * 查询客户结算详情
     * @return
     */
    @RequestMapping("/settlement/info")
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    public BaseResult<UserSettlementInfoDTO> querySettlementInfo() {
        try{
            CnUserInfo userInfo = CnUserInfoUtil.getLoginContext();
            CustomerSettlementInfoDTO customerSettlementInfo = quoteInfoService.querySettlementInfo(userInfo.getAccountId());
            if(null == customerSettlementInfo){
                throw new BridgeBusinessException(Quote.CUSTOMER_SETTLEMENT_NOT_EXIST_ERROR);
            }
            UserSettlementInfoDTO userSettlementInfoDTO = new UserSettlementInfoDTO();
            BeanUtils.copyProperties(customerSettlementInfo, userSettlementInfoDTO);
            // todo 对接资金接口后设值
            userSettlementInfoDTO.setAccountBalance(1000L);

            return BaseResult.success(userSettlementInfoDTO);
        }catch (BridgeBusinessException e){
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }

    }

    /**
     * 分页查询报价客户列表
     * @param request
     * @return
     */
    @RequestMapping("/list")
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    public BaseResult<PagingResponse<CustomerSettlementInfoDTO>> queryOrderList(@RequestBody
    CustomerSettlementInfoRequest request) {
        try{
            PickUpLogUtil.info("查询报价列表.request:{}", JSONObject.toJSONString(request));
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            PagingResponse<CustomerSettlementInfoDTO> detailDTO = quoteInfoService.queryQuoteConfigList(request);
            return BaseResult.success(detailDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("查询报价列表失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("查询报价列表失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 查询客户和报价详情
     * @param request
     * @return
     */
    @RequestMapping("/detail")
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    public BaseResult<CustomerSettlementInfoDTO> queryQuoteDetail(@RequestBody
    CustomerSettlementInfoRequest request) {
        try{
            PickUpLogUtil.info("查询客户和报价详情.request:{}", JSONObject.toJSONString(request));
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            CustomerSettlementInfoDTO detailDTO = quoteInfoService.querySettlementQuoteConfig(request);
            return BaseResult.success(detailDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("查询客户和报价详情失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("查询客户和报价详情失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }

    }

    /**
     * 更新客户结算状态
     * @param request
     * @return
     */
    @RequestMapping("/update/status")
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    public BaseResult<CustomerSettlementInfoDTO> updateSettlementStatus(@RequestBody
    CustomerSettlementInfoRequest request) {
        try{
            PickUpLogUtil.info("更新客户结算状态.request:{}", JSONObject.toJSONString(request));
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            CustomerSettlementInfoDTO detailDTO = quoteInfoService.updateSettlementStatus(request);
            return BaseResult.success(detailDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("更新客户结算状态失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("更新客户结算状态失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }

    }

    /**
     * 新增/修改客户结算配置
     * @param request
     * @return
     */
    @RequestMapping("/save/customer/settlement")
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    public BaseResult<CustomerSettlementInfoDTO> saveCustomerSettlement(@RequestBody
    CustomerSettlementInfoRequest request) {
        try{PickUpLogUtil.info("保存客户结算配置.request:{}", JSONObject.toJSONString(request));
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            request.setUserType(UserSettlementTypeEnum.CUSTOMER.getType());
            CustomerSettlementInfoDTO detailDTO = quoteInfoService.saveCustomerSettlement(request);
            return BaseResult.success(detailDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("保存客户结算配置失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("保存客户结算配置失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 新增/修改CP结算配置
     * @param request
     * @return
     */
    @RequestMapping("/save/cp/settlement")
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    public BaseResult<CustomerSettlementInfoDTO> saveCpSettlement(@RequestBody
    CustomerSettlementInfoRequest request) {
        try{
            PickUpLogUtil.info("保存CP结算配置.request:{}", JSONObject.toJSONString(request));
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            request.setUserType(UserSettlementTypeEnum.CP.getType());
            CustomerSettlementInfoDTO detailDTO = quoteInfoService.saveCustomerSettlement(request);
            return BaseResult.success(detailDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("保存客户结算配置失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("保存客户结算配置失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 下载报价模板
     * @return
     */
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    @RequestMapping("/template/download")
    public BaseResult<String> downloadQuoteTemplate(@RequestBody CustomerSettlementInfoRequest request){
        try{
            PickUpLogUtil.info("下载报价模版文件.request:{}", JSONObject.toJSONString(request));
            return BaseResult.success(OssUtils.generateExpireUrl(BridgeSwitch.QUOTE_TEMPLATE_URL, null));
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("下载报价模版文件失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("下载报价模版文件失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }

    }

    /**
     * 上传报价文件
     * @return
     */
    @PostMapping("/upload/file")
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    public BaseResult<QuoteConfigDTO> uploadQuoteFile(@RequestParam("file") MultipartFile file, CustomerSettlementInfoRequest request){
        try{
            PickUpLogUtil.info("上传报价文件.request:{}", JSONObject.toJSONString(request));
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            QuoteConfigDTO quoteConfigDTO = quoteInfoService.uploadQuoteFile(file, request);
            return BaseResult.success(quoteConfigDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("上传报价文件失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("上传报价文件失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 查询产品编码列表
     * @param request
     * @return
     */
    @RequestMapping("/product/list")
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    public BaseResult<List<PickUpProductDTO>> queryProductList(@RequestBody CustomerProductDTO request) {
        try{
            if(null == request || null == request.getUserKey()){
                return BaseResult.bizFail(Error.SYSTEM_PARAM_ERROR);
            }
            PickUpLogUtil.info("查询产品编码列表.request:{}", JSONObject.toJSONString(request));
            Long userKey = request.getUserKey();

            // 运力/商家
            String userType = quoteInfoService.queryUserTypeByUserKey(userKey);
            UserSettlementTypeEnum item = UserSettlementTypeEnum.getByType(userType);
            if(null == item){
                return BaseResult.bizFail(Quote.CUSTOMER_SETTLEMENT_TYPE_CONFIG_ERROR);
            }
            List<PickUpProductDTO> list = new ArrayList<>();
            if(UserSettlementTypeEnum.CUSTOMER == item){
                WaybillCommonConfigDO commonConfigDO = waybillCommonConfigService.queryWaybillCommonConfigByValue(CommonConfigScope.USER_SCOPE.getCode(), userKey.toString());
                if(null == commonConfigDO || StringUtils.isBlank(commonConfigDO.getConfigValue())){
                    return BaseResult.bizFail(Quote.QUOTE_YL_CONFIG_ERROR);
                }

                String resCode = commonConfigDO.getConfigType();
                String appKey = null;
                if(resCode.contains(CommonConstants.RES_CODE_SPLIT)
                    && resCode.split(CommonConstants.RES_CODE_SPLIT).length == CommonConstants.RES_CODE_SPLIT_LENGTH){
                    appKey = resCode.split(CommonConstants.RES_CODE_SPLIT)[0];
                }
                if(StringUtils.isBlank(appKey)){
                    return BaseResult.bizFail(Quote.QUOTE_YL_CONFIG_ERROR);
                }

                String productCode = PickUpPlatformConfigDiamond.getCustomerProductCode(appKey, null);
                if(StringUtils.isBlank(productCode)){
                    return BaseResult.bizFail(Quote.QUOTE_YL_CONFIG_ERROR);
                }
                ProductCodeEnums productCodeItem = ProductCodeEnums.getByCode(productCode);
                if(null == productCodeItem){
                    return BaseResult.bizFail(Quote.QUOTE_PRODUCT_CODE_CONFIG_ERROR);
                }

                PickUpProductDTO productCodeDTO = new PickUpProductDTO();
                productCodeDTO.setProductCode(productCode);
                productCodeDTO.setProductName(productCodeItem.getDesc());
                list.add(productCodeDTO);
            }else if(UserSettlementTypeEnum.CP == item){
                ProductCodeEnums[] enums = ProductCodeEnums.values();
                for(ProductCodeEnums enu : enums){
                    PickUpProductDTO dto = new PickUpProductDTO();
                    dto.setProductCode(enu.getCode());
                    dto.setProductName(enu.getDesc());
                    list.add(dto);
                }
            }

            return BaseResult.success(list);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("查询产品编码列表失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("查询产品编码列表失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }

    }

    /**
     * 新增报价配置
     * @param request
     * @return
     */
    @PostMapping("/create/config")
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    public BaseResult<QuoteConfigDTO> createQuoteConfig(@RequestBody
    QuoteConfigRequest request) {
        try{
            PickUpLogUtil.info("新增报价配置.request:{}", JSONObject.toJSONString(request));
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            QuoteConfigDTO quoteConfigDTO = quoteInfoService.createQuoteConfig(request);
            PickUpLogUtil.info("新增报价配置完成.quoteConfigDTO:{}", JSONObject.toJSONString(quoteConfigDTO));
            return BaseResult.success(quoteConfigDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("新增报价配置失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("新增报价配置失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 删除报价配置
     * @param request
     * @return
     */
    @RequestMapping("/delete/config")
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    public BaseResult<String> deleteQuoteConfig(@RequestBody QuoteCommonRequest request) {
        try{
            if(null == request || null == request.getQuoteId()){
                return BaseResult.bizFail(Error.SYSTEM_PARAM_ERROR);
            }
            PickUpLogUtil.info("删除报价配置.request:{}", JSONObject.toJSONString(request));
            TicketRoleEnum role = getUserRole();
            if(TicketRoleEnum.admin != role){
                return BaseResult.bizFail(Error.NO_PERMISSION_ERROR);
            }
            // 删除报价配置 并检查对应主体客户的生效状态
            quoteInfoService.deleteQuoteConfig(request.getQuoteId());
            return BaseResult.success("成功");
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("删除报价配置失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("删除报价配置失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 查询报价记录列表
     * @param request
     * @return
     */
    @RequestMapping("/config/list")
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    public BaseResult<List<QuoteConfigDTO>> queryQuoteConfigList(@RequestBody QuoteCommonRequest request) {
        try{
            if(null == request || null == request.getUserKey()){
                return BaseResult.bizFail(Error.SYSTEM_PARAM_ERROR);
            }
            PickUpLogUtil.info("查询报价记录列表.request:{}", JSONObject.toJSONString(request));
            TicketRoleEnum role = getUserRole();
            if(TicketRoleEnum.admin != role){
                return BaseResult.bizFail(Error.NO_PERMISSION_ERROR);
            }
            List<QuoteConfigDTO> list = quoteInfoService.queryQuoteConfigList(request.getUserKey());
            return BaseResult.success(list);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("查询报价记录列表失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("查询报价记录列表失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 查询报价历史记录列表
     * @param request
     * @return
     */
    @RequestMapping("/history/config/list")
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    public BaseResult<List<QuoteConfigDTO>> queryHistoryQuoteConfigList(@RequestBody QuoteCommonRequest request) {
        try{
            if(null == request || null == request.getUserKey()){
                return BaseResult.bizFail(Error.SYSTEM_PARAM_ERROR);
            }
            PickUpLogUtil.info("查询报价历史记录列表.request:{}", JSONObject.toJSONString(request));
            TicketRoleEnum role = getUserRole();
            if(TicketRoleEnum.admin != role){
                return BaseResult.bizFail(Error.NO_PERMISSION_ERROR);
            }
            List<QuoteConfigDTO> list = quoteInfoService.queryHistoryQuoteConfigList(request.getUserKey());
            return BaseResult.success(list);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("查询报价历史记录列表失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("查询报价历史记录列表失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }

    /**
     * 下载报价文件
     * @param request
     * @return
     */
    @PickUpResourceAuthority(TicketResourceEnum.QUOTE_MANAGER)
    @RequestMapping("/file/download")
    public BaseResult<String> downQuoteFile(@RequestBody QuoteCommonRequest request) {
        try {
            if(null == request || null == request.getFileUrl()){
                return BaseResult.bizFail(Error.SYSTEM_PARAM_ERROR);
            }
            PickUpLogUtil.info("下载报价文件请求.request:{}", JSONObject.toJSONString(request));
            TicketRoleEnum role = getUserRole();
            if(TicketRoleEnum.admin != role){
                return BaseResult.bizFail(Error.NO_PERMISSION_ERROR);
            }
            String url = OssUtils.generateExpireUrl(request.getFileUrl(), null);
            PickUpLogUtil.info("下载报价文件带过期时间url:{}", url);
            return BaseResult.success(url);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("下载报价文件失败", "", e.getErrorCode(), e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("下载报价文件失败", "", "SYSTEM_ERROR", e.getMessage(), JSONObject.toJSONString(request));
            return BaseResult.bizFail(Error.SYSTEM_ERROR);
        }
    }


}
