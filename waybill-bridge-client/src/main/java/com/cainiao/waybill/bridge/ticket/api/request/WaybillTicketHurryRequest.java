package com.cainiao.waybill.bridge.ticket.api.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 工单催单请求
 * <AUTHOR>
 */
@Data
public class WaybillTicketHurryRequest implements Serializable {

    /**
     *  物流运单号
     */
    @NotBlank(message = "运单号不能为空")
    private String mailNo;

    /**
     * 工单发起方类型
     * 1：消费者
     * 2：客服小二
     */
    @NotNull(message = "请选择工单发起方类型")
    private Integer operatorType;

    /**
     * 工单类型
     * 1：签收核实
     * 2：核实取件码
     * 3：丢失/破损
     */
    @NotNull(message = "请选择工单处理类型")
    private Integer ticketType;
}
