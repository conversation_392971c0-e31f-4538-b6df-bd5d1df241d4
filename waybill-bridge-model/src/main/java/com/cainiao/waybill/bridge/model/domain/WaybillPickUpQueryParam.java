package com.cainiao.waybill.bridge.model.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */

/**
 *
 * <AUTHOR>
 */
@Data
public class WaybillPickUpQueryParam implements Serializable {

    private static final long serialVersionUID = 4825336919840178521L;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    private String senderProvince;

    private String receiverProvince;

    /**
     * 运单号列表
     */
    private List<String> mailNoList;

    private String cpCode;

    private String orderChannel;

    private String senderMobile;
}
