package com.cainiao.waybill.bridge.common.label.dto.request;

import com.cainiao.waybill.bridge.common.dto.BaseDTO;
import com.cainiao.waybill.bridge.common.label.dto.AddressDTO;

/**
 * Description: 创建网点-商家地址-小件员绑定关系 request
 *
 * <AUTHOR>
 * @Date 2017-04-26
 */
public class CreateBranchSellerAddRelRequest extends BaseDTO {
    private static final long serialVersionUID = -3583741160574615696L;

    /**
     * cp 编码
     */
    private String cpCode;

    /**
     * 网点编码
     */
    private String branchCode;

    /**
     * 商家id
     */
    private Long sellerId;

    /**
     * 商家名称
     */
    private String sellerName;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 商家地址信息
     */
    private AddressDTO sellerAddress;

    /**
     * 小件员id
     */
    private Long courierId;

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public String getSellerName() {
        return sellerName;
    }

    public CreateBranchSellerAddRelRequest setSellerName(String sellerName) {
        this.sellerName = sellerName;
        return this;
    }

    public AddressDTO getSellerAddress() {
        return sellerAddress;
    }

    public void setSellerAddress(AddressDTO sellerAddress) {
        this.sellerAddress = sellerAddress;
    }

    public Long getCourierId() {
        return courierId;
    }

    public void setCourierId(Long courierId) {
        this.courierId = courierId;
    }
}
