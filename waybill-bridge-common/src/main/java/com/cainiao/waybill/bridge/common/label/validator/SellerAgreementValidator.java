package com.cainiao.waybill.bridge.common.label.validator;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.validator.BridgeBaseValidator;
import org.apache.commons.lang.StringUtils;

/**
 * 商家签署协议 validator
 * <AUTHOR>
 * @date 2017-06-12
 */
public class SellerAgreementValidator extends BridgeBaseValidator {
    /**
     * 校验查询条件是否合法
     * @throws BridgeValidationException
     */
    public static void validate(Long sellerId, ClientInfoDTO clientInfoDTO) throws
            BridgeValidationException {
        validate(clientInfoDTO != null, "clientInfoDTO不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getAppName()), "clientInfoDTO.appName不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getOperator()), "clientInfoDTO.operator不能为空");

        validate(sellerId != null, "sellerId不能为空");
    }
}
