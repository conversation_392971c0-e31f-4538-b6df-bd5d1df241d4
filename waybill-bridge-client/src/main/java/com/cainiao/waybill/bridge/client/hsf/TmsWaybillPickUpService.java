package com.cainiao.waybill.bridge.client.hsf;

import com.cainiao.waybill.bridge.SimpleResult;
import com.cainiao.waybill.bridge.client.request.PickUpHsfCreateOrderRequest;
import com.cainiao.waybill.bridge.client.response.PickUpHsfCreateOrderResponse;

/**
 * HSF订单服务
 * <AUTHOR>
 * @date 2024/7/10 21:35
 **/
public interface TmsWaybillPickUpService {

    /**
     * 创建订单
     * @param request
     * @return
     */
    SimpleResult<PickUpHsfCreateOrderResponse> createOrder(PickUpHsfCreateOrderRequest request);
}
