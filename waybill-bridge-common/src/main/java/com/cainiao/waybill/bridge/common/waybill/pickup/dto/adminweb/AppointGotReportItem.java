package com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb;

import lombok.Data;

import java.io.Serializable;

/**
 * 预约订单揽收率计算返回值
 *
 * @author: yexin
 * @date: 2022-05-16 14:59
 **/
@Data
public class AppointGotReportItem  implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 预约订单总量
     */
    private Integer appointAllNum;
    /**
     * 预约订单揽收量
     */
    private Integer appointGotNum;
    /**
     * 预约订单揽收率
     */
    private String appointGotRate;
    /**
     * 预约订单揽收率值
     */
    private double appointGotRateValue;
}
