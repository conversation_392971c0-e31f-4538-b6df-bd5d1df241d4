package com.cainiao.waybill.bridge.model.dao.impl;

import com.cainiao.waybill.bridge.model.common.AbstractDAO;
import com.cainiao.waybill.bridge.model.dao.PickupNumSequenceDAO;
import com.cainiao.waybill.bridge.model.domain.PickupNumSequenceDO;
import com.google.common.collect.Maps;
import com.taobao.common.dao.persistence.exception.DAOException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-30
 */
public class PickupNumSequenceDAOImpl extends AbstractDAO implements PickupNumSequenceDAO {


    @Override
    public PickupNumSequenceDO queryBySerialNum(Long serialNum) throws DAOException {
        // 1. select for update
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("serialNum", serialNum);
        return  (PickupNumSequenceDO)this.executeQueryForObject("pickupNumSequence.selectBySerialNum", paramMap, getDBRoute());
    }

    @Override
    public Integer insert(PickupNumSequenceDO pickupNumSequenceDO) throws DAOException {
        return this.executeUpdate("pickupNumSequence.insert", pickupNumSequenceDO, getDBRoute());
    }

    @Override
    public Integer batchInsert(List<PickupNumSequenceDO> pickupNumSequenceList) throws DAOException {
        Map<String, Object> param = Maps.newHashMap();
        param.put("pickupNumSequenceList", pickupNumSequenceList);
        return this.executeUpdate("pickupNumSequence.batchInsert", param, getDBRoute());
    }

}
