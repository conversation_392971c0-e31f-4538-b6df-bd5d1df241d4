package com.cainiao.waybill.bridge.enterprise.common;

import lombok.Data;

import java.io.Serializable;


/**
 * 该类用于进行分页操作字段
 * <AUTHOR>
 * @date 2025-04-15 17:50:45
 */
@Data
public class TableColumns implements Serializable {

    private static final long serialVersionUID = 8746793126124064131L;
    /**
     * 当前页面标题字段
     */
    private String dataIndex;

    /**
     * 当前页面标题
     */
    private String title;

    /**
     * 是否可筛选
     */
    private Boolean sortable;

    /**
     * 当前页面标题别名
     */
    private String name;
}
