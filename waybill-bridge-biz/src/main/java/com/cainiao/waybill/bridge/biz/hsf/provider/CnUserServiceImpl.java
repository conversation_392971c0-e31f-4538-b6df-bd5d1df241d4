package com.cainiao.waybill.bridge.biz.hsf.provider;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.cainiao.waybill.bridge.SimpleResult;
import com.cainiao.waybill.bridge.biz.user.manager.CnUserManager;
import com.cainiao.waybill.bridge.biz.wrapper.WtPayPlatformWrapper;
import com.cainiao.waybill.bridge.client.hsf.CnUserService;
import com.cainiao.waybill.bridge.client.response.CustomerInfoResponse;
import com.cainiao.waybill.bridge.constants.CustomerManageStatusEnum;
import com.cainiao.waybill.bridge.constants.CustomerServiceStatusEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * 淘外预充值账号服务
 * <AUTHOR>
 * @date 2024/8/23 15:45
 **/
@Slf4j
@HSFProvider(serviceInterface = CnUserService.class)
public class CnUserServiceImpl implements CnUserService {

    @Resource
    private WtPayPlatformWrapper wtPayPlatformWrapper;

    @Resource
    private CnUserManager cnUserManager;


    @Override
    public SimpleResult<CustomerInfoResponse> queryAccountInfo(String accountId) {
        CustomerInfoResponse customerInfoResponse = new CustomerInfoResponse();
        customerInfoResponse.setAccountId(accountId);
        customerInfoResponse.setServiceStatus(CustomerServiceStatusEnum.VALID.getStatus());
        customerInfoResponse.setServiceStatusDesc(CustomerServiceStatusEnum.VALID.getDesc());

        // 查询管控状态
        String manageStatus = cnUserManager.queryPreChargeManageStatus(accountId);
        customerInfoResponse.setManageStatus(manageStatus);
        CustomerManageStatusEnum enumItem = CustomerManageStatusEnum.getByStatus(manageStatus);
        customerInfoResponse.setManageStatusDesc(null == enumItem ? "未知" : enumItem.getDesc());

        return SimpleResult.success(customerInfoResponse);
    }


}
