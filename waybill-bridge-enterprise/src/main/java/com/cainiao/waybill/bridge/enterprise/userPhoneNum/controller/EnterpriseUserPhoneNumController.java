package com.cainiao.waybill.bridge.enterprise.userPhoneNum.controller;


import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.userPhoneNum.requset.EnterpriseUserPhoneNumRequest;
import com.cainiao.waybill.bridge.enterprise.userPhoneNum.service.EnterpriseUserPhoneNumService;
import com.cainiao.waybill.bridge.enterprise.administrators.vo.EnterpriseUserPhoneNumVO;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.constant.EnterpriseImportCommonConstant;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.utils.excel.ExcelExportUtil;
import com.cainiao.waybill.bridge.enterprise.utils.excel.ExcelImportUtil;
import com.cainiao.waybill.bridge.enterprise.utils.excel.ExcelUtil;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/14
 **/
@RestController
@RequestMapping(value = "/enterprise/userPhoneNum")
public class EnterpriseUserPhoneNumController {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private EnterpriseUserPhoneNumService enterpriseUserPhoneNumService;

    @Autowired
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;


    @PostMapping("/addPhoneNum")
    @Description("添加用户联系方式")
    EnterpriseBaseResult<Boolean> addPhoneNum(@RequestBody EnterpriseUserPhoneNumRequest request) {
        LOGGER.info("addPhoneNum request:{}", JSON.toJSONString(request));
        try {
            int addResult = enterpriseUserPhoneNumService.addPhoneNum(request);
            return EnterpriseBaseResult.success(addResult == 1);
        } catch (Exception e) {
            LOGGER.error("addPhoneNum error:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @PostMapping("/deletePhoneNum")
    @Description("删除用户联系方式")
    EnterpriseBaseResult<Boolean> deletePhoneNum(@RequestBody EnterpriseUserPhoneNumRequest request) {
        LOGGER.info("deletePhoneNum request:{}", JSON.toJSONString(request));
        try {
            int delResult = enterpriseUserPhoneNumService.deletePhoneNum(request);
            return EnterpriseBaseResult.success(delResult == 1);
        } catch (Exception e) {
            LOGGER.error("deletePhoneNum error:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @PostMapping("/importPhoneNum")
    @Description("导入联系方式")
    EnterpriseBaseResult<EnterpriseUserPhoneNumVO> importPhoneNum(@RequestParam("file") MultipartFile file, @RequestParam("corpId") String corpId) {

        try {
            List<EnterpriseUserPhoneNumRequest> importDataList = ExcelImportUtil.loadDataFromInputStream(file.getInputStream(),
                    file.getOriginalFilename(),
                    EnterpriseUserPhoneNumRequest.class,
                    EnterpriseImportCommonConstant.EXCEL_MAX_SIZE);

            List<EnterpriseUserPhoneNumRequest> errorList = Lists.newArrayList();
            List<EnterpriseUserPhoneNumRequest> filteredList = Lists.newArrayList();
            for (EnterpriseUserPhoneNumRequest phoneNumRequest : importDataList) {
                //预处理
                phoneNumRequest.setCorpId(corpId);
                // 空行处理
                if (StringUtils.isBlank(phoneNumRequest.getUserId()) && StringUtils.isBlank(phoneNumRequest.getUserName())
                        && StringUtils.isBlank(phoneNumRequest.getPhoneNum())) {
                    continue;
                }

                try {
                    int addResult = enterpriseUserPhoneNumService.addPhoneNum(phoneNumRequest);
                    if (addResult == 1) {
                        filteredList.add(phoneNumRequest);
                    }
                } catch (EnterpriseException e) {
                    phoneNumRequest.setErrorInfo(e.getErrorMessage());
                    errorList.add(phoneNumRequest);
                }
            }

            EnterpriseUserPhoneNumVO phoneNumVO = new EnterpriseUserPhoneNumVO();
            phoneNumVO.setDataSize(filteredList.size());

            // 有错误列表, 生成错误文件
            if (CollectionUtils.isNotEmpty(errorList)) {
                LOGGER.info("importPhoneNum errorList:{}", errorList);
                Workbook workbook = ExcelExportUtil.build(errorList, "联系方式导入失败列表", EnterpriseUserPhoneNumRequest.class);
                String url = ExcelUtil.exportToOSS(workbook, EnterpriseImportCommonConstant.OSS_TEMP_FILE_DIR);
                phoneNumVO.setFileUrl(url);
                phoneNumVO.setErrorDataSize(errorList.size());
            }

            return EnterpriseBaseResult.success(phoneNumVO);
        } catch (Exception e) {
            LOGGER.error("importPhoneNum error : {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }
}
