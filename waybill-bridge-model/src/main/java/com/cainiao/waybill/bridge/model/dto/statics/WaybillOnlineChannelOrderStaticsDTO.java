package com.cainiao.waybill.bridge.model.dto.statics;

import java.io.Serializable;

import lombok.Data;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * <AUTHOR> zouping.fzp
 * @Classname WaybillOnlineChannelOrderStaticsDTO
 * @Description
 * @Date 2023/11/10 15:40
 * @Version 1.0
 */
@Data
public class WaybillOnlineChannelOrderStaticsDTO implements Serializable {

    private static final long serialVersionUID = -7619515193945608454L;

    /**
     * 渠道
     */
    private String orderChannels;

    /**
     * 代理
     */
    private String agent;

    /**
     * cpCode
     */
    private String cpCode;

    /**
     *  * 数量
     */
    private int num;

    /**
     * 已揽收量
     */
    private int gotNum;

    /**
     * 应接单量
     */
    private int shouldAcceptNum;

    /**
     * 30分钟接单量
     */
    private int thirtyMinuteAcceptNum;

    /**
     * 10分钟接单量
     */
    private int tenMinuteAcceptNum;

    /**
     * 取消量
     */
    private int cancelNum;

    /**
     * 换单量
     */
    private int switchNum;

    /**
     * 工单量
     */
    private int ticketNum;

    /**
     * 去重工单量
     */
    private int distinctTicketNum;

    /**
     * 应揽收量
     */
    private int shouldGotNum;

    /**
     * 已揽收量
     */
    private int gotedNum;

    /**
     * 及时揽收量
     */
    private int inTimeGotNum;

    @Override
    public boolean equals(Object o) {
        if (this == o) {return true;}

        if (o == null || getClass() != o.getClass()) {return false;}

        WaybillOnlineChannelOrderStaticsDTO that = (WaybillOnlineChannelOrderStaticsDTO)o;

        return new EqualsBuilder().append(orderChannels, that.orderChannels).append(agent,
            that.agent).append(cpCode, that.cpCode).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(orderChannels).append(agent).append(cpCode).toHashCode();
    }
}
