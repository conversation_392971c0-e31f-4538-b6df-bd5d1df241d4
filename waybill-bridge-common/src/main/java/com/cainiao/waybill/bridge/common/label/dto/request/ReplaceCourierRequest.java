package com.cainiao.waybill.bridge.common.label.dto.request;

import com.cainiao.waybill.bridge.common.dto.BaseDTO;
import com.cainiao.waybill.bridge.common.label.dto.AddressDTO;

/**
 * Description: 网点换绑小件员 request
 *
 * <AUTHOR>
 * @Date 2017-04-26
 */
public class ReplaceCourierRequest extends BaseDTO {
    private static final long serialVersionUID = -3583741160574615696L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * cp 编码
     */
    private String cpCode;

    /**
     * 网点编码
     */
    private String branchCode;

    /**
     * 商家id
     */
    private Long sellerId;

    /**
     * 新换绑的小件员id
     */
    private Long newCourierId;

    /**
     * 商家地址
     */
    private AddressDTO sellerAddress;

    public AddressDTO getSellerAddress() {
        return sellerAddress;
    }

    public void setSellerAddress(AddressDTO sellerAddress) {
        this.sellerAddress = sellerAddress;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public Long getNewCourierId() {
        return newCourierId;
    }

    public void setNewCourierId(Long newCourierId) {
        this.newCourierId = newCourierId;
    }
}
