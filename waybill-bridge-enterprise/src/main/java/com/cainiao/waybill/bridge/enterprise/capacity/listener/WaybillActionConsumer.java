package com.cainiao.waybill.bridge.enterprise.capacity.listener;

import com.alibaba.boot.ons.annotation.Subscription;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpLogisticsStatusEnum;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseSwitchHolder;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseOrderStatusEnum;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseOrderDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseOrderParam;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseOrderMapper;
import com.cainiao.waybill.galaxy.api.common.log.LogSupport;
import com.cainiao.waybill.galaxy.isv.api.constant.OrderStateEnums;
import com.taobao.cainiao.waybill.client.domain.LifeCycleNotifyDTO;
import com.taobao.cainiao.waybill.client.utils.WaybillLifeCycleNotifyDTOBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2025/8/2 11:13 AM
 */
@Subscription(topic = "CAINIAO-WAYBILL-LIFECYCLE-NEW-BIZ",
        expression = "merchant_bridge_enterprise", consumer = "waybill-bridge-enterprise-action_consumer")

public class WaybillActionConsumer implements MessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

//    static Map<Byte, EnterpriseOrderStatusEnum> orderStatusMap = new HashMap<>();
//    static Map<Byte, PickUpLogisticsStatusEnum> orderActionMap = new HashMap<>();
//
//
//    static {
//        orderStatusMap.put(WaybillLifeCycleNotifyDTOBuilder.Actions.LOGISTIC_PICKUP.getByteAction(), EnterpriseOrderStatusEnum.IN_TRANSIT);
//        orderStatusMap.put(WaybillLifeCycleNotifyDTOBuilder.Actions.APPLY_NEW.getByteAction(), EnterpriseOrderStatusEnum.PENDING);
//        orderStatusMap.put(WaybillLifeCycleNotifyDTOBuilder.Actions.LOGISTIC_SIGN.getByteAction(), EnterpriseOrderStatusEnum.SIGNED);
//        orderStatusMap.put(WaybillLifeCycleNotifyDTOBuilder.Actions.APPLY_CANCEL.getByteAction(), EnterpriseOrderStatusEnum.CANCELED);
//    }
//
//    static {
//        orderActionMap.put(WaybillLifeCycleNotifyDTOBuilder.Actions.LOGISTIC_PICKUP.getByteAction(), PickUpLogisticsStatusEnum.ACCEPT);
//        orderActionMap.put(WaybillLifeCycleNotifyDTOBuilder.Actions.APPLY_NEW.getByteAction(), PickUpLogisticsStatusEnum.CREATE);
//        orderActionMap.put(WaybillLifeCycleNotifyDTOBuilder.Actions.LOGISTIC_SIGN.getByteAction(), PickUpLogisticsStatusEnum.SIGN);
//        orderActionMap.put(WaybillLifeCycleNotifyDTOBuilder.Actions.APPLY_CANCEL.getByteAction(), PickUpLogisticsStatusEnum.CANCEL);
//    }

    @Resource
    private WaybillBridgeEnterpriseOrderMapper enterpriseOrderMapper;

    @Override
    public Action consume(Message message, ConsumeContext context) {
        String key = message.getKey();
        try {
            LifeCycleNotifyDTO lifeCycleNotifyDTO = JSONObject.parseObject(message.getBody(), LifeCycleNotifyDTO.class);
            String waybillCode = lifeCycleNotifyDTO.getWaybillCode();
            LOGGER.info("consumeAction|success|{}|{}", waybillCode, lifeCycleNotifyDTO);
            handleAction(waybillCode, lifeCycleNotifyDTO.getAction());
            return Action.CommitMessage;
        } catch (Exception e) {
            LOGGER.error("consumeAction|failed|{}|{}|{}", key, new String(message.getBody(), StandardCharsets.UTF_8), e.getMessage(), e);
            return Action.ReconsumeLater;
        }
    }

    private void handleAction(String waybillCode, Byte action) {
        // 查询订单是否存在
        WaybillBridgeEnterpriseOrderParam enterpriseOrderParam = new WaybillBridgeEnterpriseOrderParam();
        enterpriseOrderParam.createCriteria()
                .andWaybillCodeEqualTo(waybillCode);
        WaybillBridgeEnterpriseOrderDO waybillBridgeEnterpriseOrderDO = enterpriseOrderMapper.selectOneByParam(enterpriseOrderParam);
        if (waybillBridgeEnterpriseOrderDO == null) {
            return ;
        }

        Boolean updateGotTime = false;
        if (action == WaybillLifeCycleNotifyDTOBuilder.Actions.LOGISTIC_PICKUP.getByteAction()
                && waybillBridgeEnterpriseOrderDO.getGotTime() == null) {
            updateGotTime = true;
        }
        // 只有邮政的签收才做揽收节点补偿
        if (action == WaybillLifeCycleNotifyDTOBuilder.Actions.LOGISTIC_SIGN.getByteAction()) {
            if (waybillBridgeEnterpriseOrderDO.getGotTime() == null) {
                updateGotTime = true;
                action = WaybillLifeCycleNotifyDTOBuilder.Actions.LOGISTIC_PICKUP.getByteAction();
            } else {
                return;
            }
        }

        if(BooleanUtils.isTrue(updateGotTime)){
            waybillBridgeEnterpriseOrderDO.setGotTime(new Date());
        }
        Integer actionValue = EnterpriseSwitchHolder.ENTERPRISE_ACTION_STATUS_MAPPING.get(action);
        if(null != actionValue){
            waybillBridgeEnterpriseOrderDO.setAction(actionValue);
        }
        Integer OrderStatusCode = EnterpriseSwitchHolder.ENTERPRISE_ORDER_STATUS_MAPPING.get(action);
        if (null != OrderStatusCode){
            waybillBridgeEnterpriseOrderDO.setStatus(OrderStatusCode);
        }
        waybillBridgeEnterpriseOrderDO.setGmtModified(new Date());
        enterpriseOrderMapper.updateByPrimaryKey(waybillBridgeEnterpriseOrderDO);
    }
}
