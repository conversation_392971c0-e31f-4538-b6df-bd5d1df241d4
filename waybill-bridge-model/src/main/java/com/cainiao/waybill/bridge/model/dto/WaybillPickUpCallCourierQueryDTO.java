package com.cainiao.waybill.bridge.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/16-下午5:52
 */
@Data
public class WaybillPickUpCallCourierQueryDTO {

    private Long id;

    private Date bizTimeStart;

    private Date bizTimeEnd;

    private String cpCode;

    /**
     * 语音呼叫状态，-1：调用语音呼叫接口失败，0：未调用语音呼叫接口，1：已调用语音呼叫接口，2：成功接通
     */
    private List<Integer> status;

    /**
     * 运单号
     */
    private String mailNo;

    /**
     * 接单小件员手机号
     */
    private String acceptCourierMobile;

    /**
     * 业务类型： 语音外呼可能还有其它应用场景，通过该字段区分。1：淘外电商平台-电联接单小件员30分钟内联系消费者预约上门取件时间
     */
    private Integer bizType;
}
