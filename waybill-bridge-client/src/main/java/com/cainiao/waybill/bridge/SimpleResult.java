package com.cainiao.waybill.bridge;

import java.io.Serializable;

import lombok.Data;

/**
 * 结果类
 * <AUTHOR> zouping.fzp
 * @Classname SimpleResult
 * @Description
 * @Date 2023/3/21 2:57 下午
 * @Version 1.0
 */
@Data
public class SimpleResult<T> implements Serializable {

    private static final long serialVersionUID = 8378138423650270048L;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 返回的数据
     */
    private T data;

    /**
     * 错误编码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;

    public static <R> SimpleResult<R> success() {
        SimpleResult<R> result = new SimpleResult<>();
        result.setSuccess(true);
        result.setData(null);
        return result;
    }

    public static <R> SimpleResult<R> success(R data) {
        SimpleResult<R> result = new SimpleResult<>();
        result.setSuccess(true);
        result.setData(data);
        return result;
    }

    public static <R> SimpleResult<R> failed(String errorCode, String errorMsg) {
        SimpleResult<R> result = new SimpleResult<>();
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setErrorMsg(errorMsg);
        return result;
    }

    public static <R> SimpleResult<R> failed(int errorCode, String errorMsg) {
        SimpleResult<R> result = new SimpleResult<>();
        result.setSuccess(false);
        result.setErrorCode(errorCode + "");
        result.setErrorMsg(errorMsg);
        return result;
    }

}
