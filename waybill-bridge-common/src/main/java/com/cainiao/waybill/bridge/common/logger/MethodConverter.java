package com.cainiao.waybill.bridge.common.logger;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;

public class MethodConverter extends ClassicConverter {
    public MethodConverter() {
    }

    @Override
    public String convert(ILoggingEvent le) {
        StackTraceElement[] cda = le.getCallerData();
        if(cda==null){
            return "";
        }
        if(cda.length>0){
            return cda[cda.length-1].getMethodName();
        }
        //return cda != null && cda.length > 3?cda[2].getMethodName():"?";
        return "";
    }
}
