package com.cainiao.waybill.bridge.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class WaybillKeyLogParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public WaybillKeyLogParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public WaybillKeyLogParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public WaybillKeyLogParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public WaybillKeyLogParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public WaybillKeyLogParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public WaybillKeyLogParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsIsNull() {
            addCriterion("order_channels is null");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsIsNotNull() {
            addCriterion("order_channels is not null");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsEqualTo(String value) {
            addCriterion("order_channels =", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotEqualTo(String value) {
            addCriterion("order_channels <>", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsGreaterThan(String value) {
            addCriterion("order_channels >", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsGreaterThanOrEqualTo(String value) {
            addCriterion("order_channels >=", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsLessThan(String value) {
            addCriterion("order_channels <", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsLessThanOrEqualTo(String value) {
            addCriterion("order_channels <=", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsLike(String value) {
            addCriterion("order_channels like", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotLike(String value) {
            addCriterion("order_channels not like", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsIn(List<String> values) {
            addCriterion("order_channels in", values, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotIn(List<String> values) {
            addCriterion("order_channels not in", values, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsBetween(String value1, String value2) {
            addCriterion("order_channels between", value1, value2, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotBetween(String value1, String value2) {
            addCriterion("order_channels not between", value1, value2, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andBizIdIsNull() {
            addCriterion("biz_id is null");
            return (Criteria) this;
        }

        public Criteria andBizIdIsNotNull() {
            addCriterion("biz_id is not null");
            return (Criteria) this;
        }

        public Criteria andBizIdEqualTo(String value) {
            addCriterion("biz_id =", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotEqualTo(String value) {
            addCriterion("biz_id <>", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdGreaterThan(String value) {
            addCriterion("biz_id >", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdGreaterThanOrEqualTo(String value) {
            addCriterion("biz_id >=", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLessThan(String value) {
            addCriterion("biz_id <", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLessThanOrEqualTo(String value) {
            addCriterion("biz_id <=", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdLike(String value) {
            addCriterion("biz_id like", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotLike(String value) {
            addCriterion("biz_id not like", value, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdIn(List<String> values) {
            addCriterion("biz_id in", values, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotIn(List<String> values) {
            addCriterion("biz_id not in", values, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdBetween(String value1, String value2) {
            addCriterion("biz_id between", value1, value2, "bizId");
            return (Criteria) this;
        }

        public Criteria andBizIdNotBetween(String value1, String value2) {
            addCriterion("biz_id not between", value1, value2, "bizId");
            return (Criteria) this;
        }

        public Criteria andTraceIdIsNull() {
            addCriterion("trace_id is null");
            return (Criteria) this;
        }

        public Criteria andTraceIdIsNotNull() {
            addCriterion("trace_id is not null");
            return (Criteria) this;
        }

        public Criteria andTraceIdEqualTo(String value) {
            addCriterion("trace_id =", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdNotEqualTo(String value) {
            addCriterion("trace_id <>", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdGreaterThan(String value) {
            addCriterion("trace_id >", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdGreaterThanOrEqualTo(String value) {
            addCriterion("trace_id >=", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdLessThan(String value) {
            addCriterion("trace_id <", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdLessThanOrEqualTo(String value) {
            addCriterion("trace_id <=", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdLike(String value) {
            addCriterion("trace_id like", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdNotLike(String value) {
            addCriterion("trace_id not like", value, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdIn(List<String> values) {
            addCriterion("trace_id in", values, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdNotIn(List<String> values) {
            addCriterion("trace_id not in", values, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdBetween(String value1, String value2) {
            addCriterion("trace_id between", value1, value2, "traceId");
            return (Criteria) this;
        }

        public Criteria andTraceIdNotBetween(String value1, String value2) {
            addCriterion("trace_id not between", value1, value2, "traceId");
            return (Criteria) this;
        }

        public Criteria andLogLevelIsNull() {
            addCriterion("log_level is null");
            return (Criteria) this;
        }

        public Criteria andLogLevelIsNotNull() {
            addCriterion("log_level is not null");
            return (Criteria) this;
        }

        public Criteria andLogLevelEqualTo(String value) {
            addCriterion("log_level =", value, "logLevel");
            return (Criteria) this;
        }

        public Criteria andLogLevelNotEqualTo(String value) {
            addCriterion("log_level <>", value, "logLevel");
            return (Criteria) this;
        }

        public Criteria andLogLevelGreaterThan(String value) {
            addCriterion("log_level >", value, "logLevel");
            return (Criteria) this;
        }

        public Criteria andLogLevelGreaterThanOrEqualTo(String value) {
            addCriterion("log_level >=", value, "logLevel");
            return (Criteria) this;
        }

        public Criteria andLogLevelLessThan(String value) {
            addCriterion("log_level <", value, "logLevel");
            return (Criteria) this;
        }

        public Criteria andLogLevelLessThanOrEqualTo(String value) {
            addCriterion("log_level <=", value, "logLevel");
            return (Criteria) this;
        }

        public Criteria andLogLevelLike(String value) {
            addCriterion("log_level like", value, "logLevel");
            return (Criteria) this;
        }

        public Criteria andLogLevelNotLike(String value) {
            addCriterion("log_level not like", value, "logLevel");
            return (Criteria) this;
        }

        public Criteria andLogLevelIn(List<String> values) {
            addCriterion("log_level in", values, "logLevel");
            return (Criteria) this;
        }

        public Criteria andLogLevelNotIn(List<String> values) {
            addCriterion("log_level not in", values, "logLevel");
            return (Criteria) this;
        }

        public Criteria andLogLevelBetween(String value1, String value2) {
            addCriterion("log_level between", value1, value2, "logLevel");
            return (Criteria) this;
        }

        public Criteria andLogLevelNotBetween(String value1, String value2) {
            addCriterion("log_level not between", value1, value2, "logLevel");
            return (Criteria) this;
        }

        public Criteria andActionIsNull() {
            addCriterion("action is null");
            return (Criteria) this;
        }

        public Criteria andActionIsNotNull() {
            addCriterion("action is not null");
            return (Criteria) this;
        }

        public Criteria andActionEqualTo(String value) {
            addCriterion("action =", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionNotEqualTo(String value) {
            addCriterion("action <>", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionGreaterThan(String value) {
            addCriterion("action >", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionGreaterThanOrEqualTo(String value) {
            addCriterion("action >=", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionLessThan(String value) {
            addCriterion("action <", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionLessThanOrEqualTo(String value) {
            addCriterion("action <=", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionLike(String value) {
            addCriterion("action like", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionNotLike(String value) {
            addCriterion("action not like", value, "action");
            return (Criteria) this;
        }

        public Criteria andActionIn(List<String> values) {
            addCriterion("action in", values, "action");
            return (Criteria) this;
        }

        public Criteria andActionNotIn(List<String> values) {
            addCriterion("action not in", values, "action");
            return (Criteria) this;
        }

        public Criteria andActionBetween(String value1, String value2) {
            addCriterion("action between", value1, value2, "action");
            return (Criteria) this;
        }

        public Criteria andActionNotBetween(String value1, String value2) {
            addCriterion("action not between", value1, value2, "action");
            return (Criteria) this;
        }

        public Criteria andLogCodeIsNull() {
            addCriterion("log_code is null");
            return (Criteria) this;
        }

        public Criteria andLogCodeIsNotNull() {
            addCriterion("log_code is not null");
            return (Criteria) this;
        }

        public Criteria andLogCodeEqualTo(String value) {
            addCriterion("log_code =", value, "logCode");
            return (Criteria) this;
        }

        public Criteria andLogCodeNotEqualTo(String value) {
            addCriterion("log_code <>", value, "logCode");
            return (Criteria) this;
        }

        public Criteria andLogCodeGreaterThan(String value) {
            addCriterion("log_code >", value, "logCode");
            return (Criteria) this;
        }

        public Criteria andLogCodeGreaterThanOrEqualTo(String value) {
            addCriterion("log_code >=", value, "logCode");
            return (Criteria) this;
        }

        public Criteria andLogCodeLessThan(String value) {
            addCriterion("log_code <", value, "logCode");
            return (Criteria) this;
        }

        public Criteria andLogCodeLessThanOrEqualTo(String value) {
            addCriterion("log_code <=", value, "logCode");
            return (Criteria) this;
        }

        public Criteria andLogCodeLike(String value) {
            addCriterion("log_code like", value, "logCode");
            return (Criteria) this;
        }

        public Criteria andLogCodeNotLike(String value) {
            addCriterion("log_code not like", value, "logCode");
            return (Criteria) this;
        }

        public Criteria andLogCodeIn(List<String> values) {
            addCriterion("log_code in", values, "logCode");
            return (Criteria) this;
        }

        public Criteria andLogCodeNotIn(List<String> values) {
            addCriterion("log_code not in", values, "logCode");
            return (Criteria) this;
        }

        public Criteria andLogCodeBetween(String value1, String value2) {
            addCriterion("log_code between", value1, value2, "logCode");
            return (Criteria) this;
        }

        public Criteria andLogCodeNotBetween(String value1, String value2) {
            addCriterion("log_code not between", value1, value2, "logCode");
            return (Criteria) this;
        }

        public Criteria andLogMsgIsNull() {
            addCriterion("log_msg is null");
            return (Criteria) this;
        }

        public Criteria andLogMsgIsNotNull() {
            addCriterion("log_msg is not null");
            return (Criteria) this;
        }

        public Criteria andLogMsgEqualTo(String value) {
            addCriterion("log_msg =", value, "logMsg");
            return (Criteria) this;
        }

        public Criteria andLogMsgNotEqualTo(String value) {
            addCriterion("log_msg <>", value, "logMsg");
            return (Criteria) this;
        }

        public Criteria andLogMsgGreaterThan(String value) {
            addCriterion("log_msg >", value, "logMsg");
            return (Criteria) this;
        }

        public Criteria andLogMsgGreaterThanOrEqualTo(String value) {
            addCriterion("log_msg >=", value, "logMsg");
            return (Criteria) this;
        }

        public Criteria andLogMsgLessThan(String value) {
            addCriterion("log_msg <", value, "logMsg");
            return (Criteria) this;
        }

        public Criteria andLogMsgLessThanOrEqualTo(String value) {
            addCriterion("log_msg <=", value, "logMsg");
            return (Criteria) this;
        }

        public Criteria andLogMsgLike(String value) {
            addCriterion("log_msg like", value, "logMsg");
            return (Criteria) this;
        }

        public Criteria andLogMsgNotLike(String value) {
            addCriterion("log_msg not like", value, "logMsg");
            return (Criteria) this;
        }

        public Criteria andLogMsgIn(List<String> values) {
            addCriterion("log_msg in", values, "logMsg");
            return (Criteria) this;
        }

        public Criteria andLogMsgNotIn(List<String> values) {
            addCriterion("log_msg not in", values, "logMsg");
            return (Criteria) this;
        }

        public Criteria andLogMsgBetween(String value1, String value2) {
            addCriterion("log_msg between", value1, value2, "logMsg");
            return (Criteria) this;
        }

        public Criteria andLogMsgNotBetween(String value1, String value2) {
            addCriterion("log_msg not between", value1, value2, "logMsg");
            return (Criteria) this;
        }

        public Criteria andBusinessDataIsNull() {
            addCriterion("business_data is null");
            return (Criteria) this;
        }

        public Criteria andBusinessDataIsNotNull() {
            addCriterion("business_data is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessDataEqualTo(String value) {
            addCriterion("business_data =", value, "businessData");
            return (Criteria) this;
        }

        public Criteria andBusinessDataNotEqualTo(String value) {
            addCriterion("business_data <>", value, "businessData");
            return (Criteria) this;
        }

        public Criteria andBusinessDataGreaterThan(String value) {
            addCriterion("business_data >", value, "businessData");
            return (Criteria) this;
        }

        public Criteria andBusinessDataGreaterThanOrEqualTo(String value) {
            addCriterion("business_data >=", value, "businessData");
            return (Criteria) this;
        }

        public Criteria andBusinessDataLessThan(String value) {
            addCriterion("business_data <", value, "businessData");
            return (Criteria) this;
        }

        public Criteria andBusinessDataLessThanOrEqualTo(String value) {
            addCriterion("business_data <=", value, "businessData");
            return (Criteria) this;
        }

        public Criteria andBusinessDataLike(String value) {
            addCriterion("business_data like", value, "businessData");
            return (Criteria) this;
        }

        public Criteria andBusinessDataNotLike(String value) {
            addCriterion("business_data not like", value, "businessData");
            return (Criteria) this;
        }

        public Criteria andBusinessDataIn(List<String> values) {
            addCriterion("business_data in", values, "businessData");
            return (Criteria) this;
        }

        public Criteria andBusinessDataNotIn(List<String> values) {
            addCriterion("business_data not in", values, "businessData");
            return (Criteria) this;
        }

        public Criteria andBusinessDataBetween(String value1, String value2) {
            addCriterion("business_data between", value1, value2, "businessData");
            return (Criteria) this;
        }

        public Criteria andBusinessDataNotBetween(String value1, String value2) {
            addCriterion("business_data not between", value1, value2, "businessData");
            return (Criteria) this;
        }

        public Criteria andExceptionIsNull() {
            addCriterion("exception is null");
            return (Criteria) this;
        }

        public Criteria andExceptionIsNotNull() {
            addCriterion("exception is not null");
            return (Criteria) this;
        }

        public Criteria andExceptionEqualTo(String value) {
            addCriterion("exception =", value, "exception");
            return (Criteria) this;
        }

        public Criteria andExceptionNotEqualTo(String value) {
            addCriterion("exception <>", value, "exception");
            return (Criteria) this;
        }

        public Criteria andExceptionGreaterThan(String value) {
            addCriterion("exception >", value, "exception");
            return (Criteria) this;
        }

        public Criteria andExceptionGreaterThanOrEqualTo(String value) {
            addCriterion("exception >=", value, "exception");
            return (Criteria) this;
        }

        public Criteria andExceptionLessThan(String value) {
            addCriterion("exception <", value, "exception");
            return (Criteria) this;
        }

        public Criteria andExceptionLessThanOrEqualTo(String value) {
            addCriterion("exception <=", value, "exception");
            return (Criteria) this;
        }

        public Criteria andExceptionLike(String value) {
            addCriterion("exception like", value, "exception");
            return (Criteria) this;
        }

        public Criteria andExceptionNotLike(String value) {
            addCriterion("exception not like", value, "exception");
            return (Criteria) this;
        }

        public Criteria andExceptionIn(List<String> values) {
            addCriterion("exception in", values, "exception");
            return (Criteria) this;
        }

        public Criteria andExceptionNotIn(List<String> values) {
            addCriterion("exception not in", values, "exception");
            return (Criteria) this;
        }

        public Criteria andExceptionBetween(String value1, String value2) {
            addCriterion("exception between", value1, value2, "exception");
            return (Criteria) this;
        }

        public Criteria andExceptionNotBetween(String value1, String value2) {
            addCriterion("exception not between", value1, value2, "exception");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels =", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels <>", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels >", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels >=", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels <", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels <=", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels like", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels not like", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("order_channels in", values, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("order_channels not in", values, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("order_channels between", value1, value2, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("order_channels not between", value1, value2, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andBizIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("biz_id =", value, "bizId");
            }
            return (Criteria) this;
        }

        public Criteria andBizIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("biz_id <>", value, "bizId");
            }
            return (Criteria) this;
        }

        public Criteria andBizIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("biz_id >", value, "bizId");
            }
            return (Criteria) this;
        }

        public Criteria andBizIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("biz_id >=", value, "bizId");
            }
            return (Criteria) this;
        }

        public Criteria andBizIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("biz_id <", value, "bizId");
            }
            return (Criteria) this;
        }

        public Criteria andBizIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("biz_id <=", value, "bizId");
            }
            return (Criteria) this;
        }

        public Criteria andBizIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("biz_id like", value, "bizId");
            }
            return (Criteria) this;
        }

        public Criteria andBizIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("biz_id not like", value, "bizId");
            }
            return (Criteria) this;
        }

        public Criteria andBizIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("biz_id in", values, "bizId");
            }
            return (Criteria) this;
        }

        public Criteria andBizIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("biz_id not in", values, "bizId");
            }
            return (Criteria) this;
        }

        public Criteria andBizIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("biz_id between", value1, value2, "bizId");
            }
            return (Criteria) this;
        }

        public Criteria andBizIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("biz_id not between", value1, value2, "bizId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id =", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id <>", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id >", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id >=", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id <", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id <=", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id like", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("trace_id not like", value, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("trace_id in", values, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("trace_id not in", values, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("trace_id between", value1, value2, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andTraceIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("trace_id not between", value1, value2, "traceId");
            }
            return (Criteria) this;
        }

        public Criteria andLogLevelEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_level =", value, "logLevel");
            }
            return (Criteria) this;
        }

        public Criteria andLogLevelNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_level <>", value, "logLevel");
            }
            return (Criteria) this;
        }

        public Criteria andLogLevelGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_level >", value, "logLevel");
            }
            return (Criteria) this;
        }

        public Criteria andLogLevelGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_level >=", value, "logLevel");
            }
            return (Criteria) this;
        }

        public Criteria andLogLevelLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_level <", value, "logLevel");
            }
            return (Criteria) this;
        }

        public Criteria andLogLevelLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_level <=", value, "logLevel");
            }
            return (Criteria) this;
        }

        public Criteria andLogLevelLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_level like", value, "logLevel");
            }
            return (Criteria) this;
        }

        public Criteria andLogLevelNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_level not like", value, "logLevel");
            }
            return (Criteria) this;
        }

        public Criteria andLogLevelInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("log_level in", values, "logLevel");
            }
            return (Criteria) this;
        }

        public Criteria andLogLevelNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("log_level not in", values, "logLevel");
            }
            return (Criteria) this;
        }

        public Criteria andLogLevelBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("log_level between", value1, value2, "logLevel");
            }
            return (Criteria) this;
        }

        public Criteria andLogLevelNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("log_level not between", value1, value2, "logLevel");
            }
            return (Criteria) this;
        }

        public Criteria andActionEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("action =", value, "action");
            }
            return (Criteria) this;
        }

        public Criteria andActionNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("action <>", value, "action");
            }
            return (Criteria) this;
        }

        public Criteria andActionGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("action >", value, "action");
            }
            return (Criteria) this;
        }

        public Criteria andActionGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("action >=", value, "action");
            }
            return (Criteria) this;
        }

        public Criteria andActionLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("action <", value, "action");
            }
            return (Criteria) this;
        }

        public Criteria andActionLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("action <=", value, "action");
            }
            return (Criteria) this;
        }

        public Criteria andActionLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("action like", value, "action");
            }
            return (Criteria) this;
        }

        public Criteria andActionNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("action not like", value, "action");
            }
            return (Criteria) this;
        }

        public Criteria andActionInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("action in", values, "action");
            }
            return (Criteria) this;
        }

        public Criteria andActionNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("action not in", values, "action");
            }
            return (Criteria) this;
        }

        public Criteria andActionBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("action between", value1, value2, "action");
            }
            return (Criteria) this;
        }

        public Criteria andActionNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("action not between", value1, value2, "action");
            }
            return (Criteria) this;
        }

        public Criteria andLogCodeEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_code =", value, "logCode");
            }
            return (Criteria) this;
        }

        public Criteria andLogCodeNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_code <>", value, "logCode");
            }
            return (Criteria) this;
        }

        public Criteria andLogCodeGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_code >", value, "logCode");
            }
            return (Criteria) this;
        }

        public Criteria andLogCodeGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_code >=", value, "logCode");
            }
            return (Criteria) this;
        }

        public Criteria andLogCodeLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_code <", value, "logCode");
            }
            return (Criteria) this;
        }

        public Criteria andLogCodeLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_code <=", value, "logCode");
            }
            return (Criteria) this;
        }

        public Criteria andLogCodeLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_code like", value, "logCode");
            }
            return (Criteria) this;
        }

        public Criteria andLogCodeNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_code not like", value, "logCode");
            }
            return (Criteria) this;
        }

        public Criteria andLogCodeInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("log_code in", values, "logCode");
            }
            return (Criteria) this;
        }

        public Criteria andLogCodeNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("log_code not in", values, "logCode");
            }
            return (Criteria) this;
        }

        public Criteria andLogCodeBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("log_code between", value1, value2, "logCode");
            }
            return (Criteria) this;
        }

        public Criteria andLogCodeNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("log_code not between", value1, value2, "logCode");
            }
            return (Criteria) this;
        }

        public Criteria andLogMsgEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_msg =", value, "logMsg");
            }
            return (Criteria) this;
        }

        public Criteria andLogMsgNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_msg <>", value, "logMsg");
            }
            return (Criteria) this;
        }

        public Criteria andLogMsgGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_msg >", value, "logMsg");
            }
            return (Criteria) this;
        }

        public Criteria andLogMsgGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_msg >=", value, "logMsg");
            }
            return (Criteria) this;
        }

        public Criteria andLogMsgLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_msg <", value, "logMsg");
            }
            return (Criteria) this;
        }

        public Criteria andLogMsgLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_msg <=", value, "logMsg");
            }
            return (Criteria) this;
        }

        public Criteria andLogMsgLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_msg like", value, "logMsg");
            }
            return (Criteria) this;
        }

        public Criteria andLogMsgNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("log_msg not like", value, "logMsg");
            }
            return (Criteria) this;
        }

        public Criteria andLogMsgInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("log_msg in", values, "logMsg");
            }
            return (Criteria) this;
        }

        public Criteria andLogMsgNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("log_msg not in", values, "logMsg");
            }
            return (Criteria) this;
        }

        public Criteria andLogMsgBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("log_msg between", value1, value2, "logMsg");
            }
            return (Criteria) this;
        }

        public Criteria andLogMsgNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("log_msg not between", value1, value2, "logMsg");
            }
            return (Criteria) this;
        }

        public Criteria andBusinessDataEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("business_data =", value, "businessData");
            }
            return (Criteria) this;
        }

        public Criteria andBusinessDataNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("business_data <>", value, "businessData");
            }
            return (Criteria) this;
        }

        public Criteria andBusinessDataGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("business_data >", value, "businessData");
            }
            return (Criteria) this;
        }

        public Criteria andBusinessDataGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("business_data >=", value, "businessData");
            }
            return (Criteria) this;
        }

        public Criteria andBusinessDataLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("business_data <", value, "businessData");
            }
            return (Criteria) this;
        }

        public Criteria andBusinessDataLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("business_data <=", value, "businessData");
            }
            return (Criteria) this;
        }

        public Criteria andBusinessDataLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("business_data like", value, "businessData");
            }
            return (Criteria) this;
        }

        public Criteria andBusinessDataNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("business_data not like", value, "businessData");
            }
            return (Criteria) this;
        }

        public Criteria andBusinessDataInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("business_data in", values, "businessData");
            }
            return (Criteria) this;
        }

        public Criteria andBusinessDataNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("business_data not in", values, "businessData");
            }
            return (Criteria) this;
        }

        public Criteria andBusinessDataBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("business_data between", value1, value2, "businessData");
            }
            return (Criteria) this;
        }

        public Criteria andBusinessDataNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("business_data not between", value1, value2, "businessData");
            }
            return (Criteria) this;
        }

        public Criteria andExceptionEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("exception =", value, "exception");
            }
            return (Criteria) this;
        }

        public Criteria andExceptionNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("exception <>", value, "exception");
            }
            return (Criteria) this;
        }

        public Criteria andExceptionGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("exception >", value, "exception");
            }
            return (Criteria) this;
        }

        public Criteria andExceptionGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("exception >=", value, "exception");
            }
            return (Criteria) this;
        }

        public Criteria andExceptionLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("exception <", value, "exception");
            }
            return (Criteria) this;
        }

        public Criteria andExceptionLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("exception <=", value, "exception");
            }
            return (Criteria) this;
        }

        public Criteria andExceptionLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("exception like", value, "exception");
            }
            return (Criteria) this;
        }

        public Criteria andExceptionNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("exception not like", value, "exception");
            }
            return (Criteria) this;
        }

        public Criteria andExceptionInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("exception in", values, "exception");
            }
            return (Criteria) this;
        }

        public Criteria andExceptionNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("exception not in", values, "exception");
            }
            return (Criteria) this;
        }

        public Criteria andExceptionBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("exception between", value1, value2, "exception");
            }
            return (Criteria) this;
        }

        public Criteria andExceptionNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("exception not between", value1, value2, "exception");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark =", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark <>", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark >", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark >=", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark <", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark <=", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark like", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark not like", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("remark in", values, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("remark not in", values, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("remark between", value1, value2, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("remark not between", value1, value2, "remark");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *渠道
         */
        ORDERCHANNELS("order_channels"),
        /**
         *业务ID
         */
        BIZID("biz_id"),
        /**
         *鹰眼ID
         */
        TRACEID("trace_id"),
        /**
         *日志级别
         */
        LOGLEVEL("log_level"),
        /**
         *日志动作标识
         */
        ACTION("action"),
        /**
         *日志代码
         */
        LOGCODE("log_code"),
        /**
         *日志描述
         */
        LOGMSG("log_msg"),
        /**
         *业务数据
         */
        BUSINESSDATA("business_data"),
        /**
         *异常信息
         */
        EXCEPTION("exception"),
        /**
         *备注
         */
        REMARK("remark");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}