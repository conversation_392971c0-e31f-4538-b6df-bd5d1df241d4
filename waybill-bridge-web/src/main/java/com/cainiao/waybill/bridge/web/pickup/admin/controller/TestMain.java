package com.cainiao.waybill.bridge.web.pickup.admin.controller;

import java.util.List;

import com.google.common.collect.Lists;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Request.Builder;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * <AUTHOR> zouping.fzp
 * @Classname TestMain
 * @Description
 * @Date 2022/11/18 11:12 下午
 * @Version 1.0
 */
public class TestMain {

    private static List<String> list = Lists.newArrayList(
        "YT2252369294318",
        "YT2252887985119",
        "YT2252426557727",
        "YT2253021229208",
        "YT2252588390994",
        "YT2252646751758",
        "YT2252204649400",
        "YT2252770369723",
        "YT2252791970904",
        "YT2252814141223",
        "YT2253434631611",
        "YT2252950970682",
        "YT2252713061877",
        "YT2253451190911",
        "YT2254299695419",
        "YT2253925965846",
        "YT2253958125205",
        "YT2253506947355",
        "YT2254570941049",
        "YT2253762108422",
        "YT2254831147538",
        "YT2253808546962",
        "YT2254341455641",
        "YT2254858873247",
        "YT2254383107339",
        "YT2254983286213",
        "YT2254469455182",
        "YT2254017952357",
        "YT2254608525531",
        "YT2254168740182",
        "YT2255312165724",
        "YT2255358966623",
        "YT2254368738703",
        "YT2254899975531",
        "YT2254995470622",
        "YT2255040857038",
        "YT2255616357311",
        "YT2255669392514",
        "YT2255185204435",
        "YT2254653505355",
        "YT2254653538086",
        "YT2255772178819",
        "YT2254749851205",
        "YT2255815951884",
        "YT2254802730733",
        "YT2255335884286",
        "TEMP_2wXCzVtl8bbmYUx3",
        "YT2254885240382",
        "YT2255429290782",
        "YT2255436566634",
        "YT2255965436716",
        "YT2255445345831",
        "YT2256086670050",
        "YT2255089867307",
        "YT2255100143822",
        "YT2255639240966",
        "YT2255108224328",
        "YT2256190608964",
        "YT2255128253501",
        "YT2255150822287",
        "YT2255686604026",
        "YT2255691996800",
        "YT2256234749749",
        "YT2256364031421",
        "YT2256369261977",
        "YT2256623568639",
        "YT2256650049377",
        "YT2256650427015",
        "YT2256653839813",
        "YT2255936534613",
        "YT2256728739031",
        "YT2256778978299",
        "YT2256789881119",
        "YT2256855588909",
        "YT2256885767716",
        "YT2257020986438",
        "YT2257041581726",
        "YT2257050014719",
        "YT2257064179200",
        "YT2257072234575",
        "YT2257110403509",
        "YT2257148220090",
        "YT2257211335565",
        "YT2256224351937",
        "YT2257246053042",
        "YT2256266366922",
        "YT2256269138391",
        "YT2257517993457",
        "YT2257575243055",
        "YT2256392620705",
        "YT2257586862322",
        "YT2257608148508",
        "YT2257617759620",
        "YT2257633312559",
        "YT2256439088331",
        "YT2257705530955",
        "YT2257705875927",
        "YT2257811545282",
        "YT2256542928582",
        "YT2256558397431",
        "YT2257971519368",
        "YT2257994964633",
        "YT2258015853699",
        "YT2258016857153",
        "YT2258017157420",
        "YT2258036583328",
        "YT2258155228822",
        "YT2258367204061",
        "YT2258394668188",
        "YT2258417627705",
        "YT2256814561123",
        "YT2256816429836",
        "YT2258421697836",
        "YT2256819660518",
        "YT2258473035628",
        "YT2256842485702",
        "YT2256897830652",
        "YT2258672098284",
        "YT2256960670432",
        "YT2257017271122",
        "YT2258845511182",
        "YT2258965792854",
        "YT2257096026238",
        "YT2257100442584",
        "YT2259021426613",
        "YT2259070935659",
        "YT2259224091961",
        "YT2259227426422",
        "YT2257248952722",
        "YT2259367455347",
        "YT2257361811263",
        "YT2257380525834",
        "YT2257384838664",
        "YT2259583548339",
        "YT2259598020684",
        "YT2257411326886",
        "YT2257414861116",
        "YT2257415457237",
        "YT2257418405056",
        "YT2259642610241",
        "YT2259647143886",
        "YT2259711865892",
        "YT2259789758133",
        "YT2259840037591",
        "YT2259917532037",
        "YT2259940603235",
        "YT2260030072273",
        "YT2260050294902",
        "YT2260071531966",
        "YT2257656888867",
        "YT2260105271461",
        "YT2257678130066",
        "YT2260246633285",
        "YT2257745772854",
        "YT2257779554022",
        "YT2260348835122",
        "YT2260479300978",
        "YT2260507984868",
        "YT2260508479282",
        "YT2260532667645",
        "YT2257894970392",
        "YT2257942645700",
        "YT2260776465063",
        "YT2260790109044",
        "TEMP_IsGBUAa61EgbLLBP",
        "TEMP_bKxZp7SE5Mms2Zqu",
        "YT2260850216431",
        "YT2258082482399",
        "YT2260958346627",
        "YT2261076882327",
        "YT2261209176669",
        "YT2261336554527",
        "YT2258288885348",
        "YT2258324997845",
        "YT2261584673748",
        "YT2261615507705",
        "YT2261685110177"
        );

    public static final MediaType json=MediaType.parse("application/json;charset=utf-8");

    private static String url = "https://waybill.bridge.cainiao.com/platform/order/detail/exception/cancel?mailNo=%mailNo%&cancelType=4&reason=%E8%B6%85%E6%97%B6%E6%8F%BD%E6%94%B6%EF%BC%8C%E9%A1%BE%E5%AE%A2%E8%B5%9E%E5%90%8C%E5%8F%96%E6%B6%88";

    public static void main(String[] args) throws Exception{

        //用来请求的http的url地址

        OkHttpClient okHttpClient = new OkHttpClient();

        //list = Lists.newArrayList("YT2253759079068");

        for (String mailNo : list) {
            Request request = new Builder().url(url.replace("%mailNo%", mailNo))
                .addHeader("cookie", "cna=n+AqGS+moS4CASp4SZrjLJdZ; Hm_lvt_31d77c9cbdbd359bfc3ece6be37715fa=**********; enc=49R6QrMy78bsV5D%2BYCJzedAZmK%2FF1qfzKv5JDgx5OphwzDm%2FN2QhKDgIPYeq5oyRc8DLLMndjZ0BuJmN9QDeXw%3D%3D; SSO_EMPID_HASH_V2=3b2760344905a372824ade9700be15ba; x-hng=lang=zh-CN&language=zh-CN; SSO_LANG_V2=ZH-CN; lid=b%E5%95%86%E5%AE%B6%E6%B5%8B%E8%AF%95%E5%B8%90%E5%8F%B7230; SSO_BU_HASH_V2=1e387a787818f77b803a555735045ffa; arms_uid=0ab8fce4-db1c-4f5a-aa3a-38d46c006642; cn_account_lang=zh; _hvn_login=21; SL34syaT=8C5D983BCBE6B9EFC7610EFF8ADB68A1; cpcode=null; u9sJLjkn=7b88b5fe18471c7f2b2c5f8528905b6b; xlly_s=1; lvt=*************; cncc=1fbc9a0e84f8f24393f4040f4d4d2203; TwAhx8HL=C6BCB215B673113B88DEFEF22D0D5CD441D3D197ABA710AE36592A8446521ABF9C4CCA0E18C850E281E15B02F580D2F543ADC01E6F215A474855C937BA1CAFD20745D92E4980BFE27EF8940B52A60A8ECB940D2A4E1969C0B253FF1DA4A5DBCD5024AADAA13B743A56C9DEF61120BDA2E0616CA3FD1B84D5A330A9C3DD430F49DFACAA2BB6D2B0D577F3E18FFBF1F8508B4E5FC65303D9BF59E9A24F19977BD21A2C2FF377D762272DE3D6F2A3734933D30E3EBD4AF734A3978DF2CC154896D5; accountId=MjIwOTc2MzY2MDkyNw==; isLogin=true; account=MTU2NTgwMDE0ODY=; tfstk=cI-cBnwfF_NIoqm9NIsjvgsJDt7caR3Vth-J4Y7LVZ_2K56V3sbYzy-d-YfqOjz1.; l=fBOCN6m7j3q-nHhfXO5Bhurza779BQAfcFVzaNbMiIEGC6Whd7vT_o-QmFkrjCtRR8XPg2Ye4lYdQ4pTveq7-PHbfsKUH4Rx3ApJQeLQlhGUy; isg=BNfX6Cy3eLfu8v8H7tqtklykZk0hHKt-k2dW0SkGgqZQWP-aMu0oz5Y-uvjGt4P2")
                .build();
            try (Response response = okHttpClient.newCall(request).execute()) {
                ResponseBody body = response.body();
                if (response.isSuccessful()) {
                    System.out.println("success: " + mailNo);
                } else {
                    System.out.println("fail: " + mailNo);
                }
            }
            Thread.sleep(2000);
        }



    }
}
