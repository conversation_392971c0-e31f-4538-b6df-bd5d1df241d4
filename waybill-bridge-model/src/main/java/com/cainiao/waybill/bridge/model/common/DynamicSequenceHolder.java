package com.cainiao.waybill.bridge.model.common;

import com.cainiao.waybill.common.util.StringUtil;
import com.google.common.collect.Sets;
import com.taobao.cainiao.waybill.base.Loggers;
import com.taobao.cainiao.waybill.base.WaybillPreconditions;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant;
import com.taobao.cainiao.waybill.exception.WaybillBaseException;
import com.taobao.cainiao.waybill.exception.WaybillValidatorException;
import com.taobao.cainiao.waybill.exception.util.Exceptions;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import com.taobao.tddl.client.sequence.Sequence;
import com.taobao.tddl.client.sequence.exception.SequenceException;
import com.taobao.tddl.client.sequence.impl.GroupSequence;
import com.taobao.tddl.client.sequence.impl.GroupSequenceDao;
import com.taobao.tddl.common.exception.TddlException;
import com.taobao.tddl.group.jdbc.TGroupDataSource;
import com.taobao.util.CollectionUtil;
import com.taobao.util.MapUtil;

import java.sql.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;


public class DynamicSequenceHolder {

    private TddlSequenceConfig tddlSequenceConfig;

    private Map<String, Sequence>         sequenceMap = new ConcurrentHashMap<String, Sequence>();

    private Map<String, TGroupDataSource> dataSourceMap;

    private GroupSequenceDao groupSequenceDao;

    public void init() throws Throwable {
        checkConfig();
        initGroupSequenceDao();
        initDataSource();
        loadAllSequenceFromDB();
        Diamond.addListener("sequence_refresh", "waybill", new ManagerListenerAdapter() {
            @Override
            public void receiveConfigInfo(String config) {
                if ("true".equalsIgnoreCase(config)) {
                    try {
                        loadAllSequenceFromDB();
                    } catch (Throwable e) {
                        Loggers.sequenceLogger.error("fail to reload sequence", e);
                    }
                }
            }
        });
    }

    private void initGroupSequenceDao() throws TddlException {
        if (groupSequenceDao == null) {
            groupSequenceDao = new GroupSequenceDao();
            groupSequenceDao.setAppName(tddlSequenceConfig.getAppName());
            groupSequenceDao.setDbGroupKeys(tddlSequenceConfig.getDbGroupKeys());
            groupSequenceDao.setDscount(tddlSequenceConfig.getDbGroupKeys().size());
            groupSequenceDao.setInnerStep(tddlSequenceConfig.getInnerStep());
            groupSequenceDao.setAdjust(tddlSequenceConfig.isAdjust());
            groupSequenceDao.setRetryTimes(tddlSequenceConfig.getRetryTimes());
            groupSequenceDao.setTableName(tddlSequenceConfig.getTableName());
            groupSequenceDao.setNameColumnName(tddlSequenceConfig.getNameColumnName());
            groupSequenceDao.setValueColumnName(tddlSequenceConfig.getValueColumnName());
            groupSequenceDao.setGmtModifiedColumnName(tddlSequenceConfig.getGmtModifiedColumnName());
            groupSequenceDao.setRetryTimes(tddlSequenceConfig.getRetryTimes());
            groupSequenceDao.init();
        }
    }

    private void checkConfig() throws WaybillValidatorException {
        Loggers.sequenceLogger.error("config is {}", tddlSequenceConfig);
        WaybillPreconditions.checkNotNull(tddlSequenceConfig, WaybillErrorConstant.WaybillSequenceException.TDDL_SEQUENCE_CONFIG_NULL);
        WaybillPreconditions.checkStringNotBlank(tddlSequenceConfig.getAppName(), WaybillErrorConstant.WaybillSequenceException.TDDL_SEQUENCE_CONFIG_APP_NAME_NULL);
        WaybillPreconditions.checkCollectionNotEmpty(tddlSequenceConfig.getDbGroupKeys(), WaybillErrorConstant.WaybillSequenceException.TDDL_SEQUENCE_CONFIG_DBGROUP_KEYS_EMPTY);
        for (String key : tddlSequenceConfig.getDbGroupKeys()) {
            WaybillPreconditions.checkStringNotBlank(key, WaybillErrorConstant.WaybillSequenceException.TDDL_SEQUENCE_CONFIG_ONE_DBGROUP_KEY_NULL);
        }
        WaybillPreconditions.checkNotNull(tddlSequenceConfig.getInnerStep(), WaybillErrorConstant.WaybillSequenceException.TDDL_SEQUENCE_CONFIG_INNER_STEP_NULL);
        WaybillPreconditions.checkStringNotBlank(tddlSequenceConfig.getTableName(), WaybillErrorConstant.WaybillSequenceException.TDDL_SEQUENCE_CONFIG_TABLE_NAME_NULL);
        WaybillPreconditions.checkStringNotBlank(tddlSequenceConfig.getNameColumnName(), WaybillErrorConstant.WaybillSequenceException.TDDL_SEQUENCE_CONFIG_NAME_COLUMN_NULL);
        WaybillPreconditions.checkStringNotBlank(tddlSequenceConfig.getValueColumnName(), WaybillErrorConstant.WaybillSequenceException.TDDL_SEQUENCE_CONFIG_VALUE_COLUMN_NULL);
        WaybillPreconditions.checkStringNotBlank(tddlSequenceConfig.getGmtModifiedColumnName(),
                WaybillErrorConstant.WaybillSequenceException.TDDL_SEQUENCE_CONFIG_GMT_MODIFIED_NULL);
        WaybillPreconditions.checkNotNull(tddlSequenceConfig.getRetryTimes(), WaybillErrorConstant.WaybillSequenceException.TDDL_SEQUENCE_CONFIG_RETRY_TIMES_ILEGAL);
    }


    private void initDataSource() throws Throwable {
        try {
            dataSourceMap = new ConcurrentHashMap<String, TGroupDataSource>();
            for (String dbGroupKey : tddlSequenceConfig.getDbGroupKeys()) {
                TGroupDataSource tGroupDataSource = new TGroupDataSource(dbGroupKey, tddlSequenceConfig.getAppName());
                tGroupDataSource.init();
                dataSourceMap.put(dbGroupKey, tGroupDataSource);
            }
        } catch (Throwable t) {
            Loggers.sequenceLogger.error("fail to init dataSource when get sequenceName from db", t);
            throw t;
        }
    }

    private void loadAllSequenceFromDB() throws WaybillValidatorException, WaybillBaseException {
        Set<String> sequenceNameList = getAllSequenceName();
        Loggers.sequenceLogger.error("final legal sequenceNameList is :{}", sequenceNameList);
        if (CollectionUtil.isEmpty(sequenceNameList)) {
            throw Exceptions.newWaybillValidatorException(WaybillErrorConstant.WaybillSequenceException.SEQUENCE_NAME_LIST_EMPTY);
        }
        ConcurrentHashMap<String, Sequence> newSequenceMap = new ConcurrentHashMap<String, Sequence>();
        for (String sequenceName : sequenceNameList) {
            GroupSequence sequence = createSequenceByName(sequenceName);
            newSequenceMap.put(sequenceName, sequence);
        }
        sequenceMap = newSequenceMap;
    }

    private GroupSequence createSequenceByName(String sequenceName) throws WaybillValidatorException, WaybillBaseException {
        if (StringUtil.isBlank(sequenceName)) {
            throw Exceptions.newWaybillValidatorException(WaybillErrorConstant.WaybillSequenceException.SEQUENCE_NAME_NULL);
        }
        GroupSequence sequence = new GroupSequence();
        sequence.setSequenceDao(groupSequenceDao);
        sequence.setName(sequenceName);
        try {
            sequence.init();
            return sequence;
        } catch (SQLException e) {
            throw Exceptions.newWaybillBaseException(WaybillErrorConstant.WaybillSequenceException.CREATE_SEQUENCE_ERROR, e, sequenceName);
        }
    }

    private Set<String> getAllSequenceName() throws WaybillBaseException {
        if (MapUtil.isEmpty(dataSourceMap)) {
            Loggers.sequenceLogger.error("dataSourceMap empty,cannot find sequenceName from dataSource");
            throw Exceptions.newWaybillBaseException(WaybillErrorConstant.WaybillSequenceException.SEQUENCE_DATA_SOURCE_EMPTY, getTddlSequenceConfig());
        }
        Set<String> sequenceNameList = null;
        for (TGroupDataSource tGroupDataSource : dataSourceMap.values()) {
            Set<String> sequenceNamesInCurrentDataSource = new HashSet<String>();
            Connection conn = null;
            PreparedStatement stmt = null;
            ResultSet rs = null;

            try {
                conn = tGroupDataSource.getConnection();
                stmt = conn.prepareStatement(getSelectSql());
                rs = stmt.executeQuery();
                while (rs.next()) {
                    sequenceNamesInCurrentDataSource.add(rs.getString(1));
                }
                Loggers.sequenceLogger.error("sequence in {} is: {}", tGroupDataSource.getDbGroupKey(), sequenceNamesInCurrentDataSource);

            } catch (SQLException e) {
                Loggers.sequenceLogger.error("fail to select sequenceName from db,dbKey:{}", tGroupDataSource.getDbGroupKey(), e);
                throw Exceptions.newWaybillBaseException(WaybillErrorConstant.WaybillSequenceException.SELECT_SEQUENCE_NAME_ERROR, e);
            } finally {
                closeDbResource(rs, stmt, conn);
            }

            if (sequenceNameList == null) {
                sequenceNameList = new HashSet<String>(sequenceNamesInCurrentDataSource);
                Loggers.sequenceLogger.error("sequence in first dataSource:{}", sequenceNameList);
            } else {
                sequenceNameList = Sets.intersection(sequenceNameList, sequenceNamesInCurrentDataSource);
                Loggers.sequenceLogger.error("after intersect:{}", sequenceNameList);
            }
        }
        return sequenceNameList;


    }

    private void closeDbResource(ResultSet rs, Statement stmt, Connection conn) {
        closeResultSet(rs);
        closeStatement(stmt);
        closeConnection(conn);
    }


    protected static void closeResultSet(ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                Loggers.sequenceLogger.error("could not close JDBC resultset", e);
            } catch (Throwable e) {
                Loggers.sequenceLogger.error("unexpected exception on closing JDBC resultset", e);
            }
        }
    }

    protected static void closeStatement(Statement stmt) {
        if (stmt != null) {
            try {
                stmt.close();
            } catch (SQLException e) {
                Loggers.sequenceLogger.error("could not close JDBC statement", e);
            } catch (Throwable e) {
                Loggers.sequenceLogger.error("unexpected exception on closing JDBC statement", e);
            }
        }
    }

    protected static void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                Loggers.sequenceLogger.error("could not close JDBC connection", e);
            } catch (Throwable e) {
                Loggers.sequenceLogger.error("unexpected exception on closing JDBC connection", e);
            }
        }
    }

    private String getSelectSql() {
        return "select distinct " + tddlSequenceConfig.getNameColumnName() + " from " + tddlSequenceConfig.getTableName();
    }


    public List<Long> generateSequenceBySequenceName(String sequenceName, int sequenceCount) throws WaybillBaseException, WaybillValidatorException {
        if (StringUtil.isBlank(sequenceName)) {
            throw Exceptions.newWaybillValidatorException(WaybillErrorConstant.WaybillSequenceException.SEQUENCE_NAME_NULL);
        }
        Sequence sequence = sequenceMap.get(sequenceName);
        if (sequence == null) {
            throw Exceptions.newWaybillBaseException(WaybillErrorConstant.WaybillSequenceException.SEQUENCE_NON_EXSISTENCE, sequenceName);
        }
        try {
            List<Long> ids = new ArrayList<Long>();
            for (int i = 0; i < sequenceCount; i++) {
                ids.add(sequence.nextValue());
            }
            return ids;
        } catch (SequenceException e) {
            throw Exceptions.newWaybillBaseException(WaybillErrorConstant.WaybillSequenceException.GET_SEQUENCE_ERROR, sequenceName);
        }
    }

    public TddlSequenceConfig getTddlSequenceConfig() {
        return tddlSequenceConfig;
    }

    public void setTddlSequenceConfig(TddlSequenceConfig tddlSequenceConfig) {
        this.tddlSequenceConfig = tddlSequenceConfig;
    }
}
