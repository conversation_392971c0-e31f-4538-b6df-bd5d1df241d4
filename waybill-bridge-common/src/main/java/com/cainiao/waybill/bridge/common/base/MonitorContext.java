package com.cainiao.waybill.bridge.common.base;

import java.util.Date;

import lombok.Data;

@Data
public class MonitorContext {
    /**
     * 场景
     */
    private String scenario;
    /**
     * 子业务code
     */
    private String subCode;
    /**
     * 横向code
     */
    private String code;
    /**
     * 开始时间
     */
    private Date startTime=new Date();
    /**
     * 来源
     */
    private String fromApp;
    /**
     * 用户数据
     */
    private Object userData;

}
