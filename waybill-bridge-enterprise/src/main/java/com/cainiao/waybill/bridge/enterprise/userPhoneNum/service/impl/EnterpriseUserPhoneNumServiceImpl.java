package com.cainiao.waybill.bridge.enterprise.userPhoneNum.service.impl;

import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.userPhoneNum.requset.EnterpriseUserPhoneNumRequest;
import com.cainiao.waybill.bridge.enterprise.userPhoneNum.service.EnterpriseUserPhoneNumService;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterpriseUserPhoneNumConvert;
import com.cainiao.waybill.bridge.enterprise.utils.PhoneNumberValidator;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserPhoneNumDO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserPhoneNumDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserPhoneNumMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/14
 **/
@Service
public class EnterpriseUserPhoneNumServiceImpl implements EnterpriseUserPhoneNumService {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private WaybillBridgeEnterpriseUserPhoneNumMapper enterpriseUserPhoneNumMapper;

    @Autowired
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;

    @Override
    public int addPhoneNum(EnterpriseUserPhoneNumRequest userPhoneNumRequest) {
        if (!PhoneNumberValidator.isValidPhoneNumber(userPhoneNumRequest.getPhoneNum()) ||
                StringUtils.isEmpty(PhoneNumberValidator.extractPhoneNumber(userPhoneNumRequest.getPhoneNum()))) {
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "当前添加的手机号不符合规范");
        }
        WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectByUserId(userPhoneNumRequest.getUserId(), userPhoneNumRequest.getCorpId());
        if (waybillBridgeEnterpriseUserInfoDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "当前用户不存在");
        }
        List<String> phoneNumList = enterpriseUserPhoneNumMapper.selectPhoneNumByUserId(userPhoneNumRequest.getUserId());
        if (CollectionUtils.isNotEmpty(phoneNumList) && phoneNumList.size() == 5) {
            LOGGER.error("addPhoneNum error: user phoneNumList already exist 5, userId:{}", userPhoneNumRequest.getUserId());
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "当前用户已存在5个联系电话");
        }
        if (phoneNumList.contains(PhoneNumberValidator.extractPhoneNumber(userPhoneNumRequest.getPhoneNum()))) {
            LOGGER.error("addPhoneNum error: user phoneNum already exist , phoneNum:{}", userPhoneNumRequest.getPhoneNum());
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "当前联系电话已添加");
        }
        WaybillBridgeEnterpriseUserPhoneNumDO waybillBridgeEnterpriseUserPhoneNumDO = new WaybillBridgeEnterpriseUserPhoneNumDO();
        waybillBridgeEnterpriseUserPhoneNumDO.setGmtCreate(new Date());
        waybillBridgeEnterpriseUserPhoneNumDO.setGmtModified(new Date());
        waybillBridgeEnterpriseUserPhoneNumDO.setCorpId(userPhoneNumRequest.getCorpId());
        waybillBridgeEnterpriseUserPhoneNumDO.setUserId(userPhoneNumRequest.getUserId());
        waybillBridgeEnterpriseUserPhoneNumDO.setUserName(userPhoneNumRequest.getUserName());
        waybillBridgeEnterpriseUserPhoneNumDO.setPhoneNum(PhoneNumberValidator.extractPhoneNumber(userPhoneNumRequest.getPhoneNum()));
        return enterpriseUserPhoneNumMapper.insert(waybillBridgeEnterpriseUserPhoneNumDO);
    }

    @Override
    public WaybillEnterpriseUserPhoneNumDTO getByUserIdAndPhoneNum(String userId, String phoneNum) {
        WaybillBridgeEnterpriseUserPhoneNumDO waybillBridgeEnterpriseUserPhoneNumDO = enterpriseUserPhoneNumMapper.selectOneByUserIdAndPhoneNum(userId, phoneNum);
        return WaybillBridgeEnterpriseUserPhoneNumConvert.convertFromDO(waybillBridgeEnterpriseUserPhoneNumDO);
    }

    @Override
    public List<String> getUserPhoneByUserId(String userId) {
        return enterpriseUserPhoneNumMapper.selectPhoneNumByUserId(userId);
    }

    @Override
    public int deletePhoneNum(EnterpriseUserPhoneNumRequest request) {
        WaybillBridgeEnterpriseUserInfoDO enterpriseUserInfoDO = enterpriseUserInfoMapper.selectByUserId(request.getUserId(), request.getCorpId());
        if (enterpriseUserInfoDO != null && StringUtils.equals(enterpriseUserInfoDO.getPhoneNum(), request.getPhoneNum())) {
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "该手机号码为钉钉默认手机号码，不能删除");
        }


        WaybillBridgeEnterpriseUserPhoneNumDO waybillBridgeEnterpriseUserPhoneNumDO = enterpriseUserPhoneNumMapper.selectOneByUserIdAndPhoneNum(request.getUserId(),
                request.getPhoneNum());
        if (waybillBridgeEnterpriseUserPhoneNumDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "该用户不存在该手机号码");
        }
        return enterpriseUserPhoneNumMapper.deleteByPrimaryKey(waybillBridgeEnterpriseUserPhoneNumDO.getId());
    }
}
