package com.cainiao.waybill.bridge.biz.label.manager.impl;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.label.manager.BranchSellerAddressRelationManager;
import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
import com.cainiao.waybill.bridge.biz.utils.FeatureJsonUtils;
import com.cainiao.waybill.bridge.biz.wrapper.AddressUnifyWrapper;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants.BranchSellerAddressFeature;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.LabelError;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.SystemError;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.exception.util.Exceptions;
import com.cainiao.waybill.bridge.common.label.dto.request.BranchSellerAddRelQueryRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.CreateBranchSellerAddRelRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.ReplaceCourierRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.UnbindCourierRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.UpdateSettlementTypeRequest;
import com.cainiao.waybill.bridge.common.util.Page;
import com.cainiao.waybill.bridge.model.dao.BranchSellerAddressDAO;
import com.cainiao.waybill.bridge.model.dao.bean.BranchSellerAddressRelationQuery;
import com.cainiao.waybill.bridge.model.domain.BranchSellerAddressDO;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.taobao.biz.common.division.DivisionVO;
import com.taobao.common.dao.persistence.exception.DAOException;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-28
 */
@Service
public class BranchSellerAddressRelationManagerImpl implements BranchSellerAddressRelationManager {

    private final static Logger LOGGER = LoggerFactory.getLogger(LogAppender.BRANCH_SELLER_ADDRESS);

    @Resource
    private LogManager logManager;

    @Resource
    private BranchSellerAddressDAO branchSellerAddressDAO;

    @Resource
    private AddressUnifyWrapper addressUnifyWrapper;

    @Override
    public Page<BranchSellerAddressDO> queryRelationsForBranch(BranchSellerAddRelQueryRequest request) throws BridgeBaseException {
        try {
            Date createDateStart = null;
            if (StringUtils.isNotBlank(request.getCooperationDateStart())) {
                createDateStart = DateUtils.strToDateStart(request.getCooperationDateStart(), "yyyy-MM-dd");
            }
            Date createDateEnd = null;
            if (StringUtils.isNotBlank(request.getCooperationDateEnd())) {
                createDateEnd = DateUtils.strToDateEnd(request.getCooperationDateEnd(), "yyyy-MM-dd");
            }
            String sellerName = request.getSellerName();
            String shopName = request.getShopName();

            String cpCode = request.getCpCode();
            String branchCode = request.getBranchCode();
            Byte cooperationStatus = request.getCooperationStatus();
            Long courierId = request.getCourierId();

            Integer pageNo = request.getCurrentPageIndex();
            Integer pageSize = request.getSize();

            BranchSellerAddressRelationQuery query = new BranchSellerAddressRelationQuery();
            query.setCpCode(cpCode).setBranchCode(branchCode).setCooperationDateStart(createDateStart).setCooperationDateEnd(createDateEnd)
                    .setCooperationStatus(cooperationStatus).setCourierId(courierId).setSellerNamePrefix(sellerName).setShopNamePrefix(shopName);

            return branchSellerAddressDAO.queryByBranch(query, pageNo, pageSize);
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        } catch (ParseException e) {
            throw Exceptions.newBridgeValidatorException("date format error", "date format error. cooperationDateStart:{}, cooperationDateEnd:{}",
                request.getCooperationDateStart(), request.getCooperationDateEnd());
        }
    }

    @Override
    public List<BranchSellerAddressDO> queryRelationsForSeller(Long sellerId) throws BridgeBaseException {
        try {
            return branchSellerAddressDAO.queryBySellerId(sellerId);
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        } 
    }

    @Override
    public Long createRelation(CreateBranchSellerAddRelRequest request, String operator) throws BridgeBaseException {
        try {
            // 1. 构造 do
            BranchSellerAddressDO relationDO = buildRelationDO(request, operator);
            // 2. 判断此商家地址是否已经和 cp 合作
            //    如果合作中，判断是否网点、小件员相同，如果相同直接返回，保持幂等，不同抛出异常；
            //    如果取消合作了，更新为新的合作关系
            BranchSellerAddressDO originDO = branchSellerAddressDAO.queryByCpSellerAddress(relationDO.getCpCode(), relationDO.getSellerId(), relationDO.getDivisionId(), relationDO.getAddressDetail());
            if (originDO != null) {
                if (BridgeConstants.BranchSellerAddRelCoopStatus.COOP == originDO.getCooperationStatus()) {
                    // 保持幂等，打印日志，直接返回
                    if (originDO.getBranchCode().equals(relationDO.getBranchCode()) && originDO.getCourierId().equals(relationDO.getCourierId())) {
                        logManager.logWarnMessage("BranchSellerAddressRelationManagerImpl.createRelation", "该绑定关系已经存在",
                            LOGGER);
                        return originDO.getId();
                    }
                    // 不幂等情况下。一个发货地址只能与同一快递公司建立一次服务绑定关系
                    logManager.logErrorMessage("BranchSellerAddressRelationManagerImpl.createRelation", "绑定同一CP多个网点或者小件员. request:" + request,
                        LOGGER);
                    throw Exceptions.newBridgeValidatorException(LabelError.SELLER_ADDRESS_BIND_DUPLICATE_ERROR);
                } else {
                    // 更新合作的小件员、合作时间、网点等所有信息 
                    relationDO.setId(originDO.getId());
                    branchSellerAddressDAO.update(relationDO);
                }
            } else {
                branchSellerAddressDAO.insert(relationDO);
            }
            return relationDO.getId();
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        }
    }

    private BranchSellerAddressDO buildRelationDO(CreateBranchSellerAddRelRequest request, String operator)
        throws BridgeValidationException {
        BranchSellerAddressDO relationDO = new BranchSellerAddressDO();

        String cpCode = request.getCpCode();
        String branchCode = request.getBranchCode();
        Long courierId = request.getCourierId();
        Long sellerId = request.getSellerId();
        String sellerName = request.getSellerName();
        String shopName = request.getShopName();
        DivisionVO divisionVO = addressUnifyWrapper.getMatchMinAreaDivision(request.getSellerAddress());
        if (divisionVO == null) {
            throw Exceptions.newBridgeValidatorException("parameter address error", "parameter address error, address:{}", request.getSellerAddress());
        }

        String addressDetail = request.getSellerAddress().getDetail();

        relationDO.setGmtCooperation(new Date());
        relationDO.setCpCode(cpCode);
        relationDO.setBranchCode(branchCode);
        relationDO.setCourierId(courierId);
        relationDO.setSellerId(sellerId);
        relationDO.setSellerName(sellerName);
        relationDO.setShopName(shopName);
        relationDO.setDivisionId(divisionVO.getDivisionId());
        relationDO.setAddressDetail(addressDetail);
        relationDO.setCooperationStatus(BridgeConstants.BranchSellerAddRelCoopStatus.COOP);
        relationDO.setModifier(operator.length() > 50 ? operator.substring(0,50) : operator);
        return relationDO;
    }

    @Override
    public void unbindCourier(UnbindCourierRequest request, String operator) throws BridgeBaseException {
        try {
            // 1. 校验绑定信息是否存在(一期只支持通过 id 进行解绑)
            Long relationId = request.getId();
            if (relationId == null) {
                throw Exceptions.newBridgeValidatorException("param is null", "relation id is null");
            }

            BranchSellerAddressDO relationDO = branchSellerAddressDAO.queryById(relationId);
            if (relationDO == null) {
                throw Exceptions.newBridgeValidatorException("relation is not exit", "relation is null, id:{}", relationId);
            }
			if (!relationDO.getCpCode().equals(request.getCpCode())
					|| !relationDO.getBranchCode().equals(request.getBranchCode())
					|| !relationDO.getSellerId().equals(request.getSellerId())) {
				throw Exceptions.newBridgeValidatorException("param error",
						"id、cpCode、branchCode、seller relation invalid. request:{}, relation:{}", request, relationDO);
			}
            // 保持幂等
            if (BridgeConstants.BranchSellerAddRelCoopStatus.CANCEL == relationDO.getCooperationStatus()) {
                logManager.logWarnMessage("BranchSellerAddressRelationManagerImpl.unbindCourier", "该绑定关系已经解除", LOGGER);
                return;
            }

            // 2. 解绑合作关系
            relationDO.setCooperationStatus(BridgeConstants.BranchSellerAddRelCoopStatus.CANCEL);
            relationDO.setModifier(operator.length() > 50 ? operator.substring(0, 50) : operator);
            branchSellerAddressDAO.update(relationDO);

        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        }
    }

    @Override
    public void replaceCourier(ReplaceCourierRequest request, String operator) throws BridgeBaseException {
        try {
            // 1. 校验绑定信息是否存在(一期只支持通过 id 进行解绑)
            Long relationId = request.getId();
            BranchSellerAddressDO relationDO = branchSellerAddressDAO.queryById(relationId);

            if (relationDO == null) {
                throw Exceptions.newBridgeValidatorException("relation is not exit", "relation is null, id:{}", relationId);
            }
			if (!relationDO.getCpCode().equals(request.getCpCode())
					|| !relationDO.getBranchCode().equals(request.getBranchCode())
					|| !relationDO.getSellerId().equals(request.getSellerId())) {
				throw Exceptions.newBridgeValidatorException("param error",
						"id、cpCode、branchCode、seller relation invalid. request:{}, relation:{}", request, relationDO);
			}
            if (relationDO.getCooperationStatus().equals(BridgeConstants.BranchSellerAddRelCoopStatus.CANCEL)) {
                throw Exceptions.newBridgeValidatorException("param error", "branch-seller canceled, can not operate. request:{}, relation:{}", request, relationDO);
            }

            if (relationDO.getCourierId().equals(request.getNewCourierId())) {
                logManager.logWarnMessage("replaceCourier", "商家地址已经和此小件员合作中", LOGGER);
            }

            // 2. 更新 courierId, 合作时间
            relationDO.setCourierId(request.getNewCourierId());
            relationDO.setModifier(operator.length() > 50 ? operator.substring(0, 50) : operator);
            relationDO.setGmtCooperation(new Date());
            branchSellerAddressDAO.update(relationDO);
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        }
    }

    @Override
    public void updateSettlementType(UpdateSettlementTypeRequest request, String modifier) throws BridgeBaseException {

        try {
            Long relationId = request.getId();
            BranchSellerAddressDO relationDO = branchSellerAddressDAO.queryById(relationId);
            validateCpCodeBranchCode(relationDO, relationId, request.getCpCode(), request.getBranchCode(), request.getSellerId());

            if (relationDO.getCooperationStatus().equals(BridgeConstants.BranchSellerAddRelCoopStatus.CANCEL)) {
                throw Exceptions.newBridgeValidatorException("param error", "branch-seller canceled, can not operate. request:{}, relation:{}", request, relationDO);
            }

            relationDO.setModifier(modifier.length() > 50 ? modifier.substring(0, 50) : modifier);
            String feature = FeatureJsonUtils.addStringValue(relationDO.getFeature(), BranchSellerAddressFeature.FEATURE_SETTLEMENT_TYPE_EDIT, request.getSettlementType()) ;
            relationDO.setFeature(feature);
            branchSellerAddressDAO.update(relationDO);
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        }
    }

    @Override
    public void activeSettlementType(long id, String feature) throws BridgeBaseException {
        try {

            BranchSellerAddressDO branchSellerAddressDO = new BranchSellerAddressDO() ;
            branchSellerAddressDO.setId(id);
            branchSellerAddressDO.setFeature(feature);

            branchSellerAddressDAO.update(branchSellerAddressDO) ;
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        }
    }

    @Override
    public int countForDtsSettlementTypeUpdate() throws BridgeBaseException {
        try {
            long oneDayMillis = 1 * 24 * 60 * 60 * 1000 ;
            Date yesterday = new Date(System.currentTimeMillis() - oneDayMillis) ;
            yesterday = DateUtils.dateToDateStart(yesterday) ;

            Integer count = branchSellerAddressDAO.countByGtModifyDateAndCooperation(yesterday) ;
            return count == null ? 0 : count.intValue() ;
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        }
    }

    @Override
    public List<BranchSellerAddressDO> queryForDtsSettlementTypeUpdate(int start, int pageSize)
        throws BridgeBaseException {
        try {
            long oneDayMillis = 1 * 24 * 60 * 60 * 1000 ;
            Date yesterday = new Date(System.currentTimeMillis() - oneDayMillis) ;
            yesterday = DateUtils.dateToDateStart(yesterday) ;

            return branchSellerAddressDAO.queryByGtModifyDateAndCooperation(yesterday, start, pageSize) ;
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        }
    }

    private void validateCpCodeBranchCode(BranchSellerAddressDO branchSellerAddressDO, Long relationId, String cpCode, String branchCode, Long sellerId)
        throws BridgeValidationException {

        validateBranchSelllerAddressNotNull(branchSellerAddressDO, relationId);

        if (!branchSellerAddressDO.getCpCode().equals(cpCode) || !branchSellerAddressDO.getBranchCode().equals(
            branchCode) || !branchSellerAddressDO.getSellerId().equals(sellerId)) {
            throw Exceptions.newBridgeValidatorException("param error", "id、cpCode、branchCode relation invalid. cpCode:{}, branchCode:{}, sellerId:{}, relation:{}", cpCode, branchCode, sellerId, branchSellerAddressDO);
        }

    }

    private void validateBranchSelllerAddressNotNull(BranchSellerAddressDO branchSellerAddressDO, Long relationId)
        throws BridgeValidationException {
        if (branchSellerAddressDO == null) {
            throw Exceptions.newBridgeValidatorException("relation is not exit", "relation is null, id:{}", relationId);
        }
    }

	@Override
	public BranchSellerAddressDO getRelationByRelationId(Long relationId) throws BridgeBaseException {
		try {
            if (relationId == null) {
                throw Exceptions.newBridgeValidatorException("param is null", "relation id is null");
            }

            BranchSellerAddressDO relationDO = branchSellerAddressDAO.queryById(relationId);
            if (relationDO == null) {
                throw Exceptions.newBridgeValidatorException("relation is not exit", "relation is null, id:{}", relationId);
            }
            return relationDO;
		 } catch (DAOException e) {
	         throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
	     }
	}
}
