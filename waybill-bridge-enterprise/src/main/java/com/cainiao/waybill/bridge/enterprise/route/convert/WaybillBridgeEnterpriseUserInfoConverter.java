package com.cainiao.waybill.bridge.enterprise.route.convert;

import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoDO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserInfoDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserPhoneNumMapper;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/18
 **/
@Component
public class WaybillBridgeEnterpriseUserInfoConverter {

    @Autowired
    private WaybillBridgeEnterpriseUserPhoneNumMapper enterpriseUserPhoneNumMapper;

    /**
     * DTO模型转换成DO模型
     * @param waybillEnterpriseUserInfoDTO DTO模型
     */
    /*public WaybillBridgeEnterpriseUserInfoDO convertFromDTO(WaybillEnterpriseUserInfoDTO waybillEnterpriseUserInfoDTO) {
        if (waybillEnterpriseUserInfoDTO == null) {
            return null;
        }
        WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = new WaybillBridgeEnterpriseUserInfoDO();
        BeanUtils.copyProperties(waybillEnterpriseUserInfoDTO,waybillBridgeEnterpriseUserInfoDO);
        return waybillBridgeEnterpriseUserInfoDO;
    }*/

    /**
     * DO模型转换成DTO模型
     * @param waybillBridgeEnterpriseUserInfoDO DO模型
     */
    public WaybillEnterpriseUserInfoDTO convertFromDO(WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO) {
        if (waybillBridgeEnterpriseUserInfoDO == null) {
            return null;
        }
        WaybillEnterpriseUserInfoDTO waybillEnterpriseUserInfoDTO = new WaybillEnterpriseUserInfoDTO();
        BeanUtils.copyProperties(waybillBridgeEnterpriseUserInfoDO,waybillEnterpriseUserInfoDTO);
        waybillEnterpriseUserInfoDTO.setPhoneNumList(enterpriseUserPhoneNumMapper.selectPhoneNumByUserId(waybillBridgeEnterpriseUserInfoDO.getUserId()));
        return waybillEnterpriseUserInfoDTO;
    }


    /**
     * DO模型转换成DTO模型
     * @param waybillBridgeEnterpriseUserInfoDOList DO模型
     */
    public List<WaybillEnterpriseUserInfoDTO> convertFromDOList(List<WaybillBridgeEnterpriseUserInfoDO> waybillBridgeEnterpriseUserInfoDOList) {
        List<WaybillEnterpriseUserInfoDTO> waybillEnterpriseUserInfoDTOList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(waybillBridgeEnterpriseUserInfoDOList)) {
            return waybillEnterpriseUserInfoDTOList;
        }
        waybillBridgeEnterpriseUserInfoDOList.forEach(waybillBridgeEnterpriseUserInfoDO -> {
            WaybillEnterpriseUserInfoDTO waybillEnterpriseUserInfoDTO = new WaybillEnterpriseUserInfoDTO();
            BeanUtils.copyProperties(waybillBridgeEnterpriseUserInfoDO,waybillEnterpriseUserInfoDTO);
            waybillEnterpriseUserInfoDTO.setPhoneNumList(enterpriseUserPhoneNumMapper.selectPhoneNumByUserId(waybillBridgeEnterpriseUserInfoDO.getUserId()));
            waybillEnterpriseUserInfoDTOList.add(waybillEnterpriseUserInfoDTO);
        });
        return waybillEnterpriseUserInfoDTOList;
    }
}
