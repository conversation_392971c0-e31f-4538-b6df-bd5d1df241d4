package com.cainiao.waybill.bridge.client2.consignrisk.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
public class WaybillConsignRiskSegmentConfigDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * cpCode
     */
    private String cpCode;

    /**
     * 快递公司名称
     */
    private String cpName;

    /**
     * 号段渠道
     */
    private String channel;

    /**
     * 号段内容
     */
    private String text;

    /**
     * 状态
     */
    private String status;

    /**
     * 灰度比例
     */
    private Integer grayRate;

    /**
     * 已发布内容
     */
    private String publishedText;

    /**
     * 操作者
     */
    private String operator;

}