package com.cainiao.waybill.bridge.common.util.monitor;

/**
 * <AUTHOR> on 2021-06-15.
 * hold StatInner
 */
class StatHolder implements Stat {
    private volatile Stat delegate;

    public StatHolder() {
        delegate = new StatInner();
    }

    /**
     * schedule renew,
     * @return pre delegate
     */
    public Stat renew() {
        Stat pre = delegate;
        delegate = new StatInner();
        return pre;
    }

    @Override
    public void count() {
        delegate.count();
    }

    @Override
    public void count(long count) {
        delegate.count(count);
    }

    @Override
    public void countAndSum(long count, long value) {
        delegate.countAndSum(count, value);
    }

    @Override
    public void countAndSum(long value) {
        delegate.countAndSum(value);
    }

    @Override
    public void countAndSumAndMinMax(long value) {
        delegate.countAndSumAndMinMax(value);
    }

    @Override
    public void minMax(long value) {
        delegate.minMax(value);
    }

    /**
     * 是否记录数据，无有效数据的情况下可以不打印日志
     */
    public boolean hasStated() {
        return ((StatInner) delegate).hasStated();
    }
}
