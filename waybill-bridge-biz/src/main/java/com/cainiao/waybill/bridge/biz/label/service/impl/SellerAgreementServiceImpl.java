package com.cainiao.waybill.bridge.biz.label.service.impl;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
import com.cainiao.waybill.bridge.biz.label.manager.SellerAgreementManager;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.SystemError;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.label.service.SellerAgreementService;
import com.cainiao.waybill.bridge.common.label.validator.SellerAgreementValidator;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.util.BaseResultHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2017/06/12
 */
@HSFProvider(serviceInterface = SellerAgreementService.class)
public class SellerAgreementServiceImpl implements SellerAgreementService {

    private final static Logger LOGGER = LoggerFactory.getLogger(
        LogAppender.SELLER_AGREEMENT);

    @Resource
    private LogManager logManager;

    @Resource
    private SellerAgreementManager sellerAgreementManager;

    @Override
    public BaseResultDTO<Boolean> isSellerAgreementValid(Long sellerId, ClientInfoDTO clientInfoDTO) {
        try {
            SellerAgreementValidator.validate(sellerId, clientInfoDTO);
            Boolean isValid = sellerAgreementManager.isSellerAgreementValid(sellerId);
            return BaseResultHelper.newResult(isValid);
        } catch (BridgeValidationException e) {
            logManager.logExceptionMessage("isSellerAgreementValid",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", sellerId:" + sellerId, e, LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("isSellerAgreementValid",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", sellerId:" + sellerId, e, LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        } catch (Throwable e) {
            logManager.logExceptionMessage("isSellerAgreementValid", "sellerId:" + sellerId, e, LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        }
    }

    @Override
    public BaseResultDTO<Void> signSellerAgreement(Long sellerId, ClientInfoDTO clientInfoDTO) {
        try {
            SellerAgreementValidator.validate(sellerId, clientInfoDTO);

            sellerAgreementManager.signSellerAgreement(sellerId);
            return BaseResultHelper.newResult();
        } catch (BridgeValidationException e) {
            logManager.logExceptionMessage("signSellerAgreement",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", sellerId:" + sellerId, e, LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("signSellerAgreement",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", sellerId:" + sellerId, e, LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        } catch (Throwable e) {
            logManager.logExceptionMessage("signSellerAgreement", "sellerId:" + sellerId, e, LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        }
    }

}
