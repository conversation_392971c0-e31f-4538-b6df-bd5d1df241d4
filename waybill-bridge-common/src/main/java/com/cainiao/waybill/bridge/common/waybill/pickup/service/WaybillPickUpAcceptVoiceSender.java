package com.cainiao.waybill.bridge.common.waybill.pickup.service;

import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpAcceptVoiceEvent;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;

/**
 * 小件员接单后，发送延时消息，指定时间后消费消息，根据电联状态决定是否进行语音外呼
 * <AUTHOR>
 * @date 2021/9/14-下午8:04
 */
public interface WaybillPickUpAcceptVoiceSender {

    /**
     * 小件员接单后，发送延时消息
     * @param acceptVoiceEvent :
     * @return :
     */
    BaseResultDTO<Void> send(WaybillPickUpAcceptVoiceEvent acceptVoiceEvent);
}
