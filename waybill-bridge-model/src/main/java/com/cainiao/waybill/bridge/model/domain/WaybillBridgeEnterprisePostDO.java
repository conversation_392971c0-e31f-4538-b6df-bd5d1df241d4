package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class WaybillBridgeEnterprisePostDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   企业ID
     *
     *
     * @mbg.generated
     */
    private String corpId;

    /**
     * Database Column Remarks:
     *   邮局名称
     *
     *
     * @mbg.generated
     */
    private String postName;

    /**
     * Database Column Remarks:
     *   场地主键
     *
     *
     * @mbg.generated
     */
    private Long locationId;

    /**
     * Database Column Remarks:
     *   备注
     *
     *
     * @mbg.generated
     */
    private String remark;

    /**
     * Database Column Remarks:
     *   扩展字段
     *
     *
     * @mbg.generated
     */
    private String feature;

    /**
     * Database Column Remarks:
     *   小邮局业务唯一键
     *
     *
     * @mbg.generated
     */
    private String postId;

    /**
     *
     * @return the value of waybill_bridge_enterprise_post.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_bridge_enterprise_post.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_post.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_bridge_enterprise_post.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_post.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_bridge_enterprise_post.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_post.corp_id
     *
     * @mbg.generated
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     *
     * @param corpId the value for waybill_bridge_enterprise_post.corp_id
     *
     * @mbg.generated
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_post.post_name
     *
     * @mbg.generated
     */
    public String getPostName() {
        return postName;
    }

    /**
     *
     * @param postName the value for waybill_bridge_enterprise_post.post_name
     *
     * @mbg.generated
     */
    public void setPostName(String postName) {
        this.postName = postName;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_post.location_id
     *
     * @mbg.generated
     */
    public Long getLocationId() {
        return locationId;
    }

    /**
     *
     * @param locationId the value for waybill_bridge_enterprise_post.location_id
     *
     * @mbg.generated
     */
    public void setLocationId(Long locationId) {
        this.locationId = locationId;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_post.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @param remark the value for waybill_bridge_enterprise_post.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_post.feature
     *
     * @mbg.generated
     */
    public String getFeature() {
        return feature;
    }

    /**
     *
     * @param feature the value for waybill_bridge_enterprise_post.feature
     *
     * @mbg.generated
     */
    public void setFeature(String feature) {
        this.feature = feature;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_post.post_id
     *
     * @mbg.generated
     */
    public String getPostId() {
        return postId;
    }

    /**
     *
     * @param postId the value for waybill_bridge_enterprise_post.post_id
     *
     * @mbg.generated
     */
    public void setPostId(String postId) {
        this.postId = postId;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", corpId=").append(corpId);
        sb.append(", postName=").append(postName);
        sb.append(", locationId=").append(locationId);
        sb.append(", remark=").append(remark);
        sb.append(", feature=").append(feature);
        sb.append(", postId=").append(postId);
        sb.append("]");
        return sb.toString();
    }
}