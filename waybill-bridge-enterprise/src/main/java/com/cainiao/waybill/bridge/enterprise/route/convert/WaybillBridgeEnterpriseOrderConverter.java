package com.cainiao.waybill.bridge.enterprise.route.convert;

import com.alibaba.fastjson.JSONObject;
import com.cainiao.waybill.bridge.enterprise.common.constant.EnterpriseOrderFeatureConstant;
import com.cainiao.waybill.bridge.enterprise.common.enums.*;
import com.cainiao.waybill.bridge.enterprise.utils.AddressUtils;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePostDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoDO;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseLocationDO;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeEnterpriseOrderDTO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseOrderDO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePostMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillEnterpriseLocationMapper;
import com.cainiao.waybill.common.util.FeatureUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;


/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@Service
public class WaybillBridgeEnterpriseOrderConverter {

    @Resource
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;

    @Resource
    private WaybillBridgeEnterprisePostMapper enterprisePostMapper;

    @Resource
    private WaybillEnterpriseLocationMapper enterpriseLocationMapper;


    /**
     * DTO模型转换成DO模型
     * @param waybillBridgeEnterpriseOrderDTO
     */
    public WaybillBridgeEnterpriseOrderDO convertFromDTO(WaybillBridgeEnterpriseOrderDTO waybillBridgeEnterpriseOrderDTO) {
        WaybillBridgeEnterpriseOrderDO waybillBridgeEnterpriseOrderDO = new WaybillBridgeEnterpriseOrderDO();
        BeanUtils.copyProperties(waybillBridgeEnterpriseOrderDTO,waybillBridgeEnterpriseOrderDO);
        return waybillBridgeEnterpriseOrderDO;
    }

    /**
     * DO模型转换成DTO模型
     * @param waybillBridgeEnterpriseOrderDO
     */
    public WaybillBridgeEnterpriseOrderDTO convertFromDO(WaybillBridgeEnterpriseOrderDO waybillBridgeEnterpriseOrderDO) {
        WaybillBridgeEnterpriseOrderDTO waybillBridgeEnterpriseOrderDTO = new WaybillBridgeEnterpriseOrderDTO();
        BeanUtils.copyProperties(waybillBridgeEnterpriseOrderDO,waybillBridgeEnterpriseOrderDTO);
        waybillBridgeEnterpriseOrderDTO.setSenderAddress(AddressUtils.parse(waybillBridgeEnterpriseOrderDO.getSenderAddress()));
        waybillBridgeEnterpriseOrderDTO.setConsigneeAddress(AddressUtils.parse(waybillBridgeEnterpriseOrderDO.getConsigneeAddress()));
        waybillBridgeEnterpriseOrderDTO.setStatusDesc(EnterpriseOrderStatusEnum.getDesByCode(waybillBridgeEnterpriseOrderDO.getStatus()));
        waybillBridgeEnterpriseOrderDTO.setActionDesc(EnterpriseOrderStatusEnum.getDesByCode(waybillBridgeEnterpriseOrderDO.getAction()));
        waybillBridgeEnterpriseOrderDTO.setItemDesc(EnterpriseOrderItemEnum.getDesByCode(waybillBridgeEnterpriseOrderDO.getItem()));
        waybillBridgeEnterpriseOrderDTO.setProductDesc(EnterpriseOrderProductEnum.getByCode(waybillBridgeEnterpriseOrderDO.getProduct()));
        waybillBridgeEnterpriseOrderDTO.setCpName(ExpressTypeEnum.getByExpressCode(waybillBridgeEnterpriseOrderDO.getCpCode()));
        waybillBridgeEnterpriseOrderDTO.setBusinessType(Integer.valueOf(waybillBridgeEnterpriseOrderDO.getBusinessType()));
        WaybillBridgeEnterpriseUserInfoDO operatorUser = enterpriseUserInfoMapper.selectByUserId(waybillBridgeEnterpriseOrderDO.getUserId(), waybillBridgeEnterpriseOrderDO.getCorpId());
        if (operatorUser != null) {
            waybillBridgeEnterpriseOrderDTO.setUserName(operatorUser.getUserName());
        }
        WaybillBridgeEnterprisePostDO enterprisePostDO = enterprisePostMapper.selectByPrimaryKey(waybillBridgeEnterpriseOrderDO.getPostId());
        if (enterprisePostDO != null) {
            waybillBridgeEnterpriseOrderDTO.setPostId(enterprisePostDO.getPostId());
            waybillBridgeEnterpriseOrderDTO.setPostName(enterprisePostDO.getPostName());
        }
        WaybillEnterpriseLocationDO locationDO = enterpriseLocationMapper.selectByPrimaryKey(waybillBridgeEnterpriseOrderDO.getLocationId());
        if (locationDO != null) {
            waybillBridgeEnterpriseOrderDTO.setLocationId(locationDO.getLocationId());
            waybillBridgeEnterpriseOrderDTO.setLocationName(locationDO.getLocationName());
        }
        String feature = waybillBridgeEnterpriseOrderDO.getFeature();
        if (feature != null) {
            Map<String, String> map = FeatureUtils.parseFromString(feature);
            String deptIdString = map.get(EnterpriseOrderFeatureConstant.DEPT_ID);
            if (StringUtils.isNotEmpty(deptIdString)) {
                waybillBridgeEnterpriseOrderDTO.setDeptId(Long.valueOf(deptIdString));
            }
            String deptName = map.get(EnterpriseOrderFeatureConstant.DEPT_NAME);
            if (StringUtils.isNotEmpty(deptName)) {
                waybillBridgeEnterpriseOrderDTO.setDeptName(deptName);
            }
            String insuredValue = map.get(EnterpriseOrderFeatureConstant.INSURED_VALUE);
            if (StringUtils.isNotEmpty(insuredValue)) {
                waybillBridgeEnterpriseOrderDTO.setInsured(EnterpriseOrderInsuredEnum.INSURED.getCode());
                waybillBridgeEnterpriseOrderDTO.setInsuredValue(insuredValue);
            } else {
                waybillBridgeEnterpriseOrderDTO.setInsured(EnterpriseOrderInsuredEnum.NOT_INSURED.getCode());
            }
            waybillBridgeEnterpriseOrderDTO.setCostAllocation(String.valueOf(map.get(EnterpriseOrderFeatureConstant.COST_ALLOCATION)));
        }
        return waybillBridgeEnterpriseOrderDTO;
    }
}