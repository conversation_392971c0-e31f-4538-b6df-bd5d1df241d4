package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class QuoteDetailInfoDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   报价配置ID，关联报价配置表ID
     *
     *
     * @mbg.generated
     */
    private Long quoteId;

    /**
     * Database Column Remarks:
     *   出发省份
     *
     *
     * @mbg.generated
     */
    private String sendProv;

    /**
     * Database Column Remarks:
     *   出发城市
     *
     *
     * @mbg.generated
     */
    private String sendCity;

    /**
     * Database Column Remarks:
     *   出发区县
     *
     *
     * @mbg.generated
     */
    private String sendArea;

    /**
     * Database Column Remarks:
     *   到达省份
     *
     *
     * @mbg.generated
     */
    private String receProv;

    /**
     * Database Column Remarks:
     *   到达城市
     *
     *
     * @mbg.generated
     */
    private String receCity;

    /**
     * Database Column Remarks:
     *   到达区县
     *
     *
     * @mbg.generated
     */
    private String receArea;

    /**
     * Database Column Remarks:
     *   首重，单位:g
     *
     *
     * @mbg.generated
     */
    private Integer firstWeight;

    /**
     * Database Column Remarks:
     *   续重，单位:g
     *
     *
     * @mbg.generated
     */
    private Integer stepWeight;

    /**
     * Database Column Remarks:
     *   首重价格，单位：分
     *
     *
     * @mbg.generated
     */
    private Integer firstWeightPrice;

    /**
     * Database Column Remarks:
     *   续重价格，单位：分
     *
     *
     * @mbg.generated
     */
    private Integer stepWeightPrice;

    /**
     * Database Column Remarks:
     *   对客首重价格，单位：分
     *
     *
     * @mbg.generated
     */
    private Integer userFirstWeightPrice;

    /**
     * Database Column Remarks:
     *   对客续重价格，单位：分
     *
     *
     * @mbg.generated
     */
    private Integer userStepWeightPrice;

    /**
     * Database Column Remarks:
     *   是否阶梯报价,Y/N
     *
     *
     * @mbg.generated
     */
    private String useStepPrice;

    /**
     * Database Column Remarks:
     *   阶梯报价配置
     *
     *
     * @mbg.generated
     */
    private String stepFeature;

    /**
     * Database Column Remarks:
     *   计抛比系数
     *
     *
     * @mbg.generated
     */
    private Integer volumeWeightRatio;

    /**
     * Database Column Remarks:
     *   线路奖励，单位：分
     *
     *
     * @mbg.generated
     */
    private Integer routeReward;

    /**
     * Database Column Remarks:
     *   扩展信息
     *
     *
     * @mbg.generated
     */
    private String featue;

    /**
     *
     * @return the value of quote_detail_info.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for quote_detail_info.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of quote_detail_info.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for quote_detail_info.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of quote_detail_info.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for quote_detail_info.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of quote_detail_info.quote_id
     *
     * @mbg.generated
     */
    public Long getQuoteId() {
        return quoteId;
    }

    /**
     *
     * @param quoteId the value for quote_detail_info.quote_id
     *
     * @mbg.generated
     */
    public void setQuoteId(Long quoteId) {
        this.quoteId = quoteId;
    }

    /**
     *
     * @return the value of quote_detail_info.send_prov
     *
     * @mbg.generated
     */
    public String getSendProv() {
        return sendProv;
    }

    /**
     *
     * @param sendProv the value for quote_detail_info.send_prov
     *
     * @mbg.generated
     */
    public void setSendProv(String sendProv) {
        this.sendProv = sendProv;
    }

    /**
     *
     * @return the value of quote_detail_info.send_city
     *
     * @mbg.generated
     */
    public String getSendCity() {
        return sendCity;
    }

    /**
     *
     * @param sendCity the value for quote_detail_info.send_city
     *
     * @mbg.generated
     */
    public void setSendCity(String sendCity) {
        this.sendCity = sendCity;
    }

    /**
     *
     * @return the value of quote_detail_info.send_area
     *
     * @mbg.generated
     */
    public String getSendArea() {
        return sendArea;
    }

    /**
     *
     * @param sendArea the value for quote_detail_info.send_area
     *
     * @mbg.generated
     */
    public void setSendArea(String sendArea) {
        this.sendArea = sendArea;
    }

    /**
     *
     * @return the value of quote_detail_info.rece_prov
     *
     * @mbg.generated
     */
    public String getReceProv() {
        return receProv;
    }

    /**
     *
     * @param receProv the value for quote_detail_info.rece_prov
     *
     * @mbg.generated
     */
    public void setReceProv(String receProv) {
        this.receProv = receProv;
    }

    /**
     *
     * @return the value of quote_detail_info.rece_city
     *
     * @mbg.generated
     */
    public String getReceCity() {
        return receCity;
    }

    /**
     *
     * @param receCity the value for quote_detail_info.rece_city
     *
     * @mbg.generated
     */
    public void setReceCity(String receCity) {
        this.receCity = receCity;
    }

    /**
     *
     * @return the value of quote_detail_info.rece_area
     *
     * @mbg.generated
     */
    public String getReceArea() {
        return receArea;
    }

    /**
     *
     * @param receArea the value for quote_detail_info.rece_area
     *
     * @mbg.generated
     */
    public void setReceArea(String receArea) {
        this.receArea = receArea;
    }

    /**
     *
     * @return the value of quote_detail_info.first_weight
     *
     * @mbg.generated
     */
    public Integer getFirstWeight() {
        return firstWeight;
    }

    /**
     *
     * @param firstWeight the value for quote_detail_info.first_weight
     *
     * @mbg.generated
     */
    public void setFirstWeight(Integer firstWeight) {
        this.firstWeight = firstWeight;
    }

    /**
     *
     * @return the value of quote_detail_info.step_weight
     *
     * @mbg.generated
     */
    public Integer getStepWeight() {
        return stepWeight;
    }

    /**
     *
     * @param stepWeight the value for quote_detail_info.step_weight
     *
     * @mbg.generated
     */
    public void setStepWeight(Integer stepWeight) {
        this.stepWeight = stepWeight;
    }

    /**
     *
     * @return the value of quote_detail_info.first_weight_price
     *
     * @mbg.generated
     */
    public Integer getFirstWeightPrice() {
        return firstWeightPrice;
    }

    /**
     *
     * @param firstWeightPrice the value for quote_detail_info.first_weight_price
     *
     * @mbg.generated
     */
    public void setFirstWeightPrice(Integer firstWeightPrice) {
        this.firstWeightPrice = firstWeightPrice;
    }

    /**
     *
     * @return the value of quote_detail_info.step_weight_price
     *
     * @mbg.generated
     */
    public Integer getStepWeightPrice() {
        return stepWeightPrice;
    }

    /**
     *
     * @param stepWeightPrice the value for quote_detail_info.step_weight_price
     *
     * @mbg.generated
     */
    public void setStepWeightPrice(Integer stepWeightPrice) {
        this.stepWeightPrice = stepWeightPrice;
    }

    /**
     *
     * @return the value of quote_detail_info.user_first_weight_price
     *
     * @mbg.generated
     */
    public Integer getUserFirstWeightPrice() {
        return userFirstWeightPrice;
    }

    /**
     *
     * @param userFirstWeightPrice the value for quote_detail_info.user_first_weight_price
     *
     * @mbg.generated
     */
    public void setUserFirstWeightPrice(Integer userFirstWeightPrice) {
        this.userFirstWeightPrice = userFirstWeightPrice;
    }

    /**
     *
     * @return the value of quote_detail_info.user_step_weight_price
     *
     * @mbg.generated
     */
    public Integer getUserStepWeightPrice() {
        return userStepWeightPrice;
    }

    /**
     *
     * @param userStepWeightPrice the value for quote_detail_info.user_step_weight_price
     *
     * @mbg.generated
     */
    public void setUserStepWeightPrice(Integer userStepWeightPrice) {
        this.userStepWeightPrice = userStepWeightPrice;
    }

    /**
     *
     * @return the value of quote_detail_info.use_step_price
     *
     * @mbg.generated
     */
    public String getUseStepPrice() {
        return useStepPrice;
    }

    /**
     *
     * @param useStepPrice the value for quote_detail_info.use_step_price
     *
     * @mbg.generated
     */
    public void setUseStepPrice(String useStepPrice) {
        this.useStepPrice = useStepPrice;
    }

    /**
     *
     * @return the value of quote_detail_info.step_feature
     *
     * @mbg.generated
     */
    public String getStepFeature() {
        return stepFeature;
    }

    /**
     *
     * @param stepFeature the value for quote_detail_info.step_feature
     *
     * @mbg.generated
     */
    public void setStepFeature(String stepFeature) {
        this.stepFeature = stepFeature;
    }

    /**
     *
     * @return the value of quote_detail_info.volume_weight_ratio
     *
     * @mbg.generated
     */
    public Integer getVolumeWeightRatio() {
        return volumeWeightRatio;
    }

    /**
     *
     * @param volumeWeightRatio the value for quote_detail_info.volume_weight_ratio
     *
     * @mbg.generated
     */
    public void setVolumeWeightRatio(Integer volumeWeightRatio) {
        this.volumeWeightRatio = volumeWeightRatio;
    }

    /**
     *
     * @return the value of quote_detail_info.route_reward
     *
     * @mbg.generated
     */
    public Integer getRouteReward() {
        return routeReward;
    }

    /**
     *
     * @param routeReward the value for quote_detail_info.route_reward
     *
     * @mbg.generated
     */
    public void setRouteReward(Integer routeReward) {
        this.routeReward = routeReward;
    }

    /**
     *
     * @return the value of quote_detail_info.featue
     *
     * @mbg.generated
     */
    public String getFeatue() {
        return featue;
    }

    /**
     *
     * @param featue the value for quote_detail_info.featue
     *
     * @mbg.generated
     */
    public void setFeatue(String featue) {
        this.featue = featue;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", quoteId=").append(quoteId);
        sb.append(", sendProv=").append(sendProv);
        sb.append(", sendCity=").append(sendCity);
        sb.append(", sendArea=").append(sendArea);
        sb.append(", receProv=").append(receProv);
        sb.append(", receCity=").append(receCity);
        sb.append(", receArea=").append(receArea);
        sb.append(", firstWeight=").append(firstWeight);
        sb.append(", stepWeight=").append(stepWeight);
        sb.append(", firstWeightPrice=").append(firstWeightPrice);
        sb.append(", stepWeightPrice=").append(stepWeightPrice);
        sb.append(", userFirstWeightPrice=").append(userFirstWeightPrice);
        sb.append(", userStepWeightPrice=").append(userStepWeightPrice);
        sb.append(", useStepPrice=").append(useStepPrice);
        sb.append(", stepFeature=").append(stepFeature);
        sb.append(", volumeWeightRatio=").append(volumeWeightRatio);
        sb.append(", routeReward=").append(routeReward);
        sb.append(", featue=").append(featue);
        sb.append("]");
        return sb.toString();
    }
}