package com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.type;

import com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder.WaybillAdWorkOrderUpdateRequest;
import com.cainiao.waybill.bridge.innersupport.ad.model.dto.WaybillAdWorkOrderDTO;

public interface ActionType {
    boolean hasChanged(WaybillAdWorkOrderUpdateRequest request, WaybillAdWorkOrderDTO existingWorkOrder);

    void handleChange(WaybillAdWorkOrderUpdateRequest request, WaybillAdWorkOrderDTO existingWorkOrder);
}
