package com.cainiao.waybill.bridge.common.label.dto;

import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * @description 返回给网点的"网点-商家地址"绑定关系
 * <AUTHOR>
 * @date 2017-04-26
 */
public class BranchSellerAddressRelationForBranchDTO extends BaseDTO {

    private static final long serialVersionUID = 8326099450931484315L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * cp 编码
     */
    private String cpCode;

    /**
     * 网点编码
     */
    private String branchCode;

    /**
     * 商家id
     */
    private Long sellerId;

    /**
     * 商家名称
     */
    private String sellerName;

    /**
     * 商家名称
     */
    private String shopName;

    /**
     * 商家地址信息
     */
    private AddressDTO sellerAddress;

    /**
     * 小件员id
     */
    private Long courierId;

    /**
     * 合作状态
     * 0:取消合作
     * 1:合作中
     * @See com.cainiao.waybill.bridge.common.constants.BridgeConstants.BranchSellerAddRelCoopStatus
     */
    private Byte cooperationStatus;

    /**
     * 合作时间
     * 格式 "yyyy-MM-dd"
     */
    private String cooperationDate;

    /**
     * 当前生效的结算类型
     */
    private String settlementTypeActive ;

    /**
     * 当前编辑中的结算类型
     */
    private String settlementTypeEdit ;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public String getShopName() {
        return shopName;
    }

    public BranchSellerAddressRelationForBranchDTO setShopName(String shopName) {
        this.shopName = shopName;
        return this;
    }

    public Long getCourierId() {
        return courierId;
    }

    public void setCourierId(Long courierId) {
        this.courierId = courierId;
    }

    public AddressDTO getSellerAddress() {
        return sellerAddress;
    }

    public void setSellerAddress(AddressDTO sellerAddress) {
        this.sellerAddress = sellerAddress;
    }

    public Byte getCooperationStatus() {
        return cooperationStatus;
    }

    public void setCooperationStatus(Byte cooperationStatus) {
        this.cooperationStatus = cooperationStatus;
    }

    public String getCooperationDate() {
        return cooperationDate;
    }

    public void setCooperationDate(String cooperationDate) {
        this.cooperationDate = cooperationDate;
    }

    public String getSettlementTypeActive() {
        return settlementTypeActive;
    }

    public void setSettlementTypeActive(String settlementTypeActive) {
        this.settlementTypeActive = settlementTypeActive;
    }

    public String getSettlementTypeEdit() {
        return settlementTypeEdit;
    }

    public void setSettlementTypeEdit(String settlementTypeEdit) {
        this.settlementTypeEdit = settlementTypeEdit;
    }
}
