package com.cainiao.waybill.bridge.enterprise.common.enums;

import java.util.Objects;

public enum EnterpriseOrderStatusEnum {
    /**
     * 待寄出
     */
    PENDING(1, "待寄出"),

    /**
     * 在途中
     */
    IN_TRANSIT(2, "在途中"),

    /**
     * 已签收
     */
    SIGNED(3, "已签收"),
    /**
     * 已取消
     */
    CANCELED(4, "已取消"),

    /**
     * 已删除
     */
    DELETED(5, "已删除"),

    ;

    final Integer code;
    final String describe;

    EnterpriseOrderStatusEnum(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }
    public Integer getCode() {
        return code;
    }
    public String getDescribe() {
        return describe;
    }


    public static String getDesByCode(Integer code) {
        if (null == code) {
            return null;
        }
        for (EnterpriseOrderStatusEnum value : EnterpriseOrderStatusEnum.values()) {
            if(Objects.equals(value.getCode(), code)){
                return value.describe;
            }
        }
        return null;
    }
}
