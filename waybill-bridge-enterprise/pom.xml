<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cainiao.waybill</groupId>
        <artifactId>waybill-bridge</artifactId>
        <version>${waybill-bridge.version}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>


    <modelVersion>4.0.0</modelVersion>
    <artifactId>waybill-bridge-enterprise</artifactId>
    <packaging>jar</packaging>
    <version>${waybill-bridge.version}</version>
    <name>waybill-bridge-enterprise</name>
    <dependencies>
<!--        钉钉流推送授权信息-->
        <dependency>
            <groupId>com.dingtalk.open</groupId>
            <artifactId>app-stream-client</artifactId>
        </dependency>
<!--        数据库模块-->
        <dependency>
            <groupId>com.cainiao.waybill</groupId>
            <artifactId>waybill-bridge-model</artifactId>
        </dependency>
<!--        钉钉SDK-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
        </dependency>
<!--        钉钉SDK（旧）-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibaba-dingtalk-service-sdk</artifactId>
        </dependency>

<!--        switch-->
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-switchcenter-spring-boot-starter</artifactId>
        </dependency>
<!--sprint-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
<!--logback-->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <!--web controller-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--stater-->
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-hsf-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-tair-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cainiao.waybill.galaxy</groupId>
            <artifactId>user-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cainiao.waybill</groupId>
            <artifactId>waybill-bridge-biz</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cainiao.waybill</groupId>
            <artifactId>waybill-number-client</artifactId>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>