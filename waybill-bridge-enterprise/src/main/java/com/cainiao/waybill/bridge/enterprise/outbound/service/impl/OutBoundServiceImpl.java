package com.cainiao.waybill.bridge.enterprise.outbound.service.impl;

import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.enterprise.authority.domain.EnterpriseAppConfig;
import com.cainiao.waybill.bridge.enterprise.authority.response.AuthInfoResponse;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseSwitchHolder;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseConfigEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePlatformEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.outbound.request.OrderPickUpMessage;
import com.cainiao.waybill.bridge.enterprise.outbound.request.OrderPickUpPushRequest;
import com.cainiao.waybill.bridge.enterprise.outbound.service.OutBoundService;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationSendbytemplateRequest;
import com.dingtalk.api.response.OapiMessageCorpconversationSendbytemplateResponse;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OutBoundServiceImpl implements OutBoundService {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private DingTalkAuthService dingTalkAuthService;

    @Override
    public Boolean pushOrderPickUpMessage(OrderPickUpPushRequest request) {
        try {
            // 根据corpId查询access_token
            String corpAccessToken = dingTalkAuthService.getCorpAccessToken(EnterprisePlatformEnum.DingTalk.name(), request.getCorpId());
            if(StringUtils.isEmpty(corpAccessToken)) {
                LOGGER.error("corpId get corpAccessToken failed");
                // 这边就不抛出异常阻塞，获取不到直接return false
                return Boolean.FALSE;
//                throw new EnterpriseException(EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.code(), EnterpriseErrorEnum.QUERY_ACCESSTOKEN_FAILED.describe());
            }

            // 获取appConfig,如果配置了，就是用配置的，如果没取到，使用默认的
            EnterpriseAppConfig appConfig = EnterpriseSwitchHolder.ENTERPRISE_CLIENT_CONFIG.get(request.getCorpId());

            OrderPickUpMessage orderPickUpMessage = new OrderPickUpMessage();
            orderPickUpMessage.setCpName(request.getCpName());
            orderPickUpMessage.setPickupCode(request.getPickupCode());
            orderPickUpMessage.setDay(DateUtils.generateCurrentDayYMD());
            orderPickUpMessage.setWaybillCode(request.getWaybillCode());


            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/sendbytemplate");
            OapiMessageCorpconversationSendbytemplateRequest req = new OapiMessageCorpconversationSendbytemplateRequest();
            req.setAgentId(Long.valueOf(appConfig.getAgentId()));
            req.setUseridList(request.getUserId());
            req.setTemplateId(EnterpriseSwitchHolder.ENTERPRISE_CONFIG.get(EnterpriseConfigEnum.pickUpTemplateId.name()));
            req.setData(new Gson().toJson(orderPickUpMessage));
            OapiMessageCorpconversationSendbytemplateResponse rsp = client.execute(req, corpAccessToken);
            if(rsp.isSuccess()) {
                return Boolean.TRUE;
            }
        } catch (Exception e) {
            LOGGER.error("corpId {} push order pick up template message error:{}", request.getCorpId(), ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
        return Boolean.FALSE;
    }
}
