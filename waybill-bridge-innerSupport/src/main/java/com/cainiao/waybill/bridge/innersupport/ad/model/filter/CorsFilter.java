package com.cainiao.waybill.bridge.innersupport.ad.model.filter;

import org.springframework.stereotype.Component;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@Component
public class CorsFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 初始化方法，可留空
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        String[] allowedOrigins = {
                "https://pre-cn-x-gateway.cainiao.com",
                "https://cn-x-gateway.cainiao.com",
        };
        String origin = request.getHeader("Origin");
        if (origin != null && isAllowedOrigin(origin, allowedOrigins)) {
            response.setHeader("Access-Control-Allow-Origin", origin);
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            response.setHeader("Access-Control-Allow-Headers", "*,X-Token, Content-Type, x-xsrf-token");
            response.setHeader("Access-Control-Allow-Credentials","true");
        }

        // 处理OPTIONS请求
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            response.setStatus(HttpServletResponse.SC_OK);
            PrintWriter writer = response.getWriter();
            writer.println("Hello World"); // 写入要返回的内容
            writer.close(); // 关闭输出流
            return;
        }
        filterChain.doFilter(servletRequest, servletResponse);
    }

    private boolean isAllowedOrigin(String origin, String[] allowedOrigins) {
        for (String allowedOrigin : allowedOrigins) {
            if (allowedOrigin.equals(origin)) {
                return true;
            }
        }
        return false;
    }
    @Override
    public void destroy() {
        // 销毁方法，可留空
    }
}