package com.cainiao.waybill.bridge.innersupport.ad.web;

import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.ErrorInfo;
import com.cainiao.waybill.bridge.innersupport.ad.exception.AdBusinessException;
import lombok.Data;
import java.io.Serializable;

@Data
public class AdBaseResult<T> implements Serializable {

    private static final long serialVersionUID = -2655944625760119468L;

    private String errorCode;
    private String errorMsg;
    private boolean success;
    private T data;

    public static <T> AdBaseResult<T> success(T data) {
        AdBaseResult<T> result = new AdBaseResult<T>();
        result.setData(data);
        result.setSuccess(true);
        return result;
    }

    public static <T> AdBaseResult<T> success() {
        AdBaseResult<T> result = new AdBaseResult<T>();
        result.setSuccess(true);
        return result;
    }

    public static <T> AdBaseResult<T> fail(T data) {
        AdBaseResult<T> result = new AdBaseResult<T>();
        result.setData(data);
        result.setSuccess(false);
        return result;
    }

    public static <T> AdBaseResult<T> bizFail(String errorCode, String errorMsg) {
        AdBaseResult<T> result = new AdBaseResult<T>();
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setErrorMsg(errorMsg);
        return result;
    }

    public static <T> AdBaseResult<T> bizFail(String errorMsg) {
        AdBaseResult<T> result = new AdBaseResult<T>();
        result.setSuccess(false);
        result.setErrorMsg(errorMsg);
        return result;
    }

    public static <T> AdBaseResult<T> bizFail(ErrorInfo errorInfo) {
        AdBaseResult<T> result = new AdBaseResult<T>();
        result.setSuccess(false);
        result.setErrorCode(errorInfo.getErrorCode());
        result.setErrorMsg(errorInfo.getErrorMsg());
        return result;
    }

    public static <T> AdBaseResult<T> bizFail(AdBusinessException businessException) {
        AdBaseResult<T> result = new AdBaseResult<>();
        result.setSuccess(false);
        result.setErrorCode(businessException.getErrorCode());
        result.setErrorMsg(businessException.getErrorMessage());
        return result;
    }

    public static <T> AdBaseResult<T> systemFail() {
        AdBaseResult<T> result = new AdBaseResult<T>();
        result.setSuccess(false);
        result.setErrorCode("-1");
        result.setErrorMsg("后台系统异常");
        return result;
    }
}
