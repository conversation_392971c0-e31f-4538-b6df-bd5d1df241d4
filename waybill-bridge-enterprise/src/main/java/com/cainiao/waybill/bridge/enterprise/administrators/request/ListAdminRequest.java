package com.cainiao.waybill.bridge.enterprise.administrators.request;

import com.cainiao.waybill.bridge.enterprise.common.BaseRequest;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePlatformEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 该类用于管理和处理与企业管理员相关的请求。
 * <AUTHOR>
 * @date 2025-04-16 11:10:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListAdminRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -2475975502548195184L;

    /**
     * 用户名称
     */
    private String userName;
}
