package com.cainiao.waybill.bridge.web.common.dto;

import lombok.Data;
import com.taobao.cainiao.waybill.base.Constants;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/11/10-上午11:23
 */
@Data
public class BaseResult<T> implements Serializable {
    private static final long serialVersionUID = -5306420417727600393L;
    private String errorCode;
    private String errorMsg;
    private boolean success;
    private T data;

    public static <T> BaseResult<T> success(T data) {
        BaseResult<T> result = new BaseResult<T>();
        result.setData(data);
        result.setSuccess(true);
        return result;
    }

    public static <T> BaseResult<T> success() {
        BaseResult<T> result = new BaseResult<T>();
        result.setSuccess(true);
        return result;
    }

    public static <T> BaseResult<T> fail(T data) {
        BaseResult<T> result = new BaseResult<T>();
        result.setData(data);
        result.setSuccess(false);
        return result;
    }


    public static <T> BaseResult<T> bizFail(Constants.ErrorConstant error) {
        BaseResult<T> result = new BaseResult<>();
        result.setSuccess(false);
        result.setErrorCode(error.getErrorCode());
        result.setErrorMsg(error.getErrorMsg());
        return result;
    }

    public static <T> BaseResult<T> bizFail(String errorCode, String errorMsg) {
        BaseResult<T> result = new BaseResult<T>();
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setErrorMsg(errorMsg);
        return result;
    }

    public static <T> BaseResult<T> systemFail() {
        BaseResult<T> result = new BaseResult<T>();
        result.setSuccess(false);
        result.setErrorCode("-1");
        result.setErrorMsg("后台系统异常");
        return result;
    }
}

