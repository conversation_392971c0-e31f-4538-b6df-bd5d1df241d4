package com.cainiao.waybill.bridge.biz.fee.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.fee.dto.PrePayRechargeDTO;
import com.cainiao.waybill.bridge.biz.hsf.WtPayPlatformService;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.TradeFeatureKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.WtBizTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.exception.PickUpBusinessException;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPreChargeTradeListManager;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelImportUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpFeatureUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.wrapper.TairManagerWrapper;
import com.cainiao.waybill.bridge.common.dto.WtAccountDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.cainiao.waybill.bridge.model.domain.WaybillPreChargeTradeListDO;
import com.cainiao.waybill.common.util.FeatureUtils;
import com.cainiao.waybill.number.common.Result;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2024/9/4 16:54
 **/
@Component
public class PrePayServiceImpl implements PrePayService {

    @Resource
    WtPayPlatformService wtPayPlatformService;

    @Resource
    private TairManagerWrapper tairManagerWrapper;

    /**
     * 缓存过期时间 单位：秒
     */
    private static final int DEFAULT_CACHE_EXPIRE_TIME = 3600;

    /**
     * 调账运单号后缀
     */
    private static final String PRE_PAY_RECHARGE_SUFFIX = "-1";

    /**
     * 缓存锁后缀
     */
    private static final String PRE_PAY_LOCK_SUFFIX = "_pre_pay_lock";

    /**
     * 缓存后缀
     */
    private static final String PRE_PAY_CACHE_SUFFIX = "_pre_pay_cache";

    @Resource
    private WaybillPreChargeTradeListManager waybillPreChargeTradeListManager;

    @Override
    public void doWtPay(WaybillPickUpDetailDO existDO, String cpPrice, String remark) {

        String mailNo = existDO.getMailNo();
        String lockKey = mailNo + PRE_PAY_LOCK_SUFFIX;
        String cacheKey = mailNo + PRE_PAY_CACHE_SUFFIX;
        boolean lockFlag = tairManagerWrapper.lock(lockKey);
        if(lockFlag){
            try {
                Object obj = tairManagerWrapper.get(cacheKey);
                if(obj == null){
                    String cnUserId = PickUpFeatureUtil.getFeatureItemByKey(existDO.getFeature(), PickUpConstants.TraceFeatureKey.PRE_PAY_CN_USER_ID);
                    if(StringUtils.isNotBlank(cnUserId)){
                        WtAccountDTO wtAccountDTO = wtPayPlatformService.queryAccount(cnUserId);
                        if(null != wtAccountDTO){
                            // 揽收时 扣除预充费用
                            wtPayPlatformService.wtPayOrder(mailNo, cpPrice, wtAccountDTO.getId(), wtAccountDTO.getOwnerId());
                            tairManagerWrapper.put(cacheKey, "1", 24 * DEFAULT_CACHE_EXPIRE_TIME);
                            // 记录扣费数据
                            String remarkFeature = getTradeRemarkFeature(remark);
                            savePayTradeInfo(mailNo, cpPrice, WtBizTypeEnum.PAY.getType(), Long.parseLong(wtAccountDTO.getOwnerId()), wtAccountDTO.getId().toString(), remarkFeature);
                        }else{
                            throw new PickUpBusinessException(PickUpConstants.Error.QUERY_WT_ACCOUNT_ERROR);
                        }
                    }
                }
            } finally {
                tairManagerWrapper.unlock(lockKey);
            }
        }
    }

    @Override
    public Result<String> uploadRecharge(MultipartFile file) {
        List<String> successMailNoList = new ArrayList<>();
        try {
            List<PrePayRechargeDTO> list = ExcelImportUtil.loadData(file, PrePayRechargeDTO.class, BridgeSwitch.PRE_PAY_RECHARGE_MAX_LIMIT);
            List<PrePayRechargeDTO> filteredList = list.stream()
                .filter(dto -> !isAllPropertiesEmpty(dto))
                .collect(Collectors.toList());

            int size = filteredList.size();
            List<String> mailNoList = filteredList.stream()
                .map(PrePayRechargeDTO::getMailNo)
                .distinct()           // 去重
                .collect(Collectors.toList());
            if(size > mailNoList.size()){
                throw new BridgeBusinessException("DUPLICATE_MAIL_NO", "运单号重复:"+size+"/"+mailNoList.size());
            }
            for(PrePayRechargeDTO prePayRechargeDTO : filteredList){
                try {
                    new BigDecimal(prePayRechargeDTO.getAmount());
                }catch (Exception e){
                    throw new BridgeBusinessException("PAY_AMOUNT_INVALID", "调账金额无效:"+prePayRechargeDTO.getAmount());
                }
            }
            for(PrePayRechargeDTO prePayRechargeDTO : filteredList){
                String userId = prePayRechargeDTO.getUserId();
                Object obj = tairManagerWrapper.get(userId);
                WtAccountDTO wtAccountDTO;
                if(null == obj){
                    wtAccountDTO = wtPayPlatformService.queryAccount(prePayRechargeDTO.getUserId());
                    tairManagerWrapper.put(userId, wtAccountDTO, DEFAULT_CACHE_EXPIRE_TIME);
                }else {
                    wtAccountDTO = (WtAccountDTO)obj;
                }
                String mailNo = prePayRechargeDTO.getMailNo() + PRE_PAY_RECHARGE_SUFFIX;
                // 调账
                wtPayPlatformService.wtRecharge(mailNo, prePayRechargeDTO.getAmount(), wtAccountDTO.getId(), wtAccountDTO.getOwnerId());
                String remarkFeature = getTradeRemarkFeature(prePayRechargeDTO.getRemark());
                // 记录调账记录
                savePayTradeInfo(mailNo, prePayRechargeDTO.getAmount(), WtBizTypeEnum.RECHARGE.getType(), Long.parseLong(wtAccountDTO.getOwnerId()), wtAccountDTO.getId().toString(), remarkFeature);
                successMailNoList.add(mailNo);
            }
            PickUpLogUtil.info("批量调账成功运单数{}，mailNoList:{}", mailNoList.size(), mailNoList);
            return Result.success();
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("","UPLOAD_PRE_PAY_RECHARGE_EXCEPTION",e.getErrorCode(),e.getErrorMessage(), ExceptionUtils.getFullStackTrace(e));
            return Result.fail(e.getErrorCode(), e.getErrorMessage()+"，调账成功运单:" + successMailNoList);
        }catch (Exception e){
            PickUpLogUtil.errLog("","UPLOAD_PRE_PAY_RECHARGE_EXCEPTION","SYSTEM_ERROR", e.getMessage(), ExceptionUtils.getFullStackTrace(e));
            return Result.fail("SYSTEM_ERROR", "系统异常" + "，调账成功运单:" + successMailNoList);
        }

    }

    /**
     * 对象所有属性是否为空
     * @param dto
     * @return
     */
    private boolean isAllPropertiesEmpty(PrePayRechargeDTO dto) {
        return StringUtils.isAllBlank(dto.getMailNo(), dto.getAmount(), dto.getUserId(), dto.getRemark());
    }

    /**
     * 备注扩展字段
     * @param remark
     * @return
     */
    private String getTradeRemarkFeature(String remark) {
        if(StringUtils.isBlank(remark)){
            return null;
        }
        Map<String, String> featureMap = new HashMap<>(4);
        featureMap.put(TradeFeatureKey.REMARK, remark);
        return FeatureUtils.featureMapToString(featureMap);
    }

    /**
     * 保存交易记录
     * @param tradeNo
     * @param cpPrice 元
     * @param tradeType
     * @param accountId
     * @param userName
     * @param feature
     */
    private void savePayTradeInfo(String tradeNo, String cpPrice, String tradeType, Long accountId, String userName, String feature) {
        try{
            BigDecimal price = new BigDecimal(cpPrice);
            WaybillPreChargeTradeListDO tradeDo = waybillPreChargeTradeListManager.selectOne(tradeNo, tradeType);
            if (tradeDo != null){
                PickUpLogUtil.info("PayTradeInfo already exist.tradeNo:{},price:{}", tradeDo, price);
                return;
            }
            WaybillPreChargeTradeListDO preChargeTradeDO = new WaybillPreChargeTradeListDO();
            preChargeTradeDO.setTradeNo(tradeNo);
            // 转换为分
            preChargeTradeDO.setTradeAmount(price.multiply(new BigDecimal(100)).intValue());
            preChargeTradeDO.setUserId(accountId.toString());
            preChargeTradeDO.setTradeType(tradeType);
            preChargeTradeDO.setTradeTime(new Date());
            preChargeTradeDO.setUserName(userName);
            if(StringUtils.isNotBlank(feature)){
                preChargeTradeDO.setFeature(feature);
            }
            waybillPreChargeTradeListManager.save(preChargeTradeDO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.error("savePayTradeInfo error.tradeNo:{},price:{}", tradeNo, cpPrice, ExceptionUtils.getFullStackTrace(e));
            throw new BridgeBusinessException("PRE_PAY_TRADE_SAVE_EXCEPTION","预充值交易记录保存失败");
        }
    }
}
