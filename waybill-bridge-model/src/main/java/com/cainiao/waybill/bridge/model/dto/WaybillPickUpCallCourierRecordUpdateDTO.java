package com.cainiao.waybill.bridge.model.dto;

import com.cainiao.waybill.bridge.model.domain.WaybillPickUpCallCourierRecordDO;
import lombok.Data;

import java.util.Date;

/**
 * 更新统计模板下对应记录时的传参实体
 * <AUTHOR>
 * @date 2021/8/4-上午11:48
 */
@Data
public class WaybillPickUpCallCourierRecordUpdateDTO extends WaybillPickUpCallCourierRecordDO {

    /**
     * 查询揽收时间的范围
     */
    private Date startTime;

    private Date endTime;
}
