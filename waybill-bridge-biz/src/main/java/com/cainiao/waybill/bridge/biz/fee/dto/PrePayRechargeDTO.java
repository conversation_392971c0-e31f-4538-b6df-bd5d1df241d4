package com.cainiao.waybill.bridge.biz.fee.dto;

import java.io.Serializable;

import com.cainiao.waybill.bridge.biz.utils.excel.ExcelHeader;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/9/4 17:17
 **/
@Data
public class PrePayRechargeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 运单号
     */
    @ExcelHeader("运单号")
    private String mailNo;

    /**
     * 调账金额 - 元
     */
    @ExcelHeader("调账金额(元)")
    private String amount;

    /**
     * 菜鸟账号
     */
    @ExcelHeader("菜鸟账号")
    private String userId;

    /**
     * 调账说明
     */
    @ExcelHeader("调账说明")
    private String remark;



}
