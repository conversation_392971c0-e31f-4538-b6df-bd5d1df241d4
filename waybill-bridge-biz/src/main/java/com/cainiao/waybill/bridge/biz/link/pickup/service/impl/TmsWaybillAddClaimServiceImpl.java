package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.biz.pickup.constants.InsuranceConstants;
import com.cainiao.waybill.bridge.biz.pickup.dto.WaybillInsuranceClaimRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.WaybillInsuranceClaimResponse;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillInsuranceManager;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_ADD_CLAIM.WaybillAddClaimRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_ADD_CLAIM.WaybillAddClaimResponse;
import com.taobao.pac.client.sdk.receiveservice.WaybillAddClaimService;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description Link 理赔接口
 * @date 2021/7/22-下午5:05
 */
public class TmsWaybillAddClaimServiceImpl implements WaybillAddClaimService {

    @Resource
    private WaybillInsuranceManager waybillInsuranceManager;

    @Override
    public WaybillAddClaimResponse invoke(ReceiveParams<WaybillAddClaimRequest> receiveParams) {
        PickUpLogUtil.info("AddClaim receiveParams : " + JSON.toJSONString(receiveParams));

        WaybillInsuranceClaimRequest claimRequest = new WaybillInsuranceClaimRequest();
        WaybillAddClaimRequest linkRequest = receiveParams.getRequestDataObject();
        BeanUtils.copyProperties(linkRequest.getRequest(), claimRequest);
        PickUpLogUtil.info("AddClaim claimRequest ： " + JSON.toJSONString(claimRequest));

        WaybillInsuranceClaimResponse claimResponse = waybillInsuranceManager.addClaim(claimRequest);

        com.taobao.pac.client.sdk.dataobject.response.WAYBILL_ADD_CLAIM.WaybillInsuranceClaimResponse linkClaimRes = new com.taobao.pac.client.sdk.dataobject.response.WAYBILL_ADD_CLAIM.WaybillInsuranceClaimResponse();
        BeanUtils.copyProperties(claimResponse, linkClaimRes);

        WaybillAddClaimResponse linkResponse = new WaybillAddClaimResponse();
        linkResponse.setResult(linkClaimRes);
        if (InsuranceConstants.ZhonganResult.SUCESS_RESULT_CODE.equals(claimResponse.getResultCode())) {
            linkResponse.setSuccess(true);
        } else {
            linkResponse.setSuccess(false);
            linkResponse.setErrorCode(claimResponse.getResultCode());
            linkResponse.setErrorMsg(claimResponse.getResultMsg());
        }
        PickUpLogUtil.info("AddClaim linkResponse : " + JSON.toJSONString(linkResponse));


        return linkResponse;
    }
}
