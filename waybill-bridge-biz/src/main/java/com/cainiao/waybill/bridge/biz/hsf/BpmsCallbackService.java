package com.cainiao.waybill.bridge.biz.hsf;

/**
 * <AUTHOR> on 2023/9/7.
 */
public interface BpmsCallbackService {

    /**
     * 同步退款结果
     */
    int refundResultSync(String waybillCode, String refundId, String result);

    /**
     * 算法方案审批完成
     * 需在流程完结节点配置回调HSF服务
     * @param processInstanceId
     */
    void completeAlgorithmApprove(String processInstanceId);

    /**
     * 报价配置审批完成
     * 需在流程完结节点配置回调HSF服务
     * @param processInstanceId
     */
    void completeQuoteConfigApprove(String processInstanceId);

    /**
     * 账单调账审批完成
     * @param processInstanceId
     */
    void completeBillAdjustConfigApprove(String processInstanceId);
}
