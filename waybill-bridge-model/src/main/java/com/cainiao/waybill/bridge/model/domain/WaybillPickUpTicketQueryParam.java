package com.cainiao.waybill.bridge.model.domain;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */

/**
 *
 * <AUTHOR>
 */
@Data
public class WaybillPickUpTicketQueryParam implements Serializable {

    private static final long serialVersionUID = -3931214025270960986L;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 处理方
     */
    private String dealPart;

    /**
     * 运单号列表
     */
    private List<String> mailNoList;

    /**
     * 外部订单号id列表
     */
    private List<String> outOrderIdList;

    /**
     * 外部订单号id列表
     */
    private String outOrderId;

    /**
     * 平台列表
     */
    private List<String> platformList;

    /**
     * 平台类型列表
     */
    private List<String> ticketTypeList;

    /**
     * 工单状态列表
     */
    private List<String> ticketStatusList;

    /**
     * 发件人手机号列表
     */
    private String senderMobile;

    /**
     * 工单来源列表
     */
    private List<String> ticketSourceList;

    /**
     * 服务商代码列表
     */
    private String cpCode;


    /**
     * 服务商代码列表
     */
    private List<String> cpCodeList;

    /**
     * 服务商工单状态列表
     */
    private List<String> cpTicketStatusList;

    /**
     * 发件省列表
     */
    private String senderProvince;

    /**
     * 发件省列表
     */
    private List<String> senderProvinceList;

    /**
     * 创建模式
     */
    private String createMode;

    /**
     * Database Column Remarks:
     *   一级来源
     *
     *
     * @mbg.generated
     */
    private String primarySource;

    /**
     * Database Column Remarks:
     *   一级来源
     *
     *
     * @mbg.generated
     */
    private String source;


    /**
     * Database Column Remarks:
     *   一级来源
     */
    private List<String> sourceList;

    /**
     * 超时1小时
     */
    private Date levelOneTimeout;

    /**
     * 超时12小
     */
    private Date levelTwoTimeout;

    /**
     * 超时36小时
     */
    private Date levelThreeTimeout;

    /**
     * 超时60小时
     */
    private Date levelFourTimeout;

    /**
     * 订单操作
     */
    private Integer orderOperate;

    /**
     * 工单对应订单来源
     */
    private String orderSource;

    /**
     * 工单对应订单来源列表
     */
    private List<String> orderSourceList;

    /**
     * 排除当前工单参数
     */
    private Long excludeTicketId;

    /**
     * 服务商订单id
     */
    private String cpOrderId;

    /**
     * 服务商代理
     */
    private String agent;

    /**
     * 扩展字段key
     */
    private String extraKey;

    /**
     * 分页页码
     * @mbg.generated
     */
    protected int pageIndex = 0;

    /**
     * 分页页面大小
     * @mbg.generated
     */
    protected int pageSize = 20;

    /**
     * 工单操作列表
     */
    private List<Integer> orderOperateList;

    /**
     * 操作时间
     */
    private Date operateTimeStart;
    /**
     * 操作时间
     */
    private Date operateTimeEnd;


}
