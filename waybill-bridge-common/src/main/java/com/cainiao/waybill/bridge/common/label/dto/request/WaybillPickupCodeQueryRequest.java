package com.cainiao.waybill.bridge.common.label.dto.request;


import com.cainiao.waybill.bridge.common.util.Page;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/4/25.
 * 网点查询"揽件码"订单请求
 */
public class WaybillPickupCodeQueryRequest extends Page {

    private static final long serialVersionUID = 7794691998676607944L;

    /**
     * 电子面单创建日期查询起始值
     * 格式'yyyy-MM-dd'
     * 搜索从 00:00:00 开始
     */
    private String createDateStart;

    /**
     * 电子面单创建日期查询结束值
     * 格式'yyyy-MM-dd'
     * 搜索到当天 23:59:59
     */
    private String createDateEnd;

    /**
     * 打印状态。
     * 0：未打印
     * 1：已打印
     */
    private Byte printStatus = 0;

    /**
     * 商家id
     */
    private Long sellerId;

    /**
     * 店铺名称
     * <Note>目前只支持最左匹配</Note>
     */
    private String shopName;

    /**
     * 揽件码
     */
    private String pickupCode;

    /**
     * cp 编码
     */
    private String cpCode;

    /**
     * 网点编码
     */
    private String branchCode;

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getPickupCode() {
        return pickupCode;
    }

    public void setPickupCode(String pickupCode) {
        this.pickupCode = pickupCode;
    }

    public String getCreateDateStart() {
        return createDateStart;
    }

    public void setCreateDateStart(String createDateStart) {
        this.createDateStart = createDateStart;
    }

    public String getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(String createDateEnd) {
        this.createDateEnd = createDateEnd;
    }

    public Byte getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(Byte printStatus) {
        this.printStatus = printStatus;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }
}
