//package com.cainiao.waybill.bridge.biz.link.label.service.impl;
//
//import javax.annotation.Resource;
//
//import com.cainiao.waybill.bridge.biz.label.manager.BranchSellerAddressRelationManager;
//import com.cainiao.waybill.bridge.common.base.Constants.ErrorConstant;
//import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.SystemError;
//import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
//import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
//import com.cainiao.waybill.bridge.common.label.dto.request.ReplaceCourierRequest;
//import com.cainiao.waybill.bridge.common.validator.BridgeBaseValidator;
//import com.cainiao.waybill.common.seller.dto.WaybillApplySubscriptionInfo;
//import com.cainiao.waybill.common.seller.dto.WaybillBranchAccount;
//import com.cainiao.waybill.common.seller.dto.WaybillSubscriptionQueryDTO;
//import com.cainiao.waybill.common.seller.dto.WaybillSubscriptionQueryResponseDTO;
//import com.cainiao.waybill.common.seller.service.WaybillQueryServiceForInternalSystem;
//import com.taobao.cainiao.waybill.exception.WaybillBaseException;
//import com.taobao.pac.api.open.ReceiveParams;
//import com.taobao.pac.client.sdk.dataset.request.TMS_WAYBILL_COURIER_REPLACE.TmsWaybillCourierReplaceRequest;
//import com.taobao.pac.client.sdk.dataobject.response.TMS_WAYBILL_COURIER_REPLACE.TmsWaybillCourierReplaceResponse;
//import com.taobao.pac.client.sdk.receiveservice.TmsWaybillCourierReplaceService;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
///**
// * 提供给cp换绑商家和小件员关系的link接口实现
// *
// * <AUTHOR>
// *
// */
//public class TmsWaybillCourierReplaceServiceForLinkImpl implements TmsWaybillCourierReplaceService {
//	private final static Logger logger = LoggerFactory.getLogger(LogAppender.LINK_CP);
//	@Resource
//	BranchSellerAddressRelationManager branchSellerAddressRelationManager;
//	@Resource
//	WaybillQueryServiceForInternalSystem waybillQueryService;
//
//	@Override
//	public TmsWaybillCourierReplaceResponse invoke(ReceiveParams<TmsWaybillCourierReplaceRequest> receiveParams) {
//		try {
//			/**
//			 * step1:参数校验
//			 */
//			BridgeBaseValidator.validate(StringUtils.isNotBlank(receiveParams.getCpCode()), "cpCode 不能为空");
//			BridgeBaseValidator.validate(StringUtils.isNotBlank(receiveParams.getRequestDataObject().getBranchCode()),
//					"branchCode 不能为空");
//			BridgeBaseValidator.validate(receiveParams.getRequestDataObject().getCourierId() != null, "courierId 不能为空");
//			BridgeBaseValidator.validate(receiveParams.getRequestDataObject().getNewCourierId() != null,
//					"newCourierId 不能为空");
//			BridgeBaseValidator.validate(receiveParams.getRequestDataObject().getRelationId() != null, "id不能为空");
//			BridgeBaseValidator.validate(receiveParams.getRequestDataObject().getSellerId() != null, "sellerId不能为空");
//
//			boolean isOpen = isOpenWaybillService(receiveParams.getCpCode(), receiveParams.getRequestDataObject().getBranchCode(), receiveParams.getRequestDataObject().getNewCourierId());
//			BridgeBaseValidator.validate(isOpen, "新的小件员未开通电子面单服务");
//
//			/**
//			 * step2:入参转换并调用核心业务方法
//			 */
//			ReplaceCourierRequest request = new ReplaceCourierRequest();
//			request.setBranchCode(receiveParams.getRequestDataObject().getBranchCode());
//			request.setCpCode(receiveParams.getCpCode());
//			request.setId(receiveParams.getRequestDataObject().getRelationId());
//			request.setNewCourierId(receiveParams.getRequestDataObject().getNewCourierId());
//			request.setSellerId(receiveParams.getRequestDataObject().getSellerId());
//			branchSellerAddressRelationManager.replaceCourier(request, receiveParams.getCpCode());
//			/**
//			 * step3:出参转换并返回
//			 */
//			TmsWaybillCourierReplaceResponse rsp = new TmsWaybillCourierReplaceResponse();
//			rsp.setSuccess(true);
//			return rsp;
//		} catch (BridgeValidationException e) {
//			logger.error("TmsWaybillCourierReplaceService#invoke,BridgeValidationException errorCode="
//					+ e.getErrorCode() + ",errorMsg=" + e.getErrorMessage() + ",receiveParams=" + receiveParams, e);
//			return failure(e.getErrorCode(), e.getErrorMessage());
//		} catch (Exception e) {
//			logger.error("TmsWaybillCourierReplaceService#invoke,Exception receiveParams=" + receiveParams, e);
//			return failure(SystemError.SYSTEM_BUSY);
//		}
//	}
//
//	/**
//	 * @apiNote 失败返回值封装方法
//	 */
//	private TmsWaybillCourierReplaceResponse failure(ErrorConstant error) {
//		TmsWaybillCourierReplaceResponse rsp = new TmsWaybillCourierReplaceResponse();
//		rsp.setSuccess(false);
//		rsp.setErrorCode(error.getErrorCode());
//		rsp.setErrorMsg(error.getErrorMsg());
//		return rsp;
//	}
//
//	/**
//	 * @apiNote 失败返回值封装方法
//	 */
//	private TmsWaybillCourierReplaceResponse failure(String errorCode, String errorMsg) {
//		TmsWaybillCourierReplaceResponse rsp = new TmsWaybillCourierReplaceResponse();
//		rsp.setSuccess(false);
//		rsp.setErrorCode(errorCode);
//		rsp.setErrorMsg(errorMsg);
//		return rsp;
//	}
//
//	/**
//	 * @apiNote 小件员是否开通电子面单服务
//	 */
//	private boolean isOpenWaybillService(String cpCode, String branchCode, Long courierId) throws WaybillBaseException {
//		WaybillSubscriptionQueryDTO request = new WaybillSubscriptionQueryDTO();
//		request.setCpCode(cpCode);
//
//		com.cainiao.waybill.common.result.BaseResultDTO<WaybillSubscriptionQueryResponseDTO> result = waybillQueryService
//				.waybillSubscriptionQuery(courierId, request,
//						new com.cainiao.waybill.common.dto.ClientInfoDTO("waybill-bridge", "waybill-bridge"));
//
//		if (result == null) {
//			throw new WaybillBaseException("WaybillSubscriptionQuery unexpected error",
//					"WaybillSubscriptionQuery unexpected error!");
//		}
//
//		if (result.isFailure()) {
//			com.cainiao.waybill.common.result.BaseResultDTO.ErrorInfo errorInfo = result.getOneErrorInfo();
//
//			if (errorInfo != null) {
//				throw new WaybillBaseException(errorInfo.getErrorCode(), errorInfo.getErrorMessage());
//			} else {
//				throw new WaybillBaseException("WaybillSubscriptionQuery unexpected error",
//						"WaybillSubscriptionQuery unexpected error!");
//			}
//		}
//
//		WaybillSubscriptionQueryResponseDTO dto = result.getModule();
//
//		if (dto == null || CollectionUtils.isEmpty(dto.getWaybillApplySubscription())) {
//			return false;
//		}
//
//		for (WaybillApplySubscriptionInfo sub : dto.getWaybillApplySubscription()) {
//			if (!StringUtils.equals(cpCode, sub.getCpCode()) || CollectionUtils.isEmpty(sub.getBranchAccountCols())) {
//				continue;
//			}
//
//			for (WaybillBranchAccount account : sub.getBranchAccountCols()) {
//				if (StringUtils.equals(branchCode, account.getBranchCode())) {
//					return true;
//				}
//			}
//		}
//		return false;
//	}
//}
