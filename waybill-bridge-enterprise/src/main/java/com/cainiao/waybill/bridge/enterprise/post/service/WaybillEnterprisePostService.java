package com.cainiao.waybill.bridge.enterprise.post.service;

import com.cainiao.waybill.bridge.enterprise.post.request.EnterprisePostRequest;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterprisePostDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/20
 **/
public interface WaybillEnterprisePostService {

    /**
     * 获取小邮局信息
     *
     * @param request request
     * @return 用户信息
     */
    BridgePagingDTO<WaybillEnterprisePostDTO> pageList(EnterprisePostRequest request);

    /**
     * 新增小邮局信息
     *
     * @param enterprisePostRequest request
     */
    void addPost(EnterprisePostRequest enterprisePostRequest);

    /**
     * 更新小邮局信息
     * @param enterprisePostRequest request
     */
    void updatePost(EnterprisePostRequest enterprisePostRequest);

    /**
     * 删除小邮局信息
     * @param id id
     */
    void deleteById(Long id);

    /**
     * 根据id获取小邮局信息
     * @param id id
     * @return 小邮局信息
     */
    WaybillEnterprisePostDTO getById(Long id);

    /**
     * 获取小邮局信息
     *
     * @param request request
     * @return 用户信息
     */
    List<WaybillEnterprisePostDTO> getPostList(EnterprisePostRequest request);

    /**
     * 删除小邮局信息
     * @param postId 小邮局业务id
     */
    void deletePostByPostId(String postId, String corpId);

    /**
     * 获取小邮局信息
     * @param postId 小邮局业务id
     * @return 小邮局信息
     */
    WaybillEnterprisePostDTO getPostByPostId(String postId, String corpId);
}
