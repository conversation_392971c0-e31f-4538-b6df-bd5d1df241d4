package com.cainiao.waybill.bridge.web.pickup.router;

import java.util.List;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.router.DTO.BridgeRouterConfigMetaInfo;
import com.cainiao.waybill.bridge.biz.router.request.BridgeRouterConfigMetaQueryRequest;
import com.cainiao.waybill.bridge.biz.router.service.BridgeRouterCommonService;
import com.cainiao.waybill.bridge.web.charity.controller.BaseController;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.pickup.constant.TicketResourceEnum;
import com.cainiao.waybill.bridge.web.pickup.ticket.role.PickUpResourceAuthority;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> zouping.fzp
 * @Classname BridgeRouterCommonController
 * @Description
 * @Date 2023/7/25 12:33 上午
 * @Version 1.0
 */
@Slf4j(topic = "PICK_UP_MANAGER_INFO")
@RestController
@RequestMapping(value = "/pickup/order/route")
public class BridgeRouterCommonController extends BaseController {

    @Resource
    private BridgeRouterCommonService routerCommonService;

    /**
     * 根据参数查找
     */
    @PickUpResourceAuthority(TicketResourceEnum.ORDER_ROUTER)
    @ResponseBody
    @PostMapping("/strategy/config/metainfo")
    public BaseResult<List<BridgeRouterConfigMetaInfo>> queryMetaInfo(@RequestBody BridgeRouterConfigMetaQueryRequest request){
        List<BridgeRouterConfigMetaInfo> list = routerCommonService.queryConfigMetaInfo(request.getOrderChannel());
        return BaseResult.success(list);
    }
}
