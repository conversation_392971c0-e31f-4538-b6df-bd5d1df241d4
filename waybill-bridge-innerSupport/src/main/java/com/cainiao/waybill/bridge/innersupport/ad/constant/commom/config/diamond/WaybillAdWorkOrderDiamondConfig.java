package com.cainiao.waybill.bridge.innersupport.ad.constant.commom.config.diamond;

import com.alibaba.common.lang.ExceptionUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;

public class WaybillAdWorkOrderDiamondConfig {
    public static final Logger infoLogger = LoggerFactory.getLogger("WAYBILL_ADWORKORDER_INFO");

    private static WaybillAdWorkOrderConfig CONFIG = new WaybillAdWorkOrderConfig();

    private static void initConfig() {
        // 启动只用一次场景，直接get获取配置值
        try {
            String configInfo = Diamond
                .getConfig(WaybillAdWorkOrderConstants.DiamondConfig.WAYBILL_AD_WORK_ORDER_LOGIN_USER_INFO_CONFIG_DATA_ID,
                    WaybillAdWorkOrderConstants.DiamondConfig.WAYBILL_AD_WORK_ORDER_LOGIN_USER_INFO_CONFIG_GROUP_ID, 3000);
            parseConfig(configInfo);
        } catch (IOException e1) {
            infoLogger.info("获取diamond中广告工单系统配置信息配置信息：" + ExceptionUtil.getStackTrace(e1));
        }
        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(WaybillAdWorkOrderConstants.DiamondConfig.WAYBILL_AD_WORK_ORDER_LOGIN_USER_INFO_CONFIG_DATA_ID,
            WaybillAdWorkOrderConstants.DiamondConfig.WAYBILL_AD_WORK_ORDER_LOGIN_USER_INFO_CONFIG_GROUP_ID,
            new ManagerListenerAdapter() {
                @Override
                public void receiveConfigInfo(String configInfo) {
                    try {
                        parseConfig(configInfo);
                    } catch (Exception e) {
                        infoLogger.info(
                            "ManagerListenerAdapter获取diamond中广告工单系统配置信息：" + ExceptionUtil.getStackTrace(e));
                    }
                }
            });
    }

    public static WaybillAdWorkOrderConfig getConfig() {
        return CONFIG;
    }

    static {
        initConfig();
    }

    private static void parseConfig(String configInfo) {
        if (StringUtil.isBlank(configInfo)) {
            return;
        }
        CONFIG = JSONObject.parseObject(configInfo, WaybillAdWorkOrderConfig.class);
    }

    @Data
    public static class WaybillAdWorkOrderConfig {
        private List<CainiaoCustomerService> cainiaoCustomerServiceList;

        private List<Advertiser> advertiserList;

        private List<CP> cpList;

        private List<String> customRobotGroupMessageAllowAdvertiserIdList;

        private List<String> customRobotGroupMessageAllowCpCodeList;

        private Boolean adWorkOrderMockLogin;

        /**
         * 是否允许发送钉钉群消息开关
         */
        private Boolean customRobotGroupMessageEnableSwitch;

        /**
         * 是否允许智能语音外呼开关
         */
        private Boolean callCustomerEnableSwitch;
    }

    @Data
    public static class CainiaoCustomerService {
        private String cainiaoCustomerServiceName;

        private String cainiaoCustomerServiceId;

        private List<String> loginMobileList;
    }

    @Data
    public static class Advertiser {
        private String advertiserName;

        private String advertiserId;

        /**
         * 钉钉群自定义机器人 customRobotSecret
         */
        private String customRobotSecret;

        /**
         * 钉钉群自定义机器人 customRobotToken
         */
        private String customRobotToken;

        private List<String> loginMobileList;
    }

    @Data
    public static class CP {
        private String cpName;

        private String cpCode;

        /**
         * 钉钉群自定义机器人 customRobotSecret
         */
        private String customRobotSecret;

        /**
         * 钉钉群自定义机器人 customRobotToken
         */
        private String customRobotToken;

        private List<String> loginMobileList;
    }
}
