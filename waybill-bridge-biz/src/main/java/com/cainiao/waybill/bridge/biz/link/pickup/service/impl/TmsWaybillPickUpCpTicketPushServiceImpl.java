package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketConstant.CpTicketStatus;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpTicketCompleteRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpTicketReplyRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.TicketRoleEnum;
import com.cainiao.waybill.bridge.biz.ticket.service.PickUpTicketService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_CP_TICKET_PUSH.WaybillPickUpCpTicketPushRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PICK_UP_CP_TICKET_PUSH.WaybillPickUpCpTicketPushResponse;
import com.taobao.pac.client.sdk.receiveservice.WaybillPickUpCpTicketPushService;
import org.apache.commons.compress.utils.Lists;

/**
 * 接受外部向菜鸟提交的工单
 *
 * @author: yexin
 * @date: 2022-05-06 10:41
 **/
public class TmsWaybillPickUpCpTicketPushServiceImpl implements WaybillPickUpCpTicketPushService {
    @Resource
    private PickUpTicketService pickUpTicketService;

    @Override
    public WaybillPickUpCpTicketPushResponse invoke(ReceiveParams<WaybillPickUpCpTicketPushRequest> receiveParams) {
        PickUpLogUtil.info("cp_ticket_push : " + receiveParams);
        WaybillPickUpCpTicketPushRequest request = receiveParams.getRequestDataObject();
        WaybillPickUpCpTicketPushResponse response = new WaybillPickUpCpTicketPushResponse();


        try {
            String ticketId = request.getTicketId();
            //  如果是无花果工单需要对ticketId做个转换，等无花果改造完替换掉
            if (BridgeSwitch.TICKET_ID_CONVERT.contains(receiveParams.getFromAppkey())) {
                ticketId = pickUpTicketService.covertTicketId(ticketId);
            }
            if(CpTicketStatus.DONE.name().equals(request.getProcessStatus())){
                PickUpTicketCompleteRequest completeRequest = new PickUpTicketCompleteRequest();
                completeRequest.setReplyMsg(request.getReplayMsg());
                completeRequest.setReplyFiles(request.getFiles());
                completeRequest.setTicketIdList(Lists.newArrayList());


                completeRequest.setRole(TicketRoleEnum.cpCs.name());
                completeRequest.setAccount(BridgeConstants.UserAccount.CP_SYSTEM_ACCOUNT);

                pickUpTicketService.completeTicket(Long.parseLong(ticketId), completeRequest);
            }else{
                PickUpTicketReplyRequest replyRequest = new PickUpTicketReplyRequest();
                replyRequest.setReplyMsg(request.getReplayMsg());
                replyRequest.setReplyFiles(request.getFiles());
                replyRequest.setTicketIdList(Lists.newArrayList());


                replyRequest.setRole(TicketRoleEnum.cpCs.name());
                replyRequest.setAccount(BridgeConstants.UserAccount.CP_SYSTEM_ACCOUNT);

                pickUpTicketService.replyTicket(Long.parseLong(ticketId), replyRequest);
            }

            response.setSuccess(true);
            PickUpLogUtil.info("ticket_cp_replay_success");
        } catch (BridgeBaseException e) {
            response.setSuccess(false);
            response.setErrorCode(e.getErrorCode());
            response.setErrorMsg(e.getErrorMessage());
            PickUpLogUtil.errLog("", PickUpConstants.Action.PICK_UP_TICKET_CREATE_LINK.name(), e.getErrorCode(), e.getErrorMessage(), e);
        } catch (Throwable e) {
            response.setSuccess(false);
            response.setErrorMsg(e.getMessage());
            PickUpLogUtil.errLog("", PickUpConstants.Action.PICK_UP_TICKET_CREATE_LINK.name(), "", e.getMessage(), e);
        }
        return response;
    }
}
