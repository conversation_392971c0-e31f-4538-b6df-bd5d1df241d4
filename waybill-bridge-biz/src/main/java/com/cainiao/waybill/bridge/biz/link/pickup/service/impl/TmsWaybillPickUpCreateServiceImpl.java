package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastvalidator.constraints.exception.FastValidatorException;
import com.alibaba.security.tenant.common.service.RequestService;

import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.TraceFeatureKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpCreateOrderParamConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpDetailBizTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.exception.PickUpBusinessException;
import com.cainiao.waybill.bridge.biz.pickup.dto.GoodsInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpCreateOrderRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpCreateOrderResponse;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.RoutingInfo;
import com.cainiao.waybill.bridge.biz.pickup.routing.dto.RouteBaseRequest;
import com.cainiao.waybill.bridge.biz.pickup.routing.manager.RoutingManager;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillKeyLogService;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillPickUpOrderService;
import com.cainiao.waybill.bridge.biz.user.manager.CnUserManager;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpOrderCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpPlatConfigHelp;
import com.cainiao.waybill.bridge.biz.wrapper.ContentRiskWrapper;
import com.cainiao.waybill.bridge.common.base.ScenarioConstant;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.LoggerMonitorUtil;
import com.cainiao.waybill.bridge.constants.CustomerManageStatusEnum;
import com.cainiao.waybill.bridge.model.domain.WaybillKeyLogDO;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.eagleeye.EagleEye;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_CREATE.WaybillPickUpCreateRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PICK_UP_CREATE.WaybillPickUpCreateResponse;
import com.taobao.pac.client.sdk.receiveservice.WaybillPickUpCreateService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/6/25 下午3:19
 */
public class TmsWaybillPickUpCreateServiceImpl implements WaybillPickUpCreateService {
    @Resource
    private WaybillPickUpOrderService waybillPickUpOrderService;

    @Resource
    private WaybillKeyLogService waybillKeyLogService;

    @Resource
    private ContentRiskWrapper contentRiskWrapper;

    @Resource
    private RoutingManager routingManager;

    @Resource
    private RequestService requestService;

    @Resource
    private CnUserManager cnUserManager;

    private static final String DEFAULT_SPLIT_FH = ";";

    private static final String DEFAULT_SPLIT_XHX = "_";

    private static final String DEFAULT_SPLIT_SMH = "::";


    private static final String WEI_DIAN_FROM_APP_KEY = "156650";

    @Override
    public WaybillPickUpCreateResponse invoke(ReceiveParams<WaybillPickUpCreateRequest> receiveParams) {
        PickUpLogUtil.info("淘外下单入参："+JSON.toJSONString(receiveParams));
        PickUpCreateOrderRequest createRequest = new PickUpCreateOrderRequest();
        WaybillPickUpCreateRequest linkRequestData = receiveParams.getRequestDataObject();
        WaybillPickUpCreateResponse linkResponse = new WaybillPickUpCreateResponse();

        //  现在生成orderId的格式为：fromAppKey#orderChannel_outerOrderCode，fromAppKey与阿里云的UID是不包含'_'与'#'的，但outerOrderCode不保证。
        //  微点之前本身下单时传的orderChannel为"WEI_DIAN"包含下划线，所以需要在这里转化处理掉下划线。微点取消与回传物流详情是不需要orderChannel字段的，故不需要处理
        String orderChannel = linkRequestData.getOrderChannels();
        String appKey = receiveParams.getFromAppkey();
        // 客户服务可用性检测
        if(PickUpOrderCommonUtil.checkCustomerCurrForbid(appKey, orderChannel)){
            linkResponse.setSuccess(false);
            linkResponse.setErrorCode(PickUpConstants.Error.ORDER_FORBID_ERROR.getErrorCode());
            linkResponse.setErrorMsg(PickUpConstants.Error.ORDER_FORBID_ERROR.getErrorMsg());
            return linkResponse;
        }
        // 标记拒接单CP
        List<String> forbidCpList = PickUpOrderCommonUtil.markRejectOrderCp(orderChannel);
        if(!CollectionUtils.isEmpty(forbidCpList)){
            createRequest.setForbidCpCodes(forbidCpList);
        }
        if (appKey.equals(WEI_DIAN_FROM_APP_KEY)) {
            orderChannel = orderChannel.replaceAll("_", "");
            linkRequestData.setOrderChannels(orderChannel);
        }
        LoggerMonitorUtil.start(ScenarioConstant.CREATE_ORDER, orderChannel);

        BeanUtils.copyProperties(linkRequestData, createRequest);
        createRequest.setNeedPrintData(linkRequestData.isNeedPrintData());

        String resCode = PickUpCommonUtil.buildResCode(receiveParams.getFromAppkey(), orderChannel);
        createRequest.setResCode(resCode);
        createRequest.setFromAppKey(receiveParams.getFromAppkey());
        Map<String, String> featureMap = Maps.newHashMap();
        String cnUserId = PickUpPlatConfigHelp.getCnUserId(receiveParams.getFromAppkey(), orderChannel);

        featureMap.put(TraceFeatureKey.LINK_CP_CODE, receiveParams.getCpCode());
        if(StringUtils.isNotBlank(cnUserId)){
            featureMap.put(TraceFeatureKey.PRE_PAY_CN_USER_ID, cnUserId);
            // 校验用户余额是否已被管控
            String status = cnUserManager.queryPreChargeManageStatus(cnUserId);
            if(StringUtils.equals(CustomerManageStatusEnum.INVALID.getStatus(), status)){
                throw new PickUpBusinessException(PickUpConstants.Error.WAYBILL_PRE_CHARGE_AMOUNT_LOW);
            }
        }
        createRequest.setFeatureMap(featureMap);

        AddressDTO sendAddress = new AddressDTO();
        BeanUtils.copyProperties(linkRequestData.getSendAddress(), sendAddress);
        createRequest.setSendAddress(sendAddress);

        AddressDTO consigneeAddress = new AddressDTO();
        BeanUtils.copyProperties(linkRequestData.getConsigneeAddress(), consigneeAddress);
        createRequest.setConsigneeAddress(consigneeAddress);
        //  转换下单商品信息字段
        setGoodsInfo(linkRequestData, createRequest);

        //  bizType为空，默认为实时下单模式
        if (linkRequestData.getBizType() == null) {
            createRequest.setBizType(PickUpDetailBizTypeEnum.NORMAL.getValue());
        }
        // 获取缓存失败单，短时间内同一用户同一地址重复下单时直接返回失败信息
        WaybillPickUpCreateResponse cacheResponse = waybillPickUpOrderService.getCacheFailOrder(createRequest);
        if(null != cacheResponse){
            linkResponse = cacheResponse;
            return linkResponse;
        }

        PickUpCreateOrderResponse createOrderResponse;
        String errorCode = "";
        String errorMsg = "";
        Throwable defaultException = null;
        try {

            // 1 风控-制裁
            contentRiskWrapper.checkRisk(createRequest);
            // 2 风控-违禁词
            contentRiskWrapper.checkRiskWord(createRequest);

            //  调用分拣码接口，获取网点code，若在黑名单，则不接该单
            if (BridgeSwitch.deniableOrderFromAppKey.contains(createRequest.getFromAppKey())) {
                RouteBaseRequest routeBaseRequest = new RouteBaseRequest();
                routeBaseRequest.setCpCode(PickUpConstants.Cp.YTO.name());
                //  都用寄件地址去算
                routeBaseRequest.setReceiveAddress(sendAddress);
                routeBaseRequest.setSendAddress(sendAddress);
                String branchCode = null;
                //  调用分拣码接口可能出现超时等异常情况，该case下，不能影响正常下单流程
                try {
                    RoutingInfo routingInfo = routingManager.calculateRoutingInfo(routeBaseRequest);
                    branchCode = routingInfo.getReceiveBranchCode();
                } catch (Throwable e) {
                    PickUpLogUtil.errLog(createRequest.getOuterOrderCode(), PickUpConstants.Action.PICK_UP_CREATE_LINK_ROUTE_ERROR.name(), "", e.getMessage(), e);
                }
                if (StringUtil.isNotBlank(branchCode) && BridgeSwitch.cancelRateBranchCode.contains(branchCode)) {
                    linkResponse.setSuccess(false);
                    linkResponse.setErrorCode(PickUpConstants.Error.REJECT_CUR_ORDER.getErrorCode());
                    linkResponse.setErrorMsg(PickUpConstants.Error.REJECT_CUR_ORDER.getErrorMsg());
                    return linkResponse;
                }
            }

            createOrderResponse = waybillPickUpOrderService.create(createRequest);
            BeanUtils.copyProperties(createOrderResponse, linkResponse);
            if(StringUtil.isNotBlank(createOrderResponse.getRealCpCode())){
                linkResponse.setCpCode(createOrderResponse.getRealCpCode());
            }
        } catch (FastValidatorException e) {
            linkResponse.setSuccess(false);
            errorCode = e.getCode();
            errorMsg = e.getMessage();
            linkResponse.setErrorCode(errorCode);
            linkResponse.setErrorMsg(errorMsg);
            defaultException = e;
        } catch (BridgeBaseException e) {
            linkResponse.setSuccess(false);
            errorCode = e.getErrorCode();
            errorMsg = e.getErrorMessage();
            linkResponse.setErrorCode(errorCode);
            linkResponse.setErrorMsg(errorMsg);
            defaultException = e;
        } catch (PickUpBusinessException pickUpBusinessException){
            linkResponse.setSuccess(false);
            errorCode = pickUpBusinessException.getErrorCode();
            errorMsg = pickUpBusinessException.getMessage();
            linkResponse.setErrorCode(errorCode);
            linkResponse.setErrorMsg(errorMsg);
            defaultException = pickUpBusinessException;
        } catch (BridgeBusinessException bridgeBusinessException) {
            linkResponse.setSuccess(false);
            errorCode = bridgeBusinessException.getErrorCode();
            errorMsg = bridgeBusinessException.getMessage();
            linkResponse.setErrorCode(errorCode);
            linkResponse.setErrorMsg(errorMsg);
            defaultException = bridgeBusinessException;
        } catch (Throwable e) {
            linkResponse.setSuccess(false);
            errorCode = Error.SYSTEM_ERROR.getErrorCode();
            errorMsg = e.getMessage();
            linkResponse.setErrorCode(errorCode);
            linkResponse.setErrorMsg(errorMsg);
            defaultException = e;
        }finally {
            if(null != defaultException){
                createRequest.setBizRoutingContext(null);
                PickUpLogUtil.errLog(createRequest.getOuterOrderCode(), PickUpConstants.Action.PICK_UP_CREATE_LINK.name()+"|"+orderChannel, errorCode, errorMsg, createRequest, defaultException);
                // 记录关键日志 补充监控平台统计周期不足够长
                WaybillKeyLogDO waybillKeyLogDO = new WaybillKeyLogDO();
                waybillKeyLogDO.setOrderChannels(orderChannel);
                waybillKeyLogDO.setTraceId(EagleEye.getTraceId());
                waybillKeyLogDO.setBizId(createRequest.getOuterOrderCode());
                waybillKeyLogDO.setAction(PickUpConstants.Action.PICK_UP_CREATE_LINK.name());
                waybillKeyLogDO.setLogCode(errorCode);
                waybillKeyLogDO.setLogLevel(PickUpConstants.LogLevel.ERROR);
                waybillKeyLogDO.setLogMsg(errorMsg);
                waybillKeyLogDO.setBusinessData(JSONObject.toJSONString(createRequest));
                waybillKeyLogDO.setException(ExceptionUtils.getStackTrace(defaultException));
                waybillKeyLogDO.setRemark("下单失败日志");
                waybillKeyLogService.saveLog(waybillKeyLogDO);
            }
            if(!linkResponse.isSuccess()){
                // 缓存失败单，短时间内同一用户该地址重复下单时直接返回失败信息
                waybillPickUpOrderService.cacheFailOrder(createRequest, linkResponse);
            }
        }
        LoggerMonitorUtil.end(linkResponse.isSuccess(), linkResponse.getErrorCode());
        PickUpLogUtil.info(JSON.toJSONString(linkResponse));
        return linkResponse;
    }


    private void setGoodsInfo(WaybillPickUpCreateRequest linkRequestData, PickUpCreateOrderRequest createRequest) {
        if (CollectionUtils.isEmpty(linkRequestData.getGoodsInfos())) {
            return;
        }
        List<GoodsInfo> goodsInfos = Lists.newArrayList();
        for (com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_CREATE.GoodsInfo goodsInfoParam : linkRequestData.getGoodsInfos()) {
            GoodsInfo goodsInfo = new GoodsInfo();
            BeanUtils.copyProperties(goodsInfoParam, goodsInfo);
            if (StringUtils.isNotBlank(goodsInfoParam.getExtendInfo())) {
                JSONObject extendInfo = JSONObject.parseObject(goodsInfoParam.getExtendInfo());
                goodsInfo.setPrice(extendInfo.getString(PickUpCreateOrderParamConstants.Create.PRICE));
                goodsInfo.setForwardMailNo(extendInfo.getString(PickUpCreateOrderParamConstants.Create.FORWARD_MAIL_NO));
                goodsInfo.setForwardCpCode(extendInfo.getString(PickUpCreateOrderParamConstants.Create.FORWARD_CP_CODE));
                goodsInfo.setGoodUniqueKey(extendInfo.getString(PickUpCreateOrderParamConstants.Create.GOOD_UNIQUE_KEY));
                goodsInfo.setCategory(extendInfo.getString(PickUpCreateOrderParamConstants.Create.CATEGORY));
            }
            goodsInfos.add(goodsInfo);
        }
        createRequest.setGoodsInfos(goodsInfos);
    }

}
