package com.cainiao.waybill.bridge.innersupport.ad.biz.oss;

import com.aliyun.oss.ClientConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.comm.Protocol;
import com.cainiao.waybill.bridge.innersupport.ad.config.BridgeInnerSwitch;

public class AdOssClientFactory {

    private static final String ACCESS_KEY_ID = BridgeInnerSwitch.CLOUD_ACCOUNT_ACCESS_KEY;
    private static final String ACCESS_KEY_SECRET = "2bhrEgGyrbbTTqGwB5snQDS6wPHTr8";
    public static final String ENDPOINT = "oss-cn-hangzhou.aliyuncs.com";

    /**
     * 广告工单系统 Bucket
     */
    public final static String AD_WORK_ORDER_BUCKET_NAME = "waybill-bridge-ad-work-order";

    /**
     * 广告工单系统，图片文件目录
     */
    public static final String OSS_IMG_FILE_DIR = "img";

    public static OSSClient client = new OSSClient(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET);

    private static final ClientConfiguration config;

    static {
        config = new ClientConfiguration();
        config.setProtocol(Protocol.HTTPS);
    }

    public static OSSClient new_client = new OSSClient(ENDPOINT, ACCESS_KEY_ID, ACCESS_KEY_SECRET, config);

    private AdOssClientFactory() {
    }

    public static OSSClient getOssClient() {
        return client;
    }

    public static OSSClient getOssClient(boolean needHttpsConf) {
        return needHttpsConf ? new_client : client;
    }

    public static String getAccessKeyId() {
        return ACCESS_KEY_ID;
    }

    public static String getAccessKeySecret() {
        return ACCESS_KEY_SECRET;
    }

    public static String getENDPOINT() {
        return ENDPOINT;
    }
}
