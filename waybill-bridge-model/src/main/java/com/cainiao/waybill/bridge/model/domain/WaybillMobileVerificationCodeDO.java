package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * 短信验证码信息
 *
 * Created by shouyuan.lzl on 2017-04-18 4:00 PM.
 */
public class WaybillMobileVerificationCodeDO {

    /**
     * 主键
     */
    private long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 商家id
     */
    private long sellerId;

    /**
     * 接收验证码的手机号
     */
    private String mobile;

    /**
     * 短信验证码
     */
    private String verificationCode;

    /**
     * 验证码类型 1 绑定商家和小件员关系
     */
    private int verificationType;

    /**
     * 验证正确次数
     */
    private int verificationCount;

    /**
     * 验证错误次数
     */
    private int verificationErrorCount;

    /**
     * 验证码发送次数
     */
    private int smsSendCount;

    /**
     * 验证码失效时间
     */
    private Date deadTime;

    /**
     * 最后一次验证时间
     */
    private Date lastVerificationTime;

    /**
     * 最后一次短信发送时间
     */
    private Date lastSendTime;

    /**
     * setter for column 主键
     */
    public void setId(long id) {
        this.id = id;
    }

    /**
     * getter for column 主键
     */
    public long getId() {
        return this.id;
    }

    /**
     * setter for column 创建时间
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * getter for column 创建时间
     */
    public Date getGmtCreate() {
        return this.gmtCreate;
    }

    /**
     * setter for column 修改时间
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * getter for column 修改时间
     */
    public Date getGmtModified() {
        return this.gmtModified;
    }

    /**
     * setter for column 商家id
     */
    public void setSellerId(long sellerId) {
        this.sellerId = sellerId;
    }

    /**
     * getter for column 商家id
     */
    public long getSellerId() {
        return this.sellerId;
    }

    /**
     * setter for column 接收验证码的手机号
     */
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    /**
     * getter for column 接收验证码的手机号
     */
    public String getMobile() {
        return this.mobile;
    }

    /**
     * setter for column 短信验证码
     */
    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    /**
     * getter for column 短信验证码
     */
    public String getVerificationCode() {
        return this.verificationCode;
    }

    /**
     * setter for column 验证码类型 1 绑定商家和小件员关系
     */
    public void setVerificationType(int verificationType) {
        this.verificationType = verificationType;
    }

    /**
     * getter for column 验证码类型 1 绑定商家和小件员关系
     */
    public int getVerificationType() {
        return this.verificationType;
    }

    /**
     * setter for column 验证正确次数
     */
    public void setVerificationCount(int verificationCount) {
        this.verificationCount = verificationCount;
    }

    /**
     * getter for column 验证正确次数
     */
    public int getVerificationCount() {
        return this.verificationCount;
    }

    /**
     * setter for column 验证错误次数
     */
    public void setVerificationErrorCount(int verificationErrorCount) {
        this.verificationErrorCount = verificationErrorCount;
    }

    /**
     * getter for column 验证错误次数
     */
    public int getVerificationErrorCount() {
        return this.verificationErrorCount;
    }

    /**
     * setter for column 验证码发送次数
     */
    public void setSmsSendCount(int smsSendCount) {
        this.smsSendCount = smsSendCount;
    }

    /**
     * getter for column 验证码发送次数
     */
    public int getSmsSendCount() {
        return this.smsSendCount;
    }

    /**
     * setter for column 验证码失效时间
     */
    public void setDeadTime(Date deadTime) {
        this.deadTime = deadTime;
    }

    /**
     * getter for column 验证码失效时间
     */
    public Date getDeadTime() {
        return this.deadTime;
    }

    public Date getLastVerificationTime() {
        return lastVerificationTime;
    }

    public void setLastVerificationTime(Date lastVerificationTime) {
        this.lastVerificationTime = lastVerificationTime;
    }

    public Date getLastSendTime() {
        return lastSendTime;
    }

    public void setLastSendTime(Date lastSendTime) {
        this.lastSendTime = lastSendTime;
    }

    @Override
    public String toString() {
        return "WaybillMobileVerificationCodeDO{" +
                "id=" + id +
                ", gmtCreate=" + gmtCreate +
                ", gmtModified=" + gmtModified +
                ", sellerId=" + sellerId +
                ", mobile='" + mobile + '\'' +
                ", verificationCode='" + verificationCode + '\'' +
                ", verificationType=" + verificationType +
                ", verificationCount=" + verificationCount +
                ", verificationErrorCount=" + verificationErrorCount +
                ", smsSendCount=" + smsSendCount +
                ", deadTime=" + deadTime +
                ", lastVerificationTime=" + lastVerificationTime +
                ", lastSendTime=" + lastSendTime +
                '}';
    }
}
