package com.cainiao.waybill.bridge.innersupport.ad.util;

import com.alibaba.fastvalidator.core.FastValidatorUtils;
import org.apache.commons.collections.CollectionUtils;
import javax.validation.ConstraintViolation;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description : 校验工具类
 */
public class ValidatorUtils {

    /**
     * 验证并返回单个错误信息
     * @param t 待验证的对象
     * @return 验证不通过时返回第一条错误信息；验证通过时返回null
     */
    public static <T> String validateAndReturnSingleErrorMsg(T t) {
        Set<ConstraintViolation<T>> violations = FastValidatorUtils.validate(t);
        if (CollectionUtils.isEmpty(violations)) {
            return null;
        }
        return violations.iterator().next().getMessage();
    }

    public static <T> String validateAndReturnAllErrorMsg(T t) {
        Set<ConstraintViolation<T>> violations = FastValidatorUtils.validate(t);
        if (CollectionUtils.isEmpty(violations)) {
            return null;
        }
        return violations.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(","));
    }

}
