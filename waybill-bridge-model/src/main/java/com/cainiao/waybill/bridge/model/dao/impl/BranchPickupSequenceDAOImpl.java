package com.cainiao.waybill.bridge.model.dao.impl;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.model.common.AbstractDAO;
import com.cainiao.waybill.bridge.model.dao.BranchPickupSequenceDAO;
import com.cainiao.waybill.bridge.model.domain.BranchSequenceDO;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant;
import com.taobao.common.dao.persistence.exception.DAOException;
import com.taobao.tddl.client.sequence.exception.SequenceException;
import com.taobao.tddl.client.sequence.impl.GroupSequence;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-30
 */
public class BranchPickupSequenceDAOImpl extends AbstractDAO implements BranchPickupSequenceDAO {

    @Resource
    private GroupSequence branchPickupSequenceSequence;

    @Override
    public BranchSequenceDO queryByCpAndBranchCode(String cpCode, String branchCode) throws DAOException {
        // 1. select for update
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("cpCode", cpCode);
        paramMap.put("branchCode", branchCode);
        return  (BranchSequenceDO)this.executeQueryForObject("branchSequence.selectByCpAndBranchCode", paramMap, getDBRoute());
    }

    @Override
    public Integer insert(BranchSequenceDO branchSequenceDO) throws DAOException {
        branchSequenceDO.setId(nextId());
        return this.executeUpdate("branchSequence.insert", branchSequenceDO, getDBRoute());
    }

    @Override
    public Long incrementAndGet(String cpCode, String branchCode) throws DAOException {
        // 1. select for update
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("cpCode", cpCode);
        paramMap.put("branchCode", branchCode);
        return  (Long)this.executeQueryForObject("branchSequence.selectForUpdate", paramMap, getDBRoute());
    }

    //通过TDDL获取唯一主键
    private Long nextId() throws DAOException {
        try {
            return branchPickupSequenceSequence.nextValue();
        } catch (SequenceException e) {
            throw new DAOException(WaybillErrorConstant.SystemError.TDDL_SEQUENCE_ERROR.getErrorMsg(), e);
        } catch (Throwable e) {
            throw new DAOException(e);
        }
    }
}
