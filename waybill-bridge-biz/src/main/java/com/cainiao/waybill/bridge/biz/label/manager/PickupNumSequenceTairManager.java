package com.cainiao.waybill.bridge.biz.label.manager;

import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.taobao.common.dao.persistence.exception.DAOException;

/**
 * 管理揽件码序列的 tair manager
 *
 * <AUTHOR>
 * @date 2017/05/23
 */
public interface PickupNumSequenceTairManager {

    /**
     * 根据序列号获取
     *
     * @param serialNum
     * @return
     * @throws BridgeBaseException
     * @throws DAOException
     */
    Long getPickupNum(Long serialNum) throws DAOException, BridgeBaseException;

}
