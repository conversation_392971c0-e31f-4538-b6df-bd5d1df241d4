package com.cainiao.waybill.bridge.common.waybill.constants;

import java.util.HashSet;

/**
 * 物流详情节点类型非常得多，因不同得快递，不用的物流场景而不同。下面列出了常用的类型
 *
 * <AUTHOR>
 * @date 2021/6/16
 */
public class WaybillPickUpActionConstant {

    /**
     * 圆通修改预约时间推送
     */
    public static final String YTO_UPDATE_APPOINT_TIME = "ORDER_BOOKING";

    /**
     * 异步下单
     */
    public static final String ASYNC_ORDER = "ASYNC_ORDER";

    /**
     * 异步订阅物流详情
     */
    public static final String LD_SUB_ASYNC = "LD_SUB_ASYNC";

    /**
     * 取号成功
     */
    public static final String APPLY_NAME = "APPLY_NEW";

    /**
     * 针对韵达这种通过"核价"状态回传的重量的节点，且核价与揽收含义不一致的场景
     */
    public static final String CHECK = "CHECK";

    /**
     * 取件，但未上传单号
     * <p>
     * 对外推送的时候，统一配置成DROPIN
     */
    public static final String DROP_IN = "DROPIN";

    /**
     * 揽收, 揽收可能会有多种类型
     * <p>
     * 对外推送的时候，统一配置成GOT
     */
    public static final String GOT = "GOT";

    /**
     * 待接单
     */
    public static final String WAIT = "WAIT";

    /**
     * 揽收，揽收可能会有多种类型
     */
    public static final String TMS_ACCEPT = "TMS_ACCEPT";

    /**
     * 揽收，揽收可能会有多种类型
     * tip：LD团队推送的ACCEPT表示揽收，YTO推送的ACCEPT表示已接单
     */
    public static final String ACCEPT = "ACCEPT";

    /**
     * 分单：这里的分单指的是分配到小件员，但小件员不是默认接单的情况(向圆通分到一个小件员即代表该小件员默认接单，韵达则只代表分到这个小件员，接单是单独的步骤)
     */
    //public static final String ASSIGNED = "ASSIGNED";

    /**
     * 出站
     */
    public static final String DEPARTURE = "DEPARTURE";

    /**
     * 派件扫描
     */
    public static final String SENT_SCAN = "SENT_SCAN";

    /**
     * 派送中
     */
    public static final String TMS_DELIVERING = "TMS_DELIVERING";

    /**
     * SIGNED
     */
    public static final String SIGNED = "SIGNED";

    public static final String SIGN = "SIGN";

    /**
     * CP取消
     */
    public static final String FAIL = "FAIL";

    /**
     * 丢失
     */
    public static final String LOST = "LOST";

    /**
     * 转运EMS
     */
    public static final String TO_EMS = "TO_EMS";

    /**
     * 运输中
     */
    public static final String SENT_CITY = "SENT_CITY";

    /**
     * 更新重量
     */
    public static final String MODIFY_WEIGHT = "MODIFY_WEIGHT";

    /**
     * 运输中
     */
    public static final String TRANSPORT = "TRANSPORT";

    /**
     * 派送中
     */
    public static final String DELIVERING = "DELIVERING";

    /**
     * 其他
     */
    public static final String OTHER = "OTHER";




    /**
     * 异常反馈
     */
    public static final String EXCEPTION_RESPONSE = "EXCEPTION_RESPONSE";

    /**
     * 换单 - 无花果特有
     */
    public static final String REPLACE_WAYBILL = "REPLACE_WAYBILL";

    /**
     * 揽收的actionType汇总
     * <p>
     * 揽收的状态字段目前有
     * GOT  TMS_ACCEPT ACCEPT
     * 将来还可能新增，所以这里用一个set记录
     */
    public static HashSet<String> GOT_SET;

    public static String getActionDesc(String action) {
        if (ACCEPT.equals(action)) {
            return "已接单";
        }
        // 同步中推，异步不推
        if (WAIT.equals(action)) {
            return "待接单";
        }
        if (GOT.equals(action)) {
            return "已揽收";
        }
        if (MODIFY_WEIGHT.equals(action)) {
            return "更新重量";
        }
        if (FAIL.equals(action)) {
            return "已取消";
        }
        if (DROP_IN.equals(action)) {
            return "已上门";
        }
        if (TRANSPORT.equals(action)) {
            return "运输中";
        }
        if (DELIVERING.equals(action)) {
            return "派送中";
        }
        if (SIGN.equals(action)) {
            return "已签收";
        }
        if(REPLACE_WAYBILL.equals(action)){
            return "换单";
        }
        return "";
    }

}
