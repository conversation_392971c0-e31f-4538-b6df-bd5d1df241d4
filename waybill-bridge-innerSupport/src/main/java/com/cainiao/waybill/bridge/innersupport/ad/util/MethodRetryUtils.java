package com.cainiao.waybill.bridge.innersupport.ad.util;

import com.github.rholder.retry.*;
import com.google.common.base.Predicate;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * 重试工具类
 *
 * <AUTHOR>
 */
public class MethodRetryUtils {

    /**
     * 根据用户自定义的返回结果验证方式
     * 来判断是否需要重试
     * Predicate.apply          返回为true时 重试 否则结束调用。
     * @param times             重试次数
     * @param fixedWait         重试间隔等待时间 单位毫秒
     * @param checkResult       用户自定义返回结果验证代码块
     * @param executeContent    执行内容
     * @param <T>               返回值类型
     * @return
     * @throws ExecutionException
     * @throws RetryException
     */
    public static  <T> T retryIfResult(int times, long fixedWait, Predicate<T> checkResult, Callable<T> executeContent)
            throws ExecutionException, RetryException {
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
                .retryIfResult(checkResult)
                .withStopStrategy(StopStrategies.stopAfterAttempt(times))
                .withWaitStrategy(WaitStrategies.fixedWait(fixedWait, TimeUnit.MILLISECONDS)).build();
        return retryer.call(executeContent);
    }

    /**
     * executeContent执行时抛出异常与
     * 用户指定的异常类型exceptionClass
     * 一致时尝试重试
     * @param times             重试次数
     * @param fixedWait         重试间隔等待时间 单位毫秒
     * @param exceptionClass    重试异常类型
     * @param executeContent    执行内容
     * @param <T>               返回值类型
     * @return
     * @throws ExecutionException
     * @throws RetryException
     *
     */
    public static <T> T retryIfException(int times, long fixedWait, Class<? extends Throwable> exceptionClass, Callable<T> executeContent)
            throws ExecutionException, RetryException {
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
                .retryIfExceptionOfType(exceptionClass)
                .withStopStrategy(StopStrategies.stopAfterAttempt(times))
                .withWaitStrategy(WaitStrategies.fixedWait(fixedWait, TimeUnit.MILLISECONDS)).build();
        return retryer.call(executeContent);
    }

    /**
     * 单次调用超时重试
     * 默认调用出现运行时异常重试
     * @param times             重试次数
     * @param fixedWait         重试间隔时间
     * @param timeout           每次重试超时时间
     * @param executeContent    执行内容
     * @param <T>               返回值泛型与执行内容返回值泛型一致
     * @return
     * @throws ExecutionException
     * @throws RetryException
     */
    public static <T> T retryIfTimeOut(int times, long fixedWait, long timeout, Callable<T> executeContent)
            throws ExecutionException, RetryException {
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
                .retryIfException()
                .withStopStrategy(StopStrategies.stopAfterAttempt(times))
                .withWaitStrategy(WaitStrategies.fixedWait(fixedWait, TimeUnit.MILLISECONDS))
                .withAttemptTimeLimiter(AttemptTimeLimiters.fixedTimeLimit(timeout, TimeUnit.MILLISECONDS))
                .build();
        return retryer.call(executeContent);
    }

    /**
     * 根据用户自定义的返回结果验证方式来判断是否需要重试或者executeContent执行时抛出异常与用户指定的异常类型exceptionClass一致时尝试重试
     *
     * @param times          重试次数
     * @param fixedWait      重试间隔等待时间 单位毫秒
     * @param exceptionClass 重试异常类型
     * @param checkResult    用户自定义返回结果验证代码块
     * @param executeContent 执行内容
     * @param <T>            返回值类型
     * @return
     * @throws ExecutionException
     * @throws RetryException
     */
    public static <T> T retryIfExceptionOrResult(int times, long fixedWait, Class<? extends Throwable> exceptionClass, Predicate<T> checkResult, Callable<T> executeContent)
            throws ExecutionException, RetryException {
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
                .retryIfExceptionOfType(exceptionClass)
                .retryIfResult(checkResult)
                .withStopStrategy(StopStrategies.stopAfterAttempt(times))
                .withWaitStrategy(WaitStrategies.fixedWait(fixedWait, TimeUnit.MILLISECONDS)).build();
        return retryer.call(executeContent);
    }
}
