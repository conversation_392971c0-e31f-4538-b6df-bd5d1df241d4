package com.cainiao.waybill.bridge.biz.hsf.consumer;

import com.alibaba.alipmc.api.ProcessInstanceService;
import com.alibaba.alipmc.api.TaskService;
import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.security.tenant.common.service.RequestService;

import com.cainiao.address.api.AddressCommonService;
import com.cainiao.bearlake.api.user.service.UserQueryService;
import com.cainiao.bearlake.node.service.NodeService;
import com.cainiao.cloudprint.CloudPrintService;
import com.cainiao.cnmd.opplatform.scenario.collect.service.CollectStationOrderOSearchService;
import com.cainiao.cnuser.client.service.CnUserInfoQueryService;
import com.cainiao.finance.fee.client.service.CfFeeOrderService;
import com.cainiao.finance.prerun.client.service.CfRechargeGatewayService;
import com.cainiao.geography.frontend.api.AddressStructurationService;
import com.cainiao.hyena.alibaba.common.service.CpExceptionAggregationService;
import com.cainiao.middleware.user.client.CnSmsUserClient;
import com.cainiao.rc.service.RcCompanyReadService;
import com.cainiao.waybill.bridge.biz.hsf.HelloService;
import com.cainiao.waybill.client.privacy.service.PrivacyQueryService;
import com.cainiao.waybill.common.seller.service.WaybillCloudPrintServiceForInternalSystem;
import com.cainiao.waybill.common.seller.service.WaybillMainServiceForInternalSystem;
import com.cainiao.waybill.common.seller.service.WaybillQueryServiceForInternalSystem;
import com.cainiao.waybill.common.subscription.service.CpAccountService;
import com.cainiao.waybill.galaxy.isv.service.CloudPrinterService;
import com.cainiao.waybill.galaxy.order.service.FastShipmentService;
import com.cainiao.waybill.galaxy.user.service.PriceOpenService;
import com.cainiao.waybill.galaxy.order.service.RefundService;
import com.cainiao.waybill.number.client.WaybillNumberCommonService;
import com.cainiao.waybill.trade.api.contract.service.WtContractAccountService;
import com.taobao.cainiao.waybillprint.client.service.TemplatesTopService;
import com.taobao.forest.service.cairo.RemoteWriteService;
import com.taobao.loc.service.orderinfo.LocReadService;
import com.taobao.logisticsdetail.common.service.basic.LogisticsDetailReadService;
import com.taobao.tpn.api.AuthorizeService;
import org.springframework.context.annotation.Configuration;

/**
 * hsf服务的统一个Config类，在其它需要使用的地方，直接@Autowired注入即可。详情见
 * http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf
 *
 * <AUTHOR>
 */
@Configuration
public class HSFConfig {
    @HSFConsumer
    private HelloService helloService;
    @HSFConsumer
    private CpAccountService cpAccountService;
    @HSFConsumer
    private UserQueryService userQueryService;
    @HSFConsumer(serviceVersion = "${hsf.print.version}")
    private CloudPrintService cloudPrintService ;
    @HSFConsumer
    private WaybillCloudPrintServiceForInternalSystem waybillCloudPrintServiceForInternalSystem ;
    @HSFConsumer(serviceVersion = "${hsf.print.version}")
    private TemplatesTopService templatesTopService;
    @HSFConsumer
    private WaybillMainServiceForInternalSystem waybillMainServiceForInternalSystem;
    @HSFConsumer
    private NodeService nodeService;
    @HSFConsumer
    RcCompanyReadService rcCompanyReadService;
    @HSFConsumer
    AuthorizeService authorizeService;
    @HSFConsumer
    AddressCommonService addressCommonService;
    @HSFConsumer
    LocReadService locReadService;
    @HSFConsumer
    private CnSmsUserClient cnSmsUserClient ;

    @HSFConsumer
    PrivacyQueryService privacyQueryService;

    @HSFConsumer
    WaybillNumberCommonService waybillNumberCommonService;

    @HSFConsumer(serviceVersion = "1.0.0.cncom")
    CollectStationOrderOSearchService collectStationOrderOSearchService;

    @HSFConsumer
    CnUserInfoQueryService cnUserInfoQueryService;

    /**
     * 查询品类名称
     */
    @HSFConsumer(serviceVersion = "${hsf.RemoteWriteService.version}")
    RemoteWriteService remoteWriteService;
    @HSFConsumer
    WaybillQueryServiceForInternalSystem waybillQueryServiceForInternalSystem;

    @HSFConsumer
    LogisticsDetailReadService logisticsDetailReadService;

    @HSFConsumer(serviceVersion = "${hsf.print.version}")
    AddressStructurationService addressStructurationService;

    @HSFConsumer(serviceVersion = "1.0.0_isec", clientTimeout = 50)
    RequestService requestService;

    @HSFConsumer(clientTimeout = 10000)
    FastShipmentService fastShipmentService;


    @HSFConsumer(clientTimeout = 10000)
    PriceOpenService priceOpenService;

    @HSFConsumer(clientTimeout = 10000)
    RefundService refundService;


    @HSFConsumer(serviceVersion = "1.1.5")
    ProcessInstanceService processInstanceService;

    @HSFConsumer(serviceVersion = "1.1.5")
    TaskService taskService;

    @HSFConsumer(serviceVersion = "1.0.0")
    CpExceptionAggregationService cpExceptionAggregationService;

    @HSFConsumer(serviceVersion = "1.0.0")
    WtContractAccountService wtContractAccountService;

    @HSFConsumer(serviceVersion = "1.0.0")
    CfRechargeGatewayService cfRechargeGatewayService;

    @HSFConsumer(serviceVersion = "1.0.0")
    private CfFeeOrderService cfFeeOrderService;

    @HSFConsumer(serviceVersion = "1.0.0")
    private CloudPrinterService cloudPrinterService;

}
