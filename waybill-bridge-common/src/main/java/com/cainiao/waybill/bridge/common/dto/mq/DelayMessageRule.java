package com.cainiao.waybill.bridge.common.dto.mq;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * MetaQ 延时消息控制规则
 *
 * @author: yexin
 * @date: 2022-04-28 16:38
 **/
@Data
public class DelayMessageRule implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 延时信息。key：延时单元，单位为秒。value：延时单元需要执行的次数
     */
    private Map<Integer, Integer> delayInfo;

}
