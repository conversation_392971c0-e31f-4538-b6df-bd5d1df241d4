package com.cainiao.waybill.bridge.client.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 单据中心响应内容
 * <AUTHOR>
 */
@Data
public class LocOrderResponse implements Serializable {

    /**
     * 淘宝订单id
     */
    private Long tradeId;

    /**
     * 交易系统
     */
    private String tradeSystem;

    /**
     * 淘宝卖家id
     */
    private Long sellerId;

    /**
     * 淘宝卖家昵称
     */
    private String sellerNick;

    /**
     * 商铺名称
     */
    private String shopName;

    /**
     * 买家手填真实号码
     */
    private String receiverMobilePhone;

    /**
     * 买家手填详细地址
     */
    private String receiverDetailAddress;

    /**
     * 勾选隐私保护
     */
    private String privacy;
}
