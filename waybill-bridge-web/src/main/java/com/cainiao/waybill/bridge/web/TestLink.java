package com.cainiao.waybill.bridge.web;

import com.cainiao.link.constant.Format;
import com.cainiao.link.consumer.LinkClient;
import com.cainiao.link.option.HttpOption;
import com.cainiao.link.option.RequestOption;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_CREATE_PRE_QUERY.AddressInfo;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_CREATE_PRE_QUERY.UserInfo;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_CREATE_PRE_QUERY.WaybillPickUpCreatePreQueryRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PICK_UP_CREATE_PRE_QUERY.WaybillPickUpCreatePreQueryResponse;

/**
 * <AUTHOR>
 * @date 2024/5/17 下午5:45
 **/
public class TestLink {


    public static void main(String[] args) {


        LinkClient linkClient = LinkClient.builder("753077", "APP_SECRET_XC").build();


        WaybillPickUpCreatePreQueryRequest request = new WaybillPickUpCreatePreQueryRequest();
        UserInfo consigneeInfo = new UserInfo();
        consigneeInfo.setName("硅基回收仓");
        consigneeInfo.setPhone("0755-27618014");
        AddressInfo consigneeAddress = new AddressInfo();
        consigneeAddress.setAddressDetail("观澜街道观光路1301号银星科技园7号厂房（一楼103）");
        consigneeAddress.setProvinceName("广东省");
        consigneeAddress.setCityName("深圳市");
        consigneeAddress.setAreaName("龙华区");
        consigneeInfo.setAddressInfo(consigneeAddress);
        request.setConsigneeInfo(consigneeInfo);

        UserInfo senderInfo = new UserInfo();
        senderInfo.setName("蔡国伟");
        senderInfo.setMobile("13410280362");
        AddressInfo senderAddress = new AddressInfo();
        senderAddress.setAddressDetail("兴华一路北16巷7号");
        senderAddress.setProvinceName("广东省");
        senderAddress.setCityName("深圳市");
        senderAddress.setAreaName("宝安区");
        senderInfo.setAddressInfo(senderAddress);
        request.setSenderInfo(senderInfo);
        request.setOrderChannels("GJ");
        request.setOuterOrderCode("zz_test202405170001");


        RequestOption requestOption = new RequestOption();
        requestOption.setFormat(Format.JSON);
        requestOption.setFromCode("68d4758bbb0bc805f038d6ccaae59beb");
        requestOption.setToCode("waybill-bridge");

        HttpOption httpOption = new HttpOption();
        httpOption.setConnectTimeout(3000);
        httpOption.setReadTimeout(3000);
        httpOption.setUri("https://prelink.cainiao.com/gateway/link.do");
        //linkClient.setEnv(Env.PRE);
        linkClient.setHttpOption(httpOption);
        WaybillPickUpCreatePreQueryResponse execute = linkClient.execute(request,requestOption);
    }
}
