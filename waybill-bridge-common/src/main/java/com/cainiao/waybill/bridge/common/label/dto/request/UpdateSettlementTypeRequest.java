package com.cainiao.waybill.bridge.common.label.dto.request;

import com.cainiao.waybill.bridge.common.dto.BaseDTO;
import com.cainiao.waybill.bridge.common.label.dto.AddressDTO;

/**
 * Description:网点修改客户的结算类型 request
 *
 * <AUTHOR>
 * @Date 2017-06-07
 */
public class UpdateSettlementTypeRequest extends BaseDTO {
    private static final long serialVersionUID = -3583741160574615696L;

    /**
     * 主键id，必填
     */
    private Long id;

    /**
     * cp 编码，校验使用，必填
     */
    private String cpCode;

    /**
     * 网点编码，校验使用，必填
     */
    private String branchCode;

    /**
     * 商家id，校验使用，必填
     */
    private Long sellerId ;

    /**
     * 新的结算类型，必填
     */
    private String settlementType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getSettlementType() {
        return settlementType;
    }

    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }
}
