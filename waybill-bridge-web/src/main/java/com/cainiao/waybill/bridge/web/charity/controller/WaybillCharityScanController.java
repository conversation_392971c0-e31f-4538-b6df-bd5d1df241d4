package com.cainiao.waybill.bridge.web.charity.controller;

import com.cainiao.waybill.bridge.biz.charity.request.CharityScanQueryRequest;
import com.cainiao.waybill.bridge.biz.charity.response.CharityExcelResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityPagingResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityScanQueryResponse;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityScanService;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.web.charity.CharityBaseResult;
import com.cainiao.waybill.bridge.web.charity.CharityExcelUtil;
import com.cainiao.waybill.bridge.web.charity.CharityLoginUserUtil;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 壹基金 - 新增包裹扫描统计页面
 *
 * <AUTHOR>
 */
@Api(tags = "包裹扫描统计")
@RestController
@RequestMapping(value = "/charity/Scan/")
public class WaybillCharityScanController {

    @Resource
    private WaybillCharityScanService waybillCharityScanService;

    /**
     * 包裹扫描数量统计页面
     *
     * @param charityScanQueryRequest
     * @return
     */
    @ResponseBody
    @RequestMapping("/list")
    public CharityBaseResult<CharityPagingResponse<CharityScanQueryResponse>> list(@RequestBody CharityScanQueryRequest charityScanQueryRequest) {
        BridgeValidator.validate(charityScanQueryRequest);
        return CharityBaseResult.success(waybillCharityScanService.queryScanCountList(charityScanQueryRequest));
    }

    /**
     * 包裹扫描数量统计页面导出
     *
     * @param charityScanQueryRequest
     * @return
     */
    @ResponseBody
    @RequestMapping("/list/export")
    public CharityBaseResult<CharityExcelResponse> exportList(@RequestBody CharityScanQueryRequest charityScanQueryRequest) {
        CharityLoginUserUtil.popLoginInfo(charityScanQueryRequest);
        String url = CharityExcelUtil.exportFile(charityScanQueryRequest, "包裹扫描数量统计列表", CharityScanQueryResponse.class,
                request1 -> waybillCharityScanService.queryScanCountList(request1));
        return CharityBaseResult.success(new CharityExcelResponse(url));
    }

}
