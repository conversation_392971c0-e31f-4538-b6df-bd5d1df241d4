package com.cainiao.waybill.bridge.biz.utils.wt;

import com.cainiao.waybill.trade.api.common.entity.ClientInfoDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * 淘外结算平台客户端
 * <AUTHOR>
 * @date 2024/8/27 20:12
 **/
public class WtClientSingleton {

    private static final String APP_NAME = "waybill-bridge";

    private static volatile ClientInfoDTO clientInstance;

    private WtClientSingleton() {

    }

    /**
     * 构造客户端
     * @param operator 操作人
     * @return
     */
    public static ClientInfoDTO getInstance(String operator) {
        if (clientInstance == null) {
            synchronized (ClientInfoDTO.class) {
                if (clientInstance == null) {
                    clientInstance = createClient(operator);
                }
            }
        }
        return clientInstance;
    }

    /**
     * 创建客户端
     * @param operator
     * @return
     */
    private static ClientInfoDTO createClient(String operator) {
        if(StringUtils.isBlank(operator)){
            operator = "system";
        }
        ClientInfoDTO clientInfoDTO = new ClientInfoDTO();
        clientInfoDTO.setAppName(APP_NAME);
        clientInfoDTO.setDescription("淘外寄件业务");
        clientInfoDTO.setOperator(operator);
        return clientInfoDTO;
    }
}
