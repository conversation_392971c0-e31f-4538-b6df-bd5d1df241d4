package com.cainiao.waybill.bridge.common.waybill.pickup.dto;

import com.cainiao.waybill.common.admin.dto.AddressDTO;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/6/16
 */
@Data
@Builder
public class LogisticSubscribeDTO {

    /**
     * 物流公司编号
     */
    private String cpCode;

    /**
     * 物流单号
     */
    private String mailNo;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人联系方式
     */
    private String receiverPhone;

    /**
     * 收件人地址
     */
    private AddressDTO receiverAddress;

    /**
     * 发件人联系方式
     */
    private String senderPhone;

    /**
     * 发件人姓名
     */
    private String senderName;

    /**
     * 发件人地址
     */
    private AddressDTO senderAddress;

}
