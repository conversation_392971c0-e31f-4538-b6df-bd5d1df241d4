package com.cainiao.waybill.bridge.web.pickup.response;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpTicketResponse
 * @Description
 * @Date 2022/11/29 10:47 上午
 * @Version 1.0
 */
@Data
public class PickUpTicketTraceResponse implements Serializable {

    private static final long serialVersionUID = -4646222379725643502L;

    /**
     * 回复对应id
     */
    private String replyId;
    /**
     * 工单id
     */
    private String ticketId;

    /**
     * 发生时间
     */
    private String occurTime;

    /**
     * 回复内容
     */
    private String content;

    /**
     * 文件列表
     */
    private List<String> fileList;

    /**
     * 类型
     */
    private String ticketTraceType;
}
