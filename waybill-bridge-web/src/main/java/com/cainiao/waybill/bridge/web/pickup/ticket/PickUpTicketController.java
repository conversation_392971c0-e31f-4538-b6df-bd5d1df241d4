package com.cainiao.waybill.bridge.web.pickup.ticket;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.charity.constant.CharityConstant;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpCpEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpDetailStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpOrderSource;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketConstant.CpTicketStatus;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketConstant.TicketOutSource;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketConstant.TicketStatus;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketPrimarySource;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketTypeConvert;
import com.cainiao.waybill.bridge.biz.pickup.service.impl.WaybillPickUpTicketEventMetaQSender;
import com.cainiao.waybill.bridge.biz.ticket.constants.PickUpTicketDealPartEnum;
import com.cainiao.waybill.bridge.biz.ticket.convert.PickUpOrderOperateTypeEnum;
import com.cainiao.waybill.bridge.biz.ticket.dto.*;
import com.cainiao.waybill.bridge.biz.ticket.service.PickUpTicketService;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.biz.utils.OrderSourceMapUtil;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelExportUtil;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelImportUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.web.charity.controller.BaseController;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.common.util.ExeclUtil;
import com.cainiao.waybill.bridge.web.pickup.response.PickUpCpResponse;
import com.cainiao.waybill.bridge.web.pickup.ticket.util.PickUpExcelUtil;
import com.cainiao.waybill.bridge.web.pickup.ticket.validate.TicketValidate;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpTicketController
 * @Description
 * @Date 2022/11/29 10:46 上午
 * @Version 1.0
 */
@RestController
@Slf4j(topic = "WAYBILL_PICKUP_INFO")
public class PickUpTicketController extends BaseController {

    @Autowired
    private PickUpTicketService pickUpTicketService;

    @RequestMapping("/pickup/ticket/list")
    public BaseResult<PagingResponse<PickUpTicketQueryDetailDTO>> queryTicket(
        @RequestBody PickUpTicketQueryRequest request) throws Exception {
        TicketValidate.validateQueryParam(request);
        checkMenuRoleValid(request.getQuerySource(), request.getRole());
        PagingResponse<PickUpTicketQueryDetailDTO> response = pickUpTicketService.queryList(request);

        return BaseResult.success(response);
    }

    @RequestMapping("/pickup/ticket/detail")
    public BaseResult<PickUpTicketQueryDetailDTO> queryTicketDetail(
        @RequestBody PickUpTicketQueryDetailRequest request) throws Exception {

        checkMenuRoleValid(request.getQuerySource(), request.getRole());
        PickUpTicketQueryDetailDTO response = pickUpTicketService.queryDetail(request);

        return BaseResult.success(response);
    }

    @RequestMapping("/pickup/ticket/status/count")
    public BaseResult<PickUpTicketStatusCountDTO> statusCount(@RequestBody PickUpTicketQueryRequest request)
        throws Exception {
        checkMenuRoleValid(request.getQuerySource(), request.getRole());
        PickUpTicketStatusCountDTO statusCountDTO = pickUpTicketService.statusCount(request);

        return BaseResult.success(statusCountDTO);
    }

    @RequestMapping("/pickup/ticket/create")
    public BaseResult<Void> create(@RequestBody PickUpTicketCreateRequest request)
        throws Exception {
        BridgeValidator.assertNull(request.getTicketType(), "工单类型不能为空");
        checkMenuRoleValid(request.getQuerySource(), request.getRole());
        BridgeValidator.assertBothNull(request.getMailNo(), request.getOutOrderId(), "运单号和订单号不能同时为空");
        pickUpTicketService.createTicket(request);

        return BaseResult.success();
    }

    @RequestMapping("/pickup/ticket/createToCp")
    public BaseResult<Void> createToCp(@RequestBody PickUpTicketCreateToCpRequest request)
        throws Exception {
        for (Long ticketId : request.getTicketIdList()) {

            pickUpTicketService.createTicketToCp(ticketId, request);
        }

        return BaseResult.success();
    }

    @RequestMapping("/pickup/ticket/reply")
    public BaseResult<Void> reply(@RequestBody PickUpTicketReplyRequest request) throws Exception {
        for (Long ticketId : ListUtil.non(request.getTicketIdList())) {
            pickUpTicketService.replyTicket(ticketId, request);
        }
        return BaseResult.success();
    }

    @RequestMapping("/pickup/ticket/complete")
    public BaseResult<Void> complete(@RequestBody PickUpTicketCompleteRequest request)
        throws Exception {
        for (Long ticketId : ListUtil.non(request.getTicketIdList())) {
            pickUpTicketService.completeTicket(ticketId, request);
        }
        return BaseResult.success();
    }

    @RequestMapping("/pickup/ticket/import")
    public BaseResult<String> importExcel(@RequestParam("file") MultipartFile file, PickUpBaseRequest pickUpBaseRequest) {
        List<PickUpTicketImportRequest> list = ExcelImportUtil.loadData(file, PickUpTicketImportRequest.class,
            1000);
        checkMenuRoleValid(pickUpBaseRequest.getQuerySource(), pickUpBaseRequest.getRole());
        List<PickUpTicketImportRequest> errorList = Lists.newArrayList();
        for (PickUpTicketImportRequest pickUpTicketImportRequest : list) {
            try {
                PickUpTicketCreateRequest request = convert(pickUpTicketImportRequest, pickUpBaseRequest);
                BridgeValidator.assertNull(request.getTicketType(), "工单类型不能为空");
                BridgeValidator.assertBothNull(request.getMailNo(), request.getOutOrderId(), "运单号和订单号不能同时为空");
                pickUpTicketService.createTicket(request);
            } catch (BridgeBaseException bridgeBaseException) {
                pickUpTicketImportRequest.setErrorInfo(bridgeBaseException.getErrorMessage());
                errorList.add(pickUpTicketImportRequest);
            }catch (BridgeBusinessException bridgeBaseException) {
                pickUpTicketImportRequest.setErrorInfo(bridgeBaseException.getErrorMessage());
                errorList.add(pickUpTicketImportRequest);
            } catch (Exception exception) {
                pickUpTicketImportRequest.setErrorInfo("系统异常");
                errorList.add(pickUpTicketImportRequest);
            }
        }
        if(CollectionUtils.isEmpty(errorList)){
            return BaseResult.success();
        }
        Workbook workbook = ExcelExportUtil.build(errorList, "工单导入失败列表", PickUpTicketImportRequest.class);
        String url = ExeclUtil.exportToOSS(workbook, CharityConstant.OSS_TEMP_FILE_DIR);
        return BaseResult.success(url);
    }

    @RequestMapping("/pickup/ticket/export")
    public BaseResult<String> export(@RequestBody PickUpTicketQueryRequest request) throws BridgeValidationException {
        request.setShowOrderOperate(true);
        TicketValidate.validateQueryParam(request);
        checkMenuRoleValid(request.getQuerySource(), request.getRole());
        String url;
        if(PickUpTicketPrimarySource.platform.name().equals(request.getPrimarySource())) {
            url = PickUpExcelUtil.exportFile(request, "工单列表", PickUpTicketPlatformExportDTO.class,
                request12 -> {
                    PagingResponse<PickUpTicketQueryDetailDTO> response = pickUpTicketService.queryList(request12);
                    List<PickUpTicketPlatformExportDTO> list = ListUtil.stream(response.getTableData()).map(
                        this::convertToPlatform).collect(Collectors.toList());
                    return PagingResponse.build(list, response.getPaging().getTotalCount(),
                        response.getPaging().getCurrentPage(), response.getPaging().getPageSize());
                });
        }else if(PickUpTicketDealPartEnum.CP.name().equals(request.getDealPart())){
            request.setShowTicketTrace(true);
            url  = PickUpExcelUtil.exportFile(request, "工单列表", PickUpTicketCpExportDTO.class,
                request12 -> {
                    PagingResponse<PickUpTicketQueryDetailDTO> response = pickUpTicketService.queryList(request12);
                    List<PickUpTicketCpExportDTO> list = ListUtil.stream(response.getTableData()).map(this::convertToCp).collect(Collectors.toList());
                    return PagingResponse.build(list, response.getPaging().getTotalCount(),
                        response.getPaging().getCurrentPage(), response.getPaging().getPageSize());
                });
        }else{
            request.setShowTicketTrace(true);
            url  = PickUpExcelUtil.exportFile(request, "工单列表", PickUpTicketExportDTO.class,
                request12 -> {
                    PagingResponse<PickUpTicketQueryDetailDTO> response = pickUpTicketService.queryList(request12);
                    List<PickUpTicketExportDTO> list = ListUtil.stream(response.getTableData()).map(this::convert).collect(Collectors.toList());
                    return PagingResponse.build(list, response.getPaging().getTotalCount(),
                        response.getPaging().getCurrentPage(), response.getPaging().getPageSize());
                });
        }


        return BaseResult.success(url);
    }

    /**
     * 查询工单来源映射
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping("/pickup/ticket/sourceMap")
    public BaseResult<Map<String,String>> queryTicketSourceMap(
        @RequestBody PickUpTicketQueryDetailRequest request) throws Exception {

        Map<String,String> map = OrderSourceMapUtil.getCustomerOrderChannelsMap();

        return BaseResult.success(map);
    }

    /**
     * 理赔操作导出
     */
    @RequestMapping("/pickup/ticket/operate/export")
    public BaseResult<String> operateExport(@RequestBody PickUpTicketQueryRequest request) throws BridgeValidationException {
        request.setShowOrderOperate(true);
        TicketValidate.validateQueryParam(request);
        checkMenuRoleValid(request.getQuerySource(), request.getRole());
        String url = PickUpExcelUtil.exportFile(request, "工单操作列表", PickUpTicketOperateDetailDTO.class,
                request12 -> {
                    PagingResponse<PickUpTicketQueryDetailDTO> response = pickUpTicketService.queryList(request12);
                    List<PickUpTicketOperateDetailDTO> list = ListUtil.non(response.getTableData()).stream()
                            .map(this::operateConvert).flatMap(List::stream).collect(Collectors.toList());
                    return PagingResponse.build(list, response.getPaging().getTotalCount(),
                            response.getPaging().getCurrentPage(), response.getPaging().getPageSize());
                });

        return BaseResult.success(url);
    }


    private List<PickUpTicketOperateDetailDTO> operateConvert(PickUpTicketQueryDetailDTO pickUpTicketQueryDetailDTO) {
        return ListUtil.non(pickUpTicketQueryDetailDTO.getOrderOperateResponseList()).stream()
                .filter(Objects::nonNull)
                .map(operateResponse -> {
                    PickUpTicketOperateDetailDTO operateDetailDTO = new PickUpTicketOperateDetailDTO();
                    operateDetailDTO.setMailNo(pickUpTicketQueryDetailDTO.getMailNo());
                    operateDetailDTO.setCpName(pickUpTicketQueryDetailDTO.getCpName());
                    operateDetailDTO.setOutOrderId(pickUpTicketQueryDetailDTO.getOutOrderId());
                    operateDetailDTO.setPlatform(pickUpTicketQueryDetailDTO.getOrderSourceName());
                    operateDetailDTO.setTicketCreateTime(pickUpTicketQueryDetailDTO.getTicketCreateTime());
                    operateDetailDTO.setTicketDoneTime(pickUpTicketQueryDetailDTO.getDoneTime());
                    operateDetailDTO.setOrderCreateTime(pickUpTicketQueryDetailDTO.getOrderCreateTime());
                    String operateType = operateResponse.getOperateType();
                    operateDetailDTO.setOperator(operateResponse.getOperator());
                    operateDetailDTO.setReason(operateResponse.getOperateSubTypeName());
                    operateDetailDTO.setRemark(operateResponse.getDesc());
                    operateDetailDTO.setOperateTime(operateResponse.getCreateDate());
                    operateDetailDTO.setSource(PickUpOrderOperateTypeEnum.getDescByType(operateType));
                    if (PickUpOrderOperateTypeEnum.UPDATE_WEIGHT.getCode().equals(operateType)) {
                        operateDetailDTO.setCorrectWeight(operateResponse.getValue());
                    } else if (PickUpOrderOperateTypeEnum.PUNISH.getCode().equals(operateType)
                            || PickUpOrderOperateTypeEnum.CLAIMS.getCode().equals(operateType)) {
                        operateDetailDTO.setAmount(operateResponse.getValue());
                    } else if (PickUpOrderOperateTypeEnum.CHANGE_MAIL_NO.getCode().equals(operateType)) {
                        operateDetailDTO.setExchangeOrderId(operateResponse.getValue());
                    }
                    return operateDetailDTO;
                }).collect(Collectors.toList());

    }
    private PickUpTicketExportDTO convert(PickUpTicketQueryDetailDTO pickUpTicketQueryDetailDTO){
        PickUpTicketExportDTO exportDTO = new PickUpTicketExportDTO();
        exportDTO.setMailNo(pickUpTicketQueryDetailDTO.getMailNo());
        exportDTO.setOutOrderId(pickUpTicketQueryDetailDTO.getOutOrderId());
        exportDTO.setCpOrderId(pickUpTicketQueryDetailDTO.getCpOrderId());
        exportDTO.setTicketType(PickUpTicketTypeConvert.getDescByType(pickUpTicketQueryDetailDTO.getTicketType()));
        exportDTO.setTicketStatus(TicketStatus.getDescByStatus(pickUpTicketQueryDetailDTO.getTicketStatus()));
        exportDTO.setCpTicketStatus(CpTicketStatus.getDescByStatus(pickUpTicketQueryDetailDTO.getCpTicketStatus()));
        exportDTO.setTicketSource(TicketOutSource.getDescBySource(pickUpTicketQueryDetailDTO.getTicketSource()));
        exportDTO.setOrderSource(PickUpOrderSource.getDescByOrderChannel(pickUpTicketQueryDetailDTO.getOrderSource()));
        Integer orderStatus = StringUtils.isNotBlank(pickUpTicketQueryDetailDTO.getOrderStatus())
            ? Integer.parseInt(pickUpTicketQueryDetailDTO.getOrderStatus()) : null;
        exportDTO.setOrderStatus(orderStatus != null ? PickUpDetailStatusEnum.getOrderStatusByValue(orderStatus) : null);
        exportDTO.setContent(pickUpTicketQueryDetailDTO.getContent());
        exportDTO.setTicketCreateTime(pickUpTicketQueryDetailDTO.getTicketCreateTime());
        exportDTO.setOrderCreateTime(pickUpTicketQueryDetailDTO.getOrderCreateTime());
        exportDTO.setAppointGotStartTime(pickUpTicketQueryDetailDTO.getAppointGotStartTime());
        exportDTO.setAppointGotEndTime(pickUpTicketQueryDetailDTO.getAppointGotEndTime());
        exportDTO.setSenderName(pickUpTicketQueryDetailDTO.getSenderName());
        exportDTO.setSenderMobile(pickUpTicketQueryDetailDTO.getSenderMobile());
        exportDTO.setSenderProvince(pickUpTicketQueryDetailDTO.getSenderProvince());
        exportDTO.setCpCode(pickUpTicketQueryDetailDTO.getCpName());
        exportDTO.setDoneTime(pickUpTicketQueryDetailDTO.getDoneTime());
        exportDTO.setLastReplyInfo(pickUpTicketQueryDetailDTO.getLastReplyInfo());
        exportDTO.setStructuredReasonDesc(pickUpTicketQueryDetailDTO.getStructuredReasonDesc());
        if(StringUtils.isNotBlank(pickUpTicketQueryDetailDTO.getOrderDropInTime())){
            exportDTO.setOrderDropInTime(DateUtils.parseByDbPattern(pickUpTicketQueryDetailDTO.getOrderDropInTime()));
        }
        if(StringUtils.isNotBlank(pickUpTicketQueryDetailDTO.getOrderGotTime())){
            exportDTO.setOrderGotTime(DateUtils.parseByDbPattern(pickUpTicketQueryDetailDTO.getOrderGotTime()));
        }
        if(StringUtils.isNotBlank(pickUpTicketQueryDetailDTO.getOrderSignTime())){
            exportDTO.setOrderSignTime(DateUtils.parseByDbPattern(pickUpTicketQueryDetailDTO.getOrderSignTime()));
        }

        PickUpOrderOperateResponse changeMailOperateResponse = getOrderOperate(PickUpOrderOperateTypeEnum.CHANGE_MAIL_NO,
            pickUpTicketQueryDetailDTO.getOrderOperateResponseList());
        if(changeMailOperateResponse != null){
            exportDTO.setNewMailNo(changeMailOperateResponse.getValue());
            exportDTO.setChangeMailCreator(changeMailOperateResponse.getOperator());
        }

        PickUpOrderOperateResponse cancelOperateResponse = getOrderOperate(PickUpOrderOperateTypeEnum.CANCEL_ORDER,
            pickUpTicketQueryDetailDTO.getOrderOperateResponseList());
        if(cancelOperateResponse != null){
            exportDTO.setCancelType(cancelOperateResponse.getOperateSubTypeName());
            exportDTO.setCancelTime(cancelOperateResponse.getCreateDate());
            exportDTO.setCancelDesc(cancelOperateResponse.getDesc());
        }

        PickUpOrderOperateResponse weightOperateResponse = getOrderOperate(PickUpOrderOperateTypeEnum.UPDATE_WEIGHT,
            pickUpTicketQueryDetailDTO.getOrderOperateResponseList());
        if(weightOperateResponse != null){
            exportDTO.setUpdateWeightTime(weightOperateResponse.getCreateDate());
            exportDTO.setUpdateWeightCreator(weightOperateResponse.getOperator());
        }

        return exportDTO;
    }

    private PickUpOrderOperateResponse getOrderOperate(PickUpOrderOperateTypeEnum typeEnum ,List<PickUpOrderOperateResponse> orderOperateResponseList){
        for (PickUpOrderOperateResponse response : ListUtil.non(orderOperateResponseList)) {
            if(typeEnum.getCode().equals(response.getOperateType())){
                return response;
            }
        }
        return null;
    }

    private PickUpTicketPlatformExportDTO convertToPlatform(PickUpTicketQueryDetailDTO pickUpTicketQueryDetailDTO){
        PickUpTicketPlatformExportDTO exportDTO = new PickUpTicketPlatformExportDTO();
        exportDTO.setMailNo(pickUpTicketQueryDetailDTO.getMailNo());
        exportDTO.setOutOrderId(pickUpTicketQueryDetailDTO.getOutOrderId());
        exportDTO.setTicketType(PickUpTicketTypeConvert.getDescByType(pickUpTicketQueryDetailDTO.getTicketType()));
        exportDTO.setTicketStatus(TicketStatus.getDescByStatus(pickUpTicketQueryDetailDTO.getTicketStatus()));
        exportDTO.setContent(pickUpTicketQueryDetailDTO.getContent());
        exportDTO.setTicketCreateTime(pickUpTicketQueryDetailDTO.getTicketCreateTime());
        exportDTO.setSenderProvince(pickUpTicketQueryDetailDTO.getSenderProvince());
        return exportDTO;
    }

    private PickUpTicketCpExportDTO convertToCp(PickUpTicketQueryDetailDTO detailDTO){
        PickUpTicketCpExportDTO exportDTO = new PickUpTicketCpExportDTO();
        exportDTO.setMailNo(detailDTO.getMailNo());

        Integer orderStatus = StringUtils.isNotBlank(detailDTO.getOrderStatus())
            ? Integer.parseInt(detailDTO.getOrderStatus()) : null;
        exportDTO.setOrderStatus(orderStatus != null ? PickUpDetailStatusEnum.getOrderStatusByValue(orderStatus) : null);
        exportDTO.setSenderProvince(detailDTO.getSenderProvince());
        exportDTO.setConsignProvince(detailDTO.getConsignProvince());
        exportDTO.setGotCode(detailDTO.getGotCode());
        exportDTO.setCpOrderId(detailDTO.getCpOrderId());
        exportDTO.setTicketCreateTime(detailDTO.getTicketCreateTime());
        exportDTO.setTicketModifyTime(detailDTO.getTicketModifyTime());
        exportDTO.setTicketType(PickUpTicketTypeConvert.getDescByType(detailDTO.getTicketType()));
        exportDTO.setTicketStatus(TicketStatus.getDescByStatus(detailDTO.getTicketStatus()));
        exportDTO.setContent(detailDTO.getContent());
        exportDTO.setOrderCreateTime(detailDTO.getOrderCreateTime());
        exportDTO.setAppointGotStartTime(detailDTO.getAppointGotStartTime());
        exportDTO.setAppointGotEndTime(detailDTO.getAppointGotEndTime());
        exportDTO.setLastReplyCreator(detailDTO.getLastReplyCreator());
        return exportDTO;
    }

    @RequestMapping("/pickup/ticket/cpList")
    public BaseResult<List<PickUpCpResponse>> cpList() {

        List<PickUpCpResponse> list = new ArrayList<>();
        for (PickUpCpEnum value : PickUpCpEnum.values()) {
            PickUpCpResponse response = new PickUpCpResponse();
            response.setCpCode(value.getCpCode());
            response.setCpName(value.getCpName());
            list.add(response);
        }

        return BaseResult.success(list);
    }

    private PickUpTicketCreateRequest convert(PickUpTicketImportRequest request, PickUpBaseRequest pickUpBaseRequest) {
        PickUpTicketCreateRequest createRequest = new PickUpTicketCreateRequest();
        BeanUtils.copyProperties(pickUpBaseRequest, createRequest);
        createRequest.setContent(request.getContent());
        createRequest.setMailNo(request.getMailNo());
        createRequest.setOutOrderId(request.getOutOrderId());
        createRequest.setTicketSource(PickUpOrderSource.getOrderChannelByDesc(request.getPlatform()));
        createRequest.setTicketType(PickUpTicketTypeConvert.getTypeByDesc(request.getTicketType()));
        return createRequest;
    }
}
