package com.cainiao.waybill.bridge.biz.label.manager.impl;

import java.util.Calendar;
import java.util.Date;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
import com.cainiao.waybill.bridge.biz.label.manager.SellerAgreementManager;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.util.Exceptions;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.model.dao.SellerAgreementDAO;
import com.cainiao.waybill.bridge.model.domain.SellerAgreementDO;
import com.taobao.common.dao.persistence.exception.DAOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2017/06/12
 */
@Service
public class SellerAgreementManagerImpl implements SellerAgreementManager {

    private final static Logger LOGGER = LoggerFactory.getLogger(LogAppender.SELLER_AGREEMENT);

    @Resource
    private LogManager logManager;

    @Resource
    private SellerAgreementDAO sellerAgreementDAO;

    @Override
    public Boolean isSellerAgreementValid(Long sellerId) throws BridgeBaseException {
        try {
            SellerAgreementDO sellerAgreementDO = sellerAgreementDAO.queryBySellerId(sellerId);
            if (sellerAgreementDO == null) {
                return false;
            }

            Date begin = DateUtils.dateToDateStart(sellerAgreementDO.getBeginDate());
            Date end = DateUtils.dateToDateEnd(sellerAgreementDO.getEndDate());

            long now = System.currentTimeMillis();

            return now >= begin.getTime() && now <= end.getTime();
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(BridgeErrorConstant.SystemError.DAO_EXCEPTION, e);
        }
    }

    @Override
    public void signSellerAgreement(Long sellerId) throws BridgeBaseException {

        boolean isAgreementValid = isSellerAgreementValid(sellerId);

        // 如果已经在有效期内，不签署新协议。（同时保持幂等）
        if (isAgreementValid) {
            logManager.logWarnMessage("SellerAgreementManagerImpl.signSellerAgreement", "商家协议在有效期内", LOGGER);
            return;
        }

        // 构造 DO. 数据库只存日期，日期正确即可
        Date beginDate = new Date();

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, 1);
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        Date endDate = calendar.getTime();

        SellerAgreementDO sellerAgreementDO = new SellerAgreementDO();
        sellerAgreementDO.setSellerId(sellerId);
        sellerAgreementDO.setBeginDate(beginDate);
        sellerAgreementDO.setEndDate(endDate);
        try {
            sellerAgreementDAO.insert(sellerAgreementDO);
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(BridgeErrorConstant.SystemError.DAO_EXCEPTION, e);
        }
    }
}
