package com.cainiao.waybill.bridge.biz.fast.request;

import com.cainiao.waybill.bridge.biz.charity.request.CharityBaseRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpBaseRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/26 12:14
 */
@Data
public class FastShipmentRefundRequest extends PickUpBaseRequest {
    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 寄件人姓名
     */
    private String sendName;

    /**
     * 寄件人手机号
     */
    private String sendPhone;
    /**
     * 退款金额
     */
    private Double refundAmount;
    /**
     * 退款原因
     */
    private String refundReason;
    /**
     * 附件
     */
    private List<String> attachment;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 登陆用户名
     */
    private String loginName;
}
