package com.cainiao.waybill.bridge.web.pickup;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.util.ReflectUtil;
import com.cainiao.commons.data.domain.tuple.ImmutablePair;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpAgentEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.CommonConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpCpEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketPrimarySource;
import com.cainiao.waybill.bridge.biz.pickup.constants.exception.PickUpBusinessException;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpOrderSourceDTO;
import com.cainiao.waybill.bridge.biz.ticket.constants.PickUpTicketDealPartEnum;
import com.cainiao.waybill.bridge.biz.utils.OrderSourceMapUtil;
import com.cainiao.waybill.bridge.biz.utils.excel.MarkdownExportUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.util.BridgeDateUtil;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpTicketParam;
import com.cainiao.waybill.bridge.model.dto.WaybillPickUpOrderDetailQueryDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillPickUpReportDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillPickUpTicketCountDTO;
import com.cainiao.waybill.bridge.model.dto.statics.WaybillOnlineChannelOrderStaticsDTO;
import com.cainiao.waybill.bridge.model.dto.statics.WaybillOnlineChannelOrderStaticsRobotDTO;
import com.cainiao.waybill.bridge.model.dto.statics.WaybillOnlineCpOrderStaticsDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpDetailMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpOnlineStatisticsMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpTicketMapper;
import com.cainiao.waybill.bridge.web.charity.controller.BaseController;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.pickup.request.PickUpOnlineReportRequest;
import com.cainiao.waybill.bridge.web.pickup.response.WaybillPickUpTicketReportResponse;
import com.cainiao.waybill.bridge.web.pickup.vo.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.testng.collections.Maps;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpOnlineReportController
 * @Description
 * @Date 2022/11/2 5:43 下午
 * @Version 1.0
 */
@RestController
@RequestMapping("/report")
public class PickUpOnlineReportController extends BaseController {

    @Resource
    private WaybillPickUpTicketMapper waybillPickUpTicketMapper;

    @Resource
    private WaybillPickUpDetailMapper waybillPickUpDetailMapper;

    @Resource
    private WaybillPickUpOnlineStatisticsMapper waybillPickUpOnlineStatisticsMapper;

    /**
     * 计算取货上报速率
     *
     * <p>根据请求参数计算取货上报速率。
     *
     * @param request 取货上报请求对象
     * @return JSON对象，包含速率相关信息。如果请求的访问令牌与系统预设的不一致，则返回无权限错误信息；否则返回速率的统计结果。
     */
    @GetMapping("/ticket/rate")
    public JSONObject ticketRate(PickUpOnlineReportRequest request) {

        if (!StringUtils.equals(request.getAccessToken(), BridgeSwitch.reportInterfaceAccessToken)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorCode", 401);
            jsonObject.put("errorMsg", "无权限");
            jsonObject.put("success", false);
            return jsonObject;
        }

        Date startDate = BridgeDateUtil.dateToStartTime(new Date());
        if (StringUtils.isNotBlank(request.getBizDate()) && !"today".equals(request.getBizDate())) {
            startDate = BridgeDateUtil.strToDate(request.getBizDate(), BridgeDateUtil.patternDayFormat);
        }

        Date endDate = new Date();
        if (StringUtils.isNotBlank(request.getBizDate()) && !"today".equals(request.getBizDate())) {
            endDate = BridgeDateUtil.dateToEndTime(BridgeDateUtil.strToDate(request.getBizDate(),
                BridgeDateUtil.patternDayFormat));
        }

        WaybillPickUpTicketParam ticketParam = new WaybillPickUpTicketParam();
        ticketParam.setStartTime(startDate);
        ticketParam.setEndTime(endDate);
        if (!"all".equals(request.getSource())) {
            ticketParam.setSource(request.getSource());
        }
        if (!"all".equals(request.getCpCode())) {
            ticketParam.setCpCode(request.getCpCode());
        }
        ticketParam.setPrimarySource(PickUpTicketPrimarySource.platform.name());
        ticketParam.setDealPart(PickUpTicketDealPartEnum.CN.name());
        WaybillPickUpTicketCountDTO ticketCountDTO = waybillPickUpTicketMapper.queryCount(ticketParam);

        WaybillPickUpOrderDetailQueryDTO detailQueryDTO = new WaybillPickUpOrderDetailQueryDTO();
        detailQueryDTO.setCreateStart(startDate);
        detailQueryDTO.setCreateEnd(endDate);
        if (!"all".equals(request.getSource())) {
            detailQueryDTO.setOrderChannels(request.getSource());
        }
        if (!"all".equals(request.getCpCode())) {
            detailQueryDTO.setCpCode(request.getCpCode());
        }
        Integer orderNum = waybillPickUpDetailMapper.count(detailQueryDTO);

        WaybillPickUpTicketReportResponse response = new WaybillPickUpTicketReportResponse();
        response.setTime(request.getBizDate());
        response.setOrderNum(String.valueOf(orderNum));
        response.setDistinctTicketNum(String.valueOf(ticketCountDTO.getDistinctNum()));
        response.setDistinctTicketRate(BigDecimal.valueOf(ticketCountDTO.getDistinctNum())
            .divide(BigDecimal.valueOf(orderNum), 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100)) + "%");
        response.setTicketNum(String.valueOf(ticketCountDTO.getNum()));
        response.setTicketRate(BigDecimal.valueOf(ticketCountDTO.getNum())
            .divide(BigDecimal.valueOf(orderNum), 4, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(100)) + "%");

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("errorCode", 200);
        jsonObject.put("errorMsg", "");
        jsonObject.put("success", true);
        jsonObject.put("fields", response);
        return jsonObject;
    }

    /**
     * CP重点指标数据
     * @param request
     * @return
     */
    @GetMapping("/cp/key/index")
    public JSONObject cpKeyIndexData(PickUpOnlineReportRequest request) {

        if (!StringUtils.equals(request.getAccessToken(), BridgeSwitch.reportInterfaceAccessToken)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorCode", 401);
            jsonObject.put("errorMsg", "无权限");
            jsonObject.put("success", false);
            return jsonObject;
        }

        Date startDate = BridgeDateUtil.dateToStartTime(new Date());
        Date endDate = new Date();

        WaybillPickUpTicketParam ticketParam = new WaybillPickUpTicketParam();
        ticketParam.setStartTime(startDate);
        ticketParam.setEndTime(endDate);
        ticketParam.setPrimarySource(PickUpTicketPrimarySource.platform.name());
        ticketParam.setDealPart(PickUpTicketDealPartEnum.CN.name());
        List<WaybillPickUpTicketCountDTO> ticketReportList = waybillPickUpTicketMapper.queryCountGroupCp(ticketParam);

        WaybillPickUpOrderDetailQueryDTO detailQueryDTO = new WaybillPickUpOrderDetailQueryDTO();
        detailQueryDTO.setCreateStart(startDate);
        detailQueryDTO.setCreateEnd(endDate);
        detailQueryDTO.setOrderChannelsList(BridgeSwitch.CUSTOMER_REPORT_ORDER_CHANNELS);
        List<WaybillPickUpReportDTO> reportList = waybillPickUpDetailMapper.queryReportCp(detailQueryDTO);

        // 合并订单工单数据
        List<PickUpKeyCpRobotVO> robotList = concatCpTicketList(reportList, ticketReportList);

        String content = MarkdownExportUtil.build(robotList, PickUpKeyCpRobotVO.class);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("errorCode", 200);
        jsonObject.put("errorMsg", "");
        jsonObject.put("success", true);
        jsonObject.put("fields", ImmutablePair.of("content", content));

        return jsonObject;
    }

    /**
     * CP当日揽收数据报告
     * @param request
     * @return
     */
    @GetMapping("/cp/collect/data")
    public JSONObject cpCollectData(PickUpOnlineReportRequest request) {

        if (!StringUtils.equals(request.getAccessToken(), BridgeSwitch.reportInterfaceAccessToken)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorCode", 401);
            jsonObject.put("errorMsg", "无权限");
            jsonObject.put("success", false);
            return jsonObject;
        }

        Date startDate = BridgeDateUtil.dateToStartTime(new Date());
        Date endDate =  BridgeDateUtil.dateToEndTime(new Date());

        WaybillPickUpOrderDetailQueryDTO detailQueryDTO = new WaybillPickUpOrderDetailQueryDTO();
        detailQueryDTO.setCreateStart(startDate);
        detailQueryDTO.setCreateEnd(endDate);
        // 查询今天下单的统计数据
        List<WaybillPickUpReportDTO> reportCurrList = waybillPickUpDetailMapper.queryCurrCollectCp(detailQueryDTO);
        // 查询3天前0时至昨天24时的单子
        Date preStartDate = BridgeDateUtil.dateToStartTime(BridgeDateUtil.getDateBeforeNDays(3));
        Date preEndDate =  BridgeDateUtil.dateToEndTime(BridgeDateUtil.getDateBeforeNDays(1));
        detailQueryDTO.setCreateStart(preStartDate);
        detailQueryDTO.setCreateEnd(preEndDate);

        detailQueryDTO.setCurrDateStart(startDate);
        detailQueryDTO.setCurrDateEnd(endDate);
        // 查询今日之前预约下单的统计数据
        List<WaybillPickUpReportDTO> reportPreList = waybillPickUpDetailMapper.queryPreCollectCp(detailQueryDTO);

        // 合并实时单和预约单揽收统计数据
        List<WaybillPickUpReportDTO> reportList = concatReportList(reportCurrList, reportPreList);
        // 构造CP揽收数据
        List<PickUpCpCollectRobotVO> robotList = buildCollectCpDataVo(reportList);

        String content = MarkdownExportUtil.build(robotList, PickUpCpCollectRobotVO.class);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("errorCode", 200);
        jsonObject.put("errorMsg", "");
        jsonObject.put("success", true);
        jsonObject.put("fields", ImmutablePair.of("content", content));

        return jsonObject;
    }

    /**
     * 合并实时单和预约单揽收统计数据
     * @param reportCurrList
     * @param reportPreList
     * @return
     */
    private List<WaybillPickUpReportDTO> concatReportList(List<WaybillPickUpReportDTO> reportCurrList, List<WaybillPickUpReportDTO> reportPreList) {
        if (reportCurrList == null || reportPreList == null) {
            return new ArrayList<>();
        }
        Map<String, Integer> orderNumMap = new HashMap<>(32);
        Map<String, Integer> gotedNumMap = new HashMap<>(32);
        Map<String, Integer> shouldGotNumMap = new HashMap<>(32);
        Map<String, Integer> shouldGotInTimeNumMap = new HashMap<>(32);

        for (WaybillPickUpReportDTO pickUpReportDTO : reportCurrList) {
            String cpCode = pickUpReportDTO.getCpCode();
            String agent = pickUpReportDTO.getAgent();
            String key = cpCode + agent;
            Integer orderNum = pickUpReportDTO.getOrderNum();
            Integer gotedNum = pickUpReportDTO.getGotedNum();
            Integer shouldGotNum = pickUpReportDTO.getShouldGotNum();
            Integer shouldGotInTimeGotNum = pickUpReportDTO.getShouldGotInTimeGotNum();
            orderNumMap.put(key, (null == orderNum) ? 0 : orderNum);
            gotedNumMap.put(key, (null == gotedNum) ? 0 : gotedNum);
            shouldGotNumMap.put(key, (null == shouldGotNum) ? 0 : shouldGotNum);
            shouldGotInTimeNumMap.put(key, (null == shouldGotInTimeGotNum) ? 0 : shouldGotInTimeGotNum);
        }
        List<WaybillPickUpReportDTO> reportAllList = new ArrayList<>(32);
        for(WaybillPickUpReportDTO prePickUpReportDTO : reportPreList){
            String cpCode = prePickUpReportDTO.getCpCode();
            if(StringUtils.equals(cpCode, CommonConstants.TEMP_CP_CODE)){
                continue;
            }
            String agent = prePickUpReportDTO.getAgent();
            String preKey = cpCode + agent;
            Integer preOrderNum = prePickUpReportDTO.getOrderNum();
            Integer preGotedNum = prePickUpReportDTO.getGotedNum();
            Integer preShouldGotNum = prePickUpReportDTO.getShouldGotNum();
            Integer preShouldGotInTimeGotNum = prePickUpReportDTO.getShouldGotInTimeGotNum();
            if(orderNumMap.containsKey(preKey)){
                WaybillPickUpReportDTO pickUpReportDTO = new WaybillPickUpReportDTO();
                BeanUtils.copyProperties(prePickUpReportDTO, pickUpReportDTO);
                pickUpReportDTO.setOrderNum(orderNumMap.get(preKey) + preOrderNum);
                pickUpReportDTO.setGotedNum(gotedNumMap.get(preKey) + preGotedNum);
                pickUpReportDTO.setShouldGotNum(shouldGotNumMap.get(preKey) + preShouldGotNum);
                pickUpReportDTO.setShouldGotInTimeGotNum(shouldGotInTimeNumMap.get(preKey) + preShouldGotInTimeGotNum);
                reportAllList.add(pickUpReportDTO);
            }else{
                WaybillPickUpReportDTO pickUpReportDTO = new WaybillPickUpReportDTO();
                pickUpReportDTO.setCpCode(cpCode);
                pickUpReportDTO.setAgent(agent);
                pickUpReportDTO.setOrderNum(preOrderNum);
                pickUpReportDTO.setGotedNum(preGotedNum);
                pickUpReportDTO.setShouldGotNum(preShouldGotNum);
                pickUpReportDTO.setShouldGotInTimeGotNum(preShouldGotInTimeGotNum);
                reportAllList.add(pickUpReportDTO);
            }
        }
        return reportAllList;
    }

    /**
     * 构建CP揽收数据
     * @param reportList
     * @return
     */
    private List<PickUpCpCollectRobotVO> buildCollectCpDataVo(List<WaybillPickUpReportDTO> reportList) {
        List<PickUpCpCollectRobotVO> robotList = new ArrayList<>();

        Integer sumGotedNum = 0;
        Integer sumShouldGotNum = 0;
        Integer sumShouldGotInTimeGotNum = 0;

        for(WaybillPickUpReportDTO report : reportList) {
            PickUpCpCollectRobotVO pickUpCpCollectRobotVO = new PickUpCpCollectRobotVO();

            String tempCpName = PickUpCpEnum.getNameByCode(report.getCpCode());
            String cpName = StringUtils.isBlank(tempCpName) ? report.getCpCode() : tempCpName;
            String agentName = PickUpAgentEnum.nameByAgent(report.getAgent());
            pickUpCpCollectRobotVO.setCpProduct(agentName + "-" + cpName);
            pickUpCpCollectRobotVO.setGotedNum(report.getGotedNum());
            sumGotedNum += report.getGotedNum();
            pickUpCpCollectRobotVO.setShouldGotNum(report.getShouldGotNum());
            sumShouldGotNum += report.getShouldGotNum();
            String shouldGotRate = calculatePercentage(report.getGotedNum(), report.getShouldGotNum());
            pickUpCpCollectRobotVO.setShouldGotRate(shouldGotRate);
            String shouldGotInTimeGotRate = calculatePercentage(report.getShouldGotInTimeGotNum(), report.getShouldGotNum());
            sumShouldGotInTimeGotNum += report.getShouldGotInTimeGotNum();
            pickUpCpCollectRobotVO.setShouldGotInTimeGotRate(shouldGotInTimeGotRate);

            robotList.add(pickUpCpCollectRobotVO);
        }
        PickUpCpCollectRobotVO sumPickUpCpCollectRobotVO = new PickUpCpCollectRobotVO();
        sumPickUpCpCollectRobotVO.setCpProduct("汇总");
        sumPickUpCpCollectRobotVO.setGotedNum(sumGotedNum);
        sumPickUpCpCollectRobotVO.setShouldGotNum(sumShouldGotNum);
        String sumShouldGotRate = calculatePercentage(sumGotedNum, sumShouldGotNum);
        sumPickUpCpCollectRobotVO.setShouldGotRate(sumShouldGotRate);
        String shouldGotInTimeGotRate = calculatePercentage(sumShouldGotInTimeGotNum, sumShouldGotNum);
        sumPickUpCpCollectRobotVO.setShouldGotInTimeGotRate(shouldGotInTimeGotRate);
        robotList.add(sumPickUpCpCollectRobotVO);

        return robotList;
    }

    /**
     * 客户重点指标数据
     * @param request
     * @return
     */
    @GetMapping("/customer/key/index")
    public JSONObject customerKeyIndexData(PickUpOnlineReportRequest request) {

        if (!StringUtils.equals(request.getAccessToken(), BridgeSwitch.reportInterfaceAccessToken)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorCode", 401);
            jsonObject.put("errorMsg", "无权限");
            jsonObject.put("success", false);
            return jsonObject;
        }

        // 默认当天
        Date startDate = BridgeDateUtil.dateToStartTime(new Date());
        Date endDate = new Date();

        if(StringUtils.equals(request.getBizDate(), PickUpOnlineReportRequest.LASTDAY)){
            // 昨天
            Date dateBefore = BridgeDateUtil.getDateBeforeNDays(1);
            startDate = BridgeDateUtil.dateToStartTime(dateBefore);
            endDate = BridgeDateUtil.dateToEndTime(dateBefore);
        }else if(StringUtils.isNotBlank(request.getBizDate())
            && !StringUtils.equalsAny(request.getBizDate(), PickUpOnlineReportRequest.LASTDAY, PickUpOnlineReportRequest.TODAY)){
            startDate = BridgeDateUtil.strToDate(request.getBizDate(), BridgeDateUtil.patternDayFormat);
            endDate = BridgeDateUtil.dateToEndTime(BridgeDateUtil.strToDate(request.getBizDate(),
                BridgeDateUtil.patternDayFormat));
        }

        // 查询客户重点指标数据
        return queryKeyCustomerData(request, startDate, endDate);
    }

    /**
     * 客户重点指标数据
     * @param request
     * @param startDate
     * @param endDate
     * @return
     */
    private JSONObject queryKeyCustomerData(PickUpOnlineReportRequest request, Date startDate, Date endDate) {
        if(null == request || null == startDate || null == endDate){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorCode", 402);
            jsonObject.put("errorMsg", "参数错误");
            jsonObject.put("success", false);
            return jsonObject;
        }
        WaybillPickUpTicketParam ticketParam = new WaybillPickUpTicketParam();
        ticketParam.setStartTime(startDate);
        ticketParam.setEndTime(endDate);
        ticketParam.setPrimarySource(PickUpTicketPrimarySource.platform.name());
        ticketParam.setDealPart(PickUpTicketDealPartEnum.CN.name());
        ticketParam.setSourceList(BridgeSwitch.CUSTOMER_REPORT_ORDER_CHANNELS);
        List<WaybillPickUpTicketCountDTO> ticketReportList = waybillPickUpTicketMapper.queryCountGroupSource(ticketParam);

        WaybillPickUpOrderDetailQueryDTO detailQueryDTO = new WaybillPickUpOrderDetailQueryDTO();
        detailQueryDTO.setCreateStart(startDate);
        detailQueryDTO.setCreateEnd(endDate);
        if (!"all".equals(request.getSource())) {
            detailQueryDTO.setOrderChannels(request.getSource());
        }
        if (!"all".equals(request.getCpCode())) {
            detailQueryDTO.setCpCode(request.getCpCode());
        }
        detailQueryDTO.setOrderChannelsList(BridgeSwitch.CUSTOMER_REPORT_ORDER_CHANNELS);

        List<WaybillPickUpReportDTO> reportList = queryReportCustomer(detailQueryDTO);

        // 合并订单工单数据
        List<PickUpKeyRobotVO> robotList = concatTicketList(reportList, ticketReportList);

        String content = MarkdownExportUtil.build(robotList, PickUpKeyRobotVO.class);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("errorCode", 200);
        jsonObject.put("errorMsg", "");
        jsonObject.put("success", true);
        jsonObject.put("fields", ImmutablePair.of("content", content));

        return jsonObject;
    }

    /**
     * 合并订单工单数据
     * @param reportList
     * @param ticketReportList
     */
    private List<PickUpKeyRobotVO> concatTicketList(List<WaybillPickUpReportDTO> reportList, List<WaybillPickUpTicketCountDTO> ticketReportList) {
        List<PickUpKeyRobotVO> robotList = new ArrayList<>();
        if (reportList == null || ticketReportList == null) {
            return robotList;
        }

        // 创建一个映射关系，存储 orderChannels 到 ticketNum 的映射
        Map<String, Integer> orderChannelsToDistTicketNumMap = new HashMap<>(4);
        for (WaybillPickUpTicketCountDTO ticketReport : ticketReportList) {
            String orderChannels = ticketReport.getOrderSource();
            Integer distinctTicketNum = ticketReport.getDistinctNum();
            orderChannelsToDistTicketNumMap.put(orderChannels, distinctTicketNum);
        }

        int sumAcceptIn30mNum = 0;
        int sumDistinctTicketNum = 0;
        int sumOrderNum = 0;
        int sumShouldGotInTimeGotNum = 0;
        int sumShouldGotNum = 0;
        // 遍历 reportList，根据 orderChannels 设置 ticketNum
        for (WaybillPickUpReportDTO report : reportList) {
            PickUpKeyRobotVO pickUpKeyRobotVO = new PickUpKeyRobotVO();
            String orderChannels = report.getOrderChannels();
            String tempOrderChannelsName = OrderSourceMapUtil.getOrderSource(orderChannels);
            String customerName = StringUtils.isBlank(tempOrderChannelsName) ? orderChannels : tempOrderChannelsName;
            Integer distinctTicketNum  = orderChannelsToDistTicketNumMap.get(orderChannels);
            pickUpKeyRobotVO.setCustomer(customerName);
            pickUpKeyRobotVO.setOrderNum(report.getOrderNum());
            pickUpKeyRobotVO.setDistinctTicketNum(distinctTicketNum != null ? distinctTicketNum : 0);
            String acceptIn30mRate = calculatePercentage(report.getAcceptIn30mNum(), report.getOrderNum());
            sumAcceptIn30mNum += report.getAcceptIn30mNum();
            sumOrderNum += report.getOrderNum();
            pickUpKeyRobotVO.setAcceptIn30mRate(acceptIn30mRate);
            String distinctTicketRate = calculatePercentage(pickUpKeyRobotVO.getDistinctTicketNum(), report.getOrderNum());
            sumDistinctTicketNum += pickUpKeyRobotVO.getDistinctTicketNum();
            pickUpKeyRobotVO.setDistinctTicketRate(distinctTicketRate);
            String shouldGotInTimeGotRate = calculatePercentage(report.getShouldGotInTimeGotNum(), report.getShouldGotNum());
            sumShouldGotInTimeGotNum += report.getShouldGotInTimeGotNum();
            sumShouldGotNum += report.getShouldGotNum();
            pickUpKeyRobotVO.setShouldGotInTimeGotRate(shouldGotInTimeGotRate);
            robotList.add(pickUpKeyRobotVO);
        }
        // 添加汇总数据
        PickUpKeyRobotVO collectVO = new PickUpKeyRobotVO();
        collectVO.setCustomer("总计");
        collectVO.setOrderNum(robotList.stream().mapToInt(PickUpKeyRobotVO::getOrderNum).sum());
        collectVO.setDistinctTicketNum(robotList.stream().mapToInt(PickUpKeyRobotVO::getDistinctTicketNum).sum());
        String sumAcceptIn30mRate = calculatePercentage(sumAcceptIn30mNum, sumOrderNum);
        collectVO.setAcceptIn30mRate(sumAcceptIn30mRate);
        String sumDistinctTicketRate = calculatePercentage(sumDistinctTicketNum, sumOrderNum);
        collectVO.setDistinctTicketRate(sumDistinctTicketRate);
        String sumShouldGotInTimeGotRate = calculatePercentage(sumShouldGotInTimeGotNum, sumShouldGotNum);
        collectVO.setShouldGotInTimeGotRate(sumShouldGotInTimeGotRate);
        robotList.add(collectVO);

        return robotList;
    }

    /**
     * 合并订单工单数据
     * @param reportList
     * @param ticketReportList
     */
    private List<PickUpKeyCpRobotVO> concatCpTicketList(List<WaybillPickUpReportDTO> reportList, List<WaybillPickUpTicketCountDTO> ticketReportList) {
        List<PickUpKeyCpRobotVO> robotList = new ArrayList<>();
        if (reportList == null || ticketReportList == null) {
            return robotList;
        }

        // 创建一个映射关系，存储 orderChannels 到 ticketNum 的映射
        Map<String, Integer> orderChannelsToTicketNumMap = new HashMap<>();
        for (WaybillPickUpTicketCountDTO ticketReport : ticketReportList) {
            String orderChannels = ticketReport.getOrderSource();
            String cpCode = ticketReport.getCpCode();
            String agent = ticketReport.getAgent();
            Integer ticketNum = ticketReport.getDistinctNum();
            orderChannelsToTicketNumMap.put(orderChannels+cpCode+agent, ticketNum);
        }

        // 遍历 reportList，根据 orderChannels 设置 ticketNum
        for (WaybillPickUpReportDTO report : reportList) {
            PickUpKeyCpRobotVO pickUpKeyRobotVO = new PickUpKeyCpRobotVO();
            String orderChannels = report.getOrderChannels();
            String cpCode = report.getCpCode();
            String agent = report.getAgent();
            Integer ticketNum = orderChannelsToTicketNumMap.get(orderChannels+cpCode+agent);
            String agentName = PickUpAgentEnum.nameByAgent(report.getAgent());
            String tempCpName = PickUpCpEnum.getNameByCode(report.getCpCode());
            String cpName = StringUtils.isBlank(tempCpName)? report.getCpCode() : tempCpName;
            pickUpKeyRobotVO.setCpCode(agentName+"-"+cpName);
            String orderChannelsName = OrderSourceMapUtil.getOrderSource(orderChannels);
            pickUpKeyRobotVO.setCustomer(StringUtils.isBlank(orderChannelsName) ? orderChannels : orderChannelsName);
            pickUpKeyRobotVO.setOrderNum(report.getOrderNum());
            pickUpKeyRobotVO.setDistinctTicketNum(ticketNum == null ? 0 :ticketNum);
            String distinctTicketRate = calculatePercentage(pickUpKeyRobotVO.getDistinctTicketNum(), report.getOrderNum());
            pickUpKeyRobotVO.setDistinctTicketRate(distinctTicketRate);
            robotList.add(pickUpKeyRobotVO);
        }
        return robotList;
    }


    /**
     * 计算百分比，四舍五入保留两位小数
     * @param numerator 分子
     * @param denominator 分母
     * @return
     */
    private String calculatePercentage(int numerator, int denominator) {
        if (denominator == 0) {
            return "0.00%";
        }
        if(numerator > denominator){
            numerator = denominator;
        }
        double percentage = (double) numerator / denominator * 100;
        // 四舍五入保留两位小数
        BigDecimal bd = new BigDecimal(percentage).setScale(2, RoundingMode.HALF_UP);
        return bd.toString() + "%";
    }




    @GetMapping("ticket/statistics")
    public BaseResult<List<WaybillOnlineChannelOrderStaticsDTO>> statistics(
        @RequestParam(required = false) String bizDate, String accessToken) {
        if (!StringUtils.equals(accessToken, BridgeSwitch.reportInterfaceAccessToken)) {
            throw new PickUpBusinessException("401", "无权限");
        }
        List<WaybillOnlineChannelOrderStaticsDTO> result = baseStatistics(bizDate);
        return BaseResult.success(Lists.newArrayList(result));
    }

    @GetMapping("ticket/statistics/{type}")
    public JSONObject statisticsByType(String accessToken, @PathVariable String type) {
        if (!StringUtils.equals(accessToken, BridgeSwitch.reportInterfaceAccessToken)) {
            throw new PickUpBusinessException("401", "无权限");
        }
        String content = "";

        if(StringUtils.equals(type, "CP")){
            content = cpOrderInfo();
        }else if(StringUtils.equals(type, "yesterdayStatics")){
            content = yesterdayOrderInfo();
        }else {
            content = channelOrderInfo(type);
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("errorCode", 200);
        jsonObject.put("errorMsg", "");
        jsonObject.put("success", true);
        jsonObject.put("fields", ImmutablePair.of("content", content));

        return jsonObject;
    }

    /**
     * 指定渠道订单统计
     * @param orderChannel
     * @return
     */
    public String channelOrderInfo(String orderChannel){
        List<WaybillOnlineChannelOrderStaticsDTO> result = baseStatistics(null);

        List<WaybillOnlineChannelOrderStaticsDTO> list = ListUtil.stream(result)
            .filter(t -> orderChannel.equals(t.getOrderChannels())).collect(Collectors.toList());

        list.sort((o1, o2) -> o2.getNum() - o1.getNum());

        List<PickUpJLRobotVO> robotVOList = Lists.newArrayList();



        for (WaybillOnlineChannelOrderStaticsDTO orderStaticsDTO : list) {

            PickUpJLRobotVO jlRobotVO = new PickUpJLRobotVO();
            jlRobotVO.setCpInfo(PickUpAgentEnum.nameByAgent(orderStaticsDTO.getAgent())
                    + "-" + PickUpCpEnum.getNameByCode(orderStaticsDTO.getCpCode()));
            jlRobotVO.setNum(orderStaticsDTO.getNum());
            jlRobotVO.setTicketNum(orderStaticsDTO.getTicketNum());
            jlRobotVO.setDistinctTicketNum(orderStaticsDTO.getDistinctTicketNum());
            jlRobotVO.setTicketRate(buildRate(orderStaticsDTO.getTicketNum(), orderStaticsDTO.getNum()));
            jlRobotVO.setDistinctTicketRate(buildRate(orderStaticsDTO.getDistinctTicketNum(), orderStaticsDTO.getNum()));
            robotVOList.add(jlRobotVO);
        }

        WaybillOnlineChannelOrderStaticsDTO staticsDTO = list.stream().reduce((o1, o2) -> {
            o1.setNum(o1.getNum() + o2.getNum());
            o1.setTicketNum(o1.getTicketNum() + o2.getTicketNum());
            o1.setDistinctTicketNum(o1.getDistinctTicketNum() + o2.getDistinctTicketNum());
            return o1;
        }).orElse(null);

        if(staticsDTO != null){
            PickUpJLRobotVO jlRobotVO = new PickUpJLRobotVO();
            jlRobotVO.setCpInfo("总计");
            jlRobotVO.setNum(staticsDTO.getNum());
            jlRobotVO.setTicketNum(staticsDTO.getTicketNum());
            jlRobotVO.setDistinctTicketNum(staticsDTO.getDistinctTicketNum());
            jlRobotVO.setTicketRate(buildRate(staticsDTO.getTicketNum(), staticsDTO.getNum()));
            jlRobotVO.setDistinctTicketRate(buildRate(staticsDTO.getDistinctTicketNum(), staticsDTO.getNum()));
            robotVOList.add(jlRobotVO);
        }

        return MarkdownExportUtil.build(robotVOList, PickUpJLRobotVO.class);
    }

    public String yesterdayOrderInfo(){
        String bizDate = BridgeDateUtil.dateToStr(DateUtils.addDays(new Date(), - 1), BridgeDateUtil.patternDayFormat);
        List<WaybillOnlineChannelOrderStaticsDTO> result = baseStatistics(bizDate);

        Map<String, WaybillOnlineChannelOrderStaticsRobotDTO> map = buildStaticsRobotMapByChannel(result);

        List<PickUpPlatformRobotVO> list = Lists.newArrayList();

        int sumGotedNum = 0;
        int sumShouldGotNum = 0;

        for (PickUpOrderSourceDTO orderSourceDTO : BridgeSwitch.pickupOrderSourceMapping) {
            WaybillOnlineChannelOrderStaticsRobotDTO staticsRobotDTO = map.get(orderSourceDTO.getOrderChannel());
            if(staticsRobotDTO == null){
                continue;
            }
            PickUpPlatformRobotVO platformRobotVO = new PickUpPlatformRobotVO();
            platformRobotVO.setPlatform(orderSourceDTO.getSourceName());
            platformRobotVO.setNum(staticsRobotDTO.getNum());
            platformRobotVO.setGotRate(buildRate(staticsRobotDTO.getGotedNum(), staticsRobotDTO.getShouldGotNum()));
            sumGotedNum += staticsRobotDTO.getGotedNum();
            sumShouldGotNum += staticsRobotDTO.getShouldGotNum();
            platformRobotVO.setTicketNum(staticsRobotDTO.getTicketNum());
            platformRobotVO.setTicketRate(buildRate(staticsRobotDTO.getTicketNum(), staticsRobotDTO.getNum()));
            platformRobotVO.setDistinctTicketNum(staticsRobotDTO.getDistinctTicketNum());
            platformRobotVO.setDistinctTicketRate(buildRate(staticsRobotDTO.getDistinctTicketNum(), staticsRobotDTO.getNum()));
            list.add(platformRobotVO);
        }
        // add collect
        PickUpPlatformRobotVO sumCollectRobotVO = new PickUpPlatformRobotVO();
        int sumNum = list.stream().mapToInt(PickUpPlatformRobotVO::getNum).sum();
        int sumTicketNum = list.stream().mapToInt(PickUpPlatformRobotVO::getTicketNum).sum();
        int sumDistinctTicketNum = list.stream().mapToInt(PickUpPlatformRobotVO::getDistinctTicketNum).sum();

        sumCollectRobotVO.setPlatform("汇总");
        sumCollectRobotVO.setNum(sumNum);
        sumCollectRobotVO.setGotRate(buildRate(sumGotedNum, sumShouldGotNum));
        sumCollectRobotVO.setTicketNum(sumTicketNum);
        sumCollectRobotVO.setTicketRate(buildRate(sumTicketNum, sumNum));
        sumCollectRobotVO.setDistinctTicketNum(sumDistinctTicketNum);
        sumCollectRobotVO.setDistinctTicketRate(buildRate(sumDistinctTicketNum, sumNum));

        list.add(sumCollectRobotVO);

        return MarkdownExportUtil.build(list, PickUpPlatformRobotVO.class);
    }

    public String cpOrderInfo(){
        try {

            String bizDate = BridgeDateUtil.dateToStr(DateUtils.addDays(new Date(), - 1), BridgeDateUtil.patternDayFormat);
            List<WaybillOnlineChannelOrderStaticsDTO> result = baseStatistics(bizDate);

            Map<String, WaybillOnlineCpOrderStaticsDTO> map = buildStaticsRobotMapByCp(result);

            List<PickUpCpRobotVO> list = Lists.newArrayList();

            for (WaybillOnlineCpOrderStaticsDTO staticsRobotDTO : map.values()) {
                PickUpCpRobotVO pickUpCpRobotVO = new PickUpCpRobotVO();
                pickUpCpRobotVO.setCpInfo(PickUpAgentEnum.nameByAgent(staticsRobotDTO.getAgent())
                    + "-" + PickUpCpEnum.getNameByCode(staticsRobotDTO.getCpCode()));
                pickUpCpRobotVO.setShouldGotNum(staticsRobotDTO.getShouldGotNum());
                pickUpCpRobotVO.setGotedNum(staticsRobotDTO.getGotedNum());
                pickUpCpRobotVO.setGotRate(buildRate(staticsRobotDTO.getGotedNum(), staticsRobotDTO.getShouldGotNum()));
                list.add(pickUpCpRobotVO);
            }
            return MarkdownExportUtil.build(list, PickUpCpRobotVO.class);
        } catch (Exception e) {
            PickUpLogUtil.errLog("", "schedule_real_time_report_job", "schedule_real_time_report_job_error", "实时播报异常", e);
        }
        return null;
    }

    private String buildRate(int para1, int para2){
        if(para2 == 0){
            return "-";
        }
        return new BigDecimal(para1).multiply(new BigDecimal(100)).divide(new BigDecimal(para2), 2, RoundingMode.HALF_UP).toString() + "%";
    }

    @NotNull
    private static Map<String, WaybillOnlineChannelOrderStaticsRobotDTO> buildStaticsRobotMapByChannel(
        List<WaybillOnlineChannelOrderStaticsDTO> result) {
        Map<String, WaybillOnlineChannelOrderStaticsRobotDTO> map = Maps.newHashMap();

        for (WaybillOnlineChannelOrderStaticsDTO orderStaticsDTO : ListUtil.non(result)) {
            WaybillOnlineChannelOrderStaticsRobotDTO robotDTO = map.computeIfAbsent(orderStaticsDTO.getOrderChannels(),
                s -> new WaybillOnlineChannelOrderStaticsRobotDTO());
            robotDTO.setOrderChannels(orderStaticsDTO.getOrderChannels());
            robotDTO.setNum(orderStaticsDTO.getNum() + robotDTO.getNum());
            robotDTO.setShouldGotNum(orderStaticsDTO.getShouldGotNum() + robotDTO.getShouldGotNum());
            robotDTO.setGotedNum(orderStaticsDTO.getGotedNum() + robotDTO.getGotedNum());
            robotDTO.setTicketNum(orderStaticsDTO.getTicketNum() + robotDTO.getTicketNum());
            robotDTO.setDistinctTicketNum(orderStaticsDTO.getDistinctTicketNum() + robotDTO.getDistinctTicketNum());
        }
        return map;
    }

    @NotNull
    private static Map<String, WaybillOnlineCpOrderStaticsDTO> buildStaticsRobotMapByCp(
        List<WaybillOnlineChannelOrderStaticsDTO> result) {
        Map<String, WaybillOnlineCpOrderStaticsDTO> map = Maps.newHashMap();

        for (WaybillOnlineChannelOrderStaticsDTO orderStaticsDTO : ListUtil.non(result)) {
            WaybillOnlineCpOrderStaticsDTO robotDTO = map.computeIfAbsent(orderStaticsDTO.getAgent() + orderStaticsDTO.getCpCode(),
                s -> new WaybillOnlineCpOrderStaticsDTO());
            robotDTO.setAgent(orderStaticsDTO.getAgent());
            robotDTO.setCpCode(orderStaticsDTO.getCpCode());
            robotDTO.setShouldGotNum(orderStaticsDTO.getShouldGotNum() + robotDTO.getShouldGotNum());
            robotDTO.setGotedNum(orderStaticsDTO.getGotedNum() + robotDTO.getGotedNum());
        }
        return map;
    }

    private List<WaybillOnlineChannelOrderStaticsDTO> baseStatistics(String bizDate) {

        Date date = new Date();
        if (StringUtils.isNotBlank(bizDate)) {
            date = BridgeDateUtil.strToDate(bizDate, BridgeDateUtil.patternDayFormat);
        }

        Date start = BridgeDateUtil.dateToStartTime(date);
        Date end = BridgeDateUtil.dateToEndTime(date);

        // 统计当天订单信息
        List<WaybillOnlineChannelOrderStaticsDTO> orderStaticsList
            = waybillPickUpOnlineStatisticsMapper.staticsOrderNumByChannel(start, end);

        // 统计当天工单信息
        List<WaybillOnlineChannelOrderStaticsDTO> ticketStatisList
            = waybillPickUpOnlineStatisticsMapper.staticsTicketNumByChannel(start, end);

        // 当天应该揽收的信息，包括当天前一天17天到当天17点的实时单和预约到当天的订单
        Date dayStart = new DateTime(start).minusDays(1).withHourOfDay(17)
            .withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0).toDate();
        Date dayEnd = new DateTime(end).withHourOfDay(17)
            .withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0).toDate();
        List<WaybillOnlineChannelOrderStaticsDTO> gotedStaticsList
            = waybillPickUpOnlineStatisticsMapper.staticsOrderGotNumByChannel(start, end,
            dayStart, dayEnd);

        Map<WaybillOnlineChannelOrderStaticsDTO, WaybillOnlineChannelOrderStaticsDTO> result = new HashMap<>();
        fillResult(orderStaticsList, result);
        fillResult(ticketStatisList, result);
        fillResult(gotedStaticsList, result);

        return Lists.newArrayList(result.values());
    }

    private void fillResult(List<WaybillOnlineChannelOrderStaticsDTO> list,
        Map<WaybillOnlineChannelOrderStaticsDTO, WaybillOnlineChannelOrderStaticsDTO> result) {
        for (WaybillOnlineChannelOrderStaticsDTO orderStaticsDTO : list) {
            WaybillOnlineChannelOrderStaticsDTO staticsDTO = result.computeIfAbsent(orderStaticsDTO, dto -> dto);
            copyProperties(orderStaticsDTO, staticsDTO);
        }
    }

    private void copyProperties(WaybillOnlineChannelOrderStaticsDTO orig, WaybillOnlineChannelOrderStaticsDTO target) {
        for (Field field : ReflectUtil.getFields(orig.getClass())) {
            Object num = ReflectUtil.getFieldValue(orig, field);
            if (num instanceof Integer && (Integer)num > 0) {
                ReflectUtil.setFieldValue(target, field, num);
            }
        }
    }

    /**
     * 一手客户重点指标数据
     * @param request
     * @return
     */
    @GetMapping("/yishou/customer/index")
    public JSONObject yishouCustomerIndexData(PickUpOnlineReportRequest request) {

        if (!StringUtils.equals(request.getAccessToken(), BridgeSwitch.reportInterfaceAccessToken)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorCode", 401);
            jsonObject.put("errorMsg", "无权限");
            jsonObject.put("success", false);
            return jsonObject;
        }

        List<PickUpYiShouRobotVO> robotList = new ArrayList<>();
        robotList.addAll(getYishouReportData(request, 1));
        robotList.addAll(getYishouReportData(request, 2));
        String content = MarkdownExportUtil.build(robotList, PickUpYiShouRobotVO.class);
        return createSuccessResponse(content);
    }

    private List<PickUpYiShouRobotVO> getYishouReportData(PickUpOnlineReportRequest request, int daysBefore) {
        Date startDate = BridgeDateUtil.getDateBeforeNDays(daysBefore);
        Date endDate = BridgeDateUtil.dateToEndTime(startDate);

        WaybillPickUpOrderDetailQueryDTO detailQueryDTO = new WaybillPickUpOrderDetailQueryDTO();
        detailQueryDTO.setCreateStart(startDate);
        detailQueryDTO.setCreateEnd(endDate);
        detailQueryDTO.setOrderChannelsList(Collections.singletonList("YISHOU"));
        // 仅在非 "all" 的情况下设置 cpCode
        if (!"all".equals(request.getCpCode())) {
            detailQueryDTO.setCpCode(request.getCpCode());
        }

        WaybillPickUpReportDTO reportDTO = queryCustomerData(detailQueryDTO);
        if (reportDTO != null) {
            PickUpYiShouRobotVO pickUpYiShouRobotVO = new PickUpYiShouRobotVO();
            pickUpYiShouRobotVO.setTimeRange(BridgeDateUtil.dateToStr(startDate,BridgeDateUtil.patternDayFormat));
            pickUpYiShouRobotVO.setOrderNum(reportDTO.getOrderNum());
            String acceptIn10mRate = calculatePercentage(reportDTO.getAcceptIn10mNum(), reportDTO.getOrderNum());
            pickUpYiShouRobotVO.setAccept10mRate(acceptIn10mRate);
            String shouldGotInTimeGotRate = calculatePercentage(reportDTO.getShouldGotInTimeGotNum(), reportDTO.getShouldGotNum());
            pickUpYiShouRobotVO.setInTimeGotRate(shouldGotInTimeGotRate);
            String gotNumRate = calculatePercentage(reportDTO.getCustomerGotNum(), reportDTO.getShouldGotNum());
            pickUpYiShouRobotVO.setGotRate(gotNumRate);
            pickUpYiShouRobotVO.setCancelNum(reportDTO.getCancelNum());
            String cancelNum = calculatePercentage(reportDTO.getCancelNum(), reportDTO.getOrderNum());
            pickUpYiShouRobotVO.setCancelRate(cancelNum);
            return Collections.singletonList(pickUpYiShouRobotVO);
        }
        return Collections.emptyList();
    }
    /**
     * 支付宝重点指标数据
     * @param request
     * @return
     */
    @GetMapping("/alipay/customer/index")
    public JSONObject alipayCustomerIndexData(PickUpOnlineReportRequest request) {

        if (!StringUtils.equals(request.getAccessToken(), BridgeSwitch.reportInterfaceAccessToken)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("errorCode", 401);
            jsonObject.put("errorMsg", "无权限");
            jsonObject.put("success", false);
            return jsonObject;
        }

        List<PickUpAlipayRobotVO> robotList = new ArrayList<>();
        robotList.addAll(getAlipayReportData(request, 1));
        robotList.addAll(getAlipayReportData(request, 2));
        String content = MarkdownExportUtil.build(robotList, PickUpAlipayRobotVO.class);
        return createSuccessResponse(content);
    }

    private List<PickUpAlipayRobotVO> getAlipayReportData(PickUpOnlineReportRequest request, int daysBefore) {
        Date startDate = BridgeDateUtil.getDateBeforeNDays(daysBefore);
        Date endDate = BridgeDateUtil.dateToEndTime(startDate);

        WaybillPickUpOrderDetailQueryDTO detailQueryDTO = new WaybillPickUpOrderDetailQueryDTO();
        detailQueryDTO.setCreateStart(startDate);
        detailQueryDTO.setCreateEnd(endDate);
        detailQueryDTO.setOrderChannelsList(Collections.singletonList("ALIPAY"));

        // 仅在非 "all" 的情况下设置 cpCode
        if (!"all".equals(request.getCpCode())) {
            detailQueryDTO.setCpCode(request.getCpCode());
        }

        WaybillPickUpReportDTO reportDTO = queryCustomerData(detailQueryDTO);
        if (reportDTO != null) {
            PickUpAlipayRobotVO pickUpAlipayRobotVO = new PickUpAlipayRobotVO();
            pickUpAlipayRobotVO.setTimeRange(BridgeDateUtil.dateToStr(startDate,BridgeDateUtil.patternDayFormat));
            pickUpAlipayRobotVO.setOrderNum(reportDTO.getOrderNum());
            String acceptRate = calculatePercentage(reportDTO.getAcceptIn10mNum(), reportDTO.getOrderNum());
            pickUpAlipayRobotVO.setAccept10mRate(acceptRate);
            String shouldGotInTimeGotRate = calculatePercentage(reportDTO.getShouldGotInTimeGotNum(), reportDTO.getShouldGotNum());
            pickUpAlipayRobotVO.setGotInTimeRate(shouldGotInTimeGotRate);
            // 应揽已揽率
            String shouldGotNumRate = calculatePercentage(reportDTO.getCustomerGotNum(), reportDTO.getShouldGotNum());
            pickUpAlipayRobotVO.setGotRate(shouldGotNumRate);
            return Collections.singletonList(pickUpAlipayRobotVO);
        }
        return Collections.emptyList();
    }
    /**
     * 指标数据
     * @param detailQueryDTO
     * @return
     */
    private WaybillPickUpReportDTO queryCustomerData(WaybillPickUpOrderDetailQueryDTO detailQueryDTO) {

        List<WaybillPickUpReportDTO> reportList = queryReportCustomer(detailQueryDTO);
        if (CollectionUtils.isEmpty(reportList)) {
            return null;
        }
        //只会返回一条
        return reportList.get(0);
    }

    /**
     * 查询指标数据 聚合实时单和预约单
     * @param detailQueryDTO
     * @return
     */
    private List<WaybillPickUpReportDTO> queryReportCustomer(WaybillPickUpOrderDetailQueryDTO detailQueryDTO){

        // 实时单
        List<WaybillPickUpReportDTO> reportListCurrList = waybillPickUpDetailMapper.queryReportCustomerCurr(detailQueryDTO);

        Date newStartDate = DateUtils.addDays(detailQueryDTO.getCreateStart(), -3);
        Date appoDateStart = com.cainiao.waybill.bridge.common.util.DateUtils.dateToDateStart(detailQueryDTO.getCreateEnd());
        Date appoDateEnd = com.cainiao.waybill.bridge.common.util.DateUtils.dateToDateEnd(detailQueryDTO.getCreateEnd());
        detailQueryDTO.setCreateStart(newStartDate);
        detailQueryDTO.setAppoDateStart(appoDateStart);
        detailQueryDTO.setAppoDateEnd(appoDateEnd);

        // 预约单
        List<WaybillPickUpReportDTO> reportListAppoList = waybillPickUpDetailMapper.queryReportCustomerAppo(detailQueryDTO);

        // 合并实时单和预约单数据
        for(WaybillPickUpReportDTO currPickUpReportDTO : reportListCurrList){
            WaybillPickUpReportDTO waybillPickUpReportDTO = reportListAppoList.stream().filter(
                reportDTO -> reportDTO.getOrderChannels().equals(currPickUpReportDTO.getOrderChannels()))
                .findFirst().orElse(new WaybillPickUpReportDTO());
            currPickUpReportDTO.setShouldGotNum(currPickUpReportDTO.getCurrShouldDropInNum() + waybillPickUpReportDTO.getAppoShouldDropInNum());
            currPickUpReportDTO.setShouldGotedNum(currPickUpReportDTO.getCurrShouldDropInNum() + waybillPickUpReportDTO.getAppoShouldDropInNum());
            currPickUpReportDTO.setShouldGotInTimeGotNum(currPickUpReportDTO.getCurrInTimeDropInNum() + waybillPickUpReportDTO.getAppoInTimeDropInNum());
            currPickUpReportDTO.setCustomerGotInTimeNum(currPickUpReportDTO.getShouldGotInTimeGotNum());
            currPickUpReportDTO.setCustomerGotNum(currPickUpReportDTO.getCurrCustomerGotNum() + waybillPickUpReportDTO.getAppoCustomerGotNum());
        }
        return reportListCurrList;
    }



    // 创建成功响应
    private JSONObject createSuccessResponse(String content) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("errorCode", 200);
        jsonObject.put("errorMsg", "");
        jsonObject.put("success", true);
        jsonObject.put("fields", ImmutablePair.of("content", content));
        return jsonObject;
    }

}
