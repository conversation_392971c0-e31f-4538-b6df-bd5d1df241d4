package com.cainiao.waybill.bridge.web.pickup.ticket.util;

import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> zouping.fzp
 * @Classname AddressUtil
 * @Description
 * @Date 2022/8/19 3:49 下午
 * @Version 1.0
 */
public class AddressUtil {

    public static AddressDTO parseFormatAddress(String formatAddress) {
        if (StringUtils.isBlank(formatAddress)) {
            return null;
        }
        try {
            AddressDTO addressDTO = new AddressDTO(formatAddress);
            return addressDTO;
        } catch (Exception exception) {
            PickUpLogUtil.errLog("", "ADDRESS_PARSE_ERROR", "ADDESS_ERROR", "地址解析错误:" + formatAddress);
            throw new BridgeBusinessException("ADDESS_ERROR", "地址解析错误");
        }
    }

    public static String parseProvinceFromFormatAddress(String formatAddress) {
        AddressDTO addressDTO = parseFormatAddress(formatAddress);
        if (addressDTO == null) {
            return null;
        }
        return addressDTO.getProvinceName();
    }

    public static String addressFromFormat(String formatAddress) {
        AddressDTO addressDTO = parseFormatAddress(formatAddress);
        if (addressDTO == null) {
            return null;
        }
        return nonStr(addressDTO.getProvinceName())
            + nonStr(addressDTO.getCityName())
            + nonStr(addressDTO.getAreaName())
            + nonStr(addressDTO.getTownName())
            + nonStr(addressDTO.getAddressDetail());
    }

    private static String nonStr(String str) {
        if (str == null) {
            return StringUtils.EMPTY;
        }
        return str;
    }
}
