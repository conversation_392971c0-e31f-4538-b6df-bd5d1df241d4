package com.cainiao.waybill.bridge.innersupport.ad.biz.strategy.sendGroupMessage.type;

import com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder.WaybillAdWorkOrderUpdateRequest;
import com.cainiao.waybill.bridge.innersupport.ad.constant.emums.AdCustomRobotGroupTypeEnum;
import com.cainiao.waybill.bridge.innersupport.ad.constant.emums.AdUserRoleEnum;
import com.cainiao.waybill.bridge.innersupport.ad.model.dto.WaybillAdWorkOrderDTO;
import com.cainiao.waybill.bridge.innersupport.ad.util.CustomRobotGroupMessage;

import java.util.Objects;

/**
 * 修改广告主
 */
public class AdvertiserUpdateActionType implements ActionType {

    private String userRole;

    public AdvertiserUpdateActionType(String userRole) {
        this.userRole = userRole;
    }

    @Override
    public boolean hasChanged(WaybillAdWorkOrderUpdateRequest request, WaybillAdWorkOrderDTO existingWorkOrder) {
        return existingWorkOrder != null && request.getAdvertiserId() != null && !Objects.equals(request.getAdvertiserId(), existingWorkOrder.getAdvertiserId());
    }


    @Override
    public void handleChange(WaybillAdWorkOrderUpdateRequest request, WaybillAdWorkOrderDTO existingWorkOrder) {
        // 菜鸟客服
        if (Objects.equals(userRole, AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue())) {
            // 修改广告主，只有菜鸟客服可以修改广告主
            // 菜鸟客服 & 广告主群里，@广告主
            String sendContent = "【客诉提醒】，" + existingWorkOrder.getMailNo() + "，广告客诉工单已更新，请查收！";
            String adCustomRobotGroupType = AdCustomRobotGroupTypeEnum.CAINIAO_AND_ADVERTISER_DING_TALK_GROUP.getValue();
            String adCustomRobotGroupTarget = request.getAdvertiserId();
            String atUserRoleType = AdUserRoleEnum.ADVERTISER_ROLE.getValue();
            String messageContent = sendContent;
            CustomRobotGroupMessage.sendGroupMessage(
                    adCustomRobotGroupType,
                    adCustomRobotGroupTarget,
                    atUserRoleType,
                    messageContent
            );
        }
    }
}
