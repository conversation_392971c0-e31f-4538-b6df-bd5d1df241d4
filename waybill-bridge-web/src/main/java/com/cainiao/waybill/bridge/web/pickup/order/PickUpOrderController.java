package com.cainiao.waybill.bridge.web.pickup.order;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.biz.fee.service.PrePayService;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpOrderExportResponse;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpSwitchCpTask;
import com.cainiao.waybill.bridge.biz.pickup.manager.impl.PickUpSwitchCpManager;
import com.cainiao.waybill.bridge.biz.ticket.dto.LogisticsDetailDTO;
import com.cainiao.waybill.bridge.biz.ticket.dto.PagingResponse;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpBaseRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpLogisticsDetailRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpOrderDetailDTO;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpOrderDetailQueryRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpOrderListQueryRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpOrderOperateListRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpOrderOperateRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpOrderOperateResponse;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpOrderSwitchCpRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpOrderSwitchCpResponse;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpOrderSwitchCpResultRequest;
import com.cainiao.waybill.bridge.biz.ticket.service.PickUpOrderService;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.web.charity.controller.BaseController;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.pickup.constant.TicketResourceEnum;
import com.cainiao.waybill.bridge.web.pickup.ticket.role.PickUpResourceAuthority;
import com.cainiao.waybill.bridge.web.pickup.ticket.util.PickUpExcelUtil;
import com.cainiao.waybill.number.common.Result;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpOrderController
 * @Description
 * @Date 2022/11/29 11:53 下午
 * @Version 1.0
 */
@Slf4j(topic = "PICK_UP_MANAGER_INFO")
@RestController
public class PickUpOrderController extends BaseController {

    @Autowired
    private PickUpOrderService pickUpOrderService;

    @Resource
    private PrePayService prePayService;

    @Autowired
    private PickUpSwitchCpManager pickUpSwitchCpManager;

    @PickUpResourceAuthority(resourceList = {TicketResourceEnum.ORDER_MANAGER, TicketResourceEnum.CP_TICKET_MANAGER, TicketResourceEnum.PLATFORM_MANAGER})
    @RequestMapping("/pickup/order/detail")
    public BaseResult<PickUpOrderDetailDTO> orderDetail(@RequestBody PickUpOrderDetailQueryRequest request) {
        BridgeValidator.validate(request);
        checkMenuRoleValid(request.getQuerySource(), request.getRole());
        PickUpOrderDetailDTO detailDTO = pickUpOrderService.queryOrderDetail(request);
        if (detailDTO == null) {
            return BaseResult.bizFail("order_not_found", "该订单不存在");
        }
        return BaseResult.success(detailDTO);
    }

    /**
     * 平台订单查询
     */
    @PickUpResourceAuthority(TicketResourceEnum.ORDER_MANAGER)
    @RequestMapping("/pickup/order/list")
    public BaseResult<PagingResponse<PickUpOrderDetailDTO>> queryOrderList(@RequestBody PickUpOrderListQueryRequest request) {
        BridgeValidator.validate(request);
        PagingResponse<PickUpOrderDetailDTO> detailDTO = pickUpOrderService.queryOrderList(request);
        return BaseResult.success(detailDTO);
    }

    @PickUpResourceAuthority(TicketResourceEnum.PLATFORM_ORDER)
    @RequestMapping("/pickup/order/platform/list")
    public BaseResult<PagingResponse<PickUpOrderDetailDTO>> queryPlatformOrderList(@RequestBody PickUpOrderListQueryRequest request) {
        BridgeValidator.validate(request);
        request.setPlatformOrder(true);
        PagingResponse<PickUpOrderDetailDTO> detailDTO = pickUpOrderService.queryOrderList(request);
        return BaseResult.success(detailDTO);
    }

    @PickUpResourceAuthority(resourceList = {TicketResourceEnum.ORDER_MANAGER, TicketResourceEnum.PLATFORM_ORDER})
    @RequestMapping("/pickup/order/export")
    public BaseResult<String> export(@RequestBody PickUpOrderListQueryRequest request) {
        if(BridgeSwitch.EXPORT_ORDER_SWITCH){
            PickUpLogUtil.info("请求导出订单。request:{}", JSONObject.toJSONString(request));
            String url  = PickUpExcelUtil.exportFile(request, "订单列表", PickUpOrderExportResponse.class,
                request12 -> pickUpOrderService.queryOrderList(request), BridgeSwitch.ORDER_EXPORT_LIMIT);
            return BaseResult.success(url);
        }else {
            return BaseResult.bizFail("EXPORT_FORBID","导出功能已关闭");
        }
    }

    @PickUpResourceAuthority(resourceList =  {TicketResourceEnum.ORDER_MANAGER, TicketResourceEnum.CP_TICKET_MANAGER, TicketResourceEnum.PLATFORM_ORDER})
    @RequestMapping("/pickup/order/logistics/detail")
    public BaseResult<List<LogisticsDetailDTO>> logisticsDetail(@RequestBody PickUpLogisticsDetailRequest request) {
        BridgeValidator.validate(request);
        checkMenuRoleValid(request.getQuerySource(), request.getRole());
        List<LogisticsDetailDTO> list = pickUpOrderService.queryLogistics(request);
        return BaseResult.success(list);
    }

    @PickUpResourceAuthority(TicketResourceEnum.ORDER_MANAGER)
    @RequestMapping("/pickup/order/operate")
    public BaseResult<Map<Long, String>> operateOrder(@RequestBody PickUpOrderOperateRequest request) throws Exception {
        PickUpLogUtil.info(
            "operate_order" + JSON.toJSONString(request) + ", user: " + JSON.toJSONString(request));
        BridgeValidator.assertBoolean(CollectionUtils.isEmpty(request.getCnOrderIdList()), "订单不能为空");

        Map<Long, String> map = Maps.newHashMap();
        for (Long cnOrderId : ListUtil.non(request.getCnOrderIdList())) {
            try{
                pickUpOrderService.operateOrder(cnOrderId, request);
            }catch (BridgeBaseException exception){
                map.put(cnOrderId, exception.getErrorMessage());
                PickUpLogUtil.info("operate_order_error, {}", JSON.toJSONString(request), exception);
                return BaseResult.bizFail(exception.getErrorCode(), exception.getErrorMessage());
            }catch (BridgeBusinessException exception){
                PickUpLogUtil.info("operate_order_error, {}", JSON.toJSONString(request), exception);
                map.put(cnOrderId, exception.getErrorMessage());
                return BaseResult.bizFail(exception.getErrorCode(), exception.getErrorMessage());
            }
        }
        return BaseResult.success(map);
    }

    @PickUpResourceAuthority(TicketResourceEnum.ORDER_MANAGER)
    @RequestMapping("/pickup/order/operate/list")
    public BaseResult<List<PickUpOrderOperateResponse>> listOperateOrder(
        @RequestBody PickUpOrderOperateListRequest request)
        throws Exception {
        BridgeValidator.validate(request);
        List<PickUpOrderOperateResponse> list = pickUpOrderService.operateOrderList(request.getCnOrderId());
        return BaseResult.success(list);
    }

    @PickUpResourceAuthority(TicketResourceEnum.ORDER_MANAGER)
    @RequestMapping("/pickup/order/switch/cp")
    public BaseResult<PickUpOrderSwitchCpResponse> switchCp(@RequestBody PickUpOrderSwitchCpRequest request) {
        BridgeValidator.validate(request);

        PickUpOrderSwitchCpResponse response = new PickUpOrderSwitchCpResponse();
        String taskId = pickUpSwitchCpManager.createSwitchCpTask(request.getCnOrderIdList(), request.getCpCode(),
            request.getAccount(), request.getDesc());

        response.setTaskId(taskId);
        return BaseResult.success(response);
    }

    @PickUpResourceAuthority(TicketResourceEnum.ORDER_MANAGER)
    @RequestMapping("/pickup/order/switch/cp/task")
    public BaseResult<PickUpSwitchCpTask> switchCpTaskResult(@RequestBody PickUpOrderSwitchCpResultRequest request) {
        BridgeValidator.validate(request);

        PickUpSwitchCpTask pickUpSwitchCpTask = pickUpSwitchCpManager.switchCpTaskResult(request.getTaskId());

        return BaseResult.success(pickUpSwitchCpTask);
    }

    /**
     * 淘外预充值结算平台批量调账
     * @param file
     * @param pickUpBaseRequest
     * @return
     */
    @RequestMapping("/pickup/order/prepay/recharge")
    public BaseResult<String> importExcel(@RequestParam("file") MultipartFile file, PickUpBaseRequest pickUpBaseRequest) {
        Result<String> result = prePayService.uploadRecharge(file);
        if (!result.isSuccess()) {
            return BaseResult.bizFail(result.getErrorCode(), result.getErrorMsg());
        }
        return BaseResult.success("成功");
    }
}
