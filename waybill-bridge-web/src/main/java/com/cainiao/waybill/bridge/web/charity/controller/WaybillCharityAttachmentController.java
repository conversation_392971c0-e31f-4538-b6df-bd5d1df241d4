package com.cainiao.waybill.bridge.web.charity.controller;

import com.cainiao.waybill.bridge.biz.charity.request.CharityAttachmentQueryRequest;
import com.cainiao.waybill.bridge.biz.charity.response.*;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityAttachmentService;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.web.charity.CharityBaseResult;
import com.cainiao.waybill.bridge.web.charity.CharityExcelUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 附件资料界面管理
 * <AUTHOR>
 */
@Api(tags = "附件资料界面管理")
@RestController
@RequestMapping(value = "/charity/attachment")
public class WaybillCharityAttachmentController extends BaseController{

    @Autowired
    private WaybillCharityAttachmentService waybillCharityAttachmentService;

    /**
     * 附件资料列表查询
     * 支持多条件
     */
    @ResponseBody
    @RequestMapping("/list")
    public CharityBaseResult<CharityPagingResponse<CharityAttachmentQueryResponse>> list(@RequestBody CharityAttachmentQueryRequest attachmentQueryRequest){
        BridgeValidator.validate(attachmentQueryRequest);
        return CharityBaseResult.success(waybillCharityAttachmentService.queryAttachList2(attachmentQueryRequest));
    }


    /**
     * 附件信息列表导出
     */
    @ResponseBody
    @RequestMapping("/export")
    public CharityBaseResult<CharityExcelResponse> export(@RequestBody CharityAttachmentQueryRequest attachmentQueryRequest) {
        BridgeValidator.validate(attachmentQueryRequest);
        String url = CharityExcelUtil.exportFile(attachmentQueryRequest, "附件统计列表", CharityAttachmentQueryResponse.class,
                request -> waybillCharityAttachmentService.queryAttachList2(request));
        return CharityBaseResult.success(new CharityExcelResponse(url));

    }
}
