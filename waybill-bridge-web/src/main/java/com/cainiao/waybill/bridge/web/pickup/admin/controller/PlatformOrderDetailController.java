package com.cainiao.waybill.bridge.web.pickup.admin.controller;

import com.alibaba.buc.sso.client.util.SimpleUserUtil;
import com.alibaba.buc.sso.client.vo.BucSSOUser;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.exception.PickUpBusinessException;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.service.AdminWebCommonReportService;
import com.cainiao.waybill.bridge.biz.pickup.service.AdminWebReportService;
import com.cainiao.waybill.bridge.biz.pickup.service.LinkLogQueryService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb.*;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.common.util.AclPermissionHelper;
import com.cainiao.waybill.bridge.web.pickup.admin.PermissionCheck;
import com.cainiao.waybill.bridge.web.pickup.constant.PickUpWebAdminConstant;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 淘外业务异常问题处理
 *
 * <AUTHOR>
 * @date 2021/11/10-上午11:19
 */
//@RestController
//@RequestMapping("/platform/order/detail")
public class PlatformOrderDetailController {

    @Resource
    private HttpServletRequest httpServletRequest;

    @Resource
    private HttpServletResponse httpServletResponse;

    @Resource
    private WaybillPickUpOrderManager waybillPickUpOrderManager;

    @Resource
    private AdminWebReportService adminWebReportService;

    @Resource
    private LinkLogQueryService linkLogQueryService;

    @Resource
    private AclPermissionHelper aclPermissionHelper;

    @GetMapping("/acl")
    public boolean aclTest() {
        return aclPermissionHelper.judgeOperatorPermission(PickUpWebAdminConstant.Permission.WAYBILL_PICK_UP_DETAIL_ORDER_EXPORT, httpServletRequest);
    }

    //@GetMapping("/virtual/record")
    //public BaseResult<List<VirtualCallRecordResponse>> virtualRecordQuery(String mailNo) {
    //    if (PermissionCheck.checkReportPermission()) {
    //        return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorCode(),
    //            PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorMsg());
    //    }
    //
    //    PickUpLogUtil.info("/platform/order/detail/virtual/record : " + mailNo);
    //    if (StringUtil.isBlank(mailNo)) {
    //        return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
    //                PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
    //    }
    //    try {
    //        List<VirtualCallRecordResponse> virtualRecord = waybillPickUpOrderManager.getVirtualRecord(mailNo);
    //        return BaseResult.success(virtualRecord);
    //    } catch (PickUpBusinessException businessException) {
    //        return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
    //    } catch (Throwable e) {
    //        PickUpLogUtil.errLog("", PickUpWebAdminConstant.LogAction.VIRTUAL_RECORD_QUERY.name(),
    //                PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
    //        return BaseResult.systemFail();
    //    }
    //}

    /**
     * 解决客户要求取消的那些已经揽收的订单。
     * <p>
     * case1：CP揽收后回传的物流节点没有重量，在菜鸟系统与客户系统还是接单状态，此时客户自主取消会提示"已揽收，无法取消"；
     * 此时也分为几种情况，一是客户就自己找网点拿回物品不寄了，但后续CP可能又把带重量的揽收节点传回来了(1030那次节点延时case)，那菜鸟系统就会对该单计费。
     * 二是客户线下去网点自己付费给网点寄出，即平台看来这单应该是取消的。但网点可能用到还是之前的运单号，物流详情继续回传，那么后续该订单在菜鸟看来就是正常的订单，
     * 会进行计费。
     *
     * @param mailNo     :
     * @param cancelType : 取消类型
     * @param reason     :取消原因描述
     * @return ：
     */
    @GetMapping("/exception/cancel")
    public BaseResult<Void> cancel(String mailNo, String cancelType, String reason) {

        if (PermissionCheck.checkCustomerServicePermission()) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorCode(),
                PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorMsg());
        }
        PickUpLogUtil.info("/platform/order/detail/exception/cancel : mailNo=" + mailNo + ", cancelType=" + cancelType + ", reason=" + reason);
        if (StringUtil.isBlank(mailNo) || StringUtil.isBlank(cancelType) || StringUtil.isBlank(reason)) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
        }
        try {
            waybillPickUpOrderManager.operatorCancel(mailNo, cancelType, reason, PermissionCheck.getUserPhone());
            return BaseResult.success();
        } catch (PickUpBusinessException businessException) {
            return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
        } catch (Throwable e) {
            PickUpLogUtil.errLog(mailNo, PickUpWebAdminConstant.LogAction.PLATFORM_DETAIL_EXCEPTION_CANCEL.name(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
            return BaseResult.systemFail();
        }
    }

    @GetMapping("/exception/cancel/type")
    public BaseResult<List<ItemMap>> getCancelTypes() {
        return BaseResult.success(PickUpCommonUtil.parseAdminWebMapItem(PickUpConstants.AdminWeb.MapInfoKey.CANCEL_REASON.name()));
    }

    @GetMapping("/exception/weight")
    public BaseResult<Void> rePushWeight(String mailNo, String weight, String reason) {

        if (PermissionCheck.checkCustomerServicePermission()) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorCode(),
                PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorMsg());
        }
        PickUpLogUtil.info("/platform/order/detail/exception/weight : mailNo=" + mailNo + ", weight=" + weight + ", reason=" + reason);
        if (StringUtil.isBlank(mailNo) || StringUtil.isBlank(reason) || StringUtil.isBlank(weight)) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
        }
        try {
            waybillPickUpOrderManager.operatorRePushWeight(mailNo, weight, reason, PermissionCheck.getUserPhone());
            return BaseResult.success();
        } catch (PickUpBusinessException businessException) {
            return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
        } catch (Throwable e) {
            PickUpLogUtil.errLog(mailNo, PickUpWebAdminConstant.LogAction.PLATFORM_DETAIL_EXCEPTION_RE_PUSH_WEIGHT.name(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
            return BaseResult.systemFail();
        }
    }

    @GetMapping("/ld")
    public BaseResult<LdResponse> ldQuery(String mailNo) {
        PickUpLogUtil.info("/platform/order/detail/ld : mailNo=" + mailNo);
        if (StringUtil.isBlank(mailNo)) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
        }
        try {
            LdResponse ldResponse = linkLogQueryService.getPickUpLd(mailNo);
            return BaseResult.success(ldResponse);
        } catch (PickUpBusinessException businessException) {
            return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
        } catch (Throwable e) {
            PickUpLogUtil.errLog(mailNo, PickUpWebAdminConstant.LogAction.PLATFORM_DETAIL_LD.name(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
            return BaseResult.systemFail();
        }
    }

    @PostMapping("/query")
    public BaseResult<OrderDetailResponse> detailQuery(@RequestBody OrderDetailRequest detailRequest) {
        PickUpLogUtil.info("/platform/order/detail/query : detailRequest=" + JSON.toJSONString(detailRequest));

        if (PermissionCheck.checkCustomerServicePermission()) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorCode(),
                PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorMsg());
        }

        if (StringUtil.isBlank(detailRequest.getOuterOrderCode()) && StringUtil.isBlank(detailRequest.getMailNo())) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
        }
        try {
            OrderDetailResponse response = adminWebReportService.orderDetailQueryByNo(detailRequest);
            return BaseResult.success(response);
        } catch (PickUpBusinessException businessException) {
            return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
        } catch (Throwable e) {
            PickUpLogUtil.errLog(detailRequest.getMailNo(), PickUpWebAdminConstant.LogAction.PLATFORM_DETAIL_QUERY_WITH_NO.name(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
            return BaseResult.systemFail();
        }
    }

    @GetMapping("/export")
    public BaseResult<Void> detailExport(OrderDetailRequest detailRequest) {
        //  联调阶段不进行权限拦截
        if (PermissionCheck.checkCustomerServicePermission()) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorCode(),
                PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorMsg());
        }
        if (StringUtil.isBlank(detailRequest.getType())) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
        }
        try {
            AdminWebCommonReportService.convertDateStr2Date(detailRequest);
            PickUpConstants.AdminWeb.ReportEnum reportEnum = PickUpConstants.AdminWeb.ReportEnum.PLATFORM;
            if (detailRequest.getReportSide() == 1) {
                reportEnum = PickUpConstants.AdminWeb.ReportEnum.CP;
            }
            List<ReportDetailRow> exportItemList = adminWebReportService.exportOrderDetailQuery(detailRequest, reportEnum);
            //  构建、输出Excel
            HSSFWorkbook workbook = AdminWebCommonReportService.buildExcel(exportItemList);
            httpServletResponse.setHeader("Content-disposition", "attachment; filename=" + "detailExport.xls");
            httpServletResponse.setContentType("application/msexcel");
            try (ServletOutputStream output = httpServletResponse.getOutputStream()) {
                workbook.write(output);
                output.flush();
            } catch (IOException e) {
                PickUpLogUtil.info("输出/下载 寄件订单详情异常:" + ExceptionUtils.getStackTrace(e));
                return BaseResult.bizFail("IO EXCEPTION", "IO流输出EXCEL失败");
            }
            return BaseResult.success();
        } catch (PickUpBusinessException businessException) {
            return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpWebAdminConstant.LogAction.PLATFORM_DETAIL_EXPORT.name(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
            return BaseResult.systemFail();
        }
    }

    @PostMapping("/pro")
    public BaseResult<ProvinceReportResponse> provinceCount(@RequestBody OrderDetailRequest platformOrderDetailRequest) {
        if (PermissionCheck.checkReportPermission()) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorCode(),
                PickUpWebAdminConstant.Error.WEB_ADMIN_NO_EXPORT_PERMISSION.getErrorMsg());
        }

        PickUpLogUtil.info("/platform/order/detail/pro : " + JSON.toJSONString(platformOrderDetailRequest));
        if (platformOrderDetailRequest == null || StringUtil.isBlank(platformOrderDetailRequest.getPlatformName())) {
            return BaseResult.bizFail(PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorCode(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_PARAM_EMPTY.getErrorMsg());
        }
        try {
            AdminWebCommonReportService.convertDateStr2Date(platformOrderDetailRequest);
            ProvinceReportResponse provinceReportResponse = adminWebReportService.platformProStatistics(platformOrderDetailRequest);
            return BaseResult.success(provinceReportResponse);
        } catch (PickUpBusinessException businessException) {
            return BaseResult.bizFail(businessException.getErrorCode(), businessException.getErrorMessage());
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpWebAdminConstant.LogAction.PLATFORM_ORDER_REPORT_PRO.name(),
                    PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorCode(), PickUpWebAdminConstant.Error.WEB_ADMIN_SYSTEM_ERROR.getErrorMsg(), e);
            return BaseResult.systemFail();
        }
    }

}
