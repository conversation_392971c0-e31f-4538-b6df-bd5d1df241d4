package com.cainiao.waybill.bridge.biz.link.provider;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import com.cainiao.link.annotation.HsfServer;
import com.cainiao.link.annotation.LinkProvider;
import com.cainiao.link.provider.LinkReceiver;
import com.cainiao.link.provider.ReceiverContext;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.AlipayIotKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.CommonConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpDetailBizTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpCreateOrderRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpCreateOrderResponse;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillKeyLogService;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillPickUpOrderService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpOrderCommonUtil;
import com.cainiao.waybill.bridge.biz.wrapper.ContentRiskWrapper;
import com.cainiao.waybill.bridge.common.base.ScenarioConstant;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.common.util.LoggerMonitorUtil;
import com.cainiao.waybill.bridge.model.domain.WaybillKeyLogDO;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import com.cainiao.waybill.common.util.FeatureUtils;
import com.taobao.eagleeye.EagleEye;
import com.taobao.pac.client.sdk.dataobject.request.TMS_CREATE_ORDER_ONLINE_NOTIFY.ItemInfo;
import com.taobao.pac.client.sdk.dataobject.request.TMS_CREATE_ORDER_ONLINE_NOTIFY.OrderExtendField;
import com.taobao.pac.client.sdk.dataobject.request.TMS_CREATE_ORDER_ONLINE_NOTIFY.PackageInfo;
import com.taobao.pac.client.sdk.dataobject.request.TMS_CREATE_ORDER_ONLINE_NOTIFY.Receiver;
import com.taobao.pac.client.sdk.dataobject.request.TMS_CREATE_ORDER_ONLINE_NOTIFY.Sender;
import com.taobao.pac.client.sdk.dataobject.request.TMS_CREATE_ORDER_ONLINE_NOTIFY.TmsCreateOrderOnlineNotifyRequest;
import com.taobao.pac.client.sdk.dataobject.response.TMS_CREATE_ORDER_ONLINE_NOTIFY.TmsCreateOrderOnlineNotifyResponse;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PICK_UP_CREATE.WaybillPickUpCreateResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * TMS配送单下发接口
 * 支付宝逆向IOT下单
 * 接入文档：https://alidocs.dingtalk.com/i/nodes/mExel2BLV59rgdDPi6qjp7DaVgk9rpMq
 * <AUTHOR>
 * @date 2025/1/7 10:23
 **/
@HsfServer
@LinkProvider
public class TmsCreateOrderOnlineNotifyProvider implements
    LinkReceiver<TmsCreateOrderOnlineNotifyRequest, TmsCreateOrderOnlineNotifyResponse> {


    @Resource
    private ContentRiskWrapper contentRiskWrapper;

    @Resource
    private WaybillPickUpOrderService waybillPickUpOrderService;

    @Resource
    private WaybillKeyLogService waybillKeyLogService;

    @Override
    public TmsCreateOrderOnlineNotifyResponse receive(ReceiverContext receiverContext,
        TmsCreateOrderOnlineNotifyRequest tmsCreateOrderOnlineNotifyRequest) {

        return dealIotOrderCreateRequest(tmsCreateOrderOnlineNotifyRequest);
    }

    /**
     * 处理iot寄件下单
     * @param tmsCreateOrderOnlineNotifyRequest
     * @return
     */
    private TmsCreateOrderOnlineNotifyResponse dealIotOrderCreateRequest(TmsCreateOrderOnlineNotifyRequest tmsCreateOrderOnlineNotifyRequest) {
        TmsCreateOrderOnlineNotifyResponse response = new TmsCreateOrderOnlineNotifyResponse();
        PickUpLogUtil.info("支付宝逆向寄件下单接口入参.tmsCreateOrderOnlineNotifyRequest:{}",
            JSONObject.toJSONString(tmsCreateOrderOnlineNotifyRequest));
        LoggerMonitorUtil.start(ScenarioConstant.TMS_WAYBILL_LINK_CREATE_ORDER, null);

        // 客户服务可用性检测
        if(PickUpOrderCommonUtil.checkCustomerCurrForbid(AlipayIotKey.IOT_APP_KEY, AlipayIotKey.IOT_ORDER_CHANNELS)){
            response.setSuccess(false);
            response.setErrorCode(PickUpConstants.Error.ORDER_FORBID_ERROR.getErrorCode());
            response.setErrorMsg(PickUpConstants.Error.ORDER_FORBID_ERROR.getErrorMsg());
            return response;
        }
        PickUpCreateOrderRequest orderRequest = new PickUpCreateOrderRequest();
        Throwable defaultException = null;
        try{

            // 校验参数
            validateCreateParams(tmsCreateOrderOnlineNotifyRequest);

            // 构建上下文
            buildContext(orderRequest);

            // 处理基础订单信息
            buildBaseOrderInfo(tmsCreateOrderOnlineNotifyRequest, orderRequest);

            // 处理物品信息
            buildGoodsInfo(tmsCreateOrderOnlineNotifyRequest, orderRequest);

            // 处理收寄件人信息
            buildSenderReceiverInfo(tmsCreateOrderOnlineNotifyRequest, orderRequest);

            // 获取缓存失败单，短时间内同一用户同一地址重复下单时直接返回失败信息
            WaybillPickUpCreateResponse cacheResponse = waybillPickUpOrderService.getCacheFailOrder(orderRequest);
            if(null != cacheResponse){
                response.setSuccess(cacheResponse.isSuccess());
                response.setErrorCode(cacheResponse.getErrorCode());
                response.setErrorMsg(cacheResponse.getErrorMsg());
                return response;
            }

            // 风控-制裁
            contentRiskWrapper.checkRisk(orderRequest);

            // 风控-违禁词
            contentRiskWrapper.checkRiskWord(orderRequest);

            // 下单
            PickUpCreateOrderResponse createOrderResponse = waybillPickUpOrderService.create(orderRequest);
            response.setSuccess(true);
            response.setAsyncMessageId(createOrderResponse.getMailNo());
            PickUpLogUtil.info("支付宝逆向寄件下单接口出参.response:{}", JSONObject.toJSONString(createOrderResponse));
        }catch (BridgeBaseException e){
            response.setSuccess(false);
            response.setErrorCode(e.getErrorCode());
            response.setErrorMsg(e.getErrorMessage());
            defaultException = e;
            PickUpLogUtil.errLog(tmsCreateOrderOnlineNotifyRequest.getLogisticsId(), PickUpConstants.Action.IOT_CREATE_ORDER_EXCEPTION.name(), e.getErrorCode(), e.getErrorMessage(), ExceptionUtils.getFullStackTrace(e));
        }catch (Throwable e) {
            response.setSuccess(false);
            response.setErrorCode(Error.SYSTEM_ERROR.getErrorCode());
            response.setErrorMsg(e.getMessage());
            defaultException = e;
            PickUpLogUtil.errLog(tmsCreateOrderOnlineNotifyRequest.getLogisticsId(), PickUpConstants.Action.IOT_CREATE_ORDER_EXCEPTION.name(), "IOT_CREATE_ORDER_EXCEPTION", e.getMessage(), ExceptionUtils.getFullStackTrace(e));
        }finally {
            LoggerMonitorUtil.end(response.isSuccess(), response.getErrorCode());
            if(!response.isSuccess()){
                orderRequest.setBizRoutingContext(null);
                PickUpLogUtil.errLog(orderRequest.getOuterOrderCode(), PickUpConstants.Action.PICK_UP_CREATE_LINK.name()+"|"+AlipayIotKey.IOT_ORDER_CHANNELS, response.getErrorCode(), response.getErrorMsg(), orderRequest, defaultException);
                // 缓存失败单，短时间内同一用户该地址重复下单时直接返回失败信息
                WaybillPickUpCreateResponse linkResponse = new WaybillPickUpCreateResponse();
                linkResponse.setSuccess(response.isSuccess());
                linkResponse.setErrorCode(response.getErrorCode());
                linkResponse.setErrorMsg(response.getErrorMsg());
                // 获取缓存失败单，短时间内同一用户同一地址重复下单时直接返回失败信息
                waybillPickUpOrderService.cacheFailOrder(orderRequest, linkResponse);
                // 记录关键日志 补充监控平台统计周期不足够长
                WaybillKeyLogDO waybillKeyLogDO = new WaybillKeyLogDO();
                waybillKeyLogDO.setOrderChannels(AlipayIotKey.IOT_ORDER_CHANNELS);
                waybillKeyLogDO.setTraceId(EagleEye.getTraceId());
                waybillKeyLogDO.setBizId(orderRequest.getOuterOrderCode());
                waybillKeyLogDO.setAction(PickUpConstants.Action.PICK_UP_CREATE_LINK.name());
                waybillKeyLogDO.setLogCode(response.getErrorCode());
                waybillKeyLogDO.setLogLevel(PickUpConstants.LogLevel.ERROR);
                waybillKeyLogDO.setLogMsg(response.getErrorMsg());
                waybillKeyLogDO.setBusinessData(JSONObject.toJSONString(orderRequest));
                waybillKeyLogDO.setException(ExceptionUtils.getStackTrace(defaultException));
                waybillKeyLogDO.setRemark("下单失败日志");
                waybillKeyLogService.saveLog(waybillKeyLogDO);
            }
        }
        return response;
    }

    /**
     * 构建上下文
     * @param orderRequest
     */
    private void buildContext(PickUpCreateOrderRequest orderRequest) {
        orderRequest.setFromAppKey(AlipayIotKey.IOT_APP_KEY);

        String resCode = PickUpCommonUtil.buildResCode(AlipayIotKey.IOT_APP_KEY, AlipayIotKey.IOT_ORDER_CHANNELS);
        orderRequest.setResCode(resCode);
        orderRequest.setOrderChannels(AlipayIotKey.IOT_ORDER_CHANNELS);

        Map<String, String> featureMap = new HashMap<>(16);
        featureMap.put(PickUpConstants.TraceFeatureKey.LINK_CP_CODE, orderRequest.getFromAppKey());
        orderRequest.setFeatureMap(featureMap);
    }

    /**
     * 基础订单信息处理
     * @param tmsCreateOrderOnlineNotifyRequest
     * @param orderRequest
     */
    private void buildBaseOrderInfo(TmsCreateOrderOnlineNotifyRequest tmsCreateOrderOnlineNotifyRequest, PickUpCreateOrderRequest orderRequest)
        throws BridgeBaseException {
        Map<String, String> featureMap = orderRequest.getFeatureMap();
        orderRequest.setOuterOrderCode(tmsCreateOrderOnlineNotifyRequest.getLogisticsId());
        orderRequest.setRemark(tmsCreateOrderOnlineNotifyRequest.getRemark());
        // 默认实时单
        orderRequest.setBizType(PickUpDetailBizTypeEnum.NORMAL.getValue());
        //List<OrderExtendField>
        //    orderExtfieldList = tmsCreateOrderOnlineNotifyRequest.getOrderExtendFields();
        //if(CollectionUtils.isEmpty(orderExtfieldList)){
        //    return;
        //}
        Map<String,String> extendMap = tmsCreateOrderOnlineNotifyRequest.getExtendFields();
        //for(OrderExtendField orderExtendField: orderExtfieldList){
        //    if(StringUtils.isNoneBlank(orderExtendField.getKey(), orderExtendField.getValue())){
        //        extendMap.put(orderExtendField.getKey(), orderExtendField.getValue());
        //    }
        //}
        orderRequest.setExtendInfo(FeatureUtils.parseFromMap(extendMap));
        if(MapUtils.isEmpty(extendMap)){
            return;
        }
        // 上门退业务类型
        if(StringUtils.isNotBlank(extendMap.get(AlipayIotKey.IOT_SUB_ORDER_TYPE_KEY))){
            featureMap.put(AlipayIotKey.IOT_PRE_SUB_ORDER_TYPE_KEY, extendMap.get(AlipayIotKey.IOT_SUB_ORDER_TYPE_KEY));
        }
        orderRequest.setFeatureMap(featureMap);
        // 预约单
        if(StringUtils.isNotBlank(extendMap.get(AlipayIotKey.IOT_EXT_BIZ_TYPE_KEY))){
            String iotBizType = extendMap.get(AlipayIotKey.IOT_EXT_BIZ_TYPE_KEY);
            try{
                Integer.parseInt(iotBizType);
            }catch (Exception e){
                throw new BridgeBaseException(PickUpConstants.Error.IOT_EXT_BIZ_TYPE_ERROR.getErrorCode(), PickUpConstants.Error.IOT_EXT_BIZ_TYPE_ERROR.getErrorMsg());
            }
            orderRequest.setBizType(Integer.parseInt(iotBizType));
            if(StringUtils.equals(iotBizType, PickUpDetailBizTypeEnum.APPOINT.getValue()+"")){
                if(StringUtils.isAnyBlank(extendMap.get(AlipayIotKey.IOT_EXT_APPO_GOT_START_TIME_KEY), extendMap.get(AlipayIotKey.IOT_EXT_APPO_GOT_END_TIME_KEY))){
                    throw new BridgeBaseException(PickUpConstants.Error.IOT_EXT_APPO_TIME_EMPTY_ERROR.getErrorCode(), PickUpConstants.Error.IOT_EXT_APPO_TIME_EMPTY_ERROR.getErrorMsg());
                }
                try{
                    Date startDate = DateUtils.strToDate(
                        extendMap.get(AlipayIotKey.IOT_EXT_APPO_GOT_START_TIME_KEY), DateUtils.defaultPattern);
                    Date endDate = DateUtils.strToDate(
                        extendMap.get(AlipayIotKey.IOT_EXT_APPO_GOT_END_TIME_KEY), DateUtils.defaultPattern);
                    orderRequest.setAppointGotStartTime(startDate);
                    orderRequest.setAppointGotEndTime(endDate);
                }catch (Exception e){
                    throw new BridgeBaseException(PickUpConstants.Error.IOT_EXT_APPO_TIME_PARSE_ERROR.getErrorCode(), PickUpConstants.Error.IOT_EXT_APPO_TIME_PARSE_ERROR.getErrorMsg());
                }
            }
        }

    }

    /**
     * 收寄件人信息处理
     * @param tmsCreateOrderOnlineNotifyRequest
     * @param orderRequest
     */
    private void buildSenderReceiverInfo(TmsCreateOrderOnlineNotifyRequest tmsCreateOrderOnlineNotifyRequest, PickUpCreateOrderRequest orderRequest) {
        Sender sender = tmsCreateOrderOnlineNotifyRequest.getSender();
        AddressDTO senderAddress = new AddressDTO();
        senderAddress.setProvinceName(sender.getProvince());
        senderAddress.setCityName(sender.getCity());
        senderAddress.setAreaName(sender.getCounty());
        senderAddress.setTownName(sender.getSenderTown());
        senderAddress.setZip(sender.getZipCode());
        senderAddress.setAddressDetail(sender.getAddress());
        orderRequest.setSendName(sender.getName());
        orderRequest.setSendPhone(sender.getMobile());
        orderRequest.setSendMobile(sender.getMobile());
        orderRequest.setSendAddress(senderAddress);

        Receiver receiver = tmsCreateOrderOnlineNotifyRequest.getReceiver();
        AddressDTO receiverAddress = new AddressDTO();
        receiverAddress.setProvinceName(receiver.getProvince());
        receiverAddress.setCityName(receiver.getCity());
        receiverAddress.setAreaName(receiver.getCounty());
        receiverAddress.setTownName(receiver.getReceiverTown());
        receiverAddress.setZip(receiver.getZipCode());
        receiverAddress.setAddressDetail(receiver.getAddress());

        orderRequest.setConsigneeName(receiver.getName());
        orderRequest.setConsigneePhone(receiver.getPhone());
        orderRequest.setConsigneeMobile(receiver.getMobile());
        orderRequest.setConsigneeAddress(receiverAddress);


    }

    /**
     * 校验创建参数
     * @param request
     * @throws BridgeBaseException
     */
    private void validateCreateParams(TmsCreateOrderOnlineNotifyRequest request) throws BridgeBaseException {

        if(null == request || StringUtils.isAnyBlank(request.getLogisticsId(), request.getCpCode(), request.getOrderBizType())){
            throw new BridgeBaseException(PickUpConstants.Error.SYSTEM_PARAM_ERROR.getErrorCode(), PickUpConstants.Error.SYSTEM_PARAM_ERROR.getErrorMsg());
        }

        if(!StringUtils.equals(request.getServiceFlag(), AlipayIotKey.IOT_BIZ_TYPE)){
            throw new BridgeBaseException(PickUpConstants.Error.IOT_NOT_NX_SERVICE_ERROR.getErrorCode(), PickUpConstants.Error.IOT_NOT_NX_SERVICE_ERROR.getErrorMsg());
        }

        PackageInfo packageInfo = request.getPackageInfo();

        if(null == packageInfo){
            throw new BridgeBaseException(PickUpConstants.Error.IOT_PACKAGE_INFO_NULL_ERROR.getErrorCode(), PickUpConstants.Error.IOT_PACKAGE_INFO_NULL_ERROR.getErrorMsg());
        }

        Sender sender = request.getSender();
        if(null == sender || StringUtils.isAnyBlank(sender.getProvince(), sender.getAddress(), sender.getName()) || StringUtils.isAllBlank(sender.getMobile(), sender.getPhone())){
            throw new BridgeBaseException(PickUpConstants.Error.IOT_SENDER_INFO_NULL_ERROR.getErrorCode(), PickUpConstants.Error.IOT_SENDER_INFO_NULL_ERROR.getErrorMsg());
        }

        Receiver receiver = request.getReceiver();
        if(null == receiver || StringUtils.isAnyBlank(receiver.getProvince(), receiver.getAddress(), receiver.getName()) || StringUtils.isAllBlank(receiver.getMobile(), receiver.getPhone())){
            throw new BridgeBaseException(PickUpConstants.Error.IOT_RECEIVER_INFO_NULL_ERROR.getErrorCode(), PickUpConstants.Error.IOT_RECEIVER_INFO_NULL_ERROR.getErrorMsg());
        }

    }

    /**
     * 物品信息处理
     * @param linkRequest
     * @param orderRequest
     */
    private void buildGoodsInfo(TmsCreateOrderOnlineNotifyRequest linkRequest, PickUpCreateOrderRequest orderRequest) {
        PackageInfo packageInfo = linkRequest.getPackageInfo();
        List<ItemInfo> itemList = packageInfo.getItemList();
        List<com.cainiao.waybill.bridge.biz.pickup.dto.GoodsInfo> goodsInfoList = new ArrayList<>();

        Map<String, String> featureMap = orderRequest.getFeatureMap();
        for(ItemInfo itemInfo: itemList){
            Map<String, String> goodsExtFieldMap = new HashMap<>(8);
            com.cainiao.waybill.bridge.biz.pickup.dto.GoodsInfo goodsInfo = new com.cainiao.waybill.bridge.biz.pickup.dto.GoodsInfo();
            if(StringUtils.isBlank(itemInfo.getItemName())){
                goodsInfo.setName("设备");
            }else{
                goodsInfo.setName(itemInfo.getItemName());
            }
            goodsExtFieldMap.put(AlipayIotKey.IOT_PRE_CAT_NAME_KEY, goodsInfo.getName());
            if(StringUtils.isBlank(packageInfo.getPackageWeight())){
                goodsInfo.setWeight(1000);
            }else {
                goodsInfo.setWeight(Integer.parseInt(packageInfo.getPackageWeight()));
            }
            if(CollectionUtils.isEmpty(packageInfo.getItemList())){
                goodsInfo.setQuantity(1);
            }else {
                goodsInfo.setQuantity(packageInfo.getItemList().size());
            }
            // 设备扩展信息
            String extFields = itemInfo.getExtendFields();
            Map<String, String> extFieldMap = FeatureUtils.parseFromString(extFields);
            if(StringUtils.isNotBlank(extFieldMap.get(AlipayIotKey.IOT_SN_LIST_KEY))){
                String snListStr = extFieldMap.get(AlipayIotKey.IOT_SN_LIST_KEY);
                String snList = getSnList(snListStr);
                goodsExtFieldMap.put(AlipayIotKey.IOT_PRE_SN_LIST_KEY, snList);
            }
            if(StringUtils.isNotBlank(extFieldMap.get(AlipayIotKey.IOT_IS_SN_MGT_KEY))){
                goodsExtFieldMap.put(AlipayIotKey.IOT_PRE_IS_SN_MGT_KEY, extFieldMap.get(AlipayIotKey.IOT_IS_SN_MGT_KEY));
            }
            if(StringUtils.isNotBlank(extFieldMap.get(AlipayIotKey.IOT_IS_SN_MGT_KEY))){
                goodsExtFieldMap.put(AlipayIotKey.IOT_PRE_CAT_NAME_KEY, extFieldMap.get(AlipayIotKey.IOT_CAT_NAME_KEY));
            }

            if(null != itemInfo.getItemNum()){
                goodsExtFieldMap.put(AlipayIotKey.IOT_PRE_ITEM_NUM_KEY, itemInfo.getItemNum().toString());
            }else{
                goodsExtFieldMap.put(AlipayIotKey.IOT_PRE_ITEM_NUM_KEY, "1");
            }
            if(StringUtils.isNotBlank(extFieldMap.get(AlipayIotKey.IOT_INSURED_PRICE_KEY))){
                // 单位 厘
                goodsExtFieldMap.put(AlipayIotKey.IOT_PRE_INSURED_PRICE_KEY, extFieldMap.get(AlipayIotKey.IOT_INSURED_PRICE_KEY));
            }
            if(StringUtils.isNotBlank(itemInfo.getItemName())){
                goodsExtFieldMap.put(AlipayIotKey.IOT_PRE_ITEM_NAME_KEY, extFieldMap.get(AlipayIotKey.IOT_ITEM_NAME_KEY));
            }
            goodsInfo.setFeatureMap(goodsExtFieldMap);
            goodsInfoList.add(goodsInfo);
        }
        // 上门取退的业务类型
        List<OrderExtendField> orderExtendFields = linkRequest.getOrderExtendFields();
        if(!CollectionUtils.isEmpty(orderExtendFields)){
            for(OrderExtendField orderExtendField: orderExtendFields){
                if(StringUtils.isNotBlank(orderExtendField.getValue())){
                    featureMap.put(AlipayIotKey.IOT_PREFIX + orderExtendField.getKey(), orderExtendField.getValue());
                }
            }
        }
        orderRequest.setFeatureMap(featureMap);
        orderRequest.setGoodsInfos(goodsInfoList);
    }

    /**
     * 获取snList
     * @param snListStr
     * @return
     */
    private String getSnList(String snListStr) {
        try{
            List<String> list = JSONObject.parseObject(snListStr, ArrayList.class);
            if(!CollectionUtils.isEmpty(list)){
                return StringUtils.join(list, "；");
            }
        }catch (Exception e){
            // ignore
        }
        return snListStr;
    }


}
