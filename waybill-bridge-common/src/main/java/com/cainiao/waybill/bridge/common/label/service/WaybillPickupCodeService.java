package com.cainiao.waybill.bridge.common.label.service;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.label.dto.WaybillPickupCodeDTO;
import com.cainiao.waybill.bridge.common.label.dto.request.UpdateWeightRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeApplyNewRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeQueryRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeUpdateRequest;
import com.cainiao.waybill.bridge.common.label.dto.response.WaybillPickupCodeApplyResponse;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.util.Page;

/**
 * 揽件码面单寄件服务。
 *
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/4/25.
 */
public interface WaybillPickupCodeService {

    ///**
    // * 申请带"揽件码"的寄件电子面单
    // *
    // * @param request 申请面单参数
    // * @param clientInfoDTO
    // * @param courierId 小件员id
    // * @return
    // */
    //BaseResultDTO<WaybillPickupCodeApplyResponse> applyNew(WaybillPickupCodeApplyNewRequest request, Long courierId, ClientInfoDTO clientInfoDTO);

    /**
     * 查询"揽件码"电子面单列表，支持分页
     *
     * @param request 查询面单参数
     * @param clientInfoDTO
     * @param courierId 小件员id
     * @return
     */
    BaseResultDTO<Page<WaybillPickupCodeDTO>> query(WaybillPickupCodeQueryRequest request, Long courierId, ClientInfoDTO clientInfoDTO);

    /**
     * 修改"揽件码"电子面单的打印状态接口
     *
     * @param request 修改面单参数
     * @param clientInfoDTO
     * @param courierId 小件员id
     * @return
     */
    BaseResultDTO<Void> updatePrintStatus(WaybillPickupCodeUpdateRequest request, Long courierId, ClientInfoDTO clientInfoDTO);

    /**
     * 更新包裹重量接口
     * @param request
     * @param courierId
     * @param clientInfoDTO
     * @return
     */
    BaseResultDTO<Void> updateWeight(UpdateWeightRequest request, Long courierId, ClientInfoDTO clientInfoDTO);
}
