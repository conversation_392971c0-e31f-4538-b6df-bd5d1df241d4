package com.cainiao.waybill.bridge.innersupport.ad.model.mapper;

import com.cainiao.waybill.bridge.innersupport.ad.model.domain.WaybillAdWorkOrderDO;
import com.cainiao.waybill.bridge.innersupport.ad.model.domain.WaybillAdWorkOrderParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * WaybillAdWorkOrderMapper
 */

@Mapper
public interface WaybillAdWorkOrderMapper {
    /**
     * 根据WHERE条件COUNT
     * @param waybillAdWorkOrderParam
     */
    long countByParam(@Param("waybillAdWorkOrder") WaybillAdWorkOrderParam waybillAdWorkOrderParam);

    /**
     * 插入单条记录
     * @return
     */
    int insert(@Param("waybillAdWorkOrder") WaybillAdWorkOrderDO waybillAdWorkOrderDO);

    /**
     * 根据WHERE条件查询
     */
    List<WaybillAdWorkOrderDO> selectByParam(@Param("waybillAdWorkOrder") WaybillAdWorkOrderParam waybillAdWorkOrderParam);


    /**
     * 更新
     * @param waybillAdWorkOrderDO
     * @return
     */
    int updateByPrimaryKeySelective(@Param("waybillAdWorkOrder") WaybillAdWorkOrderDO waybillAdWorkOrderDO);


    /**
     * 删除
     * @param id
     * @return
     */
    int deleteByPrimaryKey(@Param("id") Long id);


    WaybillAdWorkOrderDO selectByPrimaryKey(@Param("id") Long id);


    /**
     * @param waybillAdWorkOrder
     * @return
     */
    int insertSelective(@Param("waybillAdWorkOrder") WaybillAdWorkOrderDO waybillAdWorkOrder);


    /**
     * update
     * @param waybillAdWorkOrder
     * @return
     */
    int updateByPrimaryKey(@Param("waybillAdWorkOrder") WaybillAdWorkOrderDO waybillAdWorkOrder);
}
