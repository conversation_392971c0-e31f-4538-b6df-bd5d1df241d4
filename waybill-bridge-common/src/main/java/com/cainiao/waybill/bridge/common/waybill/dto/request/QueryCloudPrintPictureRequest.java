package com.cainiao.waybill.bridge.common.waybill.dto.request;

/**
 *
 * 获取电子面单云打印图片数据request
 *
 * Created by shouyuan.lzl on 2017-04-27 9:17 PM.
 */
public class QueryCloudPrintPictureRequest {

    /**
     * 对应面单的商家id，必填
     */
    private long waybillSellerId ;

    /**
     * 运单号，必填
     */
    private String waybillCode ;

    /**
     * cp code，必填
     */
    private String cpCode ;

    public long getWaybillSellerId() {
        return waybillSellerId;
    }

    public void setWaybillSellerId(long waybillSellerId) {
        this.waybillSellerId = waybillSellerId;
    }

    public String getWaybillCode() {
        return waybillCode;
    }

    public void setWaybillCode(String waybillCode) {
        this.waybillCode = waybillCode;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }
}
