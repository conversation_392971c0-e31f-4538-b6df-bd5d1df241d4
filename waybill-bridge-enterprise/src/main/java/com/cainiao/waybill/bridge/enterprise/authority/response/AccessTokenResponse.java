package com.cainiao.waybill.bridge.enterprise.authority.response;

import lombok.Data;

import java.io.Serializable;

/**
 * 该类用于处理访问令牌的响应结果。
 * 1.该类实现了`Serializable`接口，支持序列化操作。
 * <AUTHOR>
 * @date 2025-04-15 11:01:40
 */
@Data
public class AccessTokenResponse implements Serializable {
    private static final long serialVersionUID = 2655673686738135763L;

    /**
     * 权限code
     */
    private String accessToken;
}
