package com.cainiao.waybill.bridge.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class BillAdjustFileInfoParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public BillAdjustFileInfoParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public BillAdjustFileInfoParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public BillAdjustFileInfoParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public BillAdjustFileInfoParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public BillAdjustFileInfoParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public BillAdjustFileInfoParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andBillIdIsNull() {
            addCriterion("bill_id is null");
            return (Criteria) this;
        }

        public Criteria andBillIdIsNotNull() {
            addCriterion("bill_id is not null");
            return (Criteria) this;
        }

        public Criteria andBillIdEqualTo(String value) {
            addCriterion("bill_id =", value, "billId");
            return (Criteria) this;
        }

        public Criteria andBillIdNotEqualTo(String value) {
            addCriterion("bill_id <>", value, "billId");
            return (Criteria) this;
        }

        public Criteria andBillIdGreaterThan(String value) {
            addCriterion("bill_id >", value, "billId");
            return (Criteria) this;
        }

        public Criteria andBillIdGreaterThanOrEqualTo(String value) {
            addCriterion("bill_id >=", value, "billId");
            return (Criteria) this;
        }

        public Criteria andBillIdLessThan(String value) {
            addCriterion("bill_id <", value, "billId");
            return (Criteria) this;
        }

        public Criteria andBillIdLessThanOrEqualTo(String value) {
            addCriterion("bill_id <=", value, "billId");
            return (Criteria) this;
        }

        public Criteria andBillIdLike(String value) {
            addCriterion("bill_id like", value, "billId");
            return (Criteria) this;
        }

        public Criteria andBillIdNotLike(String value) {
            addCriterion("bill_id not like", value, "billId");
            return (Criteria) this;
        }

        public Criteria andBillIdIn(List<String> values) {
            addCriterion("bill_id in", values, "billId");
            return (Criteria) this;
        }

        public Criteria andBillIdNotIn(List<String> values) {
            addCriterion("bill_id not in", values, "billId");
            return (Criteria) this;
        }

        public Criteria andBillIdBetween(String value1, String value2) {
            addCriterion("bill_id between", value1, value2, "billId");
            return (Criteria) this;
        }

        public Criteria andBillIdNotBetween(String value1, String value2) {
            addCriterion("bill_id not between", value1, value2, "billId");
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdIsNull() {
            addCriterion("adjust_file_id is null");
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdIsNotNull() {
            addCriterion("adjust_file_id is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdEqualTo(Long value) {
            addCriterion("adjust_file_id =", value, "adjustFileId");
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdNotEqualTo(Long value) {
            addCriterion("adjust_file_id <>", value, "adjustFileId");
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdGreaterThan(Long value) {
            addCriterion("adjust_file_id >", value, "adjustFileId");
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdGreaterThanOrEqualTo(Long value) {
            addCriterion("adjust_file_id >=", value, "adjustFileId");
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdLessThan(Long value) {
            addCriterion("adjust_file_id <", value, "adjustFileId");
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdLessThanOrEqualTo(Long value) {
            addCriterion("adjust_file_id <=", value, "adjustFileId");
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdIn(List<Long> values) {
            addCriterion("adjust_file_id in", values, "adjustFileId");
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdNotIn(List<Long> values) {
            addCriterion("adjust_file_id not in", values, "adjustFileId");
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdBetween(Long value1, Long value2) {
            addCriterion("adjust_file_id between", value1, value2, "adjustFileId");
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdNotBetween(Long value1, Long value2) {
            addCriterion("adjust_file_id not between", value1, value2, "adjustFileId");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeIsNull() {
            addCriterion("adjust_type is null");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeIsNotNull() {
            addCriterion("adjust_type is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeEqualTo(String value) {
            addCriterion("adjust_type =", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeNotEqualTo(String value) {
            addCriterion("adjust_type <>", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeGreaterThan(String value) {
            addCriterion("adjust_type >", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeGreaterThanOrEqualTo(String value) {
            addCriterion("adjust_type >=", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeLessThan(String value) {
            addCriterion("adjust_type <", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeLessThanOrEqualTo(String value) {
            addCriterion("adjust_type <=", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeLike(String value) {
            addCriterion("adjust_type like", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeNotLike(String value) {
            addCriterion("adjust_type not like", value, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeIn(List<String> values) {
            addCriterion("adjust_type in", values, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeNotIn(List<String> values) {
            addCriterion("adjust_type not in", values, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeBetween(String value1, String value2) {
            addCriterion("adjust_type between", value1, value2, "adjustType");
            return (Criteria) this;
        }

        public Criteria andAdjustTypeNotBetween(String value1, String value2) {
            addCriterion("adjust_type not between", value1, value2, "adjustType");
            return (Criteria) this;
        }

        public Criteria andFileNameIsNull() {
            addCriterion("file_name is null");
            return (Criteria) this;
        }

        public Criteria andFileNameIsNotNull() {
            addCriterion("file_name is not null");
            return (Criteria) this;
        }

        public Criteria andFileNameEqualTo(String value) {
            addCriterion("file_name =", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotEqualTo(String value) {
            addCriterion("file_name <>", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameGreaterThan(String value) {
            addCriterion("file_name >", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameGreaterThanOrEqualTo(String value) {
            addCriterion("file_name >=", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLessThan(String value) {
            addCriterion("file_name <", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLessThanOrEqualTo(String value) {
            addCriterion("file_name <=", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameLike(String value) {
            addCriterion("file_name like", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotLike(String value) {
            addCriterion("file_name not like", value, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameIn(List<String> values) {
            addCriterion("file_name in", values, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotIn(List<String> values) {
            addCriterion("file_name not in", values, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameBetween(String value1, String value2) {
            addCriterion("file_name between", value1, value2, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileNameNotBetween(String value1, String value2) {
            addCriterion("file_name not between", value1, value2, "fileName");
            return (Criteria) this;
        }

        public Criteria andFileUrlIsNull() {
            addCriterion("file_url is null");
            return (Criteria) this;
        }

        public Criteria andFileUrlIsNotNull() {
            addCriterion("file_url is not null");
            return (Criteria) this;
        }

        public Criteria andFileUrlEqualTo(String value) {
            addCriterion("file_url =", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlNotEqualTo(String value) {
            addCriterion("file_url <>", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlGreaterThan(String value) {
            addCriterion("file_url >", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlGreaterThanOrEqualTo(String value) {
            addCriterion("file_url >=", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlLessThan(String value) {
            addCriterion("file_url <", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlLessThanOrEqualTo(String value) {
            addCriterion("file_url <=", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlLike(String value) {
            addCriterion("file_url like", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlNotLike(String value) {
            addCriterion("file_url not like", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlIn(List<String> values) {
            addCriterion("file_url in", values, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlNotIn(List<String> values) {
            addCriterion("file_url not in", values, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlBetween(String value1, String value2) {
            addCriterion("file_url between", value1, value2, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlNotBetween(String value1, String value2) {
            addCriterion("file_url not between", value1, value2, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andUploadDateIsNull() {
            addCriterion("upload_date is null");
            return (Criteria) this;
        }

        public Criteria andUploadDateIsNotNull() {
            addCriterion("upload_date is not null");
            return (Criteria) this;
        }

        public Criteria andUploadDateEqualTo(Date value) {
            addCriterion("upload_date =", value, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateNotEqualTo(Date value) {
            addCriterion("upload_date <>", value, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateGreaterThan(Date value) {
            addCriterion("upload_date >", value, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateGreaterThanOrEqualTo(Date value) {
            addCriterion("upload_date >=", value, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateLessThan(Date value) {
            addCriterion("upload_date <", value, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateLessThanOrEqualTo(Date value) {
            addCriterion("upload_date <=", value, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateIn(List<Date> values) {
            addCriterion("upload_date in", values, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateNotIn(List<Date> values) {
            addCriterion("upload_date not in", values, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateBetween(Date value1, Date value2) {
            addCriterion("upload_date between", value1, value2, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadDateNotBetween(Date value1, Date value2) {
            addCriterion("upload_date not between", value1, value2, "uploadDate");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdIsNull() {
            addCriterion("upload_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdIsNotNull() {
            addCriterion("upload_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdEqualTo(Long value) {
            addCriterion("upload_user_id =", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdNotEqualTo(Long value) {
            addCriterion("upload_user_id <>", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdGreaterThan(Long value) {
            addCriterion("upload_user_id >", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("upload_user_id >=", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdLessThan(Long value) {
            addCriterion("upload_user_id <", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdLessThanOrEqualTo(Long value) {
            addCriterion("upload_user_id <=", value, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdIn(List<Long> values) {
            addCriterion("upload_user_id in", values, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdNotIn(List<Long> values) {
            addCriterion("upload_user_id not in", values, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdBetween(Long value1, Long value2) {
            addCriterion("upload_user_id between", value1, value2, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andUploadUserIdNotBetween(Long value1, Long value2) {
            addCriterion("upload_user_id not between", value1, value2, "uploadUserId");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andAdjustAmountIsNull() {
            addCriterion("adjust_amount is null");
            return (Criteria) this;
        }

        public Criteria andAdjustAmountIsNotNull() {
            addCriterion("adjust_amount is not null");
            return (Criteria) this;
        }

        public Criteria andAdjustAmountEqualTo(Long value) {
            addCriterion("adjust_amount =", value, "adjustAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustAmountNotEqualTo(Long value) {
            addCriterion("adjust_amount <>", value, "adjustAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustAmountGreaterThan(Long value) {
            addCriterion("adjust_amount >", value, "adjustAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("adjust_amount >=", value, "adjustAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustAmountLessThan(Long value) {
            addCriterion("adjust_amount <", value, "adjustAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustAmountLessThanOrEqualTo(Long value) {
            addCriterion("adjust_amount <=", value, "adjustAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustAmountIn(List<Long> values) {
            addCriterion("adjust_amount in", values, "adjustAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustAmountNotIn(List<Long> values) {
            addCriterion("adjust_amount not in", values, "adjustAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustAmountBetween(Long value1, Long value2) {
            addCriterion("adjust_amount between", value1, value2, "adjustAmount");
            return (Criteria) this;
        }

        public Criteria andAdjustAmountNotBetween(Long value1, Long value2) {
            addCriterion("adjust_amount not between", value1, value2, "adjustAmount");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andBillIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_id =", value, "billId");
            }
            return (Criteria) this;
        }

        public Criteria andBillIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_id <>", value, "billId");
            }
            return (Criteria) this;
        }

        public Criteria andBillIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_id >", value, "billId");
            }
            return (Criteria) this;
        }

        public Criteria andBillIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_id >=", value, "billId");
            }
            return (Criteria) this;
        }

        public Criteria andBillIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_id <", value, "billId");
            }
            return (Criteria) this;
        }

        public Criteria andBillIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_id <=", value, "billId");
            }
            return (Criteria) this;
        }

        public Criteria andBillIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_id like", value, "billId");
            }
            return (Criteria) this;
        }

        public Criteria andBillIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("bill_id not like", value, "billId");
            }
            return (Criteria) this;
        }

        public Criteria andBillIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("bill_id in", values, "billId");
            }
            return (Criteria) this;
        }

        public Criteria andBillIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("bill_id not in", values, "billId");
            }
            return (Criteria) this;
        }

        public Criteria andBillIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("bill_id between", value1, value2, "billId");
            }
            return (Criteria) this;
        }

        public Criteria andBillIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("bill_id not between", value1, value2, "billId");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("adjust_file_id =", value, "adjustFileId");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("adjust_file_id <>", value, "adjustFileId");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("adjust_file_id >", value, "adjustFileId");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("adjust_file_id >=", value, "adjustFileId");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("adjust_file_id <", value, "adjustFileId");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("adjust_file_id <=", value, "adjustFileId");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("adjust_file_id in", values, "adjustFileId");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("adjust_file_id not in", values, "adjustFileId");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("adjust_file_id between", value1, value2, "adjustFileId");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustFileIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("adjust_file_id not between", value1, value2, "adjustFileId");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustTypeEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("adjust_type =", value, "adjustType");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustTypeNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("adjust_type <>", value, "adjustType");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustTypeGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("adjust_type >", value, "adjustType");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustTypeGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("adjust_type >=", value, "adjustType");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustTypeLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("adjust_type <", value, "adjustType");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustTypeLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("adjust_type <=", value, "adjustType");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustTypeLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("adjust_type like", value, "adjustType");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustTypeNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("adjust_type not like", value, "adjustType");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustTypeInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("adjust_type in", values, "adjustType");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustTypeNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("adjust_type not in", values, "adjustType");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustTypeBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("adjust_type between", value1, value2, "adjustType");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustTypeNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("adjust_type not between", value1, value2, "adjustType");
            }
            return (Criteria) this;
        }

        public Criteria andFileNameEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_name =", value, "fileName");
            }
            return (Criteria) this;
        }

        public Criteria andFileNameNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_name <>", value, "fileName");
            }
            return (Criteria) this;
        }

        public Criteria andFileNameGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_name >", value, "fileName");
            }
            return (Criteria) this;
        }

        public Criteria andFileNameGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_name >=", value, "fileName");
            }
            return (Criteria) this;
        }

        public Criteria andFileNameLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_name <", value, "fileName");
            }
            return (Criteria) this;
        }

        public Criteria andFileNameLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_name <=", value, "fileName");
            }
            return (Criteria) this;
        }

        public Criteria andFileNameLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_name like", value, "fileName");
            }
            return (Criteria) this;
        }

        public Criteria andFileNameNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_name not like", value, "fileName");
            }
            return (Criteria) this;
        }

        public Criteria andFileNameInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("file_name in", values, "fileName");
            }
            return (Criteria) this;
        }

        public Criteria andFileNameNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("file_name not in", values, "fileName");
            }
            return (Criteria) this;
        }

        public Criteria andFileNameBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("file_name between", value1, value2, "fileName");
            }
            return (Criteria) this;
        }

        public Criteria andFileNameNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("file_name not between", value1, value2, "fileName");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url =", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url <>", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url >", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url >=", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url <", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url <=", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url like", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("file_url not like", value, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("file_url in", values, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("file_url not in", values, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("file_url between", value1, value2, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andFileUrlNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("file_url not between", value1, value2, "fileUrl");
            }
            return (Criteria) this;
        }

        public Criteria andUploadDateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("upload_date =", value, "uploadDate");
            }
            return (Criteria) this;
        }

        public Criteria andUploadDateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("upload_date <>", value, "uploadDate");
            }
            return (Criteria) this;
        }

        public Criteria andUploadDateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("upload_date >", value, "uploadDate");
            }
            return (Criteria) this;
        }

        public Criteria andUploadDateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("upload_date >=", value, "uploadDate");
            }
            return (Criteria) this;
        }

        public Criteria andUploadDateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("upload_date <", value, "uploadDate");
            }
            return (Criteria) this;
        }

        public Criteria andUploadDateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("upload_date <=", value, "uploadDate");
            }
            return (Criteria) this;
        }

        public Criteria andUploadDateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("upload_date in", values, "uploadDate");
            }
            return (Criteria) this;
        }

        public Criteria andUploadDateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("upload_date not in", values, "uploadDate");
            }
            return (Criteria) this;
        }

        public Criteria andUploadDateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("upload_date between", value1, value2, "uploadDate");
            }
            return (Criteria) this;
        }

        public Criteria andUploadDateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("upload_date not between", value1, value2, "uploadDate");
            }
            return (Criteria) this;
        }

        public Criteria andUploadUserIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("upload_user_id =", value, "uploadUserId");
            }
            return (Criteria) this;
        }

        public Criteria andUploadUserIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("upload_user_id <>", value, "uploadUserId");
            }
            return (Criteria) this;
        }

        public Criteria andUploadUserIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("upload_user_id >", value, "uploadUserId");
            }
            return (Criteria) this;
        }

        public Criteria andUploadUserIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("upload_user_id >=", value, "uploadUserId");
            }
            return (Criteria) this;
        }

        public Criteria andUploadUserIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("upload_user_id <", value, "uploadUserId");
            }
            return (Criteria) this;
        }

        public Criteria andUploadUserIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("upload_user_id <=", value, "uploadUserId");
            }
            return (Criteria) this;
        }

        public Criteria andUploadUserIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("upload_user_id in", values, "uploadUserId");
            }
            return (Criteria) this;
        }

        public Criteria andUploadUserIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("upload_user_id not in", values, "uploadUserId");
            }
            return (Criteria) this;
        }

        public Criteria andUploadUserIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("upload_user_id between", value1, value2, "uploadUserId");
            }
            return (Criteria) this;
        }

        public Criteria andUploadUserIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("upload_user_id not between", value1, value2, "uploadUserId");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark =", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark <>", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark >", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark >=", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark <", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark <=", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark like", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("remark not like", value, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("remark in", values, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("remark not in", values, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("remark between", value1, value2, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("remark not between", value1, value2, "remark");
            }
            return (Criteria) this;
        }

        public Criteria andStatusEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("status =", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("status <>", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("status >", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("status >=", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("status <", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("status <=", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("status like", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("status not like", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("status in", values, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("status not in", values, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("status between", value1, value2, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("status not between", value1, value2, "status");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustAmountEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("adjust_amount =", value, "adjustAmount");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustAmountNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("adjust_amount <>", value, "adjustAmount");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustAmountGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("adjust_amount >", value, "adjustAmount");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustAmountGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("adjust_amount >=", value, "adjustAmount");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustAmountLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("adjust_amount <", value, "adjustAmount");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustAmountLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("adjust_amount <=", value, "adjustAmount");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustAmountInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("adjust_amount in", values, "adjustAmount");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustAmountNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("adjust_amount not in", values, "adjustAmount");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustAmountBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("adjust_amount between", value1, value2, "adjustAmount");
            }
            return (Criteria) this;
        }

        public Criteria andAdjustAmountNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("adjust_amount not between", value1, value2, "adjustAmount");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *账单ID
         */
        BILLID("bill_id"),
        /**
         *调账文件ID
         */
        ADJUSTFILEID("adjust_file_id"),
        /**
         *调账类型
         */
        ADJUSTTYPE("adjust_type"),
        /**
         *文件名称
         */
        FILENAME("file_name"),
        /**
         *文件地址
         */
        FILEURL("file_url"),
        /**
         *上传时间
         */
        UPLOADDATE("upload_date"),
        /**
         *上传人
         */
        UPLOADUSERID("upload_user_id"),
        /**
         *备注
         */
        REMARK("remark"),
        /**
         *状态
         */
        STATUS("status"),
        /**
         *调账总金额，单位分
         */
        ADJUSTAMOUNT("adjust_amount");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}