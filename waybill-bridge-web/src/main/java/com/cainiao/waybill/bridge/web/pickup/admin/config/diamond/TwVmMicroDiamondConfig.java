package com.cainiao.waybill.bridge.web.pickup.admin.config.diamond;

import com.alibaba.common.lang.ExceptionUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.web.pickup.constant.PickUpWebAdminConstant;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021/11/26-下午5:08
 */
public class TwVmMicroDiamondConfig {

    private static String microConfig = "";

    private static void initConfig() {
        // 启动只用一次场景，直接get获取配置值
        try {
            microConfig = Diamond
                    .getConfig(PickUpWebAdminConstant.DiamondConfig.PICK_UP_ORDER_REPORT_MICRO_DATA_ID,
                            PickUpWebAdminConstant.DiamondConfig.PICK_UP_ORDER_REPORT_MICRO_GROUP_ID, 3000);
        } catch (IOException e1) {
            PickUpLogUtil.info("获取diamond中信息失败：" + ExceptionUtil.getStackTrace(e1));
        }
        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(PickUpWebAdminConstant.DiamondConfig.PICK_UP_ORDER_REPORT_MICRO_DATA_ID, PickUpWebAdminConstant.DiamondConfig.PICK_UP_ORDER_REPORT_MICRO_GROUP_ID,
                new ManagerListenerAdapter() {
                    @Override
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            microConfig = configInfo;
                        } catch (Exception e) {
                            PickUpLogUtil.info("ManagerListenerAdapter获取diamond中信息失败：" + ExceptionUtil.getStackTrace(e));
                        }
                    }
                });
    }

    static {
        initConfig();
    }

    public static String getMicroConfig() {
        return microConfig;
    }
}
