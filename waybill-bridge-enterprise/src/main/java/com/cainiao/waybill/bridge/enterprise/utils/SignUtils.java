package com.cainiao.waybill.bridge.enterprise.utils;

import org.apache.commons.codec.binary.Base64;

import java.security.MessageDigest;

public class SignUtils {

    public static String doSign(String content, String charset, String keys) {
        String sign = "";
        content = content + keys;
        try {
            //MD5加密
            MessageDigest md = MessageDigest.getInstance("MD5");
            //MD5加密后的字节数组
            md.update(content.getBytes(charset));
            //MD5加密后的十六进制字符串
            sign = new String(Base64.encodeBase64(md.digest()), charset);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return sign;
    }
}
