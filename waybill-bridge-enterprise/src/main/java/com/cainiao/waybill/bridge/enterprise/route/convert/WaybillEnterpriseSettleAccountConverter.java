package com.cainiao.waybill.bridge.enterprise.route.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseProductTypeEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.ExpressTypeEnum;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseSettleAccountDO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseSettleAccountDTO;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.Jar;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 该类用于在数据传输对象（DTO）与数据对象（DO）之间进行转换。
 * <AUTHOR>
 * @date 2025-04-23 15:35:39
 */
public class WaybillEnterpriseSettleAccountConverter {

    /**
     * DTO模型转换成DO模型
     * @param WaybillEnterpriseSettleAccountDTO
     */
    /*public static WaybillBridgeEnterpriseSettleAccountDO convertFromDTO(WaybillEnterpriseSettleAccountDTO waybillEnterpriseSettleAccountDTO) {
        if (waybillEnterpriseSettleAccountDTO == null) {
            return null;
        }
        WaybillBridgeEnterpriseSettleAccountDO waybillBridgeEnterpriseSettleAccountDO = new WaybillBridgeEnterpriseSettleAccountDO();
        BeanUtils.copyProperties(waybillEnterpriseSettleAccountDTO,waybillBridgeEnterpriseSettleAccountDO);
        return waybillBridgeEnterpriseSettleAccountDO;
    }*/

    /**
     * DO模型转换成DTO模型
     * @param waybillBridgeEnterpriseSettleAccountDO
     */
    public static WaybillEnterpriseSettleAccountDTO convertFromDO(WaybillBridgeEnterpriseSettleAccountDO waybillBridgeEnterpriseSettleAccountDO) {
        if (waybillBridgeEnterpriseSettleAccountDO == null) {
            return null;
        }
        WaybillEnterpriseSettleAccountDTO waybillEnterpriseSettleAccountDTO = new WaybillEnterpriseSettleAccountDTO();
        BeanUtils.copyProperties(waybillBridgeEnterpriseSettleAccountDO,waybillEnterpriseSettleAccountDTO);
        String product = waybillBridgeEnterpriseSettleAccountDO.getProduct();
        String byExpressCode = ExpressTypeEnum.getByExpressCode(waybillBridgeEnterpriseSettleAccountDO.getCpCode());
        waybillEnterpriseSettleAccountDTO.setCpName(byExpressCode);
        if (product != null) {
            Gson gson = new Gson();
            Type listType = new TypeToken<List<String>>() {}.getType();
            List<String> productList = gson.fromJson(product, listType);
            List<String> collect = productList.stream().map(EnterpriseProductTypeEnum::getProductType).collect(Collectors.toList());
            String result = String.join(",", collect);
            waybillEnterpriseSettleAccountDTO.setProduct(result);
            List<JSONObject> productTypes = productList.stream().map(item -> {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("value", item);
                String productType = EnterpriseProductTypeEnum.getProductType(item);
                if (StringUtils.isNotEmpty(productType) && !productType.startsWith(byExpressCode)) {
                    productType = byExpressCode + productType;
                }
                jsonObject.put("label", productType);
                return jsonObject;
            }).collect(Collectors.toList());
            waybillEnterpriseSettleAccountDTO.setProductList(productTypes);

        }
        return waybillEnterpriseSettleAccountDTO;
    }
}