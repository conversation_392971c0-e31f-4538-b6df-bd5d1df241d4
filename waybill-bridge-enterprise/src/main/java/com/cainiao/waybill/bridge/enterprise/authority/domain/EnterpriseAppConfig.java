package com.cainiao.waybill.bridge.enterprise.authority.domain;


import lombok.Data;

import java.io.Serializable;

/**
 * 该类用于配置一个企业应用的基本信息，包括客户端ID和客户端密钥。
 * <AUTHOR>
 * @date 2025-06-06 17:14:53
 */
@Data
public class EnterpriseAppConfig implements Serializable {

    private static final long serialVersionUID = 6033174070258264865L;
    /**
     * 原appKey
     */
    private String clientId;

    /**
     * 原appSecret
     */
    private String clientSecret;

    /**
     * 客户名称
     */
    private String clientName;

    /**
     * customKey
     */
    private String customKey;

    /**
     * customSecret
     */
    private String customSecret;

    /**
     * agentId模版推送
     */
    private String agentId;

}