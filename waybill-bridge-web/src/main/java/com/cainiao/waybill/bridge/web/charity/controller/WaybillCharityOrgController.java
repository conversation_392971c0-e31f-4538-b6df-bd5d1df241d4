package com.cainiao.waybill.bridge.web.charity.controller;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.cainiao.waybill.bridge.biz.charity.constant.CharityConstant;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CharityOrgLevelEnum;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CharityOrgTypeEnum;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CharityRoleEnum;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CharityStatusEnum;
import com.cainiao.waybill.bridge.biz.charity.request.CharityOrgExcelImportRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityOrgQueryRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityOrgSaveRequest;
import com.cainiao.waybill.bridge.biz.charity.response.CharityExcelResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityOrgExcelExportResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityOrgResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityOrgUserRelationExcelExportResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityPagingResponse;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityOrgService;
import com.cainiao.waybill.bridge.biz.charity.util.CharityUtil;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelExportUtil;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelImportUtil;
import com.cainiao.waybill.bridge.biz.wrapper.AddressUnifyWrapper;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.AssertUtil;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.common.waybill.pickup.service.AddressCleanService;
import com.cainiao.waybill.bridge.web.charity.CharityBaseResult;
import com.cainiao.waybill.bridge.web.charity.CharityExcelUtil;
import com.cainiao.waybill.bridge.web.charity.CharityLoginUserUtil;
import com.cainiao.waybill.bridge.web.common.util.ExeclUtil;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Api(tags = "慈善组织管理")
@RestController
@RequestMapping(value = "/charity/org")
public class WaybillCharityOrgController extends BaseController {

    @Autowired
    private WaybillCharityOrgService charityOrgService;

    @Autowired
    private AddressCleanService addressCleanService;

    /**
     * 根据参数查找
     */
    @ApiOperation("查询所有的组织列表")
    @ResponseBody
    @RequestMapping("/list/all")
    public CharityBaseResult<List<CharityOrgResponse>> listAllOrg(@RequestBody CharityOrgQueryRequest request) {
        if (request == null || StringUtils.isNotBlank(request.getOrgCode())) {
            return CharityBaseResult.success(Lists.newArrayList());
        }
        BridgeValidator.validate(request);
        request.setPageSize(1000);
        request.setCurrentPage(1);
        CharityPagingResponse<CharityOrgResponse> response = charityOrgService.listAllProjectOrg(request, false);
        return CharityBaseResult.success(response.getTableData());
    }

    /**
     * 根据参数查找
     */
    @ApiOperation("查询所有的组织列表")
    @ResponseBody
    @RequestMapping("/list")
    public CharityBaseResult<CharityPagingResponse<CharityOrgResponse>> listOrg(
        @RequestBody CharityOrgQueryRequest request) {
        if (request == null || StringUtils.isNotBlank(request.getOrgCode())) {
            return CharityBaseResult.success(CharityPagingResponse.build(Lists.newArrayList(), 0,
                1, 10));
        }
        BridgeValidator.validate(request);
        return CharityBaseResult.success(charityOrgService.listAllProjectOrg(request, true));
    }

    /**
     * 根据参数查找
     */
    @ApiOperation("查询所有的组织列表")
    @ResponseBody
    @RequestMapping("/export")
    public CharityBaseResult<CharityExcelResponse> export(@RequestBody CharityOrgQueryRequest request) {
        BridgeValidator.validate(request);

        String url = CharityExcelUtil.exportFile(request, "组织机构列表", CharityOrgExcelExportResponse.class,
            request1 -> charityOrgService.listAllProjectOrg(request1, true));

        return CharityBaseResult.success(new CharityExcelResponse(url));
    }

    @ApiOperation("导入文件")
    @ResponseBody
    @RequestMapping("/import")
    public CharityBaseResult<CharityExcelResponse> importExcel(@RequestParam("file") MultipartFile file) {
        List<CharityOrgExcelImportRequest> list = ExcelImportUtil.loadData(file, CharityOrgExcelImportRequest.class,
            1000);
        List<CharityOrgExcelImportRequest> errorList = Lists.newArrayList();
        for (CharityOrgExcelImportRequest importRequest : ListUtil.non(list)) {
            try {
                CharityOrgSaveRequest request = convert(importRequest);
                CharityLoginUserUtil.popLoginInfo(request);
                BridgeValidator.validate(request);

                charityOrgService.saveProjectOrg(request);
            } catch (BridgeBusinessException exception) {
                importRequest.setErrorInfo(exception.getErrorMessage());
                errorList.add(importRequest);
            } catch (Exception exception) {
                importRequest.setErrorInfo(exception.getMessage());
                errorList.add(importRequest);
            }
        }
        if (CollectionUtils.isEmpty(errorList)) {
            return CharityBaseResult.success();
        }
        Workbook workbook = ExcelExportUtil.build(errorList, "组织机构导入失败列表", CharityOrgExcelImportRequest.class);
        String url = ExeclUtil.exportToOSS(workbook, CharityConstant.OSS_TEMP_FILE_DIR);
        return CharityBaseResult.success(new CharityExcelResponse(url));
    }

    /**
     * 保存或者更新
     */
    @ApiOperation("创建或者更新组织")
    @ResponseBody
    @RequestMapping("/save")
    public CharityBaseResult<Void> saveOrg(@RequestBody CharityOrgSaveRequest request) {
        // 鉴权 仅管理员可保存
        boolean isAdmin = CharityRoleEnum.admin.name().equals(request.getRole());
        AssertUtil.assertValidate(!isAdmin, "您暂无权限保存组织信息");
        if (StringUtils.isNotBlank(request.getOrgCode()) && request.getStatus() != null) {
            AssertUtil.assertValidate(StringUtils.isBlank(CharityStatusEnum.descByStatus(request.getStatus())),
                "状态不正确");
            AssertUtil.assertValidate(StringUtils.isBlank(request.getProject()), "项目不能为空");
            charityOrgService.updateStatus(request);
            return CharityBaseResult.success();
        }
        BridgeValidator.validate(request);
        charityOrgService.saveProjectOrg(request);
        return CharityBaseResult.success();
    }

    private CharityOrgSaveRequest convert(CharityOrgExcelImportRequest request) {
        CharityOrgSaveRequest saveRequest = new CharityOrgSaveRequest();
        saveRequest.setName(request.getName());
        saveRequest.setProject(CharityUtil.getProjectByName(request.getProjectName()).getProject());
        saveRequest.setType(CharityOrgTypeEnum.nameToCode(request.getTypeName()));
        saveRequest.setLevel(CharityOrgLevelEnum.nameToCode(request.getLevelName()));
        saveRequest.setPrincipalName(request.getPrincipalName());
        saveRequest.setPrincipalPhone(request.getPrincipalPhone());
        saveRequest.setPrincipalMail(request.getPrincipalMail());
        AssertUtil.assertValidate(StringUtils.isBlank(request.getProvinceName()), "省地址不能为空");
        AssertUtil.assertValidate(StringUtils.isBlank(request.getCityName()), "市地址不能为空");
        AssertUtil.assertValidate(StringUtils.isBlank(request.getDistrictName()), "县地址不能为空");
        AssertUtil.assertValidate(StringUtils.isBlank(request.getAddressDetail()), "县地址不能为空");

        try {
            AddressDTO addressDTO = new AddressDTO();
            addressDTO.setProvinceName(request.getProvinceName());
            addressDTO.setCityName(request.getCityName());
            addressDTO.setAreaName(request.getDistrictName());
            addressDTO.setTownName(request.getTownName());
            addressDTO.setAddressDetail(request.getAddressDetail());
            AddressDTO cleanAddress = addressCleanService.clean(addressDTO);
            if (cleanAddress != null) {
                if (cleanAddress.getProvinceId() != null) {
                    saveRequest.setProvince(String.valueOf(cleanAddress.getProvinceId()));
                }
                if (cleanAddress.getCityId() != null) {
                    saveRequest.setCity(String.valueOf(cleanAddress.getCityId()));
                }
                if (cleanAddress.getAreaId() != null) {
                    saveRequest.setDistrict(String.valueOf(cleanAddress.getAreaId()));
                }
                if (cleanAddress.getTownId() != null) {
                    saveRequest.setTown(String.valueOf(cleanAddress.getTownId()));
                }
                saveRequest.setAddressDetail(cleanAddress.getAddressDetail());
            }
        } catch (BridgeBaseException e) {
            throw new BridgeBusinessException("address_clean_fail", "地址清洗错误");
        }
        return saveRequest;
    }

}
