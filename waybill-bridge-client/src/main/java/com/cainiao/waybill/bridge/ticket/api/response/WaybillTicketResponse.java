package com.cainiao.waybill.bridge.ticket.api.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 工单信息
 * <AUTHOR>
 */
@Data
public class WaybillTicketResponse implements Serializable {

    /**
     *   主键 ticketId
     */
    private Long id;

    /**
     *   创建时间
     */
    private Date gmtCreate;

    /**
     *   修改时间
     */
    private Date gmtModified;

    /**
     *   物流运单号
     */
    private String mailNo;

    /**
     *   入柜时间
     */
    private Date intoBoxTime;

    /**
     *   工单发起方  1：消费者  2：客服小二
     */
    private String operatorType;

    /**
     *   工单类型 1：签收核实 2：核实取件码 3：丢失/破损
     */
    private String ticketType;

    /**
     *   工单状态 0:取消 1:待处理 2:处理中 3:完结
     */
    private String ticketStatus;

    /**
     *   工单创建内容
     */
    private String ticketCreateContent;

    /**
     *   工单回复内容
     */
    private String ticketRespContent;

    /**
     *   工单附件列表名称
     */
    private String ticketFiles;

    /**
     *   是否进行催单 0:不进行 1:进行
     */
    private Byte hurryMark;

    /**
     *   扩展字段
     */
    private String feature;
}
