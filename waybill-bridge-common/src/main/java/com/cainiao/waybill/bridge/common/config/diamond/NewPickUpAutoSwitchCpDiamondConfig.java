package com.cainiao.waybill.bridge.common.config.diamond;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.alibaba.common.lang.ExceptionUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.common.constants.PickUpConfigConstants;
import com.google.common.collect.Lists;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2022/07/26-下午3:16
 */
public class NewPickUpAutoSwitchCpDiamondConfig {

    public static final Logger infoLogger = LoggerFactory.getLogger("WAYBILL_PICKUP_INFO");

    private static List<PickUpAutoSwitchCpConfig> CONFIG = Lists.newArrayList();

    private static void initConfig() {
        // 启动只用一次场景，直接get获取配置值
        try {
            String configInfo = Diamond
                .getConfig(PickUpConfigConstants.DiamondConfig.PICK_UP_AUTO_SWITCH_CP_CONFIG_DATA_ID,
                    PickUpConfigConstants.DiamondConfig.PICK_UP_AUTO_SWITCH_CP_CONFIG_GROUP_ID, 3000);
            parseVmVersionConfig(configInfo);
        } catch (IOException e1) {
            infoLogger.info("获取diamond中自动切换cp配置信息：" + ExceptionUtil.getStackTrace(e1));
        }
        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(PickUpConfigConstants.DiamondConfig.PICK_UP_AUTO_SWITCH_CP_CONFIG_DATA_ID,
            PickUpConfigConstants.DiamondConfig.PICK_UP_AUTO_SWITCH_CP_CONFIG_GROUP_ID,
            new ManagerListenerAdapter() {
                @Override
                public void receiveConfigInfo(String configInfo) {
                    try {
                        parseVmVersionConfig(configInfo);
                    } catch (Exception e) {
                        infoLogger.info(
                            "ManagerListenerAdapter获取diamond中配置的自动切换cp配置信息：" + ExceptionUtil.getStackTrace(e));
                    }
                }
            });
    }

    public static List<PickUpAutoSwitchCpConfig> getConfig() {
        return CONFIG;
    }

    static {
        initConfig();
    }

    private static void parseVmVersionConfig(String configInfo) {
        if (StringUtil.isBlank(configInfo)) {
            return;
        }
        CONFIG = JSONArray.parseArray(configInfo, PickUpAutoSwitchCpConfig.class);
    }

    @Data
    public static class PickUpAutoSwitchCpConfig {

        private List<String> canSwitchCpList;

        private List<String> canSwitchResCode;

        private Map<String, Map<String, Integer>> backUpCpAndRateMap;

        private List<String> switchAcceptBranchCodeStrategy;

        private List<String> switchAppointAcceptBranchCodeStrategy;

        private List<String> switchPathStrategy;

        private List<String> switchAppointPathStrategy;

        /**
         * 固定时间转单，预约单超过天数
         */
        private Integer fixTimeSwitchAppointOrderOverDays;

        /**
         * 固定时间转单，普通单超过天数
         */
        private Integer fixTimeSwitchNormalOrderOverDays;

        /**
         * 非预约单，固定时间转单, 格式：06:00
         */
        private String fixTimeSwitchNormalOrder;

        /**
         * 预约单固定时间转单, 格式：23:00
         */
        private String fixTimeSwitchAppointOrder;

        /**
         * 预约单超时揽收转单
         */
        private Integer switchAppointOrderOverHour;

        /**
         * 单日单超时时间揽收转单
         */
        private Integer switchNormalOrderOverHour;


    }
}
