package com.cainiao.waybill.bridge.innersupport.ad.web.wrapper;

import com.cainiao.waybill.ad.api.common.BaseResultDTO;
import com.cainiao.waybill.ad.api.request.AdTicketCallRequest;
import com.cainiao.waybill.ad.api.service.WaybillAdTicketService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description 工单联系客户
 * <AUTHOR>
 * @Date 2024/12/5 15:07
 */
@Component
@Slf4j
public class WorkOrderCallWrapper {

    @Resource
    private WaybillAdTicketService waybillAdTicketService;


    /**
     * 联系客户
     *
     * @param waybillCode 快递单号,字符串格式
     * @param phone 电话号码，字符串格式
     * @return 无返回结果，如果调用服务方法"callCustomer"执行电话呼叫操作失败或没有返回结果，则记录错误日志
     */
    public void callCustomer(String waybillCode, String phone) {
        if (StringUtils.isAnyBlank(waybillCode, phone)) {
            return;
        }
        AdTicketCallRequest request = new AdTicketCallRequest();
        request.setWaybillCode(waybillCode);
        request.setPhone(phone);
        BaseResultDTO<Boolean> callResult = waybillAdTicketService.callCustomer(request);
        if (callResult == null || !callResult.isSuccessAndHasModule()) {
            log.error("WorkOrderCallWrapper#callCustomer error, waybillCode:{}, phone:{}", waybillCode, phone);
        }
    }

}
