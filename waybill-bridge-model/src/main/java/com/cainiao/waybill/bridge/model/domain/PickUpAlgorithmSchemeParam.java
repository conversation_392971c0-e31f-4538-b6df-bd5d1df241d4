package com.cainiao.waybill.bridge.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class PickUpAlgorithmSchemeParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public PickUpAlgorithmSchemeParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public PickUpAlgorithmSchemeParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public PickUpAlgorithmSchemeParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public PickUpAlgorithmSchemeParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public PickUpAlgorithmSchemeParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public PickUpAlgorithmSchemeParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNull() {
            addCriterion("platform is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNotNull() {
            addCriterion("platform is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformEqualTo(String value) {
            addCriterion("platform =", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotEqualTo(String value) {
            addCriterion("platform <>", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThan(String value) {
            addCriterion("platform >", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanOrEqualTo(String value) {
            addCriterion("platform >=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThan(String value) {
            addCriterion("platform <", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanOrEqualTo(String value) {
            addCriterion("platform <=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLike(String value) {
            addCriterion("platform like", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotLike(String value) {
            addCriterion("platform not like", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformIn(List<String> values) {
            addCriterion("platform in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotIn(List<String> values) {
            addCriterion("platform not in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformBetween(String value1, String value2) {
            addCriterion("platform between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotBetween(String value1, String value2) {
            addCriterion("platform not between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsIsNull() {
            addCriterion("order_channels is null");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsIsNotNull() {
            addCriterion("order_channels is not null");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsEqualTo(String value) {
            addCriterion("order_channels =", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotEqualTo(String value) {
            addCriterion("order_channels <>", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsGreaterThan(String value) {
            addCriterion("order_channels >", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsGreaterThanOrEqualTo(String value) {
            addCriterion("order_channels >=", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsLessThan(String value) {
            addCriterion("order_channels <", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsLessThanOrEqualTo(String value) {
            addCriterion("order_channels <=", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsLike(String value) {
            addCriterion("order_channels like", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotLike(String value) {
            addCriterion("order_channels not like", value, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsIn(List<String> values) {
            addCriterion("order_channels in", values, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotIn(List<String> values) {
            addCriterion("order_channels not in", values, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsBetween(String value1, String value2) {
            addCriterion("order_channels between", value1, value2, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotBetween(String value1, String value2) {
            addCriterion("order_channels not between", value1, value2, "orderChannels");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListIsNull() {
            addCriterion("available_cp_list is null");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListIsNotNull() {
            addCriterion("available_cp_list is not null");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListEqualTo(String value) {
            addCriterion("available_cp_list =", value, "availableCpList");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListNotEqualTo(String value) {
            addCriterion("available_cp_list <>", value, "availableCpList");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListGreaterThan(String value) {
            addCriterion("available_cp_list >", value, "availableCpList");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListGreaterThanOrEqualTo(String value) {
            addCriterion("available_cp_list >=", value, "availableCpList");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListLessThan(String value) {
            addCriterion("available_cp_list <", value, "availableCpList");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListLessThanOrEqualTo(String value) {
            addCriterion("available_cp_list <=", value, "availableCpList");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListLike(String value) {
            addCriterion("available_cp_list like", value, "availableCpList");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListNotLike(String value) {
            addCriterion("available_cp_list not like", value, "availableCpList");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListIn(List<String> values) {
            addCriterion("available_cp_list in", values, "availableCpList");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListNotIn(List<String> values) {
            addCriterion("available_cp_list not in", values, "availableCpList");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListBetween(String value1, String value2) {
            addCriterion("available_cp_list between", value1, value2, "availableCpList");
            return (Criteria) this;
        }

        public Criteria andAvailableCpListNotBetween(String value1, String value2) {
            addCriterion("available_cp_list not between", value1, value2, "availableCpList");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionIsNull() {
            addCriterion("target_dimension is null");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionIsNotNull() {
            addCriterion("target_dimension is not null");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionEqualTo(String value) {
            addCriterion("target_dimension =", value, "targetDimension");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionNotEqualTo(String value) {
            addCriterion("target_dimension <>", value, "targetDimension");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionGreaterThan(String value) {
            addCriterion("target_dimension >", value, "targetDimension");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionGreaterThanOrEqualTo(String value) {
            addCriterion("target_dimension >=", value, "targetDimension");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionLessThan(String value) {
            addCriterion("target_dimension <", value, "targetDimension");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionLessThanOrEqualTo(String value) {
            addCriterion("target_dimension <=", value, "targetDimension");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionLike(String value) {
            addCriterion("target_dimension like", value, "targetDimension");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionNotLike(String value) {
            addCriterion("target_dimension not like", value, "targetDimension");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionIn(List<String> values) {
            addCriterion("target_dimension in", values, "targetDimension");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionNotIn(List<String> values) {
            addCriterion("target_dimension not in", values, "targetDimension");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionBetween(String value1, String value2) {
            addCriterion("target_dimension between", value1, value2, "targetDimension");
            return (Criteria) this;
        }

        public Criteria andTargetDimensionNotBetween(String value1, String value2) {
            addCriterion("target_dimension not between", value1, value2, "targetDimension");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintIsNull() {
            addCriterion("target_donstraint is null");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintIsNotNull() {
            addCriterion("target_donstraint is not null");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintEqualTo(String value) {
            addCriterion("target_donstraint =", value, "targetDonstraint");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintNotEqualTo(String value) {
            addCriterion("target_donstraint <>", value, "targetDonstraint");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintGreaterThan(String value) {
            addCriterion("target_donstraint >", value, "targetDonstraint");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintGreaterThanOrEqualTo(String value) {
            addCriterion("target_donstraint >=", value, "targetDonstraint");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintLessThan(String value) {
            addCriterion("target_donstraint <", value, "targetDonstraint");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintLessThanOrEqualTo(String value) {
            addCriterion("target_donstraint <=", value, "targetDonstraint");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintLike(String value) {
            addCriterion("target_donstraint like", value, "targetDonstraint");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintNotLike(String value) {
            addCriterion("target_donstraint not like", value, "targetDonstraint");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintIn(List<String> values) {
            addCriterion("target_donstraint in", values, "targetDonstraint");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintNotIn(List<String> values) {
            addCriterion("target_donstraint not in", values, "targetDonstraint");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintBetween(String value1, String value2) {
            addCriterion("target_donstraint between", value1, value2, "targetDonstraint");
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintNotBetween(String value1, String value2) {
            addCriterion("target_donstraint not between", value1, value2, "targetDonstraint");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressIsNull() {
            addCriterion("algorithm_progress is null");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressIsNotNull() {
            addCriterion("algorithm_progress is not null");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressEqualTo(String value) {
            addCriterion("algorithm_progress =", value, "algorithmProgress");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressNotEqualTo(String value) {
            addCriterion("algorithm_progress <>", value, "algorithmProgress");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressGreaterThan(String value) {
            addCriterion("algorithm_progress >", value, "algorithmProgress");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressGreaterThanOrEqualTo(String value) {
            addCriterion("algorithm_progress >=", value, "algorithmProgress");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressLessThan(String value) {
            addCriterion("algorithm_progress <", value, "algorithmProgress");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressLessThanOrEqualTo(String value) {
            addCriterion("algorithm_progress <=", value, "algorithmProgress");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressLike(String value) {
            addCriterion("algorithm_progress like", value, "algorithmProgress");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressNotLike(String value) {
            addCriterion("algorithm_progress not like", value, "algorithmProgress");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressIn(List<String> values) {
            addCriterion("algorithm_progress in", values, "algorithmProgress");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressNotIn(List<String> values) {
            addCriterion("algorithm_progress not in", values, "algorithmProgress");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressBetween(String value1, String value2) {
            addCriterion("algorithm_progress between", value1, value2, "algorithmProgress");
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressNotBetween(String value1, String value2) {
            addCriterion("algorithm_progress not between", value1, value2, "algorithmProgress");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultIsNull() {
            addCriterion("algorithm_calc_result is null");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultIsNotNull() {
            addCriterion("algorithm_calc_result is not null");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultEqualTo(String value) {
            addCriterion("algorithm_calc_result =", value, "algorithmCalcResult");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultNotEqualTo(String value) {
            addCriterion("algorithm_calc_result <>", value, "algorithmCalcResult");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultGreaterThan(String value) {
            addCriterion("algorithm_calc_result >", value, "algorithmCalcResult");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultGreaterThanOrEqualTo(String value) {
            addCriterion("algorithm_calc_result >=", value, "algorithmCalcResult");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultLessThan(String value) {
            addCriterion("algorithm_calc_result <", value, "algorithmCalcResult");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultLessThanOrEqualTo(String value) {
            addCriterion("algorithm_calc_result <=", value, "algorithmCalcResult");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultLike(String value) {
            addCriterion("algorithm_calc_result like", value, "algorithmCalcResult");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultNotLike(String value) {
            addCriterion("algorithm_calc_result not like", value, "algorithmCalcResult");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultIn(List<String> values) {
            addCriterion("algorithm_calc_result in", values, "algorithmCalcResult");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultNotIn(List<String> values) {
            addCriterion("algorithm_calc_result not in", values, "algorithmCalcResult");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultBetween(String value1, String value2) {
            addCriterion("algorithm_calc_result between", value1, value2, "algorithmCalcResult");
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultNotBetween(String value1, String value2) {
            addCriterion("algorithm_calc_result not between", value1, value2, "algorithmCalcResult");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestIsNull() {
            addCriterion("algorithm_digest is null");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestIsNotNull() {
            addCriterion("algorithm_digest is not null");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestEqualTo(String value) {
            addCriterion("algorithm_digest =", value, "algorithmDigest");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestNotEqualTo(String value) {
            addCriterion("algorithm_digest <>", value, "algorithmDigest");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestGreaterThan(String value) {
            addCriterion("algorithm_digest >", value, "algorithmDigest");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestGreaterThanOrEqualTo(String value) {
            addCriterion("algorithm_digest >=", value, "algorithmDigest");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestLessThan(String value) {
            addCriterion("algorithm_digest <", value, "algorithmDigest");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestLessThanOrEqualTo(String value) {
            addCriterion("algorithm_digest <=", value, "algorithmDigest");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestLike(String value) {
            addCriterion("algorithm_digest like", value, "algorithmDigest");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestNotLike(String value) {
            addCriterion("algorithm_digest not like", value, "algorithmDigest");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestIn(List<String> values) {
            addCriterion("algorithm_digest in", values, "algorithmDigest");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestNotIn(List<String> values) {
            addCriterion("algorithm_digest not in", values, "algorithmDigest");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestBetween(String value1, String value2) {
            addCriterion("algorithm_digest between", value1, value2, "algorithmDigest");
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestNotBetween(String value1, String value2) {
            addCriterion("algorithm_digest not between", value1, value2, "algorithmDigest");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeIsNull() {
            addCriterion("effective_time is null");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeIsNotNull() {
            addCriterion("effective_time is not null");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEqualTo(Date value) {
            addCriterion("effective_time =", value, "effectiveTime");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeNotEqualTo(Date value) {
            addCriterion("effective_time <>", value, "effectiveTime");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeGreaterThan(Date value) {
            addCriterion("effective_time >", value, "effectiveTime");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("effective_time >=", value, "effectiveTime");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeLessThan(Date value) {
            addCriterion("effective_time <", value, "effectiveTime");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeLessThanOrEqualTo(Date value) {
            addCriterion("effective_time <=", value, "effectiveTime");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeIn(List<Date> values) {
            addCriterion("effective_time in", values, "effectiveTime");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeNotIn(List<Date> values) {
            addCriterion("effective_time not in", values, "effectiveTime");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeBetween(Date value1, Date value2) {
            addCriterion("effective_time between", value1, value2, "effectiveTime");
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeNotBetween(Date value1, Date value2) {
            addCriterion("effective_time not between", value1, value2, "effectiveTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andPublisherIsNull() {
            addCriterion("publisher is null");
            return (Criteria) this;
        }

        public Criteria andPublisherIsNotNull() {
            addCriterion("publisher is not null");
            return (Criteria) this;
        }

        public Criteria andPublisherEqualTo(String value) {
            addCriterion("publisher =", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherNotEqualTo(String value) {
            addCriterion("publisher <>", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherGreaterThan(String value) {
            addCriterion("publisher >", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherGreaterThanOrEqualTo(String value) {
            addCriterion("publisher >=", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherLessThan(String value) {
            addCriterion("publisher <", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherLessThanOrEqualTo(String value) {
            addCriterion("publisher <=", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherLike(String value) {
            addCriterion("publisher like", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherNotLike(String value) {
            addCriterion("publisher not like", value, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherIn(List<String> values) {
            addCriterion("publisher in", values, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherNotIn(List<String> values) {
            addCriterion("publisher not in", values, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherBetween(String value1, String value2) {
            addCriterion("publisher between", value1, value2, "publisher");
            return (Criteria) this;
        }

        public Criteria andPublisherNotBetween(String value1, String value2) {
            addCriterion("publisher not between", value1, value2, "publisher");
            return (Criteria) this;
        }

        public Criteria andCalcKeyIsNull() {
            addCriterion("calc_key is null");
            return (Criteria) this;
        }

        public Criteria andCalcKeyIsNotNull() {
            addCriterion("calc_key is not null");
            return (Criteria) this;
        }

        public Criteria andCalcKeyEqualTo(String value) {
            addCriterion("calc_key =", value, "calcKey");
            return (Criteria) this;
        }

        public Criteria andCalcKeyNotEqualTo(String value) {
            addCriterion("calc_key <>", value, "calcKey");
            return (Criteria) this;
        }

        public Criteria andCalcKeyGreaterThan(String value) {
            addCriterion("calc_key >", value, "calcKey");
            return (Criteria) this;
        }

        public Criteria andCalcKeyGreaterThanOrEqualTo(String value) {
            addCriterion("calc_key >=", value, "calcKey");
            return (Criteria) this;
        }

        public Criteria andCalcKeyLessThan(String value) {
            addCriterion("calc_key <", value, "calcKey");
            return (Criteria) this;
        }

        public Criteria andCalcKeyLessThanOrEqualTo(String value) {
            addCriterion("calc_key <=", value, "calcKey");
            return (Criteria) this;
        }

        public Criteria andCalcKeyLike(String value) {
            addCriterion("calc_key like", value, "calcKey");
            return (Criteria) this;
        }

        public Criteria andCalcKeyNotLike(String value) {
            addCriterion("calc_key not like", value, "calcKey");
            return (Criteria) this;
        }

        public Criteria andCalcKeyIn(List<String> values) {
            addCriterion("calc_key in", values, "calcKey");
            return (Criteria) this;
        }

        public Criteria andCalcKeyNotIn(List<String> values) {
            addCriterion("calc_key not in", values, "calcKey");
            return (Criteria) this;
        }

        public Criteria andCalcKeyBetween(String value1, String value2) {
            addCriterion("calc_key between", value1, value2, "calcKey");
            return (Criteria) this;
        }

        public Criteria andCalcKeyNotBetween(String value1, String value2) {
            addCriterion("calc_key not between", value1, value2, "calcKey");
            return (Criteria) this;
        }

        public Criteria andApproveKeyIsNull() {
            addCriterion("approve_key is null");
            return (Criteria) this;
        }

        public Criteria andApproveKeyIsNotNull() {
            addCriterion("approve_key is not null");
            return (Criteria) this;
        }

        public Criteria andApproveKeyEqualTo(String value) {
            addCriterion("approve_key =", value, "approveKey");
            return (Criteria) this;
        }

        public Criteria andApproveKeyNotEqualTo(String value) {
            addCriterion("approve_key <>", value, "approveKey");
            return (Criteria) this;
        }

        public Criteria andApproveKeyGreaterThan(String value) {
            addCriterion("approve_key >", value, "approveKey");
            return (Criteria) this;
        }

        public Criteria andApproveKeyGreaterThanOrEqualTo(String value) {
            addCriterion("approve_key >=", value, "approveKey");
            return (Criteria) this;
        }

        public Criteria andApproveKeyLessThan(String value) {
            addCriterion("approve_key <", value, "approveKey");
            return (Criteria) this;
        }

        public Criteria andApproveKeyLessThanOrEqualTo(String value) {
            addCriterion("approve_key <=", value, "approveKey");
            return (Criteria) this;
        }

        public Criteria andApproveKeyLike(String value) {
            addCriterion("approve_key like", value, "approveKey");
            return (Criteria) this;
        }

        public Criteria andApproveKeyNotLike(String value) {
            addCriterion("approve_key not like", value, "approveKey");
            return (Criteria) this;
        }

        public Criteria andApproveKeyIn(List<String> values) {
            addCriterion("approve_key in", values, "approveKey");
            return (Criteria) this;
        }

        public Criteria andApproveKeyNotIn(List<String> values) {
            addCriterion("approve_key not in", values, "approveKey");
            return (Criteria) this;
        }

        public Criteria andApproveKeyBetween(String value1, String value2) {
            addCriterion("approve_key between", value1, value2, "approveKey");
            return (Criteria) this;
        }

        public Criteria andApproveKeyNotBetween(String value1, String value2) {
            addCriterion("approve_key not between", value1, value2, "approveKey");
            return (Criteria) this;
        }

        public Criteria andApproveStatusIsNull() {
            addCriterion("approve_status is null");
            return (Criteria) this;
        }

        public Criteria andApproveStatusIsNotNull() {
            addCriterion("approve_status is not null");
            return (Criteria) this;
        }

        public Criteria andApproveStatusEqualTo(String value) {
            addCriterion("approve_status =", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotEqualTo(String value) {
            addCriterion("approve_status <>", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusGreaterThan(String value) {
            addCriterion("approve_status >", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusGreaterThanOrEqualTo(String value) {
            addCriterion("approve_status >=", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusLessThan(String value) {
            addCriterion("approve_status <", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusLessThanOrEqualTo(String value) {
            addCriterion("approve_status <=", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusLike(String value) {
            addCriterion("approve_status like", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotLike(String value) {
            addCriterion("approve_status not like", value, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusIn(List<String> values) {
            addCriterion("approve_status in", values, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotIn(List<String> values) {
            addCriterion("approve_status not in", values, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusBetween(String value1, String value2) {
            addCriterion("approve_status between", value1, value2, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotBetween(String value1, String value2) {
            addCriterion("approve_status not between", value1, value2, "approveStatus");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNull() {
            addCriterion("ext_info is null");
            return (Criteria) this;
        }

        public Criteria andExtInfoIsNotNull() {
            addCriterion("ext_info is not null");
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualTo(String value) {
            addCriterion("ext_info =", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualTo(String value) {
            addCriterion("ext_info <>", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThan(String value) {
            addCriterion("ext_info >", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualTo(String value) {
            addCriterion("ext_info >=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThan(String value) {
            addCriterion("ext_info <", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualTo(String value) {
            addCriterion("ext_info <=", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoLike(String value) {
            addCriterion("ext_info like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLike(String value) {
            addCriterion("ext_info not like", value, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoIn(List<String> values) {
            addCriterion("ext_info in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotIn(List<String> values) {
            addCriterion("ext_info not in", values, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoBetween(String value1, String value2) {
            addCriterion("ext_info between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetween(String value1, String value2) {
            addCriterion("ext_info not between", value1, value2, "extInfo");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andNameEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("name =", value, "name");
            }
            return (Criteria) this;
        }

        public Criteria andNameNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("name <>", value, "name");
            }
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("name >", value, "name");
            }
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("name >=", value, "name");
            }
            return (Criteria) this;
        }

        public Criteria andNameLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("name <", value, "name");
            }
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("name <=", value, "name");
            }
            return (Criteria) this;
        }

        public Criteria andNameLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("name like", value, "name");
            }
            return (Criteria) this;
        }

        public Criteria andNameNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("name not like", value, "name");
            }
            return (Criteria) this;
        }

        public Criteria andNameInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("name in", values, "name");
            }
            return (Criteria) this;
        }

        public Criteria andNameNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("name not in", values, "name");
            }
            return (Criteria) this;
        }

        public Criteria andNameBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("name between", value1, value2, "name");
            }
            return (Criteria) this;
        }

        public Criteria andNameNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("name not between", value1, value2, "name");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform =", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform <>", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform >", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform >=", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform <", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform <=", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform like", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform not like", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("platform in", values, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("platform not in", values, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("platform between", value1, value2, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("platform not between", value1, value2, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels =", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels <>", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels >", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels >=", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels <", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels <=", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels like", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("order_channels not like", value, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("order_channels in", values, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("order_channels not in", values, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("order_channels between", value1, value2, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andOrderChannelsNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("order_channels not between", value1, value2, "orderChannels");
            }
            return (Criteria) this;
        }

        public Criteria andAvailableCpListEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("available_cp_list =", value, "availableCpList");
            }
            return (Criteria) this;
        }

        public Criteria andAvailableCpListNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("available_cp_list <>", value, "availableCpList");
            }
            return (Criteria) this;
        }

        public Criteria andAvailableCpListGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("available_cp_list >", value, "availableCpList");
            }
            return (Criteria) this;
        }

        public Criteria andAvailableCpListGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("available_cp_list >=", value, "availableCpList");
            }
            return (Criteria) this;
        }

        public Criteria andAvailableCpListLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("available_cp_list <", value, "availableCpList");
            }
            return (Criteria) this;
        }

        public Criteria andAvailableCpListLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("available_cp_list <=", value, "availableCpList");
            }
            return (Criteria) this;
        }

        public Criteria andAvailableCpListLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("available_cp_list like", value, "availableCpList");
            }
            return (Criteria) this;
        }

        public Criteria andAvailableCpListNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("available_cp_list not like", value, "availableCpList");
            }
            return (Criteria) this;
        }

        public Criteria andAvailableCpListInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("available_cp_list in", values, "availableCpList");
            }
            return (Criteria) this;
        }

        public Criteria andAvailableCpListNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("available_cp_list not in", values, "availableCpList");
            }
            return (Criteria) this;
        }

        public Criteria andAvailableCpListBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("available_cp_list between", value1, value2, "availableCpList");
            }
            return (Criteria) this;
        }

        public Criteria andAvailableCpListNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("available_cp_list not between", value1, value2, "availableCpList");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDimensionEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_dimension =", value, "targetDimension");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDimensionNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_dimension <>", value, "targetDimension");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDimensionGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_dimension >", value, "targetDimension");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDimensionGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_dimension >=", value, "targetDimension");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDimensionLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_dimension <", value, "targetDimension");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDimensionLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_dimension <=", value, "targetDimension");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDimensionLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_dimension like", value, "targetDimension");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDimensionNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_dimension not like", value, "targetDimension");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDimensionInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("target_dimension in", values, "targetDimension");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDimensionNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("target_dimension not in", values, "targetDimension");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDimensionBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("target_dimension between", value1, value2, "targetDimension");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDimensionNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("target_dimension not between", value1, value2, "targetDimension");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_donstraint =", value, "targetDonstraint");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_donstraint <>", value, "targetDonstraint");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_donstraint >", value, "targetDonstraint");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_donstraint >=", value, "targetDonstraint");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_donstraint <", value, "targetDonstraint");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_donstraint <=", value, "targetDonstraint");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_donstraint like", value, "targetDonstraint");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("target_donstraint not like", value, "targetDonstraint");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("target_donstraint in", values, "targetDonstraint");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("target_donstraint not in", values, "targetDonstraint");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("target_donstraint between", value1, value2, "targetDonstraint");
            }
            return (Criteria) this;
        }

        public Criteria andTargetDonstraintNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("target_donstraint not between", value1, value2, "targetDonstraint");
            }
            return (Criteria) this;
        }

        public Criteria andStatusEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("status =", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("status <>", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("status >", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("status >=", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("status <", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("status <=", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("status like", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("status not like", value, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("status in", values, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("status not in", values, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("status between", value1, value2, "status");
            }
            return (Criteria) this;
        }

        public Criteria andStatusNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("status not between", value1, value2, "status");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_progress =", value, "algorithmProgress");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_progress <>", value, "algorithmProgress");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_progress >", value, "algorithmProgress");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_progress >=", value, "algorithmProgress");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_progress <", value, "algorithmProgress");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_progress <=", value, "algorithmProgress");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_progress like", value, "algorithmProgress");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_progress not like", value, "algorithmProgress");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("algorithm_progress in", values, "algorithmProgress");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("algorithm_progress not in", values, "algorithmProgress");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("algorithm_progress between", value1, value2, "algorithmProgress");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmProgressNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("algorithm_progress not between", value1, value2, "algorithmProgress");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_calc_result =", value, "algorithmCalcResult");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_calc_result <>", value, "algorithmCalcResult");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_calc_result >", value, "algorithmCalcResult");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_calc_result >=", value, "algorithmCalcResult");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_calc_result <", value, "algorithmCalcResult");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_calc_result <=", value, "algorithmCalcResult");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_calc_result like", value, "algorithmCalcResult");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_calc_result not like", value, "algorithmCalcResult");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("algorithm_calc_result in", values, "algorithmCalcResult");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("algorithm_calc_result not in", values, "algorithmCalcResult");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("algorithm_calc_result between", value1, value2, "algorithmCalcResult");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmCalcResultNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("algorithm_calc_result not between", value1, value2, "algorithmCalcResult");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_digest =", value, "algorithmDigest");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_digest <>", value, "algorithmDigest");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_digest >", value, "algorithmDigest");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_digest >=", value, "algorithmDigest");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_digest <", value, "algorithmDigest");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_digest <=", value, "algorithmDigest");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_digest like", value, "algorithmDigest");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("algorithm_digest not like", value, "algorithmDigest");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("algorithm_digest in", values, "algorithmDigest");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("algorithm_digest not in", values, "algorithmDigest");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("algorithm_digest between", value1, value2, "algorithmDigest");
            }
            return (Criteria) this;
        }

        public Criteria andAlgorithmDigestNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("algorithm_digest not between", value1, value2, "algorithmDigest");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time =", value, "effectiveTime");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time <>", value, "effectiveTime");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time >", value, "effectiveTime");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time >=", value, "effectiveTime");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time <", value, "effectiveTime");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("effective_time <=", value, "effectiveTime");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("effective_time in", values, "effectiveTime");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("effective_time not in", values, "effectiveTime");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("effective_time between", value1, value2, "effectiveTime");
            }
            return (Criteria) this;
        }

        public Criteria andEffectiveTimeNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("effective_time not between", value1, value2, "effectiveTime");
            }
            return (Criteria) this;
        }

        public Criteria andCreatorEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("creator =", value, "creator");
            }
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("creator <>", value, "creator");
            }
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("creator >", value, "creator");
            }
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("creator >=", value, "creator");
            }
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("creator <", value, "creator");
            }
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("creator <=", value, "creator");
            }
            return (Criteria) this;
        }

        public Criteria andCreatorLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("creator like", value, "creator");
            }
            return (Criteria) this;
        }

        public Criteria andCreatorNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("creator not like", value, "creator");
            }
            return (Criteria) this;
        }

        public Criteria andCreatorInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("creator in", values, "creator");
            }
            return (Criteria) this;
        }

        public Criteria andCreatorNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("creator not in", values, "creator");
            }
            return (Criteria) this;
        }

        public Criteria andCreatorBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("creator between", value1, value2, "creator");
            }
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("creator not between", value1, value2, "creator");
            }
            return (Criteria) this;
        }

        public Criteria andPublisherEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("publisher =", value, "publisher");
            }
            return (Criteria) this;
        }

        public Criteria andPublisherNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("publisher <>", value, "publisher");
            }
            return (Criteria) this;
        }

        public Criteria andPublisherGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("publisher >", value, "publisher");
            }
            return (Criteria) this;
        }

        public Criteria andPublisherGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("publisher >=", value, "publisher");
            }
            return (Criteria) this;
        }

        public Criteria andPublisherLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("publisher <", value, "publisher");
            }
            return (Criteria) this;
        }

        public Criteria andPublisherLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("publisher <=", value, "publisher");
            }
            return (Criteria) this;
        }

        public Criteria andPublisherLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("publisher like", value, "publisher");
            }
            return (Criteria) this;
        }

        public Criteria andPublisherNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("publisher not like", value, "publisher");
            }
            return (Criteria) this;
        }

        public Criteria andPublisherInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("publisher in", values, "publisher");
            }
            return (Criteria) this;
        }

        public Criteria andPublisherNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("publisher not in", values, "publisher");
            }
            return (Criteria) this;
        }

        public Criteria andPublisherBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("publisher between", value1, value2, "publisher");
            }
            return (Criteria) this;
        }

        public Criteria andPublisherNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("publisher not between", value1, value2, "publisher");
            }
            return (Criteria) this;
        }

        public Criteria andCalcKeyEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("calc_key =", value, "calcKey");
            }
            return (Criteria) this;
        }

        public Criteria andCalcKeyNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("calc_key <>", value, "calcKey");
            }
            return (Criteria) this;
        }

        public Criteria andCalcKeyGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("calc_key >", value, "calcKey");
            }
            return (Criteria) this;
        }

        public Criteria andCalcKeyGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("calc_key >=", value, "calcKey");
            }
            return (Criteria) this;
        }

        public Criteria andCalcKeyLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("calc_key <", value, "calcKey");
            }
            return (Criteria) this;
        }

        public Criteria andCalcKeyLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("calc_key <=", value, "calcKey");
            }
            return (Criteria) this;
        }

        public Criteria andCalcKeyLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("calc_key like", value, "calcKey");
            }
            return (Criteria) this;
        }

        public Criteria andCalcKeyNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("calc_key not like", value, "calcKey");
            }
            return (Criteria) this;
        }

        public Criteria andCalcKeyInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("calc_key in", values, "calcKey");
            }
            return (Criteria) this;
        }

        public Criteria andCalcKeyNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("calc_key not in", values, "calcKey");
            }
            return (Criteria) this;
        }

        public Criteria andCalcKeyBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("calc_key between", value1, value2, "calcKey");
            }
            return (Criteria) this;
        }

        public Criteria andCalcKeyNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("calc_key not between", value1, value2, "calcKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveKeyEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_key =", value, "approveKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveKeyNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_key <>", value, "approveKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveKeyGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_key >", value, "approveKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveKeyGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_key >=", value, "approveKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveKeyLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_key <", value, "approveKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveKeyLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_key <=", value, "approveKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveKeyLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_key like", value, "approveKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveKeyNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_key not like", value, "approveKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveKeyInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("approve_key in", values, "approveKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveKeyNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("approve_key not in", values, "approveKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveKeyBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("approve_key between", value1, value2, "approveKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveKeyNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("approve_key not between", value1, value2, "approveKey");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status =", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status <>", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status >", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status >=", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status <", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status <=", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status like", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("approve_status not like", value, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("approve_status in", values, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("approve_status not in", values, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("approve_status between", value1, value2, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andApproveStatusNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("approve_status not between", value1, value2, "approveStatus");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info =", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info <>", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info >", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info >=", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info <", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info <=", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info like", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("ext_info not like", value, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("ext_info in", values, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("ext_info not in", values, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("ext_info between", value1, value2, "extInfo");
            }
            return (Criteria) this;
        }

        public Criteria andExtInfoNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("ext_info not between", value1, value2, "extInfo");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *方案名称
         */
        NAME("name"),
        /**
         *平台名称
         */
        PLATFORM("platform"),
        /**
         *平台渠道
         */
        ORDERCHANNELS("order_channels"),
        /**
         *可用运力列表
         */
        AVAILABLECPLIST("available_cp_list"),
        /**
         *目标指标项
         */
        TARGETDIMENSION("target_dimension"),
        /**
         *目标方向值
         */
        TARGETDONSTRAINT("target_donstraint"),
        /**
         *方案状态
         */
        STATUS("status"),
        /**
         *算法执行进度
         */
        ALGORITHMPROGRESS("algorithm_progress"),
        /**
         *算法测算结果
         */
        ALGORITHMCALCRESULT("algorithm_calc_result"),
        /**
         *算法测算结果摘要
         */
        ALGORITHMDIGEST("algorithm_digest"),
        /**
         *生效时间
         */
        EFFECTIVETIME("effective_time"),
        /**
         *创建人
         */
        CREATOR("creator"),
        /**
         *发布人
         */
        PUBLISHER("publisher"),
        /**
         *测算方案key
         */
        CALCKEY("calc_key"),
        /**
         *审批流key
         */
        APPROVEKEY("approve_key"),
        /**
         *审批状态
         */
        APPROVESTATUS("approve_status"),
        /**
         *扩展字段
         */
        EXTINFO("ext_info");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}