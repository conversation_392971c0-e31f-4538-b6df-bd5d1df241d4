package com.cainiao.waybill.bridge.web.pickup.cp;

/**
 * <AUTHOR>
 * @date 2021/7/9 下午5:52
 */

import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import com.alibaba.common.lang.ExceptionUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.util.NumberUtil;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.CancelCodes;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpAgentEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.FHD.ConfigKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.TraceFeatureKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpEventConstants;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpCancelOrderRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.fhd.FhdLogisticCallbackRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.fhd.FhdOrderStatusCallbackRequest;
import com.cainiao.waybill.bridge.biz.pickup.manager.PickUpCpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.manager.factory.PickUpCpOrderFactory;
import com.cainiao.waybill.bridge.biz.pickup.service.impl.WaybillPickUpEventMetaQSender;
import com.cainiao.waybill.bridge.biz.ticket.FhdEncryptUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.CpLogisticNoUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.CpWeightPushLimitUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.MailNoUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCpCodeUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpFeatureUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.base.ScenarioConstant;
import com.cainiao.waybill.bridge.common.constants.LogisticStatusEnum;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEventBuilder;
import com.cainiao.waybill.bridge.common.util.LoggerMonitorUtil;
import com.cainiao.waybill.bridge.common.waybill.constants.WaybillPickUpActionConstant;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/tracepush")
public class FhdTracePushRpc {

    private static final String RESP_FIELD_REASON = "message";
    private static final String RESP_CODE = "code";

    @Resource
    private WaybillPickUpOrderManager waybillPickUpOrderManager;

    @Resource
    private WaybillPickUpEventMetaQSender waybillPickUpEventMetaQSender;

    @PostMapping("/fhd")
    public Map<String, Object> tracePush(@RequestHeader(name = "pid") Long pid,
        @RequestHeader(name = "timestamp") String timestamp, @RequestHeader(name = "nonceStr") String nonceStr,
        @RequestHeader(name = "sign") String sign, @RequestBody String body) {

        PickUpLogUtil.record("outer_accept_fhd", body);
        LoggerMonitorUtil.start(ScenarioConstant.ORDER_CALL_BACK, PickUpAgentEnum.FHD.getAgent());

        PickUpLogUtil.info(String
            .format("data_digest, pid: %s, timestamp: %s, noneStr: %s, sign: %s", pid, timestamp, nonceStr, sign));

        Map<String, Object> result = Maps.newHashMap();
        result.put(RESP_CODE, 0);


        try {
            Map<String, String> fhdConfig = null;
            for (Map<String, String> config : BridgeSwitch.sourceMappingFhdPickUpConfig.values()) {
                if(StringUtil.equals(config.get(ConfigKey.pid.name()), String.valueOf(pid))){
                    fhdConfig = config;
                }
            }
            if(fhdConfig == null){
                PickUpLogUtil.errLog("", PickUpConstants.Action.TRACE_PUSH_RPC_INVALID_PARAM.name(),
                    PickUpConstants.Error.TRACE_PUSH_RPC_ERROR_FHD.getErrorCode(), "pid不存在");
                return result;
            }

            String checkSign = fhdConfig.get(ConfigKey.checkSign.name());

            String encryptSign = FhdEncryptUtil.pushEncryptSign(body, String.valueOf(pid),
                fhdConfig.get(PickUpConstants.FHD.ConfigKey.appSecret.name()),
                nonceStr, timestamp, sign);
            if ("true".equals(checkSign) && (!StringUtils.equals(sign, encryptSign))) {
                PickUpLogUtil.errLog("", PickUpConstants.Action.TRACE_PUSH_RPC_INVALID_PARAM.name(),
                    PickUpConstants.Error.TRACE_PUSH_RPC_ERROR_FHD.getErrorCode(), "签名校验失败");
                result.put(RESP_CODE, 1);
                result.put(RESP_FIELD_REASON, "签名校验失败");
                return result;
            }

            JSONObject jsonObject = JSONObject.parseObject(body);

            if ("expressOrderStatus".equals(jsonObject.getString("type"))) {
                dealOrderStatus(jsonObject.getJSONObject("message"), result, false, pid, fhdConfig);
            }

            if ("fhdExpressOrderStatus".equals(jsonObject.getString("type"))) {
                dealOrderStatus(jsonObject.getJSONObject("message"), result, true, pid, fhdConfig);
            }

            if ("expressLogisticsStatus".equals(jsonObject.getString("type"))) {
                dealLogistic(jsonObject.getJSONObject("message"), result);
            }

        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpConstants.Action.TRACE_PUSH_RPC.name(),
                PickUpConstants.Error.TRACE_PUSH_RPC_ERROR_YTO.getErrorCode(),
                PickUpConstants.Error.TRACE_PUSH_RPC_ERROR_YTO.getErrorMsg() + ExceptionUtil.getStackTrace(e));
            result.put(RESP_CODE, -1);
            result.put(RESP_FIELD_REASON, PickUpConstants.Error.TRACE_PUSH_SYS_ERROR.getErrorMsg());
        }finally {
            LoggerMonitorUtil.end(Objects.equals(result.get(RESP_CODE), 0), String.valueOf(result.get(RESP_FIELD_REASON)));
        }
        return result;
    }

    private void dealOrderStatus(JSONObject message, Map<String, Object> result, boolean newVersion, Long pid, Map<String, String> fhdConfig) {
        FhdOrderStatusCallbackRequest statusCallbackRequest = message.toJavaObject(FhdOrderStatusCallbackRequest.class);
        WaybillPickUpDetailDO detailDO;
        if(NumberUtil.isNumber(statusCallbackRequest.getOrderId())){
            detailDO = waybillPickUpOrderManager.getById(Long.parseLong(statusCallbackRequest.getOrderId()));
        }else{
            ImmutablePair<String, String> pair = CpLogisticNoUtil.FHD.parseResCodeAndOuterOrderCode(
                statusCallbackRequest.getOrderId());
            //根据回传的resCode和outOrderCode查询订单信息
            detailDO = waybillPickUpOrderManager.get(pair.left, pair.right);
        }

        String orderStatusCode = statusCallbackRequest.getOrderStatusCode();
        if(StringUtils.isBlank(orderStatusCode)){
            orderStatusCode = statusCallbackRequest.getOrderStatus();
        }

        String cpCode = PickUpCpCodeUtil.expressToPlatformCpCode(detailDO);
        // 如果cpCode已经更换并且转单状态已经变化，则认为订单已经转单
        if (detailDO == null || (detailDO.getSwitchCpStatus() != null && !StringUtils.equals(cpCode, statusCallbackRequest.getExpressCode()))
                || orderTimeout(orderStatusCode, detailDO)) {
            // 取消节点不处理
            if("CP_CANCEL".equals(orderStatusCode)){
                return;
            }

            // 超时订单取消
            PickUpCancelOrderRequest request = new PickUpCancelOrderRequest();
            request.setCpCode(statusCallbackRequest.getExpressCode());
            request.setCancelDesc("超时创建订单");
            request.setOrderId(statusCallbackRequest.getOrderId());
            request.setPreOrderIdFlag(true);
            request.setPid(pid);
            try {
                PickUpCpOrderManager cpPickUpOrderManager = PickUpCpOrderFactory.getCpOrderManagerByAgent(PickUpAgentEnum.FHD.getAgent(),
                    statusCallbackRequest.getExpressCode());
                cpPickUpOrderManager.cancel(request);
            } catch (Throwable e) {
                LoggerMonitorUtil.start(ScenarioConstant.FHD_ORDER_NOT_EXITS_CANCEL_ERROR, null);
                LoggerMonitorUtil.fail(e.getMessage());
                PickUpLogUtil.errLog("", PickUpConstants.Action.FHD_ORDER_NOT_EXISTS_CANCEL_EXCEPTION.name(),
                    "fhd_not_exits_order_cancel", e.getMessage(), e);

                result.put(RESP_CODE, 3);
                result.put(RESP_FIELD_REASON, "订单不存在");
                return;
            }

            PickUpLogUtil.info("fhd_order_not_exists_cancel_success, out_order_id:{}", statusCallbackRequest.getOrderId());
            return;
        }

        String action = getAction(orderStatusCode, detailDO, statusCallbackRequest, newVersion, fhdConfig);

        //  action为空，即不是希望拿到的节点类型，不向电商平台下发
        if (StringUtil.isBlank(action)) {
            return;
        }

        //  判断CP回传重量的时间是否超过指定的时间限制
        if (!CpWeightPushLimitUtil.cpPushWeightInLimitRange(action, detailDO)) {
            result.put(RESP_CODE, 4);
            result.put(RESP_FIELD_REASON, PickUpConstants.Error.TRACE_PUSH_GOT_LIMIT.getErrorMsg() + "T+"
                + BridgeSwitch.cpReturnWeightDayLimit);
            PickUpLogUtil.errLog("", PickUpConstants.Action.FHD_RETURN_WEIGHT_LIMIT.name(),
                PickUpConstants.Error.FHD_RETURN_WEIGHT_LIMIT.getErrorCode(),
                PickUpConstants.Error.FHD_RETURN_WEIGHT_LIMIT.getErrorMsg() + "existDetailDO:" + JSON
                    .toJSONString(detailDO));
            return;
        }

        WaybillPickUpEvent pickUpEvent = new WaybillPickUpEvent();
        pickUpEvent.setResCode(detailDO.getResCode());
        pickUpEvent.setMailNo(detailDO.getMailNo());
        pickUpEvent.setCpCode(detailDO.getCpCode());
        pickUpEvent.setAction(action);
        pickUpEvent.setActionDesc(WaybillPickUpActionConstant.getActionDesc(action));
        pickUpEvent.setActionGmtModified(System.currentTimeMillis());
        pickUpEvent.setLinkCpCode(
            PickUpFeatureUtil.getFromMulString(detailDO, PickUpConstants.TraceFeatureKey.LINK_CP_CODE));
        pickUpEvent.setOuterOrderCode(detailDO.getOuterOrderCode());
        setExtraInfo(pickUpEvent, statusCallbackRequest);

        if (!BridgeSwitch.fhdPushSwitch) {
            return;
        }

        waybillPickUpEventMetaQSender.send(pickUpEvent);
    }

    private static boolean orderTimeout(String orderStatusCode, WaybillPickUpDetailDO detailDO) {
        // 分配小件员了，订单还没有生成，说明没有下单超时失败了
        return BridgeSwitch.fhdOrderCheckCp && "ASSIGN_COURIER".equals(orderStatusCode) && MailNoUtil.isTempCpCode(detailDO.getCpCode());
    }

    private void setExtraInfo(WaybillPickUpEvent pickUpEvent,
        FhdOrderStatusCallbackRequest orderStatusCallbackRequest) {
        //  订单扩展信息
        Map<String, String> extraInfo = Maps.newHashMap();
        extraInfo.put(PickUpConstants.TraceFeatureKey.REAL_CP_CODE, orderStatusCallbackRequest.getExpressCode());
        if (WaybillPickUpActionConstant.GOT.equals(pickUpEvent.getAction()) || WaybillPickUpActionConstant.MODIFY_WEIGHT.equals(pickUpEvent.getAction())) {
            int weight = getWeight(orderStatusCallbackRequest);
            if (weight > 0) {
                extraInfo.put(PickUpEventConstants.ExtraInfoKey.WEIGHT, String.valueOf(weight));
            }
        }
        if (WaybillPickUpActionConstant.ACCEPT.equals(pickUpEvent.getAction())) {
            if (orderStatusCallbackRequest.getOrderEvent() != null) {
                // 兼容京东逻辑
                String courierName = orderStatusCallbackRequest.getOrderEvent().getCourierName();
                if(StringUtils.isBlank(courierName)){
                    courierName = orderStatusCallbackRequest.getOrderEvent().getCourierNo();
                }
                if(StringUtils.isBlank(courierName)){
                    courierName = orderStatusCallbackRequest.getOrderEvent().getName();
                }
                extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_NAME, courierName);
                String courierMobile = orderStatusCallbackRequest.getOrderEvent().getCourierPhone();
                if(StringUtils.isBlank(courierMobile)){
                    courierMobile = orderStatusCallbackRequest.getOrderEvent().getMobile();
                }
                extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_MOBILE, courierMobile);

                if(StringUtils.isNotBlank(orderStatusCallbackRequest.getOrderEvent().getSendStartTime())
                    && StringUtils.isNotBlank(orderStatusCallbackRequest.getOrderEvent().getSendEndTime())){
                    extraInfo.put(PickUpEventConstants.ExtraInfoKey.APPOINT_START_TIME, orderStatusCallbackRequest.getOrderEvent().getSendStartTime());
                    extraInfo.put(PickUpEventConstants.ExtraInfoKey.APPOINT_END_TIME, orderStatusCallbackRequest.getOrderEvent().getSendEndTime());
                }
            }
            long acceptTime = System.currentTimeMillis();
            extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_TIME, PickUpFeatureUtil.formatTime(acceptTime));
        }
        if (WaybillPickUpActionConstant.FAIL.equals(pickUpEvent.getAction())) {
            if (orderStatusCallbackRequest.getOrderEvent() != null) {
                // 兼容京东逻辑
                String reason = PickUpFeatureUtil.dealFailReason(orderStatusCallbackRequest.getOrderEvent().getComments());
                if(StringUtils.isBlank(reason)){
                    reason =  PickUpFeatureUtil.dealFailReason(orderStatusCallbackRequest.getOrderEvent().getTraceMark());
                }
                extraInfo.put(PickUpConstants.TraceFeatureKey.FAIL_REASON, reason);
                extraInfo.put(TraceFeatureKey.FAIL_CODE, CancelCodes.CP_CANCEL.getCode());
            }
            extraInfo.put(PickUpConstants.TraceFeatureKey.CANCEL_TIME,
                PickUpFeatureUtil.formatTime(pickUpEvent.getActionGmtModified()));
        }
        if (MapUtils.isNotEmpty(extraInfo)) {
            pickUpEvent.setExtraInfo(JSON.toJSONString(extraInfo));
        }
    }

    private String getAction(String orderStatus, WaybillPickUpDetailDO detailDO,
        FhdOrderStatusCallbackRequest orderStatusCallbackRequest, boolean newVersion, Map<String, String> fhdConfig) {
        if(newVersion){
            orderStatus = "new-" + orderStatus;
        }
        String action = BridgeSwitch.fhdPickUpAction.get(orderStatus);
        // 京东特殊处理，当前状态为上门接货完成但traceMark未退还时则认为是取消
        if(StringUtils.equals(orderStatusCallbackRequest.getOrderStatus(), fhdConfig.get(ConfigKey.gotStatus.name()))
            && orderStatusCallbackRequest.getOrderEvent() != null
            && StringUtils.equals(orderStatusCallbackRequest.getOrderEvent().getTraceMark(), fhdConfig.get(ConfigKey.gotCancel.name())) ){
            action = WaybillPickUpActionConstant.FAIL;
        }
        if (StringUtil.isBlank(action)) {
            return null;
        }
        String fromAppKey = PickUpCommonUtil.parseFromAppKey(detailDO.getResCode());
        if (WaybillPickUpActionConstant.GOT.equals(action) && !BridgeSwitch.noneSupportModifyAppKey.contains(fromAppKey)) {
            String existWeightStr = PickUpFeatureUtil.getFromMulString(detailDO,
                PickUpConstants.TraceFeatureKey.CP_WEIGHT);
            // if (detailDO.getStatus().equals(PickUpDetailStatusEnum.GOT.getValue())) {
            if (StringUtil.isNotBlank(existWeightStr)) {
                int existWeight = Integer.parseInt(existWeightStr);
                int newWeight = getWeight(orderStatusCallbackRequest);
                if (newWeight > 0 && newWeight != existWeight) {
                    return WaybillPickUpActionConstant.MODIFY_WEIGHT;
                } else {
                    return null;
                }
            }
        }
        return action;
    }

    private int getWeightByCalculate(FhdOrderStatusCallbackRequest orderStatusCallbackRequest) {
        if (orderStatusCallbackRequest.getOrderEvent() == null
            || orderStatusCallbackRequest.getOrderEvent().getCalculateWeight() == null) {
            return 0;
        }
        return orderStatusCallbackRequest.getOrderEvent().getCalculateWeight();
    }

    private int getWeight(FhdOrderStatusCallbackRequest orderStatusCallbackRequest) {
        return getWeightByCalculate(orderStatusCallbackRequest);
    }

    private void dealLogistic(JSONObject message, Map<String, Object> result) throws Exception {
        FhdLogisticCallbackRequest logisticCallbackRequest = message.toJavaObject(FhdLogisticCallbackRequest.class);

        WaybillPickUpDetailDO detailDO;
        if(NumberUtil.isNumber(logisticCallbackRequest.getOrderId())){
            detailDO = waybillPickUpOrderManager.getById(Long.parseLong(logisticCallbackRequest.getOrderId()));
        }else{
            ImmutablePair<String, String> pair = CpLogisticNoUtil.FHD.parseResCodeAndOuterOrderCode(
                logisticCallbackRequest.getOrderId());
            //根据回传的resCode和outOrderCode查询订单信息
            detailDO = waybillPickUpOrderManager.get(pair.left, pair.right);
        }

        // 订单不存在或者已经转单
        String cpCode = PickUpCpCodeUtil.expressToPlatformCpCode(detailDO);
        // 如果cpCode已经更换并且转单状态已经变化，则认为订单已经转单
        if (detailDO == null || (detailDO.getSwitchCpStatus() != null && !StringUtils.equals(cpCode, logisticCallbackRequest.getExpressCode()))) {
            result.put(RESP_CODE, 3);
            result.put(RESP_FIELD_REASON, "订单不存在");
            return;
        }
        //  揽收和签收由状态接口推送。其它节点直接转发
        dealNormalAction(detailDO, logisticCallbackRequest);
    }

    private void dealNormalAction(WaybillPickUpDetailDO existDetailDO, FhdLogisticCallbackRequest requestData)
        throws Exception {
        String action = BridgeSwitch.fhdPickUpLogisticsAction.get(requestData.getLogisticsStatus());
        if (StringUtils.isBlank(action)) {
            return;
        }
        WaybillPickUpEvent waybillPickUpEvent = convertToEvent(requestData, existDetailDO, action);
        LogisticStatusEnum logisticStatusEnum = LogisticStatusEnum.getWithEnumName(action);
        if (logisticStatusEnum != null) {
            waybillPickUpEvent.setActionDesc(logisticStatusEnum.getStatusDesc());
        }
        waybillPickUpEventMetaQSender.send(waybillPickUpEvent);
    }

    private WaybillPickUpEvent convertToEvent(FhdLogisticCallbackRequest requestData,
        WaybillPickUpDetailDO existDetailDO, String action) throws Exception {
        WaybillPickUpEventBuilder ansBuilder = WaybillPickUpEventBuilder.builder()
            .cpCode(existDetailDO.getCpCode())
            .mailNo(existDetailDO.getMailNo())
            .outerOrderCode(existDetailDO.getOuterOrderCode())
            .resCode(existDetailDO.getResCode())
            .linkCpCode(PickUpFeatureUtil.getFromMulString(existDetailDO, PickUpConstants.TraceFeatureKey.LINK_CP_CODE))
            .action(action)
            .actionDesc(requestData.getLogisticsStatusDesc())
            .lastActionDetail(requestData.getLogisticsDesc())
            .actionGmtModified(
                requestData.getCreateTime() != null ? requestData.getCreateTime() * 1000 : System.currentTimeMillis())
            .addExtraInfo(PickUpConstants.TraceFeatureKey.REAL_CP_CODE, requestData.getExpressCode());
        return ansBuilder.build();
    }
}
