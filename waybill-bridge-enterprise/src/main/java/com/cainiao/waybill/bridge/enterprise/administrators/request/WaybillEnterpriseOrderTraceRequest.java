package com.cainiao.waybill.bridge.enterprise.administrators.request;

import com.cainiao.waybill.bridge.enterprise.common.BaseRequest;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 寄件物流详情查询
 * <AUTHOR>
 * @date 2025/4/28 10:55
 */
@Data
public class WaybillEnterpriseOrderTraceRequest extends BaseRequest {

    private static final long serialVersionUID = 1632092952799686205L;
    /**
     * 外部订单ID-唯一
     */
    private String outerOrderCode;


}