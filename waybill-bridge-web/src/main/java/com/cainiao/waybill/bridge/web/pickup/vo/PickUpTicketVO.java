package com.cainiao.waybill.bridge.web.pickup.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/13 下午8:14
 */
@Getter
@Setter
@EqualsAndHashCode
public class PickUpTicketVO implements Serializable {
    @ExcelIgnore
    private static final long serialVersionUID = 1L;

    @ExcelProperty("工单id")
    private String id;

    @ExcelProperty("创建时间")
    private String gmtCreate;

    @ExcelProperty("运单号")
    private String mailNo;

    @ExcelProperty("快递公司")
    private String cpCode;

    @ExcelProperty("订单编号")
    private String outerOrderCode;

    @ExcelProperty("商品名称")
    private String goodsName;

    @ExcelProperty("寄件人")
    private String sendName;

    @ExcelProperty("寄件省份")
    private String sendProvince;

    @ExcelProperty("收件人")
    private String consigneeName;

    @ExcelProperty("收件地址")
    private String consigneeAddress;

    @ExcelProperty("工单类型")
    private String ticketType;

    @ExcelProperty("订单状态")
    private String orderStatus;

    @ExcelProperty("工单状态")
    private String ticketStatus;

    @ExcelProperty("是否处罚")
    private String isPunish;

    @ExcelProperty("是否撤销")
    private String isCancel;

    @ExcelProperty("是否修改重量")
    private String isUpdateWeigth;

    @ExcelProperty("订单创建时间")
    private String pickOrderGmtCreate;

    @ExcelProperty("工单来源")
    private String ticketPrimarySource;

    @ExcelProperty("工单超时类型")
    private String timeoutType;

    @ExcelProperty("工单内容")
    private String content;
}
