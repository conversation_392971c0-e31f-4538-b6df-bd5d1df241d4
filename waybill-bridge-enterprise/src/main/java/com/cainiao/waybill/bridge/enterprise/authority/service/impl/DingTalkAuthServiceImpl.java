package com.cainiao.waybill.bridge.enterprise.authority.service.impl;

import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.dingtalkoauth2_1_0.models.*;
import com.aliyun.tea.TeaException;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.enterprise.administrators.request.User;
import com.cainiao.waybill.bridge.enterprise.administrators.response.UserInfoResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.service.DingTalkUserService;
import com.cainiao.waybill.bridge.enterprise.authority.domain.EnterpriseAppConfig;
import com.cainiao.waybill.bridge.enterprise.authority.response.AuthInfoResponse;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseSwitchHolder;
import com.cainiao.waybill.bridge.enterprise.common.constant.DingTalkApiUrlConstant;
import com.cainiao.waybill.bridge.enterprise.common.constant.EnterpriseCommonConstant;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseConfigEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.utils.EnterpriseCacheUtil;
import com.cainiao.waybill.bridge.enterprise.utils.SessionUtil;
import com.cainiao.waybill.bridge.enterprise.utils.SignUtils;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseAuthDO;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseAuthParam;
import com.cainiao.waybill.bridge.model.mapper.WaybillEnterpriseAuthMapper;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiServiceGetAuthInfoRequest;
import com.dingtalk.api.response.OapiServiceGetAuthInfoResponse;
import com.taobao.tair.impl.mc.MultiClusterTairManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
public class DingTalkAuthServiceImpl implements DingTalkAuthService {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    Client dingTalkAuthClient;

    @Autowired
    private DingTalkUserService dingTalkUserService;

    @Autowired
    WaybillEnterpriseAuthMapper waybillEnterpriseAuthMapper;

    @Autowired
    private EnterpriseCacheUtil enterpriseCacheUtil;


    @Qualifier("waybillPickUpTairManager")
    @Resource
    private MultiClusterTairManager tairManager;

    @Value("${enterprise.dingtalk.clientId}")
    String clientId;

    @Value("${enterprise.dingtalk.clientSecret}")
    String clientSecret;

    /**
     * 生成权限Code
     */
    @Override
    public String getAccessToken(String platform, String authCorpId) {
        if(StringUtils.isEmpty(authCorpId) || StringUtils.isEmpty(platform)) {
            LOGGER.info("cropId : {} or platform : {} is null", authCorpId, platform);
            return null;
        }
        // 根据corpId查询记录
        WaybillEnterpriseAuthParam waybillEnterpriseAuthParam = new WaybillEnterpriseAuthParam();
        waybillEnterpriseAuthParam.createCriteria().andCorpIdEqualTo(authCorpId);
        try {
        WaybillEnterpriseAuthDO enterpriseAuthDO = waybillEnterpriseAuthMapper.selectOneByParam(waybillEnterpriseAuthParam);
        if(null == enterpriseAuthDO) {
            LOGGER.info("cropId : {} not found from DB", authCorpId);
            return null;
        }
        // 获取appConfig,如果配置了，就是用配置的，如果没取到，使用默认的
        EnterpriseAppConfig appConfig = EnterpriseSwitchHolder.ENTERPRISE_CLIENT_CONFIG.get(authCorpId);
        if(null != appConfig){
            clientId = appConfig.getCustomKey();
            clientSecret = appConfig.getCustomSecret();
        }
        LOGGER.info("start build getAccessToken request clientId : {} clientSecret: {}", clientId, clientSecret);
        GetTokenRequest getTokenRequest = new GetTokenRequest()
                    .setGrantType("client_credentials")
                    .setClientId(clientId)
                    .setClientSecret(clientSecret);

        GetTokenResponse accessTokenResponse = dingTalkAuthClient.getToken(authCorpId, getTokenRequest);
        if(null != accessTokenResponse && null != accessTokenResponse.getBody()) {
            String accessToken = accessTokenResponse.getBody().getAccessToken();
            long expireIn = accessTokenResponse.getBody().getExpiresIn();
            LOGGER.info("cropId : {} suiteTicket : {} getAccessToken {} success expire in {}", authCorpId, enterpriseAuthDO.getSuiteTicket(), accessToken, expireIn);
            return accessToken;
        }
        } catch (TeaException err) {
            LOGGER.error("cropId : {}  getAccessToken occur business error", authCorpId, err);
        } catch (Exception _err) {
            LOGGER.error("cropId : {}  getAccessToken occur unknown error", authCorpId, _err);
        }
        return null;
    }

    @Override
    public String getAccessTokenAppKey(String platform, String authCorpId) {
        if(StringUtils.isEmpty(authCorpId) || StringUtils.isEmpty(platform)) {
            LOGGER.info("cropId : {} or platform : {} is null", authCorpId, platform);
            return null;
        }
        // 根据corpId查询记录
        WaybillEnterpriseAuthParam waybillEnterpriseAuthParam = new WaybillEnterpriseAuthParam();
        waybillEnterpriseAuthParam.createCriteria().andCorpIdEqualTo(authCorpId);
        try {
            WaybillEnterpriseAuthDO enterpriseAuthDO = waybillEnterpriseAuthMapper.selectOneByParam(waybillEnterpriseAuthParam);
            if(null == enterpriseAuthDO) {
                LOGGER.info("cropId : {} not found from DB", authCorpId);
                return null;
            }
            // 获取appConfig,如果配置了，就是用配置的，如果没取到，使用默认的
            EnterpriseAppConfig appConfig = EnterpriseSwitchHolder.ENTERPRISE_CLIENT_CONFIG.get(authCorpId);
            if(null != appConfig){
                clientId = appConfig.getClientId();
                clientSecret = appConfig.getClientSecret();
            }
            LOGGER.info("start build getAccessTokenAppKey request clientId : {} clientSecret: {}", clientId, clientSecret);
            GetTokenRequest getTokenRequest = new GetTokenRequest()
                    .setGrantType("client_credentials")
                    .setClientId(clientId)
                    .setClientSecret(clientSecret);

            GetTokenResponse accessTokenResponse = dingTalkAuthClient.getToken(authCorpId, getTokenRequest);
            if(null != accessTokenResponse && null != accessTokenResponse.getBody()) {
                String accessToken = accessTokenResponse.getBody().getAccessToken();
                long expireIn = accessTokenResponse.getBody().getExpiresIn();
                LOGGER.info("cropId : {} suiteTicket : {} getAccessTokenAppKey {} success expire in {}", authCorpId, enterpriseAuthDO.getSuiteTicket(), accessToken, expireIn);
                return accessToken;
            }
        } catch (TeaException err) {
            LOGGER.error("cropId : {}  getAccessTokenAppKey occur business error", authCorpId, err);
        } catch (Exception _err) {
            LOGGER.error("cropId : {}  getAccessTokenAppKey occur unknown error", authCorpId, _err);
        }
        return null;
    }

    @Override
    public String getCorpAccessToken(String platform, String authCorpId) {
        if(StringUtils.isEmpty(authCorpId) || StringUtils.isEmpty(platform)) {
            LOGGER.info("cropId : {} or platform : {} is null", authCorpId, platform);
            return null;
        }
        // 根据corpId查询记录
        WaybillEnterpriseAuthParam waybillEnterpriseAuthParam = new WaybillEnterpriseAuthParam();
        waybillEnterpriseAuthParam.createCriteria().andCorpIdEqualTo(authCorpId);
        try {
            WaybillEnterpriseAuthDO enterpriseAuthDO = waybillEnterpriseAuthMapper.selectOneByParam(waybillEnterpriseAuthParam);
            if(null == enterpriseAuthDO) {
                LOGGER.info("cropId : {} not found from DB", authCorpId);
                return null;
            }

            // 获取appConfig,如果配置了，就是用配置的，如果没取到，使用默认的
            EnterpriseAppConfig appConfig = EnterpriseSwitchHolder.ENTERPRISE_CLIENT_CONFIG.get(authCorpId);

            if(null == appConfig){
                LOGGER.info("cropId : {} config not found", authCorpId);
                return null;
            }

            GetCorpAccessTokenRequest getCorpAccessTokenRequest = new GetCorpAccessTokenRequest()
                    .setSuiteKey(appConfig.getCustomKey())
                    .setSuiteSecret(appConfig.getCustomSecret())
                    .setAuthCorpId(authCorpId)
                    .setSuiteTicket(enterpriseAuthDO.getSuiteTicket());

            GetCorpAccessTokenResponse corpAccessToken = dingTalkAuthClient.getCorpAccessToken(getCorpAccessTokenRequest);

            if(null != corpAccessToken && null != corpAccessToken.getBody()) {
                String accessToken = corpAccessToken.getBody().getAccessToken();
                long expireIn = corpAccessToken.getBody().getExpireIn();
                LOGGER.info("cropId : {} suiteTicket : {} getCorpAccessToken {} success expire in {}", authCorpId, enterpriseAuthDO.getSuiteTicket(), accessToken, expireIn);
                return accessToken;
            }
        } catch (TeaException err) {
            LOGGER.error("cropId : {}  getCorpAccessToken occur business error", authCorpId, err);
        } catch (Exception _err) {
            LOGGER.error("cropId : {}  getCorpAccessToken occur unknown error", authCorpId, _err);
        }
        return null;
    }

    @Override
    public String getUserToken(String authCode, String corpId) {
        try {
            // 获取appConfig,如果配置了，就是用配置的，如果没取到，使用默认的
            EnterpriseAppConfig appConfig = EnterpriseSwitchHolder.ENTERPRISE_CLIENT_CONFIG.get(corpId);
            if(null != appConfig){
                clientId = appConfig.getClientId();
                clientSecret = appConfig.getClientSecret();
            }

            GetUserTokenRequest getUserTokenRequest = new GetUserTokenRequest()
                    .setClientId(clientId)
                    .setClientSecret(clientSecret)
                    .setCode(authCode)
                    .setGrantType(EnterpriseCommonConstant.ENTERPRISE_AUTH_CODE);

            GetUserTokenResponse userTokenResponse = dingTalkAuthClient.getUserToken(getUserTokenRequest);
            if(null != userTokenResponse && null != userTokenResponse.getBody()) {
                String userToken = userTokenResponse.getBody().getAccessToken();
                long expireIn = userTokenResponse.getBody().getExpireIn();
                LOGGER.info("authCode : {} getUserToken {} success expire in {}", authCode, userToken, expireIn);
                return userToken;
            }
        } catch (TeaException err) {
            LOGGER.error("authCode : {}  getUserToken occur business error", authCode, err);
        } catch (Exception _err) {
            LOGGER.error("authCode : {}  getUserToken occur unknown error", authCode, _err);
        }
        return null;
    }


    @Override
    public String getDingDIngUrl(String requestType) {
        String url = EnterpriseSwitchHolder.DINGDING_REQUEST_URL.get(requestType);
        if (StringUtils.isEmpty(url)) {
            url = DingTalkApiUrlConstant.dingDingRequestUrl.get(requestType);
        }
        return url;
    }


    @Override
    public String createSession(String corpId, String userId, String salt) {
//        String sessionId = SignUtils.doSign(UUID.randomUUID().toString(), "UTF-8", salt);
        try {
            UserInfoResponse response = dingTalkUserService.getUserInfoToDingTalkByUserId(corpId, userId);
            if(null == response){
                throw new EnterpriseException(EnterpriseErrorEnum.LOGIN_USERID_NOT_EXIST.code(), EnterpriseErrorEnum.LOGIN_USERID_NOT_EXIST.describe());
            }
            User user = new User();
            user.setUserId(userId);
            user.setCorpId(corpId);
            user.setUserType(response.getUserType());
            String newToken = SessionUtil.generateToken(user);
            String currentToken = getSession(userId);
            if(StringUtils.isEmpty(currentToken)){
                enterpriseCacheUtil.put(EnterpriseCommonConstant.ENTERPRISE_USER_SESSION_KEY + userId, newToken, 60 * 60 * 24 * 3);
                currentToken = newToken;
            } else {
                enterpriseCacheUtil.put(EnterpriseCommonConstant.ENTERPRISE_USER_SESSION_KEY + userId, currentToken, 60 * 60 * 24 * 3);
            }
        return currentToken;
        } catch (EnterpriseException e){
            LOGGER.error("create session and put cache error", e);
        }
        return null;
    }

    @Override
    public String getSession(String userId) {
        try {
            Object sessionCacheObj = enterpriseCacheUtil.get(EnterpriseCommonConstant.ENTERPRISE_USER_SESSION_KEY + userId);
            if(null != sessionCacheObj){
                return sessionCacheObj.toString();
            }
        } catch (BridgeBusinessException e){
            LOGGER.error("getSession from cache error", e);
        }
        return null;
    }

    @Override
    public String delSession(String userId) {
        try {
            enterpriseCacheUtil.del(EnterpriseCommonConstant.ENTERPRISE_USER_SESSION_KEY + userId);
        } catch (BridgeBusinessException e){
            LOGGER.error("delSession from cache error", e);
        }
        return null;
    }

    @Override
    public Boolean cleanSession(String userId) {
        try {
            enterpriseCacheUtil.del(EnterpriseCommonConstant.ENTERPRISE_USER_SESSION_KEY + userId);
            return Boolean.TRUE;
        } catch (BridgeBusinessException e){
            LOGGER.error("cleanSession from cache error", e);
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean checkSession(String userId, String sessionId) {
        try {
            if (StringUtils.isEmpty(sessionId)){
                throw new EnterpriseException(EnterpriseErrorEnum.LOGIN_SESSION_EXPIRED.code(), EnterpriseErrorEnum.LOGIN_SESSION_EXPIRED.describe());
            }
            String session = getSession(userId);
            return sessionId.equals(session);
        }catch (EnterpriseException e){
            LOGGER.error("userId : {}  checkSession {} occur unknown error", userId, sessionId, e);
            return Boolean.FALSE;
        }
    }

    @Override
    public AuthInfoResponse getEnterpriseAuth(String platform, String corpId) {
        try {
            // 根据corpId查询记录
            WaybillEnterpriseAuthParam waybillEnterpriseAuthParam = new WaybillEnterpriseAuthParam();
            waybillEnterpriseAuthParam.createCriteria().andCorpIdEqualTo(corpId);
            WaybillEnterpriseAuthDO enterpriseAuthDO = waybillEnterpriseAuthMapper.selectOneByParam(waybillEnterpriseAuthParam);
            if(null == enterpriseAuthDO) {
                LOGGER.info("cropId : {} not found from DB", corpId);
                return null;
            }

            DingTalkClient client = new DefaultDingTalkClient(DingTalkApiUrlConstant.GET_ENTERPRISE_AUTH);
            OapiServiceGetAuthInfoRequest req = new OapiServiceGetAuthInfoRequest();
            req.setAuthCorpid(corpId);
            // 第三方企业应用的填写应用SuiteKey和SuiteSecret。
            // 定制应用填写应用的CustomKey和CustomSecret。
            OapiServiceGetAuthInfoResponse rsp = client.execute(req, clientId ,clientSecret ,enterpriseAuthDO.getSuiteTicket());
            OapiServiceGetAuthInfoResponse.AuthInfo authInfo = rsp.getAuthInfo();
            AuthInfoResponse authInfoResponse = new AuthInfoResponse();
            if(null != authInfo){
                List<OapiServiceGetAuthInfoResponse.Agent> agents = authInfo.getAgent();
                agents.forEach(o->{
                    if (null != o.getAppid() && EnterpriseSwitchHolder.ENTERPRISE_CONFIG.get(EnterpriseConfigEnum.appId.name()).equals(String.valueOf(o.getAppid()))){
                        authInfoResponse.setAgentId(o.getAgentid());
                    }
                });
            }
            return authInfoResponse;
        } catch (Exception e) {
            LOGGER.error("corpId {} request enterprise auth error", corpId, e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public boolean addEnterpriseAuth(String platform, String corpId) {
        WaybillEnterpriseAuthDO waybillEnterpriseAuthDO = new WaybillEnterpriseAuthDO();
        waybillEnterpriseAuthDO.setCorpId(corpId);
        waybillEnterpriseAuthDO.setPlatform(platform);
        waybillEnterpriseAuthDO.setGmtCreate(new Date());
        waybillEnterpriseAuthDO.setGmtModified(new Date());
        waybillEnterpriseAuthDO.setRefreshTime(new Date());
        waybillEnterpriseAuthDO.setSuiteTicket("inner_test_suite_ticket");
        waybillEnterpriseAuthMapper.insert(waybillEnterpriseAuthDO);

        return Boolean.TRUE;
    }

}
