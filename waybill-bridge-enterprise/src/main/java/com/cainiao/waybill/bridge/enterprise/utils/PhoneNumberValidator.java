package com.cainiao.waybill.bridge.enterprise.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/5/15
 **/
public class PhoneNumberValidator {

    // 手机号码正则
    private static final String PHONE_VALID = "^(?:\\+?(\\d{1,4})[-\\s]?|\\(\\d{1,4}\\))?(?:86)?1\\d{10}$";
    // 带区号手机后码截取
    private static final String PHONE_REGEX = "^(?:\\+?\\d{1,4}[-\\s]?|\\(\\d{1,4}\\))?(\\d{11})$";
    // 座机号码正则
    private static final String LAND_LINE = "^(0\\d{2,3})?[-]?\\d{7,8}$";
    // 四位以上数字
    private static final String FOUR_DIGIT_NUM = "^\\d{4,11}$";

    private static final Pattern PHONE_VALID_PATTERN = Pattern.compile(PHONE_VALID);
    private static final Pattern PHONE_PATTERN = Pattern.compile(PHONE_REGEX);
    private static final Pattern LAND_LINE_PATTERN = Pattern.compile(LAND_LINE);
    private static final Pattern FOUR_DIGIT_NUM_PATTERN = Pattern.compile(FOUR_DIGIT_NUM);

    /**
     * 校验手机号格式
     *
     * @param phoneNumber 手机号字符串
     * @return 如果格式正确返回 true，否则返回 false
     */
    public static boolean isValidPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return false;
        }
        return PHONE_VALID_PATTERN.matcher(phoneNumber).matches() || LAND_LINE_PATTERN.matcher(phoneNumber).matches();
    }

    /**
     * 校验手机号格式，并提取纯手机号
     *
     * @param phoneNumber 手机号字符串
     * @return 返回纯手机号
     */
    public static String extractPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return null;
        }

        Matcher phoneMatcher = PHONE_PATTERN.matcher(phoneNumber);
        Matcher landLineMatcher = LAND_LINE_PATTERN.matcher(phoneNumber);
        if (phoneMatcher.matches()) {
            return phoneMatcher.group(1);
        } else if (landLineMatcher.matches()) {
            return phoneNumber;
        }
        return null;
    }

    /**
     * 校验手机号是否为四位以上数字
     *
     * @param number 手机号字符串
     * @return 如果格式正确返回 true，否则返回 false
     */
    public static boolean isFourDigitNumber(String number) {
        if (number == null || number.isEmpty()) {
            return false;
        }
        return FOUR_DIGIT_NUM_PATTERN.matcher(number).matches();
    }

    public static boolean isValidNumber(String str) {
        return str != null && str.matches("^(\\d+(-\\d+)*)?$");
    }

}
