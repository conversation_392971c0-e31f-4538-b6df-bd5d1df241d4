package com.cainiao.waybill.bridge.common.metaq;

import com.alibaba.fastjson.JSONObject;
import com.cainiao.waybill.bridge.common.waybill.constants.WaybillPickUpActionConstant;

/**
 * <AUTHOR>
 * @date 2021/6/16
 */
public class WaybillPickUpEventBuilder {

    public static WaybillPickUpEventBuilder builder() {
        WaybillPickUpEventBuilder ans = new WaybillPickUpEventBuilder();
        return ans;
    }

    private String cpCode;

    private String mailNo;

    private String resCode;

    private String linkCpCode;

    /**
     * 物流详情节点类型
     * {@link WaybillPickUpActionConstant}
     */
    private String action;

    private String actionDesc;

    private String lastActionDetail;

    private Long actionGmtModified;

    private String actionCity;

    private String outerOrderCode;

    private JSONObject extraInfoJSONObj;


    public WaybillPickUpEventBuilder cpCode(String cpCode) {
        this.cpCode = cpCode;
        return this;
    }

    public WaybillPickUpEventBuilder mailNo(String mailNo) {
        this.mailNo = mailNo;
        return this;
    }

    public WaybillPickUpEventBuilder resCode(String resCode) {
        this.resCode = resCode;
        return this;
    }

    public WaybillPickUpEventBuilder linkCpCode(String linkCpCode) {
        this.linkCpCode = linkCpCode;
        return this;
    }

    /**
     * 物流详情节点类型
     * {@link WaybillPickUpActionConstant}
     */
    public WaybillPickUpEventBuilder action(String action) {
        this.action = action;
        return this;
    }

    public WaybillPickUpEventBuilder actionDesc(String actionDesc) {
        this.actionDesc = actionDesc;
        return this;
    }

    public WaybillPickUpEventBuilder lastActionDetail(String lastActionDetail) {
        this.lastActionDetail = lastActionDetail;
        return this;
    }

    public WaybillPickUpEventBuilder actionGmtModified(Long actionGmtModified) {
        this.actionGmtModified = actionGmtModified;
        return this;
    }

    public WaybillPickUpEventBuilder actionCity(String actionCity) {
        this.actionCity = actionCity;
        return this;
    }

    public WaybillPickUpEventBuilder addExtraInfo(String key, Object value) {
        if (this.extraInfoJSONObj == null) {
            this.extraInfoJSONObj = new JSONObject();
        }

        extraInfoJSONObj.put(key, value);
        return this;
    }

    public WaybillPickUpEventBuilder outerOrderCode(String outerOrderCode) {
        this.outerOrderCode = outerOrderCode;
        return this;
    }

    public WaybillPickUpEvent build() {
        String extraStr = this.extraInfoJSONObj != null ? this.extraInfoJSONObj.toJSONString(): null;

        WaybillPickUpEvent ans = new WaybillPickUpEvent()
                .setAction(this.action)
                .setActionCity(this.actionCity)
                .setActionDesc(this.actionDesc)
                .setActionGmtModified(this.actionGmtModified)
                .setCpCode(this.cpCode)
                .setExtraInfo(extraStr)
                .setLastActionDetail(this.lastActionDetail)
                .setMailNo(this.mailNo)
                .setOuterOrderCode(this.outerOrderCode)
                .setResCode(this.resCode)
                .setLinkCpCode(this.linkCpCode);
        return ans;
    }

}
