## 目录结构
解压后生成以下两个子目录

* waybill-bridge-service，包含各中间件的使用示例代码，代码在src/main/java目录下的com.cainiao.waybill包中。
* waybill-bridge-start，包含启动类`com.cainiao.waybill.Application`。中间件使用示例的单元测试代码在`src/test/java`目录下的`com.cainiao.waybill`包中。日志配置文件为`src/main/resources`目录下的logback-spring.xml。autoconfig的配置在`src/main/resources/META-INF/autoconf/auto-config.xml`中，属性文件antx.properties在根目录下。

## 使用方式
### 在开发工具中执行
将工程导入eclipse或者idea后，直接执行包含main方法的类`com.cainiao.waybill.Application`。

### 使用fat jar的方式
这也是pandora boot应用发布的方式。首先执行下列命令打包
   
```sh
mvn package
```

如果选择了auto-config，可在命令后加

```sh 
-Dautoconfig.userProperties=waybill-bridge-start/antx.properties
```

通过-D参数指定antx.properties的位置，否则会进入autoconfig的交互模式

然后进入`waybill-bridge-start/target`目录，执行fat jar

```sh
java -Dpandora.location=${sar} -jar waybill-bridge-start-1.0.0-SNAPSHOT.jar
```

其中${sar}为sar包的路径

### 通过mvn命令直接启动
第一次调用前先要执行

```sh
mvn install
```

如果maven工程的Artifact，group id，version等都未变化，只需执行一次即可。

然后直接通过命令执行start子工程

```sh
mvn -pl waybill-bridge-start pandora-boot:run
```

以上两个命令，如果选择了auto-config，可在命令后加

```sh 
-Dautoconfig.userProperties=waybill-bridge-start/antx.properties
```

通过-D参数指定antx.properties的位置，否则会进入autoconfig的交互模式properties的位置

## aone发布
请参考文档 http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/aone-guide

## 相关链接
### pandora boot
* gitbook ： http://mw.alibaba-inc.com/products/pandoraboot/_book/
* wiki ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/home

### HSF
* gitbook ： http://mw.alibaba-inc.com/products/hsf/_book/
* wiki ： http://gitlab.alibaba-inc.com/middleware/hsf2-0/wikis/home
* HSF用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf

### TDDL
* gitbook ： http://mw.alibaba-inc.com/products/tddl/_book/
* wiki ： http://gitlab.alibaba-inc.com/middleware/tddl5-wiki/wikis/home
* TDDL用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-tddl

### notify
* 文档中心 ： http://mw.alibaba-inc.com/products/notify/_book/index.html
* 用户文档 ： http://baike.corp.taobao.com/index.php/Notify
* notify用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-notify

### tair
* 文档中心 ： http://baike.corp.taobao.com/index.php/CS_RD/tair
* tair用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-tair

### diamond
* gitbook ： http://mw.alibaba-inc.com/products/diamondserver/_book/
* wiki ： http://gitlab.alibaba-inc.com/middleware/diamond/wikis/home
* diamond用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-diamond

### sentinel
* gitbook ： http://mw.alibaba-inc.com/products/sentinel/_book/index.html
* wiki ： http://gitlab.alibaba-inc.com/middleware-asp/sentinel/wikis/brief-introduction
* diamond用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-sentinel

### alimonitor
* alimonitor地址： https://monitor.alibaba-inc.com
* alimonitor用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-alimonitor

### eagleeye
* gitbook ： http://mw.alibaba-inc.com/products/eagleeye/_book/index.html
* 用户文档 ： http://tbdocs.alibaba-inc.com/display/HSF/EagleEye
* eagleeye用法详细说明 ： http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-eagleeye