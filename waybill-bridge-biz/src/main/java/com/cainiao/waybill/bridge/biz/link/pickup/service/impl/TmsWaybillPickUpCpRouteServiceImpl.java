package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.biz.common.config.RouteThreadPoolExecutorConfig;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpAttrNameEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.RouteReachableResult;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpAttributeManager;
import com.cainiao.waybill.bridge.biz.pickup.routing.constant.RouteConstant;
import com.cainiao.waybill.bridge.biz.pickup.routing.dto.RouteBaseRequest;
import com.cainiao.waybill.bridge.biz.pickup.routing.dto.RouteBaseResult;
import com.cainiao.waybill.bridge.biz.pickup.routing.manager.RoutingReachableManager;
import com.cainiao.waybill.bridge.biz.pickup.routing.strategy.RoutingStrategy;
import com.cainiao.waybill.bridge.biz.pickup.routing.strategy.RoutingStrategyFactory;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillPickUpRouteDynSimulatorSender;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpAttributeDO;
import com.cainiao.waybill.common.util.FeatureUtils;
import com.google.common.collect.Maps;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_CP_ROUTE.AddressDTO;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_CP_ROUTE.WaybillCpRouteRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_CP_ROUTE.WaybillCpRouteResponse;
import com.taobao.pac.client.sdk.receiveservice.WaybillCpRouteService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2021/9/23-下午8:16
 */
public class TmsWaybillPickUpCpRouteServiceImpl implements WaybillCpRouteService {

    private static final String ROUTING_SUCCESS = "1";
    private static final String ROUTING_FAIL = "0";

    @Resource
    private RoutingReachableManager routingReachableManager;

    @Resource
    private WaybillPickUpAttributeManager waybillPickUpAttributeManager;

    @Resource
    private WaybillPickUpRouteDynSimulatorSender waybillPickUpRouteDynSimulatorSender;

    @Override
    public WaybillCpRouteResponse invoke(ReceiveParams<WaybillCpRouteRequest> receiveParams) {
        PickUpLogUtil.info("WaybillCpRouteRequest : " + JSON.toJSONString(receiveParams));
        WaybillCpRouteResponse routeResponse = new WaybillCpRouteResponse();
        routeResponse.setSuccess(true);

        WaybillCpRouteRequest routeRequest = receiveParams.getRequestDataObject();
        if (StringUtil.isBlank(routeRequest.getCpCode())) {
            routeResponse.setSuccess(false);
            routeResponse.setErrorCode(PickUpConstants.Error.CP_ROUTE_REQUEST_PARAM_CP_CODE_EMPTY.getErrorCode());
            routeResponse.setErrorMsg(PickUpConstants.Error.CP_ROUTE_REQUEST_PARAM_CP_CODE_EMPTY.getErrorMsg());
            return routeResponse;
        }
        if (!BridgeSwitch.cpRouteSwitch.get(routeRequest.getCpCode())) {
            routeResponse.setSuccess(false);
            routeResponse.setErrorCode(PickUpConstants.Error.CP_ROUTE_SERVICE_INVALID.getErrorCode());
            routeResponse.setErrorMsg(PickUpConstants.Error.CP_ROUTE_SERVICE_INVALID.getErrorMsg());
            return routeResponse;
        }
        AddressDTO sendAddress = routeRequest.getSendAddress();
        if (sendAddress == null || StringUtil.isBlank(sendAddress.getAddressDetail())) {
            routeResponse.setSuccess(false);
            routeResponse.setErrorCode(PickUpConstants.Error.CP_ROUTE_REQUEST_PARAM_ADDRESS_EMPTY.getErrorCode());
            routeResponse.setErrorMsg(PickUpConstants.Error.CP_ROUTE_REQUEST_PARAM_ADDRESS_EMPTY.getErrorMsg());
            return routeResponse;
        }
        com.cainiao.waybill.common.admin.dto.AddressDTO addressDTO = new com.cainiao.waybill.common.admin.dto.AddressDTO();
        BeanUtils.copyProperties(sendAddress, addressDTO);

        try {
            String mailNo = routeRequest.getMailNo();
            String cpCode = routeRequest.getCpCode();
            Map<String, String> featureMap = Maps.newHashMap();
            //  打调用菜鸟分单的标识标
            featureMap.put(RouteConstant.RoutingFeatureKey.ROUTE, "1");
            //  放置格式化的寄件地址，便于数据分析
            featureMap.putAll(FeatureUtils.parseFromString(addressDTO.getAddressFormat()));

            //  构建几种分单策略公共参数
            RouteBaseRequest routeBaseRequest = new RouteBaseRequest();
            routeBaseRequest.setCpCode(routeRequest.getCpCode());
            routeBaseRequest.setSendAddress(addressDTO);
            routeBaseRequest.setReceiveAddress(addressDTO);
            routeBaseRequest.setMailNo(mailNo);

            //  筛单判断是否可达
            RouteReachableResult routeReachableResult = routingReachableManager.routingReachable(routeBaseRequest);
            //  筛单失败进行打标，然后直接返回
            if (!routeReachableResult.isReachable()) {
                featureMap.put(RouteConstant.RoutingFeatureKey.ROUTE_FILTER_FAIL, "1");
                return getRouteFailResponse(routeResponse, mailNo, cpCode, featureMap);
            }

            //  将分单请求发送到动态仿真topic，进行异步仿真
            RouteThreadPoolExecutorConfig.getInstance().submit(()->{
                waybillPickUpRouteDynSimulatorSender.sendRouteRequest(routeBaseRequest);
            });

            //  根据配置的分单策略顺序依次执行，某个策略成功后，保存过程信息直接反馈成功
            List<String> routeStrategySequence = BridgeSwitch.routeStrategySequence;
            //  记录当前的策略组合
            featureMap.put(RouteConstant.RoutingFeatureKey.ROUTE_STRATEGY, routeStrategySequence.toString());
            for (String routeStrategyName : routeStrategySequence) {
                RoutingStrategy routingStrategy = RoutingStrategyFactory.getRoutingStrategy(routeStrategyName);
                RouteBaseResult routeBaseResult = routingStrategy.doRouting(routeBaseRequest);
                //  无论当前分单策略是否成功，都保存分单过程信息
                featureMap.putAll(routeBaseResult.getTagInfo());
                //  跟圆通技术确认过，他们判断是否直接使用菜鸟分单的结果的依据是"网点code是否有值"
                if (StringUtil.isNotBlank(routeBaseResult.getAcceptOrgCode())) {
                    if (routeBaseResult.getAcceptCourierName().length() > 3) {
                        routeBaseResult.setAcceptCourierName(BridgeSwitch.defaultCourierName.get(routeBaseRequest.getCpCode()));
                    }
                    featureMap.put(RouteConstant.RoutingFeatureKey.ROUTE_SUCCESS_TAG, ROUTING_SUCCESS);
                    insertRouteRecord(cpCode, mailNo, featureMap);
                    BeanUtils.copyProperties(routeBaseResult, routeResponse);
                    return routeResponse;
                }
            }

            //  所有分单策略都未计算出，保存分单过程信息，反馈分单未命中
            return getRouteFailResponse(routeResponse, mailNo, cpCode, featureMap);
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpConstants.Action.CP_ROUTE_LINK_ERROR.name(), "", e.getMessage(), e);
            routeResponse.setSuccess(false);
            routeResponse.setErrorMsg(e.getMessage());
        }

        return routeResponse;
    }

    @NotNull
    private WaybillCpRouteResponse getRouteFailResponse(WaybillCpRouteResponse routeResponse, String mailNo, String cpCode, Map<String, String> featureMap) {
        featureMap.put(RouteConstant.RoutingFeatureKey.ROUTE_SUCCESS_TAG, ROUTING_FAIL);
        insertRouteRecord(cpCode, mailNo, featureMap);
        routeResponse.setSuccess(false);
        routeResponse.setErrorCode(PickUpConstants.Error.CP_ROUTE_SERVICE_NOT_HIT.getErrorCode());
        routeResponse.setErrorMsg(PickUpConstants.Error.CP_ROUTE_SERVICE_NOT_HIT.getErrorMsg());
        return routeResponse;
    }

    /**
     * 插入一条"正式分单"的分单详情信息
     *
     * @param mailNo     :
     * @param featureMap :
     */
    private void insertRouteRecord(String cpCode, String mailNo, Map<String, String> featureMap) {
        ThreadPoolExecutor routeThreadPool = RouteThreadPoolExecutorConfig.getInstance();
        if (StringUtil.isNotBlank(mailNo)) {
            routeThreadPool.submit(() -> {
                WaybillPickUpAttributeDO attributeDO = new WaybillPickUpAttributeDO();
                attributeDO.setCpCode(cpCode);
                attributeDO.setMailNo(mailNo);
                attributeDO.setAttributeName(PickUpAttrNameEnum.ROUTING.name());
                attributeDO.setAttributeValue(FeatureUtils.parseFromMap(featureMap));
                waybillPickUpAttributeManager.insert(attributeDO);
            });
        }
    }

}
