package com.cainiao.waybill.bridge.web.pickup.request;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.cainiao.waybill.bridge.web.pickup.response.PickUpOrderOperateResponse;
import com.cainiao.waybill.bridge.web.pickup.response.PickUpTicketTraceResponse;
import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpTicketResponse
 * @Description
 * @Date 2022/11/29 10:47 上午
 * @Version 1.0
 */
@Data
public class PickUpTicketQueryRequest implements Serializable {

    private static final long serialVersionUID = -8572693469493072865L;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 处理方
     */
    private String dealPart;

    /**
     * 运单号列表
     */
    private List<String> mailNoList;

    /**
     * 外部订单号id列表
     */
    private List<String> outOrderIdList;

    /**
     * 平台列表
     */
    private List<String> platformList;

    /**
     * 平台类型列表
     */
    private List<String> ticketTypeList;

    /**
     * 工单状态列表
     */
    private List<String> ticketStatusList;

    /**
     * 发件人手机号列表
     */
    private List<String> orderSenderMobileList;

    /**
     * 工单来源列表
     */
    private List<String> ticketSourceList;

    /**
     * 服务商代码列表
     */
    private List<String> cpCodeList;

    /**
     * 服务商工单状态列表
     */
    private List<String> cpTicketStatusList;

    /**
     * 发件省列表
     */
    private List<String> orderSenderProvinceList;

    /**
     * 是否展示敏感字段
     */
    private Boolean showSensitive = false;

    /**
     * 是否展示工单跟踪信息
     */
    private Boolean showTicketTrace = false;
}
