package com.cainiao.waybill.bridge.biz.label.manager.impl;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.label.manager.PickupNumSequenceTairManager;
import com.cainiao.waybill.bridge.biz.wrapper.TairManagerWrapper;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.LabelError;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.util.Exceptions;
import com.cainiao.waybill.bridge.model.dao.PickupNumSequenceDAO;
import com.cainiao.waybill.bridge.model.domain.PickupNumSequenceDO;
import com.taobao.common.dao.persistence.exception.DAOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 管理揽件码序列的 tair manager
 *
 * <AUTHOR>
 * @date 2017/05/23
 */
@Component
public class PickupNumSequenceTairManagerImpl implements PickupNumSequenceTairManager {

    /**
     * 揽件码序列 tair 缓存 key 前缀。
     * 真实 key 举例： pickup_sequence_serial_key_12345
     */
    private final static String KEY_PREFIX = "pickup_sequence_serial_key_";

    /**
     * tair key 过期时间。
     * sequence 默认是不会变化的，设置为永不过期即可
     */
    private final static int EXPIRE_TIME = 0;

    private final static Logger LOGGER = LoggerFactory.getLogger(LogAppender.WAYBILL_PICKUP_CODE);

    @Resource
    private TairManagerWrapper tairManagerWrapper;

    @Resource
    private PickupNumSequenceDAO pickupNumSequenceDAO;

    @Override
    public Long getPickupNum(Long serialNum) throws DAOException, BridgeBaseException {

        Object pickupNumObj = tairManagerWrapper.get(getKey(serialNum));

        // 从数据库取出对应的 pickupNum 并存入缓存
        if (pickupNumObj == null) {
            PickupNumSequenceDO pickupNumSequenceDO = pickupNumSequenceDAO.queryBySerialNum(serialNum);
            if (pickupNumSequenceDO == null) {
                throw Exceptions.newBridgeBaseException(LabelError.PICKUP_NUM_NOT_FOUND_ERROR);
            }
            Long pickupNum = pickupNumSequenceDO.getPickupNum();
            tairManagerWrapper.put(getKey(serialNum), pickupNum, EXPIRE_TIME);
            return pickupNum;
        }

        if (pickupNumObj instanceof Long) {
            return  (Long)pickupNumObj;
        }

        LOGGER.error("pickupNumObj type is incorrect。{}", pickupNumObj);

        return null;
    }

    private String getKey(Long serialNum) {
        return KEY_PREFIX + serialNum;
    }

}
