package com.cainiao.waybill.bridge.model.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.model.charity.convert.WaybillBridgeUserInfoConverter;
import com.cainiao.waybill.bridge.model.dao.WaybillBridgeUserInfoDAO;
import com.cainiao.waybill.bridge.model.dao.bean.WaybillBridgeUserInfoParameter;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeUserInfoDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeUserInfoParam;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeUserInfoParam.Criteria;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeUserInfoDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeUserInfoMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class WaybillBridgeUserInfoDAOImpl implements WaybillBridgeUserInfoDAO {
    @Autowired
    private WaybillBridgeUserInfoMapper waybillBridgeUserInfoMapper;

    @Override
    public WaybillBridgeUserInfoDTO find(String phone) {
        WaybillBridgeUserInfoParam waybillBridgeUserInfoParam = new WaybillBridgeUserInfoParam();
        Criteria criteria = waybillBridgeUserInfoParam.createCriteria();
        criteria.andPhoneEqualTo(phone);
        criteria.andStatusEqualTo((byte)1);
        List<WaybillBridgeUserInfoDO> list = waybillBridgeUserInfoMapper.selectByParam(waybillBridgeUserInfoParam);
        if (null == list || list.isEmpty()) {
            return null;
        }
        return WaybillBridgeUserInfoConverter.convertFromDO(list.get(0));
    }

    /**
     * 列表查询
     * @param param
     */
    @Override
    public List<WaybillBridgeUserInfoDTO> list(WaybillBridgeUserInfoParameter param) {
        WaybillBridgeUserInfoParam waybillBridgeUserInfoParam = new WaybillBridgeUserInfoParam();
        Criteria criteria = waybillBridgeUserInfoParam.createCriteria();
        if(param.getId() != null){
            criteria.andIdEqualTo(param.getId());
        }
        if(param.getCainiaoUserId() != null){
            criteria.andCainiaoUserIdEqualTo(param.getCainiaoUserId());
        }
        if(param.getPhone() != null){
            criteria.andPhoneEqualTo(param.getPhone());
        }
        if(CollectionUtils.isNotEmpty(param.getIdList())){
            criteria.andIdIn(param.getIdList());
        }
        criteria.andStatusEqualTo((byte)1);
        List<WaybillBridgeUserInfoDO> list = waybillBridgeUserInfoMapper.selectByParam(waybillBridgeUserInfoParam);
        if (null == list || list.isEmpty()) {
                return null;
        }
        List<WaybillBridgeUserInfoDTO> result = new ArrayList<>();
        for (WaybillBridgeUserInfoDO record : list) {
            WaybillBridgeUserInfoDTO waybillBridgeUserInfoDTO = WaybillBridgeUserInfoConverter.convertFromDO(record);
                result.add(waybillBridgeUserInfoDTO);
        }
        return result;
    }

    /**
     * 创建
     * @param param
     */
    @Override
    public WaybillBridgeUserInfoDTO create(WaybillBridgeUserInfoParameter param) {
        WaybillBridgeUserInfoDO record = new WaybillBridgeUserInfoDO();
        record.setGmtCreate(new Date());
        record.setGmtModified(new Date());
        record.setNick(param.getNick());
        record.setPhone(param.getPhone());
        record.setCainiaoUserId(param.getCainiaoUserId());
        record.setScene(param.getScene());
        record.setStatus(param.getStatus());
        record.setRole(param.getRole());
        record.setFeature(param.getFeature());
        waybillBridgeUserInfoMapper.insert(record);
        return WaybillBridgeUserInfoConverter.convertFromDO(record);
    }

    /**
     * 修改
     * @param dto
     * @param param
     */
    @Override
    public void updateSelective(WaybillBridgeUserInfoDTO dto, WaybillBridgeUserInfoParameter param) {
        WaybillBridgeUserInfoDO record = WaybillBridgeUserInfoConverter.convertFromDTO(dto);
        record.setGmtModified(new Date());
        WaybillBridgeUserInfoParam waybillBridgeUserInfoParam = new WaybillBridgeUserInfoParam();
        Criteria criteria = waybillBridgeUserInfoParam.createCriteria();
        if(StringUtils.isBlank(param.getPhone()) && param.getId() == null){
            throw new BridgeBusinessException("update_param_empty", "更新条件为空");
        }
        if(StringUtils.isNotBlank(param.getPhone())){
            criteria.andPhoneEqualTo(param.getPhone());
        }
        if(param.getId() != null){
            criteria.andIdEqualTo(param.getId());
        }
        waybillBridgeUserInfoMapper.updateByParamSelective(record, waybillBridgeUserInfoParam);
    }
}
