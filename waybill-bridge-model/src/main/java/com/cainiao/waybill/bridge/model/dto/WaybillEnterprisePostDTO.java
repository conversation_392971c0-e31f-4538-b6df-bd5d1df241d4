package com.cainiao.waybill.bridge.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/20
 **/
@Data
public class WaybillEnterprisePostDTO {

    /**
     * 小邮局业务唯一键
     */
    private String postId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 邮局名称
     */
    private String postName;

    /**
     * 场地主键
     */
    private String locationId;

    /**
     * 场地名称
     */
    private String locationName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段
     */
    private String feature;

    /**
     * 邮局关联的管理员名称
     */
    private List<String> adminNameList;

    /**
     * 邮局关联的管理员信息
     */
    private List<WaybillEnterpriseUserInfoDTO> adminInfoList;
}
