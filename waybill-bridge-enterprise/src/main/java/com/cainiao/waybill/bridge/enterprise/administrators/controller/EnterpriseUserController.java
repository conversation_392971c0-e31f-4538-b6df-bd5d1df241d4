package com.cainiao.waybill.bridge.enterprise.administrators.controller;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.administrators.request.*;
import com.cainiao.waybill.bridge.enterprise.administrators.response.ListAdminResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.response.UserDeptInfoResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.response.UserInfoResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.response.UserTokenResponse;
import com.cainiao.waybill.bridge.enterprise.administrators.service.DingTalkUserService;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.utils.PhoneNumberValidator;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseUserInfoDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Description;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(value = "/enterprise/user")
public class EnterpriseUserController {
    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private DingTalkUserService dingTalkUserService;

    @Autowired
    private DingTalkAuthService dingTalkAuthService;

    @PostMapping("/getAdminUserIdList")
    @Description("获取钉钉管理员列表")
    EnterpriseBaseResult<List<ListAdminResponse>> getAdminUserIdList(@RequestBody ListAdminRequest request) {
        LOGGER.info("getAdminUserIdList request:{}", JSON.toJSONString(request));
        try {
            List<ListAdminResponse> responseList = dingTalkUserService.getAdminUserIdList(request.getCorpId());
            return EnterpriseBaseResult.success(responseList);
        } catch (Exception e){
            LOGGER.error("getAdminUserIdList error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @PostMapping("/getUserInfoByCode")
    @ResponseBody
    EnterpriseBaseResult<UserInfoResponse> getUserInfoByCode(@RequestBody UserInfoRequest request) {
        LOGGER.info("getUserInfoByCode request:{}", JSON.toJSONString(request));
        try {
            UserInfoResponse response = dingTalkUserService.getUserInfoByCode(request.getCorpId(), request.getCode());
            return EnterpriseBaseResult.success(response);
        } catch (Exception e){
            LOGGER.error("getUserInfoByCode error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @PostMapping("/getDeptInfoById")
    @ResponseBody
    EnterpriseBaseResult<UserDeptInfoResponse> getDeptInfoById(@RequestBody UserDeptInfoRequest request) {
        LOGGER.info("getDeptInfoById request:{}", JSON.toJSONString(request));
        try {
            UserDeptInfoResponse response = dingTalkUserService.getUserDeptInfoById(request.getCorpId(), request.getDeptId());
            return EnterpriseBaseResult.success(response);
        } catch (Exception e){
            LOGGER.error("getDeptInfoById error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @PostMapping("/getUserInfoByUnionId")
    @ResponseBody
    EnterpriseBaseResult<UserInfoResponse> getUserInfoByUnionId(@RequestBody UserInfoRequest request) {
        LOGGER.info("getUserInfoByUnionId request:{}", JSON.toJSONString(request));
        try {
            UserInfoResponse response = dingTalkUserService.getUserInfoByUnionId(request.getCorpId(), request.getUnionId());
            return EnterpriseBaseResult.success(response);
        } catch (Exception e){
            LOGGER.error("getUserInfoByUnionId error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @PostMapping("/getUserTokenInfoByAuthCode")
    @ResponseBody
    EnterpriseBaseResult<UserTokenResponse> getUserTokenInfoByAuthCode(@RequestBody UserInfoRequest request) {
        LOGGER.info("getUserTokenInfoByAuthCode request:{}", JSON.toJSONString(request));
        try {
            UserTokenResponse response = dingTalkUserService.fetchUserInfoAuthCodeFromMiniApp(request.getCorpId(), request.getAuthCode());
            return EnterpriseBaseResult.success(response);
        } catch (Exception e){
            LOGGER.error("getUserTokenInfoByAuthCode error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @PostMapping("/page")
    @Description("分页查询用户")
    EnterpriseBaseResult<BridgePagingDTO<WaybillEnterpriseUserInfoDTO>> page(@RequestBody GetEnterpriseUserInfoRequest request) {
        LOGGER.info("page request:{}", JSON.toJSONString(request));
        try {
            return EnterpriseBaseResult.success(dingTalkUserService.pageList(request));
        } catch (Exception e) {
            LOGGER.error("page error, reason: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @PostMapping("/addUser")
    @Description("添加用户")
    EnterpriseBaseResult<Boolean> addUser(@RequestBody EnterpriseUserRequest request) {
        LOGGER.info("addAdmin request:{}", JSON.toJSONString(request));
        try {
            int addResult = dingTalkUserService.addUser(request);
            return EnterpriseBaseResult.success(addResult == 1);
        } catch (Exception e) {
            LOGGER.error("addUser error, exception:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("/deleteById")
    @Description("删除用户")
    EnterpriseBaseResult<Boolean> deleteById(@RequestParam("id") Long id) {
        try {
            int deleteResult = dingTalkUserService.deleteUserById(id);
            return EnterpriseBaseResult.success(deleteResult == 1);
        } catch (Exception e) {
            LOGGER.error("deleteById error, exception:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @PostMapping("/getUserBySearchValue")
    @Description("根据姓名/手机号/用户id查询用户信息")
    EnterpriseBaseResult<BridgePagingDTO<WaybillEnterpriseUserInfoDTO>> getUserBySearchValue(@RequestBody EnterpriseUserRequest request) {
        LOGGER.info("getUserBySearchValue request:{}", JSON.toJSONString(request));
        try {
            if (StringUtils.isEmpty(request.getSearchValue()) || StringUtils.isEmpty(request.getCorpId())) {
                return EnterpriseBaseResult.success(BridgePagingDTO.build(Lists.newArrayList(),0,
                        request.getCurrentPage(), request.getPageSize()));
            }
            BridgePagingDTO<WaybillEnterpriseUserInfoDTO> userInfoDTOList = dingTalkUserService.getUserBySearchValue(request);
            return EnterpriseBaseResult.success(userInfoDTOList);
        } catch (Exception e) {
            LOGGER.error("getUserBySearchValue error, reason:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @PostMapping("/dingTalkUserPage")
    @Description("根据用户名称获取钉钉用户")
    EnterpriseBaseResult<BridgePagingDTO<UserInfoResponse>> dingTalkUserPage(@RequestBody GetEnterpriseUserInfoRequest request) {
        LOGGER.info("dingTalkUserPage request:{}", JSON.toJSONString(request));
        try {
            return EnterpriseBaseResult.success(dingTalkUserService.getDingTalkUserInfo(request));
        } catch (Exception e) {
            LOGGER.error("dingTalkUserPage error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("/getUserById")
    @Description("根据主键id获取用户详情")
    EnterpriseBaseResult<WaybillEnterpriseUserInfoDTO> getUserById(@RequestParam("id") Long id) {
        try {
            WaybillEnterpriseUserInfoDTO waybillEnterpriseUserInfoDTO = dingTalkUserService.getById(id);
            return EnterpriseBaseResult.success(waybillEnterpriseUserInfoDTO);
        } catch (Exception e) {
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("getUserInfoByUserId")
    @Description("根据userId获取用户信息")
    EnterpriseBaseResult<WaybillEnterpriseUserInfoDTO> getUserByUserId(@RequestParam("userId") String userId, @RequestParam("corpId") String corpId) {
        LOGGER.info("getUserToDingTalk request, userId:{}, corpId:{}", userId, corpId);
        try {
            return EnterpriseBaseResult.success(dingTalkUserService.getByUserId(userId, corpId));
        } catch (Exception e) {
            LOGGER.error("getUserByUserId error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("/removeAdminById")
    @Description("移除管理员")
    EnterpriseBaseResult<Boolean> removeAdminById(@RequestParam("id") Long id) {
        try {
            int deleteResult = dingTalkUserService.removeAdminById(id);
            return EnterpriseBaseResult.success(deleteResult == 1);
        } catch (Exception e) {
            LOGGER.error("removeAdminById error, exception:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("/removeAdminByUserId")
    @Description("根据userId移除管理员")
    EnterpriseBaseResult<Boolean> removeAdminByUserId(@RequestParam("userId") String userId) {
        try {
            int deleteResult = dingTalkUserService.removeAdminByUserId(userId);
            return EnterpriseBaseResult.success(deleteResult == 1);
        } catch (Exception e) {
            LOGGER.error("removeAdminByUserId error, exception:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @PostMapping("/getUserDeptList")
    @Description("根据userId查询父部门列表")
    EnterpriseBaseResult<List<List<UserDeptInfoResponse>>> getUserDeptList(@RequestBody EnterpriseUserRequest request) {
        LOGGER.info("getUserDeptList request:{}", JSON.toJSONString(request));
        try {
            if(StringUtils.isEmpty(request.getUserId())){
                return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.PARAM_ERROR.code(), EnterpriseErrorEnum.PARAM_ERROR.describe());
            }
            List<List<UserDeptInfoResponse>> userInfoDTOList = dingTalkUserService.getDingTalkDeptListByUserId(request.getCorpId(), request.getUserId());
            return EnterpriseBaseResult.success(userInfoDTOList);
        } catch (Exception e) {
            LOGGER.error("getUserDeptList error, reason:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }
}
