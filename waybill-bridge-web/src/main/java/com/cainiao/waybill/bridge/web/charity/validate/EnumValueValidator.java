package com.cainiao.waybill.bridge.web.charity.validate;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import com.cainiao.waybill.bridge.biz.charity.constant.enums.BaseCodeEnum;

/**
 * <AUTHOR> zouping.fzp
 * @Classname EnumValueValidator
 * @Description
 * @Date 2022/8/25 4:56 下午
 * @Version 1.0
 */
public class EnumValueValidator implements ConstraintValidator<IsEnumValue, Byte> {

    private Class<? extends Enum<? extends BaseCodeEnum>> enumType;
    private boolean notNull;

    public EnumValueValidator() {
    }

    @Override
    public void initialize(IsEnumValue constraintAnnotation) {
        this.enumType = constraintAnnotation.enumType();
        notNull = constraintAnnotation.notNull();
    }

    @Override
    public boolean isValid(Byte value, ConstraintValidatorContext context) {
        if (value == null) {
            return !notNull;
        }
        return ValidatorUtil.isValidateEnumWhenNotNull(value, enumType);
    }
}
