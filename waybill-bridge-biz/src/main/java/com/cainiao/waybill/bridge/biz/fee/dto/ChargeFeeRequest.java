package com.cainiao.waybill.bridge.biz.fee.dto;

import java.util.Date;

import com.cainiao.finance.dragonhorse.common.constants.ServiceTypeEnum;
import com.cainiao.finance.dragonhorse.common.constants.TenantEnum;
import com.cainiao.finance.dragonhorse.common.constants.UserSystemEnum;
import com.taobao.util.Money;
import com.taobao.wlb.dragonhorse.charge.client.constants.ChargePeriodEnum;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 结算收费请求
 * <AUTHOR>
 * @date 2024/7/22 21:29
 **/
@Data
public class ChargeFeeRequest {

    /**
     * 来源系统编码
     * 在结算注册，由结算同学提供
     */
    @NotBlank(message = "来源系统编码不能为空")
    private String sourceSystem;

    /**
     * 服务商品编码
     * 在结算注册，由结算同学提供
     */
    @NotBlank(message = "服务商品编码不能为空")
    private String serviceItemCode;

    /**
     * 服务商品名称
     **/
    private String serviceItemName;

    /**
     * 业务单据号
     */
    @NotBlank(message = "业务单据号不能为空")
    private String serviceOrderCode;

    /**
     * 外部订单号
     * 上游业务系统自己的唯一内部ID，幂等、对账用
     */
    @NotBlank(message = "外部订单号不能为空")
    private String externalOrderId;

    /**
     * 业务时间
     */
    @NotBlank(message = "业务时间不能为空")
    private Date bizTime;

    /**
     * 扣款周期
     */
    @NotBlank(message = "扣款周期不能为空")
    private ChargePeriodEnum chargePeriod;

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;

    /**
     * 用户类型
     */
    @NotBlank(message = "用户类型不能为空")
    private String userType;

    /**
     * 服务类型
     */
    @NotBlank(message = "服务类型不能为空")
    private ServiceTypeEnum serviceType;

    /**
     * 用户体系
     */
    @NotBlank(message = "用户体系不能为空")
    private UserSystemEnum userSystem;

    /**
     * 租户信息
     **/
    @NotBlank(message = "租户信息不能为空")
    private TenantEnum tenant;

    /**
     * 组织机构
     */
    @NotBlank(message = "组织机构不能为空")
    private String orgUnit;

    /**
     * 费用项编码
     */
    @NotBlank(message = "费用项编码不能为空")
    private String feeCode;

    /**
     * 费用项名称
     */
    private String feeName;

    /**
     * 费用项类型
     * @see com.taobao.wlb.dragonhorse.charge.client.constants.FeeTypeEnum
     */
    @NotBlank(message = "费用项类型不能为空")
    private Integer feeType;

    /**
     * 支付币种
     * payCurrency币种必须和money的币种保持一致，否则校验不通过
     */
    @NotBlank(message = "支付币种不能为空")
    private String payCurrency;

    /**
     * 支付金额、支付币种
     * 币种必须和payCurrency币种保持一致，否则校验不通过
     */
    @NotBlank(message = "支付金额不能为空")
    private Money money;

    /**
     * 支付渠道
     * @see com.taobao.wlb.dragonhorse.charge.client.constants.PayTypeEnum
     */
    @NotBlank(message = "支付渠道不能为空")
    private Integer payType;

}
