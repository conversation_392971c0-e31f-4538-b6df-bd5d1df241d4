package com.cainiao.waybill.bridge.innersupport.ad.biz.manager.impl;

import com.cainiao.waybill.bridge.innersupport.ad.biz.manager.WaybillAdWorkOrderManager;
import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.AdErrorConstants;
import com.cainiao.waybill.bridge.innersupport.ad.exception.AdBusinessException;
import com.cainiao.waybill.bridge.innersupport.ad.model.convert.WaybillAdWorkOrderConverter;
import com.cainiao.waybill.bridge.innersupport.ad.model.domain.WaybillAdWorkOrderDO;
import com.cainiao.waybill.bridge.innersupport.ad.model.domain.WaybillAdWorkOrderParam;
import com.cainiao.waybill.bridge.innersupport.ad.model.dto.WaybillAdWorkOrderDTO;
import com.cainiao.waybill.bridge.innersupport.ad.model.mapper.WaybillAdWorkOrderMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Component
public class WaybillAdWorkOrderManagerImpl implements WaybillAdWorkOrderManager {

    @Resource
    private WaybillAdWorkOrderMapper waybillAdWorkOrderMapper;

    @Override
    public Long create(WaybillAdWorkOrderDTO waybillAdWorkOrderDTO) {
        WaybillAdWorkOrderDO waybillAdWorkOrderDO = WaybillAdWorkOrderConverter.INSTANCE.convertDTO2DO(waybillAdWorkOrderDTO);
        Date now = new Date();
        waybillAdWorkOrderDO.setGmtCreate(now);
        waybillAdWorkOrderDO.setGmtModified(now);
        int effectRow = waybillAdWorkOrderMapper.insert(waybillAdWorkOrderDO);
        if (effectRow != 1) {
            throw new AdBusinessException(AdErrorConstants.CREATE_FAIL.getErrorCode(), AdErrorConstants.CREATE_FAIL.getErrorMsg());
        }
        return waybillAdWorkOrderDO.getId();
    }

    @Override
    public Long countByParam(WaybillAdWorkOrderParam param) {
        return waybillAdWorkOrderMapper.countByParam(param);
    }


    @Override
    public List<WaybillAdWorkOrderDTO> queryByParam(WaybillAdWorkOrderParam param) {
        List<WaybillAdWorkOrderDTO> waybillAdWorkOrderDTOS = new ArrayList<>();
        List<WaybillAdWorkOrderDO> waybillAdWorkOrderDOS = waybillAdWorkOrderMapper.selectByParam(param);
        if (CollectionUtils.isNotEmpty(waybillAdWorkOrderDOS)) {
            waybillAdWorkOrderDOS.forEach(workOrderDO -> {
                waybillAdWorkOrderDTOS.add(WaybillAdWorkOrderConverter.INSTANCE.convertDO2DTO(workOrderDO));
            });
        }
        return waybillAdWorkOrderDTOS;
    }

    @Override
    public void update(WaybillAdWorkOrderDTO waybillAdWorkOrderDTO) {
        if(waybillAdWorkOrderDTO.getId() == null) {
            throw new AdBusinessException(AdErrorConstants.PARAM_ERROR.getErrorCode(), AdErrorConstants.PARAM_ERROR.getErrorMsg());
        }
        WaybillAdWorkOrderDO waybillAdWorkOrderDO = WaybillAdWorkOrderConverter.INSTANCE.convertDTO2DO(waybillAdWorkOrderDTO);
        Date now = new Date();
        waybillAdWorkOrderDO.setGmtModified(now);
        int effectRow = waybillAdWorkOrderMapper.updateByPrimaryKeySelective(waybillAdWorkOrderDO);
        if (effectRow != 1) {
            throw new AdBusinessException(AdErrorConstants.UPDATE_FAIL.getErrorCode(), AdErrorConstants.UPDATE_FAIL.getErrorMsg());
        }
    }

    @Override
    public void deleteById(Long id) {
        if(id == null) {
            throw new AdBusinessException(AdErrorConstants.PARAM_ERROR.getErrorCode(), AdErrorConstants.PARAM_ERROR.getErrorMsg());
        }
        int effectRow = waybillAdWorkOrderMapper.deleteByPrimaryKey(id);
        if (effectRow != 1) {
            throw new AdBusinessException(AdErrorConstants.DELETE_FAIL);
        }
    }


    @Override
    public WaybillAdWorkOrderDTO queryById(Long id) {
        WaybillAdWorkOrderDO detailDO = waybillAdWorkOrderMapper.selectByPrimaryKey(id);
        if (null == detailDO) {
            throw new AdBusinessException(AdErrorConstants.NOT_FOUND_WAYBILL_AD_WORK_ORDER_BY_ID, "id:" + id);
        }
        return WaybillAdWorkOrderConverter.INSTANCE.convertDO2DTO(detailDO);
    }
}
