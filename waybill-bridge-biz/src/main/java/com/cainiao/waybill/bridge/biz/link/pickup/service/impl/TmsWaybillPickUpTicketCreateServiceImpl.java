package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketConstant.ExtraInfoKey;
import com.cainiao.waybill.bridge.biz.pickup.dto.ticket.ExternTicketCreateRequest;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillPickUpTicketService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.google.common.collect.Maps;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_TICKET_CREATE.WaybillPickUpTicketCreateRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PICK_UP_TICKET_CREATE.WaybillPickUpTicketCreateResponse;
import com.taobao.pac.client.sdk.receiveservice.WaybillPickUpTicketCreateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 接受外部向菜鸟提交的工单
 *
 * @author: yexin
 * @date: 2022-05-06 10:41
 **/
public class TmsWaybillPickUpTicketCreateServiceImpl implements WaybillPickUpTicketCreateService {

    @Resource
    private WaybillPickUpTicketService waybillPickUpTicketService;

    @Override
    public WaybillPickUpTicketCreateResponse invoke(ReceiveParams<WaybillPickUpTicketCreateRequest> receiveParams) {
        PickUpLogUtil.info("WaybillPickUpTicketCreateRequest : " + receiveParams);
        String fromResCode = receiveParams.getCpCode();
        String fromAppKey = PickUpCommonUtil.mappingFromAppKey(receiveParams.getFromAppkey());
        WaybillPickUpTicketCreateRequest linkTicketCreateRequest = receiveParams.getRequestDataObject();
        WaybillPickUpTicketCreateResponse linkTicketCreateResponse = new WaybillPickUpTicketCreateResponse();

        try {
            if(StringUtils.isAnyBlank(linkTicketCreateRequest.getOrderChannels(), linkTicketCreateRequest.getOuterOrderCode(),
                linkTicketCreateRequest.getContent(), linkTicketCreateRequest.getType(), linkTicketCreateRequest.getTicketId())){
                throw new BridgeBaseException("REQUEST_PARAMS_INVALID","请求参数无效");
            }
            ExternTicketCreateRequest ticketCreateRequest = new ExternTicketCreateRequest();
            BeanUtils.copyProperties(linkTicketCreateRequest, ticketCreateRequest);
            ticketCreateRequest.setResCode(PickUpCommonUtil.buildResCode(fromAppKey, linkTicketCreateRequest.getOrderChannels()));
            Map<String, String> extraInfoMap = Maps.newHashMapWithExpectedSize(1);
            extraInfoMap.put(PickUpConstants.TraceFeatureKey.LINK_CP_CODE, fromResCode);
            extraInfoMap.put(ExtraInfoKey.FILE_URLS, linkTicketCreateRequest.getFiles());
            ticketCreateRequest.setExtraInfoMap(extraInfoMap);
            ticketCreateRequest.setLinkFromAppKey(fromAppKey);


            waybillPickUpTicketService.create(ticketCreateRequest);
            linkTicketCreateResponse.setSuccess(true);
            PickUpLogUtil.info("ticket create success");
        } catch (BridgeBaseException e) {
            linkTicketCreateResponse.setSuccess(false);
            linkTicketCreateResponse.setErrorCode(e.getErrorCode());
            linkTicketCreateResponse.setErrorMsg(e.getErrorMessage());
            PickUpLogUtil.errLog("", PickUpConstants.Action.PICK_UP_TICKET_CREATE_LINK.name(), e.getErrorCode(), e.getErrorMessage(), e);
        } catch (Throwable e) {
            linkTicketCreateResponse.setSuccess(false);
            linkTicketCreateResponse.setErrorMsg(e.getMessage());
            PickUpLogUtil.errLog("", PickUpConstants.Action.PICK_UP_TICKET_CREATE_LINK.name(), "", e.getMessage(), e);
        }
        return linkTicketCreateResponse;
    }
}
