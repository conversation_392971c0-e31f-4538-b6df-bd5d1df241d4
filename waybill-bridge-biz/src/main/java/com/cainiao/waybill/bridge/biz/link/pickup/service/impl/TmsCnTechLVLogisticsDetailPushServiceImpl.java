package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.cainiao.waybill.bridge.biz.logistics.dto.WaybillLogisticsDetailPushRequest;
import com.cainiao.waybill.bridge.biz.logistics.dto.WaybillLogisticsDetailPushResponse;
import com.cainiao.waybill.bridge.biz.logistics.manager.TmsWaybillLogisticDetailManager;
import com.cainiao.waybill.bridge.biz.logistics.util.LogisticsDetailMapper;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.base.ScenarioConstant;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.util.LoggerMonitorUtil;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.api.open.ReceiveService;
import com.taobao.pac.client.sdk.dataobject.response.CNTECH_LV_LOGISTICS_DETAIL_PUSH.LpcPackPubResponse;
import com.taobao.pac.client.sdk.dataobject.request.CNTECH_LV_LOGISTICS_DETAIL_PUSH.LpcPackPubRequest;

import javax.annotation.Resource;

/**
 *
 * V2 上线后 可以废弃
 * 供应链物流详情信息推送接口实现，用于接收供应链侧推送的物流轨迹，并转发推送给已方客户
 *
 * 供应链侧轨迹推送接口 CNTECH_LV_LOGISTICS_DETAIL_PUSH
 * <AUTHOR>
 * @date 2024/2/29
 */
public class TmsCnTechLVLogisticsDetailPushServiceImpl implements ReceiveService<LpcPackPubRequest, LpcPackPubResponse> {

    @Resource
    private TmsWaybillLogisticDetailManager tmsWaybillLogisticDetailManager;

    @Override
    public LpcPackPubResponse invoke(ReceiveParams<LpcPackPubRequest> receiveParams) {
        LpcPackPubResponse response = new LpcPackPubResponse();
        LpcPackPubRequest pubRequest = receiveParams.getRequestDataObject();
        try {

            LoggerMonitorUtil.start(ScenarioConstant.WAYBILL_LOGISTICS_DETAIL_PUB, pubRequest.getMailNo(),  pubRequest.getCpCode(), receiveParams.getFromAppkey());

            WaybillLogisticsDetailPushRequest pushParam = LogisticsDetailMapper.MAPPER.convertRequest(pubRequest);
            WaybillLogisticsDetailPushResponse pushResponse = tmsWaybillLogisticDetailManager.pushLogisticsDetail(pushParam);
            response =  LogisticsDetailMapper.MAPPER.convertResponse(pushResponse);
            return response;
        } catch (BridgeBaseException e) {
            response.setSuccess(false);
            response.setErrorCode(e.getErrorCode());
            response.setErrorMsg(e.getErrorMessage());
            PickUpLogUtil.errLog(pubRequest.getMailNo(), PickUpConstants.Action.LOGISTICS_DETAIL_PUB_LINK.name(), e.getErrorCode(), e.getMessage(), e);
            return response;
        }  catch (Throwable e) {
            response.setSuccess(false);
            String errorCode = BridgeErrorConstant.BaseError.UNKNOWN.getErrorCode();
            response.setErrorCode(errorCode);
            response.setErrorMsg(BridgeErrorConstant.BaseError.UNKNOWN.getErrorMsg());
            PickUpLogUtil.errLog(pubRequest.getMailNo(), PickUpConstants.Action.LOGISTICS_DETAIL_PUB_LINK.name(), errorCode, e.getMessage(), e);
            return response;
        } finally {
            LoggerMonitorUtil.end(response.isSuccess(), response.getErrorCode());
        }
    }
}
