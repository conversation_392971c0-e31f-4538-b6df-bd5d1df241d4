package com.cainiao.waybill.bridge.pickup.bean;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpOrderTraceDTO
 * @Description
 * @Date 2023/4/3 11:31 上午
 * @Version 1.0
 */
@Data
public class PickUpOrderLogisticDetailDTO implements Serializable {

    private static final long serialVersionUID = 894699476957974481L;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 服务商
     */
    private String cpCode;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 物流详情
     */
    List<PickUpOrderTraceDTO> traceList;
}
