package com.cainiao.waybill.bridge.web.charity.controller;

import java.util.List;
import java.util.function.Function;

import com.cainiao.waybill.bridge.biz.charity.constant.CharityConstant;
import com.cainiao.waybill.bridge.biz.charity.request.CharityOrgExcelImportRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityOrgSaveRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityPackageImportRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityPackagePrintRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityPackagePrintedRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityPackageQueryRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityPackageSaveRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityPackageTraceRequest;
import com.cainiao.waybill.bridge.biz.charity.response.CharityExcelResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityOrgResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityPackageExportResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityPackagePrintResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityPackagePrintedExportResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityPackageResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityPagingResponse;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityOrgService;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityPackageService;
import com.cainiao.waybill.bridge.biz.charity.util.CharityUtil;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelExportUtil;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelImportUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.AssertUtil;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.common.waybill.pickup.service.AddressCleanService;
import com.cainiao.waybill.bridge.web.charity.CharityBaseResult;
import com.cainiao.waybill.bridge.web.charity.CharityExcelUtil;
import com.cainiao.waybill.bridge.web.charity.CharityLoginUserUtil;
import com.cainiao.waybill.bridge.web.common.util.ExeclUtil;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import io.swagger.annotations.Api;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@Api(tags = "打单包裹管理")
@RestController
@RequestMapping(value = "/charity/package/")
public class WaybillCharityPackageController extends BaseController {

    @Autowired
    private WaybillCharityPackageService charityPackageService;

    @Autowired
    private AddressCleanService addressCleanService;

    @Autowired
    private WaybillCharityOrgService charityOrgService;

    /**
     * 列表查询
     */
    @ResponseBody
    @RequestMapping("/list")
    public CharityBaseResult<CharityPagingResponse<CharityPackageResponse>> list(@RequestBody CharityPackageQueryRequest request) {
        BridgeValidator.validate(request);
        CharityPagingResponse<CharityPackageResponse> result = charityPackageService.queryPackageList(request);
        return CharityBaseResult.success(result);
    }

    /**
     * 列表查询
     */
    @ResponseBody
    @RequestMapping("/export")
    public CharityBaseResult<CharityExcelResponse> export(@RequestBody CharityPackageQueryRequest request) {
        BridgeValidator.validate(request);
        request.setNeedTrace(true);
        Class tClass = CharityPackageExportResponse.class;
        if(Boolean.TRUE.equals(request.getPrint())){
            tClass = CharityPackagePrintedExportResponse.class;
        }
        String fileUrl = CharityExcelUtil.exportFile(request, "包裹列表", tClass, request1 -> charityPackageService.queryPackageList(request1));

        return CharityBaseResult.success(new CharityExcelResponse(fileUrl));
    }

    /**
     * 列表查询
     */
    @ResponseBody
    @RequestMapping("/import")
    public CharityBaseResult<CharityExcelResponse> importPackage(@RequestParam("file") MultipartFile file) {

        List<CharityPackageImportRequest> list = ExcelImportUtil.loadData(file, CharityPackageImportRequest.class,
            200);
        List<CharityPackageImportRequest> errorList = Lists.newArrayList();
        for (CharityPackageImportRequest importRequest : ListUtil.non(list)) {
            try {
                CharityPackageSaveRequest request = convert(importRequest);
                CharityLoginUserUtil.popLoginInfo(request);
                BridgeValidator.validate(request);

                charityPackageService.savePackage(request);
            } catch (BridgeBusinessException exception) {
                importRequest.setErrorInfo(exception.getErrorMessage());
                errorList.add(importRequest);
            } catch (Exception exception) {
                importRequest.setErrorInfo(exception.getMessage());
                errorList.add(importRequest);
            }
        }
        if (CollectionUtils.isEmpty(errorList)) {
            return CharityBaseResult.success();
        }
        Workbook workbook = ExcelExportUtil.build(errorList, "包裹导入失败列表", CharityPackageImportRequest.class);
        String url = ExeclUtil.exportToOSS(workbook, CharityConstant.OSS_TEMP_FILE_DIR);
        return CharityBaseResult.success(new CharityExcelResponse(url));
    }

    /**
     * 创建
     */
    @RequestMapping("/create")
    public CharityBaseResult<List<CharityPackagePrintResponse>> create(@RequestBody CharityPackageSaveRequest request) {
        BridgeValidator.validate(request);

        List<CharityPackagePrintResponse> list = charityPackageService.savePackage(request);
        return CharityBaseResult.success(list);
    }

    /**
     * 打印
     */
    @RequestMapping("/print")
    public CharityBaseResult<List<CharityPackagePrintResponse>> print(@RequestBody CharityPackagePrintRequest request) {
        BridgeValidator.validate(request);
        List<CharityPackagePrintResponse> list = charityPackageService.queryPackagePrint(request);
        return CharityBaseResult.success(list);
    }

    /**
     * 打印结果回传
     */
    @RequestMapping("/printed")
    public CharityBaseResult<List<CharityPackagePrintResponse>> printed(@RequestBody CharityPackagePrintedRequest request) {
        BridgeValidator.validate(request);
        charityPackageService.printed(request);
        return CharityBaseResult.success();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public CharityBaseResult<Void> updateSelective(@RequestBody CharityPackagePrintRequest request) {
        BridgeValidator.validate(request);
        charityPackageService.deletePackage(request);
        return CharityBaseResult.success();
    }

    /**
     * 详情，包含物流详情
     */
    @RequestMapping("/detail")
    public CharityBaseResult<CharityPackageResponse> trace(@RequestBody CharityPackageTraceRequest request) {
        BridgeValidator.validate(request);
        CharityPackageResponse response = charityPackageService.queryPackageDetail(request);
        return CharityBaseResult.success(response);
    }

    private CharityPackageSaveRequest convert(CharityPackageImportRequest request) {
        CharityPackageSaveRequest saveRequest = new CharityPackageSaveRequest();
        saveRequest.setProject(CharityUtil.getProjectByName(request.getProjectName()).getProject());
        saveRequest.setSenderName(request.getSenderName());
        saveRequest.setSenderPhone(request.getSenderPhone());
        // 查询寄件网点
        if(StringUtils.isNotBlank(request.getSenderOrgName())){
            CharityOrgResponse senderOrg = charityOrgService.findByName(saveRequest.getProject(),
                request.getSenderOrgName());
            AssertUtil.assertValidate(senderOrg == null, "寄件执行机构不存在");
            saveRequest.setSenderOrgCode(senderOrg.getOrgCode());
        }
        cleanSenderAddress(request, saveRequest);

        saveRequest.setReceiverName(request.getReceiverName());
        saveRequest.setReceiverPhone(request.getReceiverPhone());
        // 查询收件网点
        if(StringUtils.isNotBlank(request.getReceiverOrgCodeName())){
            CharityOrgResponse receiverOrg = charityOrgService.findByName(saveRequest.getProject(),
                request.getReceiverOrgCodeName());
            AssertUtil.assertValidate(receiverOrg == null, "收件执行机构不存在");
            saveRequest.setReceiverOrgCode(receiverOrg.getOrgCode());
        }
        cleanReceiver(request, saveRequest);

        saveRequest.setGoodsInfo(request.getGoodsInfo());
        saveRequest.setGoodsName(request.getGoodsName());
        saveRequest.setGoodsSpecs(request.getGoodsSpecs());
        saveRequest.setBatchInfo(request.getBatchInfo());
        if(StringUtils.isNotBlank(request.getPrice())){
            AssertUtil.assertValidate(!NumberUtils.isNumber(request.getPrice()), "物资单价必须为数字");
            AssertUtil.assertValidate(Double.parseDouble(request.getPrice()) <= 0, "物资单价必须为正数");
            saveRequest.setPrice(request.getPrice());
        }
        AssertUtil.assertValidate(StringUtils.isBlank(request.getNum()), "物资数量不能为空");
        AssertUtil.assertValidate(!NumberUtils.isNumber(request.getNum()), "物资数量必须为数字");
        saveRequest.setNum(Integer.parseInt(request.getNum()));

        return saveRequest;
    }

    private void cleanReceiver(CharityPackageImportRequest request, CharityPackageSaveRequest saveRequest) {
        AddressDTO addressDTO = new AddressDTO();
        addressDTO.setProvinceName(request.getReceiverProvinceName());
        addressDTO.setCityName(request.getReceiverCityName());
        addressDTO.setAreaName(request.getReceiverDistrictName());
        addressDTO.setTownName(request.getReceiverTownName());
        addressDTO.setAddressDetail(request.getReceiverAddressDetail());
        if(StringUtils.isBlank(addressDTO.getProvinceName()) && StringUtils.isBlank(addressDTO.getCityName())
            && StringUtils.isBlank(addressDTO.getAreaName()) && StringUtils.isBlank(addressDTO.getTownName())
            && StringUtils.isBlank(addressDTO.getAddressDetail())){
            return;
        }
        AssertUtil.assertValidate(StringUtils.isBlank(addressDTO.getProvinceName()), "补全收件省地址");
        AssertUtil.assertValidate(StringUtils.isBlank(addressDTO.getCityName()), "补全收件市地址");
        AssertUtil.assertValidate(StringUtils.isBlank(addressDTO.getAreaName()), "补全收件县地址");
        AssertUtil.assertValidate(StringUtils.isBlank(addressDTO.getAddressDetail()), "补全收件详细地址");
        try {
            AddressDTO cleanAddress = addressCleanService.clean(addressDTO);
            if (cleanAddress != null) {
                if (cleanAddress.getProvinceId() != null) {
                    saveRequest.setReceiverProvince(String.valueOf(cleanAddress.getProvinceId()));
                }
                if (cleanAddress.getCityId() != null) {
                    saveRequest.setReceiverCity(String.valueOf(cleanAddress.getCityId()));
                }
                if (cleanAddress.getAreaId() != null) {
                    saveRequest.setReceiverDistrict(String.valueOf(cleanAddress.getAreaId()));
                }
                if (cleanAddress.getTownId() != null) {
                    saveRequest.setReceiverTown(String.valueOf(cleanAddress.getTownId()));
                }
                saveRequest.setReceiverAddressDetail(cleanAddress.getAddressDetail());
            }
        } catch (BridgeBaseException e) {
            throw new BridgeBusinessException("address_clean_fail", "地址清洗错误");
        }
    }

    private void cleanSenderAddress(CharityPackageImportRequest request, CharityPackageSaveRequest saveRequest) {
        AddressDTO addressDTO = new AddressDTO();
        addressDTO.setProvinceName(request.getSenderProvinceName());
        addressDTO.setCityName(request.getSenderCityName());
        addressDTO.setAreaName(request.getSenderDistrictName());
        addressDTO.setTownName(request.getSenderTownName());
        addressDTO.setAddressDetail(request.getSenderAddressDetail());
        // 地址为空则直接返回
        if(StringUtils.isBlank(addressDTO.getProvinceName()) && StringUtils.isBlank(addressDTO.getCityName())
            && StringUtils.isBlank(addressDTO.getAreaName()) && StringUtils.isBlank(addressDTO.getTownName())
            && StringUtils.isBlank(addressDTO.getAddressDetail())){
            return;
        }
        AssertUtil.assertValidate(StringUtils.isBlank(addressDTO.getProvinceName()), "补全发件省地址");
        AssertUtil.assertValidate(StringUtils.isBlank(addressDTO.getCityName()), "补全发件市地址");
        AssertUtil.assertValidate(StringUtils.isBlank(addressDTO.getAreaName()), "补全发件县地址");
        AssertUtil.assertValidate(StringUtils.isBlank(addressDTO.getAddressDetail()), "补全发件详细地址");
        try {
            AddressDTO cleanAddress = addressCleanService.clean(addressDTO);
            if (cleanAddress != null) {
                if (cleanAddress.getProvinceId() != null) {
                    saveRequest.setSenderProvince(String.valueOf(cleanAddress.getProvinceId()));
                }
                if (cleanAddress.getCityId() != null) {
                    saveRequest.setSenderCity(String.valueOf(cleanAddress.getCityId()));
                }
                if (cleanAddress.getAreaId() != null) {
                    saveRequest.setSenderDistrict(String.valueOf(cleanAddress.getAreaId()));
                }
                if (cleanAddress.getTownId() != null) {
                    saveRequest.setSenderTown(String.valueOf(cleanAddress.getTownId()));
                }
                saveRequest.setSenderAddressDetail(cleanAddress.getAddressDetail());
            }
        } catch (BridgeBaseException e) {
            throw new BridgeBusinessException("address_clean_fail", "地址清洗错误");
        }
    }

}
