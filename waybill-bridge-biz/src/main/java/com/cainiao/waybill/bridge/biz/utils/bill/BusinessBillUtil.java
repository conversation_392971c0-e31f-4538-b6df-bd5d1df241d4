package com.cainiao.waybill.bridge.biz.utils.bill;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.cainiao.waybill.bridge.biz.pickup.dto.bill.BillDetailInfoDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.odps.PickUpFinanceBillRecordDetailDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.quote.QuoteDetailInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

/**
 * 业务侧计费账单工具
 * <AUTHOR>
 * @date 2025/5/6 15:16
 **/
public class BusinessBillUtil {

    /**
     * 计费地址级别 1:省
     */
    public static final int CHARGE_LEVEL_PROVINCE = 1;

    /**
     * 计费地址级别 2:市
     */
    public static final int CHARGE_LEVEL_CITY = 2;

    /**
     * 计费地址级别 3:区
     */
    public static final int CHARGE_LEVEL_AREA = 3;



    /**
     * 解析计费地址
     * @param sendAddress 数据格式  p:江苏省;c:苏州市;a:吴江区;t:黎里镇;ad:黎里镇云溪四季庭9栋708
     * @param chargeLevel 1:省 2:市 3:区 默认:省
     * @return
     */
    public static String parseChargeAddress(String sendAddress, int chargeLevel) {
        if(StringUtils.isBlank(sendAddress)){
            return "";
        }
        String[] addressArr = sendAddress.split(";");
        String province = "";
        String city = "";
        String area = "";
        for(String address : addressArr){
            String[] addressPair = address.split(":");
            if(addressPair.length != 2){
                continue;
            }
            if("p".equals(addressPair[0])){
                province = addressPair[1];
            }
            if("c".equals(addressPair[0])){
                city = addressPair[1];
            }
            if("a".equals(addressPair[0])){
                area = addressPair[1];
            }
        }
        if(chargeLevel == CHARGE_LEVEL_AREA){
            return province + city + area;
        } else if(chargeLevel == CHARGE_LEVEL_CITY){
            return province + city;
        } else if(chargeLevel == CHARGE_LEVEL_PROVINCE){
            return province ;
        }
        return province;
    }

    /**
     * 解析时间字符串
     * @param timeStr
     * @param desc
     * @param pattern
     * @return
     */
    public static Date parseDateStr(String timeStr, String desc, String pattern) {
        try {
            if(StringUtils.isBlank(pattern)){
                pattern = DateUtils.pattern24hh;
            }
            Date time = DateUtils.strToDate(timeStr, pattern);
            return time;
        }catch (Exception e){
            throw new BridgeBusinessException(desc + "解析失败:" + timeStr);
        }

    }

    /**
     * 过滤账单报价明细
     * @param financeBill
     * @param quoteDetailList
     * @param chargeLevel
     * @return
     */
    public static QuoteDetailInfoDTO filterQuoteDetailList(
        PickUpFinanceBillRecordDetailDTO financeBill, List<QuoteDetailInfoDTO> quoteDetailList, int chargeLevel) {
        QuoteDetailInfoDTO quoteDetailInfoDTO;
        String sendAddress = parseChargeAddress(financeBill.getSend_address(), chargeLevel);
        String receiveAddress = parseChargeAddress(financeBill.getConsignee_address(), chargeLevel);

        quoteDetailInfoDTO = quoteDetailList.stream().filter(quoteDetail -> {
            String quoteSendAddress = StringUtils.isNotBlank(quoteDetail.getSendProv()) ? quoteDetail.getSendProv() : ""
                + (StringUtils.isNotBlank(quoteDetail.getSendCity()) ? quoteDetail.getSendCity() : "")
                + (StringUtils.isNotBlank(quoteDetail.getSendArea()) ? quoteDetail.getSendArea() : "");
            String quoteReceiveAddress = StringUtils.isNotBlank(quoteDetail.getReceProv()) ? quoteDetail.getReceProv() : ""
                + (StringUtils.isNotBlank(quoteDetail.getReceCity()) ? quoteDetail.getReceCity() : "")
                + (StringUtils.isNotBlank(quoteDetail.getReceArea()) ? quoteDetail.getReceArea() : "");

            return StringUtils.equals(sendAddress, quoteSendAddress)
                && StringUtils.equals(receiveAddress, quoteReceiveAddress);
        }).findFirst().orElse(null);

        return quoteDetailInfoDTO;
    }

    /**
     * 计算原始汇总结算金额
     * @param billDetailInfoList
     * @return
     */
    public static int calcOriginalSettlementAmount(List<BillDetailInfoDTO> billDetailInfoList) {
        int originalSettlementAmount = 0;
        if (CollectionUtils.isEmpty(billDetailInfoList)) {
            return originalSettlementAmount;
        }

        List<BillDetailInfoDTO> copiedList = new ArrayList<>(billDetailInfoList);
        for (BillDetailInfoDTO billDetailInfoDTO : copiedList) {
            // 读取DTO字段时，确保值的一致性（若DTO可变，需同步或克隆）
            int itemFee = getItemFee(billDetailInfoDTO);
            originalSettlementAmount += itemFee;
        }
        return originalSettlementAmount;
    }

    /**
     * 获取计费清单明细项金额
     * @param billDetailInfoDTO
     * @return
     */
    private static int getItemFee(BillDetailInfoDTO billDetailInfoDTO) {
        if(null == billDetailInfoDTO){
            return 0;
        }
        int cpServiceFee = (null == billDetailInfoDTO.getCpServiceFee()) ? 0 : billDetailInfoDTO.getCpServiceFee();
        int freightRewardFee = (null == billDetailInfoDTO.getFreightRewardFee()) ? 0 : billDetailInfoDTO.getFreightRewardFee();
        int abnormalClaimFee = (null == billDetailInfoDTO.getAbnormalClaimFee()) ? 0 : billDetailInfoDTO.getAbnormalClaimFee();
        int userRightsFee = (null == billDetailInfoDTO.getUserRightsFee()) ? 0 : billDetailInfoDTO.getUserRightsFee();

        return cpServiceFee - freightRewardFee - abnormalClaimFee - userRightsFee;
    }

}
