package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.api.open.ReceiveService;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PACK_PUB.WaybillPackPubRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PACK_PUB.WaybillPackPubResponse;

/**
 * <AUTHOR>
 * @date 2021/6/21
 */
public class TmsWaybillPackPubReceiveServiceImpl implements ReceiveService<WaybillPackPubRequest, WaybillPackPubResponse> {


    @Override
    public WaybillPackPubResponse invoke(ReceiveParams<WaybillPackPubRequest> receiveParams) {
        PickUpLogUtil.info("waybill pack pub default receiver : " + JSON.toJSONString(receiveParams));
        WaybillPackPubResponse response = new WaybillPackPubResponse();
        response.setSuccess(true);
        return response;
    }
}
