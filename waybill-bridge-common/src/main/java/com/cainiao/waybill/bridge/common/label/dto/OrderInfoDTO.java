package com.cainiao.waybill.bridge.common.label.dto;


import com.cainiao.waybill.bridge.common.dto.BaseDTO;

import java.util.List;

/**
 * Created by guochao.tgc on 2016/6/3.
 */
public class OrderInfoDTO extends BaseDTO {

    private static final long serialVersionUID = -6707576969667513126L;

    /**
     * 订单渠道信息，如TM,TB
     */
    private String            orderChannelsType;
    /**
     * 交易订单号列表
     */
    private List<String>      tradeOrderList;

    public OrderInfoDTO() {}

    public OrderInfoDTO(String orderChannel, List<String> tradeOrderList) {
        this.orderChannelsType = orderChannel;
        this.tradeOrderList = tradeOrderList;
    }

    public String getOrderChannelsType() {
        return orderChannelsType;
    }

    public void setOrderChannelsType(String orderChannelsType) {
        this.orderChannelsType = orderChannelsType;
    }

    public List<String> getTradeOrderList() {
        return tradeOrderList;
    }

    public void setTradeOrderList(List<String> tradeOrderList) {
        this.tradeOrderList = tradeOrderList;
    }
}
