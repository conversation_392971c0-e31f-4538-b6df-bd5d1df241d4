package com.cainiao.waybill.bridge.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class WaybillPreChargeTradeListParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public WaybillPreChargeTradeListParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public WaybillPreChargeTradeListParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public WaybillPreChargeTradeListParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public WaybillPreChargeTradeListParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public WaybillPreChargeTradeListParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public WaybillPreChargeTradeListParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNull() {
            addCriterion("user_name is null");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNotNull() {
            addCriterion("user_name is not null");
            return (Criteria) this;
        }

        public Criteria andUserNameEqualTo(String value) {
            addCriterion("user_name =", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotEqualTo(String value) {
            addCriterion("user_name <>", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThan(String value) {
            addCriterion("user_name >", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("user_name >=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThan(String value) {
            addCriterion("user_name <", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanOrEqualTo(String value) {
            addCriterion("user_name <=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLike(String value) {
            addCriterion("user_name like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotLike(String value) {
            addCriterion("user_name not like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameIn(List<String> values) {
            addCriterion("user_name in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotIn(List<String> values) {
            addCriterion("user_name not in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameBetween(String value1, String value2) {
            addCriterion("user_name between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotBetween(String value1, String value2) {
            addCriterion("user_name not between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andTradeNoIsNull() {
            addCriterion("trade_no is null");
            return (Criteria) this;
        }

        public Criteria andTradeNoIsNotNull() {
            addCriterion("trade_no is not null");
            return (Criteria) this;
        }

        public Criteria andTradeNoEqualTo(String value) {
            addCriterion("trade_no =", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoNotEqualTo(String value) {
            addCriterion("trade_no <>", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoGreaterThan(String value) {
            addCriterion("trade_no >", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoGreaterThanOrEqualTo(String value) {
            addCriterion("trade_no >=", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoLessThan(String value) {
            addCriterion("trade_no <", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoLessThanOrEqualTo(String value) {
            addCriterion("trade_no <=", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoLike(String value) {
            addCriterion("trade_no like", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoNotLike(String value) {
            addCriterion("trade_no not like", value, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoIn(List<String> values) {
            addCriterion("trade_no in", values, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoNotIn(List<String> values) {
            addCriterion("trade_no not in", values, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoBetween(String value1, String value2) {
            addCriterion("trade_no between", value1, value2, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeNoNotBetween(String value1, String value2) {
            addCriterion("trade_no not between", value1, value2, "tradeNo");
            return (Criteria) this;
        }

        public Criteria andTradeTypeIsNull() {
            addCriterion("trade_type is null");
            return (Criteria) this;
        }

        public Criteria andTradeTypeIsNotNull() {
            addCriterion("trade_type is not null");
            return (Criteria) this;
        }

        public Criteria andTradeTypeEqualTo(String value) {
            addCriterion("trade_type =", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeNotEqualTo(String value) {
            addCriterion("trade_type <>", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeGreaterThan(String value) {
            addCriterion("trade_type >", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeGreaterThanOrEqualTo(String value) {
            addCriterion("trade_type >=", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeLessThan(String value) {
            addCriterion("trade_type <", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeLessThanOrEqualTo(String value) {
            addCriterion("trade_type <=", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeLike(String value) {
            addCriterion("trade_type like", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeNotLike(String value) {
            addCriterion("trade_type not like", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeIn(List<String> values) {
            addCriterion("trade_type in", values, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeNotIn(List<String> values) {
            addCriterion("trade_type not in", values, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeBetween(String value1, String value2) {
            addCriterion("trade_type between", value1, value2, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeNotBetween(String value1, String value2) {
            addCriterion("trade_type not between", value1, value2, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeAmountIsNull() {
            addCriterion("trade_amount is null");
            return (Criteria) this;
        }

        public Criteria andTradeAmountIsNotNull() {
            addCriterion("trade_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTradeAmountEqualTo(Integer value) {
            addCriterion("trade_amount =", value, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountNotEqualTo(Integer value) {
            addCriterion("trade_amount <>", value, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountGreaterThan(Integer value) {
            addCriterion("trade_amount >", value, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountGreaterThanOrEqualTo(Integer value) {
            addCriterion("trade_amount >=", value, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountLessThan(Integer value) {
            addCriterion("trade_amount <", value, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountLessThanOrEqualTo(Integer value) {
            addCriterion("trade_amount <=", value, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountIn(List<Integer> values) {
            addCriterion("trade_amount in", values, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountNotIn(List<Integer> values) {
            addCriterion("trade_amount not in", values, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountBetween(Integer value1, Integer value2) {
            addCriterion("trade_amount between", value1, value2, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeAmountNotBetween(Integer value1, Integer value2) {
            addCriterion("trade_amount not between", value1, value2, "tradeAmount");
            return (Criteria) this;
        }

        public Criteria andTradeTimeIsNull() {
            addCriterion("trade_time is null");
            return (Criteria) this;
        }

        public Criteria andTradeTimeIsNotNull() {
            addCriterion("trade_time is not null");
            return (Criteria) this;
        }

        public Criteria andTradeTimeEqualTo(Date value) {
            addCriterion("trade_time =", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeNotEqualTo(Date value) {
            addCriterion("trade_time <>", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeGreaterThan(Date value) {
            addCriterion("trade_time >", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("trade_time >=", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeLessThan(Date value) {
            addCriterion("trade_time <", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeLessThanOrEqualTo(Date value) {
            addCriterion("trade_time <=", value, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeIn(List<Date> values) {
            addCriterion("trade_time in", values, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeNotIn(List<Date> values) {
            addCriterion("trade_time not in", values, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeBetween(Date value1, Date value2) {
            addCriterion("trade_time between", value1, value2, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andTradeTimeNotBetween(Date value1, Date value2) {
            addCriterion("trade_time not between", value1, value2, "tradeTime");
            return (Criteria) this;
        }

        public Criteria andBalanceIsNull() {
            addCriterion("balance is null");
            return (Criteria) this;
        }

        public Criteria andBalanceIsNotNull() {
            addCriterion("balance is not null");
            return (Criteria) this;
        }

        public Criteria andBalanceEqualTo(Integer value) {
            addCriterion("balance =", value, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceNotEqualTo(Integer value) {
            addCriterion("balance <>", value, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceGreaterThan(Integer value) {
            addCriterion("balance >", value, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceGreaterThanOrEqualTo(Integer value) {
            addCriterion("balance >=", value, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceLessThan(Integer value) {
            addCriterion("balance <", value, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceLessThanOrEqualTo(Integer value) {
            addCriterion("balance <=", value, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceIn(List<Integer> values) {
            addCriterion("balance in", values, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceNotIn(List<Integer> values) {
            addCriterion("balance not in", values, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceBetween(Integer value1, Integer value2) {
            addCriterion("balance between", value1, value2, "balance");
            return (Criteria) this;
        }

        public Criteria andBalanceNotBetween(Integer value1, Integer value2) {
            addCriterion("balance not between", value1, value2, "balance");
            return (Criteria) this;
        }

        public Criteria andFeatureIsNull() {
            addCriterion("feature is null");
            return (Criteria) this;
        }

        public Criteria andFeatureIsNotNull() {
            addCriterion("feature is not null");
            return (Criteria) this;
        }

        public Criteria andFeatureEqualTo(String value) {
            addCriterion("feature =", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotEqualTo(String value) {
            addCriterion("feature <>", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThan(String value) {
            addCriterion("feature >", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanOrEqualTo(String value) {
            addCriterion("feature >=", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLessThan(String value) {
            addCriterion("feature <", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanOrEqualTo(String value) {
            addCriterion("feature <=", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLike(String value) {
            addCriterion("feature like", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotLike(String value) {
            addCriterion("feature not like", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureIn(List<String> values) {
            addCriterion("feature in", values, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotIn(List<String> values) {
            addCriterion("feature not in", values, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureBetween(String value1, String value2) {
            addCriterion("feature between", value1, value2, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotBetween(String value1, String value2) {
            addCriterion("feature not between", value1, value2, "feature");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id =", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id <>", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id >", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id >=", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id <", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id <=", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id like", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_id not like", value, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_id in", values, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_id not in", values, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_id between", value1, value2, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_id not between", value1, value2, "userId");
            }
            return (Criteria) this;
        }

        public Criteria andUserNameEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_name =", value, "userName");
            }
            return (Criteria) this;
        }

        public Criteria andUserNameNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_name <>", value, "userName");
            }
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_name >", value, "userName");
            }
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_name >=", value, "userName");
            }
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_name <", value, "userName");
            }
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_name <=", value, "userName");
            }
            return (Criteria) this;
        }

        public Criteria andUserNameLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_name like", value, "userName");
            }
            return (Criteria) this;
        }

        public Criteria andUserNameNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("user_name not like", value, "userName");
            }
            return (Criteria) this;
        }

        public Criteria andUserNameInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_name in", values, "userName");
            }
            return (Criteria) this;
        }

        public Criteria andUserNameNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("user_name not in", values, "userName");
            }
            return (Criteria) this;
        }

        public Criteria andUserNameBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_name between", value1, value2, "userName");
            }
            return (Criteria) this;
        }

        public Criteria andUserNameNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("user_name not between", value1, value2, "userName");
            }
            return (Criteria) this;
        }

        public Criteria andTradeNoEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_no =", value, "tradeNo");
            }
            return (Criteria) this;
        }

        public Criteria andTradeNoNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_no <>", value, "tradeNo");
            }
            return (Criteria) this;
        }

        public Criteria andTradeNoGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_no >", value, "tradeNo");
            }
            return (Criteria) this;
        }

        public Criteria andTradeNoGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_no >=", value, "tradeNo");
            }
            return (Criteria) this;
        }

        public Criteria andTradeNoLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_no <", value, "tradeNo");
            }
            return (Criteria) this;
        }

        public Criteria andTradeNoLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_no <=", value, "tradeNo");
            }
            return (Criteria) this;
        }

        public Criteria andTradeNoLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_no like", value, "tradeNo");
            }
            return (Criteria) this;
        }

        public Criteria andTradeNoNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_no not like", value, "tradeNo");
            }
            return (Criteria) this;
        }

        public Criteria andTradeNoInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("trade_no in", values, "tradeNo");
            }
            return (Criteria) this;
        }

        public Criteria andTradeNoNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("trade_no not in", values, "tradeNo");
            }
            return (Criteria) this;
        }

        public Criteria andTradeNoBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("trade_no between", value1, value2, "tradeNo");
            }
            return (Criteria) this;
        }

        public Criteria andTradeNoNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("trade_no not between", value1, value2, "tradeNo");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTypeEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_type =", value, "tradeType");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTypeNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_type <>", value, "tradeType");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTypeGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_type >", value, "tradeType");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTypeGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_type >=", value, "tradeType");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTypeLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_type <", value, "tradeType");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTypeLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_type <=", value, "tradeType");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTypeLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_type like", value, "tradeType");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTypeNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("trade_type not like", value, "tradeType");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTypeInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("trade_type in", values, "tradeType");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTypeNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("trade_type not in", values, "tradeType");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTypeBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("trade_type between", value1, value2, "tradeType");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTypeNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("trade_type not between", value1, value2, "tradeType");
            }
            return (Criteria) this;
        }

        public Criteria andTradeAmountEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("trade_amount =", value, "tradeAmount");
            }
            return (Criteria) this;
        }

        public Criteria andTradeAmountNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("trade_amount <>", value, "tradeAmount");
            }
            return (Criteria) this;
        }

        public Criteria andTradeAmountGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("trade_amount >", value, "tradeAmount");
            }
            return (Criteria) this;
        }

        public Criteria andTradeAmountGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("trade_amount >=", value, "tradeAmount");
            }
            return (Criteria) this;
        }

        public Criteria andTradeAmountLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("trade_amount <", value, "tradeAmount");
            }
            return (Criteria) this;
        }

        public Criteria andTradeAmountLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("trade_amount <=", value, "tradeAmount");
            }
            return (Criteria) this;
        }

        public Criteria andTradeAmountInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("trade_amount in", values, "tradeAmount");
            }
            return (Criteria) this;
        }

        public Criteria andTradeAmountNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("trade_amount not in", values, "tradeAmount");
            }
            return (Criteria) this;
        }

        public Criteria andTradeAmountBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("trade_amount between", value1, value2, "tradeAmount");
            }
            return (Criteria) this;
        }

        public Criteria andTradeAmountNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("trade_amount not between", value1, value2, "tradeAmount");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTimeEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("trade_time =", value, "tradeTime");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTimeNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("trade_time <>", value, "tradeTime");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTimeGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("trade_time >", value, "tradeTime");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTimeGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("trade_time >=", value, "tradeTime");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTimeLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("trade_time <", value, "tradeTime");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTimeLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("trade_time <=", value, "tradeTime");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTimeInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("trade_time in", values, "tradeTime");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTimeNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("trade_time not in", values, "tradeTime");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTimeBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("trade_time between", value1, value2, "tradeTime");
            }
            return (Criteria) this;
        }

        public Criteria andTradeTimeNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("trade_time not between", value1, value2, "tradeTime");
            }
            return (Criteria) this;
        }

        public Criteria andBalanceEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("balance =", value, "balance");
            }
            return (Criteria) this;
        }

        public Criteria andBalanceNotEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("balance <>", value, "balance");
            }
            return (Criteria) this;
        }

        public Criteria andBalanceGreaterThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("balance >", value, "balance");
            }
            return (Criteria) this;
        }

        public Criteria andBalanceGreaterThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("balance >=", value, "balance");
            }
            return (Criteria) this;
        }

        public Criteria andBalanceLessThanWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("balance <", value, "balance");
            }
            return (Criteria) this;
        }

        public Criteria andBalanceLessThanOrEqualToWhenPresent(Integer value) {
            if(value != null) {
                addCriterion("balance <=", value, "balance");
            }
            return (Criteria) this;
        }

        public Criteria andBalanceInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("balance in", values, "balance");
            }
            return (Criteria) this;
        }

        public Criteria andBalanceNotInWhenPresent(List<Integer> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("balance not in", values, "balance");
            }
            return (Criteria) this;
        }

        public Criteria andBalanceBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("balance between", value1, value2, "balance");
            }
            return (Criteria) this;
        }

        public Criteria andBalanceNotBetweenWhenPresent(Integer value1, Integer value2) {
            if(value1 != null && value2 != null){
                addCriterion("balance not between", value1, value2, "balance");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature =", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature <>", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature >", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature >=", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature <", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature <=", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature like", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("feature not like", value, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("feature in", values, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("feature not in", values, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("feature between", value1, value2, "feature");
            }
            return (Criteria) this;
        }

        public Criteria andFeatureNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("feature not between", value1, value2, "feature");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *菜鸟会员账号ID
         */
        USERID("user_id"),
        /**
         *客户名称
         */
        USERNAME("user_name"),
        /**
         *交易单号
         */
        TRADENO("trade_no"),
        /**
         *交易类型-扣款pay/调账recharge/汇总collect
         */
        TRADETYPE("trade_type"),
        /**
         *交易金额(分)
         */
        TRADEAMOUNT("trade_amount"),
        /**
         *交易时间
         */
        TRADETIME("trade_time"),
        /**
         *余额(分)
         */
        BALANCE("balance"),
        /**
         *扩展字段
         */
        FEATURE("feature");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}