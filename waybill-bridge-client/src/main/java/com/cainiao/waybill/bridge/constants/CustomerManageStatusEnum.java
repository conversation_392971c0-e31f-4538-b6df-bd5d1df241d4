package com.cainiao.waybill.bridge.constants;

/**
 * 预充值客户管控状态枚举
 * <AUTHOR>
 * @date 2024/8/23 17:39
 **/
public enum CustomerManageStatusEnum {

    /**
     * 正常
     */
    VALID("VALID", "正常"),

    /**
     * 即将管控
     */
    WARN("WARN", "即将管控"),

    /**
     * 管控中
     */
    INVALID("INVALID", "管控中")


    ;
    private String status;

    private String desc;

    CustomerManageStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static CustomerManageStatusEnum getByStatus(String status){
        for (CustomerManageStatusEnum statusEnum : CustomerManageStatusEnum.values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }
}
