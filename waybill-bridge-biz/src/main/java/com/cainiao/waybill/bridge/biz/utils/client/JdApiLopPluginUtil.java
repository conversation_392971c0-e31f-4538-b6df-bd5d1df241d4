package com.cainiao.waybill.bridge.biz.utils.client;

import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.lop.open.api.sdk.plugin.LopPlugin;
import com.lop.open.api.sdk.plugin.factory.OAuth2PluginFactory;

/**
 * 京东API插件
 * <AUTHOR>
 * @date 2024/5/28 下午5:42
 **/
public class JdApiLopPluginUtil {

    /**
     * 构造京东请求插件
     * 第一版access token将于2025-05-29 17:05:43到期，到期前需要重新获取
     * 重新获取token方法参考文档：https://alidocs.dingtalk.com/i/nodes/vy20BglGWOxjGpq0CwQM2LqrVA7depqY
     * @return
     */
    public static LopPlugin buildLopPlugin(){
        return OAuth2PluginFactory.produceLopPlugin(BridgeSwitch.JD_API_APP_KEY, BridgeSwitch.JD_API_APP_SECRET, BridgeSwitch.JD_API_ACCESS_TOKEN);
    }
}
