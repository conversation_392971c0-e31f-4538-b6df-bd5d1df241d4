package com.cainiao.waybill.bridge.biz.label.script;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
import com.cainiao.waybill.bridge.model.dao.PickupNumSequenceDAO;
import com.cainiao.waybill.bridge.model.domain.PickupNumSequenceDO;
import com.taobao.common.dao.persistence.exception.DAOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;

/**
 * Description: 初始化 pickup_num_sequence 表
 *
 * <AUTHOR> zhangsaiyong
 * @Date 2017-05-02
 */
//@Service (初始化线上完成，不需要再执行。不要随便开启)
public class InitSequenceNum implements ApplicationListener<ContextRefreshedEvent> {

    private int begin = 100000;
    private int end = 999999;

    //int BEGIN = 1;
    //int END = 9;

    private final static Logger logger = LoggerFactory.getLogger(InitSequenceNum.class);

    @Resource
    private LogManager logManager;

    @Resource
    private PickupNumSequenceDAO pickupNumSequenceDAO;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        init();
    }

    /**
     * 异步进行初始化。
     */
    private void init() {
        try {
            // 1. 查询数据库 key 为 1 的。如果有直接返回.
            PickupNumSequenceDO sequenceDO = pickupNumSequenceDAO.queryBySerialNum(1L);
            if (sequenceDO != null) {
                return;
            }

            // 2. 乱序数组
            int[] arr = createArr();
            int[] shuffledArr = shuffle(arr);

            // 3. 执行插入
            System.out.println("------init sequence- 执行开始时间：" + new Date());
            List<PickupNumSequenceDO> pickupNumSequenceDOList = new ArrayList<>(5000);
            for (int i = 0; i < shuffledArr.length; ++i) {
                PickupNumSequenceDO newDO = new PickupNumSequenceDO();
                newDO.setSerialNum(i);
                newDO.setPickupNum(shuffledArr[i]);

                pickupNumSequenceDOList.add(newDO);

                if (i % 5000 == 0) {
                    pickupNumSequenceDAO.batchInsert(pickupNumSequenceDOList);
                    pickupNumSequenceDOList = new ArrayList<>(5000);
                    logManager.logInfoMessage("InitSequenceNum","------init sequence- 执行 i=" + i + ", 时间：" + new Date(), logger);
                }
            }
            pickupNumSequenceDAO.batchInsert(pickupNumSequenceDOList);
            logManager.logInfoMessage("InitSequenceNum", "------init sequence- 执行结束时间：" + new Date(), logger);
        } catch (DAOException e) {
            logManager.logErrorMessage("InitSequenceNum", "数据库插入出错. e", new Object[]{e}, logger);
        }
    }

    private int[] createArr() {
        int count = end - begin + 1;
        int[] array = new int[count];
        for (int i = begin; i <= end; ++i) {
            int idx = i - begin;
            array[idx] = i;
        }
        return array;
    }

    public static int[] shuffle(int[] array) {
        int length = array.length;
        int[] shuffledArray = new int[length];

        int index = 0;

        java.util.Random random = new java.util.Random();
        for (int count = length; count > 0; count--) {
            int randomIndex = Math.abs(random.nextInt()) % (count);
            shuffledArray[index] = array[randomIndex];
            index += 1;
            array[randomIndex] = array[count - 1];
        }
        return shuffledArray;
    }

}
