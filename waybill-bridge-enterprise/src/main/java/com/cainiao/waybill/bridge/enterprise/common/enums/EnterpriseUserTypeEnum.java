package com.cainiao.waybill.bridge.enterprise.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2025/4/18
 **/
public enum EnterpriseUserTypeEnum {

    ADMIN(0,"管理员"),

    LOCATION_ADMIN(1,"场地管理员"),

    POST_ADMIN(2,"小邮局管理员"),

    POST_EMPLOYEE(3,"小邮局员工"),

    EMPLOYEE(4,"员工");

    public Integer status;

    public String desc;

    public Integer getStatus(){
        return status;
    }

    public String getDesc(){
        return desc;
    }

    EnterpriseUserTypeEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static Integer getStatus(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        for (EnterpriseUserTypeEnum enterpriseUserTypeEnum : EnterpriseUserTypeEnum.values()) {
            if (StringUtils.equals(enterpriseUserTypeEnum.name(), name)) {
                return enterpriseUserTypeEnum.getStatus();
            }
        }
        return null;
    }
}
