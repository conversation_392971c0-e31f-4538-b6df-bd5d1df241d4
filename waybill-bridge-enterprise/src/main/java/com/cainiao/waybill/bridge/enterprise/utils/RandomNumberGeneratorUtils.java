package com.cainiao.waybill.bridge.enterprise.utils;

import java.security.SecureRandom;
import java.util.Random;

/**
 * <AUTHOR>
 * @date 2025/5/19
 **/
public class RandomNumberGeneratorUtils {

    /**
     * 生成随机四位数字
     */
    public static int generateRandomFourDigitNumber() {
        SecureRandom secureRandom = new SecureRandom();
        return secureRandom.nextInt(9000) + 1000;
    }


    /**
     * 生成随机数字，并添加前缀
     * @param prefix 前缀
     * @param totalLength 总长度
     * @return 随机数字
     */
    public static String generateRandomNumberWithPrefix(String prefix, int totalLength) {
        if (prefix.length() >= totalLength) {
            throw new IllegalArgumentException("前缀长度不能大于或等于总长度");
        }

        // 需要生成的随机数字长度
        int randomLength = totalLength - prefix.length();
        StringBuilder stringBuilder = new StringBuilder(prefix);

        SecureRandom random = new SecureRandom();
        for (int i = 0; i < randomLength; i++) {
            int digit = random.nextInt(10); // 随机生成数字0-9
            stringBuilder.append(digit);
        }
        return stringBuilder.toString();
    }
}
