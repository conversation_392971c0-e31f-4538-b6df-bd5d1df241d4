package com.cainiao.waybill.bridge.client.request;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;

import com.cainiao.waybill.common.admin.dto.AddressDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.Range;

/**
 * 下单请求基础对象
 * <AUTHOR>
 * @date 2024/7/10 21:36
 **/
@Data
public class PickUpCreateOrderBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 外部订单编号
     */
    @NotBlank(message = "param error|外部订单号为空")
    @Length(max = 64, message = "外部订单号最大长度64")
    private String outerOrderCode;
    /**
     * 渠道  WEI_DIAN   XIAO_MI
     */
    @NotBlank(message = "param error|渠道为空")
    @Length(max = 16, message = "param error|渠道最大长度16")
    private String orderChannels;
    /**
     * 默认可以不填，由菜鸟决策
     */
    @Length(max = 16, message = "param error|cpCode最大长度16`")
    private String cpCode;

    /**
     * 寄件人姓名
     */
    @NotBlank(message = "param error|寄件人姓名为空")
    @Length(max = 32, message = "param error|寄件人姓名最大长度32")
    private String sendName;
    /**
     * 寄件人手机号
     */
    @Length(max = 32, message = "param error|寄件人手机号最大长度32")
    private String sendMobile;
    /**
     * 寄件人电话
     */
    @Length(max = 32, message = "param error|寄件人电话最大长度32")
    private String sendPhone;
    /**
     * 寄件人地址  省、市、详细地址必填
     */
    @NotNull(message = "param error|寄件人地址为空")
    private AddressDTO sendAddress;

    /**
     * 收货人姓名
     */
    @NotBlank(message = "param error|收货人姓名为空")
    @Length(max = 32, message = "param error|收货人姓名最大长度32")
    private String consigneeName;
    /**
     * 收货人手机号
     */
    @Length(max = 32, message = "param error|收货人手机号最大长度32")
    private String consigneeMobile;
    /**
     * 收货人电话
     */
    @Length(max = 32, message = "param error|收货人电话最大长度32")
    private String consigneePhone;
    /**
     * 收货人地址  省、市、详细地址必填
     */
    @NotNull(message = "param error|收货人地址为空")
    private AddressDTO consigneeAddress;

    /**
     * 0  普通业务；  1：预约时间 ，默认0
     */
    @Range(min = 0, max = 1, message = "param error|bizType只能取值0或者1")
    private Integer bizType;

    /**
     * 取件码
     */
    @Length(max = 16, message = "param error|取件码最大长度16")
    private String gotCode;

    /**
     * 备注，打印在面单上备注内容。
     */
    @Length(max = 128, message = "param error|备注最大长度128")
    private String remark;

    /**
     * 预约揽收开始时间
     */
    private Date appointGotStartTime;
    /**
     * 预约揽收结束时间
     */
    private Date appointGotEndTime;

    /**
     * 扩展字段
     */
    private String extendInfo;

}
