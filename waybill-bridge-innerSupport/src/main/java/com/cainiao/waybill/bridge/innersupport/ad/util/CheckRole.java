package com.cainiao.waybill.bridge.innersupport.ad.util;

import com.cainiao.waybill.bridge.innersupport.ad.biz.request.base.AdBaseRequest;
import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.config.diamond.WaybillAdWorkOrderDiamondConfig;
import com.cainiao.waybill.bridge.innersupport.ad.constant.emums.AdUserRoleEnum;
import com.cainiao.waybill.bridge.innersupport.ad.model.dto.WaybillAdUserDTO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CheckRole {
    public static Map<String, Object> findRoleByMobile(AdBaseRequest baseRequest) {
        WaybillAdUserDTO waybillAdUserDTO = baseRequest.getLoginUserInfo();
        String mobile = waybillAdUserDTO.getMobile();
        WaybillAdWorkOrderDiamondConfig.WaybillAdWorkOrderConfig config = WaybillAdWorkOrderDiamondConfig.getConfig();
        List<WaybillAdWorkOrderDiamondConfig.CainiaoCustomerService> cainiaoCustomerServiceList = config.getCainiaoCustomerServiceList();
        List<WaybillAdWorkOrderDiamondConfig.Advertiser> advertiserList = config.getAdvertiserList();
        List<WaybillAdWorkOrderDiamondConfig.CP> cpList = config.getCpList();
        List<Map<String, Object>> resAdvertiserList = new ArrayList<>();
        List<Map<String, Object>> resCpList = new ArrayList<>();
        Map<String, Object> advertiserNameMap = new HashMap<>();
        Map<String, Object> cpNameMap = new HashMap<>();
        // 广告主角色
        for (WaybillAdWorkOrderDiamondConfig.Advertiser advertiser : advertiserList) {
            List<String> loginMobileList = advertiser.getLoginMobileList();
            Map<String, Object> advertiserItem = new HashMap<>();
            advertiserItem.put("advertiserName", advertiser.getAdvertiserName());
            advertiserItem.put("advertiserId", advertiser.getAdvertiserId());
            if (loginMobileList != null && loginMobileList.contains(mobile)) {
                Map<String, Object> checkRoleResult = new HashMap<>();
                List<Map<String, Object>> advertisers = new ArrayList<>();
                advertisers.add(advertiserItem);
                checkRoleResult.put("userRole", AdUserRoleEnum.ADVERTISER_ROLE.getValue());
                checkRoleResult.put("advertiserList", advertisers);
                return checkRoleResult;
            } else {
                resAdvertiserList.add(advertiserItem);
            }
        }
        // CP角色
        for (WaybillAdWorkOrderDiamondConfig.CP cp : cpList) {
            List<String> loginMobileList = cp.getLoginMobileList();
            Map<String, Object> cpItem = new HashMap<>();
            cpItem.put("cpName", cp.getCpName());
            cpItem.put("cpCode", cp.getCpCode());
            if (loginMobileList != null && loginMobileList.contains(mobile)) {
                Map<String, Object> checkRoleResult = new HashMap<>();
                List<Map<String, Object>> cps = new ArrayList<>();
                cps.add(cpItem);
                checkRoleResult.put("userRole", AdUserRoleEnum.COURIER_COMPANY_ROLE.getValue());
                checkRoleResult.put("cpList", cps);
                return checkRoleResult;
            } else {
                resCpList.add(cpItem);
            }
        }
        for(Map<String, Object> item : resAdvertiserList){
            advertiserNameMap.put(item.get("advertiserId").toString(), item.get("advertiserName"));
        }
        for(Map<String, Object> item : resCpList){
            cpNameMap.put(item.get("cpCode").toString(), item.get("cpName"));
        }
        // 菜鸟客服角色
        for (WaybillAdWorkOrderDiamondConfig.CainiaoCustomerService cainiaoCustomer : cainiaoCustomerServiceList) {
            List<String> loginMobileList = cainiaoCustomer.getLoginMobileList();
            if (loginMobileList != null && loginMobileList.contains(mobile)) {
                Map<String, Object> checkRoleResult = new HashMap<>();
                checkRoleResult.put("userRole", AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue());
                checkRoleResult.put("cpList", resCpList);
                checkRoleResult.put("advertiserList", resAdvertiserList);
                checkRoleResult.put("advertiserNameMap", advertiserNameMap);
                checkRoleResult.put("cpNameMap", cpNameMap);
                return checkRoleResult;
            }
        }
        // 未知角色
        Map<String, Object> unknownRole = new HashMap<>();
        unknownRole.put("userRole", "UNKNOWN_ROLE");
        return unknownRole;
    }
}
