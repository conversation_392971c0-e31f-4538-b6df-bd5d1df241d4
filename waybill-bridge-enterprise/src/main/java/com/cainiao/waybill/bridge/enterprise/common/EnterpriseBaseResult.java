package com.cainiao.waybill.bridge.enterprise.common;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> y<PERSON><PERSON>
 * @Classname EnterpriseBaseResult
 * @Description
 * @Date 2022/8/29 2:34 下午
 * @Version 1.0
 */
@Data
public class EnterpriseBaseResult<T> implements Serializable {


    private static final long serialVersionUID = 7040616541839906652L;
    private String errorCode;
    private String errorMsg;
    private boolean success;
    private T data;

    public static <T> EnterpriseBaseResult<T> success(T data) {
        EnterpriseBaseResult<T> result = new EnterpriseBaseResult<T>();
        result.setData(data);
        result.setSuccess(true);
        return result;
    }

    public static <T> EnterpriseBaseResult<T> success() {
        EnterpriseBaseResult<T> result = new EnterpriseBaseResult<T>();
        result.setSuccess(true);
        return result;
    }

    public static <T> EnterpriseBaseResult<T> fail(T data) {
        EnterpriseBaseResult<T> result = new EnterpriseBaseResult<T>();
        result.setData(data);
        result.setSuccess(false);
        return result;
    }

    public static <T> EnterpriseBaseResult<T> bizFail(String errorCode, String errorMsg) {
        EnterpriseBaseResult<T> result = new EnterpriseBaseResult<T>();
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setErrorMsg(errorMsg);
        return result;
    }

    public static <T> EnterpriseBaseResult<T> systemFail() {
        EnterpriseBaseResult<T> result = new EnterpriseBaseResult<T>();
        result.setSuccess(false);
        result.setErrorCode("SYSTEM_ERROR");
        result.setErrorMsg("系统异常");
        return result;
    }
}
