package com.cainiao.waybill.bridge.enterprise.outbound.request;

import com.cainiao.waybill.bridge.enterprise.common.BaseRequest;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePlatformEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 该类用于处理访问令牌的响应结果。
 * <AUTHOR>
 * @date 2025-04-15 11:01:40
 */
@Data
public class OrderPickUpMessage  implements Serializable {

    private static final long serialVersionUID = -4747602203157631133L;
    /**
     * cp的名称
     */
    private String cpName;

    /**
     * 日期
     */
    private String day;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 取件码
     *
     */
    private String pickupCode;
}
