package com.cainiao.waybill.bridge.biz.fast.response;

import com.cainiao.waybill.galaxy.order.domain.enums.ApplyTypeEnum;
import com.cainiao.waybill.galaxy.order.domain.enums.TransportCapacityTypeEnums;
import com.cainiao.waybill.galaxy.payment.types.PayStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单列表字段
 */
@Data
public class FastShipmentOrderResponse implements Serializable {

    private static final long serialVersionUID = -767816535121706088L;
    /**
     * 面单号
     */
    private String waybillCode;
    /**
     * 打印机id
     */
    private String printerId;
    /**
     * 运力模式
     * @see TransportCapacityTypeEnums
     */
    private String transportCapacity;

    /**
     * 面单所属的CpCode
     */
    private String cpCode;

    /**
     * 网点名称
     */
    private String cpBranchName;
    /**
     * 小件员名称
     */
    private String courierName;

    /**
     * 小件员号码
     */
    private String courierPhone;
    /**
     * 寄件人手机号
     */
    private String sendPhone;

    /**
     * 寄件人姓名
     */
    private String sendName;
    /**
     * 发出省
     */
    private String sourceProvince;

    /**
     * 目的省
     */
    private String destinationProvince;

    /**
     * 发出市
     */
    private String sourceCity;

    /**
     * 目的市
     */
    private String destinationCity;


    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;


    /**
     * 揽收时间
     */
    private String gotTime;
    /**
     * 支付时间
     */
    private String paidTime;

    /**
     * 订单状态
     * 0, 已取号
     * 1, 已揽收
     * 2, 已签收
     */
    private String status;

    /**
     * 运力类型
     * @see ApplyTypeEnum
     */
    private String applyType;

    /**
     * 运力支付状态
     * @see PayStatusEnum
     */
    private String payStatus;

    /**
     * 总价
     */
    private Double totalPrice;

    /**
     * 扩展字段
     */
    private String feature;

    /**
     * 重量
     */
    private String weight;
    /**
     * id
     */
    private Long id;
    /**
     *  取号时,传入的用户身份信息
     */
    private String applyWaybillCodeUserIdentifier;
    /**
     *
     * 取号时，传入的用户身份信息类型
     * 0,手机号； 1，用户userId
     *
     */
    private Integer applyWaybillCodeUserIdentifierType;
    /**
     * 打单isv
     */
    private String isvKey;
    /**
     * 面单取号人的id
     * 取号的人可能是:
     * 1. 打印机商家管理员
     * 2. 被分享打印机使用权的人 (被授权人分享给第三者使用权限)
     * 3. 小件员
     */
    private Long applyWaybillCodeUserId;
    /**
     * 打印机商家管理员id
     */
    private Long printerUserId;
    /**
     * 云打印机ownerId
     */
    private Long printerOwnerId;
    /**
     * 电子面单取号账户Id
     */
    private Long waybillAccountId;
    /**
     * 网点编码
     */
    private String cpBranchCode;
    /**
     * 打印任务id
     */
    private String printTaskId;
    /**
     * 支付账单明细id
     */
    private Long payBillDetailId;
    /**
     * 打印模版url
     */
    private String templateUrl;
    /**
     * 用户自定义模版id
     */
    private String customerTemplateUrl;
    /**
     * 用户自定义模版参数定义，json
     */
    private String customerTemplateData;
    /**
     * 首重价格
     */
    private Integer firstPrice;
    /**
     * 打印机owner收款账号id
     */
    private Long printerOwnerPayeeUid;
    /**
     * 打印机商家管理员付款id
     */
    private Long printerSellerPayerUid;
    /**
     * 初始重量
     */
    private Integer initialWeight;
    /**
     * 续重量
     */
    private Integer continuedWeight;
    /**
     * 服务费
     */

    private Long serviceFee;
    /**
     * 物流详情
     */
    private Integer action;
    /**
     * 活动ID
     */
    private String activityId;
    /**
     * 优惠金额
     */
    private Long discountPrice;
    /**
     * ISV商家ID
     */
    private String sellerId;
    /**
     * 寄件人唯一标识
     */
    private String senderUnique;
    /**
     * 打印机别名
     */
    private String printerAlias;
    /**
     * 备注
     */
    private String remark;
    /**
     * 发件人地址
     */
    private String sendAddress;
    /**
     * 收件人地址
     */
    private String receiveAddress;
    /**
     * 收件人姓名
     */
    private String receiveName;
    /**
     * 收件人电话
     */
    private String receivePhone;
    /**
     * 来单宝小件员备注
     */
    private String ldbRemark;
    /**
     * 来单宝小件员核重时间
     */
    private Date checkWeightTime;



}
