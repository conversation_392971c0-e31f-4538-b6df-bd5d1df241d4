package com.cainiao.waybill.bridge.biz.fee.convert;


import com.cainiao.finance.fee.client.model.CfFeeOrderDTO;
import com.cainiao.finance.prerun.client.dto.CfRechargeDataDTO;
import com.cainiao.waybill.bridge.biz.fee.dto.ChargeFeeRequest;
import com.cainiao.waybill.bridge.biz.fee.dto.RechargeRequest;
import org.springframework.beans.BeanUtils;

/**
 * 结算对象转换
 * <AUTHOR>
 * @date 2024/7/22 21:21
 **/
public class CfFeeConvert {

    /**
     * 结算计费对象转换
     * @param chargeFeeRequest
     * @return
     */
    public static CfFeeOrderDTO convertCfChargeData(ChargeFeeRequest chargeFeeRequest){
        if(chargeFeeRequest == null){
            return null;
        }
        CfFeeOrderDTO cfFeeOrderDTO = new CfFeeOrderDTO();
        BeanUtils.copyProperties(chargeFeeRequest, cfFeeOrderDTO);
        return cfFeeOrderDTO;
    }


    /**
     * 结算重算对象转换
     * @param rechargeRequest
     * @return
     */
    public static CfRechargeDataDTO convertCfRechargeData(RechargeRequest rechargeRequest){
        if(rechargeRequest == null){
            return null;
        }
        CfRechargeDataDTO cfRechargeDataDTO = new CfRechargeDataDTO();
        BeanUtils.copyProperties(rechargeRequest, cfRechargeDataDTO);
        return cfRechargeDataDTO;
    }




}
