package com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/18-上午11:12
 */
@Data
public class OrderDetailRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 查询起始时间
     */
    private String startTime;
    /**
     * 查询终止时间
     */
    private String endTime;

    /**
     * 查询起始时间
     */
    private Date startDate;
    /**
     * 查询终止时间
     */
    private Date endDate;

    /**
     * 查询类型：所有(ALL)，取消(CANCEL)，接单(ACCEPT)，揽收(GOT)，电联(VIRTUAL)
     */
    private String type;
    /**
     * 订单来源名称(上一步后端返回的)
     */
    private String platformName;
    /**
     * CP名称(上一步后端返回的)
     */
    private String cpName;

    /**
     * 查询第几页
     */
    private Integer currentPage = 1;
    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 运单号
     */
    private String mailNo;


    /**
     * 运单号
     */
    private List<String> mailNoList;

    /**
     * 订单号
     */
    private String outerOrderCode;

    /**
     * 区别是平台侧的查询还是CP测的查询。平台侧为0，CP测为1
     */
    private int reportSide = 0;
}
