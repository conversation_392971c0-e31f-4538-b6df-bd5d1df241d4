package com.cainiao.waybill.bridge.model.dao.impl;

import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.model.dao.AccountBalanceAlarmDAO;
import com.cainiao.waybill.bridge.model.domain.AccountBalanceAlarmDO;
import com.cainiao.waybill.bridge.model.common.AbstractDAO;
import com.google.common.collect.Maps;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant;
import com.taobao.common.dao.persistence.exception.DAOException;
import com.taobao.tddl.client.sequence.exception.SequenceException;
import com.taobao.tddl.client.sequence.impl.GroupSequence;

/**
 * 余额不足告警 DAO 实现
 * <AUTHOR>
 * @since 2017/05/05
 */
public class AccountBalanceAlarmDAOImpl extends AbstractDAO implements AccountBalanceAlarmDAO {

    @Resource
    private GroupSequence accountBalanceAlarmSequence;

    private Long getId() throws DAOException {
        try {
            return accountBalanceAlarmSequence.nextValue();
        } catch (SequenceException e) {
            throw new DAOException(WaybillErrorConstant.SystemError.TDDL_SEQUENCE_ERROR.getErrorMsg(), e);
        } catch (Exception e) {
            throw new DAOException(e);
        }
    }
    
    @Override
    public AccountBalanceAlarmDO getAccountBalanceAlarm(Long cpId, String branchCode, Long accountId,
                                                        String segmentCode) throws DAOException {
        Map<String,Object> params = Maps.newHashMap();
        params.put("cpId", cpId);
        params.put("accountId", accountId);
        params.put("branchCode", branchCode);
        params.put("segmentCode", segmentCode);
        return (AccountBalanceAlarmDO)this.executeQueryForObject("accountBalanceAlarm.select", params, getDBRoute());
    }

    @Override
    public int insert(AccountBalanceAlarmDO accountBalanceAlarmDO) throws DAOException {
        long id = getId();
        accountBalanceAlarmDO.setId(id);
        return this.executeUpdate("accountBalanceAlarm.insert", accountBalanceAlarmDO, getDBRoute());
    }

    @Override
    public int updateRule(Long cpId, String branchCode, Long accountId, String segmentCode,
    			int alarmQuantity, int intervalHour, String phone) throws DAOException {
        Map<String,Object> params = Maps.newHashMap();
        params.put("cpId", cpId);
        params.put("accountId", accountId);
        params.put("branchCode", branchCode);
        params.put("alarmQuantity", alarmQuantity);
        params.put("intervalHour", intervalHour);
        params.put("segmentCode", segmentCode);
        params.put("phone", phone);
        return this.executeUpdate("accountBalanceAlarm.updateRule", params, getDBRoute());
    }

    @Override
    public int updateDate(Long cpId, String branchCode, Long accountId, String segmentCode, Date lastAlarmTime) throws DAOException {
        Map<String,Object> params = Maps.newHashMap();
        params.put("cpId",cpId);
        params.put("accountId",accountId);
        params.put("branchCode",branchCode);
        params.put("segmentCode", segmentCode);
        params.put("lastAlarmTime", lastAlarmTime);

        return this.executeUpdate("accountBalanceAlarm.updateDate", params, getDBRoute());
    }
}
