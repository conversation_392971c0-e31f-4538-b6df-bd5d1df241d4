package com.cainiao.waybill.bridge.enterprise.common.middleware;

import com.alibaba.boot.switchcenter.annotation.SwitchGroup;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.annotation.NameSpace;
import com.taobao.csp.switchcenter.bean.Switch;

import java.util.Arrays;
import java.util.List;

@SwitchGroup
@NameSpace(nameSpace = "enterpriseSwitch")
public class EnterpriseSwitch {


    @AppSwitch(des = "订单风控拦截", level = Switch.Level.p3)
    public static boolean orderRiskCheck = Boolean.TRUE;

    @AppSwitch(des = "订单内容风控校验收寄件人姓名", level = Switch.Level.p3)
    public static boolean riskCheckUserName = Boolean.TRUE;

    @AppSwitch(des = "订单内容风控校验收寄件人地址", level = Switch.Level.p3)
    public static boolean riskCheckAddress = Boolean.TRUE;

    @AppSwitch(des = "订单内容风控校验备注", level = Switch.Level.p3)
    public static boolean riskCheckRemark = Boolean.TRUE;

    @AppSwitch(des = "KFC内容违禁词风控检测词包列表", level = Switch.Level.p3)
    public static List<String> KFC_CONTENT_RISK_CHECK_PACKAGE_LIST = Arrays.asList("A639625","A642101");
}
