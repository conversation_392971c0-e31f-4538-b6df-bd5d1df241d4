package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class WaybillEnterpriseAuthDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   企业ID
     *
     *
     * @mbg.generated
     */
    private String corpId;

    /**
     * Database Column Remarks:
     *   企业套件票据
     *
     *
     * @mbg.generated
     */
    private String suiteTicket;

    /**
     * Database Column Remarks:
     *   刷新时间
     *
     *
     * @mbg.generated
     */
    private Date refreshTime;

    /**
     * Database Column Remarks:
     *   平台
     *
     *
     * @mbg.generated
     */
    private String platform;

    /**
     *
     * @return the value of waybill_enterprise_auth.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_enterprise_auth.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_enterprise_auth.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_enterprise_auth.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_enterprise_auth.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_enterprise_auth.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_enterprise_auth.corp_id
     *
     * @mbg.generated
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     *
     * @param corpId the value for waybill_enterprise_auth.corp_id
     *
     * @mbg.generated
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    /**
     *
     * @return the value of waybill_enterprise_auth.suite_ticket
     *
     * @mbg.generated
     */
    public String getSuiteTicket() {
        return suiteTicket;
    }

    /**
     *
     * @param suiteTicket the value for waybill_enterprise_auth.suite_ticket
     *
     * @mbg.generated
     */
    public void setSuiteTicket(String suiteTicket) {
        this.suiteTicket = suiteTicket;
    }

    /**
     *
     * @return the value of waybill_enterprise_auth.refresh_time
     *
     * @mbg.generated
     */
    public Date getRefreshTime() {
        return refreshTime;
    }

    /**
     *
     * @param refreshTime the value for waybill_enterprise_auth.refresh_time
     *
     * @mbg.generated
     */
    public void setRefreshTime(Date refreshTime) {
        this.refreshTime = refreshTime;
    }

    /**
     *
     * @return the value of waybill_enterprise_auth.platform
     *
     * @mbg.generated
     */
    public String getPlatform() {
        return platform;
    }

    /**
     *
     * @param platform the value for waybill_enterprise_auth.platform
     *
     * @mbg.generated
     */
    public void setPlatform(String platform) {
        this.platform = platform;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", corpId=").append(corpId);
        sb.append(", suiteTicket=").append(suiteTicket);
        sb.append(", refreshTime=").append(refreshTime);
        sb.append(", platform=").append(platform);
        sb.append("]");
        return sb.toString();
    }
}