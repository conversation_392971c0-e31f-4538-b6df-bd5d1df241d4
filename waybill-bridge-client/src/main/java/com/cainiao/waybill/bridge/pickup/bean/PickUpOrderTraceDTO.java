package com.cainiao.waybill.bridge.pickup.bean;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpOrderTraceDTO
 * @Description
 * @Date 2023/4/3 11:31 上午
 * @Version 1.0
 */
@Data
public class PickUpOrderTraceDTO implements Serializable {

    private static final long serialVersionUID = 894699476957974481L;

    /**
     * 运单号
     */
    private String mailNo;

    /**
     * 时间
     */
    private String time;

    /**
     * 淘外物流轨迹状态
     */
    private String action;

    /**
     * 状态
     */
    private String status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 详细信息描述
     */
    private String desc;

}
