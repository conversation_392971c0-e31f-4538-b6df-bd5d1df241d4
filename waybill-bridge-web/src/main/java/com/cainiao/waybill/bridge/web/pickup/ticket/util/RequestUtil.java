package com.cainiao.waybill.bridge.web.pickup.ticket.util;

import com.google.common.collect.Maps;
import lombok.SneakyThrows;

import java.lang.reflect.Field;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/4/18 下午9:31
 */
public class RequestUtil {
    @SneakyThrows
    public static Map<String, Object> convert2Map(Object request){
        Map<String, Object> map = Maps.newHashMap();
        for(Field field:request.getClass().getDeclaredFields()){
            if(field.getName().equals("serialVersionUID")){
                continue;
            }
            field.setAccessible(true);
            Object val = field.get(request);
            if(val == null){
                continue;
            }
            if(val != null && "all".equals(val.toString())){
                continue;
            }
            map.put(field.getName(),field.get(request));
        }
        return map;
    }
}
