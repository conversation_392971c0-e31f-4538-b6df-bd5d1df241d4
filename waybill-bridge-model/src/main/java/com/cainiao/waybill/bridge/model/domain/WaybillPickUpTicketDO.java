package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
/**
 *
 * <AUTHOR>
 */
public class WaybillPickUpTicketDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   运单号
     *
     *
     * @mbg.generated
     */
    private String mailNo;

    /**
     * Database Column Remarks:
     *   外部订单号
     *
     *
     * @mbg.generated
     */
    private String outerOrderCode;

    /**
     * Database Column Remarks:
     *   工单类型
     *
     *
     * @mbg.generated
     */
    private String ticketType;

    /**
     * Database Column Remarks:
     *   工单状态
     *
     *
     * @mbg.generated
     */
    private String ticketStatus;

    /**
     * Database Column Remarks:
     *   工单来源
     *
     *
     * @mbg.generated
     */
    private String source;

    /**
     * Database Column Remarks:
     *   是否处罚
     *
     *
     * @mbg.generated
     */
    private Byte isPunish;

    /**
     * Database Column Remarks:
     *   是否撤销
     *
     *
     * @mbg.generated
     */
    private Byte isCancel;

    /**
     * Database Column Remarks:
     *   是否修改重量
     *
     *
     * @mbg.generated
     */
    private Byte isUpdateWeigth;

    /**
     * Database Column Remarks:
     *   外部提交的工单编号
     *
     *
     * @mbg.generated
     */
    private String outerTicketId;

    /**
     * Database Column Remarks:
     *   扩展字段
     *
     *
     * @mbg.generated
     */
    private String extraInfo;

    /**
     * Database Column Remarks:
     *   工单内容
     *
     *
     * @mbg.generated
     */
    private String content;

    /**
     * Database Column Remarks:
     *   cpcode
     *
     *
     * @mbg.generated
     */
    private String cpCode;

    /**
     * Database Column Remarks:
     *   工单优先级
     *
     *
     * @mbg.generated
     */
    private Integer priority;

    /**
     * Database Column Remarks:
     *   一级来源
     *
     *
     * @mbg.generated
     */
    private String primarySource;

    /**
     * Database Column Remarks:
     *   省份
     *
     *
     * @mbg.generated
     */
    private String province;

    /**
     * Database Column Remarks:
     *   寄件订单状态
     *
     *
     * @mbg.generated
     */
    private String pickUpOrderStatus;

    /**
     * 处理方
     */
    private String dealPart;

    /**
     * 寄件人手机号
     */
    private String senderMobile;

    /**
     * 服务商工单状态
     */
    private String cpTicketStatus;

    /**
     * 菜鸟订单id
     */
    private String cnOrderId;

    /**
     * 订单创建方式
     */
    private String createMode;

    /**
     * 订单操作
     */
    private Integer orderOperate;

    /**
     * 工单对应订单来源
     */
    private String orderSource;

    /**
     * 服务商代理
     */
    private String agent;

    /**
     *
     * @return the value of waybill_pick_up_ticket.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_pick_up_ticket.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_pick_up_ticket.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_pick_up_ticket.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.mail_no
     *
     * @mbg.generated
     */
    public String getMailNo() {
        return mailNo;
    }

    /**
     *
     * @param mailNo the value for waybill_pick_up_ticket.mail_no
     *
     * @mbg.generated
     */
    public void setMailNo(String mailNo) {
        this.mailNo = mailNo;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.outer_order_code
     *
     * @mbg.generated
     */
    public String getOuterOrderCode() {
        return outerOrderCode;
    }

    /**
     *
     * @param outerOrderCode the value for waybill_pick_up_ticket.outer_order_code
     *
     * @mbg.generated
     */
    public void setOuterOrderCode(String outerOrderCode) {
        this.outerOrderCode = outerOrderCode;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.ticket_type
     *
     * @mbg.generated
     */
    public String getTicketType() {
        return ticketType;
    }

    /**
     *
     * @param ticketType the value for waybill_pick_up_ticket.ticket_type
     *
     * @mbg.generated
     */
    public void setTicketType(String ticketType) {
        this.ticketType = ticketType;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.ticket_status
     *
     * @mbg.generated
     */
    public String getTicketStatus() {
        return ticketStatus;
    }

    /**
     *
     * @param ticketStatus the value for waybill_pick_up_ticket.ticket_status
     *
     * @mbg.generated
     */
    public void setTicketStatus(String ticketStatus) {
        this.ticketStatus = ticketStatus;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.source
     *
     * @mbg.generated
     */
    public String getSource() {
        return source;
    }

    /**
     *
     * @param source the value for waybill_pick_up_ticket.source
     *
     * @mbg.generated
     */
    public void setSource(String source) {
        this.source = source;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.is_punish
     *
     * @mbg.generated
     */
    public Byte getIsPunish() {
        return isPunish;
    }

    /**
     *
     * @param isPunish the value for waybill_pick_up_ticket.is_punish
     *
     * @mbg.generated
     */
    public void setIsPunish(Byte isPunish) {
        this.isPunish = isPunish;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.is_cancel
     *
     * @mbg.generated
     */
    public Byte getIsCancel() {
        return isCancel;
    }

    /**
     *
     * @param isCancel the value for waybill_pick_up_ticket.is_cancel
     *
     * @mbg.generated
     */
    public void setIsCancel(Byte isCancel) {
        this.isCancel = isCancel;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.is_update_weigth
     *
     * @mbg.generated
     */
    public Byte getIsUpdateWeigth() {
        return isUpdateWeigth;
    }

    /**
     *
     * @param isUpdateWeigth the value for waybill_pick_up_ticket.is_update_weigth
     *
     * @mbg.generated
     */
    public void setIsUpdateWeigth(Byte isUpdateWeigth) {
        this.isUpdateWeigth = isUpdateWeigth;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.outer_ticket_id
     *
     * @mbg.generated
     */
    public String getOuterTicketId() {
        return outerTicketId;
    }

    /**
     *
     * @param outerTicketId the value for waybill_pick_up_ticket.outer_ticket_id
     *
     * @mbg.generated
     */
    public void setOuterTicketId(String outerTicketId) {
        this.outerTicketId = outerTicketId;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.extra_info
     *
     * @mbg.generated
     */
    public String getExtraInfo() {
        return extraInfo;
    }

    /**
     *
     * @param extraInfo the value for waybill_pick_up_ticket.extra_info
     *
     * @mbg.generated
     */
    public void setExtraInfo(String extraInfo) {
        this.extraInfo = extraInfo;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.content
     *
     * @mbg.generated
     */
    public String getContent() {
        return content;
    }

    /**
     *
     * @param content the value for waybill_pick_up_ticket.content
     *
     * @mbg.generated
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.cp_code
     *
     * @mbg.generated
     */
    public String getCpCode() {
        return cpCode;
    }

    /**
     *
     * @param cpCode the value for waybill_pick_up_ticket.cp_code
     *
     * @mbg.generated
     */
    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.priority
     *
     * @mbg.generated
     */
    public Integer getPriority() {
        return priority;
    }

    /**
     *
     * @param priority the value for waybill_pick_up_ticket.priority
     *
     * @mbg.generated
     */
    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.primary_source
     *
     * @mbg.generated
     */
    public String getPrimarySource() {
        return primarySource;
    }

    /**
     *
     * @param primarySource the value for waybill_pick_up_ticket.primary_source
     *
     * @mbg.generated
     */
    public void setPrimarySource(String primarySource) {
        this.primarySource = primarySource;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.province
     *
     * @mbg.generated
     */
    public String getProvince() {
        return province;
    }

    /**
     *
     * @param province the value for waybill_pick_up_ticket.province
     *
     * @mbg.generated
     */
    public void setProvince(String province) {
        this.province = province;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket.pick_up_order_status
     *
     * @mbg.generated
     */
    public String getPickUpOrderStatus() {
        return pickUpOrderStatus;
    }

    /**
     *
     * @param pickUpOrderStatus the value for waybill_pick_up_ticket.pick_up_order_status
     *
     * @mbg.generated
     */
    public void setPickUpOrderStatus(String pickUpOrderStatus) {
        this.pickUpOrderStatus = pickUpOrderStatus;
    }

    public String getDealPart() {
        return dealPart;
    }

    public void setDealPart(String dealPart) {
        this.dealPart = dealPart;
    }

    public String getSenderMobile() {
        return senderMobile;
    }

    public void setSenderMobile(String senderMobile) {
        this.senderMobile = senderMobile;
    }

    public String getCpTicketStatus() {
        return cpTicketStatus;
    }

    public void setCpTicketStatus(String cpTicketStatus) {
        this.cpTicketStatus = cpTicketStatus;
    }

    public String getCnOrderId() {
        return cnOrderId;
    }

    public void setCnOrderId(String cnOrderId) {
        this.cnOrderId = cnOrderId;
    }

    public String getCreateMode() {
        return createMode;
    }

    public void setCreateMode(String createMode) {
        this.createMode = createMode;
    }

    public Integer getOrderOperate() {
        return orderOperate;
    }

    public void setOrderOperate(Integer orderOperate) {
        this.orderOperate = orderOperate;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    /**
     * @return
     * @mbg.generated
     */
    @Override
    public String toString() {
        return "WaybillPickUpTicketDO{" +
            "id=" + id +
            ", gmtCreate=" + gmtCreate +
            ", gmtModified=" + gmtModified +
            ", mailNo='" + mailNo + '\'' +
            ", outerOrderCode='" + outerOrderCode + '\'' +
            ", ticketType='" + ticketType + '\'' +
            ", ticketStatus='" + ticketStatus + '\'' +
            ", source='" + source + '\'' +
            ", isPunish=" + isPunish +
            ", isCancel=" + isCancel +
            ", isUpdateWeigth=" + isUpdateWeigth +
            ", outerTicketId='" + outerTicketId + '\'' +
            ", extraInfo='" + extraInfo + '\'' +
            ", content='" + content + '\'' +
            ", cpCode='" + cpCode + '\'' +
            ", priority=" + priority +
            ", primarySource='" + primarySource + '\'' +
            ", province='" + province + '\'' +
            ", pickUpOrderStatus='" + pickUpOrderStatus + '\'' +
            ", dealPart='" + dealPart + '\'' +
            ", senderMobile='" + senderMobile + '\'' +
            ", cpTicketStatus='" + cpTicketStatus + '\'' +
            ", cnOrderId='" + cnOrderId + '\'' +
            ", createMode='" + createMode + '\'' +
            ", orderOperate=" + orderOperate +
            ", orderSource='" + orderSource + '\'' +
            ", agent='" + agent + '\'' +
            '}';
    }
}
