package com.cainiao.waybill.bridge.web.pickup.response;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpTicketResponse
 * @Description
 * @Date 2022/11/29 10:47 上午
 * @Version 1.0
 */
@Data
public class PickUpTicketResponse implements Serializable {

    private static final long serialVersionUID = -4646222379725643502L;

    /**
     * 工单id
     */
    private String ticketId;

    /**
     * 运单号
     */
    private String mailNo;

    /**
     * 菜鸟订单id
     */
    private String cnOrderId;

    /**
     * 外部订单id
     */
    private String outOrderId;

    /**
     * 工单类型
     */
    private String ticketType;

    /**
     * 工单状态
     */
    private String ticketStatus;

    /**
     * 服务商工单状态
     */
    private String cpTicketStatus;

    /**
     * 工单来源
     */
    private String ticketSource;

    /**
     * 工单创建时间
     */
    private String ticketCreateTime;

    /**
     * 工单超时类型列表
     */
    private List<String> ticketTimeoutTypeList;

    /**
     * 工单跟踪列表
     */
    private List<PickUpTicketTraceResponse> ticketTraceList;

    /**
     * 平台
     */
    private String platform;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 订单创建时间
     */
    private String orderCreateTime;

    /**
     * 订单操作列表
     */
    private List<PickUpOrderOperateResponse> orderOperateList;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 发件人
     */
    private String senderName;

    /**
     * 发件人手机号
     */
    private String senderMobile;

    /**
     * 寄件省
     */
    private String senderProvince;

    /**
     * 寄件地址
     */
    private String senderAddress;

    /**
     * 收件人名称
     */
    private String consignName;

    /**
     * 收件人手机号
     */
    private String consignMobile;

    /**
     * 收件人省份
     */
    private String consignProvince;

    /**
     * 收件人地址
     */
    private String consignAddress;

    /**
     * 预约开始时间
     */
    private String appointGotStartTime;

    /**
     * 预约结束时间
     */
    private String appointGotEndTime;

    /**
     * 物品名称
     */
    private String goodsName;

    /**
     * 取件码
     */
    private String gotCode;

    /**
     * 物流详情
     */
    private String lastLogisticsDetail;

    /**
     * 服务商订单id
     */
    private String cpOrderId;
}
