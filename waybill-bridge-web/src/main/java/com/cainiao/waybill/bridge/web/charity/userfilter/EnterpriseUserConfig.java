package com.cainiao.waybill.bridge.web.charity.userfilter;

import com.cainiao.cnuser.client.service.CnUserInfoQueryService;
import com.cainiao.waybill.bridge.biz.common.user.WaybillBridgeUserInfoService;
import com.cainiao.waybill.bridge.enterprise.authority.controller.EnterpriseAuthController;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/4/22 下午12:01
 */
@Configuration
public class EnterpriseUserConfig extends WebMvcConfigurerAdapter {

    @Resource
    private DingTalkAuthService dingTalkAuthService;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        // 注册自定义的拦截器passwordStateInterceptor
        EnterpriseUserInterceptor enterpriseUserInterceptor = new EnterpriseUserInterceptor();
        enterpriseUserInterceptor.setDingTalkAuthService(dingTalkAuthService);
        registry.addInterceptor(enterpriseUserInterceptor).addPathPatterns("/enterprise/**")
                .excludePathPatterns("/enterprise/user/getUserInfoByCode",
                        "/enterprise/user/getUserTokenInfoByAuthCode",
                        "/enterprise/user/getUserInfoByUnionId",
                        "/enterprise/auth/login", "/enterprise/vm/**");
    }
}
