package com.cainiao.waybill.bridge.biz.utils.metaq;

import com.alibaba.rocketmq.client.exception.MQClientException;

import com.alibaba.rocketmq.client.producer.SendResult;

import com.alibaba.rocketmq.common.message.Message;

import com.taobao.metaq.client.MetaProducer;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/9/10 17:47
 **/
public class MetaQMessageUtil {

    /**
     * 发送metaq消息
     * @param topic
     * @param tag
     * @param key
     * @param msgBody
     * @return
     */
    private static String sendMsg(String topic, String tag, String key, String msgBody,String producerName){
        /**
         * 一个应用创建一个Producer，由应用来维护此对象，可以设置为全局对象或者单例<br>
         * 注意：ProducerGroupName需要由应用来保证唯一<br>
         * ProducerGroup这个概念发送普通的消息时，作用不大，但是发送分布式事务消息时，比较关键，
         * 因为服务器会回查这个Group下的任意一个Producer
         */

        MetaProducer producer = MetaProducerSingleton.getInstance(producerName);

        if(StringUtils.isBlank(topic)){
            topic = "LOGISTICS_SERVICE_MESSAGE";
        }
        if(StringUtils.isBlank(tag)){
            tag = "create_order";
        }
        if(StringUtils.isBlank(msgBody)){
            msgBody = "{\"externalAccountId\":*************,\"externalAccountType\":1,\"logisticsChildOrderList\":[{\"extensionData\":{\"appointComment\":\"菜鸟裹裹承运，2024-09-11 09:00:00前为您分配快递员\",\"customerServiceContact\":\"951966\",\"gotCode\":\"7182\",\"orderDetailUrl\":\"\"},\"externalOrderList\":[{\"externalOrderId\":\"9723663\"}],\"itemId\":**********,\"orderId\":*****************,\"orderStatus\":0}],\"parentOrderId\":*****************}";
        }

        /**
         * 下面这段代码表明一个Producer对象可以发送多个topic，多个tag的消息。
         * 注意：send方法是同步调用，只要不抛异常就标识成功。但是发送成功也可会有多种状态，<br>
         * 例如消息写入Master成功，但是Slave不成功，这种情况消息属于成功，但是对于个别应用如果对消息可靠性要求极高，<br>
         * 需要对这种情况做处理。另外，消息可能会存在发送失败的情况，失败重试由应用来处理。
         */
        try {

            for (int i = 0; i < 20; i++) {
                {
                    // key，消息的Key字段是为了唯一标识消息的，方便运维排查问题。如果不设置Key，则无法定位消息丢失原因。
                    Message msg = new Message(topic,
                        tag,
                        key,
                        (msgBody).getBytes());
                    SendResult sendResult = producer.send(msg);
                    System.out.println(sendResult);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            return "发送失败";
        }

        /**
         * 应用退出时，要调用shutdown来清理资源，关闭网络连接，从MetaQ服务器上注销自己
         * 注意：我们建议应用在JBOSS、Tomcat等容器的退出钩子里调用shutdown方法
         */
        //producer.shutdown();
        return "成功";
    }

    private String start(String producerName) throws MQClientException {

        MetaProducer producer = MetaProducerSingleton.getInstance(producerName);
        producer.start();
        return "成功";

    }

    private String shutdown (String producerName) throws MQClientException {

        MetaProducer producer = MetaProducerSingleton.getInstance(producerName);
        producer.shutdown();
        return "成功";

    }


    public static void main(String[] args) throws MQClientException, InterruptedException {

        sendMsg(null, null, "*****************-1", "", "");
    }

}