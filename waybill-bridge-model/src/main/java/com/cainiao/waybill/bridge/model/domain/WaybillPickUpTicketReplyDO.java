package com.cainiao.waybill.bridge.model.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
/**
 *
 * <AUTHOR>
 */
public class WaybillPickUpTicketReplyDO implements Serializable {

    private static final long serialVersionUID = -5338080807332872394L;
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   工单唯一键id
     *
     *
     * @mbg.generated
     */
    private Long ticketId;

    /**
     * Database Column Remarks:
     *   回复内容
     *
     *
     * @mbg.generated
     */
    private String content;

    /**
     * Database Column Remarks:
     *   图片地址，多个图片逗号分隔
     *
     *
     * @mbg.generated
     */
    private String imgUrls;

    /**
     * 扩展字段
     */
    private String feature;

    /**
     * 平台级来源
     */
    private String primarySource;

    /**
     *
     * @return the value of waybill_pick_up_ticket_reply.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_pick_up_ticket_reply.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket_reply.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_pick_up_ticket_reply.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket_reply.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_pick_up_ticket_reply.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket_reply.ticket_id
     *
     * @mbg.generated
     */
    public Long getTicketId() {
        return ticketId;
    }

    /**
     *
     * @param ticketId the value for waybill_pick_up_ticket_reply.ticket_id
     *
     * @mbg.generated
     */
    public void setTicketId(Long ticketId) {
        this.ticketId = ticketId;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket_reply.content
     *
     * @mbg.generated
     */
    public String getContent() {
        return content;
    }

    /**
     *
     * @param content the value for waybill_pick_up_ticket_reply.content
     *
     * @mbg.generated
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     *
     * @return the value of waybill_pick_up_ticket_reply.img_urls
     *
     * @mbg.generated
     */
    public String getImgUrls() {
        return imgUrls;
    }

    /**
     *
     * @param imgUrls the value for waybill_pick_up_ticket_reply.img_urls
     *
     * @mbg.generated
     */
    public void setImgUrls(String imgUrls) {
        this.imgUrls = imgUrls;
    }

    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }

    public String getPrimarySource() {
        return primarySource;
    }

    public void setPrimarySource(String primarySource) {
        this.primarySource = primarySource;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", ticketId=").append(ticketId);
        sb.append(", content=").append(content);
        sb.append(", imgUrls=").append(imgUrls);
        sb.append(", feature=").append(feature);
        sb.append(", primarySource=").append(primarySource);
        sb.append("]");
        return sb.toString();
    }
}
