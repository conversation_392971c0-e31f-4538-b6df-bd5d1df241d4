package com.cainiao.waybill.bridge.common.label.service;


import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.label.dto.request.UpdateSettlementTypeRequest;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.util.Page;
import com.cainiao.waybill.bridge.common.label.dto.BranchSellerAddressRelationForBranchDTO;
import com.cainiao.waybill.bridge.common.label.dto.BranchSellerAddressRelationForSellerDTO;
import com.cainiao.waybill.bridge.common.label.dto.request.BranchSellerAddRelQueryRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.CreateBranchSellerAddRelRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.ReplaceCourierRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.UnbindCourierRequest;

import java.util.List;

/**
 * 网点商家地址绑定关系服务
 *
 * Created by z<PERSON><PERSON><PERSON><PERSON> on 2017/4/25.
 */
public interface BranchSellerAddressRelationService {
	
    /**
     * 查询网点下绑定关系列表，支持分页查询
     *
     * @param request 查询请求，cpCode，branchCode 必传
     * @param clientAppInfo
     * @return
     */
    BaseResultDTO<Page<BranchSellerAddressRelationForBranchDTO>> queryRelationsForBranch(BranchSellerAddRelQueryRequest request, ClientInfoDTO clientAppInfo);

    /**
     * 查询商家下绑定关系列表
     *
     * @param sellerId 商家id
     * @param clientAppInfo
     * @return
     */
    BaseResultDTO<List<BranchSellerAddressRelationForSellerDTO>> queryRelationsForSeller(Long sellerId, ClientInfoDTO clientAppInfo);

    /**
     * 网点操作解绑小件员
     *
     * @param request
     * @param clientAppInfo
     */
    BaseResultDTO<Void> unbind(UnbindCourierRequest request, ClientInfoDTO clientAppInfo);

    /**
     * 网点操作换绑小件员
     *
     * @param request
     * @param clientAppInfo
     * @return
     */
    BaseResultDTO<Void> replaceCourier(ReplaceCourierRequest request, ClientInfoDTO clientAppInfo);

    /**
     * 网点修改客户的结算类型
     *
     * @param request
     * @param clientAppInfo
     * @return
     */
    BaseResultDTO<Void> updateSettlementType(UpdateSettlementTypeRequest request, ClientInfoDTO clientAppInfo);

    ///**
    // * 新建网点-商家地址-小件员绑定关系
    // *
    // * @param request
    // * @param clientAppInfo
    // * @return
    // */
    //BaseResultDTO<Void> create(CreateBranchSellerAddRelRequest request, ClientInfoDTO clientAppInfo);
}
