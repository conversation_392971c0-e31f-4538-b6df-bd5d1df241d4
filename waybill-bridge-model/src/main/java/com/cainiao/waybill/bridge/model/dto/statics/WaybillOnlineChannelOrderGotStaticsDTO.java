package com.cainiao.waybill.bridge.model.dto.statics;

import java.io.Serializable;

import lombok.Data;

/**
 * 订单渠道统计信息
 * <AUTHOR> zouping.fzp
 * @Classname WaybillOnlineChannelOrderStaticsDTO
 * @Description
 * @Date 2023/11/10 15:40
 * @Version 1.0
 */
@Data
public class WaybillOnlineChannelOrderGotStaticsDTO implements Serializable {

    private static final long serialVersionUID = -7619515193945608454L;

    /**
     * 订单渠道
     */
    private String orderChannels;

    /**
     * 服务商代理
     */
    private String agent;

    /**
     * 服务商
     */
    private String cpCode;

    /**
     * 应揽收量
     */
    private Integer shouldGotNum;

    /**
     * 已揽收量
     */
    private Integer gotedNum;

    /**
     * 及时揽收量
     */
    private Integer inTimeGotNum;

}
