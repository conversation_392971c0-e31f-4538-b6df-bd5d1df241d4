package com.cainiao.waybill.bridge.innersupport.ad.constant.commom;

import lombok.Data;

/**
 * @Description 错误信息
 */
@Data
public class ErrorInfo extends BaseDTO {
    private static final long serialVersionUID = 3906155698770105997L;
    /**
     * 错误码
     */
    private String errorCode;
    /**
     * 错误信息
     */
    private String errorMsg;


    public ErrorInfo(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public ErrorInfo() {
    }

    @Override
    public String toString(){
        return "[" + errorCode + "]" + ":" + errorMsg;
    }

}
