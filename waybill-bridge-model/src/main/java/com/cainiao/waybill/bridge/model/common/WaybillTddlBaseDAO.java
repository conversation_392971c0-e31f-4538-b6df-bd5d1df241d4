package com.cainiao.waybill.bridge.model.common;


import java.util.Map;

import com.google.common.collect.ImmutableMap;
import com.taobao.cainiao.waybill.constants.WaybillConstant;
import com.taobao.tddl.client.sequence.Sequence;
import com.taobao.tddl.client.sequence.exception.SequenceException;
import com.taobao.util.CollectionUtil;

public class WaybillTddlBaseDAO {

    private Sequence waybillSequence;

    private Sequence waybillSequenceName;

    private Sequence waybillDetailSequence;

    private Sequence waybillAttributeSequence;

    private Sequence waybillLogSequence;

    private Sequence waybillLifecycleSequence;

    private Sequence waybillLifecycleNewSequence;

    private Sequence waybillUserSequence;
    
    private Sequence waybillBillsNewSequence;

    public Sequence getWaybillLifecycleNewSequence() {
        return waybillLifecycleNewSequence;
    }

    public void setWaybillLifecycleNewSequence(Sequence waybillLifecycleNewSequence) {
        this.waybillLifecycleNewSequence = waybillLifecycleNewSequence;
    }

    private Sequence sufengWaybillCodeSequence;

    private Sequence emsSequence;

    private Sequence eybSequence;

    private Sequence ytoSequence;

    private Sequence zto37Sequence;

    private Sequence htkySequence;

    private Sequence ucSequence;

    private Sequence zjs99Sequence;

    private Sequence wbzySequence;

    private Sequence wbjmSequence;

    private Sequence stoSequence;

    private Sequence gtoSequence;

    private Sequence ttkdexSequence;

    private Sequence fastSequence;

    private Sequence qfkdSequence;

    private Sequence postbSequence;

    private Sequence postb98Sequence;

    private Sequence yundaSequence;

    private Sequence dbkdSequence;

    private Sequence waybillBranchAddressSequence;

    private Sequence cainiaoWaybillSequence;

    private Sequence stoieWaybillSequence;

    private Sequence yundaOppositeSequence;

    private Map<String, Sequence> sequenceMap;

    private Sequence waybillOppositeSequence;

    private Sequence waybillSendAddressSequence;

    private Sequence waybillSubscriptionSequence;

	private Sequence waybillServiceConfigSequence;

	private Sequence waybillServiceAttributeSequence;

	private Sequence waybillServiceSubscriptionSequence;

	private Sequence waybillServiceSubscriptionLogSequence;

    private Sequence waybillSubscriptionLogSequence;
	
	private Sequence waybillSubscriptionCheckSequence;

    private Sequence cainiao4plSequence;

    private void sequenceMapInit() {
        sequenceMap = ImmutableMap.<String, Sequence>builder().put(WaybillConstant.CainiaoPartnerCode.SUNFENG, sufengWaybillCodeSequence)
                .put(WaybillConstant.CainiaoPartnerCode.ZHAIJISONG, zjs99Sequence).put(WaybillConstant.CainiaoPartnerCode.ZHONGTONG, zto37Sequence)
                .put(WaybillConstant.CainiaoPartnerCode.YUANTONG, ytoSequence).put(WaybillConstant.CainiaoPartnerCode.HUITONG, htkySequence)
                .put(WaybillConstant.CainiaoPartnerCode.EMS, emsSequence).put(WaybillConstant.CainiaoPartnerCode.EMS_EYB, eybSequence)
                .put(WaybillConstant.CainiaoPartnerCode.YOUSU, ucSequence).put(WaybillConstant.CainiaoPartnerCode.WBJM_TEST, wbjmSequence)
                .put(WaybillConstant.CainiaoPartnerCode.WBZY_TEST, wbzySequence).put(WaybillConstant.CainiaoPartnerCode.SHENTONG, stoSequence)
                .put(WaybillConstant.CainiaoPartnerCode.GUOTONG, gtoSequence).put(WaybillConstant.CainiaoPartnerCode.TTKDEX, ttkdexSequence)
                .put(WaybillConstant.CainiaoPartnerCode.FAST, fastSequence).put(WaybillConstant.CainiaoPartnerCode.QFKD, qfkdSequence)
                .put(WaybillConstant.CainiaoPartnerCode.POSTB, postb98Sequence).put(WaybillConstant.CainiaoPartnerCode.YUNDA, yundaSequence)
                .put(WaybillConstant.CainiaoPartnerCode.CAINIAO, cainiaoWaybillSequence).put(WaybillConstant.CainiaoPartnerCode.STOIE, stoieWaybillSequence)
                .put(WaybillConstant.CainiaoPartnerCode.DBKD, dbkdSequence).put(WaybillConstant.CainiaoPartnerCode.YUNDA_OPPOSITE, yundaOppositeSequence)
                .put(WaybillConstant.CainiaoPartnerCode.SUNFENG, cainiao4plSequence).build();
    }

    public long getWaybillUserId() throws SequenceException {
        return waybillUserSequence.nextValue();
    }

    public long getWaybillOppositeId() throws SequenceException {
        return waybillOppositeSequence.nextValue();
    }

    public long getWaybillLifecycleId() throws SequenceException {
        return waybillLifecycleSequence.nextValue();
    }

	public long getWaybillLifecycleNewId() throws SequenceException {
		return waybillLifecycleNewSequence.nextValue();
	}


    public long getWaybillId() throws SequenceException {
		return waybillSequence.nextValue();
	}
	
	public long getWaybillDetailId() throws SequenceException {
		return waybillDetailSequence.nextValue();
	}
	
	public long getWaybillLogId() throws SequenceException {
		return waybillLogSequence.nextValue();
	}
	
	public long getWaybillAttributeId() throws SequenceException {
		return waybillAttributeSequence.nextValue();
	}
	
	public long getWaybillBranchAddressId() throws SequenceException {
		return waybillBranchAddressSequence.nextValue();
	}

	public long getWaybillServiceConfigId() throws SequenceException {
		return waybillServiceConfigSequence.nextValue();
	}

	public long getWaybillServiceSubscriptionId() throws SequenceException {
		return waybillServiceSubscriptionSequence.nextValue();
	}

	public long getWaybillServiceAttributeId() throws SequenceException {
		return waybillServiceAttributeSequence.nextValue();
	}

	public long getWaybillServiceSubscriptionLogId() throws SequenceException {
		return waybillServiceSubscriptionLogSequence.nextValue();
	}

    public long getWaybillSubscriptionLogId() throws SequenceException {
        return waybillSubscriptionLogSequence.nextValue();
    }
	
	public long getSequenceCodeByCpCode(String cpCode) throws SequenceException {
//		if (CollectionUtil.isEmpty(sequenceMap)) {
//            sequenceMapInit();
//        }
//        if (sequenceMap.containsKey(cpCode)) {
//            return sequenceMap.get(cpCode).nextValue();
//        } else {
//            throw new SequenceException("The sequence for cpCode can not found,please init this tddl sequence! cpCode:" + cpCode);
//        }
        return getSequenceCodeByCpCode(cpCode, false);
    }

    public long getSequenceCodeByCpCode(String cpCode, boolean newRule) throws SequenceException {
        if (CollectionUtil.isEmpty(sequenceMap)) {
            sequenceMapInit();
        }
        if (sequenceMap.containsKey(cpCode)) {
            return sequenceMap.get(cpCode).nextValue();
        } else {
            throw new SequenceException("The sequence for cpCode can not found,please init this tddl sequence! cpCode:" + cpCode);
        }
    }

    public long getWaybillSequenceNameId() throws SequenceException {
        return waybillSequenceName.nextValue();
    }

    public long getWaybillSendAddressId() throws SequenceException {
        return waybillSendAddressSequence.nextValue();
    }

    /**
     * 获取顺丰的面单序列号
     *
     * @return
     * @throws SequenceException
     */
    public long getSuFengSequenceCode() throws SequenceException {
        return sufengWaybillCodeSequence.nextValue();
    }


    /**
     * 获取EMS的面单序列号
     *
     * @return
     * @throws SequenceException
     */
    public long getEMSSequenceCode() throws SequenceException {
        return emsSequence.nextValue();
    }

    /**
     * 获取EMS 经济型快递的面单序列号
     *
     * @return
     * @throws SequenceException
     */
    public long getEYBSequenceCode() throws SequenceException {
        return eybSequence.nextValue();
    }

    /**
     * 获取圆通的面单序列号
     *
     * @return
     * @throws SequenceException
     */
    public long getYTOSequenceCode() throws SequenceException {
        return ytoSequence.nextValue();
    }



    /**
     * 获取汇通快运的面单序列号
     *
     * @return
     * @throws SequenceException
     */
    public long getHTKYSequenceCode() throws SequenceException {
        return htkySequence.nextValue();
    }

    /**
     * 获取优速的面单序列号
     *
     * @return
     * @throws SequenceException
     */
    public long getUCSequenceCode() throws SequenceException {
        return ucSequence.nextValue();
    }

    /**
     * 获取直营测试服务商面单序列号
     *
     * @return
     * @throws SequenceException
     */
    public long getWBZYSequenceCode() throws SequenceException {
        return wbzySequence.nextValue();
    }

    /**
     *
     * @return
     * @throws SequenceException
     */
    public long getWBJMSequenceCode() throws SequenceException {
        return wbjmSequence.nextValue();
    }

    /**
     * 获取申通电子面单序列号
     *
     * @return
     * @throws SequenceException
     */
    public long getSTOSequenceCode() throws SequenceException {
        return stoSequence.nextValue();
    }


    /**
     * 获取国通电子面单序列号
     *
     * @return
     * @throws SequenceException
     */
    public long getGTOSequenceCode() throws SequenceException {
        return gtoSequence.nextValue();
    }

    /**
     * 获取天天快递电子面单序列号
     *
     * @return
     * @throws SequenceException
     */
    public long getTTKDEXSequenceCode() throws SequenceException {
        return ttkdexSequence.nextValue();
    }

    /**
     * 获取快捷快递电子面单号
     *
     * @return
     * @throws SequenceException
     */
    public long getFASTSequenceCode() throws SequenceException {
        return fastSequence.nextValue();
    }

    /**
     * 获取全峰快递电子面单号
     *
     * @return
     * @throws SequenceException
     */
    public long getQFKDSequenceCode() throws SequenceException {
        return qfkdSequence.nextValue();
    }



    /**
     * 获取邮政国内小包电子面单号
     *
     * @return
     * @throws SequenceException
     */
    public long getPOSTBSequenceCode() throws SequenceException {
        return postbSequence.nextValue();
    }

    /**
     * 获取韵达电子面单号
     *
     * @return
     * @throws SequenceException
     */
    public long getYUNDASequenceCode() throws SequenceException {
        return yundaSequence.nextValue();
    }

    /**
     * 获取菜鸟电子面单号
     *
     * @return
     * @throws SequenceException
     */
    public long getCainiaoWaybillSequenceCode() throws SequenceException {
        return cainiaoWaybillSequence.nextValue();
    }

    /**
     * 获取申通国际电子面单号
     *
     * @return
     * @throws SequenceException
     */
    public long getSTOIEWaybillSequenceCode() throws SequenceException {
        return stoieWaybillSequence.nextValue();
    }

    public long getWaybillBillsNewId() throws SequenceException {
		return waybillBillsNewSequence.nextValue();
	}

    /**
     * 获取韵达逆向电子面单号
     *
     * @return
     * @throws SequenceException
     */
    public long getYundaOppositeWaybillSequenceCode() throws SequenceException {
        return yundaOppositeSequence.nextValue();
    }

    /**
     * 获取cp订购表(wlb_waybill_subscription) 的id
     *
     * @return
     * @throws SequenceException
     */
    public long getWaybillSubscriptionId() throws SequenceException {
        return waybillSubscriptionSequence.nextValue();
    }

    /**
     * 获取cp重复订购检查表(wlb_waybill_subscription_check) 的id
     * 
     * @return
     * @throws SequenceException
     */
    public long getWaybillSubscriptionCheckId() throws SequenceException {
        return waybillSubscriptionCheckSequence.nextValue();
    }


    public Sequence getCainiao4plSequence() throws SequenceException {
        return cainiao4plSequence;
    }

    public Sequence getWaybillSequence() {
        return waybillSequence;
    }

    public void setWaybillLifecycleSequence(Sequence waybillLifecycleSequence) {
        this.waybillLifecycleSequence = waybillLifecycleSequence;
    }

    public void setWaybillSequence(Sequence waybillSequence) {
        this.waybillSequence = waybillSequence;
    }

    public Sequence getWaybillDetailSequence() {
        return waybillDetailSequence;
    }

    public void setWaybillDetailSequence(Sequence waybillDetailSequence) {
        this.waybillDetailSequence = waybillDetailSequence;
    }

    public Sequence getWaybillLogSequence() {
        return waybillLogSequence;
    }

    public void setWaybillLogSequence(Sequence waybillLogSequence) {
        this.waybillLogSequence = waybillLogSequence;
    }

    public void setWaybillAttributeSequence(Sequence waybillAttributeSequence) {
        this.waybillAttributeSequence = waybillAttributeSequence;
    }

    public Sequence getSufengWaybillCodeSequence() {
        return sufengWaybillCodeSequence;
    }

    public void setSufengWaybillCodeSequence(Sequence sufengWaybillCodeSequence) {
        this.sufengWaybillCodeSequence = sufengWaybillCodeSequence;
    }

    public void setEmsSequence(Sequence emsSequence) {
        this.emsSequence = emsSequence;
    }

    public void setEybSequence(Sequence eybSequence) {
        this.eybSequence = eybSequence;
    }

    public void setYtoSequence(Sequence ytoSequence) {
        this.ytoSequence = ytoSequence;
    }

    public void setZto37Sequence(Sequence zto37Sequence) {
        this.zto37Sequence = zto37Sequence;
    }

    public void setHtkySequence(Sequence htkySequence) {
        this.htkySequence = htkySequence;
    }

    public void setUcSequence(Sequence ucSequence) {
        this.ucSequence = ucSequence;
    }

    public void setWbzySequence(Sequence wbzySequence) {
        this.wbzySequence = wbzySequence;
    }

    public void setWbjmSequence(Sequence wbjmSequence) {
        this.wbjmSequence = wbjmSequence;
    }

    public void setWaybillBranchAddressSequence(Sequence waybillBranchAddressSequence) {
        this.waybillBranchAddressSequence = waybillBranchAddressSequence;
    }

    public void setStoSequence(Sequence stoSequence) {
        this.stoSequence = stoSequence;
    }

    public void setGtoSequence(Sequence gtoSequence) {
        this.gtoSequence = gtoSequence;
    }

    public void setTtkdexSequence(Sequence ttkdexSequence) {
        this.ttkdexSequence = ttkdexSequence;
    }

    public void setFastSequence(Sequence fastSequence) {
        this.fastSequence = fastSequence;
    }

    public void setQfkdSequence(Sequence qfkdSequence) {
        this.qfkdSequence = qfkdSequence;
    }

    public void setPostbSequence(Sequence postbSequence) {
        this.postbSequence = postbSequence;
    }

    public void setYundaSequence(Sequence yundaSequence) {
        this.yundaSequence = yundaSequence;
    }

    public void setYundaOppositeSequence(Sequence yundaOppositeSequence) {
        this.yundaOppositeSequence = yundaOppositeSequence;
    }

    public void setCainiaoWaybillSequence(Sequence cainiaoWaybillSequence) {
        this.cainiaoWaybillSequence = cainiaoWaybillSequence;
    }

    public void setStoieWaybillSequence(Sequence stoieWaybillSequence) {
        this.stoieWaybillSequence = stoieWaybillSequence;
    }

    public void setWaybillOppositeSequence(Sequence waybillOppositeSequence) {
        this.waybillOppositeSequence = waybillOppositeSequence;
    }

    public Sequence getDbkdSequence() {
        return dbkdSequence;
    }

    public void setDbkdSequence(Sequence dbkdSequence) {
        this.dbkdSequence = dbkdSequence;
    }

    public void setPostb98Sequence(Sequence postb98Sequence) {
        this.postb98Sequence = postb98Sequence;
    }

    public void setZjs99Sequence(Sequence zjs99Sequence) {
        this.zjs99Sequence = zjs99Sequence;
    }

    public void setWaybillSubscriptionSequence(Sequence waybillSubscriptionSequence) {
        this.waybillSubscriptionSequence = waybillSubscriptionSequence;
    }

    public void setWaybillSubscriptionCheckSequence(Sequence waybillSubscriptionCheckSequence) {
        this.waybillSubscriptionCheckSequence = waybillSubscriptionCheckSequence;
    }

    public void setWaybillSequenceName(Sequence waybillSequenceName) {
        this.waybillSequenceName = waybillSequenceName;
    }

    public void setWaybillSendAddressSequence(Sequence waybillSendAddressSequence) {
        this.waybillSendAddressSequence = waybillSendAddressSequence;
    }

    public Sequence getWaybillUserSequence() {
        return waybillUserSequence;
    }

    public void setWaybillUserSequence(Sequence waybillUserSequence) {
        this.waybillUserSequence = waybillUserSequence;
    }

	public void setCainiao4plSequence(Sequence cainiao4plSequence) {
		this.cainiao4plSequence = cainiao4plSequence;
	}

	public void setWaybillServiceConfigSequence(Sequence waybillServiceConfigSequence) {
		this.waybillServiceConfigSequence = waybillServiceConfigSequence;
	}

	public void setWaybillServiceAttributeSequence(Sequence waybillServiceAttributeSequence) {
		this.waybillServiceAttributeSequence = waybillServiceAttributeSequence;
	}

	public void setWaybillServiceSubscriptionSequence(Sequence waybillServiceSubscriptionSequence) {
		this.waybillServiceSubscriptionSequence = waybillServiceSubscriptionSequence;
	}

	public void setWaybillServiceSubscriptionLogSequence(Sequence waybillServiceSubscriptionLogSequence) {
		this.waybillServiceSubscriptionLogSequence = waybillServiceSubscriptionLogSequence;
	}

    public void setWaybillSubscriptionLogSequence(Sequence waybillSubscriptionLogSequence) {
        this.waybillSubscriptionLogSequence = waybillSubscriptionLogSequence;
    }
    
    public void setWaybillBillsNewSequence(Sequence waybillBillsNewSequence) {
		this.waybillBillsNewSequence = waybillBillsNewSequence;
	}
}
