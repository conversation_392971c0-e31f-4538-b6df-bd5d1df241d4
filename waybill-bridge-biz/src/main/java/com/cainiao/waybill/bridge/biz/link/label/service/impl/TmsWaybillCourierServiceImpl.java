package com.cainiao.waybill.bridge.biz.link.label.service.impl;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.cainiao.link.client.ConciseLinkClient;
import com.cainiao.logisticscloud.link.api.protocol.SendResult;
import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.LabelError;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.label.service.TmsWaybillCourierService;
import com.cainiao.waybill.bridge.common.label.validator.WaybillBranchSellerAddRelValidator;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.util.BaseResultHelper;
import com.taobao.pac.client.sdk.dataobject.request.TMS_WAYBILL_COURIER_GET.TmsWaybillCourierGetRequest;
import com.taobao.pac.client.sdk.dataobject.response.TMS_WAYBILL_COURIER_GET.TmsWaybillCourierGetResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 通过link平台调用cp接口，获取小件员的信息
 *
 * <AUTHOR>
 */
@HSFProvider(serviceInterface = TmsWaybillCourierService.class)
public class TmsWaybillCourierServiceImpl implements TmsWaybillCourierService {
    private final static Logger LOGGER = LoggerFactory.getLogger(LogAppender.LINK_CP);

    @Resource
    private LogManager logManager;
    @Resource
    private ConciseLinkClient linkClient;

    /**
     * 获取小件员的id
     *
     * @param cpCode
     * @param qrCodeStr
     * @param clientAppInfo
     * @return
     */
    public BaseResultDTO<Long> getCourierIdForQianNiu(String cpCode, String qrCodeStr, ClientInfoDTO clientAppInfo) {
        String action = "TmsWaybillCourierService.getCourierIdForQianNiu";
        try {
            // 参数校验
            WaybillBranchSellerAddRelValidator.validateQueryForQiuNiuRequest(cpCode, qrCodeStr, clientAppInfo);
            TmsWaybillCourierGetRequest request = new TmsWaybillCourierGetRequest();
            request.setCpCode(cpCode);
            request.setQrCodeStr(qrCodeStr);
            SendResult<TmsWaybillCourierGetResponse> response = linkClient.sendMsgSyncWithSysResult(qrCodeStr, cpCode, request);
            if (response == null) {
                logManager.logErrorMessage(action, "cpCode:" + cpCode
                    + ", qrCodeStr:" + qrCodeStr + ", response:null", LOGGER);
                return BaseResultHelper.errorResult(LabelError.COURIER_GET_FAIL);
            }
            if (!response.isSuccess() || response.getResponse() == null) {
                logManager.logErrorMessage(action, "cpCode:" + cpCode
                    + ", qrCodeStr:" + qrCodeStr + ", response:" + response, LOGGER);
                return BaseResultHelper.errorResult(LabelError.COURIER_IS_NOT_ALLOW);
            }
            logManager.logErrorMessage(action, "cpCode:" + cpCode
                + ", qrCodeStr:" + qrCodeStr + response.getResponse().getCourierId(), LOGGER);
            if (response.getResponse().getCourierId() != null && response.getResponse().getCourierId() > 0) {
                return BaseResultHelper.newResult(response.getResponse().getCourierId());
            } else {
                return BaseResultHelper.errorResult(LabelError.COURIER_IS_NOT_ALLOW);
            }
        } catch (BridgeValidationException e) {
            logManager.logExceptionMessage(action, "code:" + e.getErrorCode()
                + ", msg:" + e.getErrorMessage() + ", cpCode:" + cpCode + ", qrCodeStr:" + qrCodeStr, e, LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage(action, "code:" + e.getErrorCode()
                + ", msg:" + e.getErrorMessage() + ", cpCode:" + cpCode + ", qrCodeStr:" + qrCodeStr, e, LOGGER);
            return BaseResultHelper.errorResult(LabelError.COURIER_GET_FAIL);
        } catch (Throwable e) {
            logManager.logExceptionMessage(action, "cpCode:" + cpCode + ", qrCodeStr:" + qrCodeStr, e, LOGGER);
            return BaseResultHelper.errorResult(LabelError.COURIER_GET_FAIL);
        }
    }
}
