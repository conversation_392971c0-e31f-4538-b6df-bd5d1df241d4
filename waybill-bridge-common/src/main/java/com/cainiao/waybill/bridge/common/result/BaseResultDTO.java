package com.cainiao.waybill.bridge.common.result;

import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.BaseError;
import com.cainiao.waybill.bridge.common.dto.BaseDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用的单个请求处理结果
 *
 * <AUTHOR>
 * @since Jan 27, 2016 3:49:07 PM
 * @param <T>
 */
public class BaseResultDTO<T> extends BaseDTO {

    private static final long	serialVersionUID	= 4661096805690919752L;

    public static class ErrorInfo extends BaseDTO {
        private static final long	serialVersionUID	= -6603296983003762171L;
        private String				errorCode;
        private String				errorMessage;

        public ErrorInfo() {

        }

        public ErrorInfo(String errorCode, String errorMessage) {
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("ErrorInfo{");
            sb.append("errorCode='").append(errorCode).append('\'');
            sb.append(", errorMessage='").append(errorMessage).append('\'');
            sb.append('}');
            return sb.toString();
        }
    }

    private boolean			success			= true;
    private T				module;
    /**
     * 错误码对应的错误信息
     */
    private List<ErrorInfo>	errorInfoList	= new ArrayList<ErrorInfo>();

    /**
     * 创建一个result。
     */
    public BaseResultDTO() {

    }

    public BaseResultDTO(String objectId, T module) {
        this.setObjectId(objectId);
        this.module = module;
    }

    public boolean isSuccess() {
        return success;
    }

    public boolean isFailure() {
        return !isSuccess();
    }

    public void setSuccess(final boolean success) {
        this.success = success;
    }

    /**
     * 请求返回的数据，可以为空
     *
     * @return
     */
    public T getModule() {
        return module;
    }

    public void setModule(T module) {
        this.module = module;
    }

    /**
     * 向结果添加错误信息
     *
     * @param errorCode
     *            错误码，不能为空
     * @param errorMessage
     *            错误信息
     */
    public void addErrorMessage(String errorCode, String errorMessage) {
        if (errorInfoList == null) {
            errorInfoList = new ArrayList<ErrorInfo>();
        }
        if (StringUtils.isBlank(errorCode)) {
            throw new IllegalArgumentException(BaseError.PARAMETER_ILLEGAL.getErrorMsg()
                + ",errorCode cant be empty");
        }
        errorInfoList.add(new ErrorInfo(errorCode, errorMessage));
    }

    public void addErroInfo(ErrorInfo errorInfo) {
        if (errorInfo == null) {
            throw new IllegalArgumentException(BaseError.PARAMETER_ILLEGAL.getErrorMsg()
                + ",errorInfo cant be null");
        }
        addErrorMessage(errorInfo.getErrorCode(), errorInfo.getErrorMessage());
    }

    /**
     * 指定只获取一个错误信息(对应具有多个错误信息的情况，只返回一个)
     *
     * @return null或者一个错误信息
     */
    public ErrorInfo getOneErrorInfo() {
        if (CollectionUtils.isEmpty(errorInfoList)) {
            return null;
        } else {
            return errorInfoList.get(0);
        }
    }

    public List<ErrorInfo> getErrorInfoList() {
        if (CollectionUtils.isEmpty(errorInfoList)) {
            return new ArrayList<ErrorInfo>();
        }
        return errorInfoList;
    }

    public List<String> getErrorCodeList() {
        List<String> errorCodeList = Lists.newArrayList();
        List<ErrorInfo> errorInfoList = getErrorInfoList();
        if (CollectionUtils.isEmpty(errorInfoList)) {
            return errorCodeList;
        }
        for (ErrorInfo errorInfo : errorInfoList) {
            errorCodeList.add(errorInfo.getErrorCode());
        }
        return errorCodeList;
    }

    public String getErrorMessage() {
        ErrorInfo errorInfo = getOneErrorInfo();
        return errorInfo == null ? null : errorInfo.getErrorMessage();
    }
}
