package com.cainiao.waybill.bridge.common.dto.mq;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @author: yexin
 * @date: 2022-04-28 16:44
 **/
public class MetaQDelayLevel implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final Integer SECOND_1 = 1;
    public static final Integer SECOND_5 = 5;
    public static final Integer SECOND_10 = 10;
    public static final Integer SECOND_30 = 30;

    public static final Integer MINUTE_1 = 60;
    public static final Integer MINUTE_2 = 120;
    public static final Integer MINUTE_3 = 180;
    public static final Integer MINUTE_4 = 240;
    public static final Integer MINUTE_5 = 300;
    public static final Integer MINUTE_6 = 360;
    public static final Integer MINUTE_7 = 420;
    public static final Integer MINUTE_8 = 480;
    public static final Integer MINUTE_9 = 540;
    public static final Integer MINUTE_10 = 600;
    public static final Integer MINUTE_20 = 1200;
    public static final Integer MINUTE_30 = 1800;

    public static final Integer HOUR_1 = 3600;
    public static final Integer HOUR_2 = 7200;


    /**
     * 将18个延时级别转换为秒单位，并按照从大到小的顺序排放
     */
    public static final List<Integer> delaySecondList = Lists.newArrayList(7200, 3600, 1800, 1200, 600, 540, 480, 420, 360, 300, 240, 180, 120, 60, 30, 10, 5, 1);

    public static final Map<Integer, String> delaySecondAndDelayLevelMap = Maps.newHashMapWithExpectedSize(delaySecondList.size());

    static {
        delaySecondAndDelayLevelMap.put(HOUR_2, "18");
        delaySecondAndDelayLevelMap.put(HOUR_1, "17");

        delaySecondAndDelayLevelMap.put(MINUTE_30, "16");
        delaySecondAndDelayLevelMap.put(MINUTE_20, "15");
        delaySecondAndDelayLevelMap.put(MINUTE_10, "14");
        delaySecondAndDelayLevelMap.put(MINUTE_9, "13");
        delaySecondAndDelayLevelMap.put(MINUTE_8, "12");
        delaySecondAndDelayLevelMap.put(MINUTE_7, "11");
        delaySecondAndDelayLevelMap.put(MINUTE_6, "10");
        delaySecondAndDelayLevelMap.put(MINUTE_5, "9");
        delaySecondAndDelayLevelMap.put(MINUTE_4, "8");
        delaySecondAndDelayLevelMap.put(MINUTE_3, "7");
        delaySecondAndDelayLevelMap.put(MINUTE_2, "6");
        delaySecondAndDelayLevelMap.put(MINUTE_1, "5");

        delaySecondAndDelayLevelMap.put(SECOND_30, "4");
        delaySecondAndDelayLevelMap.put(SECOND_10, "3");
        delaySecondAndDelayLevelMap.put(SECOND_5, "2");
        delaySecondAndDelayLevelMap.put(SECOND_1, "1");
    }
}
