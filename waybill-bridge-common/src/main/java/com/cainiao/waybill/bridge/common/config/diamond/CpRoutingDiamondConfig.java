package com.cainiao.waybill.bridge.common.config.diamond;

import java.io.IOException;
import java.util.List;

import com.alibaba.common.lang.ExceptionUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.common.constants.PickUpConfigConstants;
import com.google.common.collect.Lists;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2022/07/26-下午3:16
 */
public class CpRoutingDiamondConfig {

    public static final Logger infoLogger = LoggerFactory.getLogger("WAYBILL_PICKUP_INFO");

    private static JSONObject ROUTING_CONFIG = new JSONObject();

    private static void initConfig() {
        // 启动只用一次场景，直接get获取配置值
        try {
            String configInfo = Diamond
                .getConfig(PickUpConfigConstants.DiamondConfig.PICK_UP_CP_ROUTING_CONFIG_DATA_ID,
                    PickUpConfigConstants.DiamondConfig.PICK_UP_CP_ROUTING_CONFIG_GROUP_ID, 3000);
            parseVmVersionConfig(configInfo);
        } catch (IOException e1) {
            infoLogger.info("获取diamond中cp路由配置信息：" + ExceptionUtil.getStackTrace(e1));
        }
        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(PickUpConfigConstants.DiamondConfig.PICK_UP_CP_ROUTING_CONFIG_DATA_ID,
            PickUpConfigConstants.DiamondConfig.PICK_UP_CP_ROUTING_CONFIG_GROUP_ID,
            new ManagerListenerAdapter() {
                @Override
                public void receiveConfigInfo(String configInfo) {
                    try {
                        parseVmVersionConfig(configInfo);
                    } catch (Exception e) {
                        infoLogger.info(
                            "ManagerListenerAdapter获取diamond中配置的cp路由配置信息：" + ExceptionUtil.getStackTrace(e));
                    }
                }
            });
    }


    static {
        initConfig();
    }

    private static void parseVmVersionConfig(String configInfo) {
        if (StringUtil.isBlank(configInfo)) {
            return;
        }
        ROUTING_CONFIG = JSONObject.parseObject(configInfo);
    }

    private static final String SWITCH_BRANCH = "switchBranch";
    private static final String ALL_PATH = "全部";
    private static final String SWITCH_BRANCH_TO_CP_LIST = "switchBranchToCpList";
    private static final String BLOCK_BRANCH = "blockBranch";

    //{
    //    "YTO": {
    //        "switchBranch:{"001": ["全部"]},
    //        "blockBranch":{"001":["全部"]},
    //        "switchBranchToCpList":【"GUOGUO"]}
    //}

    public static JSONObject getRoutingConfig(String cpCode){
        JSONObject branch = ROUTING_CONFIG.getJSONObject(cpCode);
        return branch;
    }

    public static List<String> isBlackBranchAndPath(String cpCode, String branchCode, String senderProvince, String receiveProvince){
        JSONObject branch = ROUTING_CONFIG.getJSONObject(cpCode);
        if(branch == null){
            return Lists.newArrayList();
        }
        JSONObject blackBranch = branch.getJSONObject(SWITCH_BRANCH);
        if(blackBranch == null){
           return Lists.newArrayList();
        }
        JSONArray pathArray = blackBranch.getJSONArray(branchCode);
        String path = senderProvince + "-" + receiveProvince;
        if(pathArray != null && (pathArray.contains(ALL_PATH) || pathArray.contains(path))){
            JSONArray jsonArray = branch.getJSONArray(SWITCH_BRANCH_TO_CP_LIST);
            if(jsonArray == null || jsonArray.size() < 1){
                return Lists.newArrayList();
            }
            return jsonArray.toJavaList(String.class);
        }
        return Lists.newArrayList();
    }

    public static boolean isBlockBranch(String cpCode, String branchCode, String senderProvince, String receiveProvince){
        JSONObject branch = ROUTING_CONFIG.getJSONObject(cpCode);
        if(branch == null){
            return false;
        }
        JSONObject blockBranch = branch.getJSONObject(BLOCK_BRANCH);
        if(blockBranch == null || blockBranch.size() < 1){
            return false;
        }
        JSONArray pathArray = blockBranch.getJSONArray(branchCode);
        String path = senderProvince + "-" + receiveProvince;
        if(pathArray != null && (pathArray.contains(ALL_PATH) || pathArray.contains(path))){
            return true;
        }
        return false;
    }

    public static void main(String[] args) {
        String data = "{\n"
            + "    \"YTO\":{\n"
            + "        \"switchBranch\":{\n"
            + "            \"002\":[\n"
            + "                \"浙江省-重庆市\"\n"
            + "            ]\n"
            + "        },\n"
            + "        \"blockBranch\":{\n"
            + "            \"001\":[\n"
            + "                \"浙江省-重庆市\"\n"
            + "            ]\n"
            + "        },\n"
            + "        \"switchBranchToCpList\":[\n"
            + "            \"GUOGUO\"\n"
            + "        ]\n"
            + "    }\n"
            + "}";

        parseVmVersionConfig(data);
        System.out.println(getRoutingConfig("YTO").size());
        //System.out.println(getRoutingConfig("YUNDA").size());
        System.out.println(isBlockBranch("YTO", "001", "安徽省", "重庆市"));
        System.out.println(isBlockBranch("YTO", "001", "浙江省", "重庆市"));
        System.out.println(isBlackBranchAndPath("YTO", "001", "安徽省", "重庆市"));
    }
}
