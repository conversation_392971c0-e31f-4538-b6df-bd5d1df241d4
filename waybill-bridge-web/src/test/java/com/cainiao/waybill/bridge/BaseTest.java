package com.cainiao.waybill.bridge;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.nio.file.Files;

import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;

/**
 * 单测基础类，勿删
 * Created By Thub IDEA插件
 *
 * <AUTHOR> zouping.fzp
 * @Classname BaseTest
 * @Description
 * @Date 2023/10/26 20:09
 * @Version 1.0
 */
public class BaseTest {
    protected static String readJson(String filePath) {
        try {
            File file = new File(BaseTest.class.getClassLoader()
                .getResource(filePath).getFile());
            return String.join("", Files.readAllLines(file.toPath()));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    protected static <T> void initSwitch(Class<T> tClass, String filePath)
        throws IllegalAccessException, NoSuchFieldException {
        String config = readJson(filePath);
        JSONObject jsonObject = JSONObject.parseObject(config);
        for (String key : jsonObject.keySet()) {
            Field field = tClass.getDeclaredField(key);
            Type genericType = field.getGenericType();

            Object t = jsonObject.getObject(key, genericType);
            field.setAccessible(true);
            field.set(BridgeSwitch.class, t);
        }
    }
}
