package com.cainiao.waybill.bridge.enterprise.administrators.service;


import com.cainiao.waybill.bridge.biz.pickup.dto.update.PickUpUpdateOrderRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.update.PickUpUpdateOrderResponse;
import com.cainiao.waybill.bridge.biz.ticket.dto.LogisticsDetailDTO;
import com.cainiao.waybill.bridge.enterprise.administrators.request.WaybillEnterpriseOrderCancelRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.WaybillEnterpriseOrderQueryRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.WaybillEnterpriseOrderRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.WaybillEnterpriseOrderTraceRequest;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseOrderDO;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeEnterpriseOrderDTO;

import java.text.ParseException;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public interface WaybillBridgeEnterpriseOrderService {
    /**


    /**
     * 列表查询
     * @param request
     */
    BridgePagingDTO<WaybillBridgeEnterpriseOrderDTO> pageList(WaybillEnterpriseOrderQueryRequest request) throws ParseException;

    /**
     * 创建
     * @param request
     */
    String create(WaybillEnterpriseOrderRequest request) throws Exception;


    /**
     * 取消
     * @param request
     */
    String cancel(WaybillEnterpriseOrderCancelRequest request) throws Throwable;

    /**
     * 寄件订单更新：
     * test
     *
     * @param request ：
     * @return ：
     * @throws Throwable ：
     */
    String update(WaybillBridgeEnterpriseOrderDO request) throws Throwable;

    /**
     * 创建测试
     * @param request
     */
    String createTest(WaybillEnterpriseOrderRequest request);

    /**
     * 删除数据
     * @par
     */
    int deleteOrderById(long id);

    /**
     * 删除数据的feature
     * @par
     */
    int removeOrderFeatureById(long id, String feature);


    /**
     * 删除数据
     * @par
     */
    List<LogisticsDetailDTO> queryWaybillTrace(WaybillEnterpriseOrderTraceRequest request);

    /**
     * 获取打印报文
     * @par
     */
    String queryEMSPrintData(WaybillBridgeEnterpriseOrderDO enterpriseOrderRequest, String waybillCode);

}