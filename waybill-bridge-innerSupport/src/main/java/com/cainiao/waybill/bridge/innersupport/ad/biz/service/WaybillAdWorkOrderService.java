package com.cainiao.waybill.bridge.innersupport.ad.biz.service;

import com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder.*;
import com.cainiao.waybill.bridge.innersupport.ad.biz.response.common.AdPagingResponse;
import com.cainiao.waybill.bridge.innersupport.ad.model.dto.WaybillAdWorkOrderDTO;

public interface WaybillAdWorkOrderService {
    /**
     * 创建工单
     * @param request 创建请求
     * @return 返回创建结果，true表示创建成功，false表示创建失败
     */
    Boolean create(WaybillAdWorkOrderCreateRequest request);

    /**
     * 工单列表查询
     * @param queryRequest
     * @return
     */
    AdPagingResponse<WaybillAdWorkOrderDTO> listPage(WaybillAdWorkOrderQueryRequest queryRequest);

    /**
     * 更新工单
     * @param request 更新请求
     * @return 返回更新结果，true表示更新成功，false表示更新失败
     */
    Boolean modified(WaybillAdWorkOrderUpdateRequest request);

    /**
     * 删除工单
     * @param waybillAdWorkOrderDeleteRequest
     * @return
     */
    Boolean deleteById(WaybillAdWorkOrderDeleteRequest waybillAdWorkOrderDeleteRequest);

    /**
     * 批量更新面单打印时间
     * <p>批量更新指定运单的打印时间
     * @param request 更新运单打印时间的请求参数
     * @return 更新成功返回true，更新失败返回false
     */
    Boolean updateWaybillPrintTimeBatch(WaybillAdWorkOrderUpdateWaybillPrintTimeRequest request);


    /**
     * 批量更新广告创意Id
     * @param request 运单广告工单广告创意Id更新请求
     * @return 更新是否成功的布尔值。若更新成功，则返回true；否则返回false。
     * #throw 当更新广告Id出现异常时，抛出异常。
     */
    Boolean updateAdIdBatch(WaybillAdWorkOrderUpdateWaybillPrintTimeRequest request);

}
