package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class WaybillEnterpriseLocationDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   企业唯一id
     *
     *
     * @mbg.generated
     */
    private String corpId;

    /**
     * Database Column Remarks:
     *   办公地点自定义名称
     *
     *
     * @mbg.generated
     */
    private String locationName;

    /**
     * Database Column Remarks:
     *   办公场地详细地址
     *
     *
     * @mbg.generated
     */
    private String locationAddress;

    /**
     * Database Column Remarks:
     *   关联月结账号配置
     *
     *
     * @mbg.generated
     */
    private String waybillAccountVid;

    /**
     * Database Column Remarks:
     *   备注
     *
     *
     * @mbg.generated
     */
    private String remark;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   地址编码
     *
     *
     * @mbg.generated
     */
    private String locationAddressCode;

    /**
     * Database Column Remarks:
     *   场地业务唯一字段
     *
     *
     * @mbg.generated
     */
    private String locationId;

    /**
     *
     * @return the value of waybill_enterprise_location.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_enterprise_location.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_enterprise_location.corp_id
     *
     * @mbg.generated
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     *
     * @param corpId the value for waybill_enterprise_location.corp_id
     *
     * @mbg.generated
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    /**
     *
     * @return the value of waybill_enterprise_location.location_name
     *
     * @mbg.generated
     */
    public String getLocationName() {
        return locationName;
    }

    /**
     *
     * @param locationName the value for waybill_enterprise_location.location_name
     *
     * @mbg.generated
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     *
     * @return the value of waybill_enterprise_location.location_address
     *
     * @mbg.generated
     */
    public String getLocationAddress() {
        return locationAddress;
    }

    /**
     *
     * @param locationAddress the value for waybill_enterprise_location.location_address
     *
     * @mbg.generated
     */
    public void setLocationAddress(String locationAddress) {
        this.locationAddress = locationAddress;
    }

    /**
     *
     * @return the value of waybill_enterprise_location.waybill_account_vid
     *
     * @mbg.generated
     */
    public String getWaybillAccountVid() {
        return waybillAccountVid;
    }

    /**
     *
     * @param waybillAccountVid the value for waybill_enterprise_location.waybill_account_vid
     *
     * @mbg.generated
     */
    public void setWaybillAccountVid(String waybillAccountVid) {
        this.waybillAccountVid = waybillAccountVid;
    }

    /**
     *
     * @return the value of waybill_enterprise_location.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @param remark the value for waybill_enterprise_location.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     *
     * @return the value of waybill_enterprise_location.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_enterprise_location.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_enterprise_location.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_enterprise_location.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_enterprise_location.location_address_code
     *
     * @mbg.generated
     */
    public String getLocationAddressCode() {
        return locationAddressCode;
    }

    /**
     *
     * @param locationAddressCode the value for waybill_enterprise_location.location_address_code
     *
     * @mbg.generated
     */
    public void setLocationAddressCode(String locationAddressCode) {
        this.locationAddressCode = locationAddressCode;
    }

    /**
     *
     * @return the value of waybill_enterprise_location.location_id
     *
     * @mbg.generated
     */
    public String getLocationId() {
        return locationId;
    }

    /**
     *
     * @param locationId the value for waybill_enterprise_location.location_id
     *
     * @mbg.generated
     */
    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", corpId=").append(corpId);
        sb.append(", locationName=").append(locationName);
        sb.append(", locationAddress=").append(locationAddress);
        sb.append(", waybillAccountVid=").append(waybillAccountVid);
        sb.append(", remark=").append(remark);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", locationAddressCode=").append(locationAddressCode);
        sb.append(", locationId=").append(locationId);
        sb.append("]");
        return sb.toString();
    }
}