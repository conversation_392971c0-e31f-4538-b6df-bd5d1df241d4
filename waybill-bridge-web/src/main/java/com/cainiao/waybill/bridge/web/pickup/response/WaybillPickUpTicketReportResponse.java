package com.cainiao.waybill.bridge.web.pickup.response;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname WaybillPickUpTicketReportResponse
 * @Description
 * @Date 2022/11/2 7:56 下午
 * @Version 1.0
 */
@Data
public class WaybillPickUpTicketReportResponse implements Serializable {

    private static final long serialVersionUID = -4479519295072789790L;

    /**
     * 时间
     */
    private String time;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 订单数量
     */
    private String orderNum;

    /**
     * 去重工单数
     */
    private String distinctTicketNum;

    /**
     * 去重工单率
     */
    private String distinctTicketRate;

    /**
     * 工单数
     */
    private String ticketNum;

    /**
     * 工单率
     */
    private String ticketRate;

    /**
     * 应揽及时揽收率
     */
    private String shouldGotInTimeGotRate;
}
