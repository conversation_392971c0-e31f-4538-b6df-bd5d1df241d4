package com.cainiao.waybill.bridge.innersupport.ad.exception;

import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.ErrorInfo;
import org.apache.commons.lang3.StringUtils;


/**
 * 业务操作异常,是业务操作中捕获到异常后(如IOException,OtherSystemException等),或者调用外部接口拿到errorCode,errorMessage后抛出的.
 */
public class AdBusinessException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    protected final String errorMessage;
    protected final String errorCode;

    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 注意,该构造函数是业务操作捕获到异常以后抛出的,一定有cause
     *
     * @param errorMessage
     * @param errorCode
     * @param cause
     */
    public AdBusinessException(String errorCode, String errorMessage, Throwable cause) {
        super("[" + errorCode + "]" + ":" + errorMessage, cause);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    /**
     * 注意,该构造函数是调用第三方接口返回ERROR_CODE,ERROR_MESSAGE后抛出的,用于代表第三方接口异常.
     *
     * @param errorCode
     * @param errorMessage
     */
    public AdBusinessException(String errorCode, String errorMessage) {
        super("[" + errorCode + "]" + ":" + errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public AdBusinessException(ErrorInfo errorInfo) {
        super("[" + errorInfo.getErrorCode() + "]" + ":" + errorInfo.getErrorMsg());
        this.errorCode = errorInfo.getErrorCode();
        this.errorMessage = errorInfo.getErrorMsg();
    }

    public AdBusinessException(ErrorInfo errorInfo, String msg) {
        super("[" + errorInfo.getErrorCode() + "]" + ":" + errorInfo.getErrorMsg());
        this.errorCode = errorInfo.getErrorCode();
        if (StringUtils.isNotBlank(msg)) {
            this.errorMessage = errorInfo.getErrorMsg() + "(" + msg + ")";
        } else {
            this.errorMessage = errorInfo.getErrorMsg();
        }
    }

    public ErrorInfo getErrorConstant() {
        ErrorInfo errorConstant = new ErrorInfo();
        errorConstant.setErrorCode(this.getErrorCode());
        errorConstant.setErrorMsg(this.getErrorMessage());

        return errorConstant;
    }

}
