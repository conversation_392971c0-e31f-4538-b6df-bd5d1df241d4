package com.cainiao.waybill.bridge.ticket.api.request;

import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 工单创建请求
 * <AUTHOR>
 */
@Data
public class WaybillTicketCreateRequest implements Serializable {

    /**
     *  物流运单号
     */
    @NotBlank(message = "运单号不能为空")
    private String mailNo;

    /**
     *  入柜时间
     */
    private Date intoBoxTime;

    /**
     * 工单发起方类型
     * 1：消费者
     * 2：客服小二
     */
    @NotNull(message = "请选择工单发起方类型")
    private Integer operatorType;

    /**
     * 工单类型
     * 1：签收核实
     * 2：核实取件码
     * 3：丢失/破损
     */
    @NotNull(message = "请选择工单处理类型")
    private Integer ticketType;

    /**
     * 顾客类型
     * 1：寄件人
     * 2: 收件人
     * 3: 快递员
     */
    private Integer customType;

    /**
     * 来电号码
     */
    private String callPhone;

    /**
     * 工单内容
     */
    @NotBlank(message = "工单内容不能为空")
    private String ticketContent;

    /**
     * 工单附件列表对应名称
     */
    private List<String> ticketFileNames;

    /**
     * 扩展字段
     */
    private String feature;
}
