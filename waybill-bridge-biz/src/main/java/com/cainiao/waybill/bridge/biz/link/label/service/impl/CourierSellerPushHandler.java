//package com.cainiao.waybill.bridge.biz.link.label.service.impl;
//
//import java.util.Map;
//
//import javax.annotation.Resource;
//
//import com.cainiao.iss.constants.ResultCodeConstant;
//import com.cainiao.iss.domain.dto.ExecutableTask;
//import com.cainiao.iss.domain.result.BaseResult;
//import com.cainiao.lego.exception.ProtectException;
//import com.cainiao.lego.framework.execution.ProtectionExecutor;
//import com.cainiao.link.client.ConciseLinkClient;
//import com.cainiao.logisticscloud.link.api.protocol.SendResult;
//import com.cainiao.waybill.bridge.biz.label.manager.BranchSellerAddressRelationManager;
//import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
//import com.cainiao.waybill.bridge.biz.wrapper.AddressUnifyWrapper;
//import com.cainiao.waybill.bridge.common.constants.BridgeConstants.ProtectionResource;
//import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.SystemError;
//import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
//import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
//import com.cainiao.waybill.bridge.common.label.dto.AddressDTO;
//import com.cainiao.waybill.bridge.common.util.DateUtils;
//import com.cainiao.waybill.bridge.model.domain.BranchSellerAddressDO;
//import com.taobao.pac.client.sdk.dataobject.request.TMS_WAYBILL_SELLER_COURIER_SEND.Address;
//import com.taobao.pac.client.sdk.dataobject.request.TMS_WAYBILL_SELLER_COURIER_SEND.TmsWaybillSellerCourierSendRequest;
//import com.taobao.pac.client.sdk.dataobject.response.TMS_WAYBILL_SELLER_COURIER_SEND.TmsWaybillSellerCourierSendResponse;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
///**
// * 小件员商家绑定关系下发 handler
// *
// * 下发给 cp，用于网点管理小件员商家绑定关系
// *
// * <AUTHOR>
// * @date 2017/08/03
// */
//@Component
//public class CourierSellerPushHandler {
//	private final static Logger LOGGER = LoggerFactory.getLogger(LogAppender.LINK_CP);
//	private static final String S_LIMIT = "S_LIMIT";
//
//	@Resource
//	private LogManager logManager;
//	@Resource
//	private ConciseLinkClient linkClient;
//	@Resource
//	private BranchSellerAddressRelationManager branchSellerAddressRelationManager;
//	@Resource
//	private AddressUnifyWrapper addressUnifyWrapper;
//
//	public BaseResult execute(ExecutableTask task) {
//		BaseResult baseResult = new BaseResult();
//
//		try {
//			Map<String, String> param = task.getParamsMap();
//			Long relationId = Long.valueOf(param.get("relationId"));
//			BranchSellerAddressDO branchSellerAddressDO = branchSellerAddressRelationManager
//					.getRelationByRelationId(relationId);
//
//			TmsWaybillSellerCourierSendRequest request = new TmsWaybillSellerCourierSendRequest();
//			request.setBranchCode(branchSellerAddressDO.getBranchCode());
//			request.setCooperationDate(DateUtils.dateToString(branchSellerAddressDO.getGmtCooperation(), "yyyy-MM-dd"));
//			request.setCourierId(branchSellerAddressDO.getCourierId());
//			request.setRelationId(relationId);
//			request.setSellerNick(branchSellerAddressDO.getSellerName());
//			request.setShopName(branchSellerAddressDO.getShopName());
//			request.setSellerId(branchSellerAddressDO.getSellerId());
//
//			Address sellerAddress = new Address();
//			AddressDTO addressDTO = addressUnifyWrapper.getAddressByDivisionId(branchSellerAddressDO.getDivisionId(),
//					branchSellerAddressDO.getAddressDetail());
//			sellerAddress.setAreaName(addressDTO.getDistrict());
//			sellerAddress.setCityName(addressDTO.getCity());
//			sellerAddress.setDetailAddress(addressDTO.getDetail());
//			sellerAddress.setProvinceName(addressDTO.getProvince());
//			sellerAddress.setTownName(addressDTO.getTown());
//			request.setSellerAddress(sellerAddress);
//
//			String pacLog = getPacLogKey(relationId, branchSellerAddressDO.getCpCode());
//			SendResult<TmsWaybillSellerCourierSendResponse> response = ProtectionExecutor.execWithThreadProtect(
//					ProtectionResource.generatePushRes(branchSellerAddressDO.getCpCode()), () -> {
//						return linkClient.sendMsgSyncWithSysResult(pacLog, branchSellerAddressDO.getCpCode(),
//								request);
//					});
//
//			if (!response.isSuccess()) {
//				throw new BridgeBaseException(response.getErrorCode(), response.getErrorMsg() + "^^^fromPAC");
//			}
//
//			baseResult.setSuccess(true);
//			return baseResult;
//
//		} catch (BridgeBaseException e) {
//			logManager.logExceptionMessage("CourierSellerPushHandler#execute fail",
//					"code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + "request:" + task, e, LOGGER);
//
//			baseResult.setSuccess(false);
//			baseResult.setResultCode(S_LIMIT);
//			baseResult.setResultDesc(e.getMessage());
//			return baseResult;
//		} catch (ProtectException e) {
//			logManager.logExceptionMessage("CourierSellerPushHandler#execute fail", "request:" + task, e, LOGGER);
//
//			baseResult.setSuccess(false);
//			baseResult.setResultCode(S_LIMIT);
//			baseResult.setResultDesc(e.getMessage());
//			return baseResult;
//		} catch (Throwable e) {
//			logManager.logExceptionMessage("CourierSellerPushHandler#execute fail", "request:" + task, e, LOGGER);
//
//			baseResult.setSuccess(false);
//			baseResult.setResultCode(ResultCodeConstant.NEED_REDO);
//			baseResult.setResultDesc(SystemError.SYSTEM_BUSY.getErrorMsg());
//			return baseResult;
//		}
//	}
//
//	private String getPacLogKey(Long relationId, String cpCode) {
//		return relationId + "_" + cpCode;
//	}
//}
