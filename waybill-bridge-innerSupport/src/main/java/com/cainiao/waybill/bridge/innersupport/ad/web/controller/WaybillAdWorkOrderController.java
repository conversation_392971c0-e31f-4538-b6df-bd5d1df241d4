package com.cainiao.waybill.bridge.innersupport.ad.web.controller;

import com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder.WaybillAdWorkOrderCreateRequest;
import com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder.WaybillAdWorkOrderDeleteRequest;
import com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder.WaybillAdWorkOrderQueryRequest;
import com.cainiao.waybill.bridge.innersupport.ad.biz.request.waybillAdWorkOrder.WaybillAdWorkOrderUpdateRequest;
import com.cainiao.waybill.bridge.innersupport.ad.biz.response.common.AdPagingResponse;
import com.cainiao.waybill.bridge.innersupport.ad.biz.service.WaybillAdWorkOrderService;
import com.cainiao.waybill.bridge.innersupport.ad.config.BridgeInnerSwitch;
import com.cainiao.waybill.bridge.innersupport.ad.model.convert.WaybillAdWorkOrderConverter;
import com.cainiao.waybill.bridge.innersupport.ad.model.dto.WaybillAdWorkOrderDTO;
import com.cainiao.waybill.bridge.innersupport.ad.util.CheckRole;
import com.cainiao.waybill.bridge.innersupport.ad.web.AdBaseResult;
import com.cainiao.waybill.bridge.innersupport.ad.web.vo.WaybillAdWorkOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/adworkorder")
public class WaybillAdWorkOrderController {

    @Resource
    private WaybillAdWorkOrderService waybillAdWorkOrderService;

    @RequestMapping(value = "/user-info", method = RequestMethod.POST)
    public AdBaseResult<Map<String, Object>> userInfo(@RequestBody WaybillAdWorkOrderQueryRequest request) {
        if(BridgeInnerSwitch.AD_WORK_ORDER_OFFLINE){
            return AdBaseResult.bizFail("广告工单页面已下线");
        }
        //Boolean success = waybillAdWorkOrderService.create(request);
        Map<String, Object> userInfo = CheckRole.findRoleByMobile(request);

        //if (null == success || !success) {
        //    //TODO log
        //    return AdBaseResult.systemFail();
        //}
        return AdBaseResult.success(userInfo);
    }

    @RequestMapping(value = "/insert-work-order", method = RequestMethod.POST)
    public AdBaseResult<Void> save(@RequestBody WaybillAdWorkOrderCreateRequest request) {
        if(BridgeInnerSwitch.AD_WORK_ORDER_OFFLINE){
            return AdBaseResult.bizFail("广告工单页面已下线");
        }
        Boolean success = waybillAdWorkOrderService.create(request);
        if (null == success || !success) {
            //TODO log
            return AdBaseResult.systemFail();
        }
        return AdBaseResult.success();
    }

    @RequestMapping(value = "/list-page", method = RequestMethod.POST)
    public AdBaseResult<AdPagingResponse<WaybillAdWorkOrderVO>> listPage(@RequestBody WaybillAdWorkOrderQueryRequest queryRequest) {
        if(BridgeInnerSwitch.AD_WORK_ORDER_OFFLINE){
            return AdBaseResult.bizFail("广告工单页面已下线");
        }
        AdPagingResponse<WaybillAdWorkOrderDTO> response = waybillAdWorkOrderService.listPage(queryRequest);
        if (null == response) {
            //TODO log
            return AdBaseResult.systemFail();
        }
        if (response.isSuccess()) {
            List<WaybillAdWorkOrderVO> vos = WaybillAdWorkOrderConverter.INSTANCE.convertDTOList2VOList(response.getTableData());
            AdPagingResponse<WaybillAdWorkOrderVO> result = AdPagingResponse.build(vos, response.getPaging());
            return AdBaseResult.success(result);
        } else {
            return AdBaseResult.bizFail(response.getErrorMsg());
        }
    }

    @RequestMapping(value = "/update-work-order", method = RequestMethod.POST)
    public AdBaseResult<Void> update(@RequestBody WaybillAdWorkOrderUpdateRequest request) {
        if(BridgeInnerSwitch.AD_WORK_ORDER_OFFLINE){
            return AdBaseResult.bizFail("广告工单页面已下线");
        }
        Boolean success = waybillAdWorkOrderService.modified(request);
        if (null == success || !success) {
            //TODO log
            return AdBaseResult.systemFail();
        }
        return AdBaseResult.success();
    }

    @RequestMapping(value = "/delete-work-order", method = RequestMethod.POST)
    public AdBaseResult<Void> deleteWorkOrder(@RequestBody WaybillAdWorkOrderDeleteRequest request) {
        if(BridgeInnerSwitch.AD_WORK_ORDER_OFFLINE){
            return AdBaseResult.bizFail("广告工单页面已下线");
        }
        Boolean success = waybillAdWorkOrderService.deleteById(request);
        if (null == success || !success) {
            //TODO log
            return AdBaseResult.systemFail();
        }
        return AdBaseResult.success();
    }
}
