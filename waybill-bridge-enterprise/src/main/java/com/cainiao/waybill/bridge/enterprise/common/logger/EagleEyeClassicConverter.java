package com.cainiao.waybill.bridge.enterprise.common.logger;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.taobao.eagleeye.EagleEye;

/**
 * <AUTHOR>
 * @date 2022/6/2 4:15 PM
 */
public class EagleEyeClassicConverter extends ClassicConverter {
    @Override
    public String convert(ILoggingEvent iLoggingEvent) {
        return EagleEye.getTraceId();
    }
}
