package com.cainiao.waybill.bridge.model.domain;

import lombok.Data;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
public class WaybillBridgeEnterprisePrinterDO {
    /**
     * Database Column Remarks:
     *   主键id
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   企业id
     *
     *
     * @mbg.generated
     */
    private String corpId;

    /**
     * Database Column Remarks:
     *   打印机编码
     *
     *
     * @mbg.generated
     */
    private String printerId;

    /**
     * Database Column Remarks:
     *   打印机名称
     *
     *
     * @mbg.generated
     */
    private String printerName;

    /**
     * Database Column Remarks:
     *   打印机绑定码
     *
     *
     * @mbg.generated
     */
    private String shareCode;

    /**
     * Database Column Remarks:
     *   场地名称
     *
     *
     * @mbg.generated
     */
    private String locationName;

    /**
     * Database Column Remarks:
     *   备注
     *
     *
     * @mbg.generated
     */
    private String remark;

    /**
     *
     * @return the value of waybill_bridge_enterprise_printer.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_bridge_enterprise_printer.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_printer.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_bridge_enterprise_printer.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_printer.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_bridge_enterprise_printer.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_printer.corp_id
     *
     * @mbg.generated
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     *
     * @param corpId the value for waybill_bridge_enterprise_printer.corp_id
     *
     * @mbg.generated
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_printer.printer_id
     *
     * @mbg.generated
     */
    public String getPrinterId() {
        return printerId;
    }

    /**
     *
     * @param printerId the value for waybill_bridge_enterprise_printer.printer_id
     *
     * @mbg.generated
     */
    public void setPrinterId(String printerId) {
        this.printerId = printerId;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_printer.printer_name
     *
     * @mbg.generated
     */
    public String getPrinterName() {
        return printerName;
    }

    /**
     *
     * @param printerName the value for waybill_bridge_enterprise_printer.printer_name
     *
     * @mbg.generated
     */
    public void setPrinterName(String printerName) {
        this.printerName = printerName;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_printer.share_code
     *
     * @mbg.generated
     */
    public String getShareCode() {
        return shareCode;
    }

    /**
     *
     * @param shareCode the value for waybill_bridge_enterprise_printer.share_code
     *
     * @mbg.generated
     */
    public void setShareCode(String shareCode) {
        this.shareCode = shareCode;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_printer.location_name
     *
     * @mbg.generated
     */
    public String getLocationName() {
        return locationName;
    }

    /**
     *
     * @param locationName the value for waybill_bridge_enterprise_printer.location_name
     *
     * @mbg.generated
     */
    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    /**
     *
     * @return the value of waybill_bridge_enterprise_printer.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @param remark the value for waybill_bridge_enterprise_printer.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", corpId=").append(corpId);
        sb.append(", printerId=").append(printerId);
        sb.append(", printerName=").append(printerName);
        sb.append(", shareCode=").append(shareCode);
        sb.append(", locationName=").append(locationName);
        sb.append(", remark=").append(remark);
        sb.append("]");
        return sb.toString();
    }
}