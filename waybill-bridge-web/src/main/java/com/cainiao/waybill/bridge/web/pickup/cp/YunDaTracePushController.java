package com.cainiao.waybill.bridge.web.pickup.cp;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;

import cn.hutool.core.util.NumberUtil;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.CancelCodes;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Cp;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpDetailStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpEventConstants;
import com.cainiao.waybill.bridge.biz.pickup.dto.yunda.LogisticRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.yunda.LogisticRequestData;
import com.cainiao.waybill.bridge.biz.pickup.dto.yunda.LogisticResponse;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.service.impl.WaybillPickUpEventMetaQSender;
import com.cainiao.waybill.bridge.biz.utils.pickup.*;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.util.WeightUnitUtil;
import com.cainiao.waybill.bridge.common.waybill.constants.WaybillPickUpActionConstant;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.cainiao.waybill.common.util.FeatureUtils;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 接受韵达状态回传：与其他CP差异点，尽量在这一层就处理掉
 *
 * @author: yexin
 * @date: 2022-03-11 11:20
 **/
@RestController
public class YunDaTracePushController {

    @Resource
    private WaybillPickUpOrderManager pickUpOrderManager;

    @Resource
    private WaybillPickUpEventMetaQSender waybillPickUpEventMetaQSender;


    @PostMapping("/tracepush/yunda")
    public LogisticResponse trace(@RequestBody LogisticRequest logisticRequest) {
        PickUpLogUtil.info("/tracepush/yunda : " + JSON.toJSONString(logisticRequest));
        LogisticResponse logisticResponse = new LogisticResponse();
        try {
            //  请求校验
            if (logisticRequest == null || StringUtils.isBlank(logisticRequest.getData())) {
                throw new BridgeBaseException(PickUpConstants.Error.YunDa.YUNDA_TRACE_REQUEST_EMPTY.getErrorCode(), PickUpConstants.Error.YunDa.YUNDA_TRACE_REQUEST_EMPTY.getErrorCode());
            }

            PickUpLogUtil.record("outer_accept_yunda", logisticRequest.getData());

            LogisticRequestData requestData = JSON.parseObject(logisticRequest.getData(), LogisticRequestData.class);
            if (requestData == null) {
                throw new BridgeBaseException(PickUpConstants.Error.YunDa.YUNDA_TRACE_REQUEST_EMPTY.getErrorCode(), PickUpConstants.Error.YunDa.YUNDA_TRACE_REQUEST_EMPTY.getErrorCode());
            }
            String exeCheckDigest = BridgeSwitch.yundaPickUpConfig.get(PickUpConstants.YunDa.ConfigKey.checkDigestSwitch.name());
            if ("true".equals(exeCheckDigest)) {
                String appKey = BridgeSwitch.yundaPickUpConfig.get(PickUpConstants.YunDa.ConfigKey.appKey.name());
                String appSecret = BridgeSwitch.yundaPickUpConfig.get(PickUpConstants.YunDa.ConfigKey.appSecret.name());
                String sign = YunDaOpenApiHttpUtils.MD5(logisticRequest.getData() + "_" + appSecret);
                if (!Objects.equals(appKey, logisticRequest.getAppKey()) || !Objects.equals(sign, logisticRequest.getSign())) {
                    throw new BridgeBaseException(PickUpConstants.Error.YunDa.YUNDA_LOGISTIC_SIGN_CHECK_FAIL.getErrorCode(), PickUpConstants.Error.YunDa.YUNDA_LOGISTIC_SIGN_CHECK_FAIL.getErrorCode());
                }
            }

            //  处理内容
            String logisticNo = requestData.getOuterOrderNo();
            WaybillPickUpDetailDO existDetailDO;
            // 切换成订单id
            if(NumberUtil.isNumber(logisticNo)){
                existDetailDO = pickUpOrderManager.getById(Long.valueOf(logisticNo));
            }else{
                ImmutablePair<String, String> resCodeAndOuterOrderCode = CpLogisticNoUtil.YunDa.parseResCodeAndOuterOrderCode(logisticNo);
                existDetailDO = pickUpOrderManager.get(resCodeAndOuterOrderCode.getLeft(), resCodeAndOuterOrderCode.getRight());

                //  为了兼容"修改订单号中#的替换"改动，这里先用旧格式差，查不到再用新格式查
                if (existDetailDO == null) {
                    resCodeAndOuterOrderCode = CpLogisticNoUtil.YunDa.parseNewResCodeAndOuterOrderCode(logisticNo);
                    existDetailDO = pickUpOrderManager.get(resCodeAndOuterOrderCode.getLeft(), resCodeAndOuterOrderCode.getRight());
                }
                //  线上发现，韵达回推分单节点的时间，可能比调用韵达下单接口返回时间还早。这种情况下，判断记录不存在后，分单节点会被抛弃掉。所以当发现订单不存在时，休眠几秒，再次查询
                if (existDetailDO == null) {
                    String waitTime = BridgeSwitch.yundaPickUpConfig.get(PickUpConstants.YunDa.ConfigKey.traceOrderNotExistWaitTime.name());
                    Thread.sleep(Integer.parseInt(waitTime));
                    existDetailDO = pickUpOrderManager.get(resCodeAndOuterOrderCode.getLeft(), resCodeAndOuterOrderCode.getRight());
                }
            }

            // 订单不存在或者已经转单
            if (existDetailDO == null || (!StringUtils.equals(existDetailDO.getCpCode(), Cp.YUNDA.name())
                && !MailNoUtil.isTempCpCode(existDetailDO.getCpCode()))) {
                logisticResponse.setResult(false);
                logisticResponse.setCode(PickUpConstants.Error.YunDa.ORDER_NOT_EXIST.getErrorCode());
                logisticResponse.setMessage(PickUpConstants.Error.YunDa.ORDER_NOT_EXIST.getErrorCode());
                return logisticResponse;
            }

            // 如果是临时运单号则等待处理
            if(MailNoUtil.isTempCpCode(existDetailDO.getCpCode())){
                String waitTime = BridgeSwitch.yundaPickUpConfig.get(PickUpConstants.YunDa.ConfigKey.traceOrderNotExistWaitTime.name());
                Thread.sleep(Integer.parseInt(waitTime));
            }

            logisticResponse.setResult(true);
            logisticResponse.setCode("SUCCESS");
            logisticResponse.setMessage("成功");

            WaybillPickUpEvent pickUpEvent = new WaybillPickUpEvent();
            String action = getAction(requestData, existDetailDO);
            if (StringUtil.isBlank(action)) {
                return logisticResponse;
            }
            //  判断CP回传重量的时间是否超过指定的时间限制
            if (!CpWeightPushLimitUtil.cpPushWeightInLimitRange(action, existDetailDO)) {
                logisticResponse.setResult(false);
                logisticResponse.setMessage(PickUpConstants.Error.TRACE_PUSH_GOT_LIMIT.getErrorMsg() + "T+" + BridgeSwitch.cpReturnWeightDayLimit);
                PickUpLogUtil.errLog("", PickUpConstants.Action.YUN_DA_RETURN_WEIGHT_LIMIT.name(), PickUpConstants.Error.YUN_DA_RETURN_WEIGHT_LIMIT.getErrorCode(),
                        PickUpConstants.Error.YUN_DA_RETURN_WEIGHT_LIMIT.getErrorMsg() + "existDetailDO:" + JSON.toJSONString(existDetailDO));
                return logisticResponse;
            }

            pickUpEvent.setAction(action);
            pickUpEvent.setActionDesc(WaybillPickUpActionConstant.getActionDesc(action));
            pickUpEvent.setLastActionDetail(requestData.getRemark());
            pickUpEvent.setMailNo(existDetailDO.getMailNo());
            pickUpEvent.setCpCode(PickUpConstants.Cp.YUNDA.name());
            pickUpEvent.setResCode(existDetailDO.getResCode());
            pickUpEvent.setOuterOrderCode(existDetailDO.getOuterOrderCode());
            pickUpEvent.setLinkCpCode(PickUpFeatureUtil.getFromMulString(existDetailDO, PickUpConstants.TraceFeatureKey.LINK_CP_CODE));
            pickUpEvent.setActionGmtModified(PickUpCommonUtil.parseStandardDate(requestData.getOperateTime()).getTime());
            setExtraInfo(pickUpEvent, requestData, existDetailDO);

            waybillPickUpEventMetaQSender.send(pickUpEvent);
            return logisticResponse;
        } catch (BridgeBaseException e) {
            PickUpLogUtil.errLog("", PickUpConstants.Action.YUN_LOGISTIC_ERROR.name(), e.getErrorCode(), JSON.toJSONString(logisticRequest));
            logisticResponse.setResult(false);
            logisticResponse.setCode(e.getErrorCode());
            logisticResponse.setMessage(e.getErrorMessage());
            return logisticResponse;
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpConstants.Action.YUN_LOGISTIC_ERROR.name(), e.getMessage(), JSON.toJSONString(logisticRequest), e);
            logisticResponse.setResult(false);
            logisticResponse.setCode(PickUpConstants.Error.YunDa.YUNDA_LOGISTIC_CAINIAO_SYS_EXECEPTION.getErrorCode());
            logisticResponse.setMessage(PickUpConstants.Error.YunDa.YUNDA_LOGISTIC_CAINIAO_SYS_EXECEPTION.getErrorCode());
            return logisticResponse;
        }
    }

    private void setExtraInfo(WaybillPickUpEvent pickUpEvent, LogisticRequestData requestData, WaybillPickUpDetailDO existDetailDO) {
        Map<String, String> extraInfo = Maps.newHashMap();
        //  订单扩展信息
        Map<String, String> existFeatureMap = FeatureUtils.parseFromString(existDetailDO.getFeature());
        //  操作时间
        String operateTime = PickUpFeatureUtil.formatTime(PickUpCommonUtil.parseStandardDate(requestData.getOperateTime()).getTime());

        //  韵达重量在核价节点(check)给出，揽收为单独的got节点。check时间点在got前,一般情况时间差可以忽略不计，但取决于小件员操作，具有不可控性。
        //  所以新增check handler，仅做重量保存，不修改状态、不对外推送。
        //  重量更新时，MODIFY_WEIGHT节点需要针对韵达做专门的逻辑判断：如果已经为揽收才对外推重量更新节点。
        if (WaybillPickUpActionConstant.CHECK.equals(pickUpEvent.getAction()) || WaybillPickUpActionConstant.MODIFY_WEIGHT.equals(pickUpEvent.getAction())) {
            int weight = WeightUnitUtil.kg2g(requestData.getWeight());
            if (weight > 0) {
                extraInfo.put(PickUpEventConstants.ExtraInfoKey.WEIGHT, String.valueOf(weight));
            }
        }
        //  got节点，从DB中获取CHECK节点处理时放置的重量
        if (WaybillPickUpActionConstant.GOT.equals(pickUpEvent.getAction())) {
            //  got节点与前面带重量的check节点，据韵达反馈，正常时间差在几ms。所以check可能还未落到DB，这里就获取重量
            extraInfo.put(PickUpEventConstants.ExtraInfoKey.WEIGHT, existFeatureMap.get(PickUpConstants.TraceFeatureKey.CP_WEIGHT));
        }

        //  韵达分单节点回传小件员信息，接单节点回传修改预约件时间。跟韵达确认，接单时间取分单节点推送时间即可。
        //  即处理时把韵达分单节点当做接单节点，接单节点当做修改预约件节点，都在ACCEPT handler里面处理
        if (WaybillPickUpActionConstant.ACCEPT.equals(pickUpEvent.getAction())) {
            //  对应韵达分单节点
            if (StringUtils.isNotBlank(requestData.getCourierPhone())) {
                extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_MOBILE, requestData.getCourierPhone());
                extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_NAME, requestData.getCourierName());
                extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_ORG_CODE, requestData.getBranchCode());
            } else {
                //  对应韵达接单节点
                extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_MOBILE, existFeatureMap.get(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_MOBILE));
                extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_NAME, existFeatureMap.get(PickUpConstants.TraceFeatureKey.ACCEPT_COURIER_NAME));
                extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_ORG_CODE, existFeatureMap.get(PickUpConstants.TraceFeatureKey.ACCEPT_ORG_CODE));
                extraInfo.put(PickUpEventConstants.ExtraInfoKey.APPOINT_START_TIME, requestData.getStartGotTime());
                extraInfo.put(PickUpEventConstants.ExtraInfoKey.APPOINT_END_TIME, requestData.getPreGotTime());
            }
            extraInfo.put(PickUpConstants.TraceFeatureKey.ACCEPT_TIME, operateTime);
        }
        if (WaybillPickUpActionConstant.FAIL.equals(pickUpEvent.getAction())) {
            extraInfo.put(PickUpConstants.TraceFeatureKey.FAIL_REASON, requestData.getRemark());
            extraInfo.put(PickUpConstants.TraceFeatureKey.FAIL_CODE, CancelCodes.CP_CANCEL.getCode());
            extraInfo.put(PickUpConstants.TraceFeatureKey.CANCEL_TIME, operateTime);
        }
        //  异常揽收
        if (WaybillPickUpActionConstant.EXCEPTION_RESPONSE.equals(pickUpEvent.getAction())) {
            extraInfo.put(PickUpConstants.TraceFeatureKey.EXCEPTION_TYPE, String.valueOf(requestData.getExceptionType()));
            extraInfo.put(PickUpConstants.TraceFeatureKey.EXCEPTION_DESC, requestData.getRemark());
        }

        if (MapUtils.isNotEmpty(extraInfo)) {
            pickUpEvent.setExtraInfo(JSON.toJSONString(extraInfo));
        }
    }

    /**
     * 将韵达详情名称转换为内部的详情名称
     */
    private String getAction(LogisticRequestData requestData, WaybillPickUpDetailDO detailDO) {
        String action = BridgeSwitch.yundaPickUpAction.get(requestData.getStatus());
        if (StringUtil.isBlank(action)) {
            return null;
        }
        //  韵达重量在核价节点(check)给出，揽收为单独的got节点。check时间点在got前,一般情况时间差可以忽略不计，但取决于小件员操作，具有不可控性。
        //      所以新增check handler，仅做重量保存，不修改状态，不对外推送，真正got到达时再修改状态、对外推送。
        //  重量更新时，MODIFY_WEIGHT节点也需要针对韵达做专门的逻辑判断：如果已经为揽收状态才对外推重量更新节点。
        if (WaybillPickUpActionConstant.CHECK.equals(action)) {
            String existWeightStr = PickUpFeatureUtil.getFromMulString(detailDO, PickUpConstants.TraceFeatureKey.CP_WEIGHT);
            if (StringUtil.isNotBlank(existWeightStr) && Objects.equals(detailDO.getStatus(), PickUpDetailStatusEnum.GOT.getValue())) {
                int existWeight = Integer.parseInt(existWeightStr);
                int newWeight = WeightUnitUtil.kg2g(requestData.getWeight());
                if (newWeight > 0 && newWeight != existWeight) {
                    return WaybillPickUpActionConstant.MODIFY_WEIGHT;
                } else {
                    return null;
                }
            }
        }
        //  已经为揽收状态，当再次接到got节点，不再处理。与前面保持一致，揽收动作只会执行一次，后面有重量变更，走MODIFY_WEIGHT。
        if (Objects.equals(WaybillPickUpActionConstant.GOT, action) && !Objects.equals(detailDO.getStatus(), PickUpDetailStatusEnum.ACCEPT.getValue())) {
            return null;
        }
        return action;
    }
}
