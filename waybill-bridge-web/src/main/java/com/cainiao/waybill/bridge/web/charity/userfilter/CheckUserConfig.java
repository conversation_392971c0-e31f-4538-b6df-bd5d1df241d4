package com.cainiao.waybill.bridge.web.charity.userfilter;

import javax.annotation.Resource;

import com.cainiao.cnuser.client.service.CnUserInfoQueryService;
import com.cainiao.waybill.bridge.biz.common.user.WaybillBridgeUserInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

/**
 * <AUTHOR>
 * @date 2022/4/22 下午12:01
 */
@Configuration
public class CheckUserConfig extends WebMvcConfigurerAdapter {

    @Resource
    private WaybillBridgeUserInfoService userInfoService;

    @Resource
    private CnUserInfoQueryService cnUserInfoQueryService;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {

        // 注册自定义的拦截器passwordStateInterceptor
        CheckUserInterceptor checkRoleInterceptor = new CheckUserInterceptor();
        checkRoleInterceptor.setUserInfoService(userInfoService);
        checkRoleInterceptor.setCnUserInfoQueryService(cnUserInfoQueryService);
        registry.addInterceptor(checkRoleInterceptor).addPathPatterns("/charity/**")
                .excludePathPatterns("/charity/userInfo", "/charity/userInfo/test");
    }
}
