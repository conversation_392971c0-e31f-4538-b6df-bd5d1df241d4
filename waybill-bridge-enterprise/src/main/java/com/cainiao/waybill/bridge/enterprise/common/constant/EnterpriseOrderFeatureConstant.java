package com.cainiao.waybill.bridge.enterprise.common.constant;

import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;

public interface EnterpriseOrderFeatureConstant {

    /**
     * 业务类型
     */
    String BUSINESS_TYPE = "businessType";

    /**
     * 费用归属
     */
    String COST_ALLOCATION = "costAllocation";

    /**
     * 取件码
     */
    String GOT_CODE = "gotCode";

    /**
     * cp订单id
     */
    String CP_ORDER_ID = "cpOrderId";
    /**
     * 服务商业务类型
     */

    String CP_BIZ_TYPE = "cpBizType";

    /**
     * 路由三段码信息
     */
    String DEST_ROUTE_LABEL = "destRouteLabel";

    /**
     * 顺丰面单信息
     */
    String SF_PRINT_DATA = "sfPrintData";

    /**
     * 部门id
     */
    String DEPT_ID = "deptId";

    /**
     * 部门id
     */
    String DEPT_NAME = "deptName";

    /**
     * 保价金额
     */
    String INSURED_VALUE = "insuredValue";

    /**
     * 是否到付
     */
    String DELIVERY = "delivery";
}
