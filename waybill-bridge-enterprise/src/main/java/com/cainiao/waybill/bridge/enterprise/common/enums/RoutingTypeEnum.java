package com.cainiao.waybill.bridge.enterprise.common.enums;

import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * 路由信息类型常量定义
 * 
 * <AUTHOR>
 * @since 2016/12/26.
 */
public enum RoutingTypeEnum {

    /**
     * 大头笔
     */
    SORTATION(0, true, "sortation"),
    /**
     * 集包地
     */
    CONSOLIDATION(1, true, "consolidation"),
    /**
     * 末端中心
     */
    TERMINAL_CENTER(2, true, "terminalCenter"),

    /**
     * 二段码
     */
    ROUTE_CODE(3, false, "routeCode"),

    /**
     * 三段码
     */
    CONTRACT_CODE(4, false, "contractCode"),

	/**
     * 四段码
     */
	BLOCK_CODE(6, false, "blockCode"),

    /**
     * 黑土三段码
	 * 若该值等于{@link HeituCodeErrorEnum} 表示虽然为黑土单子,但是请求失败
     */
	HEITU_SITE_CODE(7, false, "heituSiteCode"),

	/**
     * 黑土四段码
     * 若该值等于{@link HeituCodeErrorEnum} 表示虽然为黑土单子,但是请求失败
     */
    HEITU_AOI_NAME(8, false, "heituAoiName"),

    /**
     * 未段码
     */
    END_CODE(10, false, "endCode"),

    /**
     * 未段码类型
     * {@link RoutingEndTypeEnum}
     */
    END_CODE_TYPE(11, false, "endCodeType"),

    /**
     * 末中心简码
     */
    TC_SHORT_CODE(12, false, "tcShortCode"),

    /**
     * 个性化集包大头笔
     */
    CUSTOMIZED_SORTATION(13, false, "customizedSortation"),

    /**
     * 个性化集包集包地
     */
    CUSTOMIZED_CONSOLIDATION(14, false, "customizedConsolidation"),

    /**
     * 线路码
     */
    LINE_CODE(15, false, "lineCode"),

    /**
     * 交件区域码
     */
    EXCHANGE_AREA_CODE(16, false, "exchangeAreaCode"),

    /**
     * 新四段码
     */
    NEW_BLOCK_CODE(17, false, "newBlockCode"),

    ;

    private int type;
    private boolean isMetaType;
    private String name;

    RoutingTypeEnum(int type, boolean isMetaType, String name) {
        this.type = type;
        this.isMetaType = isMetaType;
        this.name = name;
    }

    public static Set<RoutingTypeEnum> getRoutingTypes(Set<Integer> routingTypes) {
        Set<RoutingTypeEnum> types = new HashSet<RoutingTypeEnum>();
        if (CollectionUtils.isNotEmpty(routingTypes)) {
            for (Integer routingType : routingTypes) {
                RoutingTypeEnum typeEnum = getRoutingType(routingType);
                if (typeEnum != null) {
                    types.add(typeEnum);
                }
            }
        }
        return types;
    }

    public static RoutingTypeEnum getRoutingType(int routingType) {
        RoutingTypeEnum[] typeEnums = RoutingTypeEnum.values();
        for (RoutingTypeEnum typeEnum : typeEnums) {
            if (typeEnum.getType() == routingType) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 获取全部分拣规则类型集合
     * 
     * @return
     */
    public static Set<Integer> getAllRoutingType() {
        Set<Integer> allRoutingType = new HashSet<Integer>();
        RoutingTypeEnum[] typeEnums = RoutingTypeEnum.values();
        for (RoutingTypeEnum typeEnum : typeEnums) {
        	// 去除黑土request过滤逻辑
            allRoutingType.add(typeEnum.getType());
        }
        return allRoutingType;
    }

    /**
     * 获取meta元数据存在的分拣规则集合
     * 
     * @return
     */
    public static Set<Integer> getAllMetaRoutingType() {
        Set<Integer> allRoutingType = new HashSet<Integer>();
        RoutingTypeEnum[] typeEnums = RoutingTypeEnum.values();
        for (RoutingTypeEnum typeEnum : typeEnums) {
            if (typeEnum.isMetaType) {
                allRoutingType.add(typeEnum.getType());
            }
        }
        return allRoutingType;
    }

    public static boolean containsRoutingType(int routingType) {
        return getRoutingType(routingType) != null;
    }

    public static Set<RoutingTypeEnum> filterMetaRoutingTypes(Set<RoutingTypeEnum> routingTypes) {
        Set<RoutingTypeEnum> metaRoutingTypes = Sets.newHashSet();
        for (RoutingTypeEnum routingType : routingTypes) {
            // 如果是meta信息的路由规则,添加到返回值
            if (routingType.isMetaType()) {
                metaRoutingTypes.add(routingType);
            }
        }
        return metaRoutingTypes;
    }

    public static Set<RoutingTypeEnum> filterNotMetaRoutingTypes(Set<RoutingTypeEnum> routingTypes) {
        Set<RoutingTypeEnum> notMetaRoutingTypes = Sets.newHashSet();
        for (RoutingTypeEnum routingType : routingTypes) {
            // 如果是meta信息的路由规则,添加到返回值
            if (!routingType.isMetaType()) {
                notMetaRoutingTypes.add(routingType);
            }
        }
        return notMetaRoutingTypes;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isMetaType() {
        return isMetaType;
    }

    public void setMetaType(boolean metaType) {
        isMetaType = metaType;
    }
}
