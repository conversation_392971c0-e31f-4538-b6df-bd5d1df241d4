package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * 电子面单账户表 物流公司+（网点）+商家维度
 *
 * <AUTHOR>
 *
 */
public class WaybillDO {

    private Long    id;

    /**
     * 合作伙伴编号（物流公司）
     */
    private String  cpCode;

    /**
     * 服务商ID
     */
    private Long    cpId;

    /**
     * 号段用途编码
     */
    private String  segmentCode;

    /**
     * 合作伙伴分支结构（网点）编号 加盟性公司：网点编号 直营性公司：子公司编号 其他：仓库类型（AE）
     */
    private String  branchCode;

    /**
     * 1:加盟 2:直营
     */
    private int     cpType;

    /**
     * 当前可用面单数
     */
    private Integer quantity;

    /**
     * 当前已经分配的面单数（商家申请）
     */
    private Integer allocatedQuantity;

    /**
     * 当前已经确认使用的面单数（商家确认）
     */
    private Integer confirmQuantity;

    /**
     * 面单数告警值（当可用面单数低于这个值时触发一些动作）
     */
    private Integer alarmQuantity;

    /**
     * 当前分配的序列号（CP直接维护面单号方式使用）
     */
    private Long    currentSequence;

    /**
     * 商家ID
     */
    private Long    sellerId;

    private String  feature;

    private String  remark;

    private Date    gmtCreate;

    private Date    gmtModified;

    private int     status;

    private Integer cancelQuantity;

    private Integer recycledQuantity;

    public Integer getRecycledQuantity() {
        return recycledQuantity;
    }

    public void setRecycledQuantity(Integer recycledQuantity) {
        this.recycledQuantity = recycledQuantity;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public Long getCpId() {
        return cpId;
    }

    public void setCpId(Long cpId) {
        this.cpId = cpId;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public int getCpType() {
        return cpType;
    }

    public void setCpType(int cpType) {
        this.cpType = cpType;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getAllocatedQuantity() {
        return allocatedQuantity;
    }

    public void setAllocatedQuantity(Integer allocatedQuantity) {
        this.allocatedQuantity = allocatedQuantity;
    }

    public Integer getConfirmQuantity() {
        return confirmQuantity;
    }

    public void setConfirmQuantity(Integer confirmQuantity) {
        this.confirmQuantity = confirmQuantity;
    }

    public Integer getAlarmQuantity() {
        return alarmQuantity;
    }

    public void setAlarmQuantity(Integer alarmQuantity) {
        this.alarmQuantity = alarmQuantity;
    }

    public Long getCurrentSequence() {
        return currentSequence;
    }

    public void setCurrentSequence(Long currentSequence) {
        this.currentSequence = currentSequence;
    }

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Integer getCancelQuantity() {
        return cancelQuantity;
    }

    public void setCancelQuantity(Integer cancelQuantity) {
        this.cancelQuantity = cancelQuantity;
    }

    public String getSegmentCode() {
        return segmentCode;
    }

    public void setSegmentCode(String segmentCode) {
        this.segmentCode = segmentCode;
    }
}
