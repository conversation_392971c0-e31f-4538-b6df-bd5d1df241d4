package com.cainiao.waybill.bridge.enterprise.outbound.service.impl;

import com.cainiao.waybill.bridge.common.constants.BridgeConstants;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseSwitchHolder;
import com.cainiao.waybill.bridge.enterprise.common.constant.EnterpriseOrderFeatureConstant;
import com.cainiao.waybill.bridge.enterprise.common.enums.*;
import com.cainiao.waybill.bridge.enterprise.outbound.request.EnterprisePackBoundRequest;
import com.cainiao.waybill.bridge.enterprise.outbound.request.OrderPickUpPushRequest;
import com.cainiao.waybill.bridge.enterprise.outbound.response.PackCpInfo;
import com.cainiao.waybill.bridge.enterprise.outbound.service.OutBoundService;
import com.cainiao.waybill.bridge.enterprise.outbound.service.WaybillBridgeEnterprisePackBoundService;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterprisePackInboundConverter;
import com.cainiao.waybill.bridge.enterprise.utils.RandomNumberGeneratorUtils;
import com.cainiao.waybill.bridge.model.domain.*;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeEnterprisePackBoundDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePackInboundMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePostMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillEnterpriseLocationMapper;
import com.cainiao.waybill.common.util.FeatureUtils;
import com.cainiao.waybill.number.client.WaybillNumberCommonService;
import com.cainiao.waybill.number.client.dto.PossibleCpCodesByWaybillCodeRequest;
import com.cainiao.waybill.number.client.dto.PossibleCpCodesByWaybillCodeResponse;
import com.cainiao.waybill.number.common.ClientInfoDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/17
 **/
@Service
public class WaybillBridgeEnterprisePackBoundServiceImpl implements WaybillBridgeEnterprisePackBoundService {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private DingTalkAuthService dingTalkAuthService;

    @Autowired
    private WaybillNumberCommonService waybillNumberCommonService;

    @Autowired
    private WaybillBridgeEnterprisePackInboundConverter enterprisePackInboundConverter;

    @Autowired
    private WaybillBridgeEnterprisePackInboundMapper enterprisePackInboundMapper;

    @Autowired
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;

    @Autowired
    private OutBoundService outBoundService;

    @Autowired
    private WaybillBridgeEnterprisePostMapper enterprisePostMapper;

    @Autowired
    private WaybillEnterpriseLocationMapper enterpriseLocationMapper;

    private static final List<String> adminList = Lists.newArrayList(EnterpriseUserTypeEnum.ADMIN.name(), EnterpriseUserTypeEnum.POST_ADMIN.name());


    @Override
    public BridgePagingDTO<WaybillBridgeEnterprisePackBoundDTO> outboundPage(EnterprisePackBoundRequest request) {
        if (StringUtils.isEmpty(request.getSourceFrom())) {
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "来源平台不可为空");
        }
        WaybillBridgeEnterprisePackInboundParam param = new WaybillBridgeEnterprisePackInboundParam();
        param.setPage(true);
        param.setPagination(request.getCurrentPage(), request.getPageSize());
        param.appendOrderByClause(WaybillBridgeEnterprisePackInboundParam.OrderCondition.ID, WaybillBridgeEnterprisePackInboundParam.SortType.DESC);
        WaybillBridgeEnterprisePackInboundParam.Criteria paramCriteria = param.createCriteria();

        // 查询角色权限,用户端为取件人,其余端为当前登录人
        String userId;
        if (StringUtils.equals(EnterpriseSourceFormEnum.USER.name(), request.getSourceFrom()) ||
                StringUtils.equals(EnterpriseSourceFormEnum.MOBILE.name(), request.getSourceFrom())) {
            userId = request.getConsigneeUserId();
        } else {
            userId = request.getOperatorUserId();
        }

        WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectByUserId(userId, request.getCorpId());
        if (waybillBridgeEnterpriseUserInfoDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "用户信息异常");
        }

        // 非管理员默认查看收件人为自己的快递
        // 用户端,不分角色只能看收件人为自己的数据
        // 移动端，不分角色看收件人为自己的数据
        paramCriteria.andCorpIdEqualTo(request.getCorpId());
        if (!adminList.contains(waybillBridgeEnterpriseUserInfoDO.getUserType()) ||
                StringUtils.equals(EnterpriseSourceFormEnum.USER.name(), request.getSourceFrom()) ||
                StringUtils.equals(EnterpriseSourceFormEnum.MOBILE.name(), request.getSourceFrom())) {
            if (StringUtils.isEmpty(userId)) {
                throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), EnterpriseErrorEnum.PARAM_ERROR.describe());
            }
            paramCriteria.andUserIdEqualTo(userId);
        }
        // 小邮局管理员默认查询自己管理小邮局下的数据
        if (StringUtils.equals(EnterpriseUserTypeEnum.POST_ADMIN.name(),waybillBridgeEnterpriseUserInfoDO.getUserType())) {
            paramCriteria.andPostIdEqualTo(waybillBridgeEnterpriseUserInfoDO.getPostId());
        } else {
            if (Objects.nonNull(request.getPostId())) {
                WaybillBridgeEnterprisePostParam postParam = new WaybillBridgeEnterprisePostParam();
                postParam.createCriteria()
                        .andPostIdEqualTo(request.getPostId())
                        .andCorpIdEqualTo(request.getCorpId());
                WaybillBridgeEnterprisePostDO postDO = enterprisePostMapper.selectOneByParam(postParam);
                if (postDO == null) {
                    throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "小邮局不存在");
                }
                paramCriteria.andPostIdEqualTo(postDO.getId());
            }
        }
        if (StringUtils.isNotEmpty(request.getWaybillCode())) {
            paramCriteria.andWaybillCodeEqualTo(request.getWaybillCode());
        }
        if (StringUtils.isNotEmpty(request.getConsigneeName())) {
            paramCriteria.andConsigneeNameEqualTo(request.getConsigneeName());
        }
        if (StringUtils.isNotEmpty(request.getConsigneePhone())) {
            paramCriteria.andConsigneePhoneEqualTo(request.getConsigneePhone());
        }
        if (StringUtils.isNotEmpty(request.getConsigneeMobile())) {
            paramCriteria.andConsigneeMobileEqualTo(request.getConsigneeMobile());
        }
        if (StringUtils.isNotEmpty(request.getPickUpCode())) {
            paramCriteria.andPickUpCodeEqualTo(request.getPickUpCode());
        }
        if (Objects.nonNull(request.getStatus())) {
            paramCriteria.andStatusEqualTo(request.getStatus().byteValue());
        }
        // 管理端/员工端可不传场地
        if (Objects.isNull(request.getLocationId())) {
            if (!StringUtils.equals(EnterpriseSourceFormEnum.USER.name(), request.getSourceFrom()) &&
                !StringUtils.equals(EnterpriseSourceFormEnum.ADMIN.name(), request.getSourceFrom())) {
                throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "场地不可为空");
            }
        } else {
            WaybillEnterpriseLocationParam locationParam = new WaybillEnterpriseLocationParam();
            locationParam.createCriteria()
                    .andLocationIdEqualTo(request.getLocationId())
                    .andCorpIdEqualTo(request.getCorpId());
            WaybillEnterpriseLocationDO locationDO = enterpriseLocationMapper.selectOneByParam(locationParam);
            if (locationDO == null) {
                throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "场地不存在");
            }
            paramCriteria.andLocationIdEqualTo(locationDO.getId());
        }

        try {
            if (request.getInboundTimeStart() != null && request.getInboundTimeEnd() != null) {
                paramCriteria.andInboundTimeBetween(DateUtils.strToDate(request.getInboundTimeStart(), DateUtils.defaultPattern),
                        DateUtils.strToDate(request.getInboundTimeEnd(), DateUtils.defaultPattern));
            }
            if (request.getOutboundTimeStart() != null && request.getOutboundTimeEnd() != null) {
                paramCriteria.andOutboundTimeBetween(DateUtils.strToDate(request.getOutboundTimeStart(), DateUtils.defaultPattern),
                        DateUtils.strToDate(request.getOutboundTimeEnd(), DateUtils.defaultPattern));
            }

            long totalCount = enterprisePackInboundMapper.countByParam(param);
            List<WaybillBridgeEnterprisePackInboundDO> enterprisePackInboundDOList = enterprisePackInboundMapper.selectByParam(param);
            // 员工端过滤异常件
            if (StringUtils.equals(EnterpriseSourceFormEnum.USER.name(), request.getSourceFrom())) {
                List<WaybillBridgeEnterprisePackInboundDO> filterList = ListUtil.non(enterprisePackInboundDOList).stream()
                        .filter(enterprisePackInboundDO -> enterprisePackInboundDO.getStatus().intValue() != EnterprisePackInboundStatusEnum.ABNORMAL_PACK.getCode())
                        .collect(Collectors.toList());
                return BridgePagingDTO.build(enterprisePackInboundConverter.convertFromDOList(filterList),
                        filterList.size(), request.getCurrentPage(), request.getPageSize());
            }
            return BridgePagingDTO.build(enterprisePackInboundConverter.convertFromDOList(enterprisePackInboundDOList),
                    totalCount, request.getCurrentPage(), request.getPageSize());
        } catch (Exception e) {
            LOGGER.error("outboundPage error :{}", ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public int expressInStore(EnterprisePackBoundRequest request) {
        WaybillBridgeEnterprisePackInboundParam param = new WaybillBridgeEnterprisePackInboundParam();
        param.createCriteria().andWaybillCodeEqualTo(request.getWaybillCode());
        WaybillBridgeEnterprisePackInboundDO waybillBridgeEnterprisePackInboundDO = enterprisePackInboundMapper.selectOneByParam(param);
        if (waybillBridgeEnterprisePackInboundDO != null) {
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "快递单已入库" + request.getWaybillCode());
        }
        WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectByUserId(request.getOperatorUserId(), request.getCorpId());
        if (waybillBridgeEnterpriseUserInfoDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "操作人不存在");
        }

        try {
            WaybillBridgeEnterprisePackInboundDO insertDO = new WaybillBridgeEnterprisePackInboundDO();
            insertDO.setGmtCreate(new Date());
            insertDO.setGmtModified(new Date());
            insertDO.setWaybillCode(request.getWaybillCode());
            if (request.getEmployee() != null && request.getEmployee()) {
                insertDO.setUserId(request.getConsigneeUserId());
                insertDO.setConsigneeName(request.getConsigneeName());
                insertDO.setConsigneePhone(request.getConsigneePhone());
                insertDO.setConsigneeMobile(request.getConsigneeMobile());
                insertDO.setStatus(EnterprisePackInboundStatusEnum.PENDING_PICK_UP.getCode().byteValue());
            } else {
                // 无员工信息视为异常件
                insertDO.setStatus(EnterprisePackInboundStatusEnum.ABNORMAL_PACK.getCode().byteValue());
                insertDO.setConsigneeName(request.getConsigneeName());
                insertDO.setConsigneePhone(request.getConsigneePhone());
                insertDO.setConsigneeMobile(request.getConsigneeMobile());
            }
            insertDO.setBizType(Byte.valueOf(request.getBizType()));
            insertDO.setCpCode(request.getCpCode());
            insertDO.setPickUpCode(String.valueOf(RandomNumberGeneratorUtils.generateRandomFourDigitNumber()));
            insertDO.setInboundTime(new Date());
            insertDO.setShelf(request.getShelf());
            insertDO.setOperatorUserId(request.getOperatorUserId());

            WaybillEnterpriseLocationParam locationParam = new WaybillEnterpriseLocationParam();
            locationParam.createCriteria()
                    .andLocationIdEqualTo(request.getLocationId())
                    .andCorpIdEqualTo(request.getCorpId());
            WaybillEnterpriseLocationDO locationDO = enterpriseLocationMapper.selectOneByParam(locationParam);
            if (locationDO == null) {
                throw new EnterpriseException(EnterpriseErrorEnum.LOCATION_NOT_EXIST.code(), EnterpriseErrorEnum.LOCATION_NOT_EXIST.describe());
            }
            insertDO.setLocationId(locationDO.getId());

            WaybillBridgeEnterprisePostParam postParam = new WaybillBridgeEnterprisePostParam();
            postParam.createCriteria()
                    .andPostIdEqualTo(request.getPostId())
                    .andCorpIdEqualTo(request.getCorpId());
            WaybillBridgeEnterprisePostDO postDO = enterprisePostMapper.selectOneByParam(postParam);
            if (postDO == null) {
                throw new EnterpriseException(EnterpriseErrorEnum.POST_NOT_EXIST.code(), EnterpriseErrorEnum.POST_NOT_EXIST.describe());
            }
            insertDO.setPostId(postDO.getId());

            insertDO.setRemark(request.getRemark());
            insertDO.setCorpId(request.getCorpId());
            Map<String, String> featureMap = Maps.newHashMap();
            if (request.getDelivery() != null) {
                featureMap.put(EnterpriseOrderFeatureConstant.DELIVERY, String.valueOf(request.getDelivery()));
            }
            insertDO.setFeature(FeatureUtils.parseFromMap(featureMap));
            int insertResult = enterprisePackInboundMapper.insert(insertDO);

            // 发送取件消息
            OrderPickUpPushRequest pickUpPushRequest = new OrderPickUpPushRequest();
            pickUpPushRequest.setCorpId(request.getCorpId());
            pickUpPushRequest.setCpName(EnterpriseSwitchHolder.CP_CODE_MAPPING.get(request.getCpCode()));
            pickUpPushRequest.setPickupCode(insertDO.getPickUpCode());
            pickUpPushRequest.setUserId(request.getConsigneeUserId());
            pickUpPushRequest.setWaybillCode(request.getWaybillCode());
            outBoundService.pushOrderPickUpMessage(pickUpPushRequest);

            return insertResult;
        } catch (Exception e) {
            LOGGER.error("expressInStore error :{}", ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public List<String> batchPickUp(EnterprisePackBoundRequest request) {
        if (CollectionUtils.isEmpty(request.getBatchOutBoundIdList()) || StringUtils.isEmpty(request.getPickUpUserId())) {
            throw  new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), EnterpriseErrorEnum.PARAM_ERROR.describe());
        }
        WaybillBridgeEnterpriseUserInfoDO waybillBridgeEnterpriseUserInfoDO = enterpriseUserInfoMapper.selectByUserId(request.getPickUpUserId(), request.getCorpId());
        if (waybillBridgeEnterpriseUserInfoDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "取件人不存在");
        }
        List<String> errorList = Lists.newArrayList();
        List<Long> batchOutBoundIdList = request.getBatchOutBoundIdList();
        for (Long id : batchOutBoundIdList) {
            WaybillBridgeEnterprisePackInboundDO waybillBridgeEnterprisePackInboundDO = enterprisePackInboundMapper.selectByPrimaryKey(id);
            if (waybillBridgeEnterprisePackInboundDO == null) {
                break;
            }
            if (Objects.equals(waybillBridgeEnterprisePackInboundDO.getStatus(), EnterprisePackInboundStatusEnum.ABNORMAL_PACK.getCode().byteValue())) {
                // 异常件默认管理员取件
                if (!StringUtils.equals(waybillBridgeEnterpriseUserInfoDO.getUserType(), EnterpriseUserTypeEnum.ADMIN.name())) {
                    errorList.add(String.format("快递%s为异常单,请联系管理员取件", waybillBridgeEnterprisePackInboundDO.getWaybillCode()));
                    break;
                }
            }
            waybillBridgeEnterprisePackInboundDO.setGmtModified(new Date());
            waybillBridgeEnterprisePackInboundDO.setOutboundTime(new Date());
            waybillBridgeEnterprisePackInboundDO.setStatus(EnterprisePackInboundStatusEnum.ALREADY_PICK_UP.getCode().byteValue());
            waybillBridgeEnterprisePackInboundDO.setPickUpUserId(request.getPickUpUserId());
            enterprisePackInboundMapper.updateByPrimaryKey(waybillBridgeEnterprisePackInboundDO);
        }
        return errorList;
    }

    @Override
    public PackCpInfo queryPossibleCpCodesByWaybillCode(String waybillCode) {
        if (StringUtils.isEmpty(waybillCode)) {
            return null;
        }
        PossibleCpCodesByWaybillCodeRequest req = new PossibleCpCodesByWaybillCodeRequest();
        req.setWaybillCode(waybillCode);
        ClientInfoDTO clientInfoDTO = new ClientInfoDTO();
        clientInfoDTO.setAppName(BridgeConstants.System.APP_NAME);
        com.cainiao.waybill.number.common.Result<List<PossibleCpCodesByWaybillCodeResponse>> possibleCpResult =
                waybillNumberCommonService.queryPossibleCpCodesByWaybillCode(req, clientInfoDTO);
        if (possibleCpResult == null || !possibleCpResult.isSuccess()) {
            LOGGER.error("queryPossibleCpCodesByWaybillCode error, waybillCode:{}, errorMsg:{}", waybillCode, possibleCpResult);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "查询快递公司失败");
        }
        PackCpInfo packCpInfo = new PackCpInfo();
        if (CollectionUtils.isEmpty(possibleCpResult.getValue())) {
            LOGGER.error("queryPossibleCpCodesByWaybillCode empty, waybillCode:{}", waybillCode);
            packCpInfo.setCpCode(ExpressTypeEnum.OTHER.expressCode);
            packCpInfo.setCpName(ExpressTypeEnum.OTHER.desc);
            return packCpInfo;
        }
        String cpCode = possibleCpResult.getValue().get(0).getCpCode();
        packCpInfo.setCpCode(cpCode);
        String cpName = EnterpriseSwitchHolder.CP_CODE_MAPPING.get(cpCode);
        if (StringUtils.isNotEmpty(cpName)) {
            packCpInfo.setCpName(cpName);
        } else {
            packCpInfo.setCpName(ExpressTypeEnum.OTHER.desc);
        }
        return packCpInfo;
    }

    @Override
    public Map<String, String> supportCpMapping() {
        return EnterpriseSwitchHolder.CP_CODE_MAPPING;
    }
}
