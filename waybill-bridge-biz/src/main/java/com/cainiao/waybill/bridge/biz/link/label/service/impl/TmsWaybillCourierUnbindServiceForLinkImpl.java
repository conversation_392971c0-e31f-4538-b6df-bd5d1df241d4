//package com.cainiao.waybill.bridge.biz.link.label.service.impl;
//
//import javax.annotation.Resource;
//
//import com.cainiao.waybill.bridge.biz.label.manager.BranchSellerAddressRelationManager;
//import com.cainiao.waybill.bridge.common.base.Constants.ErrorConstant;
//import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.SystemError;
//import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
//import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
//import com.cainiao.waybill.bridge.common.label.dto.request.UnbindCourierRequest;
//import com.cainiao.waybill.bridge.common.validator.BridgeBaseValidator;
//import com.taobao.pac.api.open.ReceiveParams;
//import com.taobao.pac.client.sdk.dataobject.request.TMS_WAYBILL_COURIER_UNBIND.TmsWaybillCourierUnbindRequest;
//import com.taobao.pac.client.sdk.dataobject.response.TMS_WAYBILL_COURIER_UNBIND.TmsWaybillCourierUnbindResponse;
//import com.taobao.pac.client.sdk.receiveservice.TmsWaybillCourierUnbindService;
//import org.apache.commons.lang.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
///**
// * 提供给cp解绑商家和小件员关系的link接口实现
// *
// * <AUTHOR>
// *
// */
//public class TmsWaybillCourierUnbindServiceForLinkImpl implements TmsWaybillCourierUnbindService {
//	private final static Logger logger = LoggerFactory.getLogger(LogAppender.LINK_CP);
//
//	@Resource
//	BranchSellerAddressRelationManager branchSellerAddressRelationManager;
//
//	@Override
//	public TmsWaybillCourierUnbindResponse invoke(ReceiveParams<TmsWaybillCourierUnbindRequest> receiveParams) {
//		try {
//			/**
//			 * step1:参数校验
//			 */
//			BridgeBaseValidator.validate(StringUtils.isNotBlank(receiveParams.getCpCode()), "cpCode 不能为空");
//			BridgeBaseValidator.validate(StringUtils.isNotBlank(receiveParams.getRequestDataObject().getBranchCode()),
//					"branchCode 不能为空");
//			BridgeBaseValidator.validate(receiveParams.getRequestDataObject().getCourierId() != null, "courierId 不能为空");
//			BridgeBaseValidator.validate(receiveParams.getRequestDataObject().getRelationId() != null, "id不能为空");
//			BridgeBaseValidator.validate(receiveParams.getRequestDataObject().getSellerId() != null, "sellerId不能为空");
//
//			/**
//			 * step2:入参转换并调用核心业务方法
//			 */
//			UnbindCourierRequest request = new UnbindCourierRequest();
//			request.setBranchCode(receiveParams.getRequestDataObject().getBranchCode());
//			request.setCpCode(receiveParams.getCpCode());
//			request.setId(receiveParams.getRequestDataObject().getRelationId());
//			request.setSellerId(receiveParams.getRequestDataObject().getSellerId());
//			branchSellerAddressRelationManager.unbindCourier(request, receiveParams.getCpCode());
//			/**
//			 * step3:出参转换并返回
//			 */
//			TmsWaybillCourierUnbindResponse rsp = new TmsWaybillCourierUnbindResponse();
//			rsp.setSuccess(true);
//			return rsp;
//		} catch (BridgeValidationException e) {
//			logger.error("TmsWaybillCourierUnbindService#invoke,BridgeValidationException errorCode=" + e.getErrorCode()
//					+ ",errorMsg=" + e.getErrorMessage() + ",receiveParams=" + receiveParams, e);
//			return failure(e.getErrorCode(), e.getErrorMessage());
//		} catch (Exception e) {
//			logger.error("TmsWaybillCourierUnbindService#invoke,Exception receiveParams=" + receiveParams, e);
//			return failure(SystemError.SYSTEM_BUSY);
//		}
//	}
//
//	/**
//	 * @apiNote 失败返回值封装方法
//	 */
//	private TmsWaybillCourierUnbindResponse failure(ErrorConstant error) {
//		TmsWaybillCourierUnbindResponse rsp = new TmsWaybillCourierUnbindResponse();
//		rsp.setSuccess(false);
//		rsp.setErrorCode(error.getErrorCode());
//		rsp.setErrorMsg(error.getErrorMsg());
//		return rsp;
//	}
//
//	/**
//	 * @apiNote 失败返回值封装方法
//	 */
//	private TmsWaybillCourierUnbindResponse failure(String errorCode, String errorMsg) {
//		TmsWaybillCourierUnbindResponse rsp = new TmsWaybillCourierUnbindResponse();
//		rsp.setSuccess(false);
//		rsp.setErrorCode(errorCode);
//		rsp.setErrorMsg(errorMsg);
//		return rsp;
//	}
//}
