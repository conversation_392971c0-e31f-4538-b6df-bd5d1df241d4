package com.cainiao.waybill.bridge.enterprise.common.enums;

public enum EnterpriseProductTypeEnum implements CodeDescribe {
    /**
     * 顺丰标快产品
     */
    SF_EXPRESS("SF_EXPRESS", "顺丰标快"),
    /**
     * 特快
     */
    EXPRESS("EXPRESS", "特快"),

    /**
     * 同城
     */
    TC("TC", "同城"),

    /**
     * 标快
     */
    STANDARD("STANDARD", "标快");


    final String code;
    final String describe;

    EnterpriseProductTypeEnum(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    @Override
    public String code() {
        return code;
    }

    @Override
    public String describe() {
        return describe;
    }

    /**
     * 获取产品类型
     * @param code
     * @return
     */
    public static String getProductType(String code) {
        for (EnterpriseProductTypeEnum productTypeEnum : EnterpriseProductTypeEnum.values()) {
            if (productTypeEnum.code().equals(code)) {
                return productTypeEnum.describe;
            }
        }
        return null;
    }
}
