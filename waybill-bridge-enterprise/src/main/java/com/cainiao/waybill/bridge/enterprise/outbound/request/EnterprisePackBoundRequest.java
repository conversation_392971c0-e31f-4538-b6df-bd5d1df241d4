package com.cainiao.waybill.bridge.enterprise.outbound.request;

import com.cainiao.waybill.bridge.enterprise.common.Paging;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePackInboundBizTypeEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePackInboundStatusEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseSourceFormEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/17
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class EnterprisePackBoundRequest extends Paging {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 状态
     * @see EnterprisePackInboundStatusEnum
     */
    private Integer status;

    /**
     * cp的编码
     */
    private String cpCode;

    /**
     * 快递业务类型
     * @see EnterprisePackInboundBizTypeEnum
     */
    private String bizType;

    /**
     * 收件人-id
     */
    private String consigneeUserId;

    /**
     * 收件人-姓名
     */
    private String consigneeName;

    /**
     * 收件人-电话
     */
    private String consigneePhone;

    /**
     * 收件人-手机
     */
    private String consigneeMobile;

    /**
     * 取件码
     */
    private String pickUpCode;

    /**
     * 取件人用户ID
     */
    private String pickUpUserId;

    /**
     * 货架
     */
    private String shelf;

    /**
     * 入库时间
     */
    private String inboundTimeStart;

    /**
     * 入库时间
     */
    private String inboundTimeEnd;

    /**
     * 出库时间
     */
    private String outboundTimeStart;

    /**
     * 出库时间
     */
    private String outboundTimeEnd;

    /**
     * 操作人用户id
     */
    private String operatorUserId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 场地ID
     */
    private String locationId;

    /**
     * 小邮局ID
     */
    private String postId;

    /**
     * 来源
     * @see EnterpriseSourceFormEnum
     */
    private String sourceFrom;

    /**
     * 批量出库
     */
    private List<Long> batchOutBoundIdList;

    /**
     * 是否员工
     */
    private Boolean employee;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 是否到付
     */
    private Integer delivery;
}
