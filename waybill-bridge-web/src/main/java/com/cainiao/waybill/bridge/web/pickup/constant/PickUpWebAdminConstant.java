package com.cainiao.waybill.bridge.web.pickup.constant;

import com.taobao.cainiao.waybill.base.Constants;

/**
 * <AUTHOR>
 * @date 2021/11/10-下午5:45
 */
public interface PickUpWebAdminConstant {
    enum LogAction {
        PLATFORM_ORDER_REPORT, PLATFORM_ORDER_DETAIL, PLATFORM_DETAIL_EXCEPTION_CANCEL, PLATFORM_DETAIL_EXCEPTION_RE_PUSH_WEIGHT,
        PLATFORM_DETAIL_LD, PLATFORM_DETAIL_EXPORT, PLATFORM_DETAIL_QUERY_WITH_NO,
        CP_ORDER_REPORT, CP_ORDER_REPORT_PRO, CP_ORDER_DETAIL,
        VIRTUAL_RECORD_QUERY,PLATFORM_ORDER_REPORT_PRO
    }

    interface Permission {
        String WAYBILL_PICK_UP_DETAIL_ORDER_EXPORT = "waybillPickUpDetailOrderExport";
    }

    interface DiamondConfig {
        String PICK_UP_ORDER_REPORT_DATA_ID = "cone.micro.config_waybill-bridge";
        String PICK_UP_ORDER_REPORT_GROUP_ID = "cone.micro";

        String PICK_UP_ORDER_REPORT_MICRO_DATA_ID = "cone.micro.config_waybill-overseas-console";
        String PICK_UP_ORDER_REPORT_MICRO_GROUP_ID = "cone.micro";

        String PICK_UP_GUOGUO_CONFIG_DATA_ID = "pick.up.guoguo.config.data.id";
        String PICK_UP_GUOGUO_CONFIG_GROUP_ID = "pick.up.guoguo.config.group.id";
    }

    interface Error {
        Constants.ErrorConstant WEB_ADMIN_SYSTEM_ERROR = newConstant("WEB_ADMIN_SYSTEM_ERROR", "淘外运营后台系统异常");
        Constants.ErrorConstant WEB_ADMIN_PARAM_EMPTY = newConstant("WEB_ADMIN_PARAM_EMPTY", "请求参数不可为空!");
        Constants.ErrorConstant WEB_ADMIN_NO_EXPORT_PERMISSION = newConstant("WEB_ADMIN_NO_PERMISSION", "无数据权限，请联系技术小二添加！");
    }

    static Constants.ErrorConstant newConstant(String errorCode, String errorMsg) {
        return new Constants.ErrorConstant().set(errorCode, errorMsg);
    }
}
