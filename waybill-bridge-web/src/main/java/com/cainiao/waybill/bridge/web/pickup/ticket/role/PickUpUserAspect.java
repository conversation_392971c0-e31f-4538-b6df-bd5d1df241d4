package com.cainiao.waybill.bridge.web.pickup.ticket.role;

import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpBaseRequest;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zouping.fzp
 * @Classname UserAspect
 * @Description
 * @Date 2022/9/15 7:32 下午
 * @Version 1.0
 */
@Component
@Order(1)
@Aspect
public class PickUpUserAspect {

    @Around("execution(* com.cainiao.waybill.bridge.web.pickup..*.*(..)) || execution(* com.cainiao.waybill.bridge.web.fast..*.*(..))")
    public Object userPop(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        if (args != null) {
            for (Object arg : args) {
                if (arg instanceof PickUpBaseRequest) {
                    PickUpLoginUserUtil.popLoginInfo((PickUpBaseRequest)arg);
                }
            }
        }
        Object object = joinPoint.proceed();
        PickUpLoginUserUtil.clear();
        return object;
    }

}
