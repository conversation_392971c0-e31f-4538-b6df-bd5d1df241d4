package com.cainiao.waybill.bridge.common.dto;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname CpCodeAndBrandCodeDTO
 * @Description
 * @Date 2022/11/15 9:19 下午
 * @Version 1.0
 */
@Data
public class CpCodeAndBrandCodeDTO implements Serializable {

    private static final long serialVersionUID = -8173813849589268205L;

    /**
     * cpCode
     */
    private String cpCode;

    /**
     * 平台code
     */
    private String brandCode;
}
