package com.cainiao.waybill.bridge.enterprise.administrators.service.impl;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.administrators.request.LocationRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.service.DingTalkLocationService;
import com.cainiao.waybill.bridge.enterprise.common.constant.EnterpriseCommonConstant;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseUserStatusEnum;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseUserTypeEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterpriseUserInfoConverter;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillEnterpriseLocationConverter;
import com.cainiao.waybill.bridge.enterprise.utils.AddressUtils;
import com.cainiao.waybill.bridge.enterprise.utils.RandomNumberGeneratorUtils;
import com.cainiao.waybill.bridge.model.domain.*;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillAccount;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseLocationDTO;
import com.cainiao.waybill.bridge.model.mapper.*;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class DingTalkLocationServiceImpl implements DingTalkLocationService {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private WaybillEnterpriseLocationMapper enterpriseLocationMapper;

    @Autowired
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;

    @Autowired
    private WaybillBridgeEnterprisePrinterMapper enterprisePrinterMapper;

    @Autowired
    private WaybillBridgeEnterprisePostMapper enterprisePostMapper;

    @Autowired
    private WaybillBridgeEnterpriseSettleAccountMapper enterpriseSettleAccountMapper;

    /**
     * 获取办公场地列表
     *
     * @param request request
     * @return 用户信息
     */
    @Override
    public BridgePagingDTO<WaybillEnterpriseLocationDTO> pageList(LocationRequest request) {

        WaybillEnterpriseLocationParam enterpriseLocationParam = new WaybillEnterpriseLocationParam();
        enterpriseLocationParam.setPage(true);
        enterpriseLocationParam.setPagination(request.getCurrentPage(), request.getPageSize());
        enterpriseLocationParam.appendOrderByClause(WaybillEnterpriseLocationParam.OrderCondition.ID, WaybillEnterpriseLocationParam.SortType.DESC);
        enterpriseLocationParam.createCriteria().andCorpIdEqualTo(request.getCorpId());

        long totalCount = enterpriseLocationMapper.countByParam(enterpriseLocationParam);

        List<WaybillEnterpriseLocationDO> enterpriseLocationDOList = enterpriseLocationMapper.selectByParam(enterpriseLocationParam);

        return BridgePagingDTO.build(WaybillEnterpriseLocationConverter.convertFromDOList(enterpriseLocationDOList),
                totalCount, request.getCurrentPage(), request.getPageSize());
    }

    @Override
    public WaybillEnterpriseLocationDO getLocationByName(LocationRequest request) {
        WaybillEnterpriseLocationParam enterpriseLocationParam = new WaybillEnterpriseLocationParam();
        enterpriseLocationParam.createCriteria()
                .andCorpIdEqualTo(request.getCorpId())
                .andLocationNameEqualTo(request.getLocationName());
        return enterpriseLocationMapper.selectOneByParam(enterpriseLocationParam);
    }

    /**
     * 添加办公场地
     *
     * @param request request
     * @return 是否成功
     */
    @Override
    public String addLocation(LocationRequest request) {
        if(StringUtils.isEmpty(request.getCorpId()) || StringUtils.isEmpty(request.getLocationName())){
            LOGGER.info("add poster location failed");
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), EnterpriseErrorEnum.PARAM_ERROR.describe());
        }
        if(getLocationByName(request) != null) {
            LOGGER.info("add poster location failed, locationName:{} has exist", request.getLocationName());
            throw new EnterpriseException(EnterpriseErrorEnum.LOCATION_HAS_EXIST.code(), EnterpriseErrorEnum.LOCATION_HAS_EXIST.describe());
        }
        WaybillEnterpriseLocationDO enterpriseLocationDO = new WaybillEnterpriseLocationDO();
        enterpriseLocationDO.setGmtCreate(new Date());
        enterpriseLocationDO.setGmtModified(new Date());
        enterpriseLocationDO.setLocationName(request.getLocationName());
        enterpriseLocationDO.setLocationAddress(AddressUtils.convertToFormattedString(request.getLocationAddress()));
        enterpriseLocationDO.setLocationAddressCode(AddressUtils.convertToFormattedString(request.getLocationAddressCode()));
        enterpriseLocationDO.setCorpId(request.getCorpId());
        enterpriseLocationDO.setRemark(request.getRemark());
        String locationId = RandomNumberGeneratorUtils.generateRandomNumberWithPrefix(EnterpriseCommonConstant.LOCATION_ID_PREFIX, 10);
        enterpriseLocationDO.setLocationId(locationId);
        try {
            enterpriseLocationMapper.insert(enterpriseLocationDO);
            return locationId;
        } catch (Exception e) {
            LOGGER.error("insert enterprise location occur unknown error, request:{}", JSON.toJSONString(request), e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public int updateLocation(LocationRequest request) {
        WaybillEnterpriseLocationDO currentLocation = getLocationByName(request);
        if(currentLocation == null){
            LOGGER.info("update poster location failed, locationName:{} not exist", request.getLocationName());
            throw new EnterpriseException(EnterpriseErrorEnum.LOCATION_NOT_EXIST.code(), EnterpriseErrorEnum.LOCATION_NOT_EXIST.describe());
        }
        try {
            List<String> accountList = request.getWaybillAccountsList().stream()
                    .map(WaybillAccount::getAccount)
                    .collect(Collectors.toList());
            // 获取该场地已绑定的月结账号
            WaybillBridgeEnterpriseSettleAccountParam param = new WaybillBridgeEnterpriseSettleAccountParam();
            param.createCriteria().andLocationIdEqualTo(currentLocation.getId());
            List<WaybillBridgeEnterpriseSettleAccountDO> currentLocationSettleAccount = enterpriseSettleAccountMapper.selectByParam(param);
            // 根据已传入的月结账号判断是否需要移除月结账号
            if (CollectionUtils.isNotEmpty(currentLocationSettleAccount)) {
                List<String> removeAccountList = currentLocationSettleAccount.stream()
                        .map(WaybillBridgeEnterpriseSettleAccountDO::getWaybillAccountNo)
                        .collect(Collectors.toList());
                removeAccountList.removeAll(accountList);
                // 移除月结账号
                if (CollectionUtils.isNotEmpty(removeAccountList)) {
                    removeAccountList.forEach(accountNum -> {
                        WaybillBridgeEnterpriseSettleAccountParam accountParam = new WaybillBridgeEnterpriseSettleAccountParam();
                        accountParam.createCriteria()
                                .andWaybillAccountNoEqualTo(accountNum)
                                .andCorpIdEqualTo(request.getCorpId());
                        WaybillBridgeEnterpriseSettleAccountDO waybillBridgeEnterpriseSettleAccountDO = enterpriseSettleAccountMapper.selectOneByParam(accountParam);
                        if (waybillBridgeEnterpriseSettleAccountDO != null) {
                            waybillBridgeEnterpriseSettleAccountDO.setGmtModified(new Date());
                            waybillBridgeEnterpriseSettleAccountDO.setLocationId(null);
                            enterpriseSettleAccountMapper.updateByPrimaryKey(waybillBridgeEnterpriseSettleAccountDO);
                        }
                    });
                }
            }
            // 绑定月结账号
            accountList.forEach(accountNum -> {
                WaybillBridgeEnterpriseSettleAccountParam accountParam = new WaybillBridgeEnterpriseSettleAccountParam();
                accountParam.createCriteria()
                        .andWaybillAccountNoEqualTo(accountNum)
                        .andCorpIdEqualTo(request.getCorpId());
                WaybillBridgeEnterpriseSettleAccountDO waybillBridgeEnterpriseSettleAccountDO = enterpriseSettleAccountMapper.selectOneByParam(accountParam);
                if (waybillBridgeEnterpriseSettleAccountDO != null &&
                        !Objects.equals(waybillBridgeEnterpriseSettleAccountDO.getLocationId(), currentLocation.getId())) {
                    waybillBridgeEnterpriseSettleAccountDO.setGmtModified(new Date());
                    waybillBridgeEnterpriseSettleAccountDO.setLocationId(currentLocation.getId());
                    enterpriseSettleAccountMapper.updateByPrimaryKey(waybillBridgeEnterpriseSettleAccountDO);
                }
            });

            WaybillEnterpriseLocationDO locationDO = new WaybillEnterpriseLocationDO();
            locationDO.setWaybillAccountVid(request.getWaybillAccountsString());
            locationDO.setId(currentLocation.getId());
            locationDO.setGmtModified(new Date());
            locationDO.setLocationAddress(AddressUtils.convertToFormattedString(request.getLocationAddress()));
            locationDO.setLocationAddressCode(AddressUtils.convertToFormattedString(request.getLocationAddressCode()));
            locationDO.setRemark(request.getRemark());
            return enterpriseLocationMapper.updateByPrimaryKeySelective(locationDO);
        } catch (Exception e) {
            LOGGER.error("update enterprise location occur unknown error, request:{}", JSON.toJSONString(request), e);
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }

    }

    /**
     * 删除办公场地
     *
     * @param id id
     * @return 是否成功
     */
    @Override
    public int deleteLocationById(Long id) {
        if (id == null) {
            LOGGER.info("delete location failed, id is null");
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), EnterpriseErrorEnum.PARAM_ERROR.describe());
        }
        WaybillEnterpriseLocationDO waybillEnterpriseLocationDO = enterpriseLocationMapper.selectByPrimaryKey(id);
        if (waybillEnterpriseLocationDO == null) {
            LOGGER.info("delete location failed, id:{} not exist Location", id);
            throw new EnterpriseException(EnterpriseErrorEnum.LOCATION_NOT_EXIST.code(), EnterpriseErrorEnum.LOCATION_NOT_EXIST.describe());
        }

        // 删除关联小邮局
        WaybillBridgeEnterprisePostParam postParam = new WaybillBridgeEnterprisePostParam();
        postParam.createCriteria().andLocationIdEqualTo(id);
        List<WaybillBridgeEnterprisePostDO> waybillBridgeEnterprisePostDOList = enterprisePostMapper.selectByParam(postParam);
        if (CollectionUtils.isNotEmpty(waybillBridgeEnterprisePostDOList)) {
            waybillBridgeEnterprisePostDOList.forEach(waybillBridgeEnterprisePostDO -> {
                enterprisePostMapper.deleteByPrimaryKey(waybillBridgeEnterprisePostDO.getId());
            });
        }

        // 删除关联打印机
        WaybillBridgeEnterprisePrinterParam printerParam = new WaybillBridgeEnterprisePrinterParam();
        printerParam.createCriteria().andCorpIdEqualTo(waybillEnterpriseLocationDO.getCorpId())
                .andLocationNameEqualTo(waybillEnterpriseLocationDO.getLocationName());
        List<WaybillBridgeEnterprisePrinterDO> waybillBridgeEnterprisePrinterDOList = enterprisePrinterMapper.selectByParam(printerParam);
        if (CollectionUtils.isNotEmpty(waybillBridgeEnterprisePrinterDOList)) {
            waybillBridgeEnterprisePrinterDOList.forEach(waybillBridgeEnterprisePrinterDO -> {
                enterprisePrinterMapper.deleteByPrimaryKey(waybillBridgeEnterprisePrinterDO.getId());
            });
        }

        // 删除关联用户
        WaybillBridgeEnterpriseUserInfoParam userInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
        userInfoParam.createCriteria().andLocationIdEqualTo(id);
        List<WaybillBridgeEnterpriseUserInfoDO> waybillBridgeEnterpriseUserInfoDOList = enterpriseUserInfoMapper.selectByParam(userInfoParam);
        if (CollectionUtils.isNotEmpty(waybillBridgeEnterpriseUserInfoDOList)) {
            waybillBridgeEnterpriseUserInfoDOList.forEach(waybillBridgeEnterpriseUserInfoDO -> {
                waybillBridgeEnterpriseUserInfoDO.setGmtModified(new Date());
                if (StringUtils.isNotBlank(waybillBridgeEnterpriseUserInfoDO.getUserType()) &&
                        waybillBridgeEnterpriseUserInfoDO.getUserType().equals(EnterpriseUserTypeEnum.POST_ADMIN.name())) {
                    waybillBridgeEnterpriseUserInfoDO.setUserType(EnterpriseUserTypeEnum.EMPLOYEE.name());
                }
                enterpriseUserInfoMapper.updateByPrimaryKey(waybillBridgeEnterpriseUserInfoDO);
            });
        }
        return enterpriseLocationMapper.deleteByPrimaryKey(id);
    }

    /**
     * 根据id获取办公场地
     *
     * @param id id
     * @return 管理员
     */
    @Override
    public WaybillEnterpriseLocationDTO getById(Long id) {
        WaybillEnterpriseLocationDO waybillEnterpriseLocationDO = enterpriseLocationMapper.selectByPrimaryKey(id);
        return WaybillEnterpriseLocationConverter.convertFromDO(waybillEnterpriseLocationDO);
    }

    @Override
    public int deleteByLocationId(String locationId, String corpId) {
        WaybillEnterpriseLocationParam param = new WaybillEnterpriseLocationParam();
        param.createCriteria()
                .andCorpIdEqualTo(corpId)
                .andLocationIdEqualTo(locationId);
        WaybillEnterpriseLocationDO waybillEnterpriseLocationDO = enterpriseLocationMapper.selectOneByParam(param);
        if (waybillEnterpriseLocationDO == null) {
            LOGGER.info("delete location failed, locationId:{} not exist Location", locationId);
            throw new EnterpriseException(EnterpriseErrorEnum.LOCATION_NOT_EXIST.code(), EnterpriseErrorEnum.LOCATION_NOT_EXIST.describe());
        }

        // 删除关联小邮局
        WaybillBridgeEnterprisePostParam postParam = new WaybillBridgeEnterprisePostParam();
        postParam.createCriteria()
                .andLocationIdEqualTo(waybillEnterpriseLocationDO.getId())
                .andCorpIdEqualTo(waybillEnterpriseLocationDO.getCorpId());
        List<WaybillBridgeEnterprisePostDO> waybillBridgeEnterprisePostDOList = enterprisePostMapper.selectByParam(postParam);
        if (CollectionUtils.isNotEmpty(waybillBridgeEnterprisePostDOList)) {
            waybillBridgeEnterprisePostDOList.forEach(waybillBridgeEnterprisePostDO -> {
                enterprisePostMapper.deleteByPrimaryKey(waybillBridgeEnterprisePostDO.getId());
            });
        }

        // 删除关联打印机
        // TODO 打印机关联locationId
        WaybillBridgeEnterprisePrinterParam printerParam = new WaybillBridgeEnterprisePrinterParam();
        printerParam.createCriteria()
                .andCorpIdEqualTo(waybillEnterpriseLocationDO.getCorpId())
                .andLocationNameEqualTo(waybillEnterpriseLocationDO.getLocationName());
        List<WaybillBridgeEnterprisePrinterDO> waybillBridgeEnterprisePrinterDOList = enterprisePrinterMapper.selectByParam(printerParam);
        if (CollectionUtils.isNotEmpty(waybillBridgeEnterprisePrinterDOList)) {
            waybillBridgeEnterprisePrinterDOList.forEach(waybillBridgeEnterprisePrinterDO -> {
                enterprisePrinterMapper.deleteByPrimaryKey(waybillBridgeEnterprisePrinterDO.getId());
            });
        }

        // 删除关联用户
        WaybillBridgeEnterpriseUserInfoParam userInfoParam = new WaybillBridgeEnterpriseUserInfoParam();
        userInfoParam.createCriteria()
                .andLocationIdEqualTo(waybillEnterpriseLocationDO.getId())
                .andCorpIdEqualTo(waybillEnterpriseLocationDO.getCorpId());
        List<WaybillBridgeEnterpriseUserInfoDO> waybillBridgeEnterpriseUserInfoDOList = enterpriseUserInfoMapper.selectByParam(userInfoParam);
        if (CollectionUtils.isNotEmpty(waybillBridgeEnterpriseUserInfoDOList)) {
            waybillBridgeEnterpriseUserInfoDOList.forEach(waybillBridgeEnterpriseUserInfoDO -> {
                waybillBridgeEnterpriseUserInfoDO.setGmtModified(new Date());
                waybillBridgeEnterpriseUserInfoDO.setLocationId(null);
                // 小邮局管理员置为员工
                if (StringUtils.isNotBlank(waybillBridgeEnterpriseUserInfoDO.getUserType()) &&
                        waybillBridgeEnterpriseUserInfoDO.getUserType().equals(EnterpriseUserTypeEnum.POST_ADMIN.name())) {
                    waybillBridgeEnterpriseUserInfoDO.setUserType(EnterpriseUserTypeEnum.EMPLOYEE.name());
                }
                enterpriseUserInfoMapper.updateByPrimaryKey(waybillBridgeEnterpriseUserInfoDO);
            });
        }
        return enterpriseLocationMapper.deleteByPrimaryKey(waybillEnterpriseLocationDO.getId());

    }

    @Override
    public WaybillEnterpriseLocationDTO getLocationByLocationId(String locationId, String corpId) {
        WaybillEnterpriseLocationParam param = new WaybillEnterpriseLocationParam();
        param.createCriteria()
                .andCorpIdEqualTo(corpId)
                .andLocationIdEqualTo(locationId);
        WaybillEnterpriseLocationDO waybillEnterpriseLocationDO = enterpriseLocationMapper.selectOneByParam(param);
        return WaybillEnterpriseLocationConverter.convertFromDO(waybillEnterpriseLocationDO);
    }
}
