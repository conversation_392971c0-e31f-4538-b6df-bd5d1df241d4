package com.cainiao.waybill.bridge.ticket.api.enums;

import java.util.Objects;

/**
 * 工单操作者类型枚举值
 * <AUTHOR>
 */
public enum  TicketOperatorTypeEnum {

    TYPE_1(1,"消费者"),
    TYPE_2(2,"客服小二"),

    ;

    private Integer code;
    private String content;

    TicketOperatorTypeEnum(Integer code,String content){
        this.code = code;
        this.content = content;
    }

    public Integer getCode(){
        return code;
    }

    public String getContent(){
        return content;
    }

    public static String getContentByCode(Integer inputCode){
        if (inputCode == null){
            return null;
        }
        for (TicketOperatorTypeEnum ticketOperatorTypeEnum : TicketOperatorTypeEnum.values()) {
            if (Objects.equals(ticketOperatorTypeEnum.getCode(),inputCode)){
                return ticketOperatorTypeEnum.getContent();
            }
        }
        return null;
    }

    public static void main(String[] args) {
        System.out.println(getContentByCode(1));
    }

}
