package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import java.math.BigDecimal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.biz.link.pickup.service.impl.prequery.PickUpPreQueryManager;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.config.PickUpPlatformConfigDiamond;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpCreateOrderParamConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpDetailBizTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpBaseInfoRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.create.pre.*;
import com.cainiao.waybill.bridge.biz.pickup.dto.lite.LitePreChargeRequestDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.lite.LitePreChargeResultDTO;
import com.cainiao.waybill.bridge.biz.pickup.manager.LitePreChargeManager;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpOrderCommonUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.WeightUnitUtil;
import com.cainiao.waybill.bridge.common.waybill.pickup.service.LogisticQueryService;
import com.cainiao.waybill.bridge.common.waybill.pickup.service.WaybillDetailQueryService;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import com.google.common.collect.Lists;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_CREATE_PRE_QUERY.AddressInfo;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_CREATE_PRE_QUERY.UserInfo;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_CREATE_PRE_QUERY.WaybillPickUpCreatePreQueryRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PICK_UP_CREATE_PRE_QUERY.*;
import com.taobao.pac.client.sdk.receiveservice.WaybillPickUpCreatePreQueryService;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error.PRE_WEIGHT_INVALIDATE_ERROR;

/**
 * 寄件预查询：是否可下实时订单、预约订单的可选时间段。
 * 指定某个cpCode时，只返回该CP的信息。不指定时返回所有的CP。
 *
 * <AUTHOR>
 * @date 2022/1/6-下午3:35
 */
public class TmsWaybillCreatePreQueryServiceImpl implements WaybillPickUpCreatePreQueryService {

    @Resource
    private LitePreChargeManager litePreChargeManager;

    @Resource
    private LogisticQueryService logisticQueryService;

    @Resource
    private WaybillDetailQueryService waybillDetailQueryService;

    @Resource
    private PickUpPreQueryManager pickUpPreQueryManager;

    @Override
    public WaybillPickUpCreatePreQueryResponse invoke(ReceiveParams<WaybillPickUpCreatePreQueryRequest> receiveParams) {
        PickUpLogUtil.info("淘外下单预查询入参：" + JSON.toJSONString(receiveParams));

        WaybillPickUpCreatePreQueryRequest linkCreatePreQueryRequest = receiveParams.getRequestDataObject();
        return deal(receiveParams, linkCreatePreQueryRequest);

    }

    private WaybillPickUpCreatePreQueryResponse deal(ReceiveParams<WaybillPickUpCreatePreQueryRequest> receiveParams, WaybillPickUpCreatePreQueryRequest linkCreatePreQueryRequest) {

        WaybillPickUpCreatePreQueryResponse linkCreatePreQueryResponse = new WaybillPickUpCreatePreQueryResponse();
        try {
            // 基础参数校验
            checkBaseRequestParam(linkCreatePreQueryRequest);
            String orderChannel = linkCreatePreQueryRequest.getOrderChannels();
            String appKey = receiveParams.getFromAppkey();
            PickUpBaseInfoRequest pickUpBaseInfoRequest = new PickUpBaseInfoRequest();
            pickUpBaseInfoRequest.setFromAppKey(appKey);
            pickUpBaseInfoRequest.setCpCode(linkCreatePreQueryRequest.getCpCode());
            pickUpBaseInfoRequest.setResCode(PickUpCommonUtil
                .buildResCode(appKey, orderChannel));
            pickUpBaseInfoRequest.setOuterOrderCode(linkCreatePreQueryRequest.getOuterOrderCode());
            pickUpBaseInfoRequest.setSendMobile(linkCreatePreQueryRequest.getSenderInfo().getMobile());
            pickUpBaseInfoRequest.setSendPhone(linkCreatePreQueryRequest.getSenderInfo().getPhone());
            pickUpBaseInfoRequest.setSendName(linkCreatePreQueryRequest.getSenderInfo().getName());
            pickUpBaseInfoRequest.setSendAddress(convert(linkCreatePreQueryRequest.getSenderInfo().getAddressInfo()));
            pickUpBaseInfoRequest.setConsignName(linkCreatePreQueryRequest.getConsigneeInfo().getName());
            pickUpBaseInfoRequest.setConsignMobile(linkCreatePreQueryRequest.getConsigneeInfo().getMobile());
            pickUpBaseInfoRequest.setConsignAddress(convert(linkCreatePreQueryRequest.getConsigneeInfo()
                .getAddressInfo()));
            pickUpBaseInfoRequest.setBizType(PickUpDetailBizTypeEnum.NORMAL.getValue());
            // 客户服务可用性检测
            if(PickUpOrderCommonUtil.checkCustomerCurrForbid(receiveParams.getFromAppkey(), orderChannel)){
                linkCreatePreQueryResponse.setSuccess(false);
                linkCreatePreQueryResponse.setErrorCode(PickUpConstants.Error.ORDER_FORBID_ERROR.getErrorCode());
                linkCreatePreQueryResponse.setErrorMsg(PickUpConstants.Error.ORDER_FORBID_ERROR.getErrorMsg());
                return linkCreatePreQueryResponse;
            }
            // 标记拒接单CP
            List<String> forbidCpList = PickUpOrderCommonUtil.markRejectOrderCp(orderChannel);
            if(!CollectionUtils.isEmpty(forbidCpList)){
                pickUpBaseInfoRequest.setForbidCpCodes(forbidCpList);
            }

            PickUpCreatePreQueryResponse response = pickUpPreQueryManager.preQuery(pickUpBaseInfoRequest);

            PickUpLogUtil.info("createPreQueryResponse : " + JSON.toJSONString(response));

            PickUpCreatePreQueryRequest createPreQueryRequest = convertLinkRequest2Request(linkCreatePreQueryRequest);
            linkCreatePreQueryResponse.setCpTimeSelectList(
                convertResponse2LinkResponse(response, createPreQueryRequest, receiveParams.getFromAppkey()));
            linkCreatePreQueryResponse.setSuccess(true);
            return linkCreatePreQueryResponse;
        } catch (BridgeBaseException e) {
            PickUpLogUtil.errLog(JSON.toJSONString(receiveParams),
                PickUpConstants.Action.PICK_UP_CREATE_PRE_QUERY.name(), e.getErrorCode(), e.getErrorMessage());
            linkCreatePreQueryResponse.setSuccess(false);
            linkCreatePreQueryResponse.setErrorCode(e.getErrorCode());
            linkCreatePreQueryResponse.setErrorMsg(e.getErrorMessage());
            return linkCreatePreQueryResponse;
        } catch (BridgeBusinessException e) {
            PickUpLogUtil.errLog(JSON.toJSONString(receiveParams),
                PickUpConstants.Action.PICK_UP_CREATE_PRE_QUERY.name(), e.getErrorCode(), e.getErrorMessage());
            linkCreatePreQueryResponse.setSuccess(false);
            linkCreatePreQueryResponse.setErrorCode(e.getErrorCode());
            linkCreatePreQueryResponse.setErrorMsg(e.getErrorMessage());
            return linkCreatePreQueryResponse;
        } catch (Throwable e) {
            PickUpLogUtil.errLog(JSON.toJSONString(receiveParams),
                PickUpConstants.Action.PICK_UP_CREATE_PRE_QUERY.name(),
                PickUpConstants.Error.PICK_UP_CREATE_PRE_QUERY_SYSTEM_ERROR.getErrorCode(),
                PickUpConstants.Error.PICK_UP_CREATE_PRE_QUERY_SYSTEM_ERROR.getErrorMsg(), ExceptionUtils.getFullStackTrace(e));
            linkCreatePreQueryResponse.setSuccess(false);
            linkCreatePreQueryResponse.setErrorCode(
                PickUpConstants.Error.PICK_UP_CREATE_PRE_QUERY_SYSTEM_ERROR.getErrorCode());
            linkCreatePreQueryResponse.setErrorMsg(
                PickUpConstants.Error.PICK_UP_CREATE_PRE_QUERY_SYSTEM_ERROR.getErrorMsg());
            return linkCreatePreQueryResponse;
        }
    }

    /**
     * 校验入参
     * @param linkCreatePreQueryRequest
     * @throws BridgeBaseException
     */
    private void checkBaseRequestParam(WaybillPickUpCreatePreQueryRequest linkCreatePreQueryRequest)
        throws BridgeBaseException {
        // 个别渠道客户不校验
        if(BridgeSwitch.NO_CHECK_PRE_QUERY_PARAM_CHANNELS.contains(linkCreatePreQueryRequest.getOrderChannels())){
            return;
        }
        UserInfo senderInfo = linkCreatePreQueryRequest.getSenderInfo();
        UserInfo consigneeInfo = linkCreatePreQueryRequest.getConsigneeInfo();

        if(null == senderInfo
            || null == consigneeInfo
            || null == senderInfo.getAddressInfo()
            || null == consigneeInfo.getAddressInfo()
            || StringUtils.isAnyBlank(senderInfo.getName(), consigneeInfo.getName(), linkCreatePreQueryRequest.getOrderChannels())
            || StringUtils.isAllBlank(senderInfo.getMobile(), senderInfo.getPhone())
            || StringUtils.isAllBlank(consigneeInfo.getMobile(), consigneeInfo.getPhone())
            || StringUtils.isAnyBlank(senderInfo.getAddressInfo().getProvinceName(), senderInfo.getAddressInfo().getAddressDetail())
            || StringUtils.isAnyBlank(consigneeInfo.getAddressInfo().getProvinceName(), consigneeInfo.getAddressInfo().getAddressDetail())){

            throw new BridgeBaseException(PickUpConstants.Error.PICK_UP_CREATE_PRE_QUERY_PARAM_ERROR.getErrorCode(), PickUpConstants.Error.PICK_UP_CREATE_PRE_QUERY_PARAM_ERROR.getErrorMsg());
        }
    }

    private AddressDTO convert(AddressInfo addressInfo) {
        AddressDTO addressDTO = new AddressDTO();
        addressDTO.setProvinceName(addressInfo.getProvinceName());
        addressDTO.setCityName(addressInfo.getCityName());
        addressDTO.setAreaName(addressInfo.getAreaName());
        addressDTO.setTownName(addressInfo.getTownName());
        addressDTO.setAddressDetail(addressInfo.getAddressDetail());
        return addressDTO;
    }

    private List<TdTimeSelect> convertResponse2LinkResponse(PickUpCreatePreQueryResponse createPreQueryResponse,
        PickUpCreatePreQueryRequest createPreQueryRequest, String fromAppKey) throws Exception {
        List<TdTimeSelect> result = Lists.newArrayList();
        if (createPreQueryResponse == null || CollectionUtils.isEmpty(createPreQueryResponse.getCpTimeSelectList())) {
            return result;
        }
        List<PickUpCreatePreTdTimeSelectDTO> cpTimeSelectList = createPreQueryResponse.getCpTimeSelectList();
        for (PickUpCreatePreTdTimeSelectDTO tdTimeSelectDTO : cpTimeSelectList) {
            TdTimeSelect linkTdTimeSelect = new TdTimeSelect();
            BeanUtils.copyProperties(tdTimeSelectDTO, linkTdTimeSelect);

            RealTime linkRealTime = new RealTime();
            BeanUtils.copyProperties(tdTimeSelectDTO.getRealTime(), linkRealTime);
            linkTdTimeSelect.setRealTime(linkRealTime);

            List<TdAppointTimeDTO> linkTdAppointTimeList = appointTime2LinkAppointTime(
                tdTimeSelectDTO.getAppointTimes());
            linkTdTimeSelect.setAppointTimes(linkTdAppointTimeList);

            if (StringUtils.isNotBlank(createPreQueryRequest.getPreWeight())
                && BridgeSwitch.needPrePriceFromAppKey.contains(fromAppKey)) {
                //  调用结算预计费接口查询预估价格
                //  当前平台的报价配置完成，调用预计费接口获取预报价
                if (BridgeSwitch.financeBillPlatformUserId.get(fromAppKey) != null) {
                    //  如果传递了正向运单号，则根据正向韵达号查询重量，并以该重量作为预估重量
                    String forwardMailNoWeight = dealForwardMailNoWeight(createPreQueryRequest);
                    if (StringUtils.isNotBlank(forwardMailNoWeight)) {
                        createPreQueryRequest.setPreWeight(forwardMailNoWeight);
                    }
                    checkValidatePreWeight(createPreQueryRequest.getPreWeight());
                    LitePreChargeResultDTO preChargeDetail = litePreChargeManager.preChargeDetail(
                        buildLitePreChargeRequest(createPreQueryRequest, fromAppKey),
                        createPreQueryRequest.getPreWeight());
                    PickUpLogUtil.info("preChargeDetail : " + preChargeDetail);
                    linkTdTimeSelect.setPrePrice(preChargeDetail.getCnPrice());
                    //  鲸灵会对消费者透出预估重量、预估价格，与最终重量、最终价格的对比。所以这里需要把预估重量也进行透出(正向查出的情况下用正向，反之使用消费者填入的)
                    JSONObject extendInfo = JSONObject.parseObject(JSON.toJSONString(preChargeDetail));
                    extendInfo.put("weight", createPreQueryRequest.getPreWeight());
                    linkTdTimeSelect.setExtendInfo(extendInfo.toJSONString());
                }
            }

            result.add(linkTdTimeSelect);
        }
        return result;
    }

    /**
     * 校验重量有效
     * @param preWeight
     */
    private void checkValidatePreWeight(String preWeight) {
        if (StringUtils.isBlank(preWeight)) {
            throw new BridgeBusinessException(PRE_WEIGHT_INVALIDATE_ERROR.getErrorCode(), PRE_WEIGHT_INVALIDATE_ERROR.getErrorMsg());
        }
        try {
            BigDecimal weight = new BigDecimal(preWeight);
            // 重量必须大于0
            if(weight.compareTo(new BigDecimal(0)) <= 0){
                throw new BridgeBusinessException(PRE_WEIGHT_INVALIDATE_ERROR.getErrorCode(), PRE_WEIGHT_INVALIDATE_ERROR.getErrorMsg());
            }
        }catch (Exception e){
            throw new BridgeBusinessException(PRE_WEIGHT_INVALIDATE_ERROR.getErrorCode(), PRE_WEIGHT_INVALIDATE_ERROR.getErrorMsg());
        }

    }

    /**
     * 根据传入的正向运单号获取重量
     */
    private String dealForwardMailNoWeight(PickUpCreatePreQueryRequest createPreQueryRequest) {
        try {
            if (StringUtils.isBlank(createPreQueryRequest.getExtendInfo())) {
                return null;
            }
            JSONObject extendInfo = JSONObject.parseObject(createPreQueryRequest.getExtendInfo());
            if (StringUtils.isBlank(extendInfo.getString(PickUpCreateOrderParamConstants.PreQuery.FORWARD_MAIL_NO))) {
                return null;
            }
            String forwardMailNo = extendInfo.getString(PickUpCreateOrderParamConstants.PreQuery.FORWARD_MAIL_NO);
            String forwardCpCode = extendInfo.getString(PickUpCreateOrderParamConstants.PreQuery.FORWARD_CP_CODE);
            //  如果平台未传cpCode，这里查询电子面单获取，电子面单也为查询到，返回null，表示无法得到正向包裹重量
            if (StringUtils.isBlank(forwardCpCode) && StringUtils.isNotBlank(forwardMailNo)) {
                forwardCpCode = waybillDetailQueryService.queryCpCodeWithMailNo(forwardMailNo);
            }
            if (StringUtils.isBlank(forwardCpCode)) {
                return null;
            }

            String forwardWeight = logisticQueryService.getLcWeight(forwardMailNo, forwardCpCode);
            PickUpLogUtil.info(
                "TmsWaybillCreatePreQueryServiceImpl::dealForwardMailNoWeight forwardMailNo:" + forwardMailNo
                    + ", forwardCpCode:" + forwardCpCode + ", forwardWeight:" + forwardWeight);
            if (weightIsAvailable(forwardWeight)) {
                PickUpLogUtil.info(
                    "TmsWaybillCreatePreQueryServiceImpl::dealForwardMailNoWeight success:" + forwardWeight);
                return forwardWeight;
            }
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", PickUpConstants.Action.PICK_UP_CREATE_PRE_GET_WEIGHT_FAIL.name(),
                PickUpConstants.Error.PICK_UP_CREATE_PRE_GET_WEIGHT_ERROR.getErrorCode(),
                PickUpConstants.Error.PICK_UP_CREATE_PRE_GET_WEIGHT_ERROR.getErrorMsg(), e);
        }
        return null;
    }

    /**
     * 判断获取到的包裹重量是否去使用：值不能为0，且不能超过最大阈值
     *
     * @param weightStr :
     * @return :
     */
    private boolean weightIsAvailable(String weightStr) {
        int gIntValue = WeightUnitUtil.gIntValue(weightStr);
        String maxWeight = BridgeSwitch.categoryWeightConfig.get(PickUpCreateOrderParamConstants.Category.MAX_CATEGORY)
            .get(PickUpCreateOrderParamConstants.Category.MAX_WEIGHT);
        return gIntValue != 0 && gIntValue <= WeightUnitUtil.gIntValue(maxWeight);
    }

    /**
     * 构建调用结算预报价的请求参数
     */
    private LitePreChargeRequestDTO buildLitePreChargeRequest(PickUpCreatePreQueryRequest createPreQueryRequest,
        String fromAppKey) {

        LitePreChargeRequestDTO litePreChargeRequestDTO = new LitePreChargeRequestDTO();
        litePreChargeRequestDTO.setMailNo(
            createPreQueryRequest.getOrderChannels() + "_" + UUID.randomUUID().toString().replaceAll("-", ""));
        litePreChargeRequestDTO.setFromAppKey(fromAppKey);
        litePreChargeRequestDTO.setSendAddress(
            createPreQueryRequest.getSenderInfo().getAddressDTO().getAddressFormat());
        litePreChargeRequestDTO.setConsigneeAddress(
            createPreQueryRequest.getConsigneeInfo().getAddressDTO().getAddressFormat());
        String productCode = PickUpPlatformConfigDiamond.getCustomerProductCode(fromAppKey, createPreQueryRequest.getOrderChannels());
        if(StringUtils.isNotBlank(productCode)){
            litePreChargeRequestDTO.setProductCode(productCode);
        }
        PickUpLogUtil.info("create pre query LitePreChargeRequest : " + litePreChargeRequestDTO);
        return litePreChargeRequestDTO;
    }

    private List<TdAppointTimeDTO> appointTime2LinkAppointTime(List<PickUpCreatePreTdAppointTimeDTO> appointTimes) {
        if (CollectionUtils.isEmpty(appointTimes)) {
            return null;
        }
        List<TdAppointTimeDTO> result = new ArrayList<>(appointTimes.size());
        for (PickUpCreatePreTdAppointTimeDTO appointTimeDTO : appointTimes) {
            TdAppointTimeDTO linkAppointTimeDTO = new TdAppointTimeDTO();
            BeanUtils.copyProperties(appointTimeDTO, linkAppointTimeDTO);
            List<TdAppointTimeSlotDTO> linkTimeSlotDTOList = new ArrayList<>(appointTimeDTO.getTimeList().size());
            for (PickUpCreatePreTdAppointTimeSlotDTO timeSlotDTO : appointTimeDTO.getTimeList()) {
                TdAppointTimeSlotDTO linkTimeSlotDTO = new TdAppointTimeSlotDTO();
                BeanUtils.copyProperties(timeSlotDTO, linkTimeSlotDTO);
                linkTimeSlotDTOList.add(linkTimeSlotDTO);
            }
            linkAppointTimeDTO.setTimeList(linkTimeSlotDTOList);
            result.add(linkAppointTimeDTO);
        }
        return result;
    }

    /**
     * 将link请求转换为内部请求
     */
    private PickUpCreatePreQueryRequest convertLinkRequest2Request(
        WaybillPickUpCreatePreQueryRequest linkCreatePreQueryRequest) throws BridgeBaseException {
        checkRequestParam(linkCreatePreQueryRequest);
        PickUpCreatePreQueryRequest createPreQueryRequest = new PickUpCreatePreQueryRequest();
        BeanUtils.copyProperties(linkCreatePreQueryRequest, createPreQueryRequest);

        PickUpCreatePreQueryUserInfo sendInfo = new PickUpCreatePreQueryUserInfo();
        BeanUtils.copyProperties(linkCreatePreQueryRequest.getSenderInfo(), sendInfo);
        AddressDTO sendAddress = new AddressDTO();
        BeanUtils.copyProperties(linkCreatePreQueryRequest.getSenderInfo().getAddressInfo(), sendAddress);
        sendInfo.setAddressDTO(sendAddress);
        createPreQueryRequest.setSenderInfo(sendInfo);

        PickUpCreatePreQueryUserInfo consignInfo = new PickUpCreatePreQueryUserInfo();
        BeanUtils.copyProperties(linkCreatePreQueryRequest.getConsigneeInfo(), consignInfo);
        AddressDTO consignAddress = new AddressDTO();
        BeanUtils.copyProperties(linkCreatePreQueryRequest.getConsigneeInfo().getAddressInfo(), consignAddress);
        consignInfo.setAddressDTO(consignAddress);
        createPreQueryRequest.setConsigneeInfo(consignInfo);

        PickUpLogUtil.info("PickUpCreatePreQueryRequest : " + JSON.toJSONString(createPreQueryRequest));

        return createPreQueryRequest;
    }

    private void checkRequestParam(WaybillPickUpCreatePreQueryRequest linkCreatePreQueryRequest)
        throws BridgeBaseException {
        BridgeBaseException paramCheckFail = new BridgeBaseException(
            PickUpConstants.Error.PICK_UP_CREATE_PRE_QUERY_PARAM_ERROR.getErrorCode(),
            PickUpConstants.Error.PICK_UP_CREATE_PRE_QUERY_PARAM_ERROR.getErrorMsg());
        if (linkCreatePreQueryRequest == null || StringUtils.isBlank(linkCreatePreQueryRequest.getOrderChannels())
            || linkCreatePreQueryRequest.getSenderInfo() == null
            || linkCreatePreQueryRequest.getConsigneeInfo() == null) {
            throw paramCheckFail;
        }
        if (linkCreatePreQueryRequest.getSenderInfo().getAddressInfo() == null
            || linkCreatePreQueryRequest.getConsigneeInfo().getAddressInfo() == null) {
            throw paramCheckFail;
        }
    }
}
