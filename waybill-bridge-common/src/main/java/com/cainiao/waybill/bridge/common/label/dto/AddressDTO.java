package com.cainiao.waybill.bridge.common.label.dto;

import com.alibaba.common.lang.StringUtil;
import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * Created by guochao.tgc on 2016/6/3.
 */
public class AddressDTO extends BaseDTO {

    /**
     * 半角分号和全角分号，对于地址中的特殊字符进行替换
     */
    private static final char   REPLACE_COLON     = '\uFF1A';
    private static final char   ORIGIN_COLON      = ':';
    private static final char   REPLACE_SEMICOLON = '\uFF1B';
    private static final char   ORIGIN_SEMICOLON  = ';';
    private static final String NULL              = "null";

    private static final long   serialVersionUID  = 7641979868946220426L;

    /**
     * 省名称（一级地址）
     */
    private String              province;


    /**
     * 市名称（二级地址）
     */
    private String              city;

    /**
     * 区名称（三级地址）
     */
    private String              district;

    /**
     * 街道\镇名称（四级地址）
     */
    private String              town;

    /**
     * 详细地址
     */
    private String              detail;

    /**
     * 邮编
     */
    private String              zipCode;

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        if (StringUtil.isNotBlank(province)) {
            this.province = province.replace('\n', ' ').replace(ORIGIN_COLON, REPLACE_COLON).replace(ORIGIN_SEMICOLON, REPLACE_SEMICOLON).trim();
            if (StringUtil.equals(NULL, this.province)) {
                this.province = null;
            }
        }
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        if (StringUtil.isNotBlank(city)) {
            this.city = city.replace('\n', ' ').replace(ORIGIN_COLON, REPLACE_COLON).replace(ORIGIN_SEMICOLON, REPLACE_SEMICOLON).trim();
            if (StringUtil.equals(NULL, this.city)) {
                this.city = null;
            }
        }
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        if (StringUtil.isNotBlank(district)) {
            this.district = district.replace('\n', ' ').replace(ORIGIN_COLON, REPLACE_COLON).replace(ORIGIN_SEMICOLON, REPLACE_SEMICOLON).trim();
            if (StringUtil.equals(NULL, this.district)) {
                this.district = null;
            }
        }
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        if (StringUtil.isNotBlank(town)) {
            this.town = town.replace('\n', ' ').replace(ORIGIN_COLON, REPLACE_COLON).replace(ORIGIN_SEMICOLON, REPLACE_SEMICOLON).trim();
            if (StringUtil.equals(NULL, this.town)) {
                this.town = null;
            }
        }
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        if (StringUtil.isNotBlank(detail)) {
            this.detail = detail.replace('\n', ' ').replace(ORIGIN_COLON, REPLACE_COLON).replace(ORIGIN_SEMICOLON, REPLACE_SEMICOLON).trim();
            if (StringUtil.equals(NULL, this.detail)) {
                this.detail = null;
            }
        }
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((detail == null) ? 0 : detail.hashCode());
        result = prime * result + ((district == null) ? 0 : district.hashCode());
        result = prime * result + ((city == null) ? 0 : city.hashCode());
        result = prime * result + ((province == null) ? 0 : province.hashCode());
        result = prime * result + ((town == null) ? 0 : town.hashCode());
        return result;
    }


    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        AddressDTO other = (AddressDTO) obj;
        if (detail == null) {
            if (other.detail != null) {
                return false;
            }
        } else if (!detail.equals(other.detail)) {
            return false;
        }
        if (district == null) {
            if (other.district != null) {
                return false;
            }
        } else if (!district.equals(other.district)) {
            return false;
        }
        if (city == null) {
            if (other.city != null) {
                return false;
            }
        } else if (!city.equals(other.city)) {
            return false;
        }
        if (province == null) {
            if (other.province != null) {
                return false;
            }
        } else if (!province.equals(other.province)) {
            return false;
        }
        if (town == null) {
            if (other.town != null) {
                return false;
            }
        } else if (!town.equals(other.town)) {
            return false;
        }
        return true;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

}
