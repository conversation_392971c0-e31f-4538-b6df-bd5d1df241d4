package com.cainiao.waybill.bridge.enterprise.printer.service.impl;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseSwitchHolder;
import com.cainiao.waybill.bridge.enterprise.printer.request.EnterprisePrinterRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.PrintContractRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.UpdatePrinterLocationRequest;
import com.cainiao.waybill.bridge.enterprise.printer.service.EnterprisePrinterService;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterprisePrinterConvert;
import com.cainiao.waybill.bridge.enterprise.route.handler.ContractInformationBuild;
import com.cainiao.waybill.bridge.enterprise.route.handler.ContractInformationBuildService;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseOrderDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePrinterDO;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterprisePrinterParam;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WayBillEnterprisePrinterDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseOrderMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePrinterMapper;
import com.cainiao.waybill.galaxy.isv.api.common.IsvResult;
import com.cainiao.waybill.galaxy.isv.bean.PrintData;
import com.cainiao.waybill.galaxy.isv.bean.request.CloudPrinterBindRequest;
import com.cainiao.waybill.galaxy.isv.bean.request.CloudPrinterPrintRequest;
import com.cainiao.waybill.galaxy.isv.bean.request.CloudPrinterVerifyCodeRequest;
import com.cainiao.waybill.galaxy.isv.service.CloudPrinterService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25
 **/
@Service
public class EnterprisePrinterServiceImpl implements EnterprisePrinterService {


    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private CloudPrinterService cloudPrinterService;

    @Autowired
    private ContractInformationBuildService contractInformationMapBuildService;

    @Autowired
    private WaybillBridgeEnterprisePrinterMapper enterprisePrinterMapper;

    @Autowired
    private WaybillBridgeEnterpriseOrderMapper enterpriseOrderMapper;

    @Override
    public void getVerifyCode(String printerId) {
        CloudPrinterVerifyCodeRequest verifyCodeRequest = new CloudPrinterVerifyCodeRequest();
        verifyCodeRequest.setUid(printerId);

        try {
            IsvResult<Void> voidIsvResult = cloudPrinterService.verifyCode(verifyCodeRequest);
        } catch (Exception e) {
            LOGGER.error("getVerifyCode error, printerId:{}, e:{}", printerId, ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public String addPrinter(EnterprisePrinterRequest request) {
        boolean checkResult = EnterprisePrinterRequest.checkRequest(request);
        if (!checkResult) {
            LOGGER.error("addPrinter checkRequest error, request:{}", request);
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), EnterpriseErrorEnum.PARAM_ERROR.describe());
        }
        WaybillBridgeEnterprisePrinterDO enterprisePrinterDO = new WaybillBridgeEnterprisePrinterDO();
        enterprisePrinterDO.setGmtCreate(new Date());
        enterprisePrinterDO.setGmtModified(new Date());
        enterprisePrinterDO.setPrinterId(request.getPrinterId());
        enterprisePrinterDO.setPrinterName(request.getPrinterName());
        enterprisePrinterDO.setCorpId(request.getCorpId());
        enterprisePrinterDO.setLocationName(request.getLocationName());
        enterprisePrinterDO.setRemark(request.getRemark());
        try {
            WaybillBridgeEnterprisePrinterParam enterprisePrinterParam = new WaybillBridgeEnterprisePrinterParam();
            enterprisePrinterParam.createCriteria()
//                    .andCorpIdEqualTo(request.getCorpId()) //  这边printerId是唯一键盘，判断当前打印机有没有被绑定过，就不需要再corpId校验
                    .andPrinterIdEqualTo(request.getPrinterId());
            WaybillBridgeEnterprisePrinterDO waybillBridgeEnterprisePrinterDO = enterprisePrinterMapper.selectOneByParam(enterprisePrinterParam);
            if (waybillBridgeEnterprisePrinterDO != null && !StringUtils.equals(waybillBridgeEnterprisePrinterDO.getCorpId(), request.getCorpId())) {
                LOGGER.info("addPrinter error, printer already bind  printerId:{}, corpId:{}", request.getPrinterId(), request.getCorpId());
                throw new EnterpriseException(EnterpriseErrorEnum.PRINTER_ALREADY_BIND.code(), EnterpriseErrorEnum.PRINTER_ALREADY_BIND.describe());
            } else if (waybillBridgeEnterprisePrinterDO != null) {
                LOGGER.info("addPrinter error, printer already exist  printerId:{}, corpId:{}", request.getPrinterId(), request.getCorpId());
                throw new EnterpriseException(EnterpriseErrorEnum.PRINTER_ALREADY_EXIST.code(), EnterpriseErrorEnum.PRINTER_ALREADY_EXIST.describe());
            }

            String shareCode = getShareCode(request.getPrinterId(), request.getVerifyCode());
            enterprisePrinterDO.setShareCode(shareCode);
            enterprisePrinterMapper.insert(enterprisePrinterDO);

            return enterprisePrinterDO.getPrinterId();
        } catch (Exception e) {
            LOGGER.error("addPrinter error, request:{}, e:{}", request, ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(),e.getMessage());
        }
    }

    @Override
    public int updatePrinter(EnterprisePrinterRequest request) {
        WaybillBridgeEnterprisePrinterParam printerParam = new WaybillBridgeEnterprisePrinterParam();
        printerParam.createCriteria().andPrinterIdEqualTo(request.getPrinterId()).andCorpIdEqualTo(request.getCorpId());
        WaybillBridgeEnterprisePrinterDO waybillBridgeEnterprisePrinterDO = enterprisePrinterMapper.selectOneByParam(printerParam);
        if (waybillBridgeEnterprisePrinterDO == null) {
            LOGGER.info("updatePrinter error, printer not exist, printerId:{}", request.getPrinterId());
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), EnterpriseErrorEnum.PARAM_ERROR.describe());
        }
        waybillBridgeEnterprisePrinterDO.setGmtModified(new Date());
        waybillBridgeEnterprisePrinterDO.setPrinterName(request.getPrinterName());
        waybillBridgeEnterprisePrinterDO.setRemark(request.getRemark());

        try {
            return enterprisePrinterMapper.updateByPrimaryKeySelective(waybillBridgeEnterprisePrinterDO);
        } catch (Exception e) {
            LOGGER.error("updatePrinter error, request:{}, e:{}", request, ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public BridgePagingDTO<WayBillEnterprisePrinterDTO> pageList(EnterprisePrinterRequest request) {
        WaybillBridgeEnterprisePrinterParam enterprisePrinterParam = new WaybillBridgeEnterprisePrinterParam();
        enterprisePrinterParam.setPage(true);
        enterprisePrinterParam.setPagination(request.getCurrentPage(), request.getPageSize());
        enterprisePrinterParam.appendOrderByClause(WaybillBridgeEnterprisePrinterParam.OrderCondition.ID, WaybillBridgeEnterprisePrinterParam.SortType.DESC);
        WaybillBridgeEnterprisePrinterParam.Criteria criteria = enterprisePrinterParam.createCriteria();
        criteria.andCorpIdEqualTo(request.getCorpId());
        if (request.getBindResult() != null && !request.getBindResult()) {
            criteria.andLocationNameIsNull();
        }

        try {
            long totalCount = enterprisePrinterMapper.countByParam(enterprisePrinterParam);
            List<WaybillBridgeEnterprisePrinterDO> bridgeEnterprisePrinterDOList = enterprisePrinterMapper.selectByParam(enterprisePrinterParam);
            return BridgePagingDTO.build(WaybillBridgeEnterprisePrinterConvert.convertFromDOList(bridgeEnterprisePrinterDOList),
                    totalCount, request.getCurrentPage(), request.getPageSize());
        } catch (Exception e) {
            LOGGER.error("pageList error, corpId:{}, e:{}", request.getCorpId(), ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }


    @Override
    public int deleteById(Long id) {
        try {
            WaybillBridgeEnterprisePrinterDO waybillBridgeEnterprisePrinterDO = enterprisePrinterMapper.selectByPrimaryKey(id);
            if (StringUtils.isNotEmpty(waybillBridgeEnterprisePrinterDO.getLocationName())) {
                throw new EnterpriseException(EnterpriseErrorEnum.PRINTER_ALREADY_BIND_LOCATION.code(), EnterpriseErrorEnum.PRINTER_ALREADY_BIND_LOCATION.describe());
            }
            return enterprisePrinterMapper.deleteByPrimaryKey(id);
        } catch (Exception e) {
            LOGGER.error("deleteById error, id:{}, e:{}", id, ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public WayBillEnterprisePrinterDTO getOneByParam(EnterprisePrinterRequest request) {
        WaybillBridgeEnterprisePrinterParam enterprisePrinterParam = new WaybillBridgeEnterprisePrinterParam();
        enterprisePrinterParam.createCriteria()
                .andCorpIdEqualTo(request.getCorpId())
                .andPrinterIdEqualTo(request.getPrinterId());

        try {
            WaybillBridgeEnterprisePrinterDO waybillBridgeEnterprisePrinterDO = enterprisePrinterMapper.selectOneByParam(enterprisePrinterParam);
            return WaybillBridgeEnterprisePrinterConvert.convertFromDO(waybillBridgeEnterprisePrinterDO);
        } catch (Exception e) {
            LOGGER.error("getByParam error, request:{}, e:{}", request, ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public WayBillEnterprisePrinterDTO getPrinterById(Long id) {
        try {
            WaybillBridgeEnterprisePrinterDO waybillBridgeEnterprisePrinterDO = enterprisePrinterMapper.selectByPrimaryKey(id);
            return WaybillBridgeEnterprisePrinterConvert.convertFromDO(waybillBridgeEnterprisePrinterDO);
        } catch (Exception e) {
            LOGGER.error("getById error, id:{}, e:{}", id, ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    @Override
    public List<WayBillEnterprisePrinterDTO> getListByLocationName(String locationName, String corpId) {
        WaybillBridgeEnterprisePrinterParam enterprisePrinterParam = new WaybillBridgeEnterprisePrinterParam();
        enterprisePrinterParam.createCriteria()
                .andLocationNameEqualTo(locationName)
                .andCorpIdEqualTo(corpId);

        List<WaybillBridgeEnterprisePrinterDO> enterprisePrinterDOList = enterprisePrinterMapper.selectByParam(enterprisePrinterParam);
        return WaybillBridgeEnterprisePrinterConvert.convertFromDOList(enterprisePrinterDOList);
    }

    @Override
    public void batchUpdate(UpdatePrinterLocationRequest request) {
        if (CollectionUtils.isEmpty(request.getRequestList())) {
            return;
        }

        try {
            List<String> errorMessageList = Lists.newArrayList();
            for (EnterprisePrinterRequest printerRequest : request.getRequestList()) {
                try {
                    WaybillBridgeEnterprisePrinterParam printerParam = new WaybillBridgeEnterprisePrinterParam();
                    printerParam.createCriteria().andPrinterIdEqualTo(printerRequest.getPrinterId()).andCorpIdEqualTo(printerRequest.getCorpId());
                    WaybillBridgeEnterprisePrinterDO waybillBridgeEnterprisePrinterDO = enterprisePrinterMapper.selectOneByParam(printerParam);
                    if (waybillBridgeEnterprisePrinterDO == null) {
                        throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "打印机不存在");
                    }
                    // 判断是否为该场地已绑定的打印机
                    if (StringUtils.isNotEmpty(printerRequest.getLocationName())
                            && StringUtils.equals(waybillBridgeEnterprisePrinterDO.getLocationName(), printerRequest.getLocationName())) {
                        continue;
                    }

                    // 取消绑定locationName置空
                    if (StringUtils.isNotEmpty(printerRequest.getLocationName()) && StringUtils.isNotEmpty(waybillBridgeEnterprisePrinterDO.getLocationName())) {
                        throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(),
                                String.format("打印机[%s]已绑定场地", waybillBridgeEnterprisePrinterDO.getPrinterName()));
                    }

                    waybillBridgeEnterprisePrinterDO.setLocationName(printerRequest.getLocationName());
                    waybillBridgeEnterprisePrinterDO.setGmtModified(new Date());
                    enterprisePrinterMapper.updateByPrimaryKey(waybillBridgeEnterprisePrinterDO);
                } catch (EnterpriseException e) {
                    LOGGER.error("bindLocation error for printerId[{}]: {}", printerRequest.getPrinterId(), e.getMessage());
                    errorMessageList.add(e.getMessage());
                } catch (Exception e) {
                    // 捕获其他未知异常
                    LOGGER.error("bindLocation unexpected error for printerId[{}]: {}", printerRequest.getPrinterId(), ExceptionUtils.getStackTrace(e));
                    errorMessageList.add(String.format("打印机Id[%s]处理失败: %s", printerRequest.getPrinterId(), e.getMessage()));
                }
            }

            if (CollectionUtils.isNotEmpty(errorMessageList)) {
                String errorMessage = String.join("; ", errorMessageList);
                LOGGER.error("bindLocation 存在异常: {}", errorMessage);
                throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), errorMessage);
            }
        } catch (EnterpriseException e) {
            LOGGER.error("bindLocation error: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("bindLocation system error: {}", ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public void printExpressContractInformation(PrintContractRequest request) {
        if (StringUtils.isEmpty(request.getPrinterId())) {
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "打印机ID不能为空");
        }
        CloudPrinterPrintRequest cloudPrinterPrintRequest = new CloudPrinterPrintRequest();
        cloudPrinterPrintRequest.setUid(request.getPrinterId());

        try {
            WaybillBridgeEnterprisePrinterParam printerParam = new WaybillBridgeEnterprisePrinterParam();
            printerParam.createCriteria().andPrinterIdEqualTo(request.getPrinterId());
            WaybillBridgeEnterprisePrinterDO enterprisePrinterDO = enterprisePrinterMapper.selectOneByParam(printerParam);
            if (enterprisePrinterDO == null) {
                throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "打印机不存在");
            }

            cloudPrinterPrintRequest.setShareCode(enterprisePrinterDO.getShareCode());

            PrintData printData = new PrintData();
            if (CollectionUtils.isNotEmpty(request.getOuterOrderCodeList())) {
                request.getOuterOrderCodeList().forEach(outerOrderCode -> {
                    WaybillBridgeEnterpriseOrderDO enterpriseOrderDO = enterpriseOrderMapper.getByOuterOrderCode(outerOrderCode);
                    if (enterpriseOrderDO == null) {
                        LOGGER.error("printExpressContractInformation error, outerOrderCode: {} 对应面单信息不存在", outerOrderCode);
                        throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(),
                                String.format("单号[%s]对应面单不存在", outerOrderCode));
                    }
                    printData.setTemplateUrl(EnterpriseSwitchHolder.CONTRACT_TEMPLATE_URL.get(enterpriseOrderDO.getCpCode()));
                    ContractInformationBuild contractInformationBuild = contractInformationMapBuildService.route(enterpriseOrderDO.getCpCode());
                    if (contractInformationBuild == null) {
                        throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "面单信息构建异常,请检查承运编码");
                    }
                    String buildData = contractInformationBuild.build(enterpriseOrderDO);
                    LOGGER.info("printExpressContractInformation buildData: {}", buildData);
                    printData.setData(buildData);
                    cloudPrinterPrintRequest.setPrintData(printData);

                    IsvResult<Void> isvResult = cloudPrinterService.print(cloudPrinterPrintRequest);
                    LOGGER.info("printExpressContractInformation isvResult: {}", JSON.toJSONString(isvResult));
                    if (!isvResult.isSuccess()) {
                        throw new EnterpriseException(EnterpriseErrorEnum.PRINT_EXPRESS_ERROR.code(), isvResult.getDescribe());
                    }
                });
            }
        } catch (Exception e) {
            LOGGER.error("printExpressContractInformation error: {}", ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public int deleteByPrinterId(String printerId, String corpId) {
        try {
            WaybillBridgeEnterprisePrinterParam printerParam = new WaybillBridgeEnterprisePrinterParam();
            printerParam.createCriteria().andPrinterIdEqualTo(printerId).andCorpIdEqualTo(corpId);
            WaybillBridgeEnterprisePrinterDO waybillBridgeEnterprisePrinterDO = enterprisePrinterMapper.selectOneByParam(printerParam);
            if (waybillBridgeEnterprisePrinterDO == null) {
                throw new EnterpriseException(EnterpriseErrorEnum.PRINTER_NOT_EXIST.code(), EnterpriseErrorEnum.PRINTER_NOT_EXIST.describe());
            }
            if (StringUtils.isNotEmpty(waybillBridgeEnterprisePrinterDO.getLocationName())) {
                throw new EnterpriseException(EnterpriseErrorEnum.PRINTER_ALREADY_BIND_LOCATION.code(), EnterpriseErrorEnum.PRINTER_ALREADY_BIND_LOCATION.describe());
            }
            return enterprisePrinterMapper.deleteByPrimaryKey(waybillBridgeEnterprisePrinterDO.getId());
        } catch (Exception e) {
            LOGGER.error("deleteById error, printerId:{}, e:{}", printerId, ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @Override
    public WayBillEnterprisePrinterDTO getPrinterByPrinterId(String printerId, String corpId) {
        try {
            WaybillBridgeEnterprisePrinterParam printerParam = new WaybillBridgeEnterprisePrinterParam();
            printerParam.createCriteria().andPrinterIdEqualTo(printerId).andCorpIdEqualTo(corpId);
            WaybillBridgeEnterprisePrinterDO waybillBridgeEnterprisePrinterDO = enterprisePrinterMapper.selectOneByParam(printerParam);
            return WaybillBridgeEnterprisePrinterConvert.convertFromDO(waybillBridgeEnterprisePrinterDO);
        } catch (Exception e) {
            LOGGER.error("getById error, printerId:{}, e:{}", printerId, ExceptionUtils.getStackTrace(e));
            throw new EnterpriseException(EnterpriseErrorEnum.SYSTEM_ERROR.code(), EnterpriseErrorEnum.SYSTEM_ERROR.describe());
        }
    }

    private String getShareCode(String printerId, String verifyCode) {
        CloudPrinterBindRequest bindRequest = new CloudPrinterBindRequest();
        bindRequest.setUid(printerId);
        bindRequest.setVerifyCode(verifyCode);

        IsvResult<String> bindResult = cloudPrinterService.bind(bindRequest);
        if (!bindResult.isSuccess() || StringUtils.isBlank(bindResult.getData())) {
            LOGGER.error("bing printer error, shareCode is null  bindResult:{}", bindResult);
            throw new EnterpriseException(EnterpriseErrorEnum.PRINTER_BIND_ERROR.code(), EnterpriseErrorEnum.PRINTER_BIND_ERROR.describe());
        }
        return bindResult.getData();
    }
}
