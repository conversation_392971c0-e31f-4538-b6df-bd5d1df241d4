package com.cainiao.waybill.bridge.innersupport.ad.constant.commom;

import com.cainiao.waybill.bridge.innersupport.ad.exception.AdBusinessException;
import lombok.Data;

/**
 * 通用请求结果
 */
@Data
public class BaseResultDTO<T> extends BaseDTO {

    private static final long serialVersionUID = -799042361689184387L;
    /**
     * 请求是否成功
     */
    private boolean success = true;

    /**
     * 返回参数信息
     */
    private T data;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMsg;


    public boolean isSuccessAndHasData() {
        return isSuccess() && data != null;
    }

    public boolean isFailure() {
        return !isSuccess();
    }


    /**
     * 获取第一个错误信息
     *
     * @return
     */
    public String getErrorMsg() {
        return errorMsg;
    }

    public static <T> BaseResultDTO<T> newErrorResult(ErrorInfo errorInfo) {
        return newErrorResult(errorInfo.getErrorCode(), errorInfo.getErrorMsg());
    }

    /**
     * 创建一个失败结果
     *
     * @param errorCode
     * @param errorMessage
     * @param <T>
     * @return
     */
    public static <T> BaseResultDTO<T> newErrorResult(String errorCode, String errorMessage) {
        BaseResultDTO<T> errorResult = new BaseResultDTO<>();
        errorResult.setSuccess(false);
        errorResult.setErrorCode(errorCode);
        errorResult.setErrorMsg(errorMessage);
        return errorResult;
    }

    /**
     * 创建一个成功结果
     *
     * @param data
     * @param <T>
     * @return
     */
    public static <T> BaseResultDTO<T> newSuccessResult(T data) {
        BaseResultDTO<T> resultDTO = new BaseResultDTO<>();
        resultDTO.setSuccess(true);
        resultDTO.setData(data);
        return resultDTO;
    }
    /**
     * 创建一个成功结果
     * @param <T>
     * @return
     */
    public static <T> BaseResultDTO<T> newSuccessResult() {
        BaseResultDTO<T> resultDTO = new BaseResultDTO<>();
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    public T getData() {
        if (this.success) {
            return data;
        } else {
            throw new AdBusinessException(errorCode, errorMsg);
        }
    }
}
