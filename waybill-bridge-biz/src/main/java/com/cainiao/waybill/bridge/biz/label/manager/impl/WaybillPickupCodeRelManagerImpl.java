package com.cainiao.waybill.bridge.biz.label.manager.impl;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
import com.cainiao.waybill.bridge.biz.label.manager.PickupCodeManager;
import com.cainiao.waybill.bridge.biz.label.manager.WaybillPickupCodeRelationManager;
import com.cainiao.waybill.bridge.biz.label.util.WaybillPickupCodeConvert;
import com.cainiao.waybill.bridge.biz.utils.FeatureJsonUtils;
import com.cainiao.waybill.bridge.biz.wrapper.AddressUnifyWrapper;
import com.cainiao.waybill.bridge.biz.wrapper.CloudPrintTemplateWrapper;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants.BranchSellerAddRelCoopStatus;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants.BranchSellerAddressFeature;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants.WaybillPickupCodeFeature;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.LabelError;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.SystemError;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.exception.util.Exceptions;
import com.cainiao.waybill.bridge.common.label.dto.AddressDTO;
import com.cainiao.waybill.bridge.common.label.dto.request.UpdateWeightRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeApplyNewRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeQueryRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.WaybillPickupCodeUpdateRequest;
import com.cainiao.waybill.bridge.common.util.Page;
import com.cainiao.waybill.bridge.model.dao.BranchSellerAddressDAO;
import com.cainiao.waybill.bridge.model.dao.WaybillPickupCodeRelationDAO;
import com.cainiao.waybill.bridge.model.dao.bean.PickupCodeRelationQuery;
import com.cainiao.waybill.bridge.model.domain.BranchSellerAddressDO;
import com.cainiao.waybill.bridge.model.domain.WaybillPickupCodeRelationDO;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.common.dto.ClientInfoDTO;
import com.cainiao.waybill.common.result.BaseResultDTO;
import com.cainiao.waybill.common.seller.dto.WaybillApplyNewInfo;
import com.cainiao.waybill.common.seller.dto.WaybillApplyNewRequest;
import com.cainiao.waybill.common.seller.service.WaybillMainServiceForInternalSystem;
import com.taobao.biz.common.division.DivisionVO;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant;
import com.taobao.common.dao.persistence.exception.DAOException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-27
 */
@Service
public class WaybillPickupCodeRelManagerImpl implements WaybillPickupCodeRelationManager {

    private final static Logger LOGGER = LoggerFactory.getLogger(LogAppender.WAYBILL_PICKUP_CODE);

    @Resource
    private LogManager logManager;

    @Resource
    private WaybillMainServiceForInternalSystem waybillMainServiceForInternalSystem;

    @Resource
    private WaybillPickupCodeRelationDAO waybillPickupCodeRelationDAO;

    @Resource
    private PickupCodeManager pickupCodeManager;

    @Resource
    private CloudPrintTemplateWrapper cloudPrintTemplateWrapper;

    @Resource
    private BranchSellerAddressDAO branchSellerAddressDAO;

    @Resource
    private AddressUnifyWrapper addressUnifyWrapper;

    private final String WAYBILL_ACCOUNT_NOT_ENOUGH = "waybill account not enough";

    @Override
    public WaybillPickupCodeRelationDO createPickupCodeWaybill(WaybillPickupCodeApplyNewRequest request, Long courierId,
                                                               com.cainiao.waybill.bridge.common.dto.ClientInfoDTO
                                                                   clientInfoDTO)
        throws BridgeBaseException {
        try {

            // 查询绑定关系
            BranchSellerAddressDO relationDO = getBranchSellerAddress(request) ;

            // 1. 校验商家小件员绑定关系
            validateSellerCourierBind(request, relationDO, courierId);

            // 2. 获取揽件码
            String pickupCode = pickupCodeManager.generatePickupCode(request.getCpCode(), request.getBranchCode());

            // 3. 调用 waybill 创建面单(放在后面，防止面单号浪费)
            String waybillCode = applyNewWaybill(request, courierId, clientInfoDTO);

            // 4. 校验面单是否已经存在，保持接口幂等，存在就直接返回
            WaybillPickupCodeRelationDO srcRelationDO = waybillPickupCodeRelationDAO.queryByWaybillCode(courierId,
                request.getCpCode(), waybillCode);
            if (srcRelationDO != null) {
                logManager.logWarnMessage("WaybillPickupCodeRelManagerImpl.createPickupCodeWaybill", "面单号已经存在", LOGGER);
                return srcRelationDO;
            }

            // 5. 插入"面单-揽件码"关系表
            WaybillPickupCodeRelationDO newRelationDO = buildWaybillPickupCodeRelationDO(request, courierId, waybillCode, pickupCode,
                relationDO, clientInfoDTO.getOperator());
            waybillPickupCodeRelationDAO.insert(newRelationDO);

            // 6. 构造返回值
            return newRelationDO;
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(WaybillErrorConstant.SystemError.DAO_EXCEPTION.getErrorCode(),
                WaybillErrorConstant.SystemError.DAO_EXCEPTION.getErrorMsg(), e);
        }
    }

    private String applyNewWaybill(WaybillPickupCodeApplyNewRequest request, Long courierId,
                                   com.cainiao.waybill.bridge.common.dto.ClientInfoDTO clientInfoDTO)
        throws BridgeBaseException, DAOException {
        ClientInfoDTO ownClientInfo = new ClientInfoDTO(BridgeConstants.System.APP_NAME, clientInfoDTO.getOperator());

        // 1. 构造参数整体框架
        WaybillApplyNewRequest waybillApplyNewRequest = WaybillPickupCodeConvert.constructApplyNewRequest(request);

        // 2. 单独设置 templateURL . 使用"三联"模板
        String templateURL = cloudPrintTemplateWrapper.selectTemplateUrlByCp(request.getCpCode(), true);
        if (StringUtils.isBlank(templateURL)) {
            throw Exceptions.newBridgeValidatorException(LabelError.CAN_NOT_GET_TEMPLATE_URL_ERROR.getErrorCode(),
                "cpCode:{}", request.getCpCode());
        }
        waybillApplyNewRequest.getTradeOrderInfoDTOCols().get(0).setTemplateURL(templateURL);

        // 3. 调用电子面单生成面单服务
        BaseResultDTO<List<WaybillApplyNewInfo>> waybillResult = waybillMainServiceForInternalSystem.applyNew(courierId,
            waybillApplyNewRequest, ownClientInfo);
        if (waybillResult == null) {
            throw new BridgeBaseException("waybill applyNew error",
                "waybill applyNew result is null" + "[from waybill]");
        }
        if (waybillResult.isFailure()) {
            BaseResultDTO.ErrorInfo errorInfo = waybillResult.getOneErrorInfo();
            if (errorInfo == null || StringUtils.isBlank(errorInfo.getErrorCode())) {
                throw new BridgeBaseException("waybill applyNew error",
                    "waybill applyNew result.errorInfo is null" + "[from waybill]");
            }
            // 如果小件员余额不足，透传给外部调用方。（需要提示给用户）
            if (WAYBILL_ACCOUNT_NOT_ENOUGH.equals(errorInfo.getErrorCode())) {
                throw new BridgeValidationException(WAYBILL_ACCOUNT_NOT_ENOUGH,
                    WAYBILL_ACCOUNT_NOT_ENOUGH + "[from waybill]");
            }

            throw new BridgeBaseException(errorInfo.getErrorCode(), errorInfo.getErrorMessage() + "[from waybill]");
        }
        if (CollectionUtils.isEmpty(waybillResult.getModule())) {
            throw new BridgeBaseException("waybill applyNew waybillResult error",
                "waybill applyNew result.module is null" + "[from waybill]");
        }

        WaybillApplyNewInfo waybillApplyNewInfo = waybillResult.getModule().get(0);

        return waybillApplyNewInfo.getWaybillCode();
    }

    /**
     * 校验商家、小件员绑定关系
     *
     * @param request
     * @param courierId
     * @throws BridgeBaseException
     * @throws DAOException
     */
    private void validateSellerCourierBind(WaybillPickupCodeApplyNewRequest request, BranchSellerAddressDO relationDO, Long courierId)
        throws BridgeBaseException, DAOException {

        // 四种情况（没有绑定、小件员不一致、网点不一致、取消合作状态）下，抛出异常
        if (relationDO == null
            || !relationDO.getCourierId().equals(courierId)
            || !relationDO.getBranchCode().equals(request.getBranchCode())
            || BranchSellerAddRelCoopStatus.COOP != relationDO.getCooperationStatus()) {
            throw Exceptions.newBridgeValidatorException(LabelError.APPLY_NEW_NOT_BIND_ERROR);
        }
    }

    private WaybillPickupCodeRelationDO buildWaybillPickupCodeRelationDO(WaybillPickupCodeApplyNewRequest request, Long courierId,
                                                                         String waybillCode, String pickupCode, BranchSellerAddressDO branchSellerAddressDO, String modifer) {
        WaybillPickupCodeRelationDO relationDO = new WaybillPickupCodeRelationDO();
        Long sellerId = request.getSellerId();
        String sellerName = request.getSellerName();
        String shopName = request.getShopName();
        String cpCode = request.getCpCode();
        String branchCode = request.getBranchCode();
        byte printStatus = 0;

        relationDO.setCourierId(courierId);
        relationDO.setCpCode(cpCode);
        relationDO.setBranchCode(branchCode);
        relationDO.setModifier(modifer);
        relationDO.setPickupCode(pickupCode);
        relationDO.setWaybillCode(waybillCode);
        relationDO.setSellerId(sellerId);
        relationDO.setSellerName(sellerName);
        relationDO.setShopName(shopName);
        relationDO.setPrintStatus(printStatus);

        if(FeatureJsonUtils.keyIsExist(branchSellerAddressDO.getFeature(), BranchSellerAddressFeature.FEATURE_SETTLEMENT_TYPE_ACTIVE)) {
            String settlementTypeActive = FeatureJsonUtils.getStringValue(branchSellerAddressDO.getFeature(), BranchSellerAddressFeature.FEATURE_SETTLEMENT_TYPE_ACTIVE) ;
            String feature = FeatureJsonUtils.addStringValue(relationDO.getFeature(), WaybillPickupCodeFeature.FEATURE_SETTLERMENT, settlementTypeActive) ;

            relationDO.setFeature(feature);
        }

        return relationDO;
    }

    private BranchSellerAddressDO getBranchSellerAddress(WaybillPickupCodeApplyNewRequest request)
        throws DAOException, BridgeValidationException {
        // 校验入参的地址是否存在
        AddressDTO sendAddress = request.getMailOrderInfoDTO().getSender().getAddress();
        DivisionVO divisionVO = addressUnifyWrapper.getMatchMinAreaDivision(sendAddress);
        if (divisionVO == null) {
            throw Exceptions.newBridgeValidatorException("parameter address error",
                "parameter address error, address:{}", sendAddress);
        }

        return branchSellerAddressDAO.queryByCpSellerAddress(request.getCpCode(),
            request.getSellerId(), divisionVO.getDivisionId(), sendAddress.getDetail());
    }

    @Override
    public Page<WaybillPickupCodeRelationDO> queryRelations(WaybillPickupCodeQueryRequest request, Long courierId,
                                                            com.cainiao.waybill.bridge.common.dto.ClientInfoDTO
                                                                clientInfoDTO)
        throws BridgeBaseException {
        try {

            Date createDateStart = null;
            if (StringUtils.isNotBlank(request.getCreateDateStart())) {
                createDateStart = DateUtils.strToDateStart(request.getCreateDateStart(), "yyyy-MM-dd");
            }
            Date createDateEnd = null;
            if (StringUtils.isNotBlank(request.getCreateDateEnd())) {
                createDateEnd = DateUtils.strToDateEnd(request.getCreateDateEnd(), "yyyy-MM-dd");
            }
            Byte printStatus = request.getPrintStatus();

            String pickupCode = request.getPickupCode();
            String shopName = request.getShopName();

            String cpCode = request.getCpCode();
            String branchCode = request.getBranchCode();

            PickupCodeRelationQuery query = new PickupCodeRelationQuery();
            query.setCourierId(courierId).setCreateDateStart(createDateStart).setCreateDateEnd(createDateEnd)
                .setPrintStatus(printStatus)
                .setPickupCode(pickupCode).setShopName(shopName).setCpCode(cpCode).setBranchCode(branchCode);

            return waybillPickupCodeRelationDAO.queryPickupWaybillWithPage(query, request.getCurrentPageIndex(),
                request.getSize());

        } catch (ParseException e) {
            throw Exceptions.newBridgeValidatorException(LabelError.DATE_FORMAT_ERROR);
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        }
    }

    @Override
    public void updatePrintStatus(WaybillPickupCodeUpdateRequest request, Long courierId,
                                  com.cainiao.waybill.bridge.common.dto.ClientInfoDTO clientInfoDTO)
        throws BridgeBaseException {
        try {
            String cpCode = request.getCpCode();
            String waybillCode = request.getWaybillCode();

            WaybillPickupCodeRelationDO relationDO = waybillPickupCodeRelationDAO.queryByWaybillCode(courierId, cpCode,
                waybillCode);
            if (relationDO == null) {
                throw Exceptions.newBridgeValidatorException(
                    WaybillErrorConstant.BaseError.PARAMETER_ILLEGAL.getErrorCode(),
                    "根据 courierId, waybillCode 找不到对应的记录");
            }
            // 保持幂等
            if (BridgeConstants.PickupCodePrintStatus.HAS_PRINTED == relationDO.getPrintStatus()) {
                logManager.logWarnMessage("updatePrintStatus", "当前状态已经是已打印", LOGGER);
                return;
            }

            // 更新为已打印
            waybillPickupCodeRelationDAO.updatePrintStatusById(relationDO.getId(), relationDO.getCourierId(), BridgeConstants.PickupCodePrintStatus.HAS_PRINTED);
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        }
    }

    @Override
    public void updateWeight(UpdateWeightRequest request, Long courierId)
        throws BridgeBaseException {

        String cpCode = request.getCpCode();
        String waybillCode = request.getWaybillCode();
        try {
            WaybillPickupCodeRelationDO relationDO = waybillPickupCodeRelationDAO.queryByWaybillCode(courierId, cpCode,
                waybillCode);
            if (relationDO == null) {
                throw Exceptions.newBridgeValidatorException(
                    WaybillErrorConstant.BaseError.PARAMETER_ILLEGAL.getErrorCode(),
                    "根据 courierId, waybillCode 找不到对应的记录");
            }

            boolean isEqual = request.getWeight() == null ? relationDO.getWeight() == null
                : request.getWeight().equals(relationDO.getWeight());
            // 保持幂等
            if (isEqual) {
                logManager.logWarnMessage("updateWeight", "更新重量和当前重量一致，不需要更新", LOGGER);
                return;
            }

            // 更新为已打印
            waybillPickupCodeRelationDAO.updateWeightById(relationDO.getId(), relationDO.getCourierId(), request.getWeight());
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        }
    }

    @Override
    public WaybillPickupCodeRelationDO queryByWaybillCode(Long courierId, String cpCode, String waybillCode)
        throws BridgeBaseException {
        try {
            return waybillPickupCodeRelationDAO.queryByWaybillCode(courierId, cpCode, waybillCode);
        } catch (DAOException e) {
            throw Exceptions.newBridgeBaseException(SystemError.DAO_EXCEPTION, e);
        }
    }
}
