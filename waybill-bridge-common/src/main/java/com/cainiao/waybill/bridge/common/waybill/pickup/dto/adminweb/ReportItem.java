package com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb;

import lombok.Data;

/**
 * 各个统计维度的指标字段
 *
 * <AUTHOR>
 * @date 2021/11/16-下午8:34
 */
@Data
public class ReportItem {
    /**
     * 总量
     */
    private Integer allNum;

    /**
     * 及时揽收
     */
    private Integer gotNum;
    /**
     * 未及时揽收
     */
    private Integer notGotNum;
    /**
     * 揽收率
     */
    private String gotRate;
    /**
     * 揽收率数值
     */
    private double gotRateValue;

    /**
     * 取消量
     */
    private Integer cancelNum;
    /**
     * 取消率
     */
    private String cancelRate;
    /**
     * 取消率数值
     */
    private double cancelRateValue;

    /**
     * 及时接单量
     */
    private Integer acceptNum;
    /**
     * 未及时接单量
     */
    private Integer notAcceptNum;
    /**
     * 接单率
     */
    private String acceptRate;
    /**
     * 接单率数值
     */
    private double acceptRateValue;

    /**
     * 小号量
     */
    private Integer virtualNum;
    /**
     * 及时电联量
     */
    private Integer virtualCallNum;
    /**
     * 未及时电联量
     */
    private Integer notVirtualCallNum;
    /**
     * 电联率
     */
    private String virtualCallRate;
    /**
     * 电联率数值
     */
    private double virtualCallRateValue;
}
