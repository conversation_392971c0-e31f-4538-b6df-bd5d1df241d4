package com.cainiao.waybill.bridge.common.label.service;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.label.dto.request.TpnMsgRequest;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.label.dto.TpnMsgDTO;

/**
 * 千牛消息统一接入 biz
 * <AUTHOR>
 * @since 2017/04/20
 */
public interface TpnMsgService {
    /**
     * 发送千牛消息
     * @param tpnMsgDTO
     * @param clientInfoDTO
     * @return String 为发送成功的messageId
     */
    @Deprecated
    BaseResultDTO<String> sendTpnMsg(TpnMsgDTO tpnMsgDTO, ClientInfoDTO clientInfoDTO);

    /**
     * 发送千牛消息
     * @param tpnMsgRequest
     * @param clientInfoDTO
     * @return String 为发送成功的messageId
     */
    BaseResultDTO<Void> sendTpnMsg(TpnMsgRequest tpnMsgRequest, ClientInfoDTO clientInfoDTO);
}
