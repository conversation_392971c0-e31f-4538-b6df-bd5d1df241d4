package com.cainiao.waybill.bridge.web.pickup.router;

import com.cainiao.waybill.bridge.biz.router.request.BridgeRouteStrategyCreateRequest;
import com.cainiao.waybill.bridge.biz.router.request.BridgeRouteStrategyModifyRequest;
import com.cainiao.waybill.bridge.biz.router.request.BridgeRouteStrategyQueryRequest;
import com.cainiao.waybill.bridge.biz.router.request.BridgeRouteStrategyStatusModifyRequest;
import com.cainiao.waybill.bridge.biz.router.response.BridgeRouteStrategyQueryResponse;
import com.cainiao.waybill.bridge.biz.router.service.BridgeOrderCpRouteStrategyService;
import com.cainiao.waybill.bridge.biz.ticket.dto.PagingResponse;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.web.charity.controller.BaseController;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.pickup.constant.TicketResourceEnum;
import com.cainiao.waybill.bridge.web.pickup.ticket.role.PickUpResourceAuthority;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 策略管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/pickup/order/route/strategy")
public class BridgeOrderCpRouteStrategyController extends BaseController {
    @Autowired
    private BridgeOrderCpRouteStrategyService strategyService;

    /**
     * 根据参数查找
     */
    @PickUpResourceAuthority(TicketResourceEnum.ORDER_ROUTER)
    @ResponseBody
    @PostMapping("/list")
    public BaseResult<PagingResponse<BridgeRouteStrategyQueryResponse>> find(
        @RequestBody BridgeRouteStrategyQueryRequest request) {
        BridgeValidator.validate(request);
        PagingResponse<BridgeRouteStrategyQueryResponse> result = strategyService.queryList(request);
        return BaseResult.success(result);
    }

    /**
     * 列表查询
     */
    @PickUpResourceAuthority(TicketResourceEnum.ORDER_ROUTER)
    @ResponseBody
    @PostMapping("/create")
    public BaseResult<Void> create(@RequestBody BridgeRouteStrategyCreateRequest request) {
        BridgeValidator.validate(request);
        strategyService.createStrategy(request);
        return BaseResult.success();
    }

    /**
     * 列表查询
     */
    @PickUpResourceAuthority(TicketResourceEnum.ORDER_ROUTER)
    @ResponseBody
    @PostMapping("/modify")
    public BaseResult<Void> create(@RequestBody BridgeRouteStrategyModifyRequest request) {
        BridgeValidator.validate(request);
        strategyService.modifyStrategy(request);
        return BaseResult.success();
    }

    /**
     * 列表查询
     */
    @PickUpResourceAuthority(TicketResourceEnum.ORDER_ROUTER)
    @ResponseBody
    @PostMapping("/status/modify")
    public BaseResult<Void> create(@RequestBody BridgeRouteStrategyStatusModifyRequest request) {
        BridgeValidator.validate(request);
        strategyService.modifyStrategyStatus(request);
        return BaseResult.success();
    }
}
