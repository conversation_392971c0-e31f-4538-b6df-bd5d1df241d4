package com.cainiao.waybill.bridge.common.label.dto.request;


import com.cainiao.waybill.bridge.common.util.Page;

/**
 * 网点管理模块。查询网点下合作的商家地址列表，查询条件 request 对象。
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/4/25.
 */
public class BranchSellerAddRelQueryRequest extends Page {
    private static final long serialVersionUID = -8682795436504270210L;

    /**
     * cp 编码
     */
    private String cpCode;

    /**
     * 网点编码
     */
    private String branchCode;

    /**
     * 合作日期查询起始值
     * 格式'yyyy-MM-dd'
     * 搜索从 00:00:00 开始
     */
    private String cooperationDateStart;

    /**
     * 合作日期查询结束值
     * 格式'yyyy-MM-dd'
     * 搜索截止到 23:59:59
     */
    private String cooperationDateEnd;

    /**
     * 商家名称，支持最左匹配
     */
    private String sellerName;

    /**
     * 店铺名称，支持最左匹配
     */
    private String shopName;

    /**
     * 小件员id
     */
    private Long courierId;

    /**
     * 合作状态
     * 0:取消合作
     * 1:合作中
     */
    private Byte cooperationStatus;

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getCooperationDateStart() {
        return cooperationDateStart;
    }

    public void setCooperationDateStart(String cooperationDateStart) {
        this.cooperationDateStart = cooperationDateStart;
    }

    public String getCooperationDateEnd() {
        return cooperationDateEnd;
    }

    public void setCooperationDateEnd(String cooperationDateEnd) {
        this.cooperationDateEnd = cooperationDateEnd;
    }

    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public String getShopName() {
        return shopName;
    }

    public BranchSellerAddRelQueryRequest setShopName(String shopName) {
        this.shopName = shopName;
        return this;
    }

    public Long getCourierId() {
        return courierId;
    }

    public void setCourierId(Long courierId) {
        this.courierId = courierId;
    }

    public Byte getCooperationStatus() {
        return cooperationStatus;
    }

    public void setCooperationStatus(Byte cooperationStatus) {
        this.cooperationStatus = cooperationStatus;
    }

}
