package com.cainiao.waybill.bridge.enterprise.common;

import com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePlatformEnum;
import lombok.Data;

import java.io.Serializable;
@Data
public class BaseRequest implements Serializable {
    private static final long serialVersionUID = 549609464450358758L;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 企业唯一id
     */
    private String corpId;

    /**
     * 平台
     * @see EnterprisePlatformEnum
     */
    private String platform;
}
