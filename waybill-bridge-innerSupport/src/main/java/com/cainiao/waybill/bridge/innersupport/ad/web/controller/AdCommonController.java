package com.cainiao.waybill.bridge.innersupport.ad.web.controller;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.PolicyConditions;
import com.cainiao.waybill.bridge.innersupport.ad.biz.oss.AdOssClientFactory;
import com.cainiao.waybill.bridge.innersupport.ad.biz.oss.AdOssUtils;
import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.AdErrorConstants;
import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.BaseResultDTO;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @Description 公共请求处理
 */
@RestController
@RequestMapping(value = "/adworkorder")
public class AdCommonController {

    public static List<String> OSS_FILE_TYPES = Lists.newArrayList("image/jpg", "image/jpeg", "image/png", "image/bmp", "image/gif",
            "multipart/related","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/zip", "application/x-rar-compressed");

    @ResponseBody
    @RequestMapping(value = "/get-oss-upload-params", method = RequestMethod.GET)
    public BaseResultDTO<Map<String, Object>> generatePkgOssToken(@RequestParam String fileName){
        if (StringUtils.isBlank(fileName)) {
            return BaseResultDTO.newErrorResult(AdErrorConstants.OSS_FILE_NAME_IS_NULL);
        }
        try {
            String[] fileNameArray = fileName.split("\\.");
            String newFileName = fileNameArray[0] + "-" + System.currentTimeMillis() + "." + fileNameArray[1];
            OSSClient ossClient = AdOssClientFactory.getOssClient();
            int expirationMinutes = 15;
            Date expiration = DateUtils.addMinutes(new Date(), expirationMinutes);
            String host = "//" + AdOssClientFactory.AD_WORK_ORDER_BUCKET_NAME + "." + AdOssClientFactory
                    .getENDPOINT();

            PolicyConditions conditions = new PolicyConditions();
            conditions.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
            conditions.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, AdOssClientFactory.OSS_IMG_FILE_DIR);
            //控制文件类型
            conditions.addConditionItem(MatchMode.In, PolicyConditions.COND_CONTENT_TYPE, OSS_FILE_TYPES.toArray(new String[0]));
            String postPolicy = ossClient.generatePostPolicy(expiration, conditions);
            byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
            String encodedPolicy = BinaryUtil.toBase64String(binaryData);
            String postSignature = ossClient.calculatePostSignature(postPolicy);

            Map<String, Object> ossPolicyMap = new HashMap<>();
            StringBuilder stringBuilder = new StringBuilder();
            ossPolicyMap.put("success", true);
            ossPolicyMap.put("errMsg", null);
            ossPolicyMap.put("host", host);
            ossPolicyMap.put("accessId", AdOssClientFactory.getAccessKeyId());
            ossPolicyMap.put("policy", encodedPolicy);
            ossPolicyMap.put("signature", postSignature);
            ossPolicyMap.put("dir", AdOssClientFactory.OSS_IMG_FILE_DIR);
            ossPolicyMap.put("key", AdOssClientFactory.OSS_IMG_FILE_DIR + "/" + newFileName);
            ossPolicyMap.put("expire", expiration.getTime() / 1000);
            String fileUrl = host + "/" + AdOssClientFactory.OSS_IMG_FILE_DIR + "/" + newFileName;
            stringBuilder.append("https:").append(fileUrl);
            ossPolicyMap.put("preSignedUrl", AdOssUtils.generateOssPresignedUrl(stringBuilder.toString(), expiration));
            return BaseResultDTO.newSuccessResult(ossPolicyMap);

        } catch (Exception e) {
            return  BaseResultDTO.newErrorResult("generatePkgOssToken error", e.getMessage());
        }
    }
}

