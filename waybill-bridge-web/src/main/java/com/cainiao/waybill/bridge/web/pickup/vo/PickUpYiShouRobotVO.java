package com.cainiao.waybill.bridge.web.pickup.vo;

import com.cainiao.waybill.bridge.biz.utils.excel.ExcelHeader;
import lombok.Data;

import java.io.Serializable;

/**
 * 一手指标数据
 * <AUTHOR>
 * @date 2025/3/19 16:24
 **/
@Data
public class PickUpYiShouRobotVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日期
     */
    @ExcelHeader("日期")
    private String timeRange;

    /**
     * 订单量
     */
    @ExcelHeader("订单量")
    private Integer orderNum;

    /**
     * 10m接单率
     */
    @ExcelHeader("接单率")
    private String accept10mRate;

    /**
     * 取消量
     */
    @ExcelHeader("取消量")
    private Integer cancelNum;

    /**
     * 取消率
     */
    @ExcelHeader("取消率")
    private String cancelRate;

    /**
     * 应揽已揽收率
     */
    @ExcelHeader("应揽已揽收率")
    private String gotRate;

    /**
     * 应揽订单及时揽收率
     */
    @ExcelHeader("应揽及时揽收率")
    private String inTimeGotRate;
}
