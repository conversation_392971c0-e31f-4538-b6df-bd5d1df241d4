package com.cainiao.waybill.bridge.biz.link.pickup.service.impl.check;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.biz.charity.constant.enums.CommonConfigScope;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CommonConfigStatusEnum;
import com.cainiao.waybill.bridge.biz.charity.constant.enums.CommonConfigTypeEnum;
import com.cainiao.waybill.bridge.biz.middleware.BridgeRouterSwitch;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.middleware.EagleEyeCallbackTask;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpDetailBizTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpBaseInfoRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpCpConfigInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.RouteReachableResult;
import com.cainiao.waybill.bridge.biz.pickup.routing.dto.RouteBaseRequest;
import com.cainiao.waybill.bridge.biz.pickup.routing.dto.RouteBaseResult;
import com.cainiao.waybill.bridge.biz.pickup.routing.manager.PickUpReachableManager;
import com.cainiao.waybill.bridge.biz.pickup.routing.manager.RoutingManager;
import com.cainiao.waybill.bridge.biz.router.DTO.ContractRouteInfo;
import com.cainiao.waybill.bridge.biz.utils.insurance.MD5Utils;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpPlatConfigHelp;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpReachableThreadPool;
import com.cainiao.waybill.bridge.biz.wrapper.TairManagerWrapper;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.model.domain.WaybillCommonConfigDO;
import com.cainiao.waybill.bridge.model.domain.WaybillCommonConfigParam;
import com.cainiao.waybill.bridge.model.mapper.WaybillCommonConfigMapper;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import com.taobao.eagleeye.EagleEye;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * 校验cp是否可用
 *
 * <AUTHOR> zouping.fzp
 * @Classname PickUpCheckManager
 * @Description
 * @Date 2023/8/11 10:27 上午
 * @Version 1.0
 */
@Component
public class PickUpCheckAvailableManager {

    @Resource
    private PickUpReachableManager pickUpReachableManager;

    @Resource
    private RoutingManager routingManager;

    @Resource
    private WaybillCommonConfigMapper waybillCommonConfigMapper;

    @Resource
    private TairManagerWrapper tairManagerWrapper;

    /**
     * 检查是否可用
     *
     * @param baseInfoRequest 基本信息请求
     * @param isPreQuery 是否预查询
     * @return 可用性检查结果
     */
    public PickUpCheckResult checkAvailable(PickUpBaseInfoRequest baseInfoRequest, boolean isPreQuery) {

        PickUpCheckResult pickUpCheckResult = new PickUpCheckResult();
        pickUpCheckResult.setAvailable(true);

        List<PickUpCpConfigInfo> configInfoList = null;

        String orderChannel = PickUpCommonUtil.parseOrderChannels(baseInfoRequest.getResCode());

        // 指定cp
        if (StringUtils.isNotBlank(baseInfoRequest.getCpCode())) {
            List<PickUpCpConfigInfo> list = PickUpPlatConfigHelp.getAssignAvailableCpListByAppKey(
                baseInfoRequest.getFromAppKey(), orderChannel);

            configInfoList = ListUtil.stream(list)
                .filter(x -> StringUtils.equals(baseInfoRequest.getCpCode(), x.getCpCode()))
                .collect(Collectors.toList());

            // 过滤被禁用的cp
            configInfoList = filterForbidCp(configInfoList, baseInfoRequest.getForbidCpCodes());

            if (CollectionUtils.isEmpty(configInfoList)) {
                pickUpCheckResult.setAvailable(false);
                pickUpCheckResult.setUnavailableReason("渠道不支持该服务商");
                return pickUpCheckResult;
            }

            // 可分配cp
        } else {
            // 获取到配置的可用cp,默认查询不可指定CP可自动分配路由的cp
            configInfoList = PickUpPlatConfigHelp.getRoutingAvailableCpListByAppKey(
                baseInfoRequest.getFromAppKey(), orderChannel);

            // 过滤被禁用的cp
            configInfoList = filterForbidCp(configInfoList, baseInfoRequest.getForbidCpCodes());

            if (CollectionUtils.isEmpty(configInfoList)) {
                pickUpCheckResult.setAvailable(false);
                pickUpCheckResult.setUnavailableReason("无可用服务商");
                return pickUpCheckResult;
            }
        }

        CopyOnWriteArrayList<PickUpCpConfigInfo> availableCpList = new CopyOnWriteArrayList<>();
        long startTime = System.currentTimeMillis();
        // 多线程校验可达性
        ThreadPoolExecutor threadPoolExecutor = PickUpReachableThreadPool.getInstance();
        final CountDownLatch countDownLatch = new CountDownLatch(configInfoList.size());

        final Object rpcContext = EagleEye.currentRpcContext();
        // 可达性校验
        ConcurrentHashMap<PickUpCpConfigInfo, RouteReachableResult> reachableResultMap = new ConcurrentHashMap<>(
            pickUpCheckResult.getRouteReachableResult());
        for (PickUpCpConfigInfo pickUpCpConfigInfo : configInfoList) {

            threadPoolExecutor.submit(
                new EagleEyeCallbackTask<String>(rpcContext) {
                    @Override
                    public String callback() {
                        try{
                            boolean checkReachAble = isPreQuery ? pickUpCpConfigInfo.getPickUpCpPreQueryInfo().getCheckReachAble()
                                : pickUpCpConfigInfo.getPickUpCpCreateOrderInfo().getCheckReachAble();

                            // 判断是否需要校验可达性
                            if (Boolean.FALSE.equals(checkReachAble)) {
                                availableCpList.add(pickUpCpConfigInfo);
                            }else {
                                // 判断是否忽略校验结果
                                boolean ignoreReachAbleResult = isPreQuery ? pickUpCpConfigInfo.getPickUpCpPreQueryInfo()
                                    .getIgnoreReachAbleResult()
                                    : pickUpCpConfigInfo.getPickUpCpCreateOrderInfo().getIgnoreReachAbleResult();

                                // 校验可达性
                                RouteReachableResult routeReachableResult = checkReach(baseInfoRequest, pickUpCpConfigInfo);
                                if(!CollectionUtils.isEmpty(routeReachableResult.getTdTimeSelectDTOList())){
                                    pickUpCpConfigInfo.setSupportCpList(routeReachableResult.getTdTimeSelectDTOList().get(0).getSupportCpList());
                                }
                                reachableResultMap.put(pickUpCpConfigInfo, routeReachableResult);

                                // 是否命中黑名单配置
                                // 暂时开关不打开也执行黑名单校验 记录命中率但不实际执行拦截
                                boolean matchBlackConfig = checkMatchBlackConfig(baseInfoRequest, pickUpCpConfigInfo.getCpCode());
                                boolean checkMatchBlackConfig = false;
                                if(BridgeRouterSwitch.OPEN_BLACK_CONFIG_INTERCEPT){
                                    checkMatchBlackConfig = matchBlackConfig;
                                }

                                // 如果可达，或可忽略可达结果，则该cp可用
                                boolean reachableFlag = routeReachableResult.isReachable() && !checkMatchBlackConfig;
                                if (reachableFlag || Boolean.TRUE.equals(ignoreReachAbleResult)) {
                                    availableCpList.add(pickUpCpConfigInfo);
                                }
                            }
                        }catch (Exception e){
                            PickUpLogUtil.errLog(baseInfoRequest.getOuterOrderCode(), "THREAD_REACHABLE_ERROR", "THREAD_REACHABLE_ERROR", ExceptionUtils.getFullStackTrace(e));
                        }finally {
                            countDownLatch.countDown();
                        }
                        return "done";
                    }
                });


        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            PickUpLogUtil.errLog(baseInfoRequest.getOuterOrderCode(), "MULTI_THREAD_REACHABLE_ERROR", "MULTI_THREAD_REACHABLE_ERROR", "主进程计时器阻塞异常");
        }
        pickUpCheckResult.setRouteReachableResult(reachableResultMap);
        String avalibleCpStr = "";
        if (!CollectionUtils.isEmpty(availableCpList)){
            avalibleCpStr = availableCpList.stream()
                .map(cpConfig -> cpConfig.getAgent() + "-" + cpConfig.getCpCode())
                .collect(Collectors.joining(","));
        }

        PickUpLogUtil.info("可达性校验耗时:{}ms {},可用cp数:{},cps:{}", System.currentTimeMillis() - startTime, baseInfoRequest.getOuterOrderCode(),availableCpList.size(), avalibleCpStr);
        if (CollectionUtils.isEmpty(availableCpList)) {
            pickUpCheckResult.setAvailable(false);
            pickUpCheckResult.setUnavailableReason("服务商不可达");
            return pickUpCheckResult;
        }

        pickUpCheckResult.setAvailableCpList(availableCpList);
        return pickUpCheckResult;
    }

    /**
     * 过滤被禁用的cp
     * @param configInfoList
     * @param forbidCpCodes
     */
    private List<PickUpCpConfigInfo> filterForbidCp(List<PickUpCpConfigInfo> configInfoList, List<String> forbidCpCodes) {
        if(CollectionUtils.isEmpty(configInfoList) || CollectionUtils.isEmpty(forbidCpCodes)){
            return configInfoList;
        }
        List<PickUpCpConfigInfo> newConfigInfoList = new ArrayList<>();
        for (PickUpCpConfigInfo pickUpCpConfigInfo : configInfoList) {
            String agent = pickUpCpConfigInfo.getAgent();
            String cpCode = pickUpCpConfigInfo.getCpCode();
            Integer cpBizType = pickUpCpConfigInfo.getBizType();
            if(cpBizType == null){
                // 默认逆向
                cpBizType = 1;
            }
            boolean match = false;
            for(String forbidCpCode : forbidCpCodes){
                if(StringUtils.equals(agent+"-"+cpCode+"-"+cpBizType, forbidCpCode)){
                    match = true;
                    break;
                }
            }
            if(!match){
                newConfigInfoList.add(pickUpCpConfigInfo);
            }
        }
        return newConfigInfoList;
    }

    /**
     * 是否命中黑名单
     * @param baseInfoRequest
     * @param cpCode
     * @return
     */
    private boolean checkMatchBlackConfig(PickUpBaseInfoRequest baseInfoRequest, String cpCode) {
        try {
            // 查询揽件网点小件员 都使用发件地址
            ContractRouteInfo contractRouteInfo = queryBranchByRoute(cpCode, baseInfoRequest.getSendAddress());
            if(null != contractRouteInfo){
                String branchCode = contractRouteInfo.getBranchCode();
                String courierCode = contractRouteInfo.getAcceptCourierNo();

                Date dateStart = DateUtils.dateToDateStart(DateUtils.getDateBeforeNDays(30));
                Date dateEnd = DateUtils.dateToDateEnd(DateUtils.getDateBeforeNDays(0));
                if(StringUtils.isNotBlank(branchCode)){
                    WaybillCommonConfigParam queryBranchConfigParam = new WaybillCommonConfigParam();
                    queryBranchConfigParam.createCriteria()
                        .andScopeEqualTo(CommonConfigScope.SYSTEM_SCOPE.getCode())
                        .andConfigTypeEqualTo(CommonConfigTypeEnum.BLACK_BRANCH.getType())
                        .andConfigValueEqualTo(branchCode)
                        .andStatusEqualTo(CommonConfigStatusEnum.NORMAL.getStatus())
                        .andGmtCreateBetween(dateStart, dateEnd);

                    // 黑名单网点匹配
                    List<WaybillCommonConfigDO> configList = waybillCommonConfigMapper.selectByParam(queryBranchConfigParam);
                    if(!CollectionUtils.isEmpty(configList)){
                        PickUpLogUtil.info("路由命中黑名单拦截|网点黑名单|branchCode:{}|拉黑原因:{}|路由CP:{}|baseInfoRequest:{}", contractRouteInfo.getBranchCode(),
                            configList.get(0).getRemark(),
                            cpCode,
                            JSONObject.toJSONString(baseInfoRequest));
                        return true;
                    }
                }
                if(StringUtils.isNotBlank(courierCode)){
                    // 网点黑名单未命中 再校验小件员
                    WaybillCommonConfigParam queryCourierConfigParam = new WaybillCommonConfigParam();
                    queryCourierConfigParam.createCriteria()
                        .andScopeEqualTo(CommonConfigScope.SYSTEM_SCOPE.getCode())
                        .andConfigTypeEqualTo(CommonConfigTypeEnum.BLACK_COURIER.getType())
                        .andConfigValueEqualTo(courierCode)
                        .andStatusEqualTo(CommonConfigStatusEnum.NORMAL.getStatus())
                        .andGmtCreateBetween(dateStart, dateEnd);
                    // 黑名单小件员匹配
                    List<WaybillCommonConfigDO> configList = waybillCommonConfigMapper.selectByParam(queryCourierConfigParam);
                    if(!CollectionUtils.isEmpty(configList)){
                        PickUpLogUtil.info("路由命中黑名单拦截|小件员黑名单|acceptCourierNo:{}|拉黑原因:{}|路由CP:{}|baseInfoRequest:{}", contractRouteInfo.getAcceptCourierNo(),
                            configList.get(0).getRemark(),
                            cpCode,
                            JSONObject.toJSONString(baseInfoRequest));
                        return true;
                    }
                }
            }
        }catch (Exception e){
            PickUpLogUtil.errLog("", PickUpConstants.Action.BLACK_CONFIG_CHECK_ERROR.name(), "", e.getMessage(),
                ExceptionUtils.getFullStackTrace(e));
        }
        return false;
    }


    private RouteReachableResult checkReach(PickUpBaseInfoRequest cpRouteRequest, PickUpCpConfigInfo cpConfigInfo) {
        RouteBaseRequest routeBaseRequest = new RouteBaseRequest();
        routeBaseRequest.setSendName(cpRouteRequest.getSendName());
        routeBaseRequest.setSendMobile(cpRouteRequest.getSendMobile());
        routeBaseRequest.setSendPhone(cpRouteRequest.getSendPhone());
        routeBaseRequest.setSendAddress(cpRouteRequest.getSendAddress());
        routeBaseRequest.setReceiveName(cpRouteRequest.getConsignName());
        routeBaseRequest.setReceiveMobile(cpRouteRequest.getConsignMobile());
        routeBaseRequest.setReceivePhone(cpRouteRequest.getConsignPhone());
        routeBaseRequest.setReceiveAddress(cpRouteRequest.getConsignAddress());
        routeBaseRequest.setCpCode(cpConfigInfo.getCpCode());
        routeBaseRequest.setAgent(cpConfigInfo.getAgent());
        routeBaseRequest.setCpBizType(cpConfigInfo.getBizType());
        routeBaseRequest.setBizType(cpRouteRequest.getBizType());
        if(PickUpDetailBizTypeEnum.APPOINT.getValue() == cpRouteRequest.getBizType()){
            routeBaseRequest.setAppointGotStartTime(cpRouteRequest.getAppointGotStartTime());
            routeBaseRequest.setAppointGotEndTime(cpRouteRequest.getAppointGotEndTime());
        }
        routeBaseRequest.setResCode(cpRouteRequest.getResCode());
        routeBaseRequest.setOuterOrderCode(cpRouteRequest.getOuterOrderCode());
        routeBaseRequest.setCpServiceCode(cpConfigInfo.getCpServiceCode());
        routeBaseRequest.setPickUpCpConfigInfo(cpConfigInfo);

        RouteBaseRequest cacheRequest = buildCacheRequest(routeBaseRequest);

        // 校验缓存
        String cacheKey = MD5Utils.md5Encode(JSONObject.toJSONString(cacheRequest));
        Object cacheObj = tairManagerWrapper.get(cacheKey);
        if(null != cacheObj){
            RouteReachableResult cacheResult = (RouteReachableResult)cacheObj;
            PickUpLogUtil.info("路由可达性缓存命中|cacheKey:{}|request:{},cacheResult:{}", cacheKey
                , JSONObject.toJSONString(cacheRequest)
                , JSONObject.toJSONString(cacheResult));
            return cacheResult;
        }else {
            PickUpLogUtil.info("路由可达性缓存未命中|cacheKey:{}|request:{}", cacheKey, JSONObject.toJSONString(cacheRequest));
        }

        RouteReachableResult routeReachableResult = new RouteReachableResult();
        try {
            routeReachableResult = pickUpReachableManager.reachable(routeBaseRequest);
            tairManagerWrapper.put(cacheKey, routeReachableResult, BridgeSwitch.CHECK_REACHABLE_CACHE_EXPIRE_TIME);
        } catch (Exception exception) {
            PickUpLogUtil.errLog("", "check_reach_exception", "check_reach_exception: " + cpConfigInfo.getCpCode(),
                "校验可达异常", exception);
        }

        return routeReachableResult;
    }

    /**
     * 构建可达性缓存请求，兼容预查询和实时单下单请求
     * @param routeBaseRequest
     * @return
     */
    private RouteBaseRequest buildCacheRequest(RouteBaseRequest routeBaseRequest) {
        RouteBaseRequest cacheRequest = new RouteBaseRequest();
        BeanUtils.copyProperties(routeBaseRequest, cacheRequest);
        AddressDTO sendAddress = cacheRequest.getSendAddress();
        sendAddress.setProvinceId(null);
        sendAddress.setCityId(null);
        sendAddress.setAreaId(null);
        sendAddress.setTownId(null);
        sendAddress.setVillageId(null);
        cacheRequest.setSendAddress(sendAddress);
        AddressDTO receiveAddress = cacheRequest.getReceiveAddress();
        receiveAddress.setProvinceId(null);
        receiveAddress.setCityId(null);
        receiveAddress.setAreaId(null);
        receiveAddress.setTownId(null);
        receiveAddress.setVillageId(null);
        cacheRequest.setReceiveAddress(receiveAddress);
        cacheRequest.setOuterOrderCode(null);
        cacheRequest.setPickUpCpConfigInfo(null);
        if(cacheRequest.getBizType() == null){
            cacheRequest.setBizType(PickUpDetailBizTypeEnum.NORMAL.getValue());
        }
        return cacheRequest;
    }

    /**
     * 根据路由查询订单揽收的网点及小件员信息
     * @param cpCode
     * @param sendAddress
     * @return
     */
    public ContractRouteInfo queryBranchByRoute(String cpCode, AddressDTO sendAddress) {
        if(StringUtils.isBlank(cpCode) || null == sendAddress){
            return null;
        }
        ContractRouteInfo contractRouteInfo = new ContractRouteInfo();
        try {
            RouteBaseRequest routeBaseRequest = new RouteBaseRequest();
            routeBaseRequest.setCpCode(cpCode);
            routeBaseRequest.setSendAddress(sendAddress);
            // 都是用寄件地址计算 揽件网点
            routeBaseRequest.setReceiveAddress(sendAddress);
            RouteBaseResult routingInfo = routingManager.contractRoute(routeBaseRequest);
            if(null == routingInfo){
                return null;
            }
            contractRouteInfo.setBranchCode(routingInfo.getAcceptOrgCode());
            contractRouteInfo.setAcceptCourierNo(routingInfo.getAcceptCourierNo());
            return contractRouteInfo;
        }catch (Exception e){
            PickUpLogUtil.errLog("", PickUpConstants.Action.CONTRACT_ROUTE_EXCEPTION.name(), "CONTRACT_ROUTE_EXCEPTION", "CONTRACT_ROUTE_EXCEPTION", e);
        }
        return null;
    }
}
