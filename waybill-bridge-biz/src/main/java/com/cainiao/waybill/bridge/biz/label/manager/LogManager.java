package com.cainiao.waybill.bridge.biz.label.manager;

import com.cainiao.waybill.common.dto.ClientInfoDTO;
import org.slf4j.Logger;

/**
 * 日志管理接口
 * 
 * <AUTHOR>
 * @since Feb 17, 2016 1:32:41 PM
 */
public interface LogManager {

    /**
     * 记录异常日志
     * 
     * @param action 动作，一般指方法名
     * @param message 日志消息
     * @param e 异常信息
     * @param clientInfo 调用方应用信息
     * @param logger 日志输出
     */
    void logExceptionMessage(String action, String message, Throwable e, ClientInfoDTO clientInfo, Logger logger);

    /**
     * 记录异常日志
     * 
     * @param action 动作，一般指方法名
     * @param message 日志消息
     * @param e 异常信息
     * @param logger 日志输出
     */
    void logExceptionMessage(String action, String message, Throwable e, Logger logger);

    /**
     * 记录错误日志
     * 
     * @param action 动作，一般指方法名
     * @param message 日志消息
     * @param clientInfo 调用方应用信息
     * @param logger 日志输出
     */
    void logErrorMessage(String action, String message, ClientInfoDTO clientInfo, Logger logger);

    /**
     * 记录错误日志
     * 
     * @param action 动作，一般指方法名
     * @param messageFormat 日志消息格式，遵循log4j标准
     * @param argArray 日志消息格式中对应的参数
     * @param clientInfo 调用方应用信息
     * @param logger 日志输出
     */
    void logErrorMessage(String action, String messageFormat, Object[] argArray, ClientInfoDTO clientInfo,
                         Logger logger);

    /**
     * 记录错误日志
     * 
     * @param action 动作，一般指方法名
     * @param message 日志消息
     * @param logger 日志输出
     */
    void logErrorMessage(String action, String message, Logger logger);

    /**
     * 记录错误日志
     * 
     * @param action 动作，一般指方法名
     * @param messageFormat 日志消息格式，遵循log4j标准
     * @param argArray 日志消息格式中对应的参数
     * @param logger 日志输出
     */
    void logErrorMessage(String action, String messageFormat, Object[] argArray, Logger logger);

    /**
     * 记录警告日志
     * 
     * @param action 动作，一般指方法名
     * @param message 日志消息
     * @param clientInfo 调用方应用信息
     * @param logger 日志输出
     */
    void logWarnMessage(String action, String message, ClientInfoDTO clientInfo, Logger logger);

    /**
     * 记录警告日志
     * 
     * @param action 动作，一般指方法名
     * @param messageFormat 日志消息格式，遵循log4j标准
     * @param argArray 日志消息格式中对应的参数
     * @param clientInfo 调用方应用信息
     * @param logger 日志输出
     */
    void logWarnMessage(String action, String messageFormat, Object[] argArray, ClientInfoDTO clientInfo, Logger logger);

    /**
     * 记录警告日志
     * 
     * @param action 动作，一般指方法名
     * @param message 日志消息
     * @param logger 日志输出
     */
    void logWarnMessage(String action, String message, Logger logger);

    /**
     * 记录警告日志
     * 
     * @param action 动作，一般指方法名
     * @param messageFormat 日志消息格式，遵循log4j标准
     * @param logger 日志输出
     */
    void logWarnMessage(String action, String messageFormat, Object[] argArray, Logger logger);

    /**
     * 记录正常日志
     * 
     * @param action 动作，一般指方法名
     * @param message 日志消息
     * @param clientInfo 调用方应用信息
     * @param logger 日志输出
     */
    void logInfoMessage(String action, String message, ClientInfoDTO clientInfo, Logger logger);

    /**
     * 记录正常日志
     * 
     * @param action 动作，一般指方法名
     * @param messageFormat 日志消息格式，遵循log4j标准
     * @param clientInfo 调用方应用信息
     * @param logger 日志输出
     */
    void logInfoMessage(String action, String messageFormat, Object[] argArray, ClientInfoDTO clientInfo, Logger logger);

    /**
     * 记录正常日志
     * 
     * @param action 动作，一般指方法名
     * @param message 日志消息
     * @param logger 日志输出
     */
    void logInfoMessage(String action, String message, Logger logger);

    /**
     * 记录正常日志
     * 
     * @param action 动作，一般指方法名
     * @param messageFormat 日志消息格式，遵循log4j标准
     * @param logger 日志输出
     */
    void logInfoMessage(String action, String messageFormat, Object[] argArray, Logger logger);
}
