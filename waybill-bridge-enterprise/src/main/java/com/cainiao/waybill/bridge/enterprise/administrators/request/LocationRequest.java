package com.cainiao.waybill.bridge.enterprise.administrators.request;


import com.cainiao.waybill.bridge.enterprise.common.Paging;
import com.cainiao.waybill.bridge.model.dto.AddressInfo;
import com.cainiao.waybill.bridge.model.dto.WaybillAccount;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 该类用于查询场地基本信息
 * <AUTHOR>
 * @date 2025-04-16 11:10:12
 */
@Data
public class LocationRequest extends Paging implements Serializable {

    private static final long serialVersionUID = -5189965672167198986L;

    /**
     * 场地名称
     */
    private String locationName;

    /**
     * 场地地址
     */
    private AddressInfo locationAddress;

    /**
     * 场地地址
     */
    private AddressInfo locationAddressCode;

    /**
     * 场地备注
     */
    private String remark;

    /**
     * 月结账号 外键
     */
    private String waybillAccountsString;


    /**
     * 月结账号 外键
     */
    private List<WaybillAccount> waybillAccountsList;

    /**
     * 是否删除月结账号
     */
    private Boolean deleteAccount;
}
