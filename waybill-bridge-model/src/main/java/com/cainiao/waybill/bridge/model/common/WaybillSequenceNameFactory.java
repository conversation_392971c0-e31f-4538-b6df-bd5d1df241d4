package com.cainiao.waybill.bridge.model.common;

import org.slf4j.Logger;

import com.google.common.base.Strings;
import com.taobao.cainiao.waybill.base.Loggers;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant;
import com.taobao.common.dao.persistence.exception.DAOException;
import com.taobao.tddl.client.sequence.exception.SequenceException;

/**
 * Created by xdang on 16/6/1.
 */
public class WaybillSequenceNameFactory {

    private static final String PREFIX = "WAYBILL";

    private static final String SPLIT  = "_";

    Logger logger = Loggers.sequenceLogger;

    WaybillTddlBaseDAO          waybillTddlBaseDAO;

    private long getId() throws DAOException {
        try {
            return waybillTddlBaseDAO.getWaybillSequenceNameId();
        } catch (SequenceException e) {
            throw new DAOException(WaybillErrorConstant.SystemError.TDDL_SEQUENCE_ERROR.getErrorMsg(), e);
        } catch (Exception e) {
            throw new DAOException(e);
        }
    }

    public String buildSequenceName(String cpCode) {

        StringBuilder nameBuilder = new StringBuilder(PREFIX);

        if (!Strings.isNullOrEmpty(cpCode)) {
            nameBuilder.append(SPLIT).append(cpCode);
        }

        try {
            nameBuilder.append(SPLIT).append(getId());
        } catch (DAOException e) {
            logger.error("build sequence name error: " + e.getMessage(), e);
            return null;
        }

        return nameBuilder.toString();
    }

    public void setWaybillTddlBaseDAO(WaybillTddlBaseDAO waybillTddlBaseDAO) {
        this.waybillTddlBaseDAO = waybillTddlBaseDAO;
    }
}
