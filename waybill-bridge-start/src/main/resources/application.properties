
project.name=waybill-bridge

# tddléç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-tddl
#spring.tddl.app=$tddl.app
#spring.tddl.sharding=$tddl.sharding
# mybatis
#mybatis.config-location=classpath:/mybatis/mybatis-config.xml
# hsféç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf
spring.hsf.group=$hsf.group
spring.hsf.version=$hsf.version
spring.hsf.timeout=$hsf.timeout
ad.spring.hsf.version=$ad.spring.hsf.version
hsf.print.version=$hsf.print.version
hsf.RemoteWriteService.version=$hsf.RemoteWriteService.version
# tairéç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-tair
spring.tair.config-ids.rcResourceTairManager=$rcResourceTairManager.config.id
spring.tair.dynamic-configs.rcResourceTairManager=true

spring.tair.config-ids.waybillTairManager=$waybill.tair.configid
spring.tair.dynamic-configs.waybillTairManager=true


# sentinel web filteréç½®
spring.sentinel.filter.urlPatterns=*.htm
# alimonitoréç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-alimonitor
spring.alimonitor.method-patterns[0]=com.cainiao.waybill.bridge.service.alimonitor.AliMonitorDemo.exception
spring.alimonitor.method-patterns[1]=com.cainiao.waybill.bridge.service.alimonitor.AliMonitorDemo.normal
spring.alimonitor.excluded-suffixes=gif,css,js,ico,do
# eagleeyeéç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-eagleeye
spring.eagleeye.enabled=true
# notifyéç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-notify
#spring.notify.subscriber.group-id=S-pb-test-subscriber1
#spring.notify.subscriber.messageListener=messageListener
#spring.notify.subscriber.binding.key=type
#spring.notify.subscriber.binding.topic=pandora-boot-test-topic
#spring.notify.publisher.group-id=P-pb-test
#spring.notify.publisher.topics=pandora-boot-test-topic2, pandora-boot-test-topic
spring.notify.publisher.group-id=client-publisher-groupId
spring.notify.publisher.topics=tpn-common

spring.ons.producer.producer-ids.waybill-pick-up-producer=PID-waybill-pick-up-producer
spring.ons.producer.mq-types.waybill-pick-up-producer=METAQ
spring.ons.consumer.consumer-ids.waybill-pick-up-consumer=CID-waybill-pick-up-consumer
spring.ons.consumer.consume-thread-numses.waybill-pick-up-consumer=2
spring.ons.consumer.mq-types.waybill-pick-up-consumer=METAQ

spring.ons.producer.producer-ids.waybill-pick-up-ticket-producer=PID-waybill-pick-up-ticket-producer
spring.ons.producer.mq-types.waybill-pick-up-ticket-producer=METAQ
spring.ons.consumer.consumer-ids.waybill-pick-up-ticket-consumer=CID-waybill-pick-up-ticket-consumer
spring.ons.consumer.consume-thread-numses.waybill-pick-up-ticket-consumer=2
spring.ons.consumer.mq-types.waybill-pick-up-ticket-consumer=METAQ

spring.ons.consumer.consumer-ids.waybill-voice-call-consumer=CID-waybill-voice-call-consumer
spring.ons.consumer.consume-thread-numses.waybill-voice-call-consumer=2
spring.ons.consumer.mq-types.waybill-voice-call-consumer=METAQ

spring.ons.consumer.consumer-ids.waybill-virtual-call-consumer=CID-waybill-virtual-call-consumer
spring.ons.consumer.consume-thread-numses.waybill-virtual-call-consumer=2
spring.ons.consumer.mq-types.waybill-virtual-call-consumer=METAQ

spring.ons.consumer.consumer-ids.waybill-pick-up-addition=CID-waybill-pick-up-addition
spring.ons.consumer.consume-thread-numses.waybill-pick-up-addition=2
spring.ons.consumer.mq-types.waybill-pick-up-addition=METAQ

spring.ons.consumer.consumer-ids.waybill-bridge-enterprise-action_consumer=CID-waybill-bridge-enterprise-action_consumer
spring.ons.consumer.consume-thread-numses.waybill-bridge-enterprise-action_consumer=4
spring.ons.consumer.mq-types.waybill-bridge-enterprise-action_consumer=METAQ

#æ¥åå20minæªçµèåºç¨åºæ¯
spring.ons.producer.producer-ids.waybill-accept-voice-producer=PID-waybill-accept-voice-producer
spring.ons.producer.mq-types.waybill-accept-voice-producer=METAQ
spring.ons.consumer.consumer-ids.waybill-accept-voice-consumer=CID-waybill-accept-voice-consumer
spring.ons.consumer.consume-thread-numses.waybill-accept-voice-consumer=2
spring.ons.consumer.mq-types.waybill-accept-voice-consumer=METAQ

#å¯ä»¶ååå¨æä»¿çåºæ¯
spring.ons.producer.producer-ids.waybill-route-dyn-simulator-producer=PID-waybill-route-dyn-simulator-producer
spring.ons.producer.mq-types.waybill-route-dyn-simulator-producer=METAQ
spring.ons.consumer.consumer-ids.waybill-route-dyn-simulator-consumer=CID-waybill-route-dyn-simulator-consumer
spring.ons.consumer.consume-thread-numses.waybill-route-dyn-simulator-consumer=2
spring.ons.consumer.mq-types.waybill-route-dyn-simulator-consumer=METAQ

#å¯¹å¤è¾åºè¯¦æåºæ¯
spring.ons.producer.producer-ids.waybill-ld-sub-producer=PID-waybill-ld-sub-producer
spring.ons.producer.mq-types.waybill-ld-sub-producer=METAQ
spring.ons.consumer.consumer-ids.waybill-ld-sub-consumer=CID-waybill-ld-sub-consumer
spring.ons.consumer.consume-thread-numses.waybill-ld-sub-consumer=2
spring.ons.consumer.mq-types.waybill-ld-sub-consumer=METAQ

#å¯ä»¶è®¢åé¢è­¦åºæ¯
spring.ons.producer.producer-ids.waybill-warning-delay-producer=PID-waybill-warning-delay-producer
spring.ons.producer.mq-types.waybill-warning-delay-producer=METAQ
spring.ons.consumer.consumer-ids.waybill-warning-delay-consumer=CID-waybill-warning-delay-consumer
spring.ons.consumer.consume-thread-numses.waybill-warning-delay-consumer=2
spring.ons.consumer.mq-types.waybill-warning-delay-consumer=METAQ


#å¤é¨å¯ä»¶è®¢åä¸å
spring.ons.producer.producer-ids.waybill-pu-ld-sub-after-producer=PID-waybill-pu-ld-sub-after-producer
spring.ons.producer.mq-types.waybill-pu-ld-sub-after-producer=METAQ
spring.ons.consumer.consumer-ids.waybill-pu-ld-sub-after-consumer=CID-waybill-pu-ld-sub-after-consumer
spring.ons.consumer.consume-thread-numses.waybill-pu-ld-sub-after-consumer=2
spring.ons.consumer.mq-types.waybill-pu-ld-sub-after-consumer=METAQ

#éç§é¢åæ¶è´¹èè®¢è´­
spring.ons.consumer.consumer-ids.waybill-privacy-buyer-phone-consumer=CID-waybill-privacy-buyer-phone-consumer
spring.ons.consumer.consume-thread-numses.waybill-privacy-buyer-phone-consumer=2
spring.ons.consumer.mq-types.waybill-privacy-buyer-phone-consumer=METAQ


spring.ons.producer.producer-ids.charity-file-analysis-producer=PID-charity-file-analysis-producer
spring.ons.producer.mq-types.charity-file-analysis-producer=METAQ
spring.ons.consumer.consumer-ids.waybill-charity-file-analysis-consumer=waybill-charity-file-analysis-consumer
spring.ons.consumer.consume-thread-numses.waybill-charity-file-analysis-consumer=2
spring.ons.consumer.mq-types.waybill-charity-file-analysis-consumer=METAQ

#??link??
link.global.appKey=waybill-bridge
link.global.appSecret=SIYONG4BRIDGE

#   å®å¨å®¡æ ¸æåºçéç½®
spring.security.jsonp.enabled = false

#æ¯æå¤ä¸ªè¡¨è¾¾å¼è±æéå·éå¼ æ³¨æå¦æä¸æ¯éç½®ä¸º /* ï¼åéè¦å ä¸ /sendBucSSOToken.do,/bucSSOLogout.do
spring.buc.urlPatterns=/*
#spring.buc.appCode=0cfe94cdbe2c4bffb1888a7b0d2109e1
#spring.buc.loginEnv=daily
spring.buc.appCode=861e4652da1e46ac86dda61bf75449a7
spring.buc.loginEnv=online
spring.buc.exclusions=/charity/**,/onefound/**

spring.security.csrf.enabled = false
spring.security.csrf.supportedMethods=POST,GET
#ä½¿ç¨anté£æ ¼éç½®éè¦è¿è¡tokenæ£æ¥çurl(å®å¨è¦æ±å¯¹ææå¢å æ¹è¿è¡tokenæ ¡éª)
spring.security.csrf.url.included = /**
spring.security.csrf.url.excluded=/tracepush/yto,/tracepush/deppon,/checkpreload.htm


## Velocityéç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-velocity
# æ¯å¦å¼å¯velocityèªå¨è£éï¼é»è®¤ä¸ºtrueãå¦æè®¾ç½®ä¸ºfalseï¼å¶ä»çéç½®é¡¹å¤±æã
spring.velocity.enabled=true
# Velocityé¡µé¢èµæºè·¯å¾ï¼é»è®¤å¼ï¼"classpath:/templates/"
spring.velocity.resource-loader-path=classpath:/velocity/templates
# å¸å±é¡µé¢èµæºè·¯å¾ï¼ç¸å¯¹äºclasspathç®å½ä¸ï¼ä¸éè¦å¢å åè®®å¤´"classpath:"ï¼
spring.velocity.layout-url=/velocity/layout/default.vm

#æ°æ®åºæ¥å¿
#mybatis.configuration.log-impl= org.apache.ibatis.logging.stdout.StdOutImpl


app.tea.flag=$app.tea.flag
app.tea.endpoint=ocr-api.cn-hangzhou.aliyuncs.com

enterprise.dingtalk.clientId=$enterprise.dingtalk.clientId
enterprise.dingtalk.clientSecret=$enterprise.dingtalk.clientSecret

spring.mvc.async.request-timeout=5000
server.tomcat.connection-timeout=10000

spring.nbp.scan=com.cainiao.waybill.bridge.biz
auto.switch.cp.template.code=auto_switch_cp_task
over.time.auto.got.template.code=over_time_auto_got_task

# æ¯å¦å¯ç¨å¼æ­¥å éï¼é»è®¤ true
spring.startup.speedup.enabled=true
# è¦å¼æ­¥åå§åç bean
spring.startup.speedup.async-bean-patterns=schedulerxWorker|metaProducer|tairManager

