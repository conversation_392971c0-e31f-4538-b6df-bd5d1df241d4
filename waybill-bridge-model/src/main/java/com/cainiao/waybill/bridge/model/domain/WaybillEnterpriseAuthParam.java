package com.cainiao.waybill.bridge.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
public class WaybillEnterpriseAuthParam {
    /**
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     *
     * @mbg.generated
     */
    @Deprecated
    protected boolean distinct;

    /**
     *
     * @mbg.generated
     */
    protected boolean page;

    /**
     *
     * @mbg.generated
     */
    protected int pageIndex;

    /**
     *
     * @mbg.generated
     */
    protected int pageSize;

    /**
     *
     * @mbg.generated
     */
    protected int pageStart;

    /**
     *
     * @mbg.generated
     */
    protected String distinctSql;

    /**
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public WaybillEnterpriseAuthParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * @param orderCondition
     * @param sortType
     * @return
     *
     * @mbg.generated
     */
    public WaybillEnterpriseAuthParam appendOrderByClause(OrderCondition orderCondition, SortType sortType) {
        if (null != orderByClause) {
            orderByClause = orderByClause + ", " + orderCondition.getColumnName() + " " + sortType.getValue();
        } else {
            orderByClause = orderCondition.getColumnName() + " " + sortType.getValue();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * @param distinct
     *
     * @mbg.generated
     */
    @Deprecated
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Deprecated
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * @param page
     * @return
     *
     * @mbg.generated
     */
    public WaybillEnterpriseAuthParam setPage(boolean page) {
        this.page = page;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public boolean isPage() {
        return page;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageIndex() {
        return pageIndex;
    }

    /**
     * @param pageSize
     * @return
     *
     * @mbg.generated
     */
    public WaybillEnterpriseAuthParam setPageSize(int pageSize) {
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageStart
     * @return
     *
     * @mbg.generated
     */
    public WaybillEnterpriseAuthParam setPageStart(int pageStart) {
        this.pageStart = pageStart < 1 ? 1 : pageStart;
        this.pageIndex = (this.pageStart - 1) * this.pageSize;
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public int getPageStart() {
        return pageStart;
    }

    /**
     * @param pageStart
     * @param pageSize
     *
     * @mbg.generated
     */
    public void setPagination(int pageStart, int pageSize) {
        this.page = true;
        this.pageSize = pageSize < 1 ? 10 : pageSize;
        this.pageIndex = pageStart < 1 ? 0 : (pageStart - 1) * this.pageSize;
    }

    /**
     * @param condition
     * @return
     *
     * @mbg.generated
     */
    public WaybillEnterpriseAuthParam appendDistinct(OrderCondition condition) {
        if (null != distinctSql){
            distinctSql = distinctSql + ", " + condition.getColumnName();
        } else {
            distinctSql = condition.getColumnName();
        }
        return this;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * @param criteria
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     *
     * @mbg.generated
     */
    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andCorpIdIsNull() {
            addCriterion("corp_id is null");
            return (Criteria) this;
        }

        public Criteria andCorpIdIsNotNull() {
            addCriterion("corp_id is not null");
            return (Criteria) this;
        }

        public Criteria andCorpIdEqualTo(String value) {
            addCriterion("corp_id =", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdNotEqualTo(String value) {
            addCriterion("corp_id <>", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdGreaterThan(String value) {
            addCriterion("corp_id >", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdGreaterThanOrEqualTo(String value) {
            addCriterion("corp_id >=", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdLessThan(String value) {
            addCriterion("corp_id <", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdLessThanOrEqualTo(String value) {
            addCriterion("corp_id <=", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdLike(String value) {
            addCriterion("corp_id like", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdNotLike(String value) {
            addCriterion("corp_id not like", value, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdIn(List<String> values) {
            addCriterion("corp_id in", values, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdNotIn(List<String> values) {
            addCriterion("corp_id not in", values, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdBetween(String value1, String value2) {
            addCriterion("corp_id between", value1, value2, "corpId");
            return (Criteria) this;
        }

        public Criteria andCorpIdNotBetween(String value1, String value2) {
            addCriterion("corp_id not between", value1, value2, "corpId");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketIsNull() {
            addCriterion("suite_ticket is null");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketIsNotNull() {
            addCriterion("suite_ticket is not null");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketEqualTo(String value) {
            addCriterion("suite_ticket =", value, "suiteTicket");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketNotEqualTo(String value) {
            addCriterion("suite_ticket <>", value, "suiteTicket");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketGreaterThan(String value) {
            addCriterion("suite_ticket >", value, "suiteTicket");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketGreaterThanOrEqualTo(String value) {
            addCriterion("suite_ticket >=", value, "suiteTicket");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketLessThan(String value) {
            addCriterion("suite_ticket <", value, "suiteTicket");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketLessThanOrEqualTo(String value) {
            addCriterion("suite_ticket <=", value, "suiteTicket");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketLike(String value) {
            addCriterion("suite_ticket like", value, "suiteTicket");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketNotLike(String value) {
            addCriterion("suite_ticket not like", value, "suiteTicket");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketIn(List<String> values) {
            addCriterion("suite_ticket in", values, "suiteTicket");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketNotIn(List<String> values) {
            addCriterion("suite_ticket not in", values, "suiteTicket");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketBetween(String value1, String value2) {
            addCriterion("suite_ticket between", value1, value2, "suiteTicket");
            return (Criteria) this;
        }

        public Criteria andSuiteTicketNotBetween(String value1, String value2) {
            addCriterion("suite_ticket not between", value1, value2, "suiteTicket");
            return (Criteria) this;
        }

        public Criteria andRefreshTimeIsNull() {
            addCriterion("refresh_time is null");
            return (Criteria) this;
        }

        public Criteria andRefreshTimeIsNotNull() {
            addCriterion("refresh_time is not null");
            return (Criteria) this;
        }

        public Criteria andRefreshTimeEqualTo(Date value) {
            addCriterion("refresh_time =", value, "refreshTime");
            return (Criteria) this;
        }

        public Criteria andRefreshTimeNotEqualTo(Date value) {
            addCriterion("refresh_time <>", value, "refreshTime");
            return (Criteria) this;
        }

        public Criteria andRefreshTimeGreaterThan(Date value) {
            addCriterion("refresh_time >", value, "refreshTime");
            return (Criteria) this;
        }

        public Criteria andRefreshTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("refresh_time >=", value, "refreshTime");
            return (Criteria) this;
        }

        public Criteria andRefreshTimeLessThan(Date value) {
            addCriterion("refresh_time <", value, "refreshTime");
            return (Criteria) this;
        }

        public Criteria andRefreshTimeLessThanOrEqualTo(Date value) {
            addCriterion("refresh_time <=", value, "refreshTime");
            return (Criteria) this;
        }

        public Criteria andRefreshTimeIn(List<Date> values) {
            addCriterion("refresh_time in", values, "refreshTime");
            return (Criteria) this;
        }

        public Criteria andRefreshTimeNotIn(List<Date> values) {
            addCriterion("refresh_time not in", values, "refreshTime");
            return (Criteria) this;
        }

        public Criteria andRefreshTimeBetween(Date value1, Date value2) {
            addCriterion("refresh_time between", value1, value2, "refreshTime");
            return (Criteria) this;
        }

        public Criteria andRefreshTimeNotBetween(Date value1, Date value2) {
            addCriterion("refresh_time not between", value1, value2, "refreshTime");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNull() {
            addCriterion("platform is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNotNull() {
            addCriterion("platform is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformEqualTo(String value) {
            addCriterion("platform =", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotEqualTo(String value) {
            addCriterion("platform <>", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThan(String value) {
            addCriterion("platform >", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanOrEqualTo(String value) {
            addCriterion("platform >=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThan(String value) {
            addCriterion("platform <", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanOrEqualTo(String value) {
            addCriterion("platform <=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLike(String value) {
            addCriterion("platform like", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotLike(String value) {
            addCriterion("platform not like", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformIn(List<String> values) {
            addCriterion("platform in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotIn(List<String> values) {
            addCriterion("platform not in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformBetween(String value1, String value2) {
            addCriterion("platform between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotBetween(String value1, String value2) {
            addCriterion("platform not between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andIdEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id =", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <>", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id >=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualToWhenPresent(Long value) {
            if(value != null) {
                addCriterion("id <=", value, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotInWhenPresent(List<Long> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("id not in", values, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andIdNotBetweenWhenPresent(Long value1, Long value2) {
            if(value1 != null && value2 != null){
                addCriterion("id not between", value1, value2, "id");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create =", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <>", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create >=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_create <=", value, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_create not in", values, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified =", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <>", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified >=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("gmt_modified <=", value, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("gmt_modified not in", values, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id =", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id <>", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id >", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id >=", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id <", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id <=", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id like", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("corp_id not like", value, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("corp_id in", values, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("corp_id not in", values, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("corp_id between", value1, value2, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andCorpIdNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("corp_id not between", value1, value2, "corpId");
            }
            return (Criteria) this;
        }

        public Criteria andSuiteTicketEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("suite_ticket =", value, "suiteTicket");
            }
            return (Criteria) this;
        }

        public Criteria andSuiteTicketNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("suite_ticket <>", value, "suiteTicket");
            }
            return (Criteria) this;
        }

        public Criteria andSuiteTicketGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("suite_ticket >", value, "suiteTicket");
            }
            return (Criteria) this;
        }

        public Criteria andSuiteTicketGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("suite_ticket >=", value, "suiteTicket");
            }
            return (Criteria) this;
        }

        public Criteria andSuiteTicketLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("suite_ticket <", value, "suiteTicket");
            }
            return (Criteria) this;
        }

        public Criteria andSuiteTicketLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("suite_ticket <=", value, "suiteTicket");
            }
            return (Criteria) this;
        }

        public Criteria andSuiteTicketLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("suite_ticket like", value, "suiteTicket");
            }
            return (Criteria) this;
        }

        public Criteria andSuiteTicketNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("suite_ticket not like", value, "suiteTicket");
            }
            return (Criteria) this;
        }

        public Criteria andSuiteTicketInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("suite_ticket in", values, "suiteTicket");
            }
            return (Criteria) this;
        }

        public Criteria andSuiteTicketNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("suite_ticket not in", values, "suiteTicket");
            }
            return (Criteria) this;
        }

        public Criteria andSuiteTicketBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("suite_ticket between", value1, value2, "suiteTicket");
            }
            return (Criteria) this;
        }

        public Criteria andSuiteTicketNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("suite_ticket not between", value1, value2, "suiteTicket");
            }
            return (Criteria) this;
        }

        public Criteria andRefreshTimeEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("refresh_time =", value, "refreshTime");
            }
            return (Criteria) this;
        }

        public Criteria andRefreshTimeNotEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("refresh_time <>", value, "refreshTime");
            }
            return (Criteria) this;
        }

        public Criteria andRefreshTimeGreaterThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("refresh_time >", value, "refreshTime");
            }
            return (Criteria) this;
        }

        public Criteria andRefreshTimeGreaterThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("refresh_time >=", value, "refreshTime");
            }
            return (Criteria) this;
        }

        public Criteria andRefreshTimeLessThanWhenPresent(Date value) {
            if(value != null) {
                addCriterion("refresh_time <", value, "refreshTime");
            }
            return (Criteria) this;
        }

        public Criteria andRefreshTimeLessThanOrEqualToWhenPresent(Date value) {
            if(value != null) {
                addCriterion("refresh_time <=", value, "refreshTime");
            }
            return (Criteria) this;
        }

        public Criteria andRefreshTimeInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("refresh_time in", values, "refreshTime");
            }
            return (Criteria) this;
        }

        public Criteria andRefreshTimeNotInWhenPresent(List<Date> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("refresh_time not in", values, "refreshTime");
            }
            return (Criteria) this;
        }

        public Criteria andRefreshTimeBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("refresh_time between", value1, value2, "refreshTime");
            }
            return (Criteria) this;
        }

        public Criteria andRefreshTimeNotBetweenWhenPresent(Date value1, Date value2) {
            if(value1 != null && value2 != null){
                addCriterion("refresh_time not between", value1, value2, "refreshTime");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform =", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformNotEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform <>", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform >", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform >=", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform <", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanOrEqualToWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform <=", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform like", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformNotLikeWhenPresent(String value) {
            if(value != null) {
                addCriterion("platform not like", value, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("platform in", values, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformNotInWhenPresent(List<String> values) {
            if(values != null && !values.isEmpty()){
                addCriterion("platform not in", values, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("platform between", value1, value2, "platform");
            }
            return (Criteria) this;
        }

        public Criteria andPlatformNotBetweenWhenPresent(String value1, String value2) {
            if(value1 != null && value2 != null){
                addCriterion("platform not between", value1, value2, "platform");
            }
            return (Criteria) this;
        }
    }

    /**
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum OrderCondition {
        /**
         *主键
         */
        ID("id"),
        /**
         *创建时间
         */
        GMTCREATE("gmt_create"),
        /**
         *修改时间
         */
        GMTMODIFIED("gmt_modified"),
        /**
         *企业ID
         */
        CORPID("corp_id"),
        /**
         *企业套件票据
         */
        SUITETICKET("suite_ticket"),
        /**
         *刷新时间
         */
        REFRESHTIME("refresh_time"),
        /**
         *平台
         */
        PLATFORM("platform");

        private String columnName;

        OrderCondition(String columnName) {
            this.columnName = columnName;
        }

        public String getColumnName() {
            return columnName;
        }

        public static OrderCondition getEnumByName(String name) {
            OrderCondition[] orderConditions = OrderCondition.values();
            for (OrderCondition orderCondition : orderConditions) {
                if (orderCondition.name().equalsIgnoreCase(name)) {
                    return orderCondition;
                }
            }
            throw new RuntimeException("OrderCondition of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return columnName;
        }
    }

    /**
     *
     * @mbg.generated
     */
    public enum SortType {
        /**
         * 升序
         */
        ASC("asc"),
        /**
         * 降序
         */
        DESC("desc");

        private String value;

        SortType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static SortType getEnumByName(String name) {
            SortType[] sortTypes = SortType.values();
            for (SortType sortType : sortTypes) {
                if (sortType.name().equalsIgnoreCase(name)) {
                    return sortType;
                }
            }
            throw new RuntimeException("SortType of " + name + " enum not exist");
        }

        @Override
        public String toString() {
            return value;
        }
    }
}