package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.cainiao.waybill.bridge.biz.logistics.dto.SubscribeAppInfo;
import com.cainiao.waybill.bridge.biz.logistics.dto.WaybillLogisticsSubscribeRequest;
import com.cainiao.waybill.bridge.biz.logistics.dto.WaybillLogisticsSubscribeResponse;
import com.cainiao.waybill.bridge.biz.logistics.manager.TmsWaybillLogisticDetailManager;
import com.cainiao.waybill.bridge.biz.logistics.util.LogisticsDetailMapper;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.base.ScenarioConstant;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.util.LoggerMonitorUtil;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.client.sdk.dataobject.request.TMS_WAYBILL_LOGISTICS_SUBSCRIBE.TmsWaybillLogisticsSubscribeRequest;
import com.taobao.pac.client.sdk.dataobject.response.TMS_WAYBILL_LOGISTICS_SUBSCRIBE.ResultDTO;
import com.taobao.pac.client.sdk.dataobject.response.TMS_WAYBILL_LOGISTICS_SUBSCRIBE.TmsWaybillLogisticsSubscribeResponse;
import com.taobao.pac.client.sdk.receiveservice.TmsWaybillLogisticsSubscribeService;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;

/**
 * 物流详情订阅接口
 *
 * 封装供应链团队的物流详情接口实现，文档详见：https://www.yuque.com/miaoxuan-l0whb/qfc4nc/groiagntlp6gp813
 * 物流详情调用供应链团队接口为 : CNTECH_LV_LOGISTICS_DETAIL_SUBSCRIBE_V2
 * <AUTHOR>
 * @date 2024/2/28
 */
public class TmsWaybillLogisticsSubServiceImpl implements TmsWaybillLogisticsSubscribeService {

    @Resource
    private TmsWaybillLogisticDetailManager  tmsWaybillLogisticDetailManager;

    @Override
    public TmsWaybillLogisticsSubscribeResponse invoke(ReceiveParams<TmsWaybillLogisticsSubscribeRequest> receiveParams) {
        TmsWaybillLogisticsSubscribeResponse response = new TmsWaybillLogisticsSubscribeResponse();
        TmsWaybillLogisticsSubscribeRequest subscribeRequest = receiveParams.getRequestDataObject();
        try {

            // 参数校验
            checkParam(subscribeRequest);

            LoggerMonitorUtil.start(ScenarioConstant.WAYBILL_LOGISTICS_DETAIL_SUB, subscribeRequest.getMailNo(), subscribeRequest.getCpCode(), receiveParams.getFromAppkey());

            // 调用供应链接口订阅物流详情接口
            response = doLogisticsSubscribe(receiveParams);

            return response;
        } catch (BridgeBaseException e) {
            response.setSuccess(false);
            response.setErrorCode(e.getErrorCode());
            response.setErrorMsg(e.getErrorMessage());
            return response;
        } catch (Throwable e) {
            response.setSuccess(false);
            String errorCode = BridgeErrorConstant.BaseError.UNKNOWN.getErrorCode();
            response.setErrorCode(errorCode);
            response.setErrorMsg(BridgeErrorConstant.BaseError.UNKNOWN.getErrorMsg());
            PickUpLogUtil.errLog(subscribeRequest.getMailNo(), PickUpConstants.Action.LOGISTICS_DETAIL_SUB_LINK.name(), errorCode, e.getMessage(), e);
            return response;
        } finally {
            LoggerMonitorUtil.end(response.isSuccess(), response.getErrorCode());
        }
    }

    private TmsWaybillLogisticsSubscribeResponse doLogisticsSubscribe(ReceiveParams<TmsWaybillLogisticsSubscribeRequest> subscribeRequest) throws BridgeBaseException {
        TmsWaybillLogisticsSubscribeRequest linkRequest = subscribeRequest.getRequestDataObject();

        WaybillLogisticsSubscribeRequest request = new WaybillLogisticsSubscribeRequest();
        request.setCpCode(linkRequest.getCpCode());
        request.setMailNo(linkRequest.getMailNo());
        request.setDeliveryAddress(linkRequest.getDeliveryAddress());
        request.setReceiverPhone(linkRequest.getReceiverPhone());
        request.setSenderPhone(linkRequest.getSenderPhone());
        request.setSenderAddress(linkRequest.getSenderAddress());

        SubscribeAppInfo subscribeAppInfo = new SubscribeAppInfo();
        subscribeAppInfo.setLinkAppKey(subscribeRequest.getFromAppkey());
        subscribeAppInfo.setLinkResCode(subscribeRequest.getCpCode());
        request.setSubscribeAppInfo(subscribeAppInfo);

        WaybillLogisticsSubscribeResponse response  = tmsWaybillLogisticDetailManager.subscribeLogisticsDetail(request);
        ResultDTO result = LogisticsDetailMapper.MAPPER.convertToResultDTO(response);

        TmsWaybillLogisticsSubscribeResponse ans = new TmsWaybillLogisticsSubscribeResponse();
        ans.setSuccess(true);
        ans.setResult(result);
        return ans;
    }

    /**
     * 参数校验
     *
     * @param request
     */
    private void checkParam(TmsWaybillLogisticsSubscribeRequest request) throws BridgeValidationException {
        if (StringUtils.isBlank(request.getCpCode())) {
            throw new BridgeValidationException("PARAM_NOT_NULL", "参数不能为空: cpCode");
        }

        if (StringUtils.isBlank(request.getMailNo())) {
            throw new BridgeValidationException("PARAM_NOT_NULL", "参数不能为空: mailNo");
        }
    }



}
