package com.cainiao.waybill.bridge.common.dto;

import java.io.Serializable;

import lombok.Data;

/**
 * 秒秒查物流状态对象
 * <AUTHOR>
 * @date 2024/6/26 11:37
 **/
@Data
public class MmcLogisticsInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 运单号
     */
    private String mailNo;

    /**
     * 物流状态
     * @see com.cainiao.waybill.bridge.biz.charity.constant.enums.mmc.MmcLogisticsStatusEnum
     */
    private String logisticsStatus;

    /**
     * 结构化话术
     */
    private String structDesc;

    /**
     * 包裹异常类型
     */
    private String exceptionTypeCode;

    /**
     * 包裹异常类型名称
     */
    private String exceptionTypeName;

    @Override
    public String toString() {
        return super.toString();
    }
}
