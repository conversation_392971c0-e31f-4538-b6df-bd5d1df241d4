package com.cainiao.waybill.bridge.common.label.service;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.label.dto.AccountBalanceAlarmDTO;
import com.cainiao.waybill.bridge.common.label.dto.AccountBalanceAlarmQueryDTO;
import com.cainiao.waybill.bridge.common.label.dto.response.AccountBalanceAlarmResponse;

/**
 * 面单余额设置service
 * 对外portal调用
 * <AUTHOR>
 * @since 2017/04/20
 */
public interface AccountBalanceAlarmService {
    /**
     * 配置余额告警
     * @param accountBalanceAlarmDTO
     * @return
     */
    BaseResultDTO<Void> saveAccountBalanceAlarm(AccountBalanceAlarmDTO accountBalanceAlarmDTO, ClientInfoDTO clientInfoDTO);

    /**
     * 查询配置的余额告警值
     * @param accountBalanceAlarmQueryDTO
     * @param clientInfoDTO
     * @return
     */
    BaseResultDTO<AccountBalanceAlarmResponse> getAccountBalanceAlarm(AccountBalanceAlarmQueryDTO accountBalanceAlarmQueryDTO, ClientInfoDTO clientInfoDTO);

}
