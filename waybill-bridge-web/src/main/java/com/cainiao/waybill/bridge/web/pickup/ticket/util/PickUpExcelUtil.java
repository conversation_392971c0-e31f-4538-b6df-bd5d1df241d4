package com.cainiao.waybill.bridge.web.pickup.ticket.util;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.cainiao.waybill.bridge.biz.charity.constant.CharityConstant;
import com.cainiao.waybill.bridge.biz.ticket.dto.PagingResponse;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpPagingBaseRequest;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelExportUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.web.common.util.ExeclUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> zouping.fzp
 * @Classname CharityExcelUtil
 * @Description
 * @Date 2022/9/6 5:09 下午
 * @Version 1.0
 */
@Slf4j(topic = "WAYBILL_PICKUP_INFO")
public class PickUpExcelUtil {

    /**
     * 默认导出上限
     */
    private static final int DEFAULT_EXPORT_LIMIT = 10000;

    /**
     * 导出(指定导出上限行数)
     * @param pagingBaseRequest
     * @param fileName
     * @param tClass
     * @param supplier
     * @param maxLimit
     * @return
     */
    public static <E, F, T extends PickUpPagingBaseRequest, R extends PagingResponse<F>> String exportFile(
        T pagingBaseRequest, String fileName, Class<E> tClass, Function<T, R> supplier, int maxLimit) {
        pagingBaseRequest.setCurrentPage(1);
        pagingBaseRequest.setPageSize(100);
        if(maxLimit < DEFAULT_EXPORT_LIMIT){
            maxLimit = DEFAULT_EXPORT_LIMIT;
        }
        int totalPage = 1;
        List<E> list = Lists.newArrayList();
        while (pagingBaseRequest.getCurrentPage() <= totalPage) {
            R r = supplier.apply(pagingBaseRequest);
            // 限制最大导出一万
            if (r.getPaging().getTotalCount() > maxLimit) {
                throw new BridgeBusinessException("export_num_over_limit", "导出数量超过"+maxLimit+"条，请缩小导出条件");
            }
            List<E> resultList = ListUtil.stream(r.getTableData()).map(x -> {
                try {
                    E e = tClass.newInstance();
                    BeanUtils.copyProperties(x, e);
                    return e;
                } catch (Exception exception) {
                    log.error("导出文件失败", exception);
                    throw new BridgeBusinessException("excel_export_exception", "文件导出异常");
                }
            }).collect(Collectors.toList());

            list.addAll(resultList);
            totalPage = r.getPaging().getTotalPage();
            pagingBaseRequest.setCurrentPage(pagingBaseRequest.getCurrentPage() + 1);
        }
        Workbook workbook = ExcelExportUtil.build(list, fileName, tClass);
        return ExeclUtil.exportToOSS(workbook, CharityConstant.OSS_TEMP_FILE_DIR);
    }

    /**
     * 导出(默认导出上限10000)
     * @param pagingBaseRequest
     * @param fileName
     * @param tClass
     * @param supplier
     * @return
     */
    public static <E, F, T extends PickUpPagingBaseRequest, R extends PagingResponse<F>> String exportFile(
        T pagingBaseRequest, String fileName, Class<E> tClass, Function<T, R> supplier) {
        return exportFile(pagingBaseRequest, fileName, tClass, supplier, DEFAULT_EXPORT_LIMIT);
    }
}
