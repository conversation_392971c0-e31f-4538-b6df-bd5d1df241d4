package com.cainiao.waybill.bridge.enterprise.administrators.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.ticket.dto.LogisticsDetailDTO;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.administrators.request.*;
import com.cainiao.waybill.bridge.enterprise.administrators.service.EnterpriseSettleAccountService;
import com.cainiao.waybill.bridge.enterprise.administrators.service.WaybillBridgeEnterpriseOrderService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeEnterpriseOrderDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseSettleAccountDTO;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.List;

/**
 * 寄件下单
 *
 * <AUTHOR>
 * @date 2025-04-23 17:23:47
 */
@RestController
@RequestMapping(value = "/enterprise/waybill")
public class EnterpriseOrderController {
    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private WaybillBridgeEnterpriseOrderService enterpriseOrderService;


    /**
     * 列表查询（分页）
     */
    @PostMapping("/send/list")
    public EnterpriseBaseResult<BridgePagingDTO<WaybillBridgeEnterpriseOrderDTO>> list(@RequestBody WaybillEnterpriseOrderQueryRequest request) {
        try {
            LOGGER.info("send list request:{}", JSONObject.toJSONString(request));
            BridgePagingDTO<WaybillBridgeEnterpriseOrderDTO> result = enterpriseOrderService.pageList(request);
            return EnterpriseBaseResult.success(result);
        } catch (EnterpriseException e) {
            LOGGER.error("enterprise waybill send list error, request:{}", JSON.toJSONString(request), e);
            return EnterpriseBaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        } catch (Exception e) {
            LOGGER.error("enterprise waybill send list error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    /**
     * 创建
     */
    @RequestMapping("/apply")
    public EnterpriseBaseResult<String> apply(@RequestBody WaybillEnterpriseOrderRequest request) {
        try {
            LOGGER.info("apply request :{}", JSONObject.toJSONString(request));
            String waybillCode = enterpriseOrderService.create(request);

            return EnterpriseBaseResult.success(waybillCode);
        } catch (EnterpriseException e) {
            LOGGER.error("enterprise waybill apply error:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        } catch (Exception e) {
            LOGGER.error("enterprise waybill apply exception: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    /**
     * 取消
     */
    @RequestMapping("/cancel")
    public EnterpriseBaseResult<String> cancel(@RequestBody WaybillEnterpriseOrderCancelRequest request) {
        try {
            LOGGER.info("cancel request:{}", JSONObject.toJSONString(request));
            String cancel = enterpriseOrderService.cancel(request);
            return EnterpriseBaseResult.success(cancel);
        } catch (EnterpriseException e) {
            LOGGER.error("enterprise waybill cancel error: {}",  ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        } catch (Throwable e) {
            LOGGER.error("enterprise waybill cancel throwable: {}",  ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }


    @GetMapping("/deleteById")
    EnterpriseBaseResult<Boolean> deleteById(@RequestParam("id") Long id) {
        int deleteResult = enterpriseOrderService.deleteOrderById(id);
        return EnterpriseBaseResult.success(deleteResult == 1);
    }

    @RequestMapping("/trace")
    EnterpriseBaseResult<List<LogisticsDetailDTO>> queryTrace(@RequestBody WaybillEnterpriseOrderTraceRequest request) {
        try{
            LOGGER.info("query waybill trace request:{}", JSONObject.toJSONString(request));
            List<LogisticsDetailDTO> logisticsDetailList = enterpriseOrderService.queryWaybillTrace(request);
            return EnterpriseBaseResult.success(logisticsDetailList);
        } catch (EnterpriseException e) {
            LOGGER.error("enterprise waybill query trace error :{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(e.getErrorCode(), e.getErrorMessage());
        } catch (Exception e) {
            LOGGER.error("enterprise waybill query trace exception :{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

}