package com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: yexin
 * @date: 2022-05-16 15:03
 **/
@Data
public class PlatformGotMainItem  implements Serializable {
    private static final long serialVersionUID = 1L;
    private String dateRange;
    private String platformName;
    private GotReportItem platformReport;
    List<PlatformGotSubCpItem> cpItemList;
}
