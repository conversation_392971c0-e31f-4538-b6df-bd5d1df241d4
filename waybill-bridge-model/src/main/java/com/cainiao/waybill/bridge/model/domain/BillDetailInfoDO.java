package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class BillDetailInfoDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   汇总账单ID
     *
     *
     * @mbg.generated
     */
    private String billSummaryId;

    /**
     * Database Column Remarks:
     *   账单月份
     *
     *
     * @mbg.generated
     */
    private String billMonth;

    /**
     * Database Column Remarks:
     *   运单号
     *
     *
     * @mbg.generated
     */
    private String mailNo;

    /**
     * Database Column Remarks:
     *   订单号
     *
     *
     * @mbg.generated
     */
    private String outOrderCode;

    /**
     * Database Column Remarks:
     *   账单类型
     *
     *
     * @mbg.generated
     */
    private String billType;

    /**
     * Database Column Remarks:
     *   结算类型
     *
     *
     * @mbg.generated
     */
    private String settlementType;

    /**
     * Database Column Remarks:
     *   订单创建时间
     *
     *
     * @mbg.generated
     */
    private Date orderCreateTime;

    /**
     * Database Column Remarks:
     *   订单揽收时间
     *
     *
     * @mbg.generated
     */
    private Date orderGotTime;

    /**
     * Database Column Remarks:
     *   订单计费时间
     *
     *
     * @mbg.generated
     */
    private Date orderChargeTime;

    /**
     * Database Column Remarks:
     *   出发省份
     *
     *
     * @mbg.generated
     */
    private String sendProv;

    /**
     * Database Column Remarks:
     *   出发城市
     *
     *
     * @mbg.generated
     */
    private String sendCity;

    /**
     * Database Column Remarks:
     *   出发区县
     *
     *
     * @mbg.generated
     */
    private String sendArea;

    /**
     * Database Column Remarks:
     *   到达省份
     *
     *
     * @mbg.generated
     */
    private String receiveProv;

    /**
     * Database Column Remarks:
     *   到达城市
     *
     *
     * @mbg.generated
     */
    private String receiveCity;

    /**
     * Database Column Remarks:
     *   到达区县
     *
     *
     * @mbg.generated
     */
    private String receiveArea;

    /**
     * Database Column Remarks:
     *   首重，单位克
     *
     *
     * @mbg.generated
     */
    private Integer firstWeight;

    /**
     * Database Column Remarks:
     *   续重，单位克
     *
     *
     * @mbg.generated
     */
    private Integer stepWeight;

    /**
     * Database Column Remarks:
     *   包裹重量，单位克
     *
     *
     * @mbg.generated
     */
    private Integer weight;

    /**
     * Database Column Remarks:
     *   供应链服务费，单位分
     *
     *
     * @mbg.generated
     */
    private Integer cpServiceFee;

    /**
     * Database Column Remarks:
     *   运费奖励费用，单位分
     *
     *
     * @mbg.generated
     */
    private Integer freightRewardFee;

    /**
     * Database Column Remarks:
     *   异常单理赔费用，单位分
     *
     *
     * @mbg.generated
     */
    private Integer abnormalClaimFee;

    /**
     * Database Column Remarks:
     *   用户权益费用，单位分
     *
     *
     * @mbg.generated
     */
    private Integer userRightsFee;

    /**
     * Database Column Remarks:
     *   3PL用户权益费用，单位分
     *
     *
     * @mbg.generated
     */
    private Integer pl3UserRightsFee;

    /**
     * Database Column Remarks:
     *   扩展字段
     *
     *
     * @mbg.generated
     */
    private String feature;

    /**
     *
     * @return the value of bill_detail_info.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for bill_detail_info.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of bill_detail_info.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for bill_detail_info.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of bill_detail_info.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for bill_detail_info.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of bill_detail_info.bill_summary_id
     *
     * @mbg.generated
     */
    public String getBillSummaryId() {
        return billSummaryId;
    }

    /**
     *
     * @param billSummaryId the value for bill_detail_info.bill_summary_id
     *
     * @mbg.generated
     */
    public void setBillSummaryId(String billSummaryId) {
        this.billSummaryId = billSummaryId;
    }

    /**
     *
     * @return the value of bill_detail_info.bill_month
     *
     * @mbg.generated
     */
    public String getBillMonth() {
        return billMonth;
    }

    /**
     *
     * @param billMonth the value for bill_detail_info.bill_month
     *
     * @mbg.generated
     */
    public void setBillMonth(String billMonth) {
        this.billMonth = billMonth;
    }

    /**
     *
     * @return the value of bill_detail_info.mail_no
     *
     * @mbg.generated
     */
    public String getMailNo() {
        return mailNo;
    }

    /**
     *
     * @param mailNo the value for bill_detail_info.mail_no
     *
     * @mbg.generated
     */
    public void setMailNo(String mailNo) {
        this.mailNo = mailNo;
    }

    /**
     *
     * @return the value of bill_detail_info.out_order_code
     *
     * @mbg.generated
     */
    public String getOutOrderCode() {
        return outOrderCode;
    }

    /**
     *
     * @param outOrderCode the value for bill_detail_info.out_order_code
     *
     * @mbg.generated
     */
    public void setOutOrderCode(String outOrderCode) {
        this.outOrderCode = outOrderCode;
    }

    /**
     *
     * @return the value of bill_detail_info.bill_type
     *
     * @mbg.generated
     */
    public String getBillType() {
        return billType;
    }

    /**
     *
     * @param billType the value for bill_detail_info.bill_type
     *
     * @mbg.generated
     */
    public void setBillType(String billType) {
        this.billType = billType;
    }

    /**
     *
     * @return the value of bill_detail_info.settlement_type
     *
     * @mbg.generated
     */
    public String getSettlementType() {
        return settlementType;
    }

    /**
     *
     * @param settlementType the value for bill_detail_info.settlement_type
     *
     * @mbg.generated
     */
    public void setSettlementType(String settlementType) {
        this.settlementType = settlementType;
    }

    /**
     *
     * @return the value of bill_detail_info.order_create_time
     *
     * @mbg.generated
     */
    public Date getOrderCreateTime() {
        return orderCreateTime;
    }

    /**
     *
     * @param orderCreateTime the value for bill_detail_info.order_create_time
     *
     * @mbg.generated
     */
    public void setOrderCreateTime(Date orderCreateTime) {
        this.orderCreateTime = orderCreateTime;
    }

    /**
     *
     * @return the value of bill_detail_info.order_got_time
     *
     * @mbg.generated
     */
    public Date getOrderGotTime() {
        return orderGotTime;
    }

    /**
     *
     * @param orderGotTime the value for bill_detail_info.order_got_time
     *
     * @mbg.generated
     */
    public void setOrderGotTime(Date orderGotTime) {
        this.orderGotTime = orderGotTime;
    }

    /**
     *
     * @return the value of bill_detail_info.order_charge_time
     *
     * @mbg.generated
     */
    public Date getOrderChargeTime() {
        return orderChargeTime;
    }

    /**
     *
     * @param orderChargeTime the value for bill_detail_info.order_charge_time
     *
     * @mbg.generated
     */
    public void setOrderChargeTime(Date orderChargeTime) {
        this.orderChargeTime = orderChargeTime;
    }

    /**
     *
     * @return the value of bill_detail_info.send_prov
     *
     * @mbg.generated
     */
    public String getSendProv() {
        return sendProv;
    }

    /**
     *
     * @param sendProv the value for bill_detail_info.send_prov
     *
     * @mbg.generated
     */
    public void setSendProv(String sendProv) {
        this.sendProv = sendProv;
    }

    /**
     *
     * @return the value of bill_detail_info.send_city
     *
     * @mbg.generated
     */
    public String getSendCity() {
        return sendCity;
    }

    /**
     *
     * @param sendCity the value for bill_detail_info.send_city
     *
     * @mbg.generated
     */
    public void setSendCity(String sendCity) {
        this.sendCity = sendCity;
    }

    /**
     *
     * @return the value of bill_detail_info.send_area
     *
     * @mbg.generated
     */
    public String getSendArea() {
        return sendArea;
    }

    /**
     *
     * @param sendArea the value for bill_detail_info.send_area
     *
     * @mbg.generated
     */
    public void setSendArea(String sendArea) {
        this.sendArea = sendArea;
    }

    /**
     *
     * @return the value of bill_detail_info.receive_prov
     *
     * @mbg.generated
     */
    public String getReceiveProv() {
        return receiveProv;
    }

    /**
     *
     * @param receiveProv the value for bill_detail_info.receive_prov
     *
     * @mbg.generated
     */
    public void setReceiveProv(String receiveProv) {
        this.receiveProv = receiveProv;
    }

    /**
     *
     * @return the value of bill_detail_info.receive_city
     *
     * @mbg.generated
     */
    public String getReceiveCity() {
        return receiveCity;
    }

    /**
     *
     * @param receiveCity the value for bill_detail_info.receive_city
     *
     * @mbg.generated
     */
    public void setReceiveCity(String receiveCity) {
        this.receiveCity = receiveCity;
    }

    /**
     *
     * @return the value of bill_detail_info.receive_area
     *
     * @mbg.generated
     */
    public String getReceiveArea() {
        return receiveArea;
    }

    /**
     *
     * @param receiveArea the value for bill_detail_info.receive_area
     *
     * @mbg.generated
     */
    public void setReceiveArea(String receiveArea) {
        this.receiveArea = receiveArea;
    }

    /**
     *
     * @return the value of bill_detail_info.first_weight
     *
     * @mbg.generated
     */
    public Integer getFirstWeight() {
        return firstWeight;
    }

    /**
     *
     * @param firstWeight the value for bill_detail_info.first_weight
     *
     * @mbg.generated
     */
    public void setFirstWeight(Integer firstWeight) {
        this.firstWeight = firstWeight;
    }

    /**
     *
     * @return the value of bill_detail_info.step_weight
     *
     * @mbg.generated
     */
    public Integer getStepWeight() {
        return stepWeight;
    }

    /**
     *
     * @param stepWeight the value for bill_detail_info.step_weight
     *
     * @mbg.generated
     */
    public void setStepWeight(Integer stepWeight) {
        this.stepWeight = stepWeight;
    }

    /**
     *
     * @return the value of bill_detail_info.weight
     *
     * @mbg.generated
     */
    public Integer getWeight() {
        return weight;
    }

    /**
     *
     * @param weight the value for bill_detail_info.weight
     *
     * @mbg.generated
     */
    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    /**
     *
     * @return the value of bill_detail_info.cp_service_fee
     *
     * @mbg.generated
     */
    public Integer getCpServiceFee() {
        return cpServiceFee;
    }

    /**
     *
     * @param cpServiceFee the value for bill_detail_info.cp_service_fee
     *
     * @mbg.generated
     */
    public void setCpServiceFee(Integer cpServiceFee) {
        this.cpServiceFee = cpServiceFee;
    }

    /**
     *
     * @return the value of bill_detail_info.freight_reward_fee
     *
     * @mbg.generated
     */
    public Integer getFreightRewardFee() {
        return freightRewardFee;
    }

    /**
     *
     * @param freightRewardFee the value for bill_detail_info.freight_reward_fee
     *
     * @mbg.generated
     */
    public void setFreightRewardFee(Integer freightRewardFee) {
        this.freightRewardFee = freightRewardFee;
    }

    /**
     *
     * @return the value of bill_detail_info.abnormal_claim_fee
     *
     * @mbg.generated
     */
    public Integer getAbnormalClaimFee() {
        return abnormalClaimFee;
    }

    /**
     *
     * @param abnormalClaimFee the value for bill_detail_info.abnormal_claim_fee
     *
     * @mbg.generated
     */
    public void setAbnormalClaimFee(Integer abnormalClaimFee) {
        this.abnormalClaimFee = abnormalClaimFee;
    }

    /**
     *
     * @return the value of bill_detail_info.user_rights_fee
     *
     * @mbg.generated
     */
    public Integer getUserRightsFee() {
        return userRightsFee;
    }

    /**
     *
     * @param userRightsFee the value for bill_detail_info.user_rights_fee
     *
     * @mbg.generated
     */
    public void setUserRightsFee(Integer userRightsFee) {
        this.userRightsFee = userRightsFee;
    }

    /**
     *
     * @return the value of bill_detail_info.pl3_user_rights_fee
     *
     * @mbg.generated
     */
    public Integer getPl3UserRightsFee() {
        return pl3UserRightsFee;
    }

    /**
     *
     * @param pl3UserRightsFee the value for bill_detail_info.pl3_user_rights_fee
     *
     * @mbg.generated
     */
    public void setPl3UserRightsFee(Integer pl3UserRightsFee) {
        this.pl3UserRightsFee = pl3UserRightsFee;
    }

    /**
     *
     * @return the value of bill_detail_info.feature
     *
     * @mbg.generated
     */
    public String getFeature() {
        return feature;
    }

    /**
     *
     * @param feature the value for bill_detail_info.feature
     *
     * @mbg.generated
     */
    public void setFeature(String feature) {
        this.feature = feature;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", billSummaryId=").append(billSummaryId);
        sb.append(", billMonth=").append(billMonth);
        sb.append(", mailNo=").append(mailNo);
        sb.append(", outOrderCode=").append(outOrderCode);
        sb.append(", billType=").append(billType);
        sb.append(", settlementType=").append(settlementType);
        sb.append(", orderCreateTime=").append(orderCreateTime);
        sb.append(", orderGotTime=").append(orderGotTime);
        sb.append(", orderChargeTime=").append(orderChargeTime);
        sb.append(", sendProv=").append(sendProv);
        sb.append(", sendCity=").append(sendCity);
        sb.append(", sendArea=").append(sendArea);
        sb.append(", receiveProv=").append(receiveProv);
        sb.append(", receiveCity=").append(receiveCity);
        sb.append(", receiveArea=").append(receiveArea);
        sb.append(", firstWeight=").append(firstWeight);
        sb.append(", stepWeight=").append(stepWeight);
        sb.append(", weight=").append(weight);
        sb.append(", cpServiceFee=").append(cpServiceFee);
        sb.append(", freightRewardFee=").append(freightRewardFee);
        sb.append(", abnormalClaimFee=").append(abnormalClaimFee);
        sb.append(", userRightsFee=").append(userRightsFee);
        sb.append(", pl3UserRightsFee=").append(pl3UserRightsFee);
        sb.append(", feature=").append(feature);
        sb.append("]");
        return sb.toString();
    }
}