package com.cainiao.waybill.bridge.enterprise.route.handler.impl;

import com.alibaba.fastjson.JSONObject;
import com.cainiao.waybill.bridge.enterprise.administrators.service.WaybillBridgeEnterpriseOrderService;
import com.cainiao.waybill.bridge.enterprise.common.enums.ExpressTypeEnum;
import com.cainiao.waybill.bridge.enterprise.route.handler.ContractInformationBuild;
import com.cainiao.waybill.bridge.enterprise.route.handler.ContractInformationBuildMethod;
import com.cainiao.waybill.bridge.enterprise.utils.AddressUtils;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseOrderDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/7
 **/
@Component
@ContractInformationBuildMethod(expressType = ExpressTypeEnum.EMS)
public class EMSContractInformation implements ContractInformationBuild {

    @Autowired
    WaybillBridgeEnterpriseOrderService waybillBridgeEnterpriseOrderService;

//    @Override
//    public String build(WaybillBridgeEnterpriseOrderDO enterpriseOrderDO) {
//        JSONObject jsonObject = new JSONObject();
//
//        JSONObject recipient = new JSONObject();
//        JSONObject recipientAddress = new JSONObject();
//        recipientAddress.put("city", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getCity());
//        recipientAddress.put("detail", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getAddressDetail());
//        recipientAddress.put("district", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getArea());
//        recipientAddress.put("province", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getProvince());
//        recipientAddress.put("town", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getTown());
//        recipient.put("address", recipientAddress);
//        recipient.put("mobile", enterpriseOrderDO.getConsigneeMobile());
//        recipient.put("name", enterpriseOrderDO.getConsigneeName());
//        recipient.put("phone", enterpriseOrderDO.getConsigneePhone());
//        jsonObject.put("recipient", recipient);
//
//        JSONObject sender = new JSONObject();
//        JSONObject sendAddress = new JSONObject();
//        sendAddress.put("city", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getCity());
//        sendAddress.put("detail", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getAddressDetail());
//        sendAddress.put("district", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getArea());
//        sendAddress.put("province", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getProvince());
//        sendAddress.put("town", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getTown());
//        sender.put("address", sendAddress);
//
//        sender.put("mobile", enterpriseOrderDO.getSenderMobile());
//        sender.put("name", enterpriseOrderDO.getSenderName());
//        sender.put("phone", enterpriseOrderDO.getSenderPhone());
//        jsonObject.put("sender", sender);
//        jsonObject.put("waybillCode", enterpriseOrderDO.getWaybillCode());
//
//        return jsonObject.toString();
//    }

    @Override
    public String build(WaybillBridgeEnterpriseOrderDO enterpriseOrderDO) {
        return waybillBridgeEnterpriseOrderService.queryEMSPrintData(enterpriseOrderDO, enterpriseOrderDO.getWaybillCode());
    }
}
