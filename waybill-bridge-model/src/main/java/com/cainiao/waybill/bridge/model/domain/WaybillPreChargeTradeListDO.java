package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class WaybillPreChargeTradeListDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   菜鸟会员账号ID
     *
     *
     * @mbg.generated
     */
    private String userId;

    /**
     * Database Column Remarks:
     *   客户名称
     *
     *
     * @mbg.generated
     */
    private String userName;

    /**
     * Database Column Remarks:
     *   交易单号
     *
     *
     * @mbg.generated
     */
    private String tradeNo;

    /**
     * Database Column Remarks:
     *   交易类型-扣款pay/调账recharge/汇总collect
     *
     *
     * @mbg.generated
     */
    private String tradeType;

    /**
     * Database Column Remarks:
     *   交易金额(分)
     *
     *
     * @mbg.generated
     */
    private Integer tradeAmount;

    /**
     * Database Column Remarks:
     *   交易时间
     *
     *
     * @mbg.generated
     */
    private Date tradeTime;

    /**
     * Database Column Remarks:
     *   余额(分)
     *
     *
     * @mbg.generated
     */
    private Integer balance;

    /**
     * Database Column Remarks:
     *   扩展字段
     *
     *
     * @mbg.generated
     */
    private String feature;

    /**
     *
     * @return the value of waybill_pre_charge_trade_list.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_pre_charge_trade_list.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_pre_charge_trade_list.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_pre_charge_trade_list.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_pre_charge_trade_list.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_pre_charge_trade_list.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_pre_charge_trade_list.user_id
     *
     * @mbg.generated
     */
    public String getUserId() {
        return userId;
    }

    /**
     *
     * @param userId the value for waybill_pre_charge_trade_list.user_id
     *
     * @mbg.generated
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     *
     * @return the value of waybill_pre_charge_trade_list.user_name
     *
     * @mbg.generated
     */
    public String getUserName() {
        return userName;
    }

    /**
     *
     * @param userName the value for waybill_pre_charge_trade_list.user_name
     *
     * @mbg.generated
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     *
     * @return the value of waybill_pre_charge_trade_list.trade_no
     *
     * @mbg.generated
     */
    public String getTradeNo() {
        return tradeNo;
    }

    /**
     *
     * @param tradeNo the value for waybill_pre_charge_trade_list.trade_no
     *
     * @mbg.generated
     */
    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    /**
     *
     * @return the value of waybill_pre_charge_trade_list.trade_type
     *
     * @mbg.generated
     */
    public String getTradeType() {
        return tradeType;
    }

    /**
     *
     * @param tradeType the value for waybill_pre_charge_trade_list.trade_type
     *
     * @mbg.generated
     */
    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    /**
     *
     * @return the value of waybill_pre_charge_trade_list.trade_amount
     *
     * @mbg.generated
     */
    public Integer getTradeAmount() {
        return tradeAmount;
    }

    /**
     *
     * @param tradeAmount the value for waybill_pre_charge_trade_list.trade_amount
     *
     * @mbg.generated
     */
    public void setTradeAmount(Integer tradeAmount) {
        this.tradeAmount = tradeAmount;
    }

    /**
     *
     * @return the value of waybill_pre_charge_trade_list.trade_time
     *
     * @mbg.generated
     */
    public Date getTradeTime() {
        return tradeTime;
    }

    /**
     *
     * @param tradeTime the value for waybill_pre_charge_trade_list.trade_time
     *
     * @mbg.generated
     */
    public void setTradeTime(Date tradeTime) {
        this.tradeTime = tradeTime;
    }

    /**
     *
     * @return the value of waybill_pre_charge_trade_list.balance
     *
     * @mbg.generated
     */
    public Integer getBalance() {
        return balance;
    }

    /**
     *
     * @param balance the value for waybill_pre_charge_trade_list.balance
     *
     * @mbg.generated
     */
    public void setBalance(Integer balance) {
        this.balance = balance;
    }

    /**
     *
     * @return the value of waybill_pre_charge_trade_list.feature
     *
     * @mbg.generated
     */
    public String getFeature() {
        return feature;
    }

    /**
     *
     * @param feature the value for waybill_pre_charge_trade_list.feature
     *
     * @mbg.generated
     */
    public void setFeature(String feature) {
        this.feature = feature;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", userId=").append(userId);
        sb.append(", userName=").append(userName);
        sb.append(", tradeNo=").append(tradeNo);
        sb.append(", tradeType=").append(tradeType);
        sb.append(", tradeAmount=").append(tradeAmount);
        sb.append(", tradeTime=").append(tradeTime);
        sb.append(", balance=").append(balance);
        sb.append(", feature=").append(feature);
        sb.append("]");
        return sb.toString();
    }
}