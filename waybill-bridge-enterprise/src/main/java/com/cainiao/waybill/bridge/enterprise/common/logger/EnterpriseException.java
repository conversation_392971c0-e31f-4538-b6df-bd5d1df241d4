package com.cainiao.waybill.bridge.enterprise.common.logger;

import com.cainiao.waybill.bridge.common.base.Constants.ErrorConstant;
import com.taobao.cainiao.waybill.base.Constants;

/**
 * Created by nut on 16/2/23.
 * 业务操作异常,是业务操作中捕获到异常后(如IOException,OtherSystemException等),或者调用外部接口拿到errorCode,errorMessage后抛出的.
 */
public class EnterpriseException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    protected final String    errorMessage;
    protected final String    errorCode;

    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }



    /**
     * 注意,该构造函数是调用第三方接口返回ERROR_CODE,ERROR_MESSAGE后抛出的,用于代表第三方接口异常.
     *
     * @param errorCode
     * @param errorMessage
     */
    public EnterpriseException(String errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }


}
