package com.cainiao.waybill.bridge.common.label.validator;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.label.dto.request.UpdateSettlementTypeRequest;
import com.cainiao.waybill.bridge.common.validator.BridgeBaseValidator;
import com.cainiao.waybill.bridge.common.label.dto.request.BranchSellerAddRelQueryRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.CreateBranchSellerAddRelRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.ReplaceCourierRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.UnbindCourierRequest;
import org.apache.commons.lang.StringUtils;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-28
 */
public class WaybillBranchSellerAddRelValidator extends BridgeBaseValidator {

	public static void validateQueryForQiuNiuRequest(String cpCode, String qrCodeStr, ClientInfoDTO clientAppInfo)
            throws BridgeValidationException {

        validate(StringUtils.isNotBlank(cpCode), "cpCode 不能为空");
        validate(StringUtils.isNotBlank(qrCodeStr), "qrCodeStr 不能为空");

        validateClientDTO(clientAppInfo);
    }
	
    public static void validateQueryForBranchRequest(BranchSellerAddRelQueryRequest request, ClientInfoDTO clientAppInfo)
            throws BridgeValidationException {

        validate(request != null, "request 不能为空");
        validate(StringUtils.isNotBlank(request.getCpCode()), "cpCode 不能为空");
        validate(StringUtils.isNotBlank(request.getBranchCode()), "branchCode 不能为空");

        validateClientDTO(clientAppInfo);
    }

    public static void validateQueryForSellerRequest(Long sellerId, ClientInfoDTO clientAppInfo)
            throws BridgeValidationException {

        validate(sellerId != null, "sellerId 不能为空");

        validateClientDTO(clientAppInfo);
    }

    public static void validateUnbindCourierRequest(UnbindCourierRequest request, ClientInfoDTO clientAppInfo)
            throws BridgeValidationException {

        validate(request != null, "request 不能为空");
        validate(StringUtils.isNotBlank(request.getCpCode()), "cpCode 不能为空");
        validate(StringUtils.isNotBlank(request.getBranchCode()), "branchCode 不能为空");
        validate(request.getSellerId() != null, "sellerId 不能为空");
        validate(request.getId() != null || request.getSellerAddress() != null, "id 和 address 至少一个不为空");

        validateClientDTO(clientAppInfo);
    }

    public static void validateReplaceCourierRequest(ReplaceCourierRequest request, ClientInfoDTO clientAppInfo)
            throws BridgeValidationException {

        validateClientDTO(clientAppInfo);

        validate(request != null, "request 不能为空");
        validate(StringUtils.isNotBlank(request.getCpCode()), "cpCode 不能为空");
        validate(StringUtils.isNotBlank(request.getBranchCode()), "branchCode 不能为空");
        validate(request.getSellerId() != null, "sellerId 不能为空");
        validate(request.getNewCourierId() != null, "newCourierId 不能为空");
        validate(request.getId() != null || request.getSellerAddress() != null, "id 和 address 至少一个不为空");
    }


    public static void validateUpdateSettlementTypeRequest(UpdateSettlementTypeRequest request, ClientInfoDTO clientAppInfo)
            throws BridgeValidationException {

        validateClientDTO(clientAppInfo);

        validate(request != null, "request 不能为空");
        validate(StringUtils.isNotBlank(request.getCpCode()), "cpCode 不能为空");
        validate(StringUtils.isNotBlank(request.getBranchCode()), "branchCode 不能为空");
        validate(request.getSellerId() != null, "sellerId 不能为空");
        validate(request.getSettlementType() != null, "settlementType 不能为空");
    }

    public static void validateCreateRequest(CreateBranchSellerAddRelRequest request, ClientInfoDTO clientAppInfo)
            throws BridgeValidationException {

        validate(request != null, "request 不能为空");
        validate(StringUtils.isNotBlank(request.getCpCode()), "cpCode 不能为空");
        validate(StringUtils.isNotBlank(request.getBranchCode()), "branchCode 不能为空");
        validate(request.getSellerId() != null, "sellerId 不能为空");
        validate(StringUtils.isNotBlank(request.getSellerName()), "sellerName 不能为空");
        validate(request.getCourierId() != null, "courierId 不能为空");
        validate(request.getSellerAddress() != null, "address 不能为空");

        validateClientDTO(clientAppInfo);
    }

    private static void validateClientDTO(ClientInfoDTO clientInfoDTO) throws BridgeValidationException {
        // clientInfoDTO 校验
        validate(clientInfoDTO != null, "clientInfoDTO不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getAppName()), "clientInfoDTO.appName不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getOperator()), "clientInfoDTO.operator不能为空");
    }
}
