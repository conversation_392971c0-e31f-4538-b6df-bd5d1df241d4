package com.cainiao.waybill.bridge.web.pickup.algorithm;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import com.ali.com.google.common.collect.Lists;
import com.ali.unit.rule.util.lang.CollectionUtils;
import com.cainiao.waybill.bridge.biz.common.config.AlgorithmCalcThreadPoolExecutorConfig;
import com.cainiao.waybill.bridge.biz.common.user.WaybillBridgeUserInfoService;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.config.PickUpPlatformConfigDiamond;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.AlgorithmKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpCpBizTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.algorithm.AlgorithmCalcResultEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.algorithm.AlgorithmCalcStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.algorithm.AlgorithmStatusEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.algorithm.ConstraintRangeEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.algorithm.ConstraintTypeEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.algorithm.TargetConstraintEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.algorithm.TargetDimensionEnum;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.AlgorithmCalcRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.AlgorithmConstraint;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.AlgorithmDetail;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.AlgorithmObjective;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.AlgorithmRequirement;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.PickUpAlgorithmApproveResponse;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.PickUpAlgorithmCreateRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.PickUpAlgorithmCreateResponse;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.PickUpAlgorithmDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.PickUpAlgorithmPublishRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.PickUpAlgorithmRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.PickUpAlgorithmRollCheckRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.algorithm.PickUpAlgorithmRollCheckResponse;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpCpConfigInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpPairDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpPlatConfig;
import com.cainiao.waybill.bridge.biz.pickup.service.PickUpAlgorithmService;
import com.cainiao.waybill.bridge.biz.ticket.dto.PagingResponse;
import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpCommonQueryConstantRequest;
import com.cainiao.waybill.bridge.biz.utils.AlgorithmUtils;
import com.cainiao.waybill.bridge.biz.utils.AlgorithmUtils.CalcResult;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.biz.utils.OrderSourceMapUtil;
import com.cainiao.waybill.bridge.biz.utils.cp.CpAgentUtils;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.wrapper.TairManagerWrapper;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.model.domain.PickUpAlgorithmConstraintConfigDO;
import com.cainiao.waybill.bridge.model.domain.PickUpAlgorithmSchemeDO;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeUserInfoDTO;
import com.cainiao.waybill.bridge.web.charity.controller.BaseController;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.pickup.constant.TicketResourceEnum;
import com.cainiao.waybill.bridge.web.pickup.ticket.role.PickUpResourceAuthority;
import com.cainiao.waybill.bridge.web.pickup.ticket.util.PickUpExcelUtil;
import com.cainiao.waybill.common.util.FeatureUtils;
import com.google.common.collect.Maps;
import groovy.util.logging.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 算法方案
 * <AUTHOR>
 * @date 2024/10/30 11:08
 **/
@Slf4j
@RestController
public class AlgorithmController extends BaseController {

    @Resource
    private PickUpAlgorithmService pickUpAlgorithmService;

    @Resource
    private TairManagerWrapper tairManagerWrapper;

    @Autowired
    private WaybillBridgeUserInfoService waybillBridgeUserInfoService;

    /**
     * 分页查询算法方案列表
     * @param request
     * @return
     */
    @RequestMapping("/pickup/algorithm/pageList")
    @PickUpResourceAuthority(TicketResourceEnum.ALGORITHM_MANAGER)
    public BaseResult<PagingResponse<PickUpAlgorithmDTO>> queryAlgorithmPageList(@RequestBody
    PickUpAlgorithmRequest request) {

        try {
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            PagingResponse<PickUpAlgorithmDTO> pageResult = pickUpAlgorithmService.queryAlgorithmByPage(request);

            return BaseResult.success(pageResult);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("查询算法方案列表失败:" + JSONObject.toJSONString(request),
                PickUpConstants.Action.ALGORITHM_PAGE_LIST_ERROR.name(),
                e.getErrorCode(), e.getErrorMessage());
            return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_PAGE_LIST_ERROR);
        }catch (Exception e){
            PickUpLogUtil.errLog("查询算法方案列表失败:" + JSONObject.toJSONString(request),
                PickUpConstants.Action.ALGORITHM_PAGE_LIST_ERROR.name(), "SYSTEM_ERROR",
                ExceptionUtils.getFullStackTrace(e));
            return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_PAGE_LIST_ERROR);
        }

    }

    /**
     * 查询算法方案详情
     * @param request
     * @return
     */
    @RequestMapping("/pickup/algorithm/detail")
    @PickUpResourceAuthority(TicketResourceEnum.ALGORITHM_MANAGER)
    public BaseResult<PickUpAlgorithmDTO> queryAlgorithmDetail(
        @RequestBody PickUpAlgorithmRequest request){

        try {
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            BridgeValidator.validate(request);
            // 前端传参id实际为calcKey
            PickUpAlgorithmSchemeDO algorithmSchemeDO = pickUpAlgorithmService.queryAlgorithmDetailByCalcKey(request.getId().toString());
            if (algorithmSchemeDO == null) {
                return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_NOT_EXIST_ERROR);
            }

            PickUpAlgorithmDTO algorithmDTO = new PickUpAlgorithmDTO();
            BeanUtils.copyProperties(algorithmSchemeDO, algorithmDTO);
            algorithmDTO.setTargetConstraint(algorithmSchemeDO.getTargetDonstraint());
            String cpList = algorithmDTO.getAvailableCpList();
            algorithmDTO.setAvailableCpList(cpList);

            // 查询算法约束
            List<PickUpAlgorithmConstraintConfigDO> configList
                = pickUpAlgorithmService.queryAlgorithmConstraintConfigList(Long.parseLong(algorithmSchemeDO.getCalcKey()));
            List<AlgorithmRequirement> requirementList = getAlgorithmRequirements(configList);
            algorithmDTO.setRequirementList(requirementList);
            // 查询历史停用方案
            List<PickUpAlgorithmSchemeDO> historyList = pickUpAlgorithmService.queryHsitoryAlgorithmList(
                algorithmSchemeDO.getOrderChannels());
            Map<String,Long> historyMap = new HashMap<>(16);
            for(PickUpAlgorithmSchemeDO history : historyList){
                historyMap.put(history.getName(), Long.parseLong(history.getCalcKey()));
            }
            algorithmDTO.setHistoryMap(historyMap);

            return BaseResult.success(algorithmDTO);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("查询算法方案详情失败:" + JSONObject.toJSONString(request),
                PickUpConstants.Action.ALGORITHM_QUERY_DETAIL_ERROR.name(),
                e.getErrorCode(), e.getErrorMessage());
            return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_QUERY_DETAIL_ERROR);
        }catch (Exception e){
            PickUpLogUtil.errLog("查询算法方案详情失败:" + JSONObject.toJSONString(request),
                PickUpConstants.Action.ALGORITHM_QUERY_DETAIL_ERROR.name(), "SYSTEM_ERROR",
                ExceptionUtils.getFullStackTrace(e));
            return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_QUERY_DETAIL_ERROR);
        }

    }

    @NotNull
    private static List<AlgorithmRequirement> getAlgorithmRequirements(
        List<PickUpAlgorithmConstraintConfigDO> configList) {
        List<AlgorithmRequirement> requirementList = new ArrayList<>();

        for(PickUpAlgorithmConstraintConfigDO config : configList){
            AlgorithmRequirement requirement = new AlgorithmRequirement();
            requirement.setBusinessTarget(config.getBusinessTarget());
            requirement.setConstraintType(config.getConstraintType());
            requirement.setConstraintRange(config.getConstraintScope());
            requirement.setConstraintValue(config.getConstraintValue());
            requirementList.add(requirement);
        }
        return requirementList;
    }

    /**
     * 测算算法
     * 如果是未保存的，则先保存记录并执行测算
     * @param request
     * @return
     */
    @RequestMapping("/pickup/algorithm/calculate")
    @PickUpResourceAuthority(TicketResourceEnum.ALGORITHM_MANAGER)
    public BaseResult<PickUpAlgorithmCreateResponse> calculateAlgorithm(@RequestBody PickUpAlgorithmCreateRequest request){
        PickUpAlgorithmCreateResponse createResponse = new PickUpAlgorithmCreateResponse();
        try {
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            PickUpLogUtil.info("发起测算任务。request:{}", JSONObject.toJSONString(request));
            PickUpAlgorithmRequest queryRequest = new PickUpAlgorithmRequest();
            queryRequest.setOrderChannels(request.getOrderChannels());
            queryRequest.setName(request.getName());
            // 当前方案版本号
            int currModelVersion = AlgorithmKey.INIT_MODEL_VERSION;
            PickUpAlgorithmSchemeDO schemeDO = pickUpAlgorithmService.queryFirstByNameAndChannel(queryRequest);
            if(request.getId() == null && schemeDO == null){
                createResponse = pickUpAlgorithmService.createAlgorithm(request);
                if (createResponse == null || StringUtils.isBlank(createResponse.getCalcKey())){
                    return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_CREATE_ERROR);
                }
            }else if(schemeDO != null){
                if(StringUtils.equals(schemeDO.getStatus(), AlgorithmStatusEnum.ENABLE.getStatus())){
                    return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_ENABLE_FORBID_CALC_ERROR);
                }
                request.setId(schemeDO.getId());
                // 已有方案版本号加1
                currModelVersion = parseCurrentModelVersion(schemeDO) + 1;
                request.setModelVersion(currModelVersion);
                PickUpAlgorithmDTO updateDTO = pickUpAlgorithmService.updateAlgorithm(request);
                if(updateDTO == null){
                    return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_NOT_EXIST_ERROR);
                }
                createResponse.setCalcKey(schemeDO.getCalcKey());
            }else {
                // 前端传值id实际为calcKey
                schemeDO = pickUpAlgorithmService.queryAlgorithmDetailByCalcKey(request.getId()+"");
                if(schemeDO == null || StringUtils.isBlank(schemeDO.getCalcKey())){
                    return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_NOT_EXIST_ERROR);
                }
                if(StringUtils.equals(schemeDO.getStatus(), AlgorithmStatusEnum.ENABLE.getStatus())){
                    return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_ENABLE_FORBID_CALC_ERROR);
                }
                // 已有方案版本号加1
                currModelVersion = parseCurrentModelVersion(schemeDO) + 1;
                request.setModelVersion(currModelVersion);
                request.setId(schemeDO.getId());
                PickUpAlgorithmDTO updateDTO = pickUpAlgorithmService.updateAlgorithm(request);
                if(updateDTO == null){
                    return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_NOT_EXIST_ERROR);
                }
                createResponse.setCalcKey(schemeDO.getCalcKey());
            }
            String calcKey = createResponse.getCalcKey();
            PickUpLogUtil.info("测算任务缓存。calcKey:{}", calcKey);
            // 异步执行测算
            asyncExecuteCalc(calcKey, request, currModelVersion);

            // 缓存测算状态
            tairManagerWrapper.put(calcKey + AlgorithmKey.ALGORITHM_CALC_SUFFIX, AlgorithmCalcStatusEnum.CALCULATING.getCode(), 0);

            createResponse.setCalcStatus(AlgorithmCalcStatusEnum.CALCULATING.getCode());
            createResponse.setCalcStatusDesc(AlgorithmCalcStatusEnum.CALCULATING.getDesc());

            return BaseResult.success(createResponse);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("算法方案测算失败:" + request.getOrderChannels()+"_"+request.getName(),
                PickUpConstants.Action.ALGORITHM_CALC_ERROR.name(),
                e.getErrorCode(), e.getErrorMessage());
            return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_CALC_ERROR);
        }catch (Exception e){
            PickUpLogUtil.errLog("算法方案测算失败:" + request.getOrderChannels()+"_"+request.getName(),
                PickUpConstants.Action.ALGORITHM_CALC_ERROR.name(), "SYSTEM_ERROR",
                ExceptionUtils.getFullStackTrace(e));
            return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_CALC_ERROR);
        }
    }

    /**
     *  解析当前模型版本 默认1
     * @param schemeDO
     * @return
     */
    private int parseCurrentModelVersion(PickUpAlgorithmSchemeDO schemeDO) {
        Map<String, String> extInfoMap = FeatureUtils.parseFromString(schemeDO.getExtInfo());
        String version = extInfoMap.get(PickUpConstants.AlgorithmKey.CURRENT_MODEL_VERSION);
        if(StringUtils.isBlank(version)){
            return 1;
        }
        try{
            return Integer.parseInt(version);
        }catch (Exception e){
            return 1;
        }
    }

    /**
     * 异步测算并缓存测算结果
     * @param calcKey
     * @param request
     * @param modelVersion
     */
    private void asyncExecuteCalc(String calcKey, PickUpAlgorithmCreateRequest request, int modelVersion) {
        // build request
        AlgorithmCalcThreadPoolExecutorConfig.getInstance().submit(() -> {

            // 执行算法测算
            CalcResult calcResult = AlgorithmUtils.executeCalc(calcKey, request, modelVersion);

            String calcStatus = AlgorithmCalcStatusEnum.CALCULATE_FAIL.getCode();
            String calcResultDesc = AlgorithmCalcResultEnum.INIT.getCode();
            String calcResultDigest = "";
            // 缓存测算结果
            if(calcResult.isSuccess()){
                if(calcResult.getResult().contains(AlgorithmKey.NO_MATCH_CALC_RESULT_DESC)){
                    calcResultDesc = AlgorithmCalcResultEnum.NO_MATCH.getCode();
                    calcResultDigest = AlgorithmKey.NO_MATCH_CALC_RESULT_DESC;
                }else {
                    calcResultDesc = AlgorithmCalcResultEnum.MATCH.getCode();
                    calcResultDigest = calcResult.getResult();
                }

                // cache 缓存测算结果
                tairManagerWrapper.put(calcKey + AlgorithmKey.ALGORITHM_CALC_SUFFIX, AlgorithmCalcStatusEnum.CALCULATE_COMPLETE.getCode(), 0);
                calcStatus = AlgorithmCalcStatusEnum.CALCULATE_COMPLETE.getCode();
            }else {
                // cache 缓存测算失败
                tairManagerWrapper.put(calcKey + AlgorithmKey.ALGORITHM_CALC_SUFFIX, AlgorithmCalcStatusEnum.CALCULATE_FAIL.getCode(), 0);
            }
            pickUpAlgorithmService.updateAlgorithmCalcStatus(calcKey, calcStatus, calcResultDesc, calcResultDigest);
        });
    }


    /**
     * 轮询算法方案测算结果
     * @param request
     * @return
     */
    @RequestMapping("/pickup/algorithm/pollCheck")
    @PickUpResourceAuthority(TicketResourceEnum.ALGORITHM_MANAGER)
    public BaseResult<PickUpAlgorithmRollCheckResponse> pollCheckAlgorithm(@RequestBody
    PickUpAlgorithmRollCheckRequest request){
        PickUpAlgorithmRollCheckResponse rollCheckResponse = new PickUpAlgorithmRollCheckResponse();
        try {
            BridgeValidator.validate(request);
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            // 查询缓存测算结果
            String cacheKey = request.getCacheKey() + AlgorithmKey.ALGORITHM_CALC_SUFFIX;
            PickUpLogUtil.info("轮询测算任务。cacheKey:{}", cacheKey);
            Object resultObj = tairManagerWrapper.get(cacheKey);
            if(null != resultObj && StringUtils.equals(resultObj.toString(), AlgorithmCalcStatusEnum.CALCULATING.getCode())){
                rollCheckResponse.setStatus(AlgorithmCalcStatusEnum.CALCULATING.getCode());
                rollCheckResponse.setStatusDesc(AlgorithmCalcStatusEnum.CALCULATING.getDesc());
                return BaseResult.success(rollCheckResponse);
            } else if(null != resultObj && StringUtils.equals(resultObj.toString(), AlgorithmCalcStatusEnum.CALCULATE_FAIL.getCode())){
                rollCheckResponse.setStatus(AlgorithmCalcStatusEnum.CALCULATE_FAIL.getCode());
                rollCheckResponse.setStatusDesc(AlgorithmCalcStatusEnum.CALCULATE_FAIL.getDesc());
                return BaseResult.success(rollCheckResponse);
            } else {
                // 未查询到缓存直接查询数据库
                PickUpAlgorithmSchemeDO schemeDO = pickUpAlgorithmService.queryAlgorithmDetailByCalcKey(request.getCacheKey());
                if(null == schemeDO){
                    rollCheckResponse.setStatus(AlgorithmCalcStatusEnum.INIT.getCode());
                    rollCheckResponse.setStatusDesc(AlgorithmCalcStatusEnum.INIT.getDesc());
                    return BaseResult.success(rollCheckResponse);
                }
                rollCheckResponse.setStatus(AlgorithmCalcStatusEnum.CALCULATE_COMPLETE.getCode());
                rollCheckResponse.setStatusDesc(AlgorithmCalcStatusEnum.CALCULATE_COMPLETE.getDesc());
                if(StringUtils.isBlank(schemeDO.getAlgorithmDigest())){
                    rollCheckResponse.setCalcResult(AlgorithmKey.NO_MATCH_CALC_RESULT_DESC);
                }else {
                    rollCheckResponse.setCalcResult(schemeDO.getAlgorithmDigest());
                }
                return BaseResult.success(rollCheckResponse);
            }
        }catch (BridgeBusinessException e){
            // log error
            PickUpLogUtil.errLog("轮询测算结果异常:" + request.getCacheKey(),
                PickUpConstants.Action.ALGORITHM_POLL_CHECK_ERROR.name(),
                e.getErrorCode(), e.getErrorMessage());
            return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_POLL_CHECK_ERROR);
        }catch (Exception e){
            PickUpLogUtil.errLog("轮询测算结果异常:" + request.getCacheKey(),
                PickUpConstants.Action.ALGORITHM_POLL_CHECK_ERROR.name(), "SYSTEM_ERROR",
                ExceptionUtils.getFullStackTrace(e));
            return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_POLL_CHECK_ERROR);
        }

    }



    /**
     * 发布算法方案
     * @param request
     * @return
     */
    @RequestMapping("/pickup/algorithm/publish")
    @PickUpResourceAuthority(TicketResourceEnum.ALGORITHM_MANAGER)
    public BaseResult<PickUpAlgorithmApproveResponse> publishAlgorithm(@RequestBody PickUpAlgorithmPublishRequest request){
        try {

            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            BridgeValidator.validate(request);
            PickUpLogUtil.info("发布算法方案。request:{}", JSONObject.toJSONString(request));
            // 校验是否已测算完成
            PickUpAlgorithmSchemeDO algorithmSchemeDO = pickUpAlgorithmService.queryAlgorithmDetailByCalcKey(request.getCalcKey());
            if(null == algorithmSchemeDO){
                return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_NOT_EXIST_ERROR);
            }

            if(!StringUtils.equals(algorithmSchemeDO.getAlgorithmProgress(),AlgorithmCalcStatusEnum.CALCULATE_COMPLETE.getCode())){
                return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_NOT_CALC_DONE_ERROR);
            }

            if(!StringUtils.equals(AlgorithmCalcResultEnum.MATCH.getCode(),algorithmSchemeDO.getAlgorithmCalcResult())){
                return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_NOT_CALC_MATCH_APPROVE_ERROR);
            }
            // 查询发布人
            WaybillBridgeUserInfoDTO userInfo
                = waybillBridgeUserInfoService.findCharityUserId(request.getLoginUserId());
            if(null == userInfo){
                return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_START_APPROVE_ERROR);
            }
            algorithmSchemeDO.setPublisher(userInfo.getNick());
            // 发起审批算法方案
            PickUpAlgorithmApproveResponse response = pickUpAlgorithmService.approveAlgorithm(algorithmSchemeDO);
            if(response == null || StringUtils.isBlank(response.getApproveKey())){
                return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_START_APPROVE_ERROR);
            }
            return BaseResult.success(response);
        }catch (BridgeBusinessException e){
            PickUpLogUtil.errLog("发起算法方案审批失败:" + request.getCalcKey(),
                PickUpConstants.Action.ALGORITHM_START_APPROVE_ERROR.name(),
                e.getErrorCode(), e.getErrorMessage());
        }catch (Exception e){
            PickUpLogUtil.errLog("发起算法方案审批失败:" + request.getCalcKey(),
                PickUpConstants.Action.ALGORITHM_START_APPROVE_ERROR.name(),
                PickUpConstants.Error.Algorithm.ALGORITHM_START_APPROVE_ERROR.getErrorCode(),
                ExceptionUtils.getFullStackTrace(e));
        }
        return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_START_APPROVE_ERROR);
    }

    /**
     * 更新算法方案并发起测算
     * @param request
     * @return
     */
    @RequestMapping("/pickup/algorithm/update")
    @PickUpResourceAuthority(TicketResourceEnum.ALGORITHM_MANAGER)
    public BaseResult<Void> updateAlgorithm(@RequestBody PickUpAlgorithmCreateRequest request){
        try {
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            BridgeValidator.validate(request);

            // 校验是否已测算完成

            // 提交发布审批

            return BaseResult.success();
        }catch (BridgeBusinessException e){
            // log error
            PickUpLogUtil.errLog("修改算法方案异常:" + request.getOrderChannels()+"_"+request.getName(),
                PickUpConstants.Action.ALGORITHM_MODIFY_ERROR.name(),
                e.getErrorCode(), e.getErrorMessage());
            return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_MODIFY_ERROR);
        }catch (Exception e){
            PickUpLogUtil.errLog("修改算法方案异常:" + request.getOrderChannels()+"_"+request.getName(),
                PickUpConstants.Action.ALGORITHM_MODIFY_ERROR.name(), "SYSTEM_ERROR",
                ExceptionUtils.getFullStackTrace(e));
            return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_MODIFY_ERROR);
        }
    }


    /**
     * 下载分单比明细
     * @param request
     * @return
     */
    @RequestMapping("/pickup/algorithm/downloadDetail")
    @PickUpResourceAuthority(TicketResourceEnum.ALGORITHM_MANAGER)
    public BaseResult<String> exportAlgorithm(@RequestBody PickUpAlgorithmRequest request) {

        try {
            checkMenuRoleValid(request.getQuerySource(), request.getRole());
            BridgeValidator.validate(request);

            PickUpAlgorithmSchemeDO algorithmSchemeDO = pickUpAlgorithmService.queryAlgorithmDetailByCalcKey(request.getCalcKey());
            if(null == algorithmSchemeDO){
                return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_NOT_EXIST_ERROR);
            }
            if(!StringUtils.equals(AlgorithmCalcResultEnum.MATCH.getCode(),algorithmSchemeDO.getAlgorithmCalcResult())){
                return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_SCHEME_NOT_MATCH_ERROR);
            }
            String calcKey = algorithmSchemeDO.getCalcKey();
            String orderChannels = algorithmSchemeDO.getOrderChannels();
            LinkedHashMap<String,String> partMap = new LinkedHashMap<>(8);
            // 注意：有多个分区字段时，分区字段传值顺序需要与表的分区顺序保持一致
            partMap.put(AlgorithmKey.ALGORITHM_ORDER_CHANNELS,orderChannels);
            partMap.put(AlgorithmKey.ALGORITHM_CALC_KEY,calcKey);
            List<AlgorithmDetail> list
                = pickUpAlgorithmService.getAlgorithmDetailList(partMap);
            request.setPageSize(list.size());
            // 执行下载
            PagingResponse<AlgorithmDetail> algorithmDetailResponse = PagingResponse.build(list, list.size(), request.getCurrentPage(), request.getPageSize());

            String url = PickUpExcelUtil.exportFile(request, "分单比明细", AlgorithmDetail.class,
                request1 -> algorithmDetailResponse);

            return BaseResult.success(url);
        }catch (BridgeBusinessException e){
            // log error
            PickUpLogUtil.errLog("下载分单比明细异常:" + request.getId()+"_"+request.getOrderChannels()+"_"+request.getName(),
                PickUpConstants.Action.ALGORITHM_EXPORT_ERROR.name(),
                e.getErrorCode(), e.getErrorMessage());
            return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_EXPORT_ERROR);
        }catch (Exception e){
            PickUpLogUtil.errLog("下载分单比明细异常:" + request.getId()+"_"+request.getOrderChannels()+"_"+request.getName(),
                PickUpConstants.Action.ALGORITHM_EXPORT_ERROR.name(), "SYSTEM_ERROR",
                ExceptionUtils.getFullStackTrace(e));
            return BaseResult.bizFail(PickUpConstants.Error.Algorithm.ALGORITHM_EXPORT_ERROR);
        }

    }

    /**
     * 常量列表项
     * @return
     */
    @RequestMapping("/pickup/algorithm/constant/list")
    @PickUpResourceAuthority(TicketResourceEnum.ALGORITHM_MANAGER)
    public BaseResult<Map<String, List<PickUpPairDTO>>> algorithmConstantList(@RequestBody
    PickUpCommonQueryConstantRequest request){

        Map<String, List<PickUpPairDTO>> map = Maps.newHashMap();
        for (String constantType : request.getConstantsType()) {
            List<PickUpPairDTO> constantList = Lists.newArrayList();

            if ("businessTarget".equals(constantType)) {
                // 业务指标项
                TargetDimensionEnum[] items = TargetDimensionEnum.values();
                for (TargetDimensionEnum itemEnum : items) {
                    PickUpPairDTO pickUpPairDTO = new PickUpPairDTO();
                    pickUpPairDTO.setText(itemEnum.getDesc());
                    pickUpPairDTO.setValue(itemEnum.getCode());
                    constantList.add(pickUpPairDTO);
                }
            }else if ("targetConstraint".equals(constantType)) {
                // 目标方向值
                TargetConstraintEnum[] items = TargetConstraintEnum.values();
                for (TargetConstraintEnum itemEnum : items) {
                    PickUpPairDTO pickUpPairDTO = new PickUpPairDTO();
                    pickUpPairDTO.setText(itemEnum.getDesc());
                    pickUpPairDTO.setValue(itemEnum.getCode());
                    constantList.add(pickUpPairDTO);
                }
            }else if ("businessTargetConstraint".equals(constantType)) {
                // 业务指标约束
                TargetDimensionEnum[] items = TargetDimensionEnum.values();
                for (TargetDimensionEnum itemEnum : items) {
                    PickUpPairDTO pickUpPairDTO = new PickUpPairDTO();
                    pickUpPairDTO.setText(itemEnum.getDesc());
                    pickUpPairDTO.setValue(itemEnum.getCode());
                    constantList.add(pickUpPairDTO);
                }
            }else if ("constraintScope".equals(constantType)) {
                // 约束范围
                ConstraintRangeEnum[] items = ConstraintRangeEnum.values();
                for (ConstraintRangeEnum itemEnum : items) {
                    PickUpPairDTO pickUpPairDTO = new PickUpPairDTO();
                    pickUpPairDTO.setText(itemEnum.getDesc());
                    pickUpPairDTO.setValue(itemEnum.getCode());
                    constantList.add(pickUpPairDTO);
                }
            }else if("constraintType".equals(constantType)){
                // 约束类型
                ConstraintTypeEnum[] items = ConstraintTypeEnum.values();
                for (ConstraintTypeEnum itemEnum : items) {
                    PickUpPairDTO pickUpPairDTO = new PickUpPairDTO();
                    pickUpPairDTO.setText(itemEnum.getDesc());
                    pickUpPairDTO.setValue(itemEnum.getCode());
                    constantList.add(pickUpPairDTO);
                }
            }
            map.put(constantType, constantList);
        }

        return BaseResult.success(map);

    }

    /**
     * 可用的CP列表
     * @param request
     * @return
     */
    @RequestMapping("/pickup/algorithm/available/cp/list")
    @PickUpResourceAuthority(TicketResourceEnum.ALGORITHM_MANAGER)
    public BaseResult<List<String>> availableCpList(@RequestBody
    PickUpAlgorithmRequest request){
        List<String> cpList = Lists.newArrayList();
        Map<String, List<PickUpPairDTO>> map = Maps.newHashMap();
        if(null == request || StringUtils.isBlank(request.getOrderChannels())){
            return BaseResult.bizFail(PickUpConstants.Error.SYSTEM_PARAM_ERROR);
        }

        String fromAppKey = OrderSourceMapUtil.getFromAppKey(request.getOrderChannels());
        if(StringUtils.isBlank(fromAppKey)){
            return BaseResult.bizFail(PickUpConstants.Error.SYSTEM_PARAM_ERROR);
        }
        PickUpPlatConfig pickUpPlatConfig = PickUpPlatformConfigDiamond.getPlatConfigOrderChannelsFirst(fromAppKey, request.getOrderChannels());
        if(null == pickUpPlatConfig || CollectionUtils.isEmpty(pickUpPlatConfig.getCpConfigList())){
            return BaseResult.bizFail(PickUpConstants.Error.SYSTEM_PARAM_ERROR);
        }

        for(PickUpCpConfigInfo cpConfig : pickUpPlatConfig.getCpConfigList()){
            Integer cpBizType = cpConfig.getBizType();
            if(cpBizType == null){
                cpBizType = PickUpCpBizTypeEnum.END_USER_SHIPPING.getBizType();
            }
            cpList.add(cpConfig.getCpCode()+"-"+cpConfig.getAgent()+"-"+cpBizType);
        }
        List<String> cpNameList = CpAgentUtils.covertAvailableCpNameList(cpList);

        return BaseResult.success(cpNameList);

    }

}
