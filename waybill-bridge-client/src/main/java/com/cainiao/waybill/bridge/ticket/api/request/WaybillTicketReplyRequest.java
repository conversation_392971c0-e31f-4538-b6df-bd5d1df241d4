package com.cainiao.waybill.bridge.ticket.api.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 工单回复请求
 * <AUTHOR>
 */
@Data
public class WaybillTicketReplyRequest implements Serializable{

    /**
     *  菜鸟内部创建的工单id
     */
    private String ticketId;

    /**
     *  运单号
     */
    private String mailNo;

    /**
     *  工单回复后的状态
     *  0("取消"), 1("待处理"), 2("处理中"), 3("完结")
     */
    private Integer ticketStatus;

    /**
     *  工单回复内容
     */
    private String ticketRespContent;

    /**
     * 扩展字段
     */
    private String feature;

}
