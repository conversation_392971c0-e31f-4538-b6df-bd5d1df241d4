package com.cainiao.waybill.bridge.enterprise.outbound.service;

import com.cainiao.waybill.bridge.enterprise.outbound.request.OrderPickUpPushRequest;

/**
 * 出库服务类
 * <AUTHOR>
 * @date 2025-05-17 11:14:58
 */
public interface OutBoundService {

    /**
     *
     * 模版消息推送
     *
     * @param request 消息推送实体
     * @return 模版消息推送
     */
    public Boolean pushOrderPickUpMessage(OrderPickUpPushRequest request);
}
