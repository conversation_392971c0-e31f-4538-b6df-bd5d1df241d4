package com.cainiao.waybill.bridge.enterprise.utils;

import com.cainiao.waybill.bridge.model.dto.AddressInfo;
import com.cainiao.waybill.common.admin.dto.AddressDTO;

public class AddressUtils {

        /**
         * Parses a formatted address string and returns an AddressInfo object.
         * @param formattedAddress The address string to parse in the format "p:value;c:value;a:value;t:value;ad:value"
         * @return AddressInfo object containing parsed details
         */
        public static AddressInfo parse(String formattedAddress) {
            AddressInfo addressInfo = new AddressInfo();
            String[] parts = formattedAddress.split(";");

            for (String part : parts) {
                String[] keyValue = part.split(":");
                if (keyValue.length == 2) {
                    switch (keyValue[0]) {
                        case "p":
                            addressInfo.setProvince(keyValue[1]);
                            break;
                        case "c":
                            addressInfo.setCity(keyValue[1]);
                            break;
                        case "a":
                            addressInfo.setArea(keyValue[1]);
                            break;
                        case "t":
                            addressInfo.setTown(keyValue[1]);
                            break;
                        case "ad":
                            addressInfo.setAddressDetail(keyValue[1]);
                            break;
                        default:
                            // Handle unknown keys if necessary
                            break;
                    }
                }
            }

            return addressInfo;
        }


    /**
     * Converts AddressInfo DTO to a formatted address string.
     * @param addressInfo The AddressInfo object containing address details
     * @return A formatted string in the specified address format
     */
    public static String convertToFormattedString(AddressInfo addressInfo) {
        StringBuilder formattedAddress = new StringBuilder();

        if (addressInfo.getProvince() != null && !addressInfo.getProvince().isEmpty()) {
            formattedAddress.append("p:").append(addressInfo.getProvince()).append(";");
        }
        if (addressInfo.getCity() != null && !addressInfo.getCity().isEmpty()) {
            formattedAddress.append("c:").append(addressInfo.getCity()).append(";");
        }
        if (addressInfo.getArea() != null && !addressInfo.getArea().isEmpty()) {
            formattedAddress.append("a:").append(addressInfo.getArea()).append(";");
        }
        if (addressInfo.getTown() != null && !addressInfo.getTown().isEmpty()) {
            formattedAddress.append("t:").append(addressInfo.getTown()).append(";");
        }
        if (addressInfo.getAddressDetail() != null && !addressInfo.getAddressDetail().isEmpty()) {
            formattedAddress.append("ad:").append(addressInfo.getAddressDetail());
        }

        return formattedAddress.toString();
    }
    public static AddressDTO addressConvert(AddressInfo addressInfo){
        if (addressInfo == null) {
            return null;
        }
        AddressDTO addressDTO = new AddressDTO();
        addressDTO.setProvinceName(addressInfo.getProvince());
        addressDTO.setCityName(addressInfo.getCity());
        addressDTO.setAreaName(addressInfo.getArea());
        addressDTO.setTownName(addressInfo.getTown());
        addressDTO.setAddressDetail(addressInfo.getAddressDetail());
        return addressDTO;
    }

    public static AddressInfo addressInfoConvert(AddressDTO addressDTO){
        if (addressDTO == null) {
            return null;
        }
        AddressInfo addressInfo = new AddressInfo();
        addressInfo.setProvince(addressDTO.getProvinceName());
        addressInfo.setCity(addressDTO.getCityName());
        addressInfo.setArea(addressDTO.getAreaName());
        addressInfo.setTown(addressDTO.getTownName());
        addressInfo.setAddressDetail(addressDTO.getAddressDetail());
        return addressInfo;
    }

}
