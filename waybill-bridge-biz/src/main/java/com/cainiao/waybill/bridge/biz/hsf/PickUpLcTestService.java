package com.cainiao.waybill.bridge.biz.hsf;

import com.cainiao.waybill.bridge.biz.hsf.bean.FixGotInfoBean;
import com.cainiao.waybill.bridge.biz.pickup.dto.lite.LitePreChargeResultDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.waybill.pickup.dto.LogisticSubscribeDTO;
import com.cainiao.waybill.galaxy.order.domain.dto.BaseResult;
import com.cainiao.waybill.galaxy.order.domain.dto.CloudPrintApplyInfosResponse;
import com.taobao.logisticsdetail.common.domain.basic.LogisticsDetailDO;
import com.taobao.logisticsdetail.common.domain.result.SingleResult;

import java.util.Date;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * 物流详情订阅测试类
 */
public interface PickUpLcTestService {

    /**
     * 调用物流详情link订阅接口
     *
     * @return
     */
    BaseResultDTO<Void> lcSubscribe(LogisticSubscribeDTO subscribeDTO);

    /**
     * 通过hsf接口查询物流详情
     * @param mailNo mailNo
     * @param cpCode cpCode
     * @return
     */
    BaseResultDTO<SingleResult<List<LogisticsDetailDO>>> queryWaybillLCDetail(String mailNo, String cpCode);

    List<String> querySignWaybillCode(List<String> mailNoList);


    String getLcWeight(String mailNo,String cpCode);

    BaseResultDTO<Void> waybillPackPubInvoke(WaybillPickUpEvent pickUpEvent);

    BaseResultDTO<List<String>> replayGot(List<String> mailNoList) throws Exception;

    BaseResultDTO<List<String>> replayModifyWeight(List<String> mailNoList) throws Exception;

    LitePreChargeResultDTO gotPrice(String mailNo) throws Exception;

    BaseResult<String> createSwitchCpGotCheckTriggerTask(List<String> mailNoList, Date triggerTime)
        throws Exception;

    BaseResult<Map<String, Boolean>> switchToCp(List<String> mailNoList, Boolean triggerNow, String switchCpCode);


    BaseResult<Void> fixCpCode(Long id, String cpCode);

    BaseResult<Void> fixOutOrderId(Long id, String outOrderId);

    BaseResult<Void> fixMailNoAndCpCode(Long id, String mailNo, String cpCode, String realCpCode);

    BaseResult<Void> fixStatus(Long id, int status);

    BaseResult<Void> fixFeature(Long id, String feature);

    BaseResult<Void> fixFeature(List<FixGotInfoBean> list);

    BaseResultDTO<String> replaySignLogistics(String mailNo) throws ParseException;

    void replaySignLogistics(List<String> mailNoList) throws ParseException;

    void replayGgMsg(String msg, String tag, int reconsumeTimes) throws BridgeBaseException, ParseException;

    BaseResultDTO<String> judgeFakeGotMailNoList(String orderChannel, String startTime, String endTime, List<String> excludeStatusList, int overHour);

    BaseResultDTO<Void> insertPickUpTicketNoToTair(List<Long> ids);
}
