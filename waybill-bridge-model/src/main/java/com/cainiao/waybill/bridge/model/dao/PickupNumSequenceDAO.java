package com.cainiao.waybill.bridge.model.dao;

import java.util.List;

import com.cainiao.waybill.bridge.model.domain.PickupNumSequenceDO;
import com.taobao.common.dao.persistence.exception.DAOException;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-26
 */
public interface PickupNumSequenceDAO {

    /**
     * 根据 serialNum 查询 sequence 值
     * @param serialNum
     * @return
     * @throws DAOException
     */
    PickupNumSequenceDO queryBySerialNum(Long serialNum) throws DAOException;

    /**
     * 插入
     *
     * @param pickupNumSequenceDO
     * @return
     * @throws DAOException
     */
    Integer insert(PickupNumSequenceDO pickupNumSequenceDO) throws DAOException;

    /**
     * 批量插入
     * @param pickupNumSequenceList
     * @return
     * @throws DAOException
     */
    Integer batchInsert(List<PickupNumSequenceDO> pickupNumSequenceList) throws DAOException;
}
