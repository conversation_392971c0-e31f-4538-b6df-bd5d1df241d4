package com.cainiao.waybill.bridge.web.pickup.ticket.role;

import java.util.List;

import com.cainiao.waybill.bridge.biz.ticket.dto.PickUpBaseRequest;
import com.cainiao.waybill.bridge.biz.ticket.dto.TicketRoleEnum;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.web.pickup.constant.TicketResourceEnum;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zouping.fzp
 * @Classname UserAspect
 * @Description
 * @Date 2022/9/15 7:32 下午
 * @Version 1.0
 */
@Component
@Order(999)
@Aspect
public class PickUpResourceAuthorityAspect {

    @Pointcut("@annotation(com.cainiao.waybill.bridge.web.pickup.ticket.role.PickUpResourceAuthority)")
    public void pointcut() {
    }

    @Around(value = "pointcut() && @annotation(annotation)")
    public Object around(ProceedingJoinPoint pj, PickUpResourceAuthority annotation) throws Throwable {
        Object[] args = pj.getArgs();
        if (args != null) {
            for (Object arg : args) {
                if (arg instanceof PickUpBaseRequest) {
                    List<TicketResourceEnum> resourceEnumList = TicketResourceEnum.roleResourceList(
                        TicketRoleEnum.get(((PickUpBaseRequest)arg).getRole()));
                    if(ListUtil.non(resourceEnumList).contains(annotation.value())){
                        return pj.proceed();
                    }
                    for (TicketResourceEnum ticketResourceEnum : annotation.resourceList()) {
                        if (ListUtil.non(resourceEnumList).contains(ticketResourceEnum)) {
                            return pj.proceed();
                        }
                    }
                }
            }
        }
        throw new BridgeBusinessException("no_right", "无权限访问");
    }
}
