package com.cainiao.waybill.bridge.common.label.dto;

import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * @description 返回给商家的"网点-商家地址"绑定关系
 * <AUTHOR>
 * @date 2017-04-26
 */
public class BranchSellerAddressRelationForSellerDTO extends BaseDTO {
    private static final long serialVersionUID = 6366529173135775038L;

    /**
     * 主键id
     * 未来支持商家修改时，用来透传
     */
    private Long id;

    /**
     * cp 编码
     */
    private String cpCode;

    /**
     * 网点编码
     */
    private String branchCode;

    /**
     * 网点名称
     */
    private String branchName;

    /**
     * 商家地址信息
     */
    private AddressDTO sellerAddress;

    /**
     * 小件员id
     */
    private Long courierId;

    /**
     * 小件员姓名
     */
    private String courierName;

    /**
     * 小件员手机号码
     */
    private String courierMobile;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public Long getCourierId() {
        return courierId;
    }

    public void setCourierId(Long courierId) {
        this.courierId = courierId;
    }

    public AddressDTO getSellerAddress() {
        return sellerAddress;
    }

    public void setSellerAddress(AddressDTO sellerAddress) {
        this.sellerAddress = sellerAddress;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getCourierName() {
        return courierName;
    }

    public void setCourierName(String courierName) {
        this.courierName = courierName;
    }

    public String getCourierMobile() {
        return courierMobile;
    }

    public void setCourierMobile(String courierMobile) {
        this.courierMobile = courierMobile;
    }
}
