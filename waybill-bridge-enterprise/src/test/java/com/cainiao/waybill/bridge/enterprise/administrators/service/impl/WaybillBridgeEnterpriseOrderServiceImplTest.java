package com.cainiao.waybill.bridge.enterprise.administrators.service.impl;

import com.cainiao.waybill.bridge.biz.pickup.dto.route.RouteReachableResult;
import com.cainiao.waybill.bridge.biz.pickup.routing.manager.RoutingReachableManager;
import com.cainiao.waybill.bridge.enterprise.administrators.request.WaybillEnterpriseOrderQueryRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.WaybillEnterpriseOrderRequest;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseUserInfoDO;
import com.cainiao.waybill.bridge.model.domain.WaybillEnterpriseLocationDO;
import com.cainiao.waybill.bridge.model.dto.AddressInfo;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseOrderMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterprisePostMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillBridgeEnterpriseUserInfoMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillEnterpriseLocationMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.UUID;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class
WaybillBridgeEnterpriseOrderServiceImplTest {
    @InjectMocks
    private WaybillBridgeEnterpriseOrderServiceImpl waybillBridgeEnterpriseOrderService;
    @Mock
    private TransactionTemplate bridgeTransactionTemplate;
    @Mock
    private WaybillBridgeEnterpriseOrderMapper enterpriseOrderMapper;
    @Mock
    private RoutingReachableManager routingReachableManager;
    @Mock
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;
    @Mock
    private com.cainiao.waybill.bridge.enterprise.administrators.service.EnterpriseSettleAccountService enterpriseSettleAccountService;
    @Mock
    private com.cainiao.waybill.bridge.enterprise.utils.EnterpriseContentRiskWrapper enterpriseContentRiskWrapper;
    @Mock
    private com.cainiao.waybill.bridge.common.waybill.pickup.service.AddressCleanService addressCleanService;
    @Mock
    private WaybillEnterpriseLocationMapper enterpriseLocationMapper;
    @Mock
    private WaybillBridgeEnterprisePostMapper enterprisePostMapper;

    // 测试用例：参数缺失抛出异常
    @Test(expected = EnterpriseException.class)
    public void testCreateParamMissing() throws Exception {
        WaybillEnterpriseOrderRequest request = new WaybillEnterpriseOrderRequest();
        waybillBridgeEnterpriseOrderService.create(request);
    }

    // 测试用例：场地快递配置校验失败抛出异常
    @Test(expected = EnterpriseException.class)
    public void testCreateAccountNotExist() throws Exception {
        WaybillEnterpriseOrderRequest request = new WaybillEnterpriseOrderRequest();
        request.setCorpId("CorpIdTest");
        request.setCpCode("CpCodeTest");
        request.setWaybillAccountNo("WaybillAccountNoTest");
        request.setSenderMobile("SenderMobileTest");
        WaybillBridgeEnterpriseUserInfoDO userInfoDO = mock(WaybillBridgeEnterpriseUserInfoDO.class);
        when(enterpriseUserInfoMapper.selectByUserId(anyString(), anyString())).thenReturn(userInfoDO);
        when(enterpriseOrderMapper.selectByParam(anyObject())).thenReturn(Collections.emptyList());
        waybillBridgeEnterpriseOrderService.create(request);
    }

    // 测试用例：成功创建订单
    @Test
    public void testCreateOrderSuccess() throws Throwable {
        WaybillEnterpriseOrderRequest request = new WaybillEnterpriseOrderRequest();
        request.setCorpId("CorpIdTest");
        request.setCpCode("CpCodeTest");
        request.setWaybillAccountNo("WaybillAccountNoTest");
        request.setSenderMobile("SenderMobileTest");
        request.setSenderName("SenderNameTest");
        request.setUserId("testuser");  // 下单人字段
        // 设置场地ID，避免"场地id为空"错误
        request.setLocationId(123L);
        // 设置产品类型，避免"产品类型为空"错误
        request.setProduct("标准快递");
        // 设置物品类型，避免"物品类型为空"错误
        request.setItem(1);
        // 设置结算类型，避免"结算类型为空"错误
        request.setBusinessType(1);
        // 设置寄件人地址信息
        AddressInfo senderAddress = new AddressInfo();
        senderAddress.setProvince("浙江省");
        senderAddress.setCity("杭州市");
        senderAddress.setArea("西湖区");
        senderAddress.setAddressDetail("文三路269号");
        request.setSenderAddress(senderAddress);
        AddressInfo consigneeAddress = new AddressInfo();
        consigneeAddress.setProvince("上海市");
        consigneeAddress.setCity("上海市");
        consigneeAddress.setArea("浦东新区");
        consigneeAddress.setAddressDetail("陆家嘴环路1000号");
        request.setConsigneeAddress(consigneeAddress);
        request.setConsigneeName("ConsigneeName");
        request.setConsigneeMobile("***********");
        WaybillBridgeEnterpriseUserInfoDO userInfoDO = mock(WaybillBridgeEnterpriseUserInfoDO.class);
        when(enterpriseUserInfoMapper.selectByUserId(anyString(), anyString())).thenReturn(userInfoDO);
        when(enterpriseOrderMapper.selectByParam(anyObject())).thenReturn(Collections.emptyList());
        // Mock enterpriseSettleAccountService.list() 返回非空列表
        when(enterpriseSettleAccountService.list(any())).thenReturn(Collections.singletonList(new com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseSettleAccountDTO()));
        // Mock addressCleanService.clean() 返回null（表示不需要清洗）
        when(addressCleanService.clean(any())).thenReturn(null);
        TransactionCallback<String> transactionCallback = mock(TransactionCallback.class);
        // routingReachableManager.routingReachable是void方法，不需要特别设置mock行为
        when(bridgeTransactionTemplate.execute(transactionCallback)).thenReturn(UUID.randomUUID().toString());
        RouteReachableResult routeReachableResult = mock(RouteReachableResult.class);
        when(routeReachableResult.isReachable()).thenReturn(true);
        when(routingReachableManager.routingReachable(anyObject())).thenReturn(routeReachableResult);
        waybillBridgeEnterpriseOrderService.create(request);
    }

    // 测试用例：调用创建订单时发生异常
    @Test(expected = EnterpriseException.class)
    public void testCreateOrderExceptionPropagation() throws Throwable {
        WaybillEnterpriseOrderRequest request = new WaybillEnterpriseOrderRequest();
        request.setCorpId("CorpIdTest");
        request.setCpCode("CpCodeTest");
        request.setWaybillAccountNo("WaybillAccountNoTest");
        AddressInfo consigneeAddress = new AddressInfo();
        consigneeAddress.setProvince("Province");
        consigneeAddress.setCity("City");
        consigneeAddress.setArea("Area");
        consigneeAddress.setAddressDetail("AddressDetail");
        request.setConsigneeAddress(consigneeAddress);
        request.setConsigneeName("ConsigneeName");
        request.setConsigneeMobile("***********");
        WaybillBridgeEnterpriseUserInfoDO userInfoDO = mock(WaybillBridgeEnterpriseUserInfoDO.class);
        when(enterpriseUserInfoMapper.selectByUserId(anyString(), anyString())).thenReturn(userInfoDO);
        when(enterpriseOrderMapper.selectByParam(anyObject())).thenReturn(Collections.emptyList());
        doThrow(new EnterpriseException(EnterpriseErrorEnum.ORDER_UNREACHABLE.code(), "Unreachable")).when(routingReachableManager).routingReachable(anyObject());
        waybillBridgeEnterpriseOrderService.create(request);
    }
}

    @Test(expected = EnterpriseException.class)
    public void testPageListExceptionWithLocationIdInvalid() throws Throwable {
        WaybillEnterpriseOrderQueryRequest queryRequest = new WaybillEnterpriseOrderQueryRequest();
        queryRequest.setCorpId("CorpIdTest");
        queryRequest.setCurrentPage(1);
        queryRequest.setPageSize(10);
        queryRequest.setStartTime("**********");
        queryRequest.setEndTime("**********");
        queryRequest.setLocationId("LocationIdTest");
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(null);

        waybillBridgeEnterpriseOrderService.pageList(queryRequest);
    }

    // 测试用例：调用创建订单时发生异常
    @Test(expected = EnterpriseException.class)
    public void testPageListExceptionWithPostIdInvalid() throws Throwable {
        WaybillEnterpriseOrderQueryRequest queryRequest = new WaybillEnterpriseOrderQueryRequest();
        queryRequest.setCorpId("CorpIdTest");
        queryRequest.setCurrentPage(1);
        queryRequest.setPageSize(10);
        queryRequest.setStartTime("**********");
        queryRequest.setEndTime("**********");
        queryRequest.setLocationId("LocationIdTest");
        WaybillEnterpriseLocationDO locationDO = new WaybillEnterpriseLocationDO();
        locationDO.setId(1L);
        when(enterpriseLocationMapper.selectOneByParam(any())).thenReturn(locationDO);

        queryRequest.setPostId("PostIdTest");
        when(enterprisePostMapper.selectOneByParam(any())).thenReturn(null);
        waybillBridgeEnterpriseOrderService.pageList(queryRequest);
    }
}
