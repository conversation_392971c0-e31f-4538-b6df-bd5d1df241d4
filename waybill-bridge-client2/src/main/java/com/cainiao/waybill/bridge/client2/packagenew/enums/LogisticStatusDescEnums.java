package com.cainiao.waybill.bridge.client2.packagenew.enums;



import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * 物流状态描述
 *
 * <AUTHOR>
 */
public enum LogisticStatusDescEnums {

    /**
     * 已揽件
     */
    ACCEPT("ACCEPT", "已揽件"),

    CONSIGN("CONSIGN", "已发货"),

    TRANSPORT("TRANSPORT", "运输中"),

    DELIVERING("DELIVERING", "派送中"),

    AGENT_SIGN("AGENT_SIGN", "待取件"),

    SIGN("SIGN", "已签收"),


    ;

    private String statusCode;

    private String statusDesc;

    LogisticStatusDescEnums(String statusCode, String statusDesc){
        this.statusCode = statusCode;
        this.statusDesc = statusDesc;
    }

    public String getStatusCode() {
        return statusCode;
    }

    public String getStatusDesc() {
        return statusDesc;
    }


    public static boolean isStatusDescValid(String statusDesc) {
        if (StringUtils.isBlank(statusDesc)){
            return true;
        }
        return Arrays.stream(LogisticStatusDescEnums.values())
                .map(LogisticStatusDescEnums::getStatusDesc)
                .map(Optional::ofNullable)
                .anyMatch(opt -> opt.map(str -> str.equals(statusDesc)).orElse(false));
    }

    /**
     * 根据状态码获取状态描述
     * @param statusCode
     * @return
     */
    public static String getDescByCode(String statusCode){
        LogisticStatusDescEnums[] enums = LogisticStatusDescEnums.values();
        for(LogisticStatusDescEnums descEnum : enums){
            if(descEnum.getStatusCode().equals(statusCode)){
                return descEnum.getStatusDesc();
            }
        }
        return null;
    }


}
