package com.cainiao.waybill.bridge.innersupport.ad.constant.commom;

/**
 * @Description 工单系统错误码常量
 */
public class AdErrorConstants {

    public final static ErrorInfo PARAM_ERROR = new ErrorInfo("PARAM_ERROR", "参数错误");

    public final static ErrorInfo CREATE_FAIL = new ErrorInfo("CREATE_FAIL", "新增失败");
    public final static ErrorInfo UPDATE_FAIL = new ErrorInfo("UPDATE_FAIL", "更新失败");
    public final static ErrorInfo DELETE_FAIL = new ErrorInfo("DELETE_FAIL", "删除失败");
    public final static ErrorInfo QUERY_FAIL = new ErrorInfo("QUERY_FAIL", "查询失败");

    public final static ErrorInfo SYSTEM_ERROR = new ErrorInfo("SYSTEM_ERROR", "系统异常");

    public final static ErrorInfo   UNKNOWN_ROLE_ERROR = new ErrorInfo("UNKNOWN_ROLE_ERROR", "用户角色异常");

    public final static ErrorInfo NOT_FOUND_WAYBILL_AD_WORK_ORDER_BY_ID = new ErrorInfo("NOT_FOUND_WAYBILL_AD_WORK_ORDER_BY_ID", "根据id没有查询到工单");

    public final static ErrorInfo HSF_TIMEOUT_ERROR = new ErrorInfo("HSF_TIMEOUT_ERROR", "网络超时，请重试");

    public static final ErrorInfo GET_LOCK_FAIL = new ErrorInfo("GET_LOCK_FAIL", "获取锁失败");

    public static final ErrorInfo RELEASE_LOCK_FAIL = new ErrorInfo("RELEASE_LOCK_FAIL", "释放锁失败");

    public final static ErrorInfo DIVISION_ERROR = new ErrorInfo("DIVISION_ERROR", "地址查找错误");

    public final static ErrorInfo USER_ROLE_NOT_MATCH = new ErrorInfo("USER_ROLE_NOT_MATCH", "用户角色不满足当前页面权限要求");
    public final static ErrorInfo GET_CN_USER_LOGIN_INFO_IS_NULL = new ErrorInfo("GET_CN_USER_LOGIN_INFO_IS_NULL", "获取菜鸟会员登录信息为空");
    public final static ErrorInfo QUERY_CN_USERINFO_ERROR = new ErrorInfo("QUERY_CN_USERINFO_ERROR", "查询菜鸟会员信息失败");

    public final static ErrorInfo QUERY_APP_USERINFO_ERROR = new ErrorInfo("QUERY_APP_USERINFO_ERROR", "查询菜鸟APP会员信息失败");

    public final static ErrorInfo CN_USERINFO_MOBILE_IS_NULL = new ErrorInfo("CN_USERINFO_MOBILE_IS_NULL", "菜鸟会员手机号为空");
    public final static ErrorInfo USER_ROLE_IS_NULL = new ErrorInfo("USER_ROLE_IS_NULL", "用户角色为空");
    public final static ErrorInfo QUERY_USER_BY_MOBILE_FAIL = new ErrorInfo("QUERY_USER_BY_MOBILE_FAIL", "根据手机号查询用户失败");
    public final static ErrorInfo USER_MOBILE_IS_EXIST = new ErrorInfo("USER_MOBILE_IS_EXIST", "用户手机号已存在");
    public static final ErrorInfo GET_OSS_TOKEN_FAIL = new ErrorInfo("GENERATE_PKG_OSS_TOKEN_FAIL", "获取oss_token失败");

    public static final ErrorInfo GET_IMAGE_URL_FAIL = new ErrorInfo("GET_IMAGE_URL_FAIL", "获取图片url失败");

    public static final ErrorInfo OSS_FILE_NAME_IS_NULL = new ErrorInfo("OSS_FILE_NAME_IS_NULL", "oss文件名为空");

    public final static ErrorInfo MAIL_NO_DUPLICATE_ERROR = new ErrorInfo("MAIL_NO_DUPLICATE_ERROR", "运单号已存在，请重试");

    public final static ErrorInfo ADVERTISER_ID_IS_NULL_ERROR = new ErrorInfo("ADVERTISER_ID_IS_NULL_ERROR", "广告主id不能为空");

    public final static ErrorInfo CP_CODE_IS_NULL_ERROR = new ErrorInfo("CP_CODE_IS_NULL_ERROR", "cp编码为不能空");
}
