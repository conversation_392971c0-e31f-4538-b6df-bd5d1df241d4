package com.cainiao.waybill.bridge.common.label.dto;

import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * <AUTHOR>
 * @since 2017/04/21
 * 余额设置查询DTO
 */
public class AccountBalanceAlarmQueryDTO extends BaseDTO {
    private static final long serialVersionUID = -552094844182455658L;
    /**
     * 账户id
     */
    private Long accountId;

    /**
     * 物流商ID
     */
    private Long    cpId;

    /**
     * 网点编码
     */
    private String  branchCode;
    /**
     * 号段用途编码,用于区分一个CP下的不同号段
     */
    private String segmentCode;

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getCpId() {
        return cpId;
    }

    public void setCpId(Long cpId) {
        this.cpId = cpId;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getSegmentCode() {
        return segmentCode;
    }

    public void setSegmentCode(String segmentCode) {
        this.segmentCode = segmentCode;
    }
}
