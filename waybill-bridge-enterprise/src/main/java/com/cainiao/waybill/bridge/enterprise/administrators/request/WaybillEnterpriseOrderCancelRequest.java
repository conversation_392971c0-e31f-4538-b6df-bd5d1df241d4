package com.cainiao.waybill.bridge.enterprise.administrators.request;

import com.cainiao.waybill.bridge.enterprise.common.BaseRequest;
import com.cainiao.waybill.bridge.enterprise.common.Paging;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 寄件取消请求
 * <AUTHOR>
 * @date 2025/4/28 10:55
 */
@Data
public class WaybillEnterpriseOrderCancelRequest extends BaseRequest {

    private static final long serialVersionUID = 181017811038722337L;
    /**
     * 场地id
     */
    private Long locationId;

    /**
     * 运力服务商
     */
    private String cpCode;

    /**
     * 业务唯一id
     */
    private String outerOrderCode;

    @Length(max = 255, message = "取消原因最大长度255")
    private String cancelDesc;




}