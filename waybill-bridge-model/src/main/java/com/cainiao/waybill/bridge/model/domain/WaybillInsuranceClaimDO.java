package com.cainiao.waybill.bridge.model.domain;

import org.hibernate.validator.constraints.NotBlank;

import java.util.Date;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
/**
 *
 * <AUTHOR>
 */
public class WaybillInsuranceClaimDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   保险产品编号
     *
     *
     * @mbg.generated
     */
    private String productCode;

    /**
     * Database Column Remarks:
     *   电商平台编号
     *
     *
     * @mbg.generated
     */
    private String platformCode;

    /**
     * Database Column Remarks:
     *   平台交易编号
     *
     *
     * @mbg.generated
     */
    private String orderId;

    /**
     * Database Column Remarks:
     *   买家申请退货时间
     *
     *
     * @mbg.generated
     */
    private Date applyTime;

    /**
     * Database Column Remarks:
     *   买家申请退货原因
     *
     *
     * @mbg.generated
     */
    private String applyReason;

    /**
     * Database Column Remarks:
     *   卖家签收时间
     *
     *
     * @mbg.generated
     */
    private Date signInTime;

    /**
     * Database Column Remarks:
     *   物流公司编号
     *
     *
     * @mbg.generated
     */
    private String expressCompany;

    /**
     * Database Column Remarks:
     *   物流单号
     *
     *
     * @mbg.generated
     */
    private String expressNo;

    /**
     * 发货人省份
     */
    private String senderProvince;

    /**
     * 发货人城市
     */
    private String senderCity;

    /**
     * 发货人区
     */
    private String senderDistrict;

    /**
     * 收货人省份
     */
    private String receiverProvince;

    /**
     * 收货人城市
     */
    private String receiverCity;

    /**
     * 收货人区
     */
    private String receiverDistrict;

    /**
     *   收货详细地址
     */
    private String receiverAddress;

    /**
     * 理赔业务号
     */
    private String claimNo;

    /**
     * 理赔额
     */
    private Integer indemnity;

    /**
     * 结案时间
     */
    private Date closeDate;

    /**
     * 理赔状态
     */
    private String status;

    /**
     * Database Column Remarks:
     *   收款账户
     *
     *
     * @mbg.generated
     */
    private String userAccount;

    /**
     * Database Column Remarks:
     *   用户姓名
     *
     *
     * @mbg.generated
     */
    private String userName;

    /**
     * Database Column Remarks:
     *   用户电话号码
     *
     *
     * @mbg.generated
     */
    private String userPhone;

    /**
     * Database Column Remarks:
     *   支付渠道，1:支付宝
     *
     *
     * @mbg.generated
     */
    private Integer payChannel;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getClaimNo() {
        return claimNo;
    }

    public void setClaimNo(String claimNo) {
        this.claimNo = claimNo;
    }

    public Integer getIndemnity() {
        return indemnity;
    }

    public void setIndemnity(Integer indemnity) {
        this.indemnity = indemnity;
    }

    public Date getCloseDate() {
        return closeDate;
    }

    public void setCloseDate(Date closeDate) {
        this.closeDate = closeDate;
    }

    public String getSenderProvince() {
        return senderProvince;
    }

    public void setSenderProvince(String senderProvince) {
        this.senderProvince = senderProvince;
    }

    public String getSenderCity() {
        return senderCity;
    }

    public void setSenderCity(String senderCity) {
        this.senderCity = senderCity;
    }

    public String getSenderDistrict() {
        return senderDistrict;
    }

    public void setSenderDistrict(String senderDistrict) {
        this.senderDistrict = senderDistrict;
    }

    public String getReceiverProvince() {
        return receiverProvince;
    }

    public void setReceiverProvince(String receiverProvince) {
        this.receiverProvince = receiverProvince;
    }

    public String getReceiverCity() {
        return receiverCity;
    }

    public void setReceiverCity(String receiverCity) {
        this.receiverCity = receiverCity;
    }

    public String getReceiverDistrict() {
        return receiverDistrict;
    }

    public void setReceiverDistrict(String receiverDistrict) {
        this.receiverDistrict = receiverDistrict;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_insurance_claim.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_insurance_claim.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_insurance_claim.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.product_code
     *
     * @mbg.generated
     */
    public String getProductCode() {
        return productCode;
    }

    /**
     *
     * @param productCode the value for waybill_insurance_claim.product_code
     *
     * @mbg.generated
     */
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.platform_code
     *
     * @mbg.generated
     */
    public String getPlatformCode() {
        return platformCode;
    }

    /**
     *
     * @param platformCode the value for waybill_insurance_claim.platform_code
     *
     * @mbg.generated
     */
    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.order_id
     *
     * @mbg.generated
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     *
     * @param orderId the value for waybill_insurance_claim.order_id
     *
     * @mbg.generated
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.apply_time
     *
     * @mbg.generated
     */
    public Date getApplyTime() {
        return applyTime;
    }

    /**
     *
     * @param applyTime the value for waybill_insurance_claim.apply_time
     *
     * @mbg.generated
     */
    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.apply_reason
     *
     * @mbg.generated
     */
    public String getApplyReason() {
        return applyReason;
    }

    /**
     *
     * @param applyReason the value for waybill_insurance_claim.apply_reason
     *
     * @mbg.generated
     */
    public void setApplyReason(String applyReason) {
        this.applyReason = applyReason;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.sign_in_time
     *
     * @mbg.generated
     */
    public Date getSignInTime() {
        return signInTime;
    }

    /**
     *
     * @param signInTime the value for waybill_insurance_claim.sign_in_time
     *
     * @mbg.generated
     */
    public void setSignInTime(Date signInTime) {
        this.signInTime = signInTime;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.express_company
     *
     * @mbg.generated
     */
    public String getExpressCompany() {
        return expressCompany;
    }

    /**
     *
     * @param expressCompany the value for waybill_insurance_claim.express_company
     *
     * @mbg.generated
     */
    public void setExpressCompany(String expressCompany) {
        this.expressCompany = expressCompany;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.express_no
     *
     * @mbg.generated
     */
    public String getExpressNo() {
        return expressNo;
    }

    /**
     *
     * @param expressNo the value for waybill_insurance_claim.express_no
     *
     * @mbg.generated
     */
    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.user_account
     *
     * @mbg.generated
     */
    public String getUserAccount() {
        return userAccount;
    }

    /**
     *
     * @param userAccount the value for waybill_insurance_claim.user_account
     *
     * @mbg.generated
     */
    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.user_name
     *
     * @mbg.generated
     */
    public String getUserName() {
        return userName;
    }

    /**
     *
     * @param userName the value for waybill_insurance_claim.user_name
     *
     * @mbg.generated
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.user_phone
     *
     * @mbg.generated
     */
    public String getUserPhone() {
        return userPhone;
    }

    /**
     *
     * @param userPhone the value for waybill_insurance_claim.user_phone
     *
     * @mbg.generated
     */
    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    /**
     *
     * @return the value of waybill_insurance_claim.pay_channel
     *
     * @mbg.generated
     */
    public Integer getPayChannel() {
        return payChannel;
    }

    /**
     *
     * @param payChannel the value for waybill_insurance_claim.pay_channel
     *
     * @mbg.generated
     */
    public void setPayChannel(Integer payChannel) {
        this.payChannel = payChannel;
    }

    @Override
    public String toString() {
        return "WaybillInsuranceClaimDO{" +
                "id=" + id +
                ", gmtCreate=" + gmtCreate +
                ", gmtModified=" + gmtModified +
                ", productCode='" + productCode + '\'' +
                ", platformCode='" + platformCode + '\'' +
                ", orderId='" + orderId + '\'' +
                ", applyTime=" + applyTime +
                ", applyReason='" + applyReason + '\'' +
                ", signInTime=" + signInTime +
                ", expressCompany='" + expressCompany + '\'' +
                ", expressNo='" + expressNo + '\'' +
                ", senderProvince='" + senderProvince + '\'' +
                ", senderCity='" + senderCity + '\'' +
                ", senderDistrict='" + senderDistrict + '\'' +
                ", receiverProvince='" + receiverProvince + '\'' +
                ", receiverCity='" + receiverCity + '\'' +
                ", receiverDistrict='" + receiverDistrict + '\'' +
                ", receiverAddress='" + receiverAddress + '\'' +
                ", claimNo='" + claimNo + '\'' +
                ", indemnity=" + indemnity +
                ", closeDate=" + closeDate +
                ", status=" + status +
                ", userAccount='" + userAccount + '\'' +
                ", userName='" + userName + '\'' +
                ", userPhone='" + userPhone + '\'' +
                ", payChannel=" + payChannel +
                '}';
    }
}