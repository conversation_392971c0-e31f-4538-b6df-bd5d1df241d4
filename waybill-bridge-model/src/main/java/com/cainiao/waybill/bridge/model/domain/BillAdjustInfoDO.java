package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class BillAdjustInfoDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   账单ID
     *
     *
     * @mbg.generated
     */
    private String billSummaryId;

    /**
     * Database Column Remarks:
     *   账单月
     *
     *
     * @mbg.generated
     */
    private String billMonth;

    /**
     * Database Column Remarks:
     *   调账类型 重量/金额
     *
     *
     * @mbg.generated
     */
    private String adjustType;

    /**
     * Database Column Remarks:
     *   订单号
     *
     *
     * @mbg.generated
     */
    private String outOrderCode;

    /**
     * Database Column Remarks:
     *   运单号
     *
     *
     * @mbg.generated
     */
    private String mailNo;

    /**
     * Database Column Remarks:
     *   重量
     *
     *
     * @mbg.generated
     */
    private Integer weight;

    /**
     * Database Column Remarks:
     *   供应链服务费，单位分
     *
     *
     * @mbg.generated
     */
    private Integer cpServiceFee;

    /**
     * Database Column Remarks:
     *   运费奖励费用，单位分
     *
     *
     * @mbg.generated
     */
    private Integer freightRewardFee;

    /**
     * Database Column Remarks:
     *   异常单理赔费用，单位分
     *
     *
     * @mbg.generated
     */
    private Integer abnormalClaimFee;

    /**
     * Database Column Remarks:
     *   用户权益费用，单位分
     *
     *
     * @mbg.generated
     */
    private Integer userRightsFee;

    /**
     * Database Column Remarks:
     *   备注
     *
     *
     * @mbg.generated
     */
    private String remark;

    /**
     * Database Column Remarks:
     *   调账状态
     *
     *
     * @mbg.generated
     */
    private String status;

    /**
     * Database Column Remarks:
     *   扩展字段
     *
     *
     * @mbg.generated
     */
    private String feature;

    /**
     * Database Column Remarks:
     *   调账文件ID
     *
     *
     * @mbg.generated
     */
    private Long billAdjustFileId;

    /**
     *
     * @return the value of bill_adjust_info.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for bill_adjust_info.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of bill_adjust_info.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for bill_adjust_info.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of bill_adjust_info.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for bill_adjust_info.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of bill_adjust_info.bill_summary_id
     *
     * @mbg.generated
     */
    public String getBillSummaryId() {
        return billSummaryId;
    }

    /**
     *
     * @param billSummaryId the value for bill_adjust_info.bill_summary_id
     *
     * @mbg.generated
     */
    public void setBillSummaryId(String billSummaryId) {
        this.billSummaryId = billSummaryId;
    }

    /**
     *
     * @return the value of bill_adjust_info.bill_month
     *
     * @mbg.generated
     */
    public String getBillMonth() {
        return billMonth;
    }

    /**
     *
     * @param billMonth the value for bill_adjust_info.bill_month
     *
     * @mbg.generated
     */
    public void setBillMonth(String billMonth) {
        this.billMonth = billMonth;
    }

    /**
     *
     * @return the value of bill_adjust_info.adjust_type
     *
     * @mbg.generated
     */
    public String getAdjustType() {
        return adjustType;
    }

    /**
     *
     * @param adjustType the value for bill_adjust_info.adjust_type
     *
     * @mbg.generated
     */
    public void setAdjustType(String adjustType) {
        this.adjustType = adjustType;
    }

    /**
     *
     * @return the value of bill_adjust_info.out_order_code
     *
     * @mbg.generated
     */
    public String getOutOrderCode() {
        return outOrderCode;
    }

    /**
     *
     * @param outOrderCode the value for bill_adjust_info.out_order_code
     *
     * @mbg.generated
     */
    public void setOutOrderCode(String outOrderCode) {
        this.outOrderCode = outOrderCode;
    }

    /**
     *
     * @return the value of bill_adjust_info.mail_no
     *
     * @mbg.generated
     */
    public String getMailNo() {
        return mailNo;
    }

    /**
     *
     * @param mailNo the value for bill_adjust_info.mail_no
     *
     * @mbg.generated
     */
    public void setMailNo(String mailNo) {
        this.mailNo = mailNo;
    }

    /**
     *
     * @return the value of bill_adjust_info.weight
     *
     * @mbg.generated
     */
    public Integer getWeight() {
        return weight;
    }

    /**
     *
     * @param weight the value for bill_adjust_info.weight
     *
     * @mbg.generated
     */
    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    /**
     *
     * @return the value of bill_adjust_info.cp_service_fee
     *
     * @mbg.generated
     */
    public Integer getCpServiceFee() {
        return cpServiceFee;
    }

    /**
     *
     * @param cpServiceFee the value for bill_adjust_info.cp_service_fee
     *
     * @mbg.generated
     */
    public void setCpServiceFee(Integer cpServiceFee) {
        this.cpServiceFee = cpServiceFee;
    }

    /**
     *
     * @return the value of bill_adjust_info.freight_reward_fee
     *
     * @mbg.generated
     */
    public Integer getFreightRewardFee() {
        return freightRewardFee;
    }

    /**
     *
     * @param freightRewardFee the value for bill_adjust_info.freight_reward_fee
     *
     * @mbg.generated
     */
    public void setFreightRewardFee(Integer freightRewardFee) {
        this.freightRewardFee = freightRewardFee;
    }

    /**
     *
     * @return the value of bill_adjust_info.abnormal_claim_fee
     *
     * @mbg.generated
     */
    public Integer getAbnormalClaimFee() {
        return abnormalClaimFee;
    }

    /**
     *
     * @param abnormalClaimFee the value for bill_adjust_info.abnormal_claim_fee
     *
     * @mbg.generated
     */
    public void setAbnormalClaimFee(Integer abnormalClaimFee) {
        this.abnormalClaimFee = abnormalClaimFee;
    }

    /**
     *
     * @return the value of bill_adjust_info.user_rights_fee
     *
     * @mbg.generated
     */
    public Integer getUserRightsFee() {
        return userRightsFee;
    }

    /**
     *
     * @param userRightsFee the value for bill_adjust_info.user_rights_fee
     *
     * @mbg.generated
     */
    public void setUserRightsFee(Integer userRightsFee) {
        this.userRightsFee = userRightsFee;
    }

    /**
     *
     * @return the value of bill_adjust_info.remark
     *
     * @mbg.generated
     */
    public String getRemark() {
        return remark;
    }

    /**
     *
     * @param remark the value for bill_adjust_info.remark
     *
     * @mbg.generated
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     *
     * @return the value of bill_adjust_info.status
     *
     * @mbg.generated
     */
    public String getStatus() {
        return status;
    }

    /**
     *
     * @param status the value for bill_adjust_info.status
     *
     * @mbg.generated
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     *
     * @return the value of bill_adjust_info.feature
     *
     * @mbg.generated
     */
    public String getFeature() {
        return feature;
    }

    /**
     *
     * @param feature the value for bill_adjust_info.feature
     *
     * @mbg.generated
     */
    public void setFeature(String feature) {
        this.feature = feature;
    }

    /**
     *
     * @return the value of bill_adjust_info.bill_adjust_file_id
     *
     * @mbg.generated
     */
    public Long getBillAdjustFileId() {
        return billAdjustFileId;
    }

    /**
     *
     * @param billAdjustFileId the value for bill_adjust_info.bill_adjust_file_id
     *
     * @mbg.generated
     */
    public void setBillAdjustFileId(Long billAdjustFileId) {
        this.billAdjustFileId = billAdjustFileId;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", billSummaryId=").append(billSummaryId);
        sb.append(", billMonth=").append(billMonth);
        sb.append(", adjustType=").append(adjustType);
        sb.append(", outOrderCode=").append(outOrderCode);
        sb.append(", mailNo=").append(mailNo);
        sb.append(", weight=").append(weight);
        sb.append(", cpServiceFee=").append(cpServiceFee);
        sb.append(", freightRewardFee=").append(freightRewardFee);
        sb.append(", abnormalClaimFee=").append(abnormalClaimFee);
        sb.append(", userRightsFee=").append(userRightsFee);
        sb.append(", remark=").append(remark);
        sb.append(", status=").append(status);
        sb.append(", feature=").append(feature);
        sb.append(", billAdjustFileId=").append(billAdjustFileId);
        sb.append("]");
        return sb.toString();
    }
}