package com.cainiao.waybill.bridge.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/22-下午5:32
 */
@Data
public class WaybillPickUpOrderDetailQueryDTO {
    /**
     * 订单生成起始时间
     */
    private Date createStart;

    /**
     * 订单生成截止时间
     */
    private Date createEnd;

    /**
     * 预约单生成起始时间
     */
    private Date appoDateStart;

    /**
     * 预约单生成截止时间
     */
    private Date appoDateEnd;


    /**
     * 当日起始时间
     */
    private Date currDateStart;

    /**
     * 当日截止时间
     */
    private Date currDateEnd;

    /**
     * 订单修改起始时间
     */
    private Date modifyStart;

    /**
     * 订单修改截止时间
     */
    private Date modifyEnd;

    /**
     * 订单状态
     */
    private List<Integer> status;

    private String cpCode;

    /**
     * res_code的组成为：fromAppKey_orderChannels，通过res_code 可like 'fromAppKey'进行查询
     */
    private String fromAppKey;

    /**
     * 渠道来源
     */
    private String resCode;

    private Integer bizType;

    private Date appointGotStartTime;

    private Date appointGotEndTime;

    private String orderChannels;

    private String mailNo;

    private List<String> mailNoList;

    private List<String> outOrderCodeList;

    private String sendProvince;

    private String consigneeProvince;

    private String sendMobile;

    private List<String> cpCodeList;

    private List<String> orderChannelsList;

    /**
     * 注意这里不是页码，而是起始下标位置
     */
    private Integer pageIndex;

    /**
     * 页长
     */
    private Integer pageSize;

    private Integer switchCpStatus;

    /**
     * 物流状态
     */
    private List<String> logisticsStatusList;

    /**
     * 扩展字段搜索key
     */
    private String featureKey;

    /**
     * 状态白名单
     */
    private List<Integer> statusWhiteList;
}
