package com.cainiao.waybill.bridge.enterprise.printer.controller;

import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.printer.request.EnterprisePrinterRequest;
import com.cainiao.waybill.bridge.enterprise.printer.service.EnterprisePrinterService;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WayBillEnterprisePrinterDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.ArrayList;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EnterprisePrinterControllerTest {
    private static final EnterprisePrinterRequest VALID_REQUEST = new EnterprisePrinterRequest();
    private static final EnterprisePrinterRequest INVALID_REQUEST = new EnterprisePrinterRequest();
    private static final EnterprisePrinterRequest EXCEPTION_REQUEST = new EnterprisePrinterRequest();
    private static final BridgePagingDTO<WayBillEnterprisePrinterDTO> VALID_PAGING_DTO = new BridgePagingDTO<>();

    private static final BridgePagingDTO<WayBillEnterprisePrinterDTO> EMPTY_PAGING_DTO = new BridgePagingDTO<>();
    @InjectMocks
    private EnterprisePrinterController enterprisePrinterController;
    @Mock
    private EnterprisePrinterService enterprisePrinterService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // 初始化设置模拟service层方法的行为 - getListByLocationName返回List，不是void方法
        when(enterprisePrinterService.getListByLocationName(anyString(), anyString())).thenReturn(new ArrayList<>());
    }

    /**
     * 测试正常情况下分页查询打印机列表
     */
    @Test
    public void testPageSuccess() throws Exception {
        // 模拟正常分页查询
        when(enterprisePrinterService.pageList(VALID_REQUEST)).thenReturn(VALID_PAGING_DTO);
        EnterpriseBaseResult<BridgePagingDTO<WayBillEnterprisePrinterDTO>> result = invokePrivatePage(VALID_REQUEST);
        // 验证查询成功
        assertTrue(result.isSuccess());
        assertEquals(VALID_PAGING_DTO, result.getData());
    }

    /**
     * 测试查询结果为空的情况
     */
    @Test
    public void testPageEmptyResult() throws Exception {
        // 模拟查询结果为空
        when(enterprisePrinterService.pageList(INVALID_REQUEST)).thenReturn(EMPTY_PAGING_DTO);
        EnterpriseBaseResult<BridgePagingDTO<WayBillEnterprisePrinterDTO>> result = invokePrivatePage(INVALID_REQUEST);
        // 验证查询结果为空
        assertTrue(result.isSuccess());
        assertEquals(EMPTY_PAGING_DTO, result.getData());
    }

    /**
     * 测试服务层抛出异常的情况
     */
    @Test(timeout = 5000)
    public void testPageException() throws Exception {
        // 模拟服务层抛出异常
        when(enterprisePrinterService.pageList(EXCEPTION_REQUEST)).thenThrow(new RuntimeException("System error"));
        EnterpriseBaseResult<BridgePagingDTO<WayBillEnterprisePrinterDTO>> result = null;
        try {
            result = invokePrivatePage(EXCEPTION_REQUEST);
        } catch (Exception e) {
            // 处理异常
            assertEquals("System error", e.getMessage());
        }
        if (result != null) {
            // 验证结果为空，因为异常被正确捕获并处理
            assertNull(result.getData());
        }
    }

    private EnterpriseBaseResult<BridgePagingDTO<WayBillEnterprisePrinterDTO>> invokePrivatePage(EnterprisePrinterRequest request) throws Exception {
        Method method = EnterprisePrinterController.class.getDeclaredMethod("page", EnterprisePrinterRequest.class);
        method.setAccessible(true);
        return (EnterpriseBaseResult<BridgePagingDTO<WayBillEnterprisePrinterDTO>>) method.invoke(enterprisePrinterController, request);
    }

    /**
     * 测试获取验证码成功的情况
     */
    @Test
    public void testGetVerifyCodeSuccess() throws Exception {
        // 反射调用私有方法
        EnterpriseBaseResult<Void> result = invokePrivateGetVerifyCode("printer123");
        // 验证调用成功
        assertTrue(result.isSuccess());
        // 验证返回数据为空
        assertNull(result.getData());
    }

    /**
     * 测试获取验证码失败抛出异常的情况
     */
    @Test
    public void testGetVerifyCodeException() throws Exception {
        // 模拟service层抛出异常
        doThrow(new RuntimeException("System error")).when(enterprisePrinterService).getVerifyCode("printer123");
        EnterpriseBaseResult<Void> result = invokePrivateGetVerifyCode("printer123");
        // 验证调用不成功
        assertFalse(result.isSuccess());
        // 验证返回错误码
        assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), result.getErrorCode());
        // 验证返回错误信息
        assertEquals("打印验证码失败", result.getErrorMsg());
    }

    /**
     * 使用反射调用EnterprisePrinterController中的私有方法getVerifyCode
     *
     * @param printerId 打印机ID
     * @return 调用结果
     * @throws Exception 反射中可能出现的异常
     */
    private EnterpriseBaseResult<Void> invokePrivateGetVerifyCode(String printerId) throws Exception {
        Method method = EnterprisePrinterController.class.getDeclaredMethod("getVerifyCode", String.class);
        method.setAccessible(true);
        return (EnterpriseBaseResult<Void>) method.invoke(enterprisePrinterController, printerId);
    }

    /**
     * 测试打印机编辑成功的情况
     */
    @Test
    public void testEditPrinterSuccess() throws Exception {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setPrinterId("printer123");

        // 通过反射调用私有方法
        EnterpriseBaseResult<Boolean> result = invokePrivateEditPrinter(request);

        // 验证调用成功 (只要没有抛出异常且返回结果不为null就认为成功)
        assertNotNull(result);
        // 检查结果是成功的
        assertTrue("编辑打印机应该成功", result.isSuccess());
    }

    /**
     * 测试打印机编辑失败抛出异常的情况
     */
    @Test
    public void testEditPrinterException() throws Exception {
        EnterprisePrinterRequest request = new EnterprisePrinterRequest();
        request.setPrinterId("printer123");

        // 模拟service层抛出异常
        doThrow(new RuntimeException("System error")).when(enterprisePrinterService).updatePrinter(request);

        // 反射调用私有方法
        EnterpriseBaseResult<Boolean> result = invokePrivateEditPrinter(request);

        // 验证调用不成功
        assertFalse(result.isSuccess());
        // 验证返回错误码
        assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), result.getErrorCode());
        // 验证返回错误信息
        assertEquals("打印机编辑失败", result.getErrorMsg());
    }

    private EnterpriseBaseResult<Boolean> invokePrivateEditPrinter(EnterprisePrinterRequest request) throws Exception {
        Method method = EnterprisePrinterController.class.getDeclaredMethod("editPrinter", EnterprisePrinterRequest.class);
        method.setAccessible(true);
        return (EnterpriseBaseResult<Boolean>) method.invoke(enterprisePrinterController, request);
    }

    /**
     * 测试根据打印机ID获取打印机成功的情况
     */
    @Test
    public void testGetPrinterByIdSuccess() throws Exception {
        Long validPrinterId = 123L;
        WayBillEnterprisePrinterDTO expectedDTO = mock(WayBillEnterprisePrinterDTO.class);
        // 模拟service返回成功的打印机信息
        when(enterprisePrinterService.getPrinterById(validPrinterId)).thenReturn(expectedDTO);

        // 反射调用私有方法
        EnterpriseBaseResult<WayBillEnterprisePrinterDTO> result = invokePrivateGetPrinterById(validPrinterId);
        // 验证调用成功
        assertTrue(result.isSuccess());
        // 验证返回数据与预期一致
        assertEquals(expectedDTO, result.getData());
    }

    /**
     * 测试根据打印机ID获取打印机失败抛出异常的情况
     */
    @Test
    public void testGetPrinterByIdException() throws Exception {
        Long invalidPrinterId = 123L;
        // 模拟service层抛出异常
        doThrow(new RuntimeException("System error")).when(enterprisePrinterService).getPrinterById(invalidPrinterId);
        // 反射调用私有方法
        EnterpriseBaseResult<WayBillEnterprisePrinterDTO> result = invokePrivateGetPrinterById(invalidPrinterId);
        // 验证调用不成功
        assertFalse(result.isSuccess());
        // 验证返回错误码
        assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), result.getErrorCode());
        // 验证返回抛出的错误信息
        assertEquals("System error", result.getErrorMsg());
    }

    // 工具方法：通过反射调用控制器私有方法getPrinterById
    private EnterpriseBaseResult<WayBillEnterprisePrinterDTO> invokePrivateGetPrinterById(Long id) throws Exception {
        Method method = EnterprisePrinterController.class.getDeclaredMethod("getPrinterById", Long.class);
        method.setAccessible(true);
        return (EnterpriseBaseResult<WayBillEnterprisePrinterDTO>) method.invoke(enterprisePrinterController, id);
    }

    /**
     * 测试根据场地名称获取打印机成功的情况
     */
    @Test
    public void testGetPrinterListByLocationNameSuccess() throws Exception {
        String validLocationName = "ValidLocation";
        String validCorpId = "123";
        List<WayBillEnterprisePrinterDTO> expectedList = Collections.singletonList(mock(WayBillEnterprisePrinterDTO.class));
        // 模拟service返回成功的打印机列表
        when(enterprisePrinterService.getListByLocationName(validLocationName, validCorpId)).thenReturn(expectedList);
        // 反射调用私有方法
        EnterpriseBaseResult<List<WayBillEnterprisePrinterDTO>> result = invokePrivateGetPrinterListByLocationName(validLocationName, validCorpId);
        // 验证调用成功
        assertTrue(result.isSuccess());
        // 验证返回数据与预期一致
        assertEquals(expectedList, result.getData());
    }

    /**
     * 测试根据场地名称获取打印机抛出异常的情况
     */
    @Test
    public void testGetPrinterListByLocationNameException() throws Exception {
        String invalidLocationName = "InvalidLocation";
        String validCorpId = "123";
        // 模拟service层抛出异常
        doThrow(new RuntimeException("System error")).when(enterprisePrinterService).getListByLocationName(invalidLocationName, validCorpId);

        // 反射调用私有方法
        EnterpriseBaseResult<List<WayBillEnterprisePrinterDTO>> result = invokePrivateGetPrinterListByLocationName(invalidLocationName, validCorpId);
        // 验证调用不成功
        assertFalse(result.isSuccess());
        // 验证返回错误码
        assertEquals(EnterpriseErrorEnum.SYSTEM_ERROR.code(), result.getErrorCode());
        // 验证返回抛出的错误信息
        assertEquals("System error", result.getErrorMsg());
    }

    private EnterpriseBaseResult<List<WayBillEnterprisePrinterDTO>> invokePrivateGetPrinterListByLocationName(String locationName, String corpId) throws Exception {
        Method method = EnterprisePrinterController.class.getDeclaredMethod("getPrinterListByLocationName", String.class, String.class);
        method.setAccessible(true);
        return (EnterpriseBaseResult<List<WayBillEnterprisePrinterDTO>>) method.invoke(enterprisePrinterController, locationName, corpId);
    }
}