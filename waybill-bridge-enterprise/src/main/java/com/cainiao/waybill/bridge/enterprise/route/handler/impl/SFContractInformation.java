package com.cainiao.waybill.bridge.enterprise.route.handler.impl;

import com.alibaba.fastjson.JSONObject;
import com.cainiao.waybill.bridge.biz.pickup.dto.sf.response.SFPrintData;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpFeatureUtil;
import com.cainiao.waybill.bridge.enterprise.common.constant.EnterpriseOrderFeatureConstant;
import com.cainiao.waybill.bridge.enterprise.common.enums.ExpressTypeEnum;
import com.cainiao.waybill.bridge.enterprise.route.handler.ContractInformationBuildMethod;
import com.cainiao.waybill.bridge.enterprise.route.handler.ContractInformationBuild;
import com.cainiao.waybill.bridge.enterprise.utils.AddressUtils;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseOrderDO;
import com.cainiao.waybill.common.util.FeatureUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/7
 **/
@Component
@ContractInformationBuildMethod(expressType = ExpressTypeEnum.SF)
public class SFContractInformation implements ContractInformationBuild {

    @Override
    public String build(WaybillBridgeEnterpriseOrderDO enterpriseOrderDO) {
        JSONObject jsonObject = new JSONObject();

        JSONObject recipient = new JSONObject();
        JSONObject recipientAddress = new JSONObject();
        recipientAddress.put("city", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getCity());
        recipientAddress.put("detail", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getAddressDetail());
        recipientAddress.put("district", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getArea());
        recipientAddress.put("province", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getProvince());
        recipientAddress.put("town", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getTown());
        recipient.put("address", recipientAddress);
        recipient.put("mobile", enterpriseOrderDO.getConsigneeMobile());
        recipient.put("name", enterpriseOrderDO.getConsigneeName());
        recipient.put("phone", enterpriseOrderDO.getConsigneePhone());
        jsonObject.put("recipient", recipient);

        JSONObject sender = new JSONObject();
        JSONObject sendAddress = new JSONObject();
        sendAddress.put("city", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getCity());
        sendAddress.put("detail", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getAddressDetail());
        sendAddress.put("district", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getArea());
        sendAddress.put("province", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getProvince());
        sendAddress.put("town", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getTown());
        sender.put("address", sendAddress);
        sender.put("mobile", enterpriseOrderDO.getSenderMobile());
        sender.put("name", enterpriseOrderDO.getSenderName());
        sender.put("phone", enterpriseOrderDO.getSenderPhone());
        jsonObject.put("sender", sender);

        jsonObject.put("waybillCode", enterpriseOrderDO.getWaybillCode());

        if (StringUtils.isNotBlank(enterpriseOrderDO.getFeature())) {
            Map<String, String> featureMap =  FeatureUtils.parseFromString(enterpriseOrderDO.getFeature());
            String dataString = featureMap.get(EnterpriseOrderFeatureConstant.SF_PRINT_DATA);
            if (StringUtils.isNotBlank(dataString)) {
                SFPrintData sfPrintData = JSONObject.parseObject(dataString, SFPrintData.class);
                JSONObject routingExtraInfo = new JSONObject();
                routingExtraInfo.put("codingMapping", sfPrintData.getCodingMapping());
                routingExtraInfo.put("peakCusCode", sfPrintData.getCodingMappingOut());
                routingExtraInfo.put("destRouteLabel", sfPrintData.getDestRouteLabel());
                routingExtraInfo.put("twoDimensionCode", sfPrintData.getTwoDimensionCode());
                routingExtraInfo.put("abFlag", sfPrintData.getAbFlag());
                routingExtraInfo.put("destAddrKeyWord", sfPrintData.getDestAddKeyWord());
                routingExtraInfo.put("proCode", sfPrintData.getProCode());
                routingExtraInfo.put("proName", sfPrintData.getProName());
                jsonObject.put("routingExtraInfo", routingExtraInfo);
            }
        }
        return jsonObject.toString();
    }
}
