package com.cainiao.waybill.bridge.innersupport.ad.biz.response.common;

import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.AdPagingInfo;
import lombok.Data;

import java.util.List;

/**
 * @Description 分页查询响应参数
 */
@Data
public class AdPagingResponse<T>{

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 数据
     */
    private List<T> tableData;

    /**
     * 分页信息
     */
    private AdPagingInfo paging;

    public static <T> AdPagingResponse<T> buildError(String errorMsg) {
        AdPagingResponse<T> response = new AdPagingResponse<>();
        response.setSuccess(false);
        response.setErrorMsg(errorMsg);
        return response;
    }

    public static <T> AdPagingResponse<T> build(List<T> tableData, long totalCount, Integer currentPage, Integer pageSize){
        AdPagingResponse<T> response = new AdPagingResponse<>();
        response.setSuccess(true);
        response.setTableData(tableData);
        AdPagingInfo pageInfo = new AdPagingInfo();
        pageInfo.setCurrentPage(currentPage);
        pageInfo.setPageSize(pageSize);
        pageInfo.setTotalCount(Math.toIntExact(totalCount));
        response.setPaging(pageInfo);
        return response;
    }

    public static <T> AdPagingResponse<T> build(List<T> tableData, AdPagingInfo pagingInfo){
        AdPagingResponse<T> response = new AdPagingResponse<>();
        response.setSuccess(true);
        response.setTableData(tableData);
        AdPagingInfo paging = new AdPagingInfo();
        paging.setCurrentPage(pagingInfo.getCurrentPage());
        paging.setPageSize(pagingInfo.getPageSize());
        paging.setTotalCount(pagingInfo.getTotalCount());
        response.setPaging(paging);
        return response;
    }

}
