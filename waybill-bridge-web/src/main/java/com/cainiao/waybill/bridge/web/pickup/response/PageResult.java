package com.cainiao.waybill.bridge.web.pickup.response;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/14 下午8:23
 */
@Getter
@Setter
public class PageResult<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<T> items;
    /**
     * 第几页
     */
    private Integer currentPage;
    /**
     * 每页大小
     */
    private Integer pageSize;
    /**
     * 总条数
     */
    private Integer totalCount;

    public int getTotalPage() {
        return totalCount % pageSize == 0 ? totalCount / pageSize : (totalCount / pageSize + 1);
    }
}
