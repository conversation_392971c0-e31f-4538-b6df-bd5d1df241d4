package com.cainiao.waybill.bridge.enterprise.common;

import lombok.Data;

import java.io.Serializable;


/**
 * 该类用于进行分页操作的基本数据处理。
 * <AUTHOR>
 * @date 2025-04-15 17:50:45
 */
@Data
public class Paging extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -7647608744976962759L;
    /**
     * 当前页面
     */
    private Integer currentPage;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总数
     */
    private Integer totalCount;
}
