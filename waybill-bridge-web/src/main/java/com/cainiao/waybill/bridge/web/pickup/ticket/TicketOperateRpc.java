package com.cainiao.waybill.bridge.web.pickup.ticket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.ObjectMetadata;
import com.cainiao.waybill.bridge.biz.pickup.constants.AsyncTaskEnum;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketConstant;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpTicketConstant.TicketCreateMode;
import com.cainiao.waybill.bridge.biz.pickup.dto.ticket.ExternTicketReplyDTO;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.manager.WaybillPickUpTicketManager;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillPickUpTicketService;
import com.cainiao.waybill.bridge.biz.ticket.constants.PickUpTicketDealPartEnum;
import com.cainiao.waybill.bridge.biz.utils.pickup.OssUtils;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpTicketUtil;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpTaskDO;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpTicketDO;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpTicketReplyDO;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpTaskMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpTicketMapper;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpTicketReplyMapper;
import com.cainiao.waybill.bridge.web.common.dto.BaseResult;
import com.cainiao.waybill.bridge.web.common.util.ExeclUtil;
import com.cainiao.waybill.bridge.web.pickup.response.TicketRole;
import com.cainiao.waybill.bridge.web.pickup.ticket.role.TicketRoleUtil;
import com.cainiao.waybill.bridge.web.pickup.ticket.task.TaskThreadPool;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.mtop.api.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/15 下午8:07
 */
@RestController
@Slf4j
@RequestMapping("/ticket")
public class TicketOperateRpc {
    @Resource
    private WaybillPickUpTicketMapper waybillPickUpTicketMapper;
    @Resource
    private WaybillPickUpTicketReplyMapper waybillPickUpTicketReplyMapper;
    @Resource
    private WaybillPickUpOrderManager waybillPickUpOrderManager;
    @Resource
    private TransactionTemplate bridgeTransactionTemplate;
    @Resource
    private WaybillPickUpTaskMapper waybillPickUpTaskMapper;

    @Resource
    private WaybillPickUpTicketService pickUpTicketService;

    @Resource
    private WaybillPickUpTicketManager pickUpTicketManager;


    @PostMapping("/new")
    public BaseResult<Void> create(
            @RequestParam() String mailNo
            , @RequestParam() String content
            , @RequestParam() String ticketType
            , @RequestParam() String[] fileUrls) throws Exception {
        WaybillPickUpDetailDO pickUpDetailDO = waybillPickUpOrderManager.get(mailNo);
        if (pickUpDetailDO == null) {
            return BaseResult.bizFail("", "运单号不存在");
        }
        WaybillPickUpTicketDO ticketDO = new WaybillPickUpTicketDO();
        ticketDO.setMailNo(mailNo);
        ticketDO.setContent(content);
        ticketDO.setTicketType(ticketType);
        ticketDO.setCpCode(pickUpDetailDO.getCpCode());
        ticketDO.setSource(pickUpDetailDO.getOrderChannels());
        ticketDO.setOuterOrderCode(pickUpDetailDO.getOuterOrderCode());
        ticketDO.setSenderMobile(pickUpDetailDO.getSendMobile());
        ticketDO.setCnOrderId(String.valueOf(pickUpDetailDO.getId()));
        ticketDO.setOrderSource(pickUpDetailDO.getOrderChannels());
        JSONObject extraInfo = new JSONObject();

        if (fileUrls != null && fileUrls.length > 0 && !"null".equals(fileUrls)) {
            extraInfo.put(PickUpTicketConstant.ExtraInfoKey.FILE_URLS, StringUtils.join(fileUrls, ","));
        }
        insert(ticketDO, extraInfo);
        return BaseResult.success();
    }

    @PostMapping("/attachment")
    public BaseResult<Map<String, String>> uploadImg(@RequestParam() MultipartFile file) {
        String ossFileName = PickUpTicketConstant.TICKET_IMG_OSS_DIR + "/" + System.currentTimeMillis() + "_" + file.getOriginalFilename();
        try (InputStream in = file.getInputStream()) {
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setObjectAcl(CannedAccessControlList.PublicRead);
            metadata.setExpirationTime(DateUtils.addDays(new Date(), 15));
            String imgUrl = OssUtils.uploadToOSS(ossFileName, in, metadata);
            Map<String, String> uploadMap = Maps.newHashMap();
            uploadMap.put("url", imgUrl);
            return BaseResult.success(uploadMap);
        } catch (Throwable th) {
            log.error(th.getMessage(), th);
            return BaseResult.bizFail("", th.getMessage());
        }
    }

    @PostMapping("/import")
    @SneakyThrows
    public BaseResult<Map<String, String>> importTickets(@RequestParam() MultipartFile file) {
        WaybillPickUpTaskDO taskDO = new WaybillPickUpTaskDO();
        taskDO.setStatus(AsyncTaskEnum.doing.name());
        waybillPickUpTaskMapper.insert(taskDO);
        Map<String, String> taskMap = com.google.common.collect.Maps.newHashMap();
        taskMap.put("taskId", taskDO.getId().toString());
        TaskThreadPool.getInstance().execute(() -> {
            try {
                List<String[]> lines = ExeclUtil.loadData(file.getInputStream());
                List<WaybillPickUpTicketDO> ticketDOList = Lists.newArrayList();
                for (String[] cols : lines) {
                    WaybillPickUpTicketDO ticketDO = new WaybillPickUpTicketDO();
                    ticketDO.setMailNo(cols[0]);
                    WaybillPickUpDetailDO pickUpDetailDO = waybillPickUpOrderManager.get(ticketDO.getMailNo());
                    if (pickUpDetailDO == null) {
                        throw new RuntimeException("运单号不存在");
                    }
                    ticketDO.setCpCode(pickUpDetailDO.getCpCode());
                    ticketDO.setSource(pickUpDetailDO.getOrderChannels());
                    ticketDO.setTicketType(PickUpTicketUtil.getTicketTypeValueWithName(cols[1]));
                    ticketDO.setContent(cols[2]);
                    ticketDO.setSenderMobile(pickUpDetailDO.getSendMobile());
                    ticketDO.setCnOrderId(String.valueOf(pickUpDetailDO.getId()));
                    ticketDO.setOrderSource(pickUpDetailDO.getOrderChannels());
                    ticketDOList.add(ticketDO);
                }
                ticketDOList.forEach(s -> {
                    insert(s, null);
                });
                taskDO.setStatus(AsyncTaskEnum.done.name());
                waybillPickUpTaskMapper.updateByPrimaryKey(taskDO);
                log.error("ticketTask:" + JSON.toJSONString(taskDO));
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
                taskDO.setStatus(AsyncTaskEnum.fail.name());
                taskDO.setMsg(e.getMessage());
                log.error("ticketTask:" + JSON.toJSONString(taskDO));
                waybillPickUpTaskMapper.updateByPrimaryKey(taskDO);
            }
        });
        return BaseResult.success(taskMap);
    }

//    /**
//     * reply回复 done完结 punish标记处罚 cancel取消 updateWeigth修改重量
//     */
//    @PostMapping("/label")
//    public BaseResult<Void> label(@RequestParam String ids, @RequestParam String labelContent
//            , @RequestParam(required = false) String content, @RequestParam(required = false) String imgUrls) {
//
//        Map<String, Object> param = Maps.newHashMap();
//        param.put("ids", ids.split(","));
//        param.put("ticketStatus", PickUpTicketConstant.TicketStatus.PROCESSING.name());
//
//        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
//
//        //回复工单
//        if ("reply".equals(labelContent)) {
//            for (Long id : idList) {
//                WaybillPickUpTicketDO ticketDO = waybillPickUpTicketMapper.selectByPrimaryKey(id);
//                if (PickUpTicketConstant.TicketStatus.DONE.name().equals(ticketDO.getTicketStatus())) {
//                    return BaseResult.bizFail(null, ticketDO.getMailNo() + ",已完结工单不能回复");
//                }
//            }
//            boolean dbResult = bridgeTransactionTemplate.execute(transactionStatus -> {
//                try {
//                    for (Long id : idList) {
//                        addReply(id, content, imgUrls);
//                    }
//                    //工单状态更新为处理中
//                    waybillPickUpTicketMapper.updateByIds(param);
//                    //  对外部推送工单回复内容
//                    replyTicketToExternPlatform(idList, PickUpTicketConstant.TicketStatus.PROCESSING, content);
//                } catch (Throwable e) {
//                    transactionStatus.setRollbackOnly();
//                    PickUpLogUtil.errLog(ids, PickUpTicketConstant.LOG_ACTION, "UNEXPECT_ERROR", e.getMessage(), e);
//                    return false;
//                }
//                return true;
//            });
//            if (!dbResult) {
//                return BaseResult.systemFail();
//            }
//            return BaseResult.success();
//        }
//
//        TicketRole role = TicketRoleUtil.currentRole();
//        if (!checkPermission(labelContent, role)) {
//            return BaseResult.bizFail(null, "操作权限不足");
//        }
//
//        //处罚
//        if ("punish".equals(labelContent)) {
//            param.put("isPunish", 1);
//            waybillPickUpTicketMapper.updateByIds(param);
//            return BaseResult.success();
//        }
//        //取消
//        if ("cancel".equals(labelContent)) {
//            param.put("isCancel", 1);
//            waybillPickUpTicketMapper.updateByIds(param);
//            return BaseResult.success();
//        }
//        //更新重量
//        if ("updateWeigth".equals(labelContent)) {
//            param.put("isUpdateWeigth", 1);
//            waybillPickUpTicketMapper.updateByIds(param);
//            return BaseResult.success();
//        }
//        //完结
//        if ("done".equals(labelContent)) {
//            param.put("ticketStatus", PickUpTicketConstant.TicketStatus.DONE.name());
//            boolean dbResult = bridgeTransactionTemplate.execute(transactionStatus -> {
//                try {
//                    //  对外部推送工单回复内容
//                    if(StringUtils.isNotBlank(content)){
//                        idList.forEach(id->{
//                            addReply(id, content, imgUrls);
//                            PickUpLogUtil.info(id + " doneprocess addReply");
//                        });
//                    }
//                    waybillPickUpTicketMapper.updateByIds(param);
//                    PickUpLogUtil.info(idList + " doneprocess updateByIds");
//                    replyTicketToExternPlatform(idList, PickUpTicketConstant.TicketStatus.DONE, content);
//                    PickUpLogUtil.info(idList + " doneprocess replyTicketToExternPlatform");
//                } catch (Throwable e) {
//                    transactionStatus.setRollbackOnly();
//                    PickUpLogUtil.errLog(ids, PickUpTicketConstant.LOG_ACTION + "_DONE", "UNEXPECT_ERROR", e.getMessage(), e);
//                    return false;
//                }
//                return true;
//            });
//            if (!dbResult) {
//                return BaseResult.systemFail();
//            }
//            return BaseResult.success();
//        }
//        //有效回复
//        if("effectiveReply".equals(labelContent)){
//            boolean dbResult = bridgeTransactionTemplate.execute(transactionStatus -> {
//                try {
//                    idList.forEach(id->{
//                        pickUpTicketManager.addTag(id, PickUpTicketConstant.ExtraInfoKey.TAG_EFFECTIVE_REPLY);
//                        if(StringUtils.isNotBlank(content)) {
//                            addReply(id, content, imgUrls);
//                        }
//                        waybillPickUpTicketMapper.updateByIds(param);
//                    });
//                } catch (Throwable e) {
//                    transactionStatus.setRollbackOnly();
//                    PickUpLogUtil.errLog(ids, PickUpTicketConstant.LOG_ACTION, "UNEXPECT_ERROR", e.getMessage(), e);
//                    return false;
//                }
//                return true;
//            });
//            if (!dbResult) {
//                return BaseResult.systemFail();
//            }
//            return BaseResult.success();
//        }
//        //破损
//        if("broken".equals(labelContent)){
//            idList.forEach(id->{
//                pickUpTicketManager.addTag(id,PickUpTicketConstant.ExtraInfoKey.TAG_BROKEN);
//            });
//            return BaseResult.success();
//        }
//        //遗失
//        if("loss".equals(labelContent)){
//            idList.forEach(id->{
//                pickUpTicketManager.addTag(id,PickUpTicketConstant.ExtraInfoKey.TAG_LOSS);
//            });
//            return BaseResult.success();
//        }
//
//        return BaseResult.success();
//    }

    private void addReply(Long id, String content, String imgUrls){
        WaybillPickUpTicketReplyDO replyDO = new WaybillPickUpTicketReplyDO();
        replyDO.setTicketId(id);
        replyDO.setContent(content);
        replyDO.setImgUrls(imgUrls);
        waybillPickUpTicketReplyMapper.insert(replyDO);
    }

    private boolean checkPermission(String labelContent, TicketRole role) {
        if (PickUpTicketConstant.RoleType.cainiao.name().equals(role.getRoleType())) {
            return true;
        }
        if ("done".equals(labelContent) && PickUpTicketConstant.RoleType.platform.name().equals(role.getRoleType())) {
            return true;
        }
        return false;
    }

    private void insert(WaybillPickUpTicketDO ticketDO, JSONObject extraInfo) {
        ticketDO.setTicketStatus(PickUpTicketConstant.TicketStatus.NEW.name());
        if (extraInfo == null) {
            extraInfo = new JSONObject();
        }
        String ticketPrimarySource = "";
        TicketRole role = TicketRoleUtil.currentRole();
        if (role.getRoleType().equals(PickUpTicketConstant.RoleType.cp.name())) {
            ticketPrimarySource = "CP工单";
            ticketDO.setPrimarySource(PickUpTicketConstant.RoleType.cp.name());
        }else if (role.getRoleType().equals(PickUpTicketConstant.RoleType.cainiao.name())) {
            ticketPrimarySource = "菜鸟小二工单";
            ticketDO.setPrimarySource(PickUpTicketConstant.RoleType.cainiao.name());
        }
        extraInfo.put(PickUpTicketConstant.ExtraInfoKey.TICKET_PRIMARY_SOURCE, ticketPrimarySource);
        ticketDO.setExtraInfo(extraInfo.toJSONString());
        ticketDO.setPriority(1);
        ticketDO.setCreateMode(TicketCreateMode.manual.name());
        ticketDO.setDealPart(PickUpTicketDealPartEnum.CN.name());
        pickUpTicketManager.insert(ticketDO, null);
    }

    /**
     * 向外部推送工单回复内容
     *
     * @param ticketIds  : 菜鸟内部DB主键id
     * @param pushStatus : 对外推送的状态
     * @param content    : 工单回复内容
     */
    private void replyTicketToExternPlatform(List<Long> ticketIds, PickUpTicketConstant.TicketStatus pushStatus, String content) {
        if (CollectionUtils.isEmpty(ticketIds) || StringUtil.isBlank(content)) {
            return;
        }
        for (Long ticketId : ticketIds) {
            if (ticketId == null) {
                continue;
            }
            ExternTicketReplyDTO ticketReplyDTO = new ExternTicketReplyDTO();
            ticketReplyDTO.setTicketId(ticketId);
            ticketReplyDTO.setReplyContent(content);
            ticketReplyDTO.setStatus(pushStatus.name());
            pickUpTicketService.reply(ticketReplyDTO);
        }
    }
}
