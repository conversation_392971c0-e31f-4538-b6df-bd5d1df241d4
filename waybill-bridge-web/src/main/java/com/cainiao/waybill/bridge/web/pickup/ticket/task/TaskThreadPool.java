package com.cainiao.waybill.bridge.web.pickup.ticket.task;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/4/22 下午5:28
 */
public class TaskThreadPool {
    final static ThreadPoolExecutor INSTANCE = new ThreadPoolExecutor(1, 3, 0L, TimeUnit.SECONDS, new ArrayBlockingQueue<>(16));

    public static ThreadPoolExecutor getInstance() {
        return TaskThreadPool.INSTANCE;
    }
}
