package com.cainiao.waybill.bridge.enterprise.outbound.service;

import com.cainiao.waybill.bridge.enterprise.outbound.request.EnterprisePackBoundRequest;
import com.cainiao.waybill.bridge.enterprise.outbound.response.PackCpInfo;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeEnterprisePackBoundDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/5/17
 **/
public interface WaybillBridgeEnterprisePackBoundService {


    /**
     * 到件分页
     * @param request 查询条件
     * @return 分页结果
     */
    BridgePagingDTO<WaybillBridgeEnterprisePackBoundDTO> outboundPage(EnterprisePackBoundRequest request);


    /**
     * 入库
     * @param request 入库条件
     */
    int expressInStore(EnterprisePackBoundRequest request);


    /**
     * 批量取件
     * @param request 批量取件条件
     * @return 批量取件异常结果
     */
    List<String> batchPickUp(EnterprisePackBoundRequest request);

    /**
     * 根据运单号查询可取件的cp
     * @param waybillCode 运单号
     * @return 可取件的cp
     */
    PackCpInfo queryPossibleCpCodesByWaybillCode(String waybillCode);

    /**
     * 查询switch配置，获取当前支持的cpCode和cpName
     * @return 可取件的cp
     */
    Map<String, String> supportCpMapping();
}
