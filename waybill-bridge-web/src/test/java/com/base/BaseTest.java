package com.base;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

/**
 * 单测基础类，勿删
 * Created By Thub IDEA插件
 *
 * <AUTHOR>
 * @date 2024/5/14 下午2:48
 **/
public class BaseTest {
    protected static String readJson(String filePath) {
        try {
            File file = new File(BaseTest.class.getClassLoader()
                .getResource(filePath).getFile());
            return String.join("", Files.readAllLines(file.toPath()));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
