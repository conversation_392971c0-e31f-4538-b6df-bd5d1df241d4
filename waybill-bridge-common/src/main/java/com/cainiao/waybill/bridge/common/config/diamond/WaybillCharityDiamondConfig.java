package com.cainiao.waybill.bridge.common.config.diamond;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.alibaba.common.lang.ExceptionUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.common.constants.WaybillCharityConstants.DiamondConfig;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2022/07/26-下午3:16
 */
public class WaybillCharityDiamondConfig {

    public static final Logger infoLogger = LoggerFactory.getLogger("WAYBILL_PICKUP_INFO");

    private static WaybillCharityConfig CONFIG = new WaybillCharityConfig();

    private static void initConfig() {
        // 启动只用一次场景，直接get获取配置值
        try {
            String configInfo = Diamond
                .getConfig(DiamondConfig.WAYBILL_CHARITY_CONFIG_DATA_ID,
                    DiamondConfig.WAYBILL_CHARITY_CONFIG_GROUP_ID, 3000);
            parseConfig(configInfo);
        } catch (IOException e1) {
            infoLogger.info("获取diamond中裹裹配置信息：" + ExceptionUtil.getStackTrace(e1));
        }
        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(DiamondConfig.WAYBILL_CHARITY_CONFIG_DATA_ID,
            DiamondConfig.WAYBILL_CHARITY_CONFIG_GROUP_ID,
            new ManagerListenerAdapter() {
                @Override
                public void receiveConfigInfo(String configInfo) {
                    try {
                        parseConfig(configInfo);
                    } catch (Exception e) {
                        infoLogger.info(
                            "ManagerListenerAdapter获取diamond中壹基金配置信息：" + ExceptionUtil.getStackTrace(e));
                    }
                }
            });
    }

    public static WaybillCharityConfig getConfig() {
        return CONFIG;
    }

    static {
        initConfig();
    }

    private static void parseConfig(String configInfo) {
        if (StringUtil.isBlank(configInfo)) {
            return;
        }
        CONFIG = JSONObject.parseObject(configInfo, WaybillCharityConfig.class);
    }

    @Data
    public static class WaybillCharityConfig {

        private List<WaybillCharityProject> projects;

        private Map<String, String> projectPrintTemplateMap;

        private String easPictureAnalysisToken;

        private String easPictureAnalysisVipHost;

        private Map<String, List<String>> addressTagConfig;

        private Map<String, String> importTemplates;

        private Map<String, String> mockAccountId;

        private String ocrDefaultSignName;
    }

    @Data
    public static class WaybillCharityProject {

        private String project;

        private String name;

        private String shortCode;

        private String charityCode;

        private String charityName;

    }
}
