package com.cainiao.waybill.bridge.biz.hsf.consumer;

import com.cainiao.cnlogin.interceptor.LoginContextInterceptor;
import com.cainiao.cnlogin.interceptor.SecurityCheckInterceptor;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

/**
 * <AUTHOR>
 * @date 2022/4/20 下午9:54
 */
@Configuration
public class CnLoginConfig extends WebMvcConfigurerAdapter {

    private LoginContextInterceptor loginContextInterceptor;

    private SecurityCheckInterceptor securityCheckInterceptor;

    @Bean
    public LoginContextInterceptor getLoginContextInterceptor() {
        loginContextInterceptor = new LoginContextInterceptor();
        //日常为".cainiao.test",预发线上为".cainiao.com"
        //新加坡预发线上为"sg.cainiao.com" 俄罗斯预发线上为"ru.cainiao.com"
        //德国预发线上为"de.cainiao.com"
        loginContextInterceptor.setCookieDomain(".cainiao.com");
        loginContextInterceptor.setUpdateCookieInterval(20);
        //如果你的应用使用的是云径版本的潘多拉,则storage=cloudpath
        loginContextInterceptor.setStorage("normal");

        return loginContextInterceptor;
    }


    @Bean
    public SecurityCheckInterceptor getSecurityCheckInterceptor() {
        securityCheckInterceptor = new SecurityCheckInterceptor();
        /* 回跳地址，一般配置为菜鸟登录页地址
           日常"http://cnlogin.cainiao.test/login
           预发线上"https://cnlogin.cainiao.com/login"
           新加坡预发线上"https://cnlogin.sg.cainiao.com/login"
           俄罗斯预发线上"https://cnlogin.ru.cainiao.com/login"
           德国预发线上"https://cnlogin.de.cainiao.com/login"
         */
        securityCheckInterceptor.setBackUrl("https://cnlogin.cainiao.com/login");
        return securityCheckInterceptor;

    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        if (BridgeSwitch.dailyTag){
            // 注册自定义的拦截器passwordStateInterceptor
            registry.addInterceptor(loginContextInterceptor).addPathPatterns("/ticket/**", "/admin/workOrderManage/workOrderManage",
                    "/charity/**", "/onefound", "admin/monitorData/monitorData", "/platform/order/**", "/cp/order/**", "/pickup/**","/quote/**" ,"/bill/**" , "/manager","/fast/shipment/order/**"
                    //,"/alisocialwork/**","/aliSocialOperations","/aliSocialCharityManager","/aliSocialOperations/**","/aliSocialCharityManager/**"
            );

            registry.addInterceptor(securityCheckInterceptor).addPathPatterns("/ticket/**", "/admin/workOrderManage/workOrderManage",
                    "/charity/**", "/onefound", "admin/monitorData/monitorData", "/platform/order/**", "/cp/order/**", "/pickup/**","/quote/**" ,"/bill/**" , "/manager","/fast/shipment/order/**"
                    //,"/alisocialwork/**","/aliSocialOperations","/aliSocialCharityManager","/aliSocialOperations/**","/aliSocialCharityManager/**"
            );
            return;
        }
        // 注册自定义的拦截器passwordStateInterceptor
        registry.addInterceptor(loginContextInterceptor).addPathPatterns("/ticket/**", "/admin/workOrderManage/workOrderManage",
                "/charity/**", "/onefound", "admin/monitorData/monitorData", "/platform/order/**", "/cp/order/**", "/pickup/**","/quote/**" ,"/bill/**" , "/manager","/fast/shipment/order/**"
                ,"/alisocialwork/**","/aliSocialOperations","/aliSocialCharityManager","/aliSocialOperations/**","/aliSocialCharityManager/**","/adworkorder/**"
        );

        registry.addInterceptor(securityCheckInterceptor).addPathPatterns("/ticket/**", "/admin/workOrderManage/workOrderManage",
                "/charity/**", "/onefound", "admin/monitorData/monitorData", "/platform/order/**", "/cp/order/**", "/pickup/**","/quote/**" ,"/bill/**" , "/manager","/fast/shipment/order/**"
                ,"/alisocialwork/**","/aliSocialOperations","/aliSocialCharityManager","/aliSocialOperations/**","/aliSocialCharityManager/**","/adworkorder/**"
        );
    }
}
