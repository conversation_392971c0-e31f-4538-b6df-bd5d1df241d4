package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * Description:网点-卖家发货地址-小件员绑定关系表
 * <AUTHOR>
 * Date 2017-04-18
 */
public class BranchSellerAddressDO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtCooperation;

    /**
     * 修改者
     */
    private String modifier;

    /**
     * cp编码
     */
    private String cpCode;

    /**
     * 网点编码
     */
    private String branchCode;

    /**
     * 卖家id
     */
    private Long sellerId;

    /**
     * 商家名称，冗余，查询条件
     */
    private String sellerName;

    /**
     * 店铺名称，冗余，查询条件
     */
    private String shopName;

    /**
     * 末级地址division_id
     */
    private Long divisionId;

    /**
     * 详细街道地址
     */
    private String addressDetail;

    /**
     * 小件员id
     */
    private Long courierId;

    /**
     * 合作状态
     */
    private Byte cooperationStatus;

    /**
     * 扩展字段,json格式
     */
    private String feature ;


    /**
     * setter for column 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * getter for column 主键
     */
    public Long getId() {
        return this.id;
    }

    /**
     * setter for column 创建时间
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * getter for column 创建时间
     */
    public Date getGmtCreate() {
        return this.gmtCreate;
    }

    public Date getGmtCooperation() {
        return gmtCooperation;
    }

    public BranchSellerAddressDO setGmtCooperation(Date gmtCooperation) {
        this.gmtCooperation = gmtCooperation;
        return this;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Long getDivisionId() {
        return divisionId;
    }

    public void setDivisionId(Long divisionId) {
        this.divisionId = divisionId;
    }

    public String getAddressDetail() {
        return addressDetail;
    }

    public void setAddressDetail(String addressDetail) {
        this.addressDetail = addressDetail;
    }

    /**
     * setter for column 网点编码
     */
    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    /**
     * getter for column 网点编码
     */
    public String getBranchCode() {
        return this.branchCode;
    }

    /**
     * setter for column 卖家id
     */
    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    /**
     * getter for column 卖家id
     */
    public Long getSellerId() {
        return this.sellerId;
    }

    /**
     * setter for column 小件员id
     */
    public void setCourierId(Long courierId) {
        this.courierId = courierId;
    }

    /**
     * getter for column 小件员id
     */
    public Long getCourierId() {
        return this.courierId;
    }

    public Byte getCooperationStatus() {
        return cooperationStatus;
    }

    public void setCooperationStatus(Byte cooperationStatus) {
        this.cooperationStatus = cooperationStatus;
    }

    public String getFeature() {
        return feature;
    }

    public void setFeature(String feature) {
        this.feature = feature;
    }
}
