package com.cainiao.waybill.bridge.enterprise.common.enums;

/**
 * <AUTHOR>
 * @date 2025/5/29
 **/
public enum DingTalkEvenTypeEnum {

    SUITE_TICKET("suite_ticket", "企业应用授权"),

    ORG_SUITE_AUTH("org_suite_auth", "企业应用开通"),

    USER_ROLE_CHANGE("user_role_change", "员工信息角色变更事件"),
    ;

    final String code;
    final String describe;

    DingTalkEvenTypeEnum(String code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public String getCode() {
        return code;
    }

    public String getDescribe() {
        return describe;
    }
}
