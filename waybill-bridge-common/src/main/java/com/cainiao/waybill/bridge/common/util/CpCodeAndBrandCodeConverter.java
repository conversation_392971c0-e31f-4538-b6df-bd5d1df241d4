package com.cainiao.waybill.bridge.common.util;

import java.util.List;

import com.cainiao.waybill.bridge.common.dto.CpCodeAndBrandCodeDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> zouping.fzp
 * @Classname CpCodeAndBrandCodeConverter
 * @Description
 * @Date 2022/11/15 9:33 下午
 * @Version 1.0
 */
public class CpCodeAndBrandCodeConverter {

    public static String cpCodeToBrandCode(List<CpCodeAndBrandCodeDTO> mapping, String cpCode) {
        if (CollectionUtils.isEmpty(mapping)) {
            return cpCode;
        }
        return mapping.stream()
            .filter(x -> StringUtils.equals(cpCode, x.getCpCode()))
            .map(CpCodeAndBrandCodeDTO::getBrandCode)
            .findAny()
            .orElse(cpCode);
    }

    public static String brandCodeToCpCode(List<CpCodeAndBrandCodeDTO> mapping, String brandCode) {
        if (CollectionUtils.isEmpty(mapping)) {
            return brandCode;
        }
        return mapping.stream()
            .filter(x -> StringUtils.equals(brandCode, x.getBrandCode()))
            .map(CpCodeAndBrandCodeDTO::getCpCode)
            .findAny()
            .orElse(brandCode);
    }
}
