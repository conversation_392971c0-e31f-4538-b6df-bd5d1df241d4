package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
/**
 *
 * <AUTHOR>
 */
public class WaybillPickUpTaskDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   任务状态：doing处理中，done已完成、fail失败
     *
     *
     * @mbg.generated
     */
    private String status;

    /**
     * Database Column Remarks:
     *   消息
     *
     *
     * @mbg.generated
     */
    private String msg;

    /**
     * Database Column Remarks:
     *   oss导出链接
     *
     *
     * @mbg.generated
     */
    private String ossUrl;

    /**
     *
     * @return the value of waybill_pick_up_task.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_pick_up_task.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_pick_up_task.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_pick_up_task.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_pick_up_task.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_pick_up_task.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_pick_up_task.status
     *
     * @mbg.generated
     */
    public String getStatus() {
        return status;
    }

    /**
     *
     * @param status the value for waybill_pick_up_task.status
     *
     * @mbg.generated
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     *
     * @return the value of waybill_pick_up_task.msg
     *
     * @mbg.generated
     */
    public String getMsg() {
        return msg;
    }

    /**
     *
     * @param msg the value for waybill_pick_up_task.msg
     *
     * @mbg.generated
     */
    public void setMsg(String msg) {
        this.msg = msg;
    }

    /**
     *
     * @return the value of waybill_pick_up_task.oss_url
     *
     * @mbg.generated
     */
    public String getOssUrl() {
        return ossUrl;
    }

    /**
     *
     * @param ossUrl the value for waybill_pick_up_task.oss_url
     *
     * @mbg.generated
     */
    public void setOssUrl(String ossUrl) {
        this.ossUrl = ossUrl;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", status=").append(status);
        sb.append(", msg=").append(msg);
        sb.append(", ossUrl=").append(ossUrl);
        sb.append("]");
        return sb.toString();
    }
}