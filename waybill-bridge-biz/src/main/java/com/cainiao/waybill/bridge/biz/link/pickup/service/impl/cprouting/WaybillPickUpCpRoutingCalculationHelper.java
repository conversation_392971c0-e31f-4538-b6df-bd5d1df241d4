package com.cainiao.waybill.bridge.biz.link.pickup.service.impl.cprouting;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;

import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.dto.CpRouteRequest;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.util.BridgeDateUtil;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpQualityIndicatorDO;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpQualityIndicatorMapper;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zouping.fzp
 * @Classname WaybillPickUpCpRoutingCalculationHelper
 * @Description
 * @Date 2022/10/24 11:05 上午
 * @Version 1.0
 */
@Component
public class WaybillPickUpCpRoutingCalculationHelper {

    private static final String DEFAULT_CONFIG = "全国";

    private static final Integer DEFAULT_PRICE = 1000;

    private static final String DEFAULT_PRICE_TEMPLATE_DATE = "1997-01-01 00:00:00";

    private static final String DEFAULT_PRICE_TEMPLATE_INDICATOR_NAME = "priceTemplate";

    @Autowired
    private WaybillPickUpQualityIndicatorMapper waybillPickUpQualityIndicatorMapper;

    private PickUpIndicatorConfig getConfig(CpRouteRequest routeRequest) {
        String address = buildAddrTown(routeRequest.getSendAddress());
        if (StringUtils.isBlank(address)) {
            return null;
        }

        List<PickUpIndicatorConfig> list = BridgeSwitch.cpIndicatorRoutingConfig;
        PickUpIndicatorConfig defaultConfig = null;
        for (PickUpIndicatorConfig pickUpIndicatorConfig : ListUtil.non(list)) {
            if (ListUtil.non(pickUpIndicatorConfig.getGraySenderAreaList()).contains(DEFAULT_CONFIG)) {
                defaultConfig = pickUpIndicatorConfig;
            }

            for (String addr : ListUtil.non(pickUpIndicatorConfig.getGraySenderAreaList())) {
                if (StringUtils.isNotBlank(addr) && address.startsWith(addr)) {
                    return pickUpIndicatorConfig;
                }
            }
        }
        return defaultConfig;
    }

    public String calculation(CpRouteRequest routeRequest, List<String> cpCodeList) {

        PickUpIndicatorConfig indicatorConfig = getConfig(routeRequest);
        if (indicatorConfig == null) {
            return null;
        }

        // 灰度比例
        if (indicatorConfig.getGrayRate() == null || RandomUtils.nextInt(0, 100) > indicatorConfig.getGrayRate()) {
            return null;
        }

        List<String> indicatorKeyList = Lists.newArrayList();
        if (StringUtils.isNotBlank(routeRequest.getOrgCode())) {
            indicatorKeyList.add(routeRequest.getOrgCode());
        }
        indicatorKeyList.add(routeRequest.getSendMobile());
        indicatorKeyList.add(buildAddrTown(routeRequest.getSendAddress()));
        Map<String, Map<String, Map<String, String>>> configMap = queryConfigMap(indicatorKeyList);

        //构建cp服务质量和价格
        List<CpQualityAndCost> cpQualityAndCosts = buildCpQualityAndCost(routeRequest, cpCodeList, indicatorConfig,
            configMap);

        return calculateCp(indicatorConfig, cpQualityAndCosts);

    }

    public static void main(String[] args) {
        PickUpIndicatorConfig indicatorConfig = new PickUpIndicatorConfig();
        indicatorConfig.setExpectComplainRate(-0.01);
        indicatorConfig.setServiceQualityWeight(2);
        indicatorConfig.setCostWeight(1);
        indicatorConfig.setCostUpLimitRate(1.02d);
        indicatorConfig.setQualityFirstRate(100);
        List<CpQualityAndCost> cpQualityAndCosts = JSON.parseArray("[{\"complainRate\":0.00000,\"cost\":680,"
            + "\"cpCode\":\"YTO\",\"orderRate\":0.99},{\"complainRate\":0.00000,\"cost\":800,\"cpCode\":\"GUOGUO\","
            + "\"orderRate\":0.01}]", CpQualityAndCost.class);
        String cpCode = calculateCp(indicatorConfig, cpQualityAndCosts);
        System.out.println(cpCode);
    }

    @Nullable
    public static String calculateCp(PickUpIndicatorConfig indicatorConfig, List<CpQualityAndCost> cpQualityAndCosts) {
        if (CollectionUtils.isEmpty(cpQualityAndCosts)) {
            return null;
        }

        // 计算性价比最高的cp(服务质量比上价格最大)
        CpQualityAndCost maxCostPerformance = cpQualityAndCosts.stream().max(
            Comparator.comparing(o -> (BigDecimal.ONE.subtract(o.getComplainRate())
                .divide(new BigDecimal(o.getCost()), 5, RoundingMode.HALF_UP)))).get();

        if (maxCostPerformance.getComplainRate()
            .compareTo(BigDecimal.valueOf(indicatorConfig.getExpectComplainRate())) <= 0) {
            // 如果成本和花费最优的cp已经满足预期则使用该cp
            maxCostPerformance.setOrderRate(rangeCheck(BigDecimal.ONE));
            return calculateCpFromCpOrderRate(cpQualityAndCosts, maxCostPerformance);
        }

        // 记录服务质量比期望服务质量更低且性价比最高的cp(服务质量与性价比最高cp差值比上价格差值最大的cp)
        CpQualityAndCost higherQualityCp = null;

        for (CpQualityAndCost cpQualityAndCost : cpQualityAndCosts) {
            if (cpQualityAndCost.equals(maxCostPerformance) || cpQualityAndCost.getComplainRate().compareTo(
                BigDecimal.valueOf(indicatorConfig.getExpectComplainRate())) >= 0) {
                continue;
            }

            if (higherQualityCp == null) {
                higherQualityCp = cpQualityAndCost;
            } else {
                BigDecimal curPerCostReduceComplainRate = maxCostPerformance.getComplainRate().subtract(
                    cpQualityAndCost.getComplainRate())
                    .divide(BigDecimal.valueOf(cpQualityAndCost.getCost() - maxCostPerformance.getCost()), 5,
                        RoundingMode.HALF_UP);

                BigDecimal higherPerCostReduceComplainRate = maxCostPerformance.getComplainRate().subtract(
                    higherQualityCp.getComplainRate())
                    .divide(BigDecimal.valueOf(higherQualityCp.getCost() - maxCostPerformance.getCost()), 5,
                        RoundingMode.HALF_UP);

                if (curPerCostReduceComplainRate.compareTo(higherPerCostReduceComplainRate) > 0) {
                    higherQualityCp = cpQualityAndCost;
                }
            }
        }

        // 存在服务质量比期望值更低的cp时
        if (higherQualityCp != null && higherQualityCp.getCost() > maxCostPerformance.getCost()) {
            // 成本优先时性价比最高的cp订单占比
            BigDecimal costRate = BigDecimal.valueOf(higherQualityCp.getCost())
                .subtract(BigDecimal.valueOf(maxCostPerformance.getCost())
                    .multiply(BigDecimal.valueOf(indicatorConfig.getCostUpLimitRate())))
                .divide(BigDecimal.valueOf(higherQualityCp.getCost() - maxCostPerformance.getCost()), 5,
                    RoundingMode.HALF_UP);

            // 质量优先时性价比最高的cp订单占比
            BigDecimal qualityRate = BigDecimal.valueOf(indicatorConfig.getExpectComplainRate()).subtract(
                higherQualityCp.getComplainRate())
                .divide(maxCostPerformance.getComplainRate().subtract(higherQualityCp.getComplainRate()), 5,
                    RoundingMode.HALF_UP);

            if (qualityRate.compareTo(costRate) >= 0) {
                maxCostPerformance.setOrderRate(rangeCheck(costRate));
                higherQualityCp.setOrderRate(BigDecimal.ONE.subtract(rangeCheck(costRate)));
            } else {
                BigDecimal addWeightRate = qualityRate.multiply(
                    BigDecimal.valueOf(indicatorConfig.getServiceQualityWeight()))
                    .add(costRate.multiply(BigDecimal.valueOf(indicatorConfig.getCostWeight())))
                    .divide(BigDecimal.valueOf(indicatorConfig.getServiceQualityWeight() + indicatorConfig.getCostWeight()), 5, RoundingMode.HALF_UP);

                maxCostPerformance.setOrderRate(rangeCheck(addWeightRate));
                higherQualityCp.setOrderRate(BigDecimal.ONE.subtract(rangeCheck(addWeightRate)));
            }

            return calculateCpFromCpOrderRate(cpQualityAndCosts, maxCostPerformance);
        }

        // 不存在服务质量比预期低的cp，则牺牲毛利到最大值尽可能提升服务质量
        if(RandomUtils.nextInt(0, 100) < indicatorConfig.getQualityFirstRate()){
            CpQualityAndCost maxQualityPerformance = cpQualityAndCosts.stream().min(
                Comparator.comparing(CpQualityAndCost::getComplainRate)).get();
            maxQualityPerformance.setOrderRate(rangeCheck(BigDecimal.ONE));
            return calculateCpFromCpOrderRate(cpQualityAndCosts, maxQualityPerformance);
        }

        // 否则选择性价比最高的
        maxCostPerformance.setOrderRate(rangeCheck(BigDecimal.ONE));
        return calculateCpFromCpOrderRate(cpQualityAndCosts, maxCostPerformance);
    }

    private static String calculateCpFromCpOrderRate(List<CpQualityAndCost> cpQualityAndCosts,
        CpQualityAndCost maxCostPerformance) {
        // 通过订单占比选择cp
        String cpCode = maxCostPerformance.getCpCode();
        int sum = cpQualityAndCosts.stream().mapToInt(
            x -> x.getOrderRate().multiply(BigDecimal.valueOf(1000)).intValue()).sum();
        int random = RandomUtils.nextInt(0, sum);
        int temp = 0;
        for (CpQualityAndCost cpQualityAndCost : cpQualityAndCosts) {
            int range = cpQualityAndCost.getOrderRate().multiply(BigDecimal.valueOf(1000)).intValue();
            if (random >= temp && random < (temp + range)) {
                cpCode = cpQualityAndCost.getCpCode();
                break;
            }
            temp += range;
        }

        PickUpLogUtil.info("calculate_cp_indicator, selectCp: " + cpCode + ", " + JSON.toJSONString(cpQualityAndCosts));
        return cpCode;
    }

    private static BigDecimal rangeCheck(BigDecimal bigDecimal) {
        if (bigDecimal.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.valueOf(0.01);
        }
        if (bigDecimal.compareTo(BigDecimal.ONE) >= 0) {
            return BigDecimal.valueOf(0.99);
        }
        return bigDecimal;
    }

    private List<CpQualityAndCost> buildCpQualityAndCost(CpRouteRequest routeRequest, List<String> cpCodeList,
        PickUpIndicatorConfig indicatorConfig, Map<String, Map<String, Map<String, String>>> configMap) {

        List<CpQualityAndCost> cpQualityAndCosts = Lists.newArrayList();

        for (String cpCode : ListUtil.non(cpCodeList)) {
            if(ListUtil.non(indicatorConfig.getExcludeCpList()).contains(cpCode)){
                continue;
            }
            List<IndicatorWeight> list = Lists.newArrayList();
            buildRate(configMap.get(cpCode), PickUpIndicatorEnum.ORG_INDICATOR.getName(),
                routeRequest.getOrgCode(), list, indicatorConfig);
            buildRate(configMap.get(cpCode), PickUpIndicatorEnum.SEND_MOBILE_INDICATOR.getName(),
                routeRequest.getSendMobile(), list, indicatorConfig);
            buildRate(configMap.get(cpCode), PickUpIndicatorEnum.ADDR_TOWN_INDICATOR.getName(),
                buildAddrTown(routeRequest.getSendAddress()), list, indicatorConfig);

            BigDecimal val = list.stream().filter(x -> x.getIndicatorVal() != null).map(
                x -> x.getIndicatorVal().multiply(x.getWeight())).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal weightSum = list.stream().filter(x -> x.getIndicatorVal() != null).map(
                IndicatorWeight::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal rate;
            // 如果当前指标不存在则使用该cp均值，未配置则认为投诉率100%
            if (weightSum == null || weightSum.equals(BigDecimal.ZERO)) {
                Double avg = indicatorConfig.getCpAverageComplainRate().get(cpCode);
                rate = avg != null ? BigDecimal.valueOf(avg) : BigDecimal.ONE;
            } else {
                rate = val.divide(weightSum, 5, RoundingMode.HALF_UP);
            }
            Integer price = getFirstWeightPrice(cpCode, routeRequest.getSendAddress(),
                routeRequest.getConsignAddress());
            // 未获取到价格则采用默认价格
            if (price == null) {
                price = DEFAULT_PRICE;
            }
            PickUpLogUtil.info(String.format("indicator_info, cp: %s, indicator: %s, price: %s", cpCode, JSON.toJSONString(list), price));
            cpQualityAndCosts.add(new CpQualityAndCost(cpCode, rate, price));
        }
        return cpQualityAndCosts;
    }

    private Map<String, Map<String, Map<String, String>>> queryConfigMap(List<String> indicatorKeyList) {
        Map<String, Map<String, Map<String, String>>> configMap = new HashMap<>();

        Date date = new Date();
        date = DateUtils.addDay(date, -1);
        date = BridgeDateUtil.dateToStartTime(date);
        List<WaybillPickUpQualityIndicatorDO> upDetailDOList = waybillPickUpQualityIndicatorMapper
            .selectByBizDateAndIndicator(BridgeDateUtil.dateToStr(date), indicatorKeyList, null);
        for (WaybillPickUpQualityIndicatorDO waybillPickUpQualityIndicatorDO : ListUtil.non(upDetailDOList)) {
            Map<String, Map<String, String>> cpIndicatorMap = configMap.get(
                waybillPickUpQualityIndicatorDO.getCpCode());
            if (cpIndicatorMap == null) {
                Map<String, Map<String, String>> temp = new HashMap<>();
                configMap.put(waybillPickUpQualityIndicatorDO.getCpCode(), temp);
                cpIndicatorMap = temp;
            }
            Map<String, String> indicatorMap = cpIndicatorMap.get(waybillPickUpQualityIndicatorDO.getIndicatorName());
            if (indicatorMap == null) {
                Map<String, String> temp = new HashMap<>();
                cpIndicatorMap.put(waybillPickUpQualityIndicatorDO.getIndicatorName(), temp);
                indicatorMap = temp;
            }
            indicatorMap.put(waybillPickUpQualityIndicatorDO.getIndicatorKey(),
                waybillPickUpQualityIndicatorDO.getIndicatorVal());
        }

        return configMap;
    }

    private Integer getFirstWeightPrice(String cpCode, AddressDTO sender, AddressDTO receiver) {
        String sendPro = sender.getProvinceName();
        String sendCity = sender.getCityName();
        String receiverPro = receiver.getProvinceName();
        String receiverCity = receiver.getCityName();

        List<String> indicatorKeyList = Lists.newArrayList();
        indicatorKeyList.add(sendPro + receiverPro);
        indicatorKeyList.add(sendCity + receiverCity);
        List<WaybillPickUpQualityIndicatorDO> indicatorDOList = waybillPickUpQualityIndicatorMapper
            .selectByBizDateAndIndicator(DEFAULT_PRICE_TEMPLATE_DATE, indicatorKeyList,
                DEFAULT_PRICE_TEMPLATE_INDICATOR_NAME);

        String proPrice = ListUtil.stream(indicatorDOList).filter(x -> StringUtils.equals(cpCode, x.getCpCode()))
            .filter(x -> StringUtils.equals(sendPro + receiverPro, x.getIndicatorKey()))
            .map(WaybillPickUpQualityIndicatorDO::getIndicatorVal)
            .findAny().orElse(null);

        String cityPrice = ListUtil.stream(indicatorDOList).filter(x -> StringUtils.equals(cpCode, x.getCpCode()))
            .filter(x -> StringUtils.equals(sendCity + receiverCity, x.getIndicatorKey()))
            .map(WaybillPickUpQualityIndicatorDO::getIndicatorVal)
            .findAny().orElse(null);

        // 同时配置省线路和市线路的则优先价格最低的线路
        if (StringUtils.isNotBlank(proPrice) && StringUtils.isNotBlank(cityPrice)) {
            return Math.min(Integer.parseInt(proPrice), Integer.parseInt(cityPrice));
        }
        if (StringUtils.isNotBlank(proPrice)) {
            return Integer.parseInt(proPrice);
        }
        if (StringUtils.isNotBlank(cityPrice)) {
            return Integer.parseInt(cityPrice);
        }
        return null;
    }

    private void buildRate(Map<String, Map<String, String>> map, String indicatorName, String indicatorKey,
        List<IndicatorWeight> list, PickUpIndicatorConfig indicatorConfig) {
        if (StringUtils.isBlank(indicatorKey) || map == null) {
            return;
        }
        if (StringUtils.isNotBlank(indicatorConfig.getIndicatorPeriodPrefix())) {
            indicatorName = indicatorConfig.getIndicatorPeriodPrefix() + indicatorName;
        }
        Map<String, String> rateMap = map.get(indicatorName);
        if (rateMap == null) {
            return;
        }
        String rate = rateMap.get(indicatorKey);
        if (StringUtils.isBlank(rate)) {
            return;
        }
        if (NumberUtils.isNumber(rate)) {
            BigDecimal val = new BigDecimal(rate);
            int weight = indicatorConfig.getIndicatorWeight() == null ? 1 :
                indicatorConfig.getIndicatorWeight().getOrDefault(indicatorName, 1);
            list.add(new IndicatorWeight(val, new BigDecimal(weight)));
        }
    }

    private String buildAddrTown(AddressDTO addressDTO) {
        if (addressDTO == null) {
            return null;
        }
        return addressDTO.getProvinceName()
            + addressDTO.getCityName()
            + addressDTO.getAreaName()
            + addressDTO.getTownName();
    }

    @Data
    public static class CpQualityAndCost {

        String cpCode;

        BigDecimal complainRate;

        Integer cost;

        BigDecimal orderRate = BigDecimal.valueOf(0.01);

        public CpQualityAndCost(String cpCode, BigDecimal complainRate, Integer cost) {
            this.cpCode = cpCode;
            this.complainRate = complainRate;
            this.cost = cost;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) { return true; }

            if (o == null || getClass() != o.getClass()) { return false; }

            CpQualityAndCost that = (CpQualityAndCost)o;

            return new EqualsBuilder().append(cpCode, that.cpCode).append(complainRate,
                that.complainRate).append(cost, that.cost).isEquals();
        }

        @Override
        public int hashCode() {
            return new HashCodeBuilder(17, 37).append(cpCode).append(complainRate).append(cost).toHashCode();
        }
    }

    @Data
    @AllArgsConstructor
    static class IndicatorWeight {

        BigDecimal indicatorVal;

        BigDecimal weight;
    }

}
