package com.cainiao.waybill.bridge.web.pickup.vo;

import com.cainiao.waybill.bridge.biz.utils.excel.ExcelHeader;
import lombok.Data;

/**
 * cp指标报告
 * <AUTHOR>
 * @date 2025/2/20 16:00
 **/
@Data
public class PickUpKeyCpRobotVO {

    /**
     * 客户名
     */
    @ExcelHeader("客户")
    private String customer;

    /**
     * CP编码
     */
    @ExcelHeader("服务商")
    private String cpCode;

    /**
     * 订单数
     */
    @ExcelHeader("订单数")
    private Integer orderNum;


    /**
     * 工单去重进线数
     */
    @ExcelHeader("去重进线数")
    private Integer distinctTicketNum;

    /**
     * 工单去重进线率
     */
    @ExcelHeader("去重进线率")
    private String distinctTicketRate;
}
