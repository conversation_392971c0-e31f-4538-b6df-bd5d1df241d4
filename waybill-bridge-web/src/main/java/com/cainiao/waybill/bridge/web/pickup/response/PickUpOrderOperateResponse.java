package com.cainiao.waybill.bridge.web.pickup.response;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpTicketResponse
 * @Description
 * @Date 2022/11/29 10:47 上午
 * @Version 1.0
 */
@Data
public class PickUpOrderOperateResponse implements Serializable {

    private static final long serialVersionUID = -4646222379725643502L;

    /**
     * 类型
     */
    private String type;
    /**
     * 子类型
     */
    private String subType;

    /**
     * 值
     */
    private String value;

    /**
     * 描述
     */
    private String desc;

    /**
     * 操作时间
     */
    private String operateTime;
}
