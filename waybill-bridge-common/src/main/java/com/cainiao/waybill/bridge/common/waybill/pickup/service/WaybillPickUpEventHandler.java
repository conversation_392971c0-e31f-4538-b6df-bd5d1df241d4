package com.cainiao.waybill.bridge.common.waybill.pickup.service;

import com.cainiao.waybill.bridge.common.metaq.PickUpEventContext;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;

/**
 * WaybillPickUpEvent 消息处理类
 * waybillPickUpEvent 自己发送再自己消费，通过metaQ转一下的目的是使用metaQ的重试能力
 *
 * <AUTHOR>
 * @date 2021-06-19
 */
public interface WaybillPickUpEventHandler {

    /**
     * 处理生命周期消息
     * 因为消息存在重试的情况，实现类必须幂等
     *
     * @param pickUpEvent   消息内容
     * @param eventContext  消息的上下文信息
     * @return 如果返回失败，消息过一段时间后将会重试, 因为目前接的metaQ消息，最多重试16次
     */
    BaseResultDTO<Void> handle(WaybillPickUpEvent pickUpEvent, PickUpEventContext eventContext);
}
