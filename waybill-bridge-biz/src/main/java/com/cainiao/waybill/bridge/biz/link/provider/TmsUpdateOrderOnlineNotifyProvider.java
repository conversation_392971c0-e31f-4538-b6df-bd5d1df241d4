package com.cainiao.waybill.bridge.biz.link.provider;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import com.cainiao.link.annotation.HsfServer;
import com.cainiao.link.annotation.LinkProvider;
import com.cainiao.link.provider.LinkReceiver;
import com.cainiao.link.provider.ReceiverContext;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.AlipayIotKey;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.Error;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpCancelOrderRequest;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillPickUpOrderService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.taobao.pac.client.sdk.dataobject.request.TMS_UPDATE_ORDER_ONLINE_NOTIFY.TmsUpdateOrderOnlineNotifyRequest;
import com.taobao.pac.client.sdk.dataobject.response.TMS_UPDATE_ORDER_ONLINE_NOTIFY.TmsUpdateOrderOnlineNotifyResponse;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 支付宝逆向寄件修改
 * <AUTHOR>
 * @date 2025/1/7 13:47
 **/
@HsfServer
@LinkProvider
public class TmsUpdateOrderOnlineNotifyProvider  implements
    LinkReceiver<TmsUpdateOrderOnlineNotifyRequest, TmsUpdateOrderOnlineNotifyResponse> {

    @Resource
    private WaybillPickUpOrderService waybillPickUpOrderService;


    @Override
    public TmsUpdateOrderOnlineNotifyResponse receive(ReceiverContext receiverContext,
        TmsUpdateOrderOnlineNotifyRequest tmsUpdateOrderOnlineNotifyRequest) {

        return dealIotOrderCancelRequest(tmsUpdateOrderOnlineNotifyRequest);

    }

    /**
     * 处理iot订单取消
     * @param tmsUpdateOrderOnlineNotifyRequest
     * @return
     */
    private TmsUpdateOrderOnlineNotifyResponse dealIotOrderCancelRequest(TmsUpdateOrderOnlineNotifyRequest tmsUpdateOrderOnlineNotifyRequest) {
        TmsUpdateOrderOnlineNotifyResponse response = new TmsUpdateOrderOnlineNotifyResponse();
        response.setSuccess(true);
        PickUpLogUtil.info("支付宝逆向寄件修改接口入参.tmsUpdateOrderOnlineNotifyRequest:{}",
            JSONObject.toJSONString(tmsUpdateOrderOnlineNotifyRequest));
        try {
            // 校验入参
            validateCreateParams(tmsUpdateOrderOnlineNotifyRequest);
            if(!StringUtils.equals(tmsUpdateOrderOnlineNotifyRequest.getStatus(), IotUpdateStatusEnum.TMS_CANCEL.getStatus())){
                // 当前仅支持订单取消 其他状态直接返回成功
                return response;
            }
            PickUpCancelOrderRequest request = new PickUpCancelOrderRequest();
            // 构建上下文
            buildContext(request);
            request.setMailNo(tmsUpdateOrderOnlineNotifyRequest.getMailNo());
            request.setOuterOrderCode(tmsUpdateOrderOnlineNotifyRequest.getLogisticsId());
            String resCode = PickUpCommonUtil.buildResCode(AlipayIotKey.IOT_APP_KEY, AlipayIotKey.IOT_ORDER_CHANNELS);
            request.setResCode(resCode);
            if(StringUtils.isNotBlank(tmsUpdateOrderOnlineNotifyRequest.getRemark())){
                request.setCancelDesc(tmsUpdateOrderOnlineNotifyRequest.getRemark());
            }else {
                request.setCancelDesc("客户取消订单，无需寄件");
            }

            waybillPickUpOrderService.cancel(request);
            return response;
        }catch (BridgeBaseException e){
            response.setSuccess(false);
            response.setErrorCode(e.getErrorCode());
            response.setErrorMsg(e.getErrorMessage());
            PickUpLogUtil.errLog(tmsUpdateOrderOnlineNotifyRequest.getLogisticsId(), PickUpConstants.Action.IOT_CREATE_ORDER_EXCEPTION.name(), e.getErrorCode(), e.getErrorMessage(), ExceptionUtils.getFullStackTrace(e));
        }catch (Throwable e) {
            response.setSuccess(false);
            response.setErrorCode(Error.SYSTEM_ERROR.getErrorCode());
            response.setErrorMsg(Error.SYSTEM_ERROR.getErrorMsg());
            PickUpLogUtil.errLog(tmsUpdateOrderOnlineNotifyRequest.getLogisticsId(), PickUpConstants.Action.IOT_CREATE_ORDER_EXCEPTION.name(), "IOT_CREATE_ORDER_EXCEPTION", e.getMessage(), ExceptionUtils.getFullStackTrace(e));
        }
        return response;
    }

    /**
     * 构建上下文
     * @param request
     */
    private void buildContext(PickUpCancelOrderRequest request) {
        request.setFromAppKey(AlipayIotKey.IOT_APP_KEY);
        String resCode = PickUpCommonUtil.buildResCode(AlipayIotKey.IOT_APP_KEY, AlipayIotKey.IOT_ORDER_CHANNELS);
        request.setResCode(resCode);
    }

    /**
     * 校验入参
     * @param cancelRequest
     */
    private void validateCreateParams(TmsUpdateOrderOnlineNotifyRequest cancelRequest)
        throws BridgeBaseException {

        if (null == cancelRequest) {
            throw new BridgeBaseException(PickUpConstants.Error.SYSTEM_PARAM_ERROR.getErrorCode(), PickUpConstants.Error.SYSTEM_PARAM_ERROR.getErrorMsg());
        }
        if (!IotUpdateStatusEnum.checkValidStatus(cancelRequest.getStatus())) {
            throw new BridgeBaseException(PickUpConstants.Error.IOT_INVALID_STATUS_ERROR.getErrorCode(), PickUpConstants.Error.IOT_INVALID_STATUS_ERROR.getErrorMsg());
        }

        if (StringUtils.isBlank(cancelRequest.getLogisticsId())) {
            throw new BridgeBaseException(PickUpConstants.Error.IOT_LOGISTICS_ID_NULL_ERROR.getErrorCode(), PickUpConstants.Error.IOT_LOGISTICS_ID_NULL_ERROR.getErrorMsg());
        }
        if (StringUtils.isBlank(cancelRequest.getMailNo())) {
            throw new BridgeBaseException(PickUpConstants.Error.IOT_MAIL_NO_NULL_ERROR.getErrorCode(), PickUpConstants.Error.IOT_MAIL_NO_NULL_ERROR.getErrorMsg());
        }

    }

    enum IotUpdateStatusEnum {

        /**
         * 取消订单
         */
        TMS_CANCEL("TMS_CANCEL", "取消订单"),

        /**
         * 接受订单
         */
        TMS_ORDER_RECEIVED("TMS_ORDER_RECEIVED", "接受订单"),

        /**
         * 拒绝订单
         */
        TMS_REJECT("TMS_REJECT", "拒绝订单"),

        /**
         * 取消订单
         */
        TMS_UNCANVASS("TMS_UNCANVASS", "揽件失败"),

        /**
         * 改地址
         */
        TMS_MODIFY_ADDRESS("TMS_MODIFY_ADDRESS", "改地址"),


        ;

        private String status;

        private String desc;

        IotUpdateStatusEnum(String status, String desc){
            this.status = status;
            this.desc = desc;
        }

        public String getStatus() {
            return status;
        }
        public String getDesc() {
            return desc;
        }

        /**
         * 校验是合法的状态
         * @param status
         * @return
         */
        public static boolean checkValidStatus(String status){
            IotUpdateStatusEnum[] enums = IotUpdateStatusEnum.values();
            for (IotUpdateStatusEnum item : enums) {
                if (item.getStatus().equals(status)) {
                    return true;
                }
            }
            return false;
        }

    }
}
