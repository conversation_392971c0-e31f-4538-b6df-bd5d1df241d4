package com.cainiao.waybill.bridge.biz.label.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.cainiao.bearlake.api.user.dto.UserQueryResponseDTO;
import com.cainiao.waybill.bridge.biz.label.manager.BranchSellerAddressRelationManager;
import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
import com.cainiao.waybill.bridge.biz.label.manager.TpnMsgManager;
//import com.cainiao.waybill.bridge.biz.middleware.iss.BridgeISSClient;
import com.cainiao.waybill.bridge.biz.utils.FeatureJsonUtils;
import com.cainiao.waybill.bridge.biz.wrapper.AddressUnifyWrapper;
import com.cainiao.waybill.bridge.biz.wrapper.BranchWrapper;
import com.cainiao.waybill.bridge.biz.wrapper.CourierWrapper;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants.BranchSellerAddressFeature;
import com.cainiao.waybill.bridge.common.constants.BridgeErrorConstant.SystemError;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants.LogAppender;
import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.label.dto.AddressDTO;
import com.cainiao.waybill.bridge.common.label.dto.BranchSellerAddressRelationForBranchDTO;
import com.cainiao.waybill.bridge.common.label.dto.BranchSellerAddressRelationForSellerDTO;
import com.cainiao.waybill.bridge.common.label.dto.request.BranchSellerAddRelQueryRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.CreateBranchSellerAddRelRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.ReplaceCourierRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.UnbindCourierRequest;
import com.cainiao.waybill.bridge.common.label.dto.request.UpdateSettlementTypeRequest;
import com.cainiao.waybill.bridge.common.label.service.BranchSellerAddressRelationService;
import com.cainiao.waybill.bridge.common.label.validator.WaybillBranchSellerAddRelValidator;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.util.BaseResultHelper;
import com.cainiao.waybill.bridge.common.util.Page;
import com.cainiao.waybill.bridge.model.domain.BranchSellerAddressDO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Description: 网点商家地址小件员绑定关系 impl
 *
 * <AUTHOR>
 * @Date 2017-04-27
 */
@HSFProvider(serviceInterface = BranchSellerAddressRelationService.class)
public class BranchSellerAddressRelationServiceImpl implements BranchSellerAddressRelationService {

    private final static Logger LOGGER = LoggerFactory.getLogger(
        LogAppender.BRANCH_SELLER_ADDRESS);

    @Resource
    private LogManager logManager;

    @Resource
    private BranchSellerAddressRelationManager branchSellerAddressRelationManager;

    @Resource
    private CourierWrapper courierWrapper;

    @Resource
    private AddressUnifyWrapper addressUnifyWrapper;

    @Resource
    private BranchWrapper branchWrapper;
    @Resource
    private TpnMsgManager tpnMsgManager;

    //@Resource
    //private BridgeISSClient bridgeISSClient;
    
    @Override
    public BaseResultDTO<Page<BranchSellerAddressRelationForBranchDTO>> queryRelationsForBranch(
        BranchSellerAddRelQueryRequest request, ClientInfoDTO clientAppInfo) {
        try {
            // 参数校验
            WaybillBranchSellerAddRelValidator.validateQueryForBranchRequest(request, clientAppInfo);

            // 查询网点下商家
            Page<BranchSellerAddressDO> relations = branchSellerAddressRelationManager.queryRelationsForBranch(request);

            // 构造response
            return BaseResultHelper.newResult(buildRelationsForBranch(relations));

        } catch (BridgeValidationException e) {
            logManager.logExceptionMessage("queryRelationsForBranch", "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request, e, LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("queryRelationsForBranch","code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request, e, LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        } catch (Throwable e) {
            logManager.logExceptionMessage("queryRelationsForBranch", "request:" + request, e, LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        }
    }

    private Page<BranchSellerAddressRelationForBranchDTO> buildRelationsForBranch(
        Page<BranchSellerAddressDO> relations) {

        List<BranchSellerAddressRelationForBranchDTO> relForBranchDTOs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(relations.getData())) {
            for (BranchSellerAddressDO relDO : relations.getData()) {
                BranchSellerAddressRelationForBranchDTO relForBranchDTO = new BranchSellerAddressRelationForBranchDTO();
                relForBranchDTO.setId(relDO.getId());
                relForBranchDTO.setCpCode(relDO.getCpCode());
                relForBranchDTO.setBranchCode(relDO.getBranchCode());
                relForBranchDTO.setSellerId(relDO.getSellerId());
                relForBranchDTO.setSellerName(relDO.getSellerName());
                relForBranchDTO.setShopName(relDO.getShopName());

                AddressDTO addressDTO = addressUnifyWrapper.getAddressByDivisionId(relDO.getDivisionId(),
                    relDO.getAddressDetail());
                relForBranchDTO.setSellerAddress(addressDTO);
                relForBranchDTO.setCourierId(relDO.getCourierId());
                relForBranchDTO.setCooperationStatus(relDO.getCooperationStatus());
                relForBranchDTO.setCooperationDate(DateFormatUtils.format(relDO.getGmtCooperation(), "yyyy-MM-dd"));

                relForBranchDTO.setSettlementTypeActive("");
                relForBranchDTO.setSettlementTypeEdit("");

                if(FeatureJsonUtils.keyIsExist(relDO.getFeature(), BranchSellerAddressFeature.FEATURE_SETTLEMENT_TYPE_ACTIVE)) {
                    String settlementTypeActive = FeatureJsonUtils.getStringValue(relDO.getFeature(), BranchSellerAddressFeature.FEATURE_SETTLEMENT_TYPE_ACTIVE) ;
                    relForBranchDTO.setSettlementTypeActive(settlementTypeActive);
                }
                if(FeatureJsonUtils.keyIsExist(relDO.getFeature(), BranchSellerAddressFeature.FEATURE_SETTLEMENT_TYPE_EDIT)) {
                    String settlementTypeEdit = FeatureJsonUtils.getStringValue(relDO.getFeature(), BranchSellerAddressFeature.FEATURE_SETTLEMENT_TYPE_EDIT) ;
                    relForBranchDTO.setSettlementTypeEdit(settlementTypeEdit);
                }

                relForBranchDTOs.add(relForBranchDTO);
            }
        }
        return new Page<>(relForBranchDTOs, relations.getCurrentPageIndex(), relations.getSize(),
            relations.getTotalRecord());
    }

    @Override
    public BaseResultDTO<List<BranchSellerAddressRelationForSellerDTO>> queryRelationsForSeller(Long sellerId,
                                                                                                ClientInfoDTO
                                                                                                    clientAppInfo) {
        try {
            // 参数校验
            WaybillBranchSellerAddRelValidator.validateQueryForSellerRequest(sellerId, clientAppInfo);

            // 查询网点下商家
            List<BranchSellerAddressDO> relations = branchSellerAddressRelationManager.queryRelationsForSeller(
                sellerId);

            // 构造返回值
            List<BranchSellerAddressRelationForSellerDTO> relationsForSeller = buildRelationsForSeller(relations);
            return BaseResultHelper.newResult(relationsForSeller);

        } catch (BridgeValidationException e) {
            logManager.logExceptionMessage("BranchSellerAddressRelationServiceImpl.queryRelationsForSeller",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", sellId:" + sellerId, e, LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("BranchSellerAddressRelationServiceImpl.queryRelationsForSeller",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", sellId:" + sellerId, e, LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        } catch (Throwable e) {
            logManager.logExceptionMessage("BranchSellerAddressRelationServiceImpl.queryRelationsForSeller",
                "sellerId:" + sellerId, e,
                LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        }
    }

    private List<BranchSellerAddressRelationForSellerDTO> buildRelationsForSeller(
        List<BranchSellerAddressDO> relations) {
        List<BranchSellerAddressRelationForSellerDTO> relationsForSeller = Lists.newArrayList();
        if (CollectionUtils.isEmpty(relations)) {
            return relationsForSeller;
        }

        for (BranchSellerAddressDO branchSellerAddressDO : relations) {
            BranchSellerAddressRelationForSellerDTO relForSellerDTO = new BranchSellerAddressRelationForSellerDTO();
            relForSellerDTO.setId(branchSellerAddressDO.getId());
            relForSellerDTO.setCpCode(branchSellerAddressDO.getCpCode());
            relForSellerDTO.setBranchCode(branchSellerAddressDO.getBranchCode());
            relForSellerDTO.setCourierId(branchSellerAddressDO.getCourierId());
            AddressDTO addressDTO = addressUnifyWrapper.getAddressByDivisionId(branchSellerAddressDO.getDivisionId(),
                branchSellerAddressDO.getAddressDetail());
            relForSellerDTO.setSellerAddress(addressDTO);

            // 补充 courierName、courierMobile
            UserQueryResponseDTO userQueryResponseDTO = courierWrapper.getUserDTOByCourierId(
                branchSellerAddressDO.getCourierId(), branchSellerAddressDO.getSellerId());
            if (userQueryResponseDTO != null) {
                relForSellerDTO.setCourierName(userQueryResponseDTO.getName());
                relForSellerDTO.setCourierMobile(userQueryResponseDTO.getMobile());
            } else {
                logManager.logWarnMessage("BranchSellerAddressRelationServiceImpl.buildRelationsForSeller",
                    "找不到对应的小件员:{}", new Object[] {branchSellerAddressDO.getCourierId()},
                    LOGGER);
            }

            // 补充 branchName
            String branchName = branchWrapper.getNameByCode(relForSellerDTO.getCpCode(),
                relForSellerDTO.getBranchCode());
            if (StringUtils.isNotBlank(branchName)) {
                relForSellerDTO.setBranchName(branchName);
            } else {
                logManager.logWarnMessage("BranchSellerAddressRelationServiceImpl.buildRelationsForSeller",
                    "找不到对应的网点名称, cpCode:{}, branchCode",
                    new Object[] {relForSellerDTO.getCpCode(), relForSellerDTO.getBranchCode()},
                    LOGGER);
            }

            relationsForSeller.add(relForSellerDTO);
        }
        return relationsForSeller;
    }

    @Override
    public BaseResultDTO<Void> unbind(UnbindCourierRequest request, ClientInfoDTO clientAppInfo) {
        try {
            // 参数校验
            WaybillBranchSellerAddRelValidator.validateUnbindCourierRequest(request, clientAppInfo);

            // 解绑网点下合作商家地址对应的小件员
            branchSellerAddressRelationManager.unbindCourier(request, clientAppInfo.getOperator());

            // 构造response
            return BaseResultHelper.newResult();

        } catch (BridgeValidationException e) {
            logManager.logExceptionMessage("unbindCourier",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request, e, LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("unbindCourier",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request, e, LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        } catch (Throwable e) {
            logManager.logExceptionMessage("unbindCourier", "request:" + request, e, LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        }
    }

    @Override
    public BaseResultDTO<Void> replaceCourier(ReplaceCourierRequest request, ClientInfoDTO clientAppInfo) {
        try {
            // 参数校验
            WaybillBranchSellerAddRelValidator.validateReplaceCourierRequest(request, clientAppInfo);

            // 换绑网点下商家地址绑定的小件员
            branchSellerAddressRelationManager.replaceCourier(request, clientAppInfo.getOperator());

            // 构造response
            return BaseResultHelper.newResult();

        } catch (BridgeValidationException e) {
            logManager.logExceptionMessage("replaceCourier", "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request, e, LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("replaceCourier",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request, e, LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        } catch (Throwable e) {
            logManager.logExceptionMessage("replaceCourier", "ReplaceCourierRequest:" + request, e,
                LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        }
    }

    @Override
    public BaseResultDTO<Void> updateSettlementType(UpdateSettlementTypeRequest request, ClientInfoDTO clientAppInfo) {
        try {
            // 参数校验
            WaybillBranchSellerAddRelValidator.validateUpdateSettlementTypeRequest(request, clientAppInfo);

            branchSellerAddressRelationManager.updateSettlementType(request, clientAppInfo.getOperator());

            // 构造response
            return BaseResultHelper.newResult();

        } catch (BridgeValidationException e) {
            logManager.logExceptionMessage("updateSettlementType", "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request, e, LOGGER);
            return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
        } catch (BridgeBaseException e) {
            logManager.logExceptionMessage("updateSettlementType",
                "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request, e, LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        } catch (Throwable e) {
            logManager.logExceptionMessage("updateSettlementType", " request:" + request, e,
                LOGGER);
            return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
        }
    }

    //@Override
    //public BaseResultDTO<Void> create(CreateBranchSellerAddRelRequest request, ClientInfoDTO clientAppInfo) {
    //    try {
    //        // 参数校验
    //        WaybillBranchSellerAddRelValidator.validateCreateRequest(request, clientAppInfo);
    //
    //        // 新建网点下商家地址小件员绑定关系
    //        Long relationId = branchSellerAddressRelationManager.createRelation(request, clientAppInfo.getOperator());
    //        //给商家授权千牛消息，用于解绑、换绑时发送千牛消息
    //        try {
    //            tpnMsgManager.authorizeTpn(request.getSellerId());
    //        } catch (Throwable e) {
    //            logManager.logExceptionMessage("authorizeTpn", "request:" + request, e, LOGGER);
    //        }
    //
    //        // 调用ISS，发起下发任务 TODO 下发不影响主流程
    //        bridgeISSClient.pushSellerCourier(request.getCpCode(), relationId);
    //
    //        // 构造response
    //        return BaseResultHelper.newResult();
    //
    //    } catch (BridgeValidationException e) {
    //        logManager.logExceptionMessage("create", "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage()  + "request:" + request, e, LOGGER);
    //        return BaseResultHelper.errorResult(e.getErrorCode(), e.getErrorMessage());
    //    } catch (BridgeBaseException e) {
    //        logManager.logExceptionMessage("create",
    //            "code:" + e.getErrorCode() + ", msg:" + e.getErrorMessage() + ", request:" + request, e, LOGGER);
    //        return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
    //    } catch (Throwable e) {
    //        logManager.logExceptionMessage("create", "request:" + request, e, LOGGER);
    //        return BaseResultHelper.errorResult(SystemError.SYSTEM_BUSY);
    //    }
    //}

}
