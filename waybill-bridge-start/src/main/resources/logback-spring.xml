<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- https://github.com/spring-projects/spring-boot/blob/v1.4.2.RELEASE/spring-boot/src/main/resources/org/springframework/boot/logging/logback/defaults.xml -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="com/alibaba/boot/logback/alimonitor.xml"/>

    <conversionRule conversionWord="trace"
                    converterClass="com.cainiao.waybill.bridge.enterprise.common.logger.EagleEyeClassicConverter"/>

    <property name="APP_NAME" value="waybill-bridge"/>
    <property name="LOG_PATH" value="${user.home}/${APP_NAME}/logs"/>

    <property name="BIZ_MONITOR" value="${LOG_PATH}/biz_monitor.log" />

    <property name="LINK_CP_LOG_FILE" value="${LOG_PATH}/link-cp.log"/>
    <property name="APPLICATION_LOG_FILE" value="${LOG_PATH}/application.log"/>
    <property name="MOBILE_CODE_LOG_FILE" value="${LOG_PATH}/mobile-code.log"/>
    <property name="WAYBILL_CLOUD_PRINT_LOG_FILE" value="${LOG_PATH}/waybill-cloud-print.log"/>
    <property name="TPN_MSG_LOG_FILE" value="${LOG_PATH}/tpn-msg.log"/>
    <property name="BRANCH_SELLER_ADDRESS_LOG_FILE" value="${LOG_PATH}/branch-seller-address.log"/>
    <property name="MIDDLE_WARE_LOG_FILE" value="${LOG_PATH}/middle-ware.log"/>
    <property name="WAYBILL_PICKUP_CODE_LOG_FILE" value="${LOG_PATH}/waybill-pickup-code.log"/>
    <property name="WAYBILL_PICKUP_INFO_LOG_FILE" value="${LOG_PATH}/waybill-pickup-info.log"/>
    <property name="WAYBILL_PKGNEW_INFO_LOG_FILE" value="${LOG_PATH}/waybill-pkgnew.log"/>
    <property name="WAYBILL_PRIVACY_INFO_LOG_FILE" value="${LOG_PATH}/waybill-privacy-info.log"/>
    <property name="ACCOUNT_ALARM_LOG_FILE" value="${LOG_PATH}/account-alarm.log"/>
    <property name="WAYBILL_PICKUP_EVENT_TRACE_LOG_FILE" value="${LOG_PATH}/waybill-pickup-event-trace.log"/>

    <property name="CHARITY_LOG_FILE" value="${LOG_PATH}/charity-info.log"/>
    <property name="PICK_UP_MANAGER_LOG_FILE" value="${LOG_PATH}/pick-up-manager-info.log"/>
    <property name="PICK_UP_MONITOR_LOG_FILE" value="${LOG_PATH}/pick-up-monitor.log"/>
    <property name="PICK_UP_MONITOR_LOG_FILE_V2" value="${LOG_PATH}/pick-up-monitor-v2.log"/>
    <property name="PICK_UP_OUTER_EVENT_LOG_FILE" value="${LOG_PATH}/pick-up-outer-event.log"/>
    <property name="ALI_SOCIAL_INFO_LOG_FILE" value="${LOG_PATH}/ali-social-work.log"/>
    <property name="WAYBILL_ADWORKORDER_INFO_LOG_FILE" value="${LOG_PATH}/waybill-adworkorder-info.log"/>

    <property name="WAYBILL_ENTERPRISE_LOG_FILE" value="${LOG_PATH}/waybill-enterprise.log"/>

<!--    企业寄件-->
    <property name="ENTERPRISE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] [%trace] [%file %line] - %msg%n"/>

    <appender name="LINK_CP_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LINK_CP_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LINK_CP_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="APPLICATION_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${APPLICATION_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${APPLICATION_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <layout class="com.cainiao.waybill.bridge.common.logger.WebLoggerLayout">
            <pattern><![CDATA[
             %d-%traceId-%C-%L %m%n
            ]]></pattern>
        </layout>
    </appender>

    <appender name="BIZ_MONITOR"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${BIZ_MONITOR}</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss}|%msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${BIZ_MONITOR}.%d{yyyy-MM-dd}</fileNamePattern>
            <maxHistory>2</maxHistory>
        </rollingPolicy>
    </appender>

    <appender name="MOBILE_CODE_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${MOBILE_CODE_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${MOBILE_CODE_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="WAYBILL_CLOUD_PRINT_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${WAYBILL_CLOUD_PRINT_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${WAYBILL_CLOUD_PRINT_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="TPN_MSG_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${TPN_MSG_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${TPN_MSG_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="ACCOUNT_ALARM_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${ACCOUNT_ALARM_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${ACCOUNT_ALARM_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>


    <appender name="BRANCH_SELLER_ADDRESS_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${BRANCH_SELLER_ADDRESS_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${BRANCH_SELLER_ADDRESS_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="MIDDLE_WARE_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${MIDDLE_WARE_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${MIDDLE_WARE_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="WAYBILL_PICKUP_EVENT_TRACE_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${WAYBILL_PICKUP_EVENT_TRACE_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${WAYBILL_PICKUP_EVENT_TRACE_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="WAYBILL_PICKUP_CODE_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${WAYBILL_PICKUP_CODE_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${WAYBILL_PICKUP_CODE_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="WAYBILL_PICKUP_INFO_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${WAYBILL_PICKUP_INFO_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${WAYBILL_PICKUP_INFO_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="WAYBILL_PKGNEW_INFO_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${WAYBILL_PKGNEW_INFO_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${WAYBILL_PKGNEW_INFO_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="WAYBILL_PRIVACY_INFO_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${WAYBILL_PRIVACY_INFO_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${WAYBILL_PRIVACY_INFO_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="CHARITY_INFO_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${CHARITY_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${CHARITY_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <layout class="com.cainiao.waybill.bridge.common.logger.WebLoggerLayout">
            <pattern><![CDATA[
             %d-%traceId-%C-%L %m%n
            ]]></pattern>
        </layout>
    </appender>

    <appender name="PICK_UP_MANAGER_INFO_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${PICK_UP_MANAGER_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${PICK_UP_MANAGER_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <layout class="com.cainiao.waybill.bridge.common.logger.WebLoggerLayout">
            <pattern><![CDATA[
             %d-%traceId-%C-%L %m%n
            ]]></pattern>
        </layout>
    </appender>

    <appender name="PICK_UP_MONITOR_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${PICK_UP_MONITOR_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${PICK_UP_MONITOR_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="PICK_UP_MONITOR_APPENDER_V2"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${PICK_UP_MONITOR_LOG_FILE_V2}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${PICK_UP_MONITOR_LOG_FILE_V2}.%d{yyyy-MM-dd}.%i</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="PICK_UP_OUTER_EVENT_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${PICK_UP_OUTER_EVENT_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${PICK_UP_OUTER_EVENT_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <layout class="com.cainiao.waybill.bridge.common.logger.WebLoggerLayout">
            <pattern><![CDATA[
             %d-%traceId-%C-%L %m%n
            ]]></pattern>
        </layout>
    </appender>

    <appender name="ALI_SOCIAL_INFO_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${ALI_SOCIAL_INFO_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${ALI_SOCIAL_INFO_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <layout class="com.cainiao.waybill.bridge.common.logger.WebLoggerLayout">
            <pattern><![CDATA[
             %d-%traceId-%C-%L %m%n
            ]]></pattern>
        </layout>
    </appender>
    <appender name="WAYBILL_ADWORKORDER_INFO_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${WAYBILL_ADWORKORDER_INFO_LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${WAYBILL_ADWORKORDER_INFO_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <layout class="com.cainiao.waybill.bridge.common.logger.WebLoggerLayout">
            <pattern><![CDATA[
             %d-%traceId-%C-%L %m%n
            ]]></pattern>
        </layout>
    </appender>

<!--    企业寄件日志-->
    <appender name="WAYBILL_ENTERPRISE_APPENDER"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${WAYBILL_ENTERPRISE_LOG_FILE}</file>
        <encoder>
            <pattern>${ENTERPRISE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${WAYBILL_ENTERPRISE_LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 控制台打印 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>


    <!-- logger -->
    <logger name="WAYBILL_PICKUP_CODE" additivity="false">
        <level value="info"/>
        <appender-ref ref="WAYBILL_PICKUP_CODE_APPENDER"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="WAYBILL_PICKUP_INFO" additivity="false">
        <level value="info"/>
        <appender-ref ref="WAYBILL_PICKUP_INFO_APPENDER"/>
    </logger>

    <logger name="WAYBILL_PKGNEW_INFO" additivity="false">
        <level value="info"/>
        <appender-ref ref="WAYBILL_PKGNEW_INFO_APPENDER"/>
    </logger>

    <logger name="WAYBILL_PRIVACY_INFO" additivity="false">
        <level value="info"/>
        <appender-ref ref="WAYBILL_PRIVACY_INFO_APPENDER"/>
    </logger>

    <logger name="MOBILE_CODE" additivity="false">
        <level value="warn"/>
        <appender-ref ref="MOBILE_CODE_APPENDER"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="WAYBILL_CLOUD_PRINT" additivity="false">
        <level value="warn"/>
        <appender-ref ref="WAYBILL_CLOUD_PRINT_APPENDER"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="TPN_MSG" additivity="false">
        <level value="warn"/>
        <appender-ref ref="TPN_MSG_APPENDER"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="ACCOUNT_ALARM" additivity="false">
        <level value="warn"/>
        <appender-ref ref="ACCOUNT_ALARM_APPENDER"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="BRANCH_SELLER_ADDRESS" additivity="false">
        <level value="warn"/>
        <appender-ref ref="BRANCH_SELLER_ADDRESS_APPENDER"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="MIDDLE_WARE" additivity="false">
        <level value="warn"/>
        <appender-ref ref="MIDDLE_WARE_APPENDER"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="PICKUP_EVENT_TRACE" additivity="false" >
        <level value="warn"/>
        <appender-ref ref="WAYBILL_PICKUP_EVENT_TRACE_APPENDER"/>
    </logger>

    <logger name="biz_monitor" additivity="false" >
        <level value="INFO"/>
        <appender-ref ref="BIZ_MONITOR"/>
    </logger>

    <logger name="LINK_CP" additivity="false">
        <level value="warn"/>
        <appender-ref ref="LINK_CP_APPENDER"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="CHARITY_INFO" additivity="false">
        <level value="info"/>
        <appender-ref ref="CHARITY_INFO_APPENDER"/>
    </logger>

    <logger name="PICK_UP_MANAGER_INFO" additivity="false">
        <level value="info"/>
        <appender-ref ref="PICK_UP_MANAGER_INFO_APPENDER"/>
    </logger>

    <logger name="PICK_UP_MONITOR_INFO" additivity="false">
        <level value="info"/>
        <appender-ref ref="PICK_UP_MONITOR_APPENDER"/>
    </logger>

    <logger name="PICK_UP_MONITOR_INFO_V2" additivity="false">
        <level value="info"/>
        <appender-ref ref="PICK_UP_MONITOR_APPENDER_V2"/>
    </logger>

    <logger name="PICK_UP_OUTER_EVENT" additivity="false">
        <level value="info"/>
        <appender-ref ref="PICK_UP_OUTER_EVENT_APPENDER"/>
    </logger>

    <logger name="ALI_SOCIAL_INFO" additivity="false">
        <level value="info"/>
        <appender-ref ref="ALI_SOCIAL_INFO_APPENDER"/>
    </logger>

    <logger name="com.cainiao.waybill.bridge.alisocial.model.mapper" level="DEBUG" additivity="false">
        <appender-ref ref="ALI_SOCIAL_INFO_APPENDER"/>
    </logger>

    <logger name="WAYBILL_ADWORKORDER_INFO" additivity="false">
        <level value="info"/>
        <appender-ref ref="WAYBILL_ADWORKORDER_INFO_APPENDER"/>
    </logger>

    <logger name="com.cainiao.waybill.bridge.innersupport.ad.model.mapper" level="DEBUG" additivity="false">
        <appender-ref ref="WAYBILL_ADWORKORDER_INFO_APPENDER"/>
    </logger>

    <logger name="com.cainiao.waybill.bridge.model.charity.mapper">
        <level value="debug"/>
        <appender-ref ref="APPLICATION_APPENDER"/>
    </logger>

    <logger name="WAYBILL_ENTERPRISE" additivity="false">
        <level value="info"/>
        <appender-ref ref="WAYBILL_ENTERPRISE_APPENDER"/>
    </logger>

    <root level="WARN">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="APPLICATION_APPENDER"/>
    </root>
</configuration>
