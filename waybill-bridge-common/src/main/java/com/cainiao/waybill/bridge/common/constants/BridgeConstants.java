package com.cainiao.waybill.bridge.common.constants;

/**
 *
 * WaybillBridge常量
 *
 * Created by shouyuan.lzl on 2017-04-19 2:08 PM.
 */
public class BridgeConstants {

    public interface System {
        String APP_NAME="waybill-bridge";
    }

    /**
     * 短信验证码的常量
     */
    public interface MobileCode {

        /**
         * 验证码类型
         */
        // 商家和小件员关系校验
        int VERIFICATION_TYPE_SELLER_COURIER_RELATION = 1 ;


        /**
         * 验证码的有效时间
         */
        // 30分钟有效期
        int MOBILE_CODE_STAY_TIME_30 = 30 * 60 * 1000 ;


        /**
         * 两条验证码的发送间隔
         */
        // 1分钟间隔
        int MOBILE_CODE_INTERVALA_TIME_1 = 1 * 60 * 1000 ;

        /**
         * 验证码验证错误次数和不让验证的时间
         */
        int VERIFICATION_ERROR_COUNT_LIMIT = 10 ;
        int VERIFICATION_ERROR_LIMIT_TIME = 30 * 60 * 1000 ;
    }

    /**
     * 揽件码面单打印状态
     */
    public interface PickupCodePrintStatus {

        /**
         * 未打印
         */
        byte NOT_PRINT = 0;

        /**
         * 已打印
         */
        byte HAS_PRINTED = 1;
    }

    /**
     * 揽件码面单打印状态
     */
    public interface BranchSellerAddRelCoopStatus {

        /**
         * 合作中
         */
        byte COOP = 1;

        /**
         * 取消合作
         */
        byte CANCEL = 0;
    }

    /**
     * 揽件码数字相关信息
     */
    public interface PickupNum {

        /**
         * 最大值(不包含)
         */
        int MAX = 1000000;

        /**
         * 最小值(包含)
         */
        int MIN = 100000;

        /**
         * 数字数量
         */
        int COUNT = MAX - MIN;

        /**
         * 网点序列起始值，随机数边界
         */
        int BRANCH_SEQUENCE_RANDOM_BOUND = 99999;
    }

    /**
     * diamond配置常量
     */
    public interface DiamondConfig {

        /**
         * hsf version diamond data id
         */
        String BRIDGE_HSF_VERSION_DIAMOND_DATA_ID = "waybill.bridge.hsf.version.dataid" ;
        /**
         * hsf version diamond group
         */
        String BRIDGE_HSF_VERSION_DIAMOND_GROUP = "waybill.bridge.hsf.version.group" ;

    }

    /**
     * 千牛消息常量
     */
    public interface TpnMsg{
        String TOPIC = "waybill";
        String TPN_MSG_SEND_ERROR = "TPN_MSG_SEND_ERROR";
    }

    /**
     * WaybillPickupCode feature属性名
     */
    public interface WaybillPickupCodeFeature {
        /**
         * WaybillPickupCode settlement feature key
         */
        String FEATURE_SETTLERMENT = "settlement" ;
    }

    /**
     * BranchSellerAddress feature 属性名
     */
    public interface BranchSellerAddressFeature {
        /**
         * BranchSellerAddress settlementTypeActive 结算类型生效中 feature key
         */
        String FEATURE_SETTLEMENT_TYPE_ACTIVE = "settlementTypeActive" ;
        /**
         * BranchSellerAddress settlementTypeEdit 结算类型编辑中 feature key
         */
        String FEATURE_SETTLEMENT_TYPE_EDIT = "settlementTypeEdit" ;
    }


    public interface ISSTaskType {

        /**
         * 无纸化面单数据下发任务
         */
        String PAPERLESS_DATA_SEND = "waybill_paperless_data_send";

        /**
         * 小件员商家信息下发任务
         */
        String COURIER_SELLER_SEND = "courier_seller_send";
    }

    /**
     * lego 资源保护常量类。（比如下发cp最多线程数）
     */
    public static class ProtectionResource {

        /**
         * 面单详情下发拼接cpCode的前缀
         */
        public static String DATA_PUSH_PREFIX = "dataPush2CP_";


        /**
         * 传入cpCode，拼接前缀获得面单详情下发的保护框架资源名
         *
         * @param cpCode
         * @return
         */
        public static String generatePushRes(String cpCode) {
            return DATA_PUSH_PREFIX + cpCode;
        }

    }

    /**
     * 物流云短信信息
     */
    public interface SMSSendInfo {
        String USER_ID = "3716133075";
        /**
         * 默认短信签名
         */
        String SIGN = "菜鸟";
        /**
         * 默认短信渠道
         */
        String CHANNEL_CODE = "0016";

        /**
         * 淘外寄件用户ID
         */
        String USER_ID_TW = "*************";

        /**
         * 淘外寄件短信签名
         */
        String SIGN_TW = "淘外寄件";

        /**
         * 淘外寄件渠道
         */
        String CHANNEL_CODE_TW = "0073";

        String SELLER_COURIER_RELATION_TEMPLATE = "SMS_10100239" ;
        String ACCOUNT_BALANCE_REMIND_TEMPLATE = "SMS_10100240" ;
    }


    public interface UserSourceInfo {

        /**
         * 用户维度扩展来源字段
         */
        String CN_SOURCE = "cainiao";

    }

    /**
     * 特殊用户账号
     */
    public interface UserAccount {
        String CP_SYSTEM_ACCOUNT = "cp系统";
    }
}
