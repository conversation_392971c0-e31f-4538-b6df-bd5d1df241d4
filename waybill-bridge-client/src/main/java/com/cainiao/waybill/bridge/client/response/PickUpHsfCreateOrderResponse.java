package com.cainiao.waybill.bridge.client.response;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/7/10 21:46
 **/
@Data
public class PickUpHsfCreateOrderResponse implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 外部订单号
     */
    private String outerOrderCode;

    /**
     * 运单号
     */
    private String mailNo;

    /**
     * CP编码
     */
    private String cpCode;

    /**
     * CP订单号
     */
    private String cpOrderId;
}
