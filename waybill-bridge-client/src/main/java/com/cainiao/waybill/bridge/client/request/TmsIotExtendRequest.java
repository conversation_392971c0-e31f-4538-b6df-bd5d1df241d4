package com.cainiao.waybill.bridge.client.request;

import lombok.Data;

/**
 * 支付宝IOT逆向揽收扩展校验信息
 * <AUTHOR>
 * @date 2025/1/8 20:49
 **/
@Data
public class TmsIotExtendRequest {

    /**
     * 序列号
     */
    private String snList;

    /**
     * 是否sn管理
     */
    private boolean isSnMgt;

    /**
     * 品类名称
     */
    private String catName;

    /**
     * 数量
     */
    private Integer itemNum;

    /**
     * 订单保价金额
     */
    private String insuredPrice;

    /**
     * 订单总保价金额
     */
    private String totalInsuredPrice;

    /**
     * 商品名称
     */
    private String itemName;
}
