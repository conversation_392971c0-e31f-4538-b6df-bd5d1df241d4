package com.cainiao.waybill.bridge.common.logger;

import ch.qos.logback.classic.pattern.ClassicConverter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import com.taobao.eagleeye.EagleEye;

public class EagleeyeRpcIdConverter extends ClassicConverter {
    public EagleeyeRpcIdConverter() {
    }

    @Override
    public String convert(ILoggingEvent iLoggingEvent) {
        return EagleEye.getRpcId() != null? EagleEye.getRpcId():"";
    }
}
