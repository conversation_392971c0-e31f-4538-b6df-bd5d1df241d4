package com.cainiao.waybill.bridge.enterprise.administrators.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户详情
 * <AUTHOR>
 * @date 2025-04-16 10:35:36
 */
@Data
public class UserDeptInfoResponse implements Serializable {

    private static final long serialVersionUID = 3523200948377174467L;
    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 父部门ID
     */
    private Long parentDeptId;

    /**
     * 部门名称
     */
    private String deptName;


    /**
     * 部门主管UserId
     */
    private List<String> deptManagerUseridList;



}
