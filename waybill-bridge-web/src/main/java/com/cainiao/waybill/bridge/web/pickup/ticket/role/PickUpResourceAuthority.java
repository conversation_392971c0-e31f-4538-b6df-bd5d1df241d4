package com.cainiao.waybill.bridge.web.pickup.ticket.role;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.List;

import com.cainiao.waybill.bridge.web.pickup.constant.TicketResourceEnum;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface PickUpResourceAuthority {

    TicketResourceEnum value() default TicketResourceEnum.ORDER_MANAGER;

    TicketResourceEnum[] resourceList() default {};
}
