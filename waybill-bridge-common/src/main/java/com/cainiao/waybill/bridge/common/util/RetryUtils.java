package com.cainiao.waybill.bridge.common.util;

import com.github.rholder.retry.Attempt;
import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.google.common.base.Predicate;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * 重试调用
 *
 * @author: yexin
 * @date: 2022-03-14 17:10
 **/
public class RetryUtils {

    public static <T> T retryWithExpectException(Callable<T> callable, int retryNum, int seconds,Throwable e) throws Exception {
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
                .retryIfException((Predicate<Throwable>) e)
                .withWaitStrategy(WaitStrategies.fixedWait(seconds, TimeUnit.SECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(retryNum))
                .build();
        return retryer.call(callable);
    }

    public static <T> T retry(Callable<T> callable, int retryNum, int seconds) throws Exception {
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
                .retryIfException()
                .withWaitStrategy(WaitStrategies.fixedWait(seconds, TimeUnit.SECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(retryNum))
                .build();
        return retryer.call(callable);
    }

    public static <T> T retryMills(Callable<T> callable, int retryNum, int millisecond) throws Exception {
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
            .retryIfException()
            .withWaitStrategy(WaitStrategies.fixedWait(millisecond, TimeUnit.MILLISECONDS))
            .withStopStrategy(StopStrategies.stopAfterAttempt(retryNum))
            .build();
        return retryer.call(callable);
    }

    /**
     * 如果出现异常，默认3秒后重试一次，最多重复2次
     *
     * @param callable :
     * @param <T>      :
     * @return :
     * @throws Exception :
     */
    public static <T> T retry(Callable<T> callable) throws Exception {
        return retry(callable, 2, 3);
    }

    public static <T> T retry(Callable<T> callable, java.util.function.Predicate<T> resultPredicate, int retryNum, int seconds) throws Throwable {
        try{
            RetryerBuilder<T> retryerBuilder = RetryerBuilder.newBuilder();
            Retryer<T> retryer = retryerBuilder
                .retryIfResult(resultPredicate::test)
                .retryIfException()
                .withWaitStrategy(WaitStrategies.fixedWait(seconds, TimeUnit.SECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(retryNum)).build();

            return retryer.call(callable);
        }catch (RetryException retryException){
            Attempt attempt = retryException.getLastFailedAttempt();
            if(attempt.hasException()){
                throw attempt.getExceptionCause();
            }else{
                return (T)attempt.getResult();
            }

        }
    }
}
