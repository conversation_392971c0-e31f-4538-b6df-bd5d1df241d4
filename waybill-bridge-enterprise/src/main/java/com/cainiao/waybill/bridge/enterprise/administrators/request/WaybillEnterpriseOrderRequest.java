package com.cainiao.waybill.bridge.enterprise.administrators.request;

import com.cainiao.waybill.bridge.enterprise.common.BaseRequest;
import com.cainiao.waybill.bridge.model.dto.AddressInfo;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 寄件下单请求
 * @date 2025/4/28 10:55
 * <AUTHOR>
 */
@Data
public class WaybillEnterpriseOrderRequest extends BaseRequest {

    private static final long serialVersionUID = 6406836981984727810L;
    /**
     * 数据库id
     */
    private Long id;
    /**
     *   场地id
     */
    private String locationId;

    /**
     *  运力服务商
     */
    private String cpCode;

    /**
     * 寄件人姓名
     */
    @NotBlank(message = "param error|寄件人姓名为空")
    @Length(max = 32, message = "param error|寄件人姓名最大长度32")
    private String senderName;
    /**
     * 寄件人手机号
     */
    @Length(max = 32, message = "param error|寄件人手机号最大长度32")
    private String senderMobile;
    /**
     * 寄件人电话
     */
    @Length(max = 32, message = "param error|寄件人电话最大长度32")
    private String senderPhone;
    /**
     * 寄件人地址  省、市、详细地址必填
     */
    @NotNull(message = "param error|寄件人地址为空")
    private AddressInfo senderAddress;
    /**
     * 寄件人地址 code
     */
    private AddressInfo senderAddressCode;
    /**
     * 收货人姓名
     */
    @NotBlank(message = "param error|收货人姓名为空")
    @Length(max = 32, message = "param error|收货人姓名最大长度32")
    private String consigneeName;
    /**
     * 收货人手机号
     */
    @Length(max = 32, message = "param error|收货人手机号最大长度32")
    private String consigneePhone;
    /**
     * 收货人电话
     */
    @Length(max = 32, message = "param error|收货人电话最大长度32")
    private String consigneeMobile;
    /**
     * 收货人地址  省、市、详细地址必填
     */
    @NotNull(message = "param error|收货人地址为空")
    private AddressInfo consigneeAddress;

    /**
     * 收货人地址 code
     */
    private AddressInfo consigneeAddressCode;

    /**
     * 物品类型
     */

    private Integer item;


    /**
     * 重量
     */
    private Integer weight;

    /**
     * 备注
     * */
    private String remark;

    /**
     * 费用归属
     */
    private String costAllocation;

    /**
     * 签名
     */
    private String sign;

    /**
     * 业务唯一id
     */
    private String outerOrderCode;

    /**
     * 因公因私
     */
    private Integer businessType;

    /**
     * 寄件产品类型
     */
    private String product;

    /**
     * 月结账号
     */
    private String waybillAccountNo;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 服务商渠道
     */
    private String agent;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 小邮局id
     */
    private String postId;

//    /**
//     * 预约揽收开始时间
//     */
//    private Date appointGotStartTime;
//    /**
//     * 预约揽收结束时间
//     */
//    private Date appointGotEndTime;

    /**
     * 是否保价
     * 0-不保价 1-保价
     */
    private Integer insured;

    /**
     * 保价金额
     */
    private Number insuredValue;

}