package com.cainiao.waybill.bridge.client.hsf;

import com.cainiao.waybill.bridge.client.response.LocOrderResponse;
import com.taobao.logisticsdetail.common.domain.basic.LogisticsDetailDO;

import java.util.List;

/**
 * 电子面单物流详情查询服务
 * <AUTHOR>
 */
public interface WaybillLDQueryService {

    /**
     * 查询物流详情
     * @param mailNo
     * @param cpCode
     * @return
     */
    List<LogisticsDetailDO> queryWaybillLogisticsDetail(String mailNo, String cpCode);

    /**
     * 根据订单号查询面单列表
     * @param orderId
     * @return
     */
    List<String> queryMailNoListByOrderId(Long orderId);

    /**
     * 根据淘宝卖家ID和交易号查询原始订单信息
     * @param sellerId
     * @param tradeId
     * @return
     */
    LocOrderResponse queryLocOrderByTradeId(Long sellerId, Long tradeId);

    /**
     * 根据运单号查询原始订单信息
     * @param mailNo
     * @return
     */
    LocOrderResponse queryLocOrderByMailNo(String mailNo);

}
