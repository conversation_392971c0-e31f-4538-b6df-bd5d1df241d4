package com.cainiao.waybill.bridge.common.label.validator;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.validator.BridgeBaseValidator;
import com.cainiao.waybill.bridge.common.label.dto.AccountBalanceAlarmDTO;
import com.cainiao.waybill.bridge.common.label.dto.AccountBalanceAlarmQueryDTO;
import org.apache.commons.lang.StringUtils;

/**
 * 账户余额告警设置校验
 * <AUTHOR>
 * @since 2017/04/21
 */
public class AccountBalanceAlarmValidator extends BridgeBaseValidator {
    /**
     * 校验条件是否合法
     * @throws BridgeValidationException
     */
    public static void validateSaveAccountBalanceAlarm(AccountBalanceAlarmDTO accountBalanceAlarmDTO, ClientInfoDTO clientInfoDTO) throws
        BridgeValidationException {
        validate(clientInfoDTO != null,"clientInfoDTO不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getAppName()), "clientInfoDTO.appName不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getOperator()), "clientInfoDTO.operator不能为空");
        validate(accountBalanceAlarmDTO != null,"accountBalanceAlarmDTO不能为空");
        validate(accountBalanceAlarmDTO.getAccountId() != null,"accountBalanceAlarmDTO.accountId不能为空");
        validate(accountBalanceAlarmDTO.getCpId() != null,"accountBalanceAlarmDTO.cpId不能为空");
        validate(StringUtils.isNotBlank(accountBalanceAlarmDTO.getBranchCode()), "accountBalanceAlarmDTO.branchCode不能为空");
        validate(accountBalanceAlarmDTO.getAlarmQuantity() != null, "accountBalanceAlarmDTO.alarmQuantity不能为空");
        validate(accountBalanceAlarmDTO.getIntervalHour() != null, "accountBalanceAlarmDTO.intervalHour不能为空");
        validate(StringUtils.isNotBlank(accountBalanceAlarmDTO.getPhone()), "accountBalanceAlarmDTO.phone不能为空");
        validate(StringUtils.isNotBlank(accountBalanceAlarmDTO.getSegmentCode()), "accountBalanceAlarmDTO.segmentCode不能为空");

    }

    /**
     * 校验条件是否合法
     * @throws BridgeValidationException
     */
    public static void validateGetAccountBalanceAlarm(AccountBalanceAlarmQueryDTO accountBalanceAlarmQueryDTO, ClientInfoDTO clientInfoDTO) throws BridgeValidationException{
        validate(clientInfoDTO != null,"clientInfoDTO不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getAppName()), "clientInfoDTO.appName不能为空");
        validate(StringUtils.isNotBlank(clientInfoDTO.getOperator()), "clientInfoDTO.operator不能为空");
        validate(accountBalanceAlarmQueryDTO != null,"accountBalanceAlarmQueryDTO不能为空");
        validate(accountBalanceAlarmQueryDTO.getAccountId() != null,"accountBalanceAlarmQueryDTO.accountId不能为空");
        validate(accountBalanceAlarmQueryDTO.getCpId() != null,"accountBalanceAlarmQueryDTO.cpId不能为空");
        validate(StringUtils.isNotBlank(accountBalanceAlarmQueryDTO.getBranchCode()), "accountBalanceAlarmQueryDTO.branchCode不能为空");
        validate(StringUtils.isNotBlank(accountBalanceAlarmQueryDTO.getSegmentCode()), "accountBalanceAlarmQueryDTO.segmentCode不能为空");

    }
}
