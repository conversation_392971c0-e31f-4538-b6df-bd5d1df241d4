package com.cainiao.waybill.bridge.web.charity.controller;

import com.cainiao.waybill.bridge.biz.charity.request.CharityExPkgDealQueryRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityExPkgLessDealRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityExPkgOtherDealRequest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityExPkgQueryRequest;
import com.cainiao.waybill.bridge.biz.charity.response.CharityExPkgQueryResponse;
import com.cainiao.waybill.bridge.biz.charity.response.CharityPagingResponse;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityAbnormalPackageService;
import com.cainiao.waybill.bridge.biz.utils.BridgeValidator;
import com.cainiao.waybill.bridge.web.charity.CharityBaseResult;
import com.cainiao.waybill.bridge.web.charity.CharityLoginUserUtil;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@Api(tags = "异常包裹管理")
@RestController
@RequestMapping(value = "/charity/package/abnormal")
public class WaybillCharityAbnormalPackageController extends BaseController {

    @Autowired
    private WaybillCharityAbnormalPackageService waybillCharityAbnormalPackageService;


    /**
     * 异常包裹列表查询 支持多种条件筛选
     */
    @ResponseBody
    @RequestMapping("/list")
    public CharityBaseResult<CharityPagingResponse<CharityExPkgQueryResponse>> queryExPkgList(@RequestBody CharityExPkgQueryRequest charityExPkgQueryRequest){
        BridgeValidator.validate(charityExPkgQueryRequest);
        return CharityBaseResult.success(waybillCharityAbnormalPackageService.queryExPkgList(charityExPkgQueryRequest));
    }

    /**
     * 异常包裹处理 包裹少件的情况
     */
    @ResponseBody
    @RequestMapping("/dealLess")
    public CharityBaseResult dealExPkgLess(@RequestBody CharityExPkgLessDealRequest charityExPkgLessDealRequest){
        BridgeValidator.validate(charityExPkgLessDealRequest);
        waybillCharityAbnormalPackageService.dealExPkgLess(charityExPkgLessDealRequest);
        return CharityBaseResult.success();
    }

    /**
     * 异常包裹处理 除包裹少件以外的情况
     */
    @ResponseBody
    @RequestMapping("/dealOther")
    public CharityBaseResult dealExPkgOther(@RequestBody CharityExPkgOtherDealRequest charityExPkgOtherDealRequest){
        BridgeValidator.validate(charityExPkgOtherDealRequest);
        waybillCharityAbnormalPackageService.dealExPkgOther(charityExPkgOtherDealRequest);
        return CharityBaseResult.success();
    }

    /**
     * 查询异常包裹处理详情 状态为已处理时
     */
    @ResponseBody
    @RequestMapping("/queryDealInfo")
    public CharityBaseResult<CharityExPkgQueryResponse> queryDealInfo(@RequestBody CharityExPkgDealQueryRequest charityExPkgDealQueryRequest){
        BridgeValidator.validate(charityExPkgDealQueryRequest);
        return CharityBaseResult.success( waybillCharityAbnormalPackageService.queryDealInfo(charityExPkgDealQueryRequest));
    }

}
