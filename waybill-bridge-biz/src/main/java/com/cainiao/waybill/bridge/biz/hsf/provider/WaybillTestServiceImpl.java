package com.cainiao.waybill.bridge.biz.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.cainiao.waybill.bridge.biz.common.mobileCode.service.SmsService;
import com.cainiao.waybill.bridge.biz.hsf.WaybillTestService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.common.dto.ClientInfoDTO;
import com.cainiao.waybill.common.result.BaseResultDTO;
import com.cainiao.waybill.common.seller.dto.PossibleCpCodesByWaybillCodeRequest;
import com.cainiao.waybill.common.seller.dto.PossibleCpCodesByWaybillCodeResponse;
import com.cainiao.waybill.common.seller.dto.WaybillDetailQueryDTO;
import com.cainiao.waybill.common.seller.dto.WaybillDetailQueryResponseDTO;
import com.cainiao.waybill.common.seller.service.WaybillQueryServiceForInternalSystem;
import com.cainiao.waybill.galaxy.order.domain.dto.BaseResult;
import com.cainiao.waybill.galaxy.order.domain.dto.CloudPrintApplyInfosRequest;
import com.cainiao.waybill.galaxy.order.domain.dto.CloudPrintApplyInfosResponse;
import com.cainiao.waybill.galaxy.order.service.CloudPrintToWaybillInnerService;
import org.apache.commons.lang.exception.ExceptionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 * @date 2022/1/18-下午5:07
 */
@HSFProvider(serviceInterface = WaybillTestService.class)
public class WaybillTestServiceImpl implements WaybillTestService {

    @Resource
    private WaybillQueryServiceForInternalSystem waybillQueryService;

    @Resource
    private CloudPrintToWaybillInnerService cloudPrintToWaybillInnerService;

    @Resource
    private SmsService smsService;

    @Override
    public BaseResult<CloudPrintApplyInfosResponse> CloudPrintInfosForInner(CloudPrintApplyInfosRequest request) {
        return cloudPrintToWaybillInnerService.cloudPrintInfosForInner(request);
    }

    @Override
    public BaseResultDTO<Integer> sendSmsToUser(String templateCode, Map<String, Map<String, String>> mobileToParams,
        String userId, String channelCode, String sign) {
        int count = 0;
        try {
            for (Entry<String, Map<String, String>> stringMapEntry : mobileToParams.entrySet()) {
                boolean result = smsService.sendSms(stringMapEntry.getKey(), templateCode, stringMapEntry.getValue(),
                    userId, channelCode, sign);
                PickUpLogUtil.info("send sms result : " + result);
                if(result){
                    count ++;
                }
            }
        }catch (Throwable throwable){
            PickUpLogUtil.info("sendSmsToUser error : " + ExceptionUtils.getStackTrace(throwable));
        }
        return BaseResultDTO.newSuccessResult(count);
    }

    @Override
    public BaseResultDTO<WaybillDetailQueryResponseDTO> waybillDetailQueryWithoutSellerId(WaybillDetailQueryDTO request, boolean isQueryHistoricalData, ClientInfoDTO clientInfo) {
        BaseResultDTO<WaybillDetailQueryResponseDTO> result = null;
        try {
            result = waybillQueryService.waybillDetailQueryWithoutSellerId(request, isQueryHistoricalData, clientInfo);
        } catch (Throwable e) {
            PickUpLogUtil.info("waybillDetailQueryWithoutSellerId error : " + ExceptionUtils.getStackTrace(e));
        }
        return result;
    }

    @Override
    public BaseResultDTO<List<PossibleCpCodesByWaybillCodeResponse>> queryPossibleCpCodesByWaybillCode(String mailNo, ClientInfoDTO clientInfo) {
        PossibleCpCodesByWaybillCodeRequest request = new PossibleCpCodesByWaybillCodeRequest();
        request.setWaybillCode(mailNo);
        BaseResultDTO<List<PossibleCpCodesByWaybillCodeResponse>> result = null;
        try {
            result = waybillQueryService.queryPossibleCpCodesByWaybillCode(request, clientInfo);
        } catch (Throwable e) {
            PickUpLogUtil.info("queryPossibleCpCodesByWaybillCode error : " + ExceptionUtils.getStackTrace(e));
        }

        return result;
    }

    @Override
    public BaseResultDTO<WaybillDetailQueryResponseDTO> waybillDetailQueryWithoutSellerIdAndCp(String waybillCode, boolean isQueryHistoricalData, ClientInfoDTO clientInfo) {
        BaseResultDTO<WaybillDetailQueryResponseDTO> result = null;
        try {
            result = waybillQueryService.waybillDetailQueryWithoutSellerIdAndCp(waybillCode, isQueryHistoricalData, clientInfo);
        } catch (Throwable e) {
            PickUpLogUtil.info("waybillDetailQueryWithoutSellerIdAndCp error : " + ExceptionUtils.getStackTrace(e));
        }
        return result;
    }
}
