package com.cainiao.waybill.bridge.web.charity.response;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname ChrityImportTemplateRequest
 * @Description
 * @Date 2022/9/8 1:51 下午
 * @Version 1.0
 */
@Data
public class CharityImportTemplateResponse implements Serializable {

    private static final long serialVersionUID = -5232921430583158218L;

    @NotNull
    private String templateFileUrl;

}
