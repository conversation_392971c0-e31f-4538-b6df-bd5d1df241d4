package com.cainiao.waybill;

import com.alibaba.boot.diamond.annotation.DiamondPropertySource;

import com.cainiao.link.annotation.EnableLink;
import com.taobao.pandora.boot.PandoraBootstrap;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.bind.annotation.CrossOrigin;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * Pandora Boot应用的入口类
 * <p>
 * 其中导入sentinel-tracer.xml是加sentinel限流，详情见
 * http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-sentinel
 * <p>
 * 其中@DiamondPropertySource是导入来自Diamond Server的配置，详情见
 * http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-diamond
 *
 * <AUTHOR>
 */
@EnableLink
@EnableScheduling
@SpringBootApplication
@DiamondPropertySource(dataId = "com.taobao.middleware:test.properties")
@ImportResource({"classpath*:sentinel-tracer.xml", "classpath:spring-context.xml", "classpath*:cnmember-account-context.xml"})
@Configuration
@EnableAutoConfiguration(exclude = {DataSourceAutoConfiguration.class, DataSourceTransactionManagerAutoConfiguration.class})
@ComponentScan(basePackages = {"com.cainiao.waybill.bridge.biz",
        "com.cainiao.waybill.bridge.web", "com.cainiao.geography.api",
        "com.cainiao.waybill.bridge.model.charity.dao",
        "com.cainiao.waybill.bridge.model.router.dao",
        "com.cainiao.waybill.bridge.alisocial",
        "com.cainiao.waybill.bridge.enterprise",
        "com.cainiao.waybill.bridge.innersupport.ad"
})
//@CrossOrigin(origins = {"https://page.cainiao.com", "https://page-pre.cainiao.com"})
public class Application {

    public static void main(String[] args) {
        PandoraBootstrap.run(args);
        SpringApplication.run(Application.class, args);
        PandoraBootstrap.markStartupAndWait();
    }
}
