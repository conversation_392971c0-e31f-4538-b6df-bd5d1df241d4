package com.cainiao.waybill.bridge.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/10/08
 */
public class WaybillBridgeTicketParam {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    protected Integer offset;

    protected Integer rows;

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     */
    public WaybillBridgeTicketParam() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @param orderByClause
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @return String
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @param distinct
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @return boolean
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @return List<Criteria>
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @param criteria
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @return Criteria
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @return Criteria
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @return Criteria
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @param offset
     */
    public void setOffset(Integer offset) {
        this.offset=offset;
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @return Integer
     */
    public Integer getOffset() {
        return offset;
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @param rows
     */
    public void setRows(Integer rows) {
        this.rows=rows;
    }

    /**
     * This method corresponds to the database table waybill_bridge_ticket
     * @return Integer
     */
    public Integer getRows() {
        return rows;
    }

    protected abstract static class AbstractGeneratedCriteria {
        protected List<Criterion> criteria;

        protected AbstractGeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNull() {
            addCriterion("gmt_create is null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIsNotNull() {
            addCriterion("gmt_create is not null");
            return (Criteria) this;
        }

        public Criteria andGmtCreateEqualTo(Date value) {
            addCriterion("gmt_create =", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotEqualTo(Date value) {
            addCriterion("gmt_create <>", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThan(Date value) {
            addCriterion("gmt_create >", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_create >=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThan(Date value) {
            addCriterion("gmt_create <", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateLessThanOrEqualTo(Date value) {
            addCriterion("gmt_create <=", value, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateIn(List<Date> values) {
            addCriterion("gmt_create in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotIn(List<Date> values) {
            addCriterion("gmt_create not in", values, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateBetween(Date value1, Date value2) {
            addCriterion("gmt_create between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtCreateNotBetween(Date value1, Date value2) {
            addCriterion("gmt_create not between", value1, value2, "gmtCreate");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNull() {
            addCriterion("gmt_modified is null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIsNotNull() {
            addCriterion("gmt_modified is not null");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedEqualTo(Date value) {
            addCriterion("gmt_modified =", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotEqualTo(Date value) {
            addCriterion("gmt_modified <>", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThan(Date value) {
            addCriterion("gmt_modified >", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("gmt_modified >=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThan(Date value) {
            addCriterion("gmt_modified <", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedLessThanOrEqualTo(Date value) {
            addCriterion("gmt_modified <=", value, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedIn(List<Date> values) {
            addCriterion("gmt_modified in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotIn(List<Date> values) {
            addCriterion("gmt_modified not in", values, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedBetween(Date value1, Date value2) {
            addCriterion("gmt_modified between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andGmtModifiedNotBetween(Date value1, Date value2) {
            addCriterion("gmt_modified not between", value1, value2, "gmtModified");
            return (Criteria) this;
        }

        public Criteria andMailNoIsNull() {
            addCriterion("mail_no is null");
            return (Criteria) this;
        }

        public Criteria andMailNoIsNotNull() {
            addCriterion("mail_no is not null");
            return (Criteria) this;
        }

        public Criteria andMailNoEqualTo(String value) {
            addCriterion("mail_no =", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoNotEqualTo(String value) {
            addCriterion("mail_no <>", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoGreaterThan(String value) {
            addCriterion("mail_no >", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoGreaterThanOrEqualTo(String value) {
            addCriterion("mail_no >=", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoLessThan(String value) {
            addCriterion("mail_no <", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoLessThanOrEqualTo(String value) {
            addCriterion("mail_no <=", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoLike(String value) {
            addCriterion("mail_no like", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoNotLike(String value) {
            addCriterion("mail_no not like", value, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoIn(List<String> values) {
            addCriterion("mail_no in", values, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoNotIn(List<String> values) {
            addCriterion("mail_no not in", values, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoBetween(String value1, String value2) {
            addCriterion("mail_no between", value1, value2, "mailNo");
            return (Criteria) this;
        }

        public Criteria andMailNoNotBetween(String value1, String value2) {
            addCriterion("mail_no not between", value1, value2, "mailNo");
            return (Criteria) this;
        }

        public Criteria andIntoBoxTimeIsNull() {
            addCriterion("into_box_time is null");
            return (Criteria) this;
        }

        public Criteria andIntoBoxTimeIsNotNull() {
            addCriterion("into_box_time is not null");
            return (Criteria) this;
        }

        public Criteria andIntoBoxTimeEqualTo(Date value) {
            addCriterion("into_box_time =", value, "intoBoxTime");
            return (Criteria) this;
        }

        public Criteria andIntoBoxTimeNotEqualTo(Date value) {
            addCriterion("into_box_time <>", value, "intoBoxTime");
            return (Criteria) this;
        }

        public Criteria andIntoBoxTimeGreaterThan(Date value) {
            addCriterion("into_box_time >", value, "intoBoxTime");
            return (Criteria) this;
        }

        public Criteria andIntoBoxTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("into_box_time >=", value, "intoBoxTime");
            return (Criteria) this;
        }

        public Criteria andIntoBoxTimeLessThan(Date value) {
            addCriterion("into_box_time <", value, "intoBoxTime");
            return (Criteria) this;
        }

        public Criteria andIntoBoxTimeLessThanOrEqualTo(Date value) {
            addCriterion("into_box_time <=", value, "intoBoxTime");
            return (Criteria) this;
        }

        public Criteria andIntoBoxTimeIn(List<Date> values) {
            addCriterion("into_box_time in", values, "intoBoxTime");
            return (Criteria) this;
        }

        public Criteria andIntoBoxTimeNotIn(List<Date> values) {
            addCriterion("into_box_time not in", values, "intoBoxTime");
            return (Criteria) this;
        }

        public Criteria andIntoBoxTimeBetween(Date value1, Date value2) {
            addCriterion("into_box_time between", value1, value2, "intoBoxTime");
            return (Criteria) this;
        }

        public Criteria andIntoBoxTimeNotBetween(Date value1, Date value2) {
            addCriterion("into_box_time not between", value1, value2, "intoBoxTime");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeIsNull() {
            addCriterion("operator_type is null");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeIsNotNull() {
            addCriterion("operator_type is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeEqualTo(String value) {
            addCriterion("operator_type =", value, "operatorType");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeNotEqualTo(String value) {
            addCriterion("operator_type <>", value, "operatorType");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeGreaterThan(String value) {
            addCriterion("operator_type >", value, "operatorType");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeGreaterThanOrEqualTo(String value) {
            addCriterion("operator_type >=", value, "operatorType");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeLessThan(String value) {
            addCriterion("operator_type <", value, "operatorType");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeLessThanOrEqualTo(String value) {
            addCriterion("operator_type <=", value, "operatorType");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeLike(String value) {
            addCriterion("operator_type like", value, "operatorType");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeNotLike(String value) {
            addCriterion("operator_type not like", value, "operatorType");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeIn(List<String> values) {
            addCriterion("operator_type in", values, "operatorType");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeNotIn(List<String> values) {
            addCriterion("operator_type not in", values, "operatorType");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeBetween(String value1, String value2) {
            addCriterion("operator_type between", value1, value2, "operatorType");
            return (Criteria) this;
        }

        public Criteria andOperatorTypeNotBetween(String value1, String value2) {
            addCriterion("operator_type not between", value1, value2, "operatorType");
            return (Criteria) this;
        }

        public Criteria andTicketTypeIsNull() {
            addCriterion("ticket_type is null");
            return (Criteria) this;
        }

        public Criteria andTicketTypeIsNotNull() {
            addCriterion("ticket_type is not null");
            return (Criteria) this;
        }

        public Criteria andTicketTypeEqualTo(String value) {
            addCriterion("ticket_type =", value, "ticketType");
            return (Criteria) this;
        }

        public Criteria andTicketTypeNotEqualTo(String value) {
            addCriterion("ticket_type <>", value, "ticketType");
            return (Criteria) this;
        }

        public Criteria andTicketTypeGreaterThan(String value) {
            addCriterion("ticket_type >", value, "ticketType");
            return (Criteria) this;
        }

        public Criteria andTicketTypeGreaterThanOrEqualTo(String value) {
            addCriterion("ticket_type >=", value, "ticketType");
            return (Criteria) this;
        }

        public Criteria andTicketTypeLessThan(String value) {
            addCriterion("ticket_type <", value, "ticketType");
            return (Criteria) this;
        }

        public Criteria andTicketTypeLessThanOrEqualTo(String value) {
            addCriterion("ticket_type <=", value, "ticketType");
            return (Criteria) this;
        }

        public Criteria andTicketTypeLike(String value) {
            addCriterion("ticket_type like", value, "ticketType");
            return (Criteria) this;
        }

        public Criteria andTicketTypeNotLike(String value) {
            addCriterion("ticket_type not like", value, "ticketType");
            return (Criteria) this;
        }

        public Criteria andTicketTypeIn(List<String> values) {
            addCriterion("ticket_type in", values, "ticketType");
            return (Criteria) this;
        }

        public Criteria andTicketTypeNotIn(List<String> values) {
            addCriterion("ticket_type not in", values, "ticketType");
            return (Criteria) this;
        }

        public Criteria andTicketTypeBetween(String value1, String value2) {
            addCriterion("ticket_type between", value1, value2, "ticketType");
            return (Criteria) this;
        }

        public Criteria andTicketTypeNotBetween(String value1, String value2) {
            addCriterion("ticket_type not between", value1, value2, "ticketType");
            return (Criteria) this;
        }

        public Criteria andTicketStatusIsNull() {
            addCriterion("ticket_status is null");
            return (Criteria) this;
        }

        public Criteria andTicketStatusIsNotNull() {
            addCriterion("ticket_status is not null");
            return (Criteria) this;
        }

        public Criteria andTicketStatusEqualTo(String value) {
            addCriterion("ticket_status =", value, "ticketStatus");
            return (Criteria) this;
        }

        public Criteria andTicketStatusNotEqualTo(String value) {
            addCriterion("ticket_status <>", value, "ticketStatus");
            return (Criteria) this;
        }

        public Criteria andTicketStatusGreaterThan(String value) {
            addCriterion("ticket_status >", value, "ticketStatus");
            return (Criteria) this;
        }

        public Criteria andTicketStatusGreaterThanOrEqualTo(String value) {
            addCriterion("ticket_status >=", value, "ticketStatus");
            return (Criteria) this;
        }

        public Criteria andTicketStatusLessThan(String value) {
            addCriterion("ticket_status <", value, "ticketStatus");
            return (Criteria) this;
        }

        public Criteria andTicketStatusLessThanOrEqualTo(String value) {
            addCriterion("ticket_status <=", value, "ticketStatus");
            return (Criteria) this;
        }

        public Criteria andTicketStatusLike(String value) {
            addCriterion("ticket_status like", value, "ticketStatus");
            return (Criteria) this;
        }

        public Criteria andTicketStatusNotLike(String value) {
            addCriterion("ticket_status not like", value, "ticketStatus");
            return (Criteria) this;
        }

        public Criteria andTicketStatusIn(List<String> values) {
            addCriterion("ticket_status in", values, "ticketStatus");
            return (Criteria) this;
        }

        public Criteria andTicketStatusNotIn(List<String> values) {
            addCriterion("ticket_status not in", values, "ticketStatus");
            return (Criteria) this;
        }

        public Criteria andTicketStatusBetween(String value1, String value2) {
            addCriterion("ticket_status between", value1, value2, "ticketStatus");
            return (Criteria) this;
        }

        public Criteria andTicketStatusNotBetween(String value1, String value2) {
            addCriterion("ticket_status not between", value1, value2, "ticketStatus");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentIsNull() {
            addCriterion("ticket_create_content is null");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentIsNotNull() {
            addCriterion("ticket_create_content is not null");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentEqualTo(String value) {
            addCriterion("ticket_create_content =", value, "ticketCreateContent");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentNotEqualTo(String value) {
            addCriterion("ticket_create_content <>", value, "ticketCreateContent");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentGreaterThan(String value) {
            addCriterion("ticket_create_content >", value, "ticketCreateContent");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentGreaterThanOrEqualTo(String value) {
            addCriterion("ticket_create_content >=", value, "ticketCreateContent");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentLessThan(String value) {
            addCriterion("ticket_create_content <", value, "ticketCreateContent");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentLessThanOrEqualTo(String value) {
            addCriterion("ticket_create_content <=", value, "ticketCreateContent");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentLike(String value) {
            addCriterion("ticket_create_content like", value, "ticketCreateContent");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentNotLike(String value) {
            addCriterion("ticket_create_content not like", value, "ticketCreateContent");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentIn(List<String> values) {
            addCriterion("ticket_create_content in", values, "ticketCreateContent");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentNotIn(List<String> values) {
            addCriterion("ticket_create_content not in", values, "ticketCreateContent");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentBetween(String value1, String value2) {
            addCriterion("ticket_create_content between", value1, value2, "ticketCreateContent");
            return (Criteria) this;
        }

        public Criteria andTicketCreateContentNotBetween(String value1, String value2) {
            addCriterion("ticket_create_content not between", value1, value2, "ticketCreateContent");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentIsNull() {
            addCriterion("ticket_resp_content is null");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentIsNotNull() {
            addCriterion("ticket_resp_content is not null");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentEqualTo(String value) {
            addCriterion("ticket_resp_content =", value, "ticketRespContent");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentNotEqualTo(String value) {
            addCriterion("ticket_resp_content <>", value, "ticketRespContent");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentGreaterThan(String value) {
            addCriterion("ticket_resp_content >", value, "ticketRespContent");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentGreaterThanOrEqualTo(String value) {
            addCriterion("ticket_resp_content >=", value, "ticketRespContent");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentLessThan(String value) {
            addCriterion("ticket_resp_content <", value, "ticketRespContent");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentLessThanOrEqualTo(String value) {
            addCriterion("ticket_resp_content <=", value, "ticketRespContent");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentLike(String value) {
            addCriterion("ticket_resp_content like", value, "ticketRespContent");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentNotLike(String value) {
            addCriterion("ticket_resp_content not like", value, "ticketRespContent");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentIn(List<String> values) {
            addCriterion("ticket_resp_content in", values, "ticketRespContent");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentNotIn(List<String> values) {
            addCriterion("ticket_resp_content not in", values, "ticketRespContent");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentBetween(String value1, String value2) {
            addCriterion("ticket_resp_content between", value1, value2, "ticketRespContent");
            return (Criteria) this;
        }

        public Criteria andTicketRespContentNotBetween(String value1, String value2) {
            addCriterion("ticket_resp_content not between", value1, value2, "ticketRespContent");
            return (Criteria) this;
        }

        public Criteria andTicketFilesIsNull() {
            addCriterion("ticket_files is null");
            return (Criteria) this;
        }

        public Criteria andTicketFilesIsNotNull() {
            addCriterion("ticket_files is not null");
            return (Criteria) this;
        }

        public Criteria andTicketFilesEqualTo(String value) {
            addCriterion("ticket_files =", value, "ticketFiles");
            return (Criteria) this;
        }

        public Criteria andTicketFilesNotEqualTo(String value) {
            addCriterion("ticket_files <>", value, "ticketFiles");
            return (Criteria) this;
        }

        public Criteria andTicketFilesGreaterThan(String value) {
            addCriterion("ticket_files >", value, "ticketFiles");
            return (Criteria) this;
        }

        public Criteria andTicketFilesGreaterThanOrEqualTo(String value) {
            addCriterion("ticket_files >=", value, "ticketFiles");
            return (Criteria) this;
        }

        public Criteria andTicketFilesLessThan(String value) {
            addCriterion("ticket_files <", value, "ticketFiles");
            return (Criteria) this;
        }

        public Criteria andTicketFilesLessThanOrEqualTo(String value) {
            addCriterion("ticket_files <=", value, "ticketFiles");
            return (Criteria) this;
        }

        public Criteria andTicketFilesLike(String value) {
            addCriterion("ticket_files like", value, "ticketFiles");
            return (Criteria) this;
        }

        public Criteria andTicketFilesNotLike(String value) {
            addCriterion("ticket_files not like", value, "ticketFiles");
            return (Criteria) this;
        }

        public Criteria andTicketFilesIn(List<String> values) {
            addCriterion("ticket_files in", values, "ticketFiles");
            return (Criteria) this;
        }

        public Criteria andTicketFilesNotIn(List<String> values) {
            addCriterion("ticket_files not in", values, "ticketFiles");
            return (Criteria) this;
        }

        public Criteria andTicketFilesBetween(String value1, String value2) {
            addCriterion("ticket_files between", value1, value2, "ticketFiles");
            return (Criteria) this;
        }

        public Criteria andTicketFilesNotBetween(String value1, String value2) {
            addCriterion("ticket_files not between", value1, value2, "ticketFiles");
            return (Criteria) this;
        }

        public Criteria andHurryMarkIsNull() {
            addCriterion("hurry_mark is null");
            return (Criteria) this;
        }

        public Criteria andHurryMarkIsNotNull() {
            addCriterion("hurry_mark is not null");
            return (Criteria) this;
        }

        public Criteria andHurryMarkEqualTo(Byte value) {
            addCriterion("hurry_mark =", value, "hurryMark");
            return (Criteria) this;
        }

        public Criteria andHurryMarkNotEqualTo(Byte value) {
            addCriterion("hurry_mark <>", value, "hurryMark");
            return (Criteria) this;
        }

        public Criteria andHurryMarkGreaterThan(Byte value) {
            addCriterion("hurry_mark >", value, "hurryMark");
            return (Criteria) this;
        }

        public Criteria andHurryMarkGreaterThanOrEqualTo(Byte value) {
            addCriterion("hurry_mark >=", value, "hurryMark");
            return (Criteria) this;
        }

        public Criteria andHurryMarkLessThan(Byte value) {
            addCriterion("hurry_mark <", value, "hurryMark");
            return (Criteria) this;
        }

        public Criteria andHurryMarkLessThanOrEqualTo(Byte value) {
            addCriterion("hurry_mark <=", value, "hurryMark");
            return (Criteria) this;
        }

        public Criteria andHurryMarkIn(List<Byte> values) {
            addCriterion("hurry_mark in", values, "hurryMark");
            return (Criteria) this;
        }

        public Criteria andHurryMarkNotIn(List<Byte> values) {
            addCriterion("hurry_mark not in", values, "hurryMark");
            return (Criteria) this;
        }

        public Criteria andHurryMarkBetween(Byte value1, Byte value2) {
            addCriterion("hurry_mark between", value1, value2, "hurryMark");
            return (Criteria) this;
        }

        public Criteria andHurryMarkNotBetween(Byte value1, Byte value2) {
            addCriterion("hurry_mark not between", value1, value2, "hurryMark");
            return (Criteria) this;
        }

        public Criteria andFeatureIsNull() {
            addCriterion("feature is null");
            return (Criteria) this;
        }

        public Criteria andFeatureIsNotNull() {
            addCriterion("feature is not null");
            return (Criteria) this;
        }

        public Criteria andFeatureEqualTo(String value) {
            addCriterion("feature =", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotEqualTo(String value) {
            addCriterion("feature <>", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThan(String value) {
            addCriterion("feature >", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureGreaterThanOrEqualTo(String value) {
            addCriterion("feature >=", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLessThan(String value) {
            addCriterion("feature <", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLessThanOrEqualTo(String value) {
            addCriterion("feature <=", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureLike(String value) {
            addCriterion("feature like", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotLike(String value) {
            addCriterion("feature not like", value, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureIn(List<String> values) {
            addCriterion("feature in", values, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotIn(List<String> values) {
            addCriterion("feature not in", values, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureBetween(String value1, String value2) {
            addCriterion("feature between", value1, value2, "feature");
            return (Criteria) this;
        }

        public Criteria andFeatureNotBetween(String value1, String value2) {
            addCriterion("feature not between", value1, value2, "feature");
            return (Criteria) this;
        }
    }

    public static class Criteria extends AbstractGeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}