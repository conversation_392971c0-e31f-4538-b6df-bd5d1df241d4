package com.cainiao.waybill.bridge.common.config.diamond;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.alibaba.common.lang.ExceptionUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.common.constants.PickUpConfigConstants;
import com.google.common.collect.Maps;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2022/07/26-下午3:16
 */
public class GuoguoDiamondConfig {

    public static final Logger infoLogger = LoggerFactory.getLogger("WAYBILL_PICKUP_INFO");

    private static GuoguoConfig GUOGUO_CONFIG = new GuoguoConfig();

    private static void initConfig() {
        // 启动只用一次场景，直接get获取配置值
        try {
            String configInfo = Diamond
                .getConfig(PickUpConfigConstants.DiamondConfig.PICK_UP_GUOGUO_CONFIG_DATA_ID,
                    PickUpConfigConstants.DiamondConfig.PICK_UP_GUOGUO_CONFIG_GROUP_ID, 3000);
            parseVmVersionConfig(configInfo);
        } catch (IOException e1) {
            infoLogger.info("获取diamond中裹裹配置信息：" + ExceptionUtil.getStackTrace(e1));
        }
        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(PickUpConfigConstants.DiamondConfig.PICK_UP_GUOGUO_CONFIG_DATA_ID,
            PickUpConfigConstants.DiamondConfig.PICK_UP_GUOGUO_CONFIG_GROUP_ID,
            new ManagerListenerAdapter() {
                @Override
                public void receiveConfigInfo(String configInfo) {
                    try {
                        parseVmVersionConfig(configInfo);
                    } catch (Exception e) {
                        infoLogger.info(
                            "ManagerListenerAdapter获取diamond中配置的裹裹配置信息：" + ExceptionUtil.getStackTrace(e));
                    }
                }
            });
    }

    public static GuoguoConfig getGuoguoConfig(){
        return GUOGUO_CONFIG;
    }

    static {
        initConfig();
    }

    private static void parseVmVersionConfig(String configInfo) {
        if (StringUtil.isBlank(configInfo)) {
            return;
        }
        GUOGUO_CONFIG = JSONObject.parseObject(configInfo, GuoguoConfig.class);
    }

    @Data
    public static class GuoguoConfig {

        private String orderAccountId;

        private Integer orderAccountType;

        private Integer terminalSource;

        /**
         * 注意！这里会影响裹裹的预约节点回传的消息消费，在修改前需要先check，com.cainiao.waybill.bridge.biz.middleware.metaq.WaybillPickUpGuoguoEventConsumer
         */
        private Long itemId;

        private String channelId;

        private String bizCode;

        private String entrySource;

        /**
         * 注意！这里会影响裹裹的预约节点回传的消息消费，在修改前需要先check，com.cainiao.waybill.bridge.biz.middleware.metaq.WaybillPickUpGuoguoEventConsumer
         */
        private String accessCode;
    }
}
