package com.cainiao.waybill.bridge.biz.utils.client;

import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.lop.open.api.sdk.DefaultDomainApiClient;

/**
 * 京东SDK接口调用客户端
 * <AUTHOR>
 * @date 2024/5/28 下午5:23
 **/
public class JdApiClientSingleton {

    private static DefaultDomainApiClient clientInstance;

    private JdApiClientSingleton() {

    }

    /**
     * 构造API客户端
     * 注意：由于只实例化了一次，如果更改URL或超时时间需要重新启动应用
     * @return
     */
    public static DefaultDomainApiClient getInstance() {
        if (clientInstance == null) {
            synchronized (DefaultDomainApiClient.class) {
                if (clientInstance == null) {
                    clientInstance = new DefaultDomainApiClient(BridgeSwitch.JD_API_SERVICE_URL,
                        BridgeSwitch.JD_API_SERVICE_CONNECT_TIMEOUT,
                        BridgeSwitch.JD_API_SERVICE_READ_TIMEOUT);
                }
            }
        }
        return clientInstance;
    }


}
