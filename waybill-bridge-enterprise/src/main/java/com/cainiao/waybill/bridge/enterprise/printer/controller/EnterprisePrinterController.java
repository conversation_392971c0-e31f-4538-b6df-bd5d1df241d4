package com.cainiao.waybill.bridge.enterprise.printer.controller;

import com.alibaba.fastjson.JSON;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.common.UserContext;
import com.cainiao.waybill.bridge.enterprise.printer.request.EnterprisePrinterRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.PrintContractRequest;
import com.cainiao.waybill.bridge.enterprise.administrators.request.UpdatePrinterLocationRequest;
import com.cainiao.waybill.bridge.enterprise.printer.service.EnterprisePrinterService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WayBillEnterprisePrinterDTO;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Description;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25
 **/
@RestController
@RequestMapping(value = "/enterprise/printer")
public class EnterprisePrinterController {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Resource
    private EnterprisePrinterService enterprisePrinterService;

    @PostMapping("/page")
    @Description("打印机列表页分页查询")
    EnterpriseBaseResult<BridgePagingDTO<WayBillEnterprisePrinterDTO>> page(@RequestBody EnterprisePrinterRequest request) {
        LOGGER.info("page request:{}", JSON.toJSONString(request));
        try {
            return EnterpriseBaseResult.success(enterprisePrinterService.pageList(request));
        } catch (Exception e) {
            LOGGER.error("page error :{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "查询打印机列表失败");
        }
    }

    @GetMapping("/getVerifyCode")
    @Description("打印机验证码打印")
    EnterpriseBaseResult<Void> getVerifyCode(@RequestParam("printerId") String printerId) {
        LOGGER.info("getVerifyCode printerId:{}", printerId);
        try {
            enterprisePrinterService.getVerifyCode(printerId);
            return EnterpriseBaseResult.success();
        } catch (Exception e) {
            LOGGER.error("getVerifyCode error :{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "打印验证码失败");
        }
    }

    @PostMapping("/addPrinter")
    @Description("打印机新增")
    EnterpriseBaseResult<String> addPrinter(@RequestBody EnterprisePrinterRequest request) {
        LOGGER.info("addPrinter request:{}", JSON.toJSONString(request));
        try {
            return EnterpriseBaseResult.success(enterprisePrinterService.addPrinter(request));
        } catch (Exception e) {
            LOGGER.error("addPrinter error :{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @PostMapping("/editPrinter")
    @Description("打印机编辑")
    EnterpriseBaseResult<Boolean> editPrinter(@RequestBody EnterprisePrinterRequest request) {
        LOGGER.info("editPrinter request:{}", JSON.toJSONString(request));
        try {
            request.setCorpId(UserContext.getUser().getCorpId());
            int updateResult = enterprisePrinterService.updatePrinter(request);
            return EnterpriseBaseResult.success(updateResult == 1);
        } catch (Exception e) {
            LOGGER.error("editPrinter error :{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "打印机编辑失败");
        }
    }

    @GetMapping("/deletePrinterById")
    @Description("打印机删除")
    @Deprecated()
    EnterpriseBaseResult<Boolean> deletePrinterById(@RequestParam("id") Long id) {
        LOGGER.info("deletePrinterById id:{}", id);
        try {
            int deleteResult = enterprisePrinterService.deleteById(id);
            return EnterpriseBaseResult.success(deleteResult == 1);
        } catch (Exception e) {
            LOGGER.error("deletePrinterById error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "删除打印机失败" + e.getMessage());
        }
    }

    @GetMapping("/getPrinterById")
    @Description("根据打印机id获取打印机")
    @Deprecated
    EnterpriseBaseResult<WayBillEnterprisePrinterDTO> getPrinterById(@RequestParam("id") Long id) {
        LOGGER.info("getPrinterById id:{}", id);
        try {
            WayBillEnterprisePrinterDTO wayBillEnterprisePrinterDTO = enterprisePrinterService.getPrinterById(id);
            return EnterpriseBaseResult.success(wayBillEnterprisePrinterDTO);
        } catch (Exception e) {
            LOGGER.error("deletePrinterById error:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("/getPrinterListByLocationName")
    @Description("根据场地名称获取打印机")
    EnterpriseBaseResult<List<WayBillEnterprisePrinterDTO>> getPrinterListByLocationName(@RequestParam("locationName") String locationName,
                                                                                         @RequestParam("corpId") String corpId) {
        LOGGER.info("getPrinterListByLocationName locationName:{}, corpId:{}", locationName, corpId);
        try {
            List<WayBillEnterprisePrinterDTO> enterprisePrinterDTOS = enterprisePrinterService.getListByLocationName(locationName, corpId);
            return EnterpriseBaseResult.success(enterprisePrinterDTOS);
        } catch (Exception e) {
            LOGGER.error("getPrinterListByLocationName error: {}",  ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @PostMapping("/updatePrinterLocation")
    @Description("修改打印机场地")
    EnterpriseBaseResult<Boolean> updatePrinterLocation(@RequestBody UpdatePrinterLocationRequest request) {
        LOGGER.info("updatePrinterLocation request:{}", JSON.toJSONString(request));
        try {
            enterprisePrinterService.batchUpdate(request);
            return EnterpriseBaseResult.success();
        } catch (Exception e) {
            LOGGER.error("addPrinterLocation error :{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @PostMapping("/printExpressContractInformation")
    @Description("打印面单联系人信息")
    EnterpriseBaseResult<Boolean> printExpressContractInformation(@RequestBody PrintContractRequest request) {
        LOGGER.info("printExpressContractInformation request:{}", JSON.toJSONString(request));
        try {
            enterprisePrinterService.printExpressContractInformation(request);
            return EnterpriseBaseResult.success();
        } catch (Exception e) {
            LOGGER.error("printExpressContractInformation error:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }

    @GetMapping("/deletePrinterByPrinterId")
    @Description("打印机删除")
    EnterpriseBaseResult<Boolean> deletePrinterByPrinterId(@RequestParam("printerId") String printerId) {
        LOGGER.info("deletePrinterByPrinterId printerId:{}", printerId);
        try {
            int deleteResult = enterprisePrinterService.deleteByPrinterId(printerId, UserContext.getUser().getCorpId());
            return EnterpriseBaseResult.success(deleteResult == 1);
        } catch (Exception e) {
            LOGGER.error("deletePrinterByPrinterId error: {}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), "删除打印机失败" + e.getMessage());
        }
    }

    @GetMapping("/getPrinterByPrinterId")
    @Description("根据打印机id获取打印机")
    EnterpriseBaseResult<WayBillEnterprisePrinterDTO> getPrinterByPrinterId(@RequestParam("printerId") String printerId) {
        LOGGER.info("getPrinterByPrinterId printerId:{}", printerId);
        try {
            WayBillEnterprisePrinterDTO wayBillEnterprisePrinterDTO = enterprisePrinterService.getPrinterByPrinterId(printerId, UserContext.getUser().getCorpId());
            return EnterpriseBaseResult.success(wayBillEnterprisePrinterDTO);
        } catch (Exception e) {
            LOGGER.error("getPrinterByPrinterId error:{}", ExceptionUtils.getStackTrace(e));
            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage());
        }
    }
}
