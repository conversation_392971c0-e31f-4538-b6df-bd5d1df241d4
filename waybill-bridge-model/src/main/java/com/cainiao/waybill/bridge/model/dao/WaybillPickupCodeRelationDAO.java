package com.cainiao.waybill.bridge.model.dao;

import com.cainiao.waybill.bridge.common.util.Page;
import com.cainiao.waybill.bridge.model.dao.bean.PickupCodeRelationQuery;
import com.cainiao.waybill.bridge.model.domain.WaybillPickupCodeRelationDO;
import com.taobao.common.dao.persistence.exception.DAOException;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2017-04-26
 */
public interface WaybillPickupCodeRelationDAO {

    /**
     * 根据电子面单号查询对应的映射关系
     *
     * @param courierId 小件员id
     * @param cpCode cp编码
     * @param waybillCode 电子面单号
     * @throws DAOException
     * @return 映射关系
     */
    WaybillPickupCodeRelationDO queryByWaybillCode(Long courierId, String cpCode, String waybillCode) throws DAOException;

    /**
     * 分页查询
     * @param query
     * @param pageNo
     * @param pageSize
     * @return
     * @throws DAOException
     */
    Page<WaybillPickupCodeRelationDO> queryPickupWaybillWithPage(PickupCodeRelationQuery query, int pageNo, int pageSize) throws DAOException;

    /**
     * 基本插入
     * @param relationDO
     * @return
     * @throws DAOException
     */
    Integer insert(WaybillPickupCodeRelationDO relationDO) throws DAOException;

    /**
     * 更新打印状态
     * @param id
     * @param courierId
     * @param printStatus
     * @return
     * @throws DAOException
     */
    Integer updatePrintStatusById(Long id, Long courierId, Byte printStatus) throws DAOException;

    /**
     * 更新重量
     * @param id
     * @param courierId
     * @param weight
     * @return
     * @throws DAOException
     */
    Integer updateWeightById(Long id, Long courierId, Integer weight) throws DAOException;
}
