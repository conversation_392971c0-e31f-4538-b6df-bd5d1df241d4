package com.cainiao.waybill.bridge.biz.link.pickup.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants;
import com.cainiao.waybill.bridge.biz.pickup.constants.PickUpCreateOrderParamConstants;
import com.cainiao.waybill.bridge.biz.pickup.dto.GoodsInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.update.PickUpUpdateOrderRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.update.PickUpUpdateOrderResponse;
import com.cainiao.waybill.bridge.biz.pickup.service.WaybillPickUpOrderService;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpCommonUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.wrapper.ContentRiskWrapper;
import com.cainiao.waybill.bridge.common.base.ScenarioConstant;
import com.cainiao.waybill.bridge.common.exception.BridgeBaseException;
import com.cainiao.waybill.bridge.common.util.LoggerMonitorUtil;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import com.google.common.collect.Lists;
import com.taobao.pac.api.open.ReceiveParams;
import com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_MODIFY.WaybillPickUpModifyRequest;
import com.taobao.pac.client.sdk.dataobject.response.WAYBILL_PICK_UP_MODIFY.WaybillPickUpModifyResponse;
import com.taobao.pac.client.sdk.receiveservice.WaybillPickUpModifyService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName: TmsWaybillPickUpModifyServiceImpl
 * @description 修改上门取件订单
 * @date 2023/12/26 16:16
 */
public class TmsWaybillPickUpModifyServiceImpl implements WaybillPickUpModifyService {

    @Resource
    private WaybillPickUpOrderService waybillPickUpOrderService;

    @Resource
    private ContentRiskWrapper contentRiskWrapper;

    @Override
    public WaybillPickUpModifyResponse invoke(ReceiveParams<WaybillPickUpModifyRequest> receiveParams) {
        PickUpLogUtil.info("淘外订单修改入参：TmsWaybillPickUpModifyServiceImpl = " + JSON.toJSONString(receiveParams));
        WaybillPickUpModifyResponse response = new WaybillPickUpModifyResponse();
        WaybillPickUpModifyRequest linkRequestData = receiveParams.getRequestDataObject();

        // 下发到cp
        try {

            PickUpUpdateOrderRequest pickUpUpdateOrderRequest = new PickUpUpdateOrderRequest();
            // 获取channel
            String orderChannel = linkRequestData.getOrderChannels();
            LoggerMonitorUtil.start(ScenarioConstant.UPDATE_ORDER, orderChannel);
            pickUpUpdateOrderRequest.setOuterOrderCode(linkRequestData.getOuterOrderCode());
            pickUpUpdateOrderRequest.setOrderChannels(linkRequestData.getOrderChannels());
            // 类型转换
            if (linkRequestData.getSenderInfo() != null) {
                AddressDTO sendaddress = new AddressDTO();
                BeanUtils.copyProperties(linkRequestData.getSenderInfo(), sendaddress);
//                // 判断发件地址中乡镇是否为空，放在清洗后校验，拙玉说圆通那边必须要四级地址
//                if (StringUtils.isBlank(sendaddress.getTownName())) {
//                    response.setSuccess(false);
//                    response.setErrorMsg(PickUpConstants.Error.OrderUpdate.TOWN_NAME_IS_NOT_NULL.getErrorMsg());
//                    response.setErrorCode(PickUpConstants.Error.OrderUpdate.TOWN_NAME_IS_NOT_NULL.getErrorCode());
//                    PickUpLogUtil.errLog(linkRequestData.getOuterOrderCode(), PickUpConstants.Action.PICK_UP_CREATE_LINK.name(), PickUpConstants.Error.OrderUpdate.TOWN_NAME_IS_NOT_NULL.getErrorCode(), PickUpConstants.Error.OrderUpdate.TOWN_NAME_IS_NOT_NULL.getErrorMsg());
//                    return response;
//                }
                // 寄件人信息
                pickUpUpdateOrderRequest.setSendName(linkRequestData.getSenderInfo().getSendName());
                pickUpUpdateOrderRequest.setSendMobile(linkRequestData.getSenderInfo().getSendMobile());
                pickUpUpdateOrderRequest.setSendAddress(sendaddress);
            }
            // 转换商品信息
            setGoodsInfo(linkRequestData, pickUpUpdateOrderRequest);

            // 获取resCode
            String resCode = PickUpCommonUtil.buildResCode(receiveParams.getFromAppkey(), orderChannel);
            pickUpUpdateOrderRequest.setResCode(resCode);
            pickUpUpdateOrderRequest.setFromAppKey(receiveParams.getFromAppkey());

            // 预约上门取件时间
            pickUpUpdateOrderRequest.setAppointGotStartTime(linkRequestData.getAppointGotStartTime());
            pickUpUpdateOrderRequest.setAppointGotEndTime(linkRequestData.getAppointGotEndTime());

            // 1 风控-制裁
            contentRiskWrapper.checkRisk(pickUpUpdateOrderRequest);
            // 2 风控-违禁词
            contentRiskWrapper.checkRiskWord(pickUpUpdateOrderRequest);

            PickUpUpdateOrderResponse modifyResponse = waybillPickUpOrderService.update(pickUpUpdateOrderRequest);
            response.setSuccess(true);
            response.setMailNo(modifyResponse.getMailNo());
        } catch (BridgeBaseException e) {
            response.setSuccess(false);
            response.setErrorMsg(e.getErrorMessage());
            response.setErrorCode(e.getErrorCode());
            PickUpLogUtil.errLog(linkRequestData.getOuterOrderCode(), PickUpConstants.Action.PICK_UP_UPDATE_LINK.name(), e.getErrorCode(), e.getMessage(), e);
            return response;
        } catch (Throwable e) {
            response.setSuccess(false);
            response.setErrorMsg(e.getMessage());
            PickUpLogUtil.errLog(linkRequestData.getOuterOrderCode(), PickUpConstants.Action.PICK_UP_UPDATE_LINK.name(), "", e.getMessage(), e);
            return response;
        } finally {
            LoggerMonitorUtil.end(response.isSuccess(), response.getErrorCode());
        }
        return response;
    }


    private void setGoodsInfo(WaybillPickUpModifyRequest linkRequestData, PickUpUpdateOrderRequest upUpdateOrderRequest) {
        if (CollectionUtils.isEmpty(linkRequestData.getGoodsInfos())) {
            return;
        }
        List<GoodsInfo> goodsInfos = Lists.newArrayList();
        for (com.taobao.pac.client.sdk.dataobject.request.WAYBILL_PICK_UP_MODIFY.GoodsInfo goodsInfoParam : linkRequestData.getGoodsInfos()) {
            GoodsInfo goodsInfo = new com.cainiao.waybill.bridge.biz.pickup.dto.GoodsInfo();
            BeanUtils.copyProperties(goodsInfoParam, goodsInfo);
            if (StringUtils.isNotBlank(goodsInfoParam.getExtendInfo())) {
                JSONObject extendInfo = JSONObject.parseObject(goodsInfoParam.getExtendInfo());
                goodsInfo.setPrice(extendInfo.getString(PickUpCreateOrderParamConstants.Create.PRICE));
                goodsInfo.setForwardMailNo(extendInfo.getString(PickUpCreateOrderParamConstants.Create.FORWARD_MAIL_NO));
                goodsInfo.setForwardCpCode(extendInfo.getString(PickUpCreateOrderParamConstants.Create.FORWARD_CP_CODE));
                goodsInfo.setGoodUniqueKey(extendInfo.getString(PickUpCreateOrderParamConstants.Create.GOOD_UNIQUE_KEY));
                goodsInfo.setCategory(extendInfo.getString(PickUpCreateOrderParamConstants.Create.CATEGORY));
            }
            goodsInfos.add(goodsInfo);
        }
        upUpdateOrderRequest.setGoodsInfos(goodsInfos);
    }
}
