package com.cainiao.waybill.bridge.enterprise.administrators.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cainiao.routingservice.common.constant.RoutingTypeEnum;
import com.cainiao.routingservice.common.dto.*;
import com.cainiao.routingservice.common.result.BatchResultDTO;
import com.cainiao.routingservice.common.service.RoutingService;
import com.cainiao.waybill.bridge.biz.link.pickup.service.impl.cprouting.PickUpCpBizRoutingContext;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.biz.pickup.constants.*;
import com.cainiao.waybill.bridge.biz.pickup.constants.exception.PickUpBusinessException;
import com.cainiao.waybill.bridge.biz.pickup.dto.*;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpCpConfigInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.RouteReachableResult;
import com.cainiao.waybill.bridge.biz.pickup.dto.sf.response.SFPrintData;
import com.cainiao.waybill.bridge.biz.pickup.manager.PickUpCpOrderManager;
import com.cainiao.waybill.bridge.biz.pickup.manager.factory.PickUpCpOrderFactory;
import com.cainiao.waybill.bridge.biz.pickup.routing.dto.RouteBaseRequest;
import com.cainiao.waybill.bridge.biz.pickup.routing.manager.RoutingReachableManager;
import com.cainiao.waybill.bridge.biz.ticket.dto.LogisticsDetailDTO;
import com.cainiao.waybill.bridge.biz.ticket.service.PickUpOrderService;
import com.cainiao.waybill.bridge.biz.utils.pickup.*;
import com.cainiao.waybill.bridge.common.constants.BridgeConstants;
import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpEvent;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;
import com.cainiao.waybill.bridge.common.util.ChineseCheckUtil;
import com.cainiao.waybill.bridge.common.util.DateUtils;
import com.cainiao.waybill.bridge.common.util.LoggerMonitorUtil;
import com.cainiao.waybill.bridge.common.util.MapUtil;
import com.cainiao.waybill.bridge.common.waybill.constants.WaybillPickUpActionConstant;
import com.cainiao.waybill.bridge.common.waybill.pickup.dto.LogisticSubscribeDTO;
import com.cainiao.waybill.bridge.common.waybill.pickup.service.AddressCleanService;
import com.cainiao.waybill.bridge.common.waybill.pickup.service.LogisticSubscribeService;
import com.cainiao.waybill.bridge.common.waybill.pickup.service.WaybillPickUpEventSender;
import com.cainiao.waybill.bridge.enterprise.administrators.request.*;
import com.cainiao.waybill.bridge.enterprise.administrators.service.EnterpriseSettleAccountService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseSwitchHolder;
import com.cainiao.waybill.bridge.enterprise.common.constant.EnterpriseCommonConstant;
import com.cainiao.waybill.bridge.enterprise.common.constant.EnterpriseOrderFeatureConstant;
import com.cainiao.waybill.bridge.enterprise.common.enums.*;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.cainiao.waybill.bridge.enterprise.utils.AddressUtils;
import com.cainiao.waybill.bridge.enterprise.utils.EnterpriseContentRiskWrapper;
import com.cainiao.waybill.bridge.enterprise.utils.SessionUtil;
import com.cainiao.waybill.bridge.model.domain.*;
import com.cainiao.waybill.bridge.model.domain.WaybillBridgeEnterpriseOrderParam.Criteria;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseOrderStatusEnum;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.cainiao.waybill.bridge.enterprise.route.convert.WaybillBridgeEnterpriseOrderConverter;
import com.cainiao.waybill.bridge.model.dto.*;

import com.cainiao.waybill.bridge.enterprise.administrators.service.WaybillBridgeEnterpriseOrderService;
import com.cainiao.waybill.bridge.model.mapper.*;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import com.cainiao.waybill.common.util.FeatureUtils;
import com.cainiao.waybill.common.util.StringUtil;
import com.cainiao.waybill.galaxy.isv.api.types.CloudPrintApplyResponse;
import com.cainiao.waybill.galaxy.isv.api.types.kuaidi100.Kuaidi100ApplyNewData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@Service
public class WaybillBridgeEnterpriseOrderServiceImpl implements WaybillBridgeEnterpriseOrderService {

    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Resource
    private EnterpriseContentRiskWrapper enterpriseContentRiskWrapper;

    @Resource
    private WaybillBridgeEnterpriseOrderMapper enterpriseOrderMapper;

    @Resource
    private EnterpriseSettleAccountService enterpriseSettleAccountService;

    @Resource
    private RoutingReachableManager routingReachableManager;

    @Resource
    private AddressCleanService addressCleanService;

    @Resource
    private WaybillEnterpriseLocationMapper enterpriseLocationMapper;

    @Resource
    private WaybillBridgeEnterprisePostMapper enterprisePostMapper;

    @Resource
    private PickUpOrderService pickUpOrderService;

    @Resource
    private WaybillBridgeEnterpriseUserInfoMapper enterpriseUserInfoMapper;

    @Resource
    private TransactionTemplate bridgeTransactionTemplate;

    // LD物流详情订阅
    @Resource
    private LogisticSubscribeService logisticSubscribeService;

    @Resource
    private WaybillBridgeEnterpriseOrderConverter enterpriseOrderConverter;


    /**
     * 列表查询
     * @param request
     */
    @Override
    public BridgePagingDTO<WaybillBridgeEnterpriseOrderDTO> pageList(WaybillEnterpriseOrderQueryRequest request) throws ParseException {
        String corpId = request.getCorpId();
        if (corpId == null || request.getCurrentPage() == null || request.getPageSize() == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), EnterpriseErrorEnum.PARAM_ERROR.code());
        }
        if (request.getStartTime() == null || request.getEndTime() == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), EnterpriseErrorEnum.PARAM_ERROR.code());
        }
        WaybillBridgeEnterpriseOrderParam waybillBridgeEnterpriseOrderParam = new WaybillBridgeEnterpriseOrderParam();
        Criteria criteria = waybillBridgeEnterpriseOrderParam.createCriteria();
        criteria.andCorpIdEqualTo(corpId);
        Date startTime = DateUtils.strToDate(request.getStartTime(), DateUtils.defaultPattern);
        Date endTime = DateUtils.strToDate(request.getEndTime(), DateUtils.defaultPattern);
        criteria.andGmtCreateBetween(startTime, endTime);

        List<String> userIdList = Lists.newArrayList();
        // 用户端,管理员也只能看取件人为自己的数据
        if (StringUtils.equals(EnterpriseSourceFormEnum.USER.name(), request.getSourceFrom())) {
            if (StringUtils.isEmpty(request.getUserId())) {
                throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), "用户端查询时,取件人不能为空");
            }
            userIdList.add(request.getUserId());
        }
        if (StringUtils.isNotBlank(request.getUserName()) &&
                !StringUtils.equals(EnterpriseSourceFormEnum.USER.name(), request.getSourceFrom())){
            WaybillBridgeEnterpriseUserInfoParam param = new WaybillBridgeEnterpriseUserInfoParam();
            param.createCriteria().andUserNameEqualTo(request.getUserName());
            List<WaybillBridgeEnterpriseUserInfoDO> operatorInfoList = enterpriseUserInfoMapper.selectByParam(param);
            if (CollectionUtils.isNotEmpty(operatorInfoList)) {
                userIdList.addAll(operatorInfoList.stream().map(WaybillBridgeEnterpriseUserInfoDO::getUserId)
                        .collect(Collectors.toList()));
            } else {
                return BridgePagingDTO.build(Collections.emptyList(), 0, request.getCurrentPage(), request.getPageSize());
            }
        }
        if (CollectionUtils.isNotEmpty(userIdList)) {
            criteria.andUserIdIn(userIdList);
        }
        if (StringUtils.isNotBlank(request.getWaybillCode())){
            criteria.andWaybillCodeEqualTo(request.getWaybillCode());
        }
        if (StringUtils.isNotEmpty(request.getLocationId())){
            WaybillEnterpriseLocationParam locationParam = new WaybillEnterpriseLocationParam();
            locationParam.createCriteria()
                    .andLocationIdEqualTo(request.getLocationId())
                    .andCorpIdEqualTo(request.getCorpId());
            WaybillEnterpriseLocationDO locationDO = enterpriseLocationMapper.selectOneByParam(locationParam);
            if (locationDO == null) {
                throw new EnterpriseException(EnterpriseErrorEnum.LOCATION_NOT_EXIST.code(), EnterpriseErrorEnum.LOCATION_NOT_EXIST.describe());
            }
            criteria.andLocationIdEqualTo(locationDO.getId());
        }
        if (request.getCpCode() != null){
            criteria.andCpCodeEqualTo(request.getCpCode());
        }
        if (request.getOrderStatus() != null){
            criteria.andStatusEqualTo(request.getOrderStatus());
        }
        if (request.getBusinessType() != null) {
            criteria.andBusinessTypeEqualTo(request.getBusinessType().byteValue());
        }
        if (StringUtils.isNotEmpty(request.getPostId())) {
            WaybillBridgeEnterprisePostParam postParam = new WaybillBridgeEnterprisePostParam();
            postParam.createCriteria()
                    .andPostIdEqualTo(request.getPostId())
                    .andCorpIdEqualTo(request.getCorpId());
            WaybillBridgeEnterprisePostDO postDO = enterprisePostMapper.selectOneByParam(postParam);
            if (postDO == null) {
                throw new EnterpriseException(EnterpriseErrorEnum.POST_NOT_EXIST.code(), EnterpriseErrorEnum.POST_NOT_EXIST.describe());
            }
            criteria.andPostIdEqualTo(postDO.getId());
        }
        long count = enterpriseOrderMapper.countByParam(waybillBridgeEnterpriseOrderParam);
        if (count == 0) {
            return BridgePagingDTO.build(Collections.emptyList(), 0, request.getCurrentPage(), request.getPageSize());
        }
        waybillBridgeEnterpriseOrderParam.setPage(true);
        waybillBridgeEnterpriseOrderParam.setPagination(request.getCurrentPage(), request.getPageSize());
        waybillBridgeEnterpriseOrderParam.appendOrderByClause(WaybillBridgeEnterpriseOrderParam.OrderCondition.ID, WaybillBridgeEnterpriseOrderParam.SortType.DESC);
        criteria.andCorpIdEqualTo(corpId);
        List<WaybillBridgeEnterpriseOrderDO> list = enterpriseOrderMapper.selectByParam(waybillBridgeEnterpriseOrderParam);
        if (CollectionUtils.isEmpty(list)) {
            return BridgePagingDTO.build(Collections.emptyList(), 0, request.getCurrentPage(), request.getPageSize());
        }
        List<WaybillBridgeEnterpriseOrderDTO> orderDTOS = list.stream().map(enterpriseOrderConverter::convertFromDO).collect(Collectors.toList());
        return BridgePagingDTO.build(orderDTOS,
                count, request.getCurrentPage(), request.getPageSize());
    }


    /**
     * 创建
     * @param request
     */
    @Override
    public String create(WaybillEnterpriseOrderRequest request) throws Exception {
        checkParam(request);

        WaybillBridgeEnterpriseUserInfoDO operator = enterpriseUserInfoMapper.selectByUserId(request.getUserId(), request.getCorpId());
        if (operator == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.OPERATOR_NOT_EXIT.code(), EnterpriseErrorEnum.OPERATOR_NOT_EXIT.describe());
        }

        //场地快递配置校验
        String corpId = request.getCorpId();
        EnterpriseSettleAccountRequest accountRequest = new EnterpriseSettleAccountRequest();
        accountRequest.setCorpId(corpId);
        accountRequest.setCpCode(request.getCpCode());
        accountRequest.setWaybillAccountNo(request.getWaybillAccountNo());

        List<WaybillEnterpriseSettleAccountDTO> list = enterpriseSettleAccountService.list(accountRequest);
        if (CollectionUtils.isEmpty(list)) {
            throw new EnterpriseException(EnterpriseErrorEnum.ACCOUNT_NOT_EXIST.code(), EnterpriseErrorEnum.ACCOUNT_NOT_EXIST.describe());
        }

        //业务id-使用uuid
        request.setOuterOrderCode(UUID.randomUUID().toString().replace("-", ""));
        // todo 查费用归属

        // 1 风控-制裁
        enterpriseContentRiskWrapper.checkRisk(request);
        // 2 风控-违禁词
        enterpriseContentRiskWrapper.checkRiskWord(request);

        //   寄件地址清洗
        AddressDTO cleanedSendAddr = cleanAddr(AddressUtils.addressConvert(request.getSenderAddress()));
        if (cleanedSendAddr != null) {
            request.setSenderAddress(AddressUtils.addressInfoConvert(cleanedSendAddr));
        }
        AddressDTO cleanedConsigneeAddr = cleanAddr(AddressUtils.addressConvert(request.getConsigneeAddress()));
        if (cleanedConsigneeAddr != null) {
            request.setConsigneeAddress(AddressUtils.addressInfoConvert(cleanedConsigneeAddr));
        }
        if (ExpressTypeEnum.EMS.expressCode.equals(request.getCpCode())) {
            request.setAgent(PickUpAgentEnum.DIRECT_CP.getAgent());
        } else {
            request.setAgent(PickUpAgentEnum.OPEN_CP.getAgent());
        }
        checkCpAvailable(request, routingReachableManager);
        //  保证寄件人手机号这个字段一定有值:最终DB该字段也一定会有值
        String sendMobile = StringUtil.coalesce(request.getSenderMobile(), request.getSenderPhone()).trim();
        request.setSenderMobile(sendMobile);
        WaybillBridgeEnterpriseOrderDO enterpriseOrderDO = this.buildDetailDO(request);

        bridgeTransactionTemplate.execute(new TransactionCallback<String>() {
            @Override
            public String doInTransaction(TransactionStatus status) {
                WaybillBridgeEnterpriseOrderDO existOrderDO = null;
                String waybillCode;
                List<WaybillBridgeEnterpriseOrderDO> waybillBridgeEnterpriseOrderDOS = queryOrder(request);
                //只有一条
                if (CollectionUtils.isNotEmpty(waybillBridgeEnterpriseOrderDOS)) {
                    existOrderDO = waybillBridgeEnterpriseOrderDOS.get(0);
                }

                //  已存在订单处理
                if (existOrderDO != null) {
                    enterpriseOrderDO.setId(existOrderDO.getId());
                    enterpriseOrderMapper.updateByPrimaryKeySelective(enterpriseOrderDO);
                } else {
                    enterpriseOrderMapper.insert(enterpriseOrderDO);
                }
                WaybillBridgeEnterpriseOrderDO byOuterOrderCode = enterpriseOrderMapper.getByOuterOrderCode(enterpriseOrderDO.getOuterOrderCode());
                if(byOuterOrderCode == null){
                    throw new EnterpriseException(EnterpriseErrorEnum.ORDER_NOT_EXIST.code(), EnterpriseErrorEnum.ORDER_NOT_EXIST.describe());
                }
                request.setId(byOuterOrderCode.getId());
                try {
                    PickUpCreateOrderResponse createOrderResponse = createOrder(request);
                    String mailNo = createOrderResponse.getMailNo();
                    waybillCode = mailNo;
                    if (StringUtil.isBlank(mailNo)) {
                        //失败更新订单状态
                        updateOrderStatusById(enterpriseOrderDO.getId(), EnterpriseOrderStatusEnum.DELETED.getCode());
                    }

                } catch (PickUpBusinessException e) {
                    if (request.getId() != null && request.getId() > 0) {
                        LoggerMonitorUtil.start(EnterpriseErrorEnum.SYSTEM_ERROR.code(), corpId, request.getLocationId() + "", "");
                    }
                    //失败更新订单状态
                    updateOrderStatusById(enterpriseOrderDO.getId(), EnterpriseOrderStatusEnum.DELETED.getCode());
                    LOGGER.error("create order error, request:{}", JSON.toJSONString(request), e);
                    throw e;
                } catch (EnterpriseException e) {
                    if (request.getId() != null && request.getId() > 0) {
                        LoggerMonitorUtil.start(EnterpriseErrorEnum.SYSTEM_ERROR.code(), corpId, request.getLocationId() + "", "");
                    }
                    //失败更新订单状态
                    updateOrderStatusById(enterpriseOrderDO.getId(), EnterpriseOrderStatusEnum.DELETED.getCode());
                    LOGGER.error(request.getOuterOrderCode(), "创建订单失败",e);
                    throw e;
                } catch (Throwable throwable) {
                    LOGGER.error(request.getOuterOrderCode(), "create_order_exception", "创建订单失败", throwable);
                    //失败更新订单状态
                    updateOrderStatusById(enterpriseOrderDO.getId(), EnterpriseOrderStatusEnum.DELETED.getCode());
                    throw new PickUpBusinessException("channel_create_error", "渠道调用失败");
                }
                return waybillCode;
            }
        });
        return null;
    }

    @Override
    public String cancel(WaybillEnterpriseOrderCancelRequest request) {
        if (StringUtils.isEmpty(request.getOuterOrderCode())){
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), EnterpriseErrorEnum.PARAM_ERROR.describe());
        }
        WaybillEnterpriseOrderRequest orderRequest = new WaybillEnterpriseOrderRequest();
        orderRequest.setCorpId(request.getCorpId());
        orderRequest.setOuterOrderCode(request.getOuterOrderCode());

        List<WaybillBridgeEnterpriseOrderDO> waybillBridgeEnterpriseOrderDOS = queryOrder(orderRequest);
        if (CollectionUtils.isEmpty(waybillBridgeEnterpriseOrderDOS)) {
            throw new EnterpriseException(EnterpriseErrorEnum.ORDER_NOT_EXIST.code(), EnterpriseErrorEnum.ORDER_NOT_EXIST.describe());
        }
        WaybillBridgeEnterpriseOrderDO enterpriseOrderDO = waybillBridgeEnterpriseOrderDOS.get(0);
        if (enterpriseOrderDO.getStatus().equals(EnterpriseOrderStatusEnum.CANCELED.getCode())
                || enterpriseOrderDO.getStatus().equals(EnterpriseOrderStatusEnum.DELETED.getCode())) {
            return "";
        }
        //非新建、接单不准取消
        if (!enterpriseOrderDO.getStatus().equals(EnterpriseOrderStatusEnum.PENDING.getCode())) {
            PickUpLogUtil.errLog(request.getOuterOrderCode(), "", PickUpConstants.Error.PICK_UP_ORDER_IS_GOT.getErrorCode(), PickUpConstants.Error.PICK_UP_ORDER_IS_GOT.getErrorMsg());
            throw new PickUpBusinessException(PickUpConstants.Error.PICK_UP_ORDER_IS_GOT.getErrorCode(), PickUpConstants.Error.PICK_UP_ORDER_IS_GOT.getErrorMsg());
        }
        PickUpCancelOrderRequest cancelRequest = new PickUpCancelOrderRequest();
        cancelRequest.setMailNo(enterpriseOrderDO.getWaybillCode());
        cancelRequest.setCpCode(enterpriseOrderDO.getCpCode());
        request.setCpCode(enterpriseOrderDO.getCpCode());
        String agent;
        if (ExpressTypeEnum.EMS.expressCode.equals(request.getCpCode())) {
            agent = PickUpAgentEnum.DIRECT_CP.getAgent();
        } else {
            agent = PickUpAgentEnum.OPEN_CP.getAgent();
        }
        PickUpCpOrderManager cpPickUpOrderManager = PickUpCpOrderFactory.getCpOrderManagerByAgent(agent,enterpriseOrderDO.getCpCode());

        cancelRequest.setCancelByConsumer(true);
        Map<String, String> feature =  FeatureUtils.parseFromString(enterpriseOrderDO.getFeature());
        cancelRequest.setCpOrderId(MapUtil.get(feature, PickUpConstants.TraceFeatureKey.CP_ORDER_ID));
        cancelRequest.setOrderId("enterprise_" + String.valueOf(enterpriseOrderDO.getId()));
        cancelRequest.setPreOrderIdFlag(feature.containsKey(PickUpConstants.TraceFeatureKey.PRE_CREATE_ORDER_FLAG));
        cancelRequest.setOuterOrderCode(enterpriseOrderDO.getOuterOrderCode());
        cancelRequest.setFrom(PickUpFromEnum.ENTERPRISE.getCode());
        WaybillPickUpDetailDO existDetailDO = new WaybillPickUpDetailDO();
        existDetailDO.setFeature(FeatureUtils.parseFromMap(feature));
        existDetailDO.setId(enterpriseOrderDO.getId());
        existDetailDO.setMailNo(enterpriseOrderDO.getWaybillCode());
        cancelRequest.setPickUpDetailDO(existDetailDO);

        try {
            // 取消接口
            cpPickUpOrderManager.cancel(cancelRequest);
            WaybillBridgeEnterpriseOrderDO orderDO = new WaybillBridgeEnterpriseOrderDO();
            orderDO.setId(enterpriseOrderDO.getId());
            orderDO.setStatus(EnterpriseOrderStatusEnum.CANCELED.getCode());
            orderDO.setCancelTime(new Date());
            Map<String, String> existFeatureMap = FeatureUtils.parseFromString(enterpriseOrderDO.getFeature());
            existFeatureMap.put(PickUpConstants.TraceFeatureKey.CANCEL_TIME, PickUpFeatureUtil.formatTime(System.currentTimeMillis()));
            existFeatureMap.put(PickUpConstants.TraceFeatureKey.FAIL_REASON, PickUpFeatureUtil.dealFailReason(request.getCancelDesc()));
            orderDO.setFeature(FeatureUtils.parseFromMap(existFeatureMap));
            enterpriseOrderMapper.updateByPrimaryKeySelective(orderDO);
            return enterpriseOrderDO.getWaybillCode();
        } catch (PickUpBusinessException e) {
            LOGGER.error("cancel order error, request:{}", JSON.toJSONString(request), e);
            throw e;
        } catch (Throwable throwable) {
            LOGGER.error(request.getOuterOrderCode(), "cancel_order_exception", " 取消订单失败", throwable);
            throw new PickUpBusinessException("channel_cancel_error", "取消订单渠道调用失败");
        }
    }

    @Override
    public String update(WaybillBridgeEnterpriseOrderDO request){
        WaybillBridgeEnterpriseOrderDO enterpriseOrderDO = enterpriseOrderMapper.get(request.getWaybillCode());
        if (enterpriseOrderDO == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.ORDER_NOT_EXIST.code(), EnterpriseErrorEnum.ORDER_NOT_EXIST.describe());
        }
        request.setId(enterpriseOrderDO.getId());
        enterpriseOrderMapper.updateByPrimaryKeySelective(request);
        return enterpriseOrderDO.getWaybillCode();
    }


    @Override
    public String createTest(WaybillEnterpriseOrderRequest request) {
        if (request == null){
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), EnterpriseErrorEnum.PARAM_ERROR.describe());
        }
        checkParam(request);
        request.setOuterOrderCode(request.getCorpId() + request.getLocationId() + System.currentTimeMillis());
        WaybillBridgeEnterpriseOrderDO enterpriseOrderDO = this.buildDetailDO(request);
        enterpriseOrderMapper.insert(enterpriseOrderDO);
        return enterpriseOrderDO.getWaybillCode();
    }

    @Override
    public int deleteOrderById(long id) {
        return enterpriseOrderMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int removeOrderFeatureById(long id, String feature) {

        WaybillBridgeEnterpriseOrderParam param = new WaybillBridgeEnterpriseOrderParam();
        Criteria criteria = param.createCriteria();
        criteria.andIdEqualTo(id);
        WaybillBridgeEnterpriseOrderDO enterpriseOrderDO = enterpriseOrderMapper.selectOneByParam(param);
        if (null == enterpriseOrderDO) {
            throw new EnterpriseException(EnterpriseErrorEnum.ORDER_NOT_EXIST.code(), EnterpriseErrorEnum.ORDER_NOT_EXIST.describe());
        }
        enterpriseOrderDO.setFeature(feature);
        return enterpriseOrderMapper.updateByPrimaryKey(enterpriseOrderDO);
    }

    @Override
    public List<LogisticsDetailDTO> queryWaybillTrace(WaybillEnterpriseOrderTraceRequest request) {
        // 参数校验
        if (StringUtils.isEmpty(request.getOuterOrderCode())){
            throw new EnterpriseException(EnterpriseErrorEnum.PARAM_ERROR.code(), EnterpriseErrorEnum.PARAM_ERROR.describe());
        }
        WaybillEnterpriseOrderRequest orderRequest = new WaybillEnterpriseOrderRequest();
        orderRequest.setCorpId(request.getCorpId());
        orderRequest.setOuterOrderCode(request.getOuterOrderCode());

        List<WaybillBridgeEnterpriseOrderDO> waybillBridgeEnterpriseOrderDOS = queryOrder(orderRequest);
        if (CollectionUtils.isEmpty(waybillBridgeEnterpriseOrderDOS)) {
            throw new EnterpriseException(EnterpriseErrorEnum.ORDER_NOT_EXIST.code(), EnterpriseErrorEnum.ORDER_NOT_EXIST.describe());
        }

        WaybillBridgeEnterpriseOrderDO enterpriseOrderDO = waybillBridgeEnterpriseOrderDOS.get(0);

        List<LogisticsDetailDTO> logisticsDetailDTOList = pickUpOrderService.queryLogistics(enterpriseOrderDO.getWaybillCode(), enterpriseOrderDO.getCpCode());
        return logisticsDetailDTOList;
    }


    private List<WaybillBridgeEnterpriseOrderDO> queryOrder(WaybillEnterpriseOrderRequest request){
        WaybillBridgeEnterpriseOrderParam waybillBridgeEnterpriseOrderParam = new WaybillBridgeEnterpriseOrderParam();
        Criteria criteria = waybillBridgeEnterpriseOrderParam.createCriteria();
        criteria.andCorpIdEqualTo(request.getCorpId());
        criteria.andOuterOrderCodeEqualTo(request.getOuterOrderCode());
        return enterpriseOrderMapper.selectByParam(waybillBridgeEnterpriseOrderParam);
    }


    public static void checkParam(WaybillEnterpriseOrderRequest request) throws EnterpriseException {
        if (request.getCorpId() == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.CORP_ID_IS_NULL.code(), EnterpriseErrorEnum.CORP_ID_IS_NULL.describe());
        }
        if (request.getLocationId() == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.LOCATION_ID_IS_NULL.code(), EnterpriseErrorEnum.LOCATION_ID_IS_NULL.describe());
        }
        if (request.getCpCode() == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.CP_CODE_IS_NULL.code(), EnterpriseErrorEnum.CP_CODE_IS_NULL.describe());
        }
        if (request.getProduct() == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.PRODUCT_IS_NULL.code(), EnterpriseErrorEnum.PRODUCT_IS_NULL.describe());
        }
        if (request.getItem() == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.ITEM_IS_NULL.code(), EnterpriseErrorEnum.ITEM_IS_NULL.describe());
        }
        if (request.getBusinessType() == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.BUSINESS_TYPE_IS_NULL.code(), EnterpriseErrorEnum.BUSINESS_TYPE_IS_NULL.describe());
        }
        if (request.getWaybillAccountNo() == null) {
            throw new EnterpriseException(EnterpriseErrorEnum.WAYBILL_ACCOUNT_NO_IS_NULL.code(), EnterpriseErrorEnum.WAYBILL_ACCOUNT_NO_IS_NULL.describe());
        }
        if (StringUtils.isBlank(request.getConsigneePhone()) && StringUtils.isBlank(request.getConsigneeMobile())) {
            throw new EnterpriseException(EnterpriseErrorEnum.CONSIGNEE_PHONE_OR_MOBILE_IS_NULL.code(),EnterpriseErrorEnum.CONSIGNEE_PHONE_OR_MOBILE_IS_NULL.describe());
        }
        if (StringUtils.isBlank(request.getConsigneeAddress().getProvince())
                    || StringUtils.isBlank(request.getConsigneeAddress().getCity())
                        || StringUtils.isBlank(request.getConsigneeAddress().getArea())
                    || StringUtils.isBlank(request.getConsigneeAddress().getAddressDetail())
        ) {
            throw new EnterpriseException(EnterpriseErrorEnum.CONSIGNEE_ADDRESS_IS_NULL.code(), EnterpriseErrorEnum.CONSIGNEE_ADDRESS_IS_NULL.describe());
        }

        if (StringUtils.isBlank(request.getUserId())) {
            throw new EnterpriseException(EnterpriseErrorEnum.OPERATOR_ID_IS_NULL.code(), EnterpriseErrorEnum.OPERATOR_ID_IS_NULL.describe());
        }
        
        checkAddrDetail(request.getConsigneeAddress().getAddressDetail());

        if (StringUtils.isBlank(request.getSenderPhone()) && StringUtils.isBlank(request.getSenderMobile())) {
            throw new EnterpriseException(EnterpriseErrorEnum.SENDER_PHONE_OR_MOBILE_IS_NULL.code(), EnterpriseErrorEnum.SENDER_PHONE_OR_MOBILE_IS_NULL.describe());

        }
        if (StringUtils.isBlank(request.getSenderAddress().getProvince())
                    || StringUtils.isBlank(request.getSenderAddress().getCity())
                    || StringUtils.isBlank(request.getSenderAddress().getArea())
                    || StringUtils.isBlank(request.getSenderAddress().getAddressDetail())
        ) {
            throw new EnterpriseException(EnterpriseErrorEnum.SENDER_ADDRESS_IS_NULL.code(), EnterpriseErrorEnum.SENDER_ADDRESS_IS_NULL.describe());
        }
        checkAddrDetail(request.getSenderAddress().getAddressDetail());

    }

    private static void checkAddrDetail(String addrDetail) throws EnterpriseException {

        if (!ChineseCheckUtil.chineseCharLengthFit(addrDetail, 2)
                || ChineseCheckUtil.isMessyCode(addrDetail)) {
            throw new EnterpriseException(EnterpriseErrorEnum.ADDRESS_DETAIL_CHECK_ERROR.code(), EnterpriseErrorEnum.ADDRESS_DETAIL_CHECK_ERROR.describe());
        }

    }
    /**
     * 清洗地址：若依赖，清洗失败不要阻断下单流程
     */
    private AddressDTO cleanAddr(AddressDTO sendAddress) {
        if (!BridgeSwitch.openAddressClean) {
            return null;
        }
        try {
            return addressCleanService.clean(sendAddress);
        } catch (Throwable e) {
            return null;
        }
    }


    /**
     * 可达性校验
     */
    public static void checkCpAvailable(WaybillEnterpriseOrderRequest request, RoutingReachableManager routingReachableManager) throws Exception {

        //  判断筛单是否可以通过
        RouteBaseRequest routeBaseRequest = new RouteBaseRequest();
        routeBaseRequest.setSendName(request.getSenderName());
        routeBaseRequest.setSendMobile(request.getSenderMobile());
        routeBaseRequest.setSendAddress(AddressUtils.addressConvert(request.getSenderAddress()));
        routeBaseRequest.setReceiveName(request.getConsigneeName());
        routeBaseRequest.setReceiveMobile(request.getConsigneeMobile());
        routeBaseRequest.setReceiveAddress(AddressUtils.addressConvert(request.getConsigneeAddress()));
        routeBaseRequest.setCpCode(request.getCpCode());
        routeBaseRequest.setOuterOrderCode(request.getOuterOrderCode());
        routeBaseRequest.setWaybillAccountNo(request.getWaybillAccountNo());
        routeBaseRequest.setAgent(request.getAgent());
        RouteReachableResult routeReachableResult = routingReachableManager.routingReachable(routeBaseRequest);
        if (!routeReachableResult.isReachable()) {
            throw new EnterpriseException(EnterpriseErrorEnum.ORDER_UNREACHABLE.code(), EnterpriseErrorEnum.ORDER_UNREACHABLE.describe());
        }
    }



    /**
     * 下单
     */
    private PickUpCreateOrderResponse createOrder(WaybillEnterpriseOrderRequest enterpriseOrderRequest) throws Throwable {
        PickUpCreateOrderRequest request = new PickUpCreateOrderRequest();
        request.setOuterOrderCode(enterpriseOrderRequest.getOuterOrderCode());
        request.setId(enterpriseOrderRequest.getId());
        PickUpCpBizRoutingContext bizRoutingContext = new PickUpCpBizRoutingContext();
        PickUpCpConfigInfo cpInfo = new PickUpCpConfigInfo();
        cpInfo.setCpCode(enterpriseOrderRequest.getCpCode());
        //todo 产品类型
        cpInfo.setBizType(PickUpCpBizTypeEnum.E_COMMERCE_SHIPPING.getBizType());
        bizRoutingContext.setCpInfo(cpInfo);
        request.setBizRoutingContext(bizRoutingContext);
        request.setSendMobile(enterpriseOrderRequest.getSenderMobile());
        request.setSendAddress(AddressUtils.addressConvert(enterpriseOrderRequest.getSenderAddress()));
        request.setSendName(enterpriseOrderRequest.getSenderName());
        request.setSendPhone(enterpriseOrderRequest.getSenderPhone());
        request.setConsigneeAddress(AddressUtils.addressConvert(enterpriseOrderRequest.getConsigneeAddress()));
        request.setConsigneeName(enterpriseOrderRequest.getConsigneeName());
        request.setConsigneeMobile(enterpriseOrderRequest.getConsigneeMobile());
        request.setConsigneePhone(enterpriseOrderRequest.getConsigneePhone());
        request.setWaybillAccountNo(enterpriseOrderRequest.getWaybillAccountNo());
        request.setFrom(PickUpFromEnum.ENTERPRISE.getCode());

        // EMS添加的参数
        // 订单类型
        request.setBizType(PickUpDetailBizTypeEnum.NORMAL.getValue());
        // 资源编码生成电子面单唯一ID
        request.setResCode("enterprise#" + enterpriseOrderRequest.getCpCode());

        // SF需要设置预约单，避免触发1小时揽派
        if(EnterpriseSwitchHolder.APPOINT_ORDER_SUPPORT_CP.contains(enterpriseOrderRequest.getCpCode())){
            setAppointmentTimes(request);
        }


        PickUpCpOrderManager cpPickUpOrderManager = PickUpCpOrderFactory.getCpOrderManagerByAgent(enterpriseOrderRequest.getAgent(),enterpriseOrderRequest.getCpCode());

        PickUpAgentEnum agentEnum = cpPickUpOrderManager.agent();

        PickUpCreateOrderResponse response = cpPickUpOrderManager.create(request);

        //  内部DB记录订单详情
        WaybillBridgeEnterpriseOrderDO enterpriseOrderDO = this.buildOrderDO(enterpriseOrderRequest, response, agentEnum);

        //  后续统一处理
        this.createOrderPostProcess(enterpriseOrderRequest, enterpriseOrderDO, response);

        //  同步下单的平台，需要在这里设置取件码
        if (StringUtil.isBlank(response.getGotCode())) {
            response.setGotCode(request.getGotCode());
        }

        return response;
    }

    private WaybillBridgeEnterpriseOrderDO buildDetailDO(WaybillEnterpriseOrderRequest request) {
        WaybillBridgeEnterpriseOrderDO enterpriseOrderDO = new WaybillBridgeEnterpriseOrderDO();
        BeanUtils.copyProperties(request, enterpriseOrderDO);
        if (StringUtils.isNotEmpty(request.getLocationId())) {
            WaybillEnterpriseLocationParam param = new WaybillEnterpriseLocationParam();
            param.createCriteria()
                    .andLocationIdEqualTo(request.getLocationId())
                    .andCorpIdEqualTo(request.getCorpId());
            WaybillEnterpriseLocationDO locationDO = enterpriseLocationMapper.selectOneByParam(param);
            if (locationDO != null) {
                enterpriseOrderDO.setLocationId(locationDO.getId());
            }
        }
        if (StringUtils.isNotEmpty(request.getPostId())) {
            WaybillBridgeEnterprisePostParam param = new WaybillBridgeEnterprisePostParam();
            param.createCriteria()
                    .andPostIdEqualTo(request.getPostId())
                    .andCorpIdEqualTo(request.getCorpId());
            WaybillBridgeEnterprisePostDO postDO = enterprisePostMapper.selectOneByParam(param);
            if (postDO != null) {
                enterpriseOrderDO.setPostId(postDO.getId());
            }
        }
        enterpriseOrderDO.setSenderAddress(AddressUtils.convertToFormattedString(request.getSenderAddress()));
        enterpriseOrderDO.setConsigneeAddress(AddressUtils.convertToFormattedString(request.getConsigneeAddress()));
        enterpriseOrderDO.setWaybillCode(MailNoUtil.generateTempMailNo());
        enterpriseOrderDO.setCpCode(request.getCpCode());
        enterpriseOrderDO.setStatus(EnterpriseOrderStatusEnum.PENDING.getCode());
        enterpriseOrderDO.setProduct(EnterpriseOrderProductEnum.getCodeByDesc(request.getProduct()));
        enterpriseOrderDO.setBusinessType(request.getBusinessType().byteValue());
        // 标记订单id提前生成，用于与外部交互使用订单id
        Map<String, String> featureMap = Maps.newHashMap();
        featureMap.put(PickUpConstants.TraceFeatureKey.PRE_CREATE_ORDER_FLAG, "1");
        enterpriseOrderDO.setFeature(FeatureUtils.parseFromMap(featureMap));
        return enterpriseOrderDO;
    }

    private WaybillBridgeEnterpriseOrderDO buildOrderDO(WaybillEnterpriseOrderRequest request, PickUpCreateOrderResponse cpResponse, PickUpAgentEnum agentEnum) {
        WaybillBridgeEnterpriseOrderDO enterpriseOrderDO = new WaybillBridgeEnterpriseOrderDO();
        BeanUtils.copyProperties(request, enterpriseOrderDO);
        if (StringUtils.isNotEmpty(request.getLocationId())) {
            WaybillEnterpriseLocationParam param = new WaybillEnterpriseLocationParam();
            param.createCriteria()
                    .andLocationIdEqualTo(request.getLocationId())
                    .andCorpIdEqualTo(request.getCorpId());
            WaybillEnterpriseLocationDO locationDO = enterpriseLocationMapper.selectOneByParam(param);
            if (locationDO != null) {
                enterpriseOrderDO.setLocationId(locationDO.getId());
            }
        }
        if (StringUtils.isNotEmpty(request.getPostId())) {
            WaybillBridgeEnterprisePostParam param = new WaybillBridgeEnterprisePostParam();
            param.createCriteria()
                    .andPostIdEqualTo(request.getPostId())
                    .andCorpIdEqualTo(request.getCorpId());
            WaybillBridgeEnterprisePostDO postDO = enterprisePostMapper.selectOneByParam(param);
            if (postDO != null) {
                enterpriseOrderDO.setPostId(postDO.getId());
            }
        }
        enterpriseOrderDO.setSenderAddress(AddressUtils.convertToFormattedString(request.getSenderAddress()));
        enterpriseOrderDO.setConsigneeAddress(AddressUtils.convertToFormattedString(request.getConsigneeAddress()));
        enterpriseOrderDO.setWaybillCode(cpResponse.getMailNo());
        enterpriseOrderDO.setCpCode(cpResponse.getCpCode());
        enterpriseOrderDO.setStatus(EnterpriseOrderStatusEnum.PENDING.getCode());
        enterpriseOrderDO.setProduct(EnterpriseOrderProductEnum.getCodeByDesc(request.getProduct()));
        Map<String, String> featureMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(request.getCostAllocation())) {
            featureMap.put(EnterpriseOrderFeatureConstant.COST_ALLOCATION, request.getCostAllocation());
        }
        if (StringUtils.isNotBlank(cpResponse.getGotCode())) {
            String gotCode = cpResponse.getGotCode();
            featureMap.put(EnterpriseOrderFeatureConstant.GOT_CODE, gotCode);
        }
        if (StringUtils.isNotBlank(cpResponse.getCpOrderId())) {
            featureMap.put(EnterpriseOrderFeatureConstant.CP_ORDER_ID, cpResponse.getCpOrderId());
        }
        if (cpResponse.getCpBizType() != null) {
            featureMap.put(EnterpriseOrderFeatureConstant.CP_BIZ_TYPE, String.valueOf(cpResponse.getCpBizType()));
        }
        // 顺丰面单打印信息
        if (StringUtils.isNotEmpty(cpResponse.getDestRouteLabel())) {
            featureMap.put(EnterpriseOrderFeatureConstant.DEST_ROUTE_LABEL, cpResponse.getDestRouteLabel());
        }
        if (cpResponse.getRouteLabelData() != null) {
            SFPrintData sfPrintData = new SFPrintData();
            BeanUtils.copyProperties(cpResponse.getRouteLabelData(), sfPrintData);
            featureMap.put(EnterpriseOrderFeatureConstant.SF_PRINT_DATA, GSON.toJson(sfPrintData));
        }
        if (request.getDeptId() != null) {
            featureMap.put(EnterpriseOrderFeatureConstant.DEPT_ID, String.valueOf(request.getDeptId()));
        }
        if (StringUtils.isNotEmpty(request.getDeptName())) {
            featureMap.put(EnterpriseOrderFeatureConstant.DEPT_NAME, request.getDeptName());
        }
        if (null != agentEnum) {
            featureMap.put(PickUpConstants.TraceFeatureKey.AGENT, agentEnum.getAgent());
        }
        if (request.getInsuredValue() != null) {
            featureMap.put(EnterpriseOrderFeatureConstant.INSURED_VALUE, String.valueOf(request.getInsuredValue()));
        }
        enterpriseOrderDO.setFeature(FeatureUtils.parseFromMap(featureMap));
        return enterpriseOrderDO;
    }


    /**
     * 下单的后续统一处理：存储订单详情、订单属性数据；发送新建消息
     */
    private void createOrderPostProcess(WaybillEnterpriseOrderRequest request, WaybillBridgeEnterpriseOrderDO newOrderDO, PickUpCreateOrderResponse response) {

        boolean dbResult;
        // 我看这边永远不为空
        if(request.getId() != null && request.getId() > 0){
            // 更新 组织更新对象
            WaybillBridgeEnterpriseOrderDO enterpriseOrderDO = enterpriseOrderMapper.selectByPrimaryKey(request.getId());
            WaybillBridgeEnterpriseOrderDO upDetailDO =  new WaybillBridgeEnterpriseOrderDO();
            upDetailDO.setId(request.getId());
            upDetailDO.setWaybillCode(response.getMailNo());
            upDetailDO.setCpCode(response.getCpCode());
            Map<String, String> featureMap =  FeatureUtils.parseFromString(enterpriseOrderDO.getFeature());
            featureMap.putAll(FeatureUtils.parseFromString(newOrderDO.getFeature()));
//            featureMap.putAll(PickUpFeatureUtil.jsonStrToMap(newOrderDO.getFeature()));
            // 写入三段码
            featureMap.put("destRouteLabel", response.getDestRouteLabel());
            upDetailDO.setFeature(FeatureUtils.parseFromMap(featureMap));
            int result = enterpriseOrderMapper.updateByPrimaryKeySelective(upDetailDO);
            dbResult = (result == 1);
        }else{
            // 面单号为空，生成临时面单号，真实单号待物流详情回传
            if(newOrderDO.getWaybillCode() == null){
                newOrderDO.setWaybillCode(MailNoUtil.generateTempMailNo());
            }
            dbResult = enterpriseOrderMapper.insert(newOrderDO) == 1;
        }
        if (!dbResult) {
            throw new RuntimeException(PickUpConstants.Error.DB_ERROR.getErrorCode());
        }

        WaybillPickUpEvent event = new WaybillPickUpEvent();
        event.setAction(WaybillPickUpActionConstant.APPLY_NAME);
        event.setMailNo(newOrderDO.getWaybillCode());
        event.setResCode(newOrderDO.getCorpId());
        event.setCpCode(newOrderDO.getCpCode());
        event.setOuterOrderCode(newOrderDO.getOuterOrderCode());
        //  传递寄件地址到"待接单"消息处理器，该处理器里面判断是否为乡村件(从这里放置，避免复查DB)
        JSONObject extraInfo = new JSONObject();
        extraInfo.put(PickUpEventConstants.ExtraInfoKey.SEND_ADDRESS, newOrderDO.getSenderAddress());
        event.setExtraInfo(extraInfo.toJSONString());
        //发出寄件订单创建的生命周期消息，后续会做物流详情的订阅
//        waybillPickUpEventSender.send(event);
        LogisticSubscribeDTO subscribeDTO = LpLogisticUtil.enterpriseOrderPrepareSubscribeDTO(newOrderDO);
        BaseResultDTO<Void> subscribeResult = logisticSubscribeService.subscribe(subscribeDTO);
        if(subscribeResult.isFailure()){
            LOGGER.error("运单{}物流详情订阅失败", newOrderDO.getWaybillCode());
        }

    }

    @Resource
    RoutingService routingService;

    private static final Gson GSON = new Gson();

    @Override
    public String queryEMSPrintData(WaybillBridgeEnterpriseOrderDO enterpriseOrderRequest, String waybillCode) {
        final CloudPrintApplyResponse data = new CloudPrintApplyResponse();
        final Kuaidi100ApplyNewData kuaidi100ApplyNewData = new Kuaidi100ApplyNewData();
        data.setKuaidi100ApplyNewData(kuaidi100ApplyNewData);
        final OrderData eOrder = new OrderData();

        try {
            final String objectId = SessionUtil.getSession();
            final RoutingInfoCalculateBatchRequestDTO requestDTO = new RoutingInfoCalculateBatchRequestDTO();
            requestDTO.setRoutingTypes(Sets.newHashSet(RoutingTypeEnum.SORTATION.getType(), RoutingTypeEnum.CONSOLIDATION.getType(), RoutingTypeEnum.ROUTE_CODE.getType(), RoutingTypeEnum.CONTRACT_CODE.getType(), RoutingTypeEnum.TERMINAL_CENTER.getType()));

            requestDTO.setObjectId(objectId);
            requestDTO.setCpCode(enterpriseOrderRequest.getCpCode());

            final RoutingInfoCalculateInnerParamDTO infoCalculateInnerParamDTO = new RoutingInfoCalculateInnerParamDTO();
            infoCalculateInnerParamDTO.setObjectId(objectId);

            final OrderDTO order = new OrderDTO();
            // 座机号
            if(StringUtils.isNotEmpty(enterpriseOrderRequest.getConsigneePhone())){
                order.setConsigneeMobile(enterpriseOrderRequest.getConsigneePhone());
            }
            // 手机号
            if(StringUtils.isNotEmpty(enterpriseOrderRequest.getConsigneeMobile())){
                order.setConsigneeMobile(enterpriseOrderRequest.getConsigneeMobile());
            }
            order.setOrderIds(Arrays.asList(enterpriseOrderRequest.getOuterOrderCode()));
            infoCalculateInnerParamDTO.setOrder(order);

            final RoutingAddressDTO routingAddress = new RoutingAddressDTO();
            routingAddress.setObjectId(objectId);

            // 收件地址
            final AddressInfo receiverAddress = AddressUtils.parse(enterpriseOrderRequest.getConsigneeAddress());
            final com.cainiao.routingservice.common.dto.AddressDTO receiveAddress = new com.cainiao.routingservice.common.dto.AddressDTO();
            receiveAddress.setObjectId(objectId);
            receiveAddress.setProvinceName(receiverAddress.getProvince());
            receiveAddress.setCityName(receiverAddress.getCity());
            receiveAddress.setTownName(receiverAddress.getTown());
            receiveAddress.setAreaName(receiverAddress.getArea());
            receiveAddress.setAddressDetail(receiverAddress.getAddressDetail());
            routingAddress.setReceiveAddress(receiveAddress);

            // 发货地址
            final AddressInfo senderAddress = AddressUtils.parse(enterpriseOrderRequest.getSenderAddress());
            final com.cainiao.routingservice.common.dto.AddressDTO sendAddress = new com.cainiao.routingservice.common.dto.AddressDTO();
            sendAddress.setObjectId(objectId);
            sendAddress.setProvinceName(senderAddress.getProvince());
            sendAddress.setCityName(senderAddress.getCity());
            sendAddress.setTownName(senderAddress.getTown());
            sendAddress.setAreaName(senderAddress.getArea());
            sendAddress.setAddressDetail(senderAddress.getAddressDetail());
            routingAddress.setSendAddress(sendAddress);

            infoCalculateInnerParamDTO.setRoutingAddress(routingAddress);

            final ArrayList<RoutingInfoCalculateInnerParamDTO> innerParams = new ArrayList<>();
            innerParams.add(infoCalculateInnerParamDTO);
            requestDTO.setInnerParams(innerParams);

            final ClientInfoDTO clientInfoDTO = new ClientInfoDTO();
            clientInfoDTO.setAppName(BridgeConstants.System.APP_NAME);
            clientInfoDTO.setOperator("query");
            final BatchResultDTO<RoutingResponseDTO> routingResponseDTOBatchResultDTO = routingService.calculateRoutingInfoBatch(requestDTO, clientInfoDTO);

            if (!routingResponseDTOBatchResultDTO.isSuccess() || routingResponseDTOBatchResultDTO.getSuccessResult(objectId) == null) {
                LOGGER.error("query routing info fail 1 {}, {}, {}", waybillCode, JSON.toJSONString(requestDTO), JSON.toJSONString(routingResponseDTOBatchResultDTO));
                kuaidi100ApplyNewData.setEOrder(GSON.toJson(eOrder));
                return constructPrinterData(data, enterpriseOrderRequest, waybillCode);
            }
            final com.cainiao.routingservice.common.result.BaseResultDTO<RoutingResponseDTO> resultDTO = routingResponseDTOBatchResultDTO.getSuccessResult(objectId);
            if (resultDTO.isFailure() || resultDTO.getModule() == null) {
                LOGGER.error("query routing info fail 2 {}, {}, {}", waybillCode, JSON.toJSONString(requestDTO), JSON.toJSONString(resultDTO));
                kuaidi100ApplyNewData.setEOrder(GSON.toJson(eOrder));
                return constructPrinterData(data, enterpriseOrderRequest, waybillCode);
            }
            final RoutingResponseDTO module = resultDTO.getModule();
            final Map<Integer, com.cainiao.routingservice.common.dto.RoutingInfoDTO> routingInfoMap = module.getRoutingInfoMap();
            final Map<String, Object> extraRoutingInfo = module.getExtraInfo();
            if (MapUtils.isEmpty(routingInfoMap)) {
                LOGGER.error("query routing info fail 3 {}, {}, {}", waybillCode, JSON.toJSONString(requestDTO), JSON.toJSONString(resultDTO));
                kuaidi100ApplyNewData.setEOrder(GSON.toJson(eOrder));
                return constructPrinterData(data, enterpriseOrderRequest, waybillCode);
            }

            // EMS拼接规则 集包地+大头笔+二段码+三段码
            // 三段码有特殊逻辑，优先取extraRoutingInfo下KEY_CONTRACT_ALIAS_CODE值，其次取CONTRACT_CODE值

            // 二段码 三段码
            final com.cainiao.routingservice.common.dto.RoutingInfoDTO routeCode = routingInfoMap.get(RoutingTypeEnum.ROUTE_CODE.getType());
            final com.cainiao.routingservice.common.dto.RoutingInfoDTO contract = routingInfoMap.get(RoutingTypeEnum.CONTRACT_CODE.getType());
            String contractName = contract == null ? "" : contract.getRoutingTextValue();
            if(StringUtils.isNotEmpty(String.valueOf(extraRoutingInfo.getOrDefault(EnterpriseCommonConstant.KEY_CONTRACT_ALIAS_CODE, "")))){
                contractName = String.valueOf(extraRoutingInfo.getOrDefault(EnterpriseCommonConstant.KEY_CONTRACT_ALIAS_CODE, ""));
            }
            // 如果没有二段码，不填充
            eOrder.setRouteCode(routeCode == null ? null : (routeCode.getRoutingTextValue() + ' ' + contractName));
            // 大头笔
            final com.cainiao.routingservice.common.dto.RoutingInfoDTO sortation = routingInfoMap.get(RoutingTypeEnum.SORTATION.getType());
            String sortationName = sortation == null ? null : sortation.getRoutingTextValue();
            // 拼接报文
            Sortation sortationObj = new Sortation();
            sortationObj.setName(sortationName);
            eOrder.setSortation(sortationObj);


            // 集包地
            final com.cainiao.routingservice.common.dto.RoutingInfoDTO consolidation = routingInfoMap.get(RoutingTypeEnum.CONSOLIDATION.getType());
            String pkgName = consolidation == null ? null : consolidation.getRoutingTextValue();
            eOrder.setPkgName(pkgName);
            // 拼接报文
            Consolidation consolidationObj = new Consolidation();
            consolidationObj.setName(pkgName);
            eOrder.setConsolidation(consolidationObj);


            eOrder.setDestSortingName(contract == null ? null : contract.getRoutingTextValue());
            kuaidi100ApplyNewData.setEOrder(GSON.toJson(eOrder));
            return constructPrinterData(data, enterpriseOrderRequest, waybillCode);
        } catch (Exception e) {
            LOGGER.error("query routing info error {}, {}, e ", waybillCode, JSON.toJSONString(enterpriseOrderRequest), e);
            kuaidi100ApplyNewData.setEOrder(GSON.toJson(eOrder));
            return constructPrinterData(data, enterpriseOrderRequest, waybillCode);
        }
    }

    @Data
    static class Sortation {
        /**
         * 大头笔
         */
        String name;
    }

    @Data
    static class Consolidation {
        /**
         * 集包地
         */
        String name;
    }

    @Data
    static class OrderData {
        /**
         * 段码
         */
        @SerializedName("bulkpen")
        String routeCode;
        /**
         * 水印
         */
        String waterMark;

        /**
         * 包裹名称
         */
        String pkgName;

        /**
         * 始发分拣名称
         */
        String orgSortingName;

        /**
         * 目的分拣名称
         */
        String destSortingName;

        /**
         * 路区
         */
        String road;

        /**
         * 二维码
         */
        String qrCode;

        /**
         * 集包地
         */
        Consolidation consolidation;

        /**
         * 大头笔
         */
        Sortation sortation;
    }


    private static String constructPrinterData(CloudPrintApplyResponse data, WaybillBridgeEnterpriseOrderDO enterpriseOrderDO, String waybillCode) {
        JSONObject jsonObject = new JSONObject();

        JSONObject recipient = new JSONObject();
        JSONObject recipientAddress = new JSONObject();
        recipientAddress.put("city", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getCity());
        recipientAddress.put("detail", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getAddressDetail());
        recipientAddress.put("district", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getArea());
        recipientAddress.put("province", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getProvince());
        recipientAddress.put("town", AddressUtils.parse(enterpriseOrderDO.getConsigneeAddress()).getTown());
        recipient.put("address", recipientAddress);
        recipient.put("mobile", enterpriseOrderDO.getConsigneeMobile());
        recipient.put("name", enterpriseOrderDO.getConsigneeName());
        recipient.put("phone", enterpriseOrderDO.getConsigneePhone());


        JSONObject sender = new JSONObject();
        JSONObject sendAddress = new JSONObject();
        sendAddress.put("city", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getCity());
        sendAddress.put("detail", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getAddressDetail());
        sendAddress.put("district", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getArea());
        sendAddress.put("province", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getProvince());
        sendAddress.put("town", AddressUtils.parse(enterpriseOrderDO.getSenderAddress()).getTown());
        sender.put("address", sendAddress);
        sender.put("mobile", enterpriseOrderDO.getSenderMobile());
        sender.put("name", enterpriseOrderDO.getSenderName());
        sender.put("phone", enterpriseOrderDO.getSenderPhone());

        jsonObject.put("recipient", recipient);
        OrderData orderData = new Gson().fromJson(Objects.toString(data.getKuaidi100ApplyNewData().getEOrder()), OrderData.class);
        //其他 cp 的路由数据
        jsonObject.put("routingInfo", orderData);
        //顺丰的路由数据
        Object parse = JSONObject.parse(Objects.toString(data.getKuaidi100ApplyNewData().getEOrder()));
        jsonObject.put("routingExtraInfo", parse);
        jsonObject.put("sender", sender);
        jsonObject.put("waybillCode", waybillCode);

        return jsonObject.toString();
    }


    private void setAppointmentTimes(PickUpCreateOrderRequest request) {
        // 当前时间
        Calendar now = Calendar.getInstance();

        // 格式化时间
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 初始化预约开始时间和结束时间
        Calendar startTime = Calendar.getInstance();
        Calendar endTime = Calendar.getInstance();

        int hour = now.get(Calendar.HOUR_OF_DAY);

        // 结束时间固定为当天的23:59:59
        endTime.set(Calendar.HOUR_OF_DAY, 23);
        endTime.set(Calendar.MINUTE, 59);
        endTime.set(Calendar.SECOND, 59);

        if (hour >= 10 && hour <= 20) {
            // 如果当前时间在10:00到18:00之间，则直接使用当前时间
            startTime.setTime(now.getTime());
        } else if (hour < 10) {
            // 如果当前时间小于10:00，则设置开始时间为当天的10:00
            startTime.set(Calendar.HOUR_OF_DAY, 10);
            startTime.set(Calendar.MINUTE, 0);
            startTime.set(Calendar.SECOND, 0);
        } else {
            // 如果当前时间大于18:00，则预约开始时间为明天的10:00
            startTime.add(Calendar.DAY_OF_MONTH, 1);
            startTime.set(Calendar.HOUR_OF_DAY, 10);
            startTime.set(Calendar.MINUTE, 0);
            startTime.set(Calendar.SECOND, 0);

            // 结束时间固定为明天的23:59:59
            endTime.add(Calendar.DAY_OF_MONTH, 1);
            endTime.set(Calendar.HOUR_OF_DAY, 23);
            endTime.set(Calendar.MINUTE, 59);
            endTime.set(Calendar.SECOND, 59);
        }


        request.setAppointGotStartTime(startTime.getTime());
        request.setAppointGotEndTime(endTime.getTime());
    }

    private void updateOrderStatusById(Long id, Integer status) {
        WaybillBridgeEnterpriseOrderDO updateOrder = new WaybillBridgeEnterpriseOrderDO();
        updateOrder.setId(id);
        updateOrder.setStatus(status);
        enterpriseOrderMapper.updateByPrimaryKeySelective(updateOrder);
    }

}