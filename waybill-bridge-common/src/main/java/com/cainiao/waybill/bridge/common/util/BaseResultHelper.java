package com.cainiao.waybill.bridge.common.util;

import com.cainiao.waybill.bridge.common.base.Constants.ErrorConstant;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;

/**
 * Created by dongcai.ldc on 2016/3/11.
 *  针对 BaseResultDTO工具类
 */
public class BaseResultHelper {
    public static <T> BaseResultDTO<T> newResult() {
        return new BaseResultDTO<T>();
    }

    public static <T> BaseResultDTO<T> newResult(T obj) {
        BaseResultDTO<T> result = new BaseResultDTO<T>();
        result.setModule(obj);
        return result;
    }

    /**
     * 组装返回的错误码信息
     * @param errorCode 错误码
     * @param errorMsg 错误描述
     * @param <T>
     * @return
     */
    public static <T> BaseResultDTO<T> errorResult(String errorCode, String errorMsg){
        BaseResultDTO<T> result = new BaseResultDTO<T>();
        result.setSuccess(false);
        result.addErrorMessage(errorCode,errorMsg);
        return result;
    }
    public static <T> BaseResultDTO<T> errorResult(ErrorConstant error) {
        return errorResult(error.getErrorCode(), error.getErrorMsg());
    }
}
