package com.cainiao.waybill.bridge.biz.hsf.provider;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.cainiao.cne.sns.client.dto.base.CneResult;
import com.cainiao.cne.sns.client.service.AbilityQueryService;
import com.cainiao.cne.sns.common.dto.ReceiveOrderQueryDTO;
import com.cainiao.cne.sns.common.enums.ReceiveOrderDTO;
import com.cainiao.geography.api.JurisdictionQueryService;
import com.cainiao.geography.api.bean.JurisdictionChain;
import com.cainiao.geography.api.bean.VersionSpec;
import com.cainiao.geography.api.request.QueryAddr;
import com.cainiao.geography.api.response.QueryResp;
import com.cainiao.geography.commons.CountryCode;
import com.cainiao.geography.commons.GeographyResponse;
import com.cainiao.geography.frontend.api.AddressStructurationService;
import com.cainiao.geography.frontend.dto.StructuredAddress;
import com.cainiao.routingservice.common.dto.ClientInfoDTO;
import com.cainiao.routingservice.common.dto.reach.ReachableServiceResponseDTO;
import com.cainiao.routingservice.common.dto.reach.RoutingReachableRequestDTO;
import com.cainiao.routingservice.common.result.BaseResultDTO;
import com.cainiao.routingservice.common.service.RoutingReachableService;
import com.cainiao.waybill.bridge.biz.charity.constant.CharityConstant;
import com.cainiao.waybill.bridge.biz.hsf.RouteTestService;
import com.cainiao.waybill.bridge.biz.hsf.bean.RoutingJudgeExcelImportDTO;
import com.cainiao.waybill.bridge.biz.middleware.oss.pickup.CharityOssClientFactory;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.ComputingCourierInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.RouteReachableResult;
import com.cainiao.waybill.bridge.biz.pickup.routing.dto.RouteBaseRequest;
import com.cainiao.waybill.bridge.biz.pickup.routing.manager.RoutingReachableManager;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelExportUtil;
import com.cainiao.waybill.bridge.biz.utils.excel.ExcelImportUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.OssUtils;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.biz.wrapper.WaybillPickUpTairWrapper;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.BridgeDateUtil;
import com.cainiao.waybill.bridge.common.waybill.pickup.service.AddressCleanService;
import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.cainiao.waybill.bridge.model.dto.WaybillPickUpOrderDetailQueryDTO;
import com.cainiao.waybill.bridge.model.mapper.WaybillPickUpDetailMapper;
import com.cainiao.waybill.common.admin.dto.AddressDTO;
import com.cainiao.waybill.common.util.RetryUtil;
import com.google.common.collect.Lists;
import com.taobao.tair.ResultCode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2021/9/23-上午10:12
 */
@HSFProvider(serviceInterface = RouteTestService.class)
public class RouteTestServiceImpl implements RouteTestService {

    @Resource
    private RoutingReachableService routingReachableService;

    @Resource
    private WaybillPickUpTairWrapper waybillPickUpTairWrapper;

    @Resource
    private JurisdictionQueryService jurisdictionQueryService;

    @Resource
    private WaybillPickUpDetailMapper waybillPickUpDetailMapper;

    @Resource
    private AbilityQueryService abilityQueryService;

    @Override
    public String isTownship(String start, String end, List<Integer> statusList) throws ParseException {

        WaybillPickUpOrderDetailQueryDTO detailQueryDTO = new WaybillPickUpOrderDetailQueryDTO();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date startTime = sdf.parse(start);
        Date endTime = sdf.parse(end);
        detailQueryDTO.setCreateStart(startTime);
        detailQueryDTO.setCreateEnd(endTime);
        detailQueryDTO.setStatus(statusList);
        List<WaybillPickUpDetailDO> detailDOList = waybillPickUpDetailMapper.queryDetail(detailQueryDTO);
        PickUpLogUtil.info("detailDOList size : " + detailDOList.size());

        VersionSpec versionSpec = new VersionSpec();
        versionSpec.setName("20210930001");
        QueryResp<List<JurisdictionChain>> listQueryResp;
        int townCount = 0;
        for (WaybillPickUpDetailDO detailDO : detailDOList) {
            QueryAddr queryAddr = new QueryAddr("CN", detailDO.getSendAddress());
            listQueryResp = jurisdictionQueryService.queryJurisdictionByAddr("CN_DZK_VILLAGE", queryAddr, versionSpec);
            if (CollectionUtils.isNotEmpty(listQueryResp.getData())) {
                townCount++;
                PickUpLogUtil.info(detailDO.getSendAddress());
            }
        }
        PickUpLogUtil.info("isTown size : " + townCount);

        return String.valueOf(townCount);
    }

    @Override
    public QueryResp<List<JurisdictionChain>> queryJurisdictionByAddr(String domain, QueryAddr queryAddr,
        VersionSpec versionSpec) {
        QueryResp<List<JurisdictionChain>> listQueryResp = jurisdictionQueryService.queryJurisdictionByAddr(domain,
            queryAddr, versionSpec);
        PickUpLogUtil.info("listQueryResp : " + JSON.toJSONString(listQueryResp));
        return listQueryResp;
    }

    @Override
    public BaseResultDTO<ReachableServiceResponseDTO> routingReachable(RoutingReachableRequestDTO requestDTO,
        ClientInfoDTO clientInfoDTO) {
        PickUpLogUtil.info("RoutingReachableRequestDTO : " + JSON.toJSONString(requestDTO));
        PickUpLogUtil.info("ClientInfoDTO : " + JSON.toJSONString(clientInfoDTO));

        BaseResultDTO<ReachableServiceResponseDTO> baseResultDTO = routingReachableService.calculateRoutingCoverage(
            requestDTO, clientInfoDTO);
        PickUpLogUtil.info("baseResultDTO : " + JSON.toJSONString(baseResultDTO));
        return baseResultDTO;
    }

    @Override
    public CneResult<ReceiveOrderDTO> zmkmRoutingReachable(ReceiveOrderQueryDTO requestDTO) throws Exception {

        CneResult<ReceiveOrderDTO> cneResult = RetryUtil.retry(() -> abilityQueryService.receiveOrder(requestDTO), 2,
            2);
        PickUpLogUtil.info("baseResultDTO : " + JSON.toJSONString(cneResult));
        return cneResult;
    }

    @Override
    public Object get(Serializable key) {
        return waybillPickUpTairWrapper.get(key);
    }

    @Override
    public List<ComputingCourierInfo> getCourier(String key) {
        Object obj = waybillPickUpTairWrapper.get(key);
        String tt = obj.toString();
        tt = tt.replace("[\"", "").replace("\"]", "");
        tt = tt.replaceAll("\",\"", ";");
        tt = tt.replaceAll("\\\\", "");
        PickUpLogUtil.info(tt);
        for (String t1 : tt.split(";")) {
            PickUpLogUtil.info(t1);
            JSONObject json = JSONObject.parseObject(t1);
            PickUpLogUtil.info(json.getString("contact"));
        }
        return null;
    }

    @Override
    public ResultCode put(Serializable key, Serializable value, int expireTime) {
        return waybillPickUpTairWrapper.put(key, value, expireTime);
    }

    @Override
    public Integer prefixDecr(Serializable pkey, Serializable skey, int value, int defaultValue, int expireTime) {
        try {
            return waybillPickUpTairWrapper.prefixDecr(pkey, skey, value, defaultValue, expireTime);
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", "", "", "", e);
        }
        return null;
    }

    @Override
    public ResultCode prefixSetCount(Serializable pkey, Serializable skey, int count) {
        try {
            return waybillPickUpTairWrapper.prefixSetCount(pkey, skey, count);
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", "", "", "", e);
        }
        return null;
    }

    @Override
    public Object prefixGet(Serializable pkey, Serializable skey) {
        try {
            return waybillPickUpTairWrapper.prefixGet(pkey, skey);
        } catch (Throwable e) {
            PickUpLogUtil.errLog("", "", "", "", e);
        }
        return null;
    }

    @Resource
    private RoutingReachableManager routingReachableManager;

    @Autowired
    private AddressStructurationService addressStructurationService;

    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(20, 20,
        0L, TimeUnit.MILLISECONDS,
        new LinkedBlockingQueue<Runnable>(100));

    @Override
    public String routingJudge(String bucketName, String fileName, boolean senderJudge, int thread, int maxNum,  List<String> cpList) {

        threadPoolExecutor.setMaximumPoolSize(thread);

        if(CollectionUtils.isEmpty(cpList)){
           cpList = Lists.newArrayList("SNWL", "STO", "YTO", "YUNDA", "HTKY", "ZTO", "DBKD", "UC", "POSTB");
        }

        OSSClient ossClient = CharityOssClientFactory.getOssClient(true);
        OSSObject ossObject = ossClient.getObject(bucketName, fileName);
        InputStream inputStream = ossObject.getObjectContent();
        List<RoutingJudgeExcelImportDTO> list = ExcelImportUtil.loadData(inputStream, fileName,
            RoutingJudgeExcelImportDTO.class, maxNum);
        PickUpLogUtil.info(String.format("地址筛单开始: %s", list.size()));

        List<Future<String>> futureList = Lists.newArrayList();
        for (List<RoutingJudgeExcelImportDTO> routingJudgeExcelImportDTOS : Lists.partition(list, list.size()/thread)) {

            List<String> finalCpList = cpList;
            Future<String> future = threadPoolExecutor.submit(() -> {
                processData(finalCpList, routingJudgeExcelImportDTOS, senderJudge);
                return null;
            });
            futureList.add(future);
        }

        for (Future<String> future : futureList) {
            try {
                future.get();
            } catch (Exception e) {
                PickUpLogUtil.errLog(null, "process_future_error", "process_future_error", "执行异常", e);
            }
        }

        Workbook workbook = ExcelExportUtil.build(list, "地址筛单判断", RoutingJudgeExcelImportDTO.class);
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setObjectAcl(CannedAccessControlList.PublicRead);
        metadata.setExpirationTime(DateUtils.addMinutes(new Date(), 15));
        String date = BridgeDateUtil.dateToStr(new Date(), BridgeDateUtil.pattern24Format);
        String uploadfileName ="地址筛单判断" + date + ".xlsx";
        String imgUrl = OssUtils.uploadToOSS(uploadfileName, convert2InputStream(workbook), metadata);
        PickUpLogUtil.info("地址筛单判断结果excel: " + imgUrl);
        return imgUrl;
    }

    private void processData(List<String> cpList, List<RoutingJudgeExcelImportDTO> list, boolean senderJudge) {
        for (int i = 0; i < list.size(); i++) {
            PickUpLogUtil.info(String.format("地址筛单: %s", i));
            RoutingJudgeExcelImportDTO routingJudgeExcelImportDTO = list.get(i);
            try {
                //  判断筛单是否可以通过

                AddressDTO sendAddress = parseAddress(routingJudgeExcelImportDTO.getSenderAddress());
                AddressDTO receiverAddress = parseAddress(routingJudgeExcelImportDTO.getReceiverAddress());
                if(receiverAddress == null){
                    throw new BridgeBusinessException("receiver_parse_error", "寄件地址解析失败");
                }

                for (String cpCode : cpList) {
                    // 收货地
                    String result = reachableCheck(sendAddress, receiverAddress, cpCode);

                    if ("SNWL".equals(cpCode)) {
                        routingJudgeExcelImportDTO.setSnwlReachable(result);
                    }
                    if ("STO".equals(cpCode)) {
                        routingJudgeExcelImportDTO.setStoReachable(result);
                    }
                    if ("YTO".equals(cpCode)) {
                        routingJudgeExcelImportDTO.setYtoReachable(result);
                    }
                    if ("YUNDA".equals(cpCode)) {
                        routingJudgeExcelImportDTO.setYundaReachable(result);
                    }
                    if ("HTKY".equals(cpCode)) {
                        routingJudgeExcelImportDTO.setHtkyReachable(result);
                    }
                    if ("ZTO".equals(cpCode)) {
                        routingJudgeExcelImportDTO.setZtoReachable(result);
                    }
                    if ("DBKD".equals(cpCode)) {
                        routingJudgeExcelImportDTO.setDbkdReachable(result);
                    }
                    if ("UC".equals(cpCode)) {
                        routingJudgeExcelImportDTO.setUcReachable(result);
                    }
                    if ("POSTB".equals(cpCode)) {
                        routingJudgeExcelImportDTO.setPostbReachable(result);
                    }

                    String senderResult = null;

                    if(senderJudge){
                        // 收货地
                        senderResult = reachableCheck(receiverAddress, sendAddress, cpCode);

                        if ("SNWL".equals(cpCode)) {
                            routingJudgeExcelImportDTO.setSnwlSenderReachable(senderResult);
                        }
                        if ("STO".equals(cpCode)) {
                            routingJudgeExcelImportDTO.setStoSenderReachable(senderResult);
                        }
                        if ("YTO".equals(cpCode)) {
                            routingJudgeExcelImportDTO.setYtoSenderReachable(senderResult);
                        }
                        if ("YUNDA".equals(cpCode)) {
                            routingJudgeExcelImportDTO.setYundaSenderReachable(senderResult);
                        }
                        if ("HTKY".equals(cpCode)) {
                            routingJudgeExcelImportDTO.setHtkySenderReachable(senderResult);
                        }
                        if ("ZTO".equals(cpCode)) {
                            routingJudgeExcelImportDTO.setZtoSenderReachable(senderResult);
                        }
                        if ("DBKD".equals(cpCode)) {
                            routingJudgeExcelImportDTO.setDbkdSenderReachable(senderResult);
                        }
                        if ("UC".equals(cpCode)) {
                            routingJudgeExcelImportDTO.setUcSenderReachable(senderResult);
                        }
                        if ("POSTB".equals(cpCode)) {
                            routingJudgeExcelImportDTO.setPostbSenderReachable(senderResult);
                        }
                    }

                    PickUpLogUtil.info(String.format("地址筛单判断cp: %s: sender: %s, receiver: %s, result: %s, senderResult: %s", cpCode,
                        routingJudgeExcelImportDTO.getSenderAddress(), routingJudgeExcelImportDTO.getReceiverAddress(), result, senderResult));
                }
            } catch (Throwable throwable) {
                routingJudgeExcelImportDTO.setErrorInfo(throwable.getMessage());
            }
        }
    }

    private String reachableCheck(AddressDTO sendAddress, AddressDTO receiverAddress, String cpCode) {
        RouteBaseRequest routeBaseRequest = new RouteBaseRequest();
        routeBaseRequest.setSendAddress(sendAddress);
        routeBaseRequest.setReceiveAddress(receiverAddress);
        routeBaseRequest.setCpCode(cpCode);
        String result = "可达";
        try {
            RouteReachableResult routeReachableResult = routingReachableManager.routingReachable(
                routeBaseRequest);
            PickUpLogUtil.info(String.format("地址筛单接口结果: %s", JSON.toJSONString(routeReachableResult)));
            if (!routeReachableResult.isReachable()) {
                result = routeReachableResult.getUnReachReason();
            }
        } catch (Exception exception) {
            PickUpLogUtil.errLog(null, "routingReachable",
                "routingReachable", "地址筛单判断", exception);
            result = exception.getMessage();
        }
        return result;
    }

    /**
     * workbook转为流
     *
     * @param wb
     * @return
     */
    private static InputStream convert2InputStream(Workbook wb) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            wb.write(bos);
            byte[] bytes = bos.toByteArray();
            return new ByteArrayInputStream(bytes);
        } catch (Throwable e) {
            PickUpLogUtil.errLog(null, "WORKBOOK_CONVERT_STREAM_ERROR", "WORKBOOK_CONVERT_STREAM_ERROR", "转换失败", e);
            return null;
        }
    }

    private AddressDTO parseAddress(String address) {
        com.cainiao.geography.frontend.dto.QueryAddr queryAddr = new com.cainiao.geography.frontend.dto.QueryAddr(
            CountryCode.CN.getCountryCode(),
            address, null);
        GeographyResponse<StructuredAddress> response = addressStructurationService.structurize(queryAddr);
        if (response == null || response.getStatus() != 0) {
            String error = response != null ? response.getMessage() : null;
            return null;
        }
        AddressDTO sender = new AddressDTO();
        sender.setProvinceName(response.getData().getProvince());
        sender.setCityName(response.getData().getCity());
        sender.setAreaName(response.getData().getCounty());
        sender.setTownName(response.getData().getTown());
        sender.setAddressDetail(response.getData().getDetailAddress());

        PickUpLogUtil.info(String.format("地址筛单地址清洗结果: address: %s: result: %s", address, sender.getAddressFormat()));
        return sender;
    }
}
