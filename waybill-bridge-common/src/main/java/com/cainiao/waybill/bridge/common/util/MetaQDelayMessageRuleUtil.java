package com.cainiao.waybill.bridge.common.util;

import java.util.Map;

/**
 * @author: yexin
 * @date: 2022-04-29 10:36
 **/
public class MetaQDelayMessageRuleUtil {

    /**
     * 从配置的延迟信息中获取一个MetaQ的延迟单位。返回null则表示当前延迟信息以全部执行完毕
     */
    public static Integer getOneDelayUnit(Map<Integer, Integer> delayInfo) {
        for (Map.Entry<Integer, Integer> entry : delayInfo.entrySet()) {
            if (entry.getValue() != null && entry.getValue() != 0) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * 更新延迟规则：减去已经执行完毕的延迟单元次数
     */
    public static void reduceOneUsedDelayUnit(Map<Integer, Integer> delayInfo, Integer delayUnitKey) {
        Integer curDelayUnitExeTimes = delayInfo.get(delayUnitKey);
        if (curDelayUnitExeTimes != null && curDelayUnitExeTimes > 0) {
            delayInfo.put(delayUnitKey, curDelayUnitExeTimes - 1);
        }
    }
}
