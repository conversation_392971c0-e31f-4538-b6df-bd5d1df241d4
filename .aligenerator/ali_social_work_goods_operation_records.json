// 右键菜单，可以一键生成哦。
// 配置格式参见 https://yuque.antfin-inc.com/xd6pvi/dr962p 
{
	"tableName":"ali_social_work_goods_operation_records",
	"domainObjectName":"AliSocialWorkGoodsOperationRecords",
	"mapperName":"AliSocialWorkGoodsOperationRecordsDAO",
	"appName":"waybill_bridge",
	"groupKey":"",
	"split":false,
	"pattern":"_[0-9]+$",
	"anno":true,
	"antZdal":false,
	"appList":[
		"waybill_bridge$",
		"cainiao_waybill_bridge@11.158.231.96:3425 [kuka_6a049b6b1509490b9e7d074d77a5c69f]$",
		"waybill-bridge$"
	],
	"blobEnable":false,
	"businessPackageName":"com.cainiao.waybill.bridge.alisocial.model.test",
	"businessTargetProject":"waybill-bridge-aliSocialWork/src/main/java",
	"catlog":"",
	"columnName":"id",
	"connectionURL":"",
	"createBaseType":false,
	"disableNamespace":false,
	"driverClass":"",
	"enableDeleteMethod":true,
	"fileEncode":"UTF-8",
	"generateBusiness":true,
	"generateKey":true,
	"ignoreColumn":false,
	"ignoreColumnValue":"id",
	"javaClientGeneratorTargetPackage":"com.cainiao.waybill.bridge.alisocial.model.test",
	"javaClientGeneratorTargetProject":"waybill-bridge-aliSocialWork/src/main/java",
	"javaClientGeneratorType":"XMLMAPPER",
	"javaModelGeneratorTargetPackage":"com.cainiao.waybill.bridge.alisocial.model.test",
	"javaModelGeneratorTargetProject":"waybill-bridge-aliSocialWork/src/main/java",
	"lastBizPackage":"com.cainiao.waybill.bridge.alisocial.model.test",
	"lastBizProjectPath":"waybill-bridge-aliSocialWork/src/main/java",
	"lastTypeHadler":"",
	"lastTypeJava":"",
	"mergePolicy":1,
	"modelType":"flat",
	"myDataBase":{
		"tables":[
			{
				"columns":[
					{
						"autoIncrement":true,
						"generatedColumn":false,
						"length":0,
						"name":"id",
						"scale":0,
						"comment":"主键",
						"type":"bigint",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"gmt_create",
						"scale":0,
						"comment":"创建时间",
						"type":"datetime",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"gmt_modified",
						"scale":0,
						"comment":"修改时间",
						"type":"datetime",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"goods_id",
						"scale":0,
						"comment":"物资编码",
						"type":"bigint",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"quantity",
						"scale":0,
						"comment":"数量",
						"type":"int",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"receipt_id",
						"scale":0,
						"comment":"出入库单、接收记录id",
						"type":"bigint",
						"enableNull":true
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":64,
						"name":"operator",
						"scale":0,
						"comment":"操作人",
						"type":"varchar(64)",
						"enableNull":true
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":64,
						"name":"operator_type",
						"scale":0,
						"comment":"操作类型",
						"type":"varchar(64)",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"donor_id",
						"scale":0,
						"comment":"捐赠方id",
						"type":"bigint",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"relief_project_id",
						"scale":0,
						"comment":"所属项目id",
						"type":"bigint",
						"enableNull":false
					},
					{
						"autoIncrement":false,
						"generatedColumn":false,
						"length":0,
						"name":"executing_agency_id",
						"scale":0,
						"comment":"执行机构id",
						"type":"bigint",
						"enableNull":true
					}
				],
				"name":"ali_social_work_goods_operation_records",
				"comment":"物资操作记录",
				"primaryKey":"id"
			}
		],
		"name":""
	},
	"password":"",
	"replaceIs":false,
	"saveParameter":true,
	"saveParameterPath":".aligenerator",
	"security":true,
	"serializable":true,
	"simpleMethod":true,
	"sqlMapGeneratorTargetPackage":"com.cainiao.waybill.bridge.alisocial.model.test",
	"sqlMapGeneratorTargetProject":"waybill-bridge-aliSocialWork/src/main/java",
	"sqlStatement":"JDBC",
	"tableSchema":"",
	"targetRuntime":"MyBatis3",
	"tddl":true,
	"tddlMap":{
		"cainiao_waybill_bridge@11.158.231.96:3425 [kuka_6a049b6b1509490b9e7d074d77a5c69f]":"",
		"waybill-bridge":"",
		"waybill_bridge":""
	},
	"typeHandlerMap":{},
	"typeMap":{},
	"useBaseDao":false,
	"useDBTime":false,
	"useDao":true,
	"useLombok":true,
	"useSqlCreate":true,
	"userId":"",
	"whenPresent":true,
	"generatedBy":"Aligenerator",
	"schemaVersion":"1.0"
}