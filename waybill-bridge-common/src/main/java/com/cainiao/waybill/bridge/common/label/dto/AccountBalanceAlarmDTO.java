package com.cainiao.waybill.bridge.common.label.dto;

import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * 余额告警设置DTO
 * <AUTHOR>
 * @since 2017/04/21
 */
public class AccountBalanceAlarmDTO extends BaseDTO {
    private static final long serialVersionUID = 926101621794858889L;
    /**
     * accountId 必填
     */
    private Long accountId;

    /**
     * 物流商ID 必填
     */
    private Long    cpId;

    /**
     * 网点编码 必填
     */
    private String  branchCode;

    /**
     * 余额告警值 必填
     */
    private Integer alarmQuantity;

    /**
     * 发送间隔时间（小时） 必填
     */
    private Integer intervalHour;

    /**
     * 用于短信提醒的电话号码 必填
     */
    private String phone;
    /**
     * 多号段 必填
     */
    private String segmentCode;

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getCpId() {
        return cpId;
    }

    public void setCpId(Long cpId) {
        this.cpId = cpId;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public Integer getAlarmQuantity() {
        return alarmQuantity;
    }

    public void setAlarmQuantity(Integer alarmQuantity) {
        this.alarmQuantity = alarmQuantity;
    }

    public Integer getIntervalHour() {
        return intervalHour;
    }

    public void setIntervalHour(Integer intervalHour) {
        this.intervalHour = intervalHour;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getSegmentCode() {
        return segmentCode;
    }

    public void setSegmentCode(String segmentCode) {
        this.segmentCode = segmentCode;
    }
}
