package com.cainiao.waybill.bridge.common.waybill.pickup.service;

import com.cainiao.waybill.bridge.common.metaq.WaybillPickUpTicketEvent;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;

/**
 * 工单事件
 *
 * <AUTHOR>
 * @date 2023-12-05
 */
public interface WaybillPickUpTicketEventSender {

    /**
     * 发送事件
     *
     * @param event 事件
     * @return 发送成功或者失败
     */
    BaseResultDTO<Void> send(WaybillPickUpTicketEvent event);
}
