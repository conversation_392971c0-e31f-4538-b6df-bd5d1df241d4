//package com.cainiao.waybill.bridge.web.charity.controller;
//
//import java.util.List;
//
//import com.cainiao.waybill.bridge.model.charity.DTO.WaybillCharityFileDTO;
//import com.cainiao.waybill.bridge.model.charity.DTO.WaybillCharityFileParameter;
//import com.cainiao.waybill.bridge.model.charity.dao.WaybillCharityFileDAO;
//import io.swagger.annotations.Api;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.ResponseBody;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * This class was generated by Ali-Generator
// * <AUTHOR>
// */
//@Api(tags = "文件图片管理")
//@RestController
//@RequestMapping(value = "/WaybillCharityFile")
//public class WaybillCharityFileController extends BaseController {
//    @Autowired
//    private WaybillCharityFileDAO waybillCharityFileDAO;
//
//    /**
//     * 根据参数查找
//     */
//    @ResponseBody
//    @GetMapping("/find")
//    public WaybillCharityFileDTO find(@RequestParam(required = false) Long id, @RequestParam(required = false) String project, @RequestParam(required = false) String orgCode, @RequestParam(required = false) Byte fileType, @RequestParam(required = false) String fileSubType, @RequestParam(required = false) String operator, @RequestParam(required = false) String receiverOrgCode, @RequestParam(required = false) String image, @RequestParam(required = false) String feature) {
//        WaybillCharityFileParameter param = new WaybillCharityFileParameter();
//        param.setId(id);
//        param.setProject(project);
//        param.setOrgCode(orgCode);
//        param.setFileType(fileType);
//        param.setFileSubType(fileSubType);
//        param.setOperator(operator);
//        param.setReceiverOrgCode(receiverOrgCode);
//        param.setImage(image);
//        param.setFeature(feature);
//        return waybillCharityFileDAO.find(param);
//    }
//
//    /**
//     * 列表查询
//     */
//    @ResponseBody
//    @GetMapping("/list")
//    public List<WaybillCharityFileDTO> list(@RequestParam(required = false) Long id, @RequestParam(required = false) String project, @RequestParam(required = false) String orgCode, @RequestParam(required = false) Byte fileType, @RequestParam(required = false) String fileSubType, @RequestParam(required = false) String operator, @RequestParam(required = false) String receiverOrgCode, @RequestParam(required = false) String image, @RequestParam(required = false) String feature) {
//        WaybillCharityFileParameter param = new WaybillCharityFileParameter();
//        param.setId(id);
//        param.setProject(project);
//        param.setOrgCode(orgCode);
//        param.setFileType(fileType);
//        param.setFileSubType(fileSubType);
//        param.setOperator(operator);
//        param.setReceiverOrgCode(receiverOrgCode);
//        param.setImage(image);
//        param.setFeature(feature);
//        return waybillCharityFileDAO.list(param);
//    }
//
//    /**
//     * 创建
//     */
//    @RequestMapping("/create")
//    public void create(@RequestParam(required = false) Long id, @RequestParam(required = false) String project, @RequestParam(required = false) String orgCode, @RequestParam(required = false) Byte fileType, @RequestParam(required = false) String fileSubType, @RequestParam(required = false) String operator, @RequestParam(required = false) String receiverOrgCode, @RequestParam(required = false) String image, @RequestParam(required = false) String feature) {
//        WaybillCharityFileParameter param = new WaybillCharityFileParameter();
//        param.setId(id);
//        param.setProject(project);
//        param.setOrgCode(orgCode);
//        param.setFileType(fileType);
//        param.setFileSubType(fileSubType);
//        param.setOperator(operator);
//        param.setReceiverOrgCode(receiverOrgCode);
//        param.setImage(image);
//        param.setFeature(feature);
//        waybillCharityFileDAO.create(param);
//    }
//
//    /**
//     * 修改
//     */
//    @RequestMapping("/update")
//    public void updateSelective(@RequestParam(required = false) Long id, @RequestParam(required = false) String project, @RequestParam(required = false) String orgCode, @RequestParam(required = false) Byte fileType, @RequestParam(required = false) String fileSubType, @RequestParam(required = false) String operator, @RequestParam(required = false) String receiverOrgCode, @RequestParam(required = false) String image, @RequestParam(required = false) String feature) {
//        WaybillCharityFileDTO dto = new WaybillCharityFileDTO();
//        dto.setId(id);
//        dto.setProject(project);
//        dto.setOrgCode(orgCode);
//        dto.setFileType(fileType);
//        dto.setFileSubType(fileSubType);
//        dto.setOperator(operator);
//        dto.setReceiverOrgCode(receiverOrgCode);
//        dto.setImage(image);
//        dto.setFeature(feature);
//        WaybillCharityFileParameter param = new WaybillCharityFileParameter();
//        param.setId(id);
//        param.setProject(project);
//        param.setOrgCode(orgCode);
//        param.setFileType(fileType);
//        param.setFileSubType(fileSubType);
//        param.setOperator(operator);
//        param.setReceiverOrgCode(receiverOrgCode);
//        param.setImage(image);
//        param.setFeature(feature);
//        waybillCharityFileDAO.updateSelective(dto, param);
//    }
//}
