package com.cainiao.waybill.bridge.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/17
 **/
@Data
public class WaybillBridgeEnterprisePackBoundDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 运单号
     */
    private String waybillCode;

    /**
     * 状态
     * com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePackInboundStatusEnum
     */
    private Byte status;

    /**
     * cp的编码
     */
    private String cpCode;

    /**
     * cp名称
     */
    private String cpName;

    /**
     * 快递业务类型
     * com.cainiao.waybill.bridge.enterprise.common.enums.EnterprisePackInboundBizTypeEnum
     */
    private Byte bizType;

    /**
     * 收件人-姓名
     */
    private String consigneeName;

    /**
     * 收件人-电话
     */
    private String consigneePhone;

    /**
     * 收件人-手机
     */
    private String consigneeMobile;

    /**
     * 取件码
     */
    private String pickUpCode;

    /**
     * 取件人用户ID
     */
    private String pickUpUserId;

    /**
     * 收件人工号
     */
    private String pickUpUserJobNumber;

    /**
     * 收件人工号
     */
    private String pickUpUserName;

    /**
     * 货架
     */
    private String shelf;

    /**
     * 入库时间
     */
    private Date inboundTime;

    /**
     * 出库时间
     */
    private Date outboundTime;

    /**
     * 操作人用户id
     */
    private String operatorUserId;

    /**
     * 操作人工号
     */
    private String operatorJobNumber;

    /**
     * 操作人工号
     */
    private String operatorUserName;

    /**
     * 场地ID
     */
    private String locationId;

    /**
     * 小邮局ID
     */
    private String postId;

    /**
     * 小邮局名称
     */
    private String postName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否到付
     */
    private Integer delivery;
}
