package com.cainiao.waybill.bridge.common.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Calendar;
import java.util.Date;

import com.alibaba.common.lang.StringUtil;

import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import org.apache.commons.lang3.time.DateFormatUtils;

/**
 * 日期 工具类
 * <p>
 * Created by shouyuan.lzl on 2017-04-27 5:49 PM.
 */
public class BridgeDateUtil {

    public static String pattern24hh = "yyyy-MM-dd HH:mm:ss";

    public static String patternDay = "yyyyMMdd";

    public static String patternDayFormat = "yyyy-MM-dd";

    public static String pattern24Format = "yyyyMMddHHmmss";

    public static String pattern24NoSeconds = "yyyy-MM-dd HH:mm";

    public static String patternHourMinute = "HH:mm";

    public static String patternHourMinuteSecond = "HH:mm:ss";


    /**
     * 将指定格式的字符串转换为Date类型的时间
     *
     * @return
     * @throws ParseException
     */
    public static Date strToDate(String str, String pattern) {
        if (StringUtil.isBlank(str)) {
            return null;
        }
        if (pattern == null) {
            pattern = pattern24hh;
        }
        DateFormat ymdhmsFormat = new SimpleDateFormat(pattern);
        try {
            return ymdhmsFormat.parse(str);
        } catch (ParseException e) {
            throw new BridgeBusinessException("time_format_error", "时间格式错误");
        }
    }

    /**
     * 将指定格式的字符串转换为Date类型的时间
     *
     * @param str
     * @return
     * @throws ParseException
     */
    public static Date strToDate(String str) {
        if (StringUtil.isBlank(str)) {
            return null;
        }
        DateFormat ymdhmsFormat = new SimpleDateFormat(pattern24hh);
        try {
            return ymdhmsFormat.parse(str);
        } catch (ParseException e) {
            throw new BridgeBusinessException("time_format_error", "时间格式错误");
        }
    }

    public static String dateToStr(Date date) {
        if (date == null) {
            return null;
        }
        DateFormat df = new SimpleDateFormat(pattern24hh);
        return df.format(date);
    }

    public static String dateToStr(Date date, String pattern) {
        if (date == null) {
            return null;
        }
        DateFormat df = new SimpleDateFormat(pattern);
        return df.format(date);
    }

    /**
     * 获取n天前的日期
     * @param n
     * @return
     */
    public static Date getDateBeforeNDays(int n) {
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 计算n天前的日期
        LocalDate dateBeforeDays = today.minusDays(n);

        // 将 LocalDate 转换为 ZonedDateTime
        ZonedDateTime zonedDateTime = dateBeforeDays.atStartOfDay(ZoneId.systemDefault());
        // 将 ZonedDateTime 转换为 Date
        return Date.from(zonedDateTime.toInstant());
    }

    public static Date dateToStartTime(Date date) {
        if (date == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        return calendar.getTime();
    }

    public static Date dateToEndTime(Date date) {
        if (date == null) {
            return null;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MILLISECOND, 999);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        return calendar.getTime();
    }

    public static void main(String[] args) {
        Date preStartDate = BridgeDateUtil.dateToStartTime(BridgeDateUtil.getDateBeforeNDays(1));
        Date preEndDate =  BridgeDateUtil.dateToEndTime(BridgeDateUtil.getDateBeforeNDays(1));
        System.out.println(dateToStr(preStartDate));
        System.out.println(dateToStr(preEndDate));
    }

}
