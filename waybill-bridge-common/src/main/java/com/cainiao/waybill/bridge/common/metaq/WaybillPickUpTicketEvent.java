package com.cainiao.waybill.bridge.common.metaq;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 逆向物流生命周期消息
 *
 * <AUTHOR>
 * @date 2021/6/16
 */
@Data
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WaybillPickUpTicketEvent implements Serializable {

    private static final long serialVersionUID = 402822744548580168L;
    /**
     * cpCode，物流公司编号
     * {@see com.cainiao.waybill.bridge.common.waybill.constants.WaybillPickUpTicketConstant.WaybillPickUpTicketEvent}
     */
    private String action;

    /**
     * 工单id
     */
    private String ticketId;

    /**
     * 服务商
     */
    private String cpCode;

    /**
     * 运单号
     */
    private String mailNo;

    /**
     * 扩展信息，json
     */
    private String extraInfo;
}
