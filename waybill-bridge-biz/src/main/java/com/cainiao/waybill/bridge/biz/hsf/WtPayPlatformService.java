package com.cainiao.waybill.bridge.biz.hsf;

/**
 * <AUTHOR>
 * @date 2024/11/11 10:49
 **/


import java.math.BigDecimal;

import com.cainiao.waybill.bridge.common.dto.WtAccountDTO;

public interface WtPayPlatformService {

    /**
     * 支付扣费
     * @param flowId 流水id 幂等
     * @param amount 金额 元
     * @param accountId
     * @param ownerId
     */
    void wtPayOrder(String flowId, String amount, long accountId, String ownerId);

    /**
     *  调账
     * @param flowId 流水id 幂等
     * @param amount 金额 元
     * @param accountId 账号
     * @param ownerId
     */
    void wtRecharge(String flowId, String amount, long accountId, String ownerId);


    /**
     * 查询淘外结算平台账号ID
     * @param accountId
     * @return
     */
    WtAccountDTO queryAccount(String accountId);

}
