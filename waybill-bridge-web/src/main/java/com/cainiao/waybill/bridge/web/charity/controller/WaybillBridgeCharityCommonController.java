package com.cainiao.waybill.bridge.web.charity.controller;

import java.util.Map;

import com.cainiao.cnlogin.api.context.CnUserInfo;
import com.cainiao.cnlogin.api.context.CnUserInfoUtil;
import com.cainiao.waybill.bridge.biz.common.user.WaybillBridgeUserInfoService;
import com.cainiao.waybill.bridge.common.config.diamond.WaybillCharityDiamondConfig;
import com.cainiao.waybill.bridge.model.dto.WaybillBridgeUserInfoDTO;
import com.cainiao.waybill.bridge.web.charity.CharityBaseResult;
import com.cainiao.waybill.bridge.web.charity.CharityLoginUserUtil;
import com.cainiao.waybill.bridge.web.charity.request.CharityImportTemplateRequest;
import com.cainiao.waybill.bridge.web.charity.response.CharityImportTemplateResponse;
import com.cainiao.waybill.bridge.web.charity.response.CharityUserInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * This class was generated by Ali-Generator
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/charity")
@Api(tags = "用户查询")
public class WaybillBridgeCharityCommonController extends BaseController {

    /**
     * 根据参数查找
     */
    @ApiOperation("查询登录用户")
    @ResponseBody
    @RequestMapping("/userInfo")
    public CharityBaseResult<CharityUserInfo> find() {
        CharityUserInfo charityUserInfo = new CharityUserInfo();
        CnUserInfo userInfo = CnUserInfoUtil.getLoginContext();

        charityUserInfo.setAccount(userInfo.getAccount());
        return CharityBaseResult.success(charityUserInfo);
    }

    /**
     * 导入模板下载
     */
    @ApiOperation("导入模板下载")
    @ResponseBody
    @RequestMapping("/import/template")
    public CharityBaseResult<CharityImportTemplateResponse> importTemplate(
        @RequestBody CharityImportTemplateRequest request) {
        CharityImportTemplateResponse response = new CharityImportTemplateResponse();

        Map<String, String> map = WaybillCharityDiamondConfig.getConfig().getImportTemplates();
        if (map == null) {
            return CharityBaseResult.success(response);
        }
        response.setTemplateFileUrl(map.get(request.getType()));
        return CharityBaseResult.success(response);
    }

}
