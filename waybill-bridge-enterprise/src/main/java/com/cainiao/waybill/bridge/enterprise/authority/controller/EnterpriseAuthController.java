package com.cainiao.waybill.bridge.enterprise.authority.controller;

import com.cainiao.waybill.bridge.common.constants.BridgeLogConstants;
import com.cainiao.waybill.bridge.enterprise.authority.request.AccessTokenRequest;
import com.cainiao.waybill.bridge.enterprise.authority.request.LoginRequest;
import com.cainiao.waybill.bridge.enterprise.authority.response.AccessTokenResponse;
import com.cainiao.waybill.bridge.enterprise.authority.response.LoginResponse;
import com.cainiao.waybill.bridge.enterprise.authority.service.DingTalkAuthService;
import com.cainiao.waybill.bridge.enterprise.common.EnterpriseBaseResult;
import com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseErrorEnum;
import com.cainiao.waybill.bridge.enterprise.common.logger.EnterpriseException;
import com.google.gson.Gson;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 该类用于处理企业认证相关的操作。
 * 1 - getAccessToken(AccessTokenRequest): 该方法用于获取企业的访问令牌，通常需要提供企业的认证请求信息。
 * <AUTHOR>
 * @date 2025-04-15 11:27:26
 */
@RestController
@RequestMapping(value = "/enterprise/auth")
public class EnterpriseAuthController {
    public static final Logger LOGGER = LoggerFactory.getLogger(BridgeLogConstants.LogAppender.WAYBILL_ENTERPRISE);

    @Autowired
    private DingTalkAuthService dingTalkAuthService;

    /**
     * 获取访问令牌-【测试环境使用，方便测试】
     *
     * <p>该方法通过请求参数获取平台和授权公司ID, 调用外部服务获取访问令牌, 然后封装成响应对象返回。
     *
     * @param request 访问令牌请求对象, 包含平台信息和授权公司ID
     * @return 返回封装后的访问令牌响应对象, 成功时包含访问令牌
     */
    @PostMapping("/getAccessToken")
    EnterpriseBaseResult<AccessTokenResponse> getAccessToken(@RequestBody AccessTokenRequest request) {
        LOGGER.info("getAccessToken request:{}", request);
//        Boolean result = dingTalkAuthService.checkSession(request.getUserId(), sessionId);
//        if(BooleanUtils.isFalse(result)){
//            return EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.LOGIN_SESSION_EXPIRED_OR_NOT_RIGHT.code(), EnterpriseErrorEnum.LOGIN_SESSION_EXPIRED_OR_NOT_RIGHT.describe());
//        }
        String accessToken = dingTalkAuthService.getAccessToken(request.getPlatform(), request.getAuthCorpId());
        AccessTokenResponse response = new AccessTokenResponse();
        response.setAccessToken(accessToken);
        return EnterpriseBaseResult.success(response);
    }

    @PostMapping("/login")
    public void login(@RequestBody LoginRequest request, HttpServletResponse response) throws IOException {
        LoginResponse loginResponse = new LoginResponse();
        try {
            String sessionId = dingTalkAuthService.createSession(request.getCorpId(), request.getUserId(), request.getSalt());
            // 设置响应状态
            response.setStatus(HttpServletResponse.SC_OK);

//            // 把 sessionId 返回给前端，Header or Cookie 都可以
//            response.setHeader("X-Session-Id", sessionId);
//            response.setStatus(HttpServletResponse.SC_OK);
            loginResponse.setResultCode(EnterpriseErrorEnum.SUCCESS.code());
            loginResponse.setSession(sessionId);
            loginResponse.setCsrfToken(sessionId);
            response.getWriter().write(new Gson().toJson(EnterpriseBaseResult.success(loginResponse)));
        } catch (EnterpriseException e){
            loginResponse.setResultCode(e.getErrorCode());
            response.getWriter().write(new Gson().toJson(EnterpriseBaseResult.bizFail(e.getErrorCode(), e.getErrorMessage())));
        } catch (Exception e){
            LOGGER.error("login error: {}", ExceptionUtils.getStackTrace(e));
            response.getWriter().write(new Gson()
                    .toJson(EnterpriseBaseResult.bizFail(EnterpriseErrorEnum.SYSTEM_ERROR.code(), e.getMessage())));
        }
    }
}
