package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * Description:揽件码随机数k-v表，k代表下标，v代表随机数
 * <AUTHOR>
 * Date 2017-04-26
 */
public class PickupNumSequenceDO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 序号
     */
    private Long serialNum;

    /**
     * 揽件码数据，一期范围100,000~999,999
     */
    private Long pickupNum;

    /**
     * setter for column 主键
     */
    public void setId(long id) {
        this.id = id;
    }

    /**
     * getter for column 主键
     */
    public long getId() {
        return this.id;
    }

    /**
     * setter for column 创建时间
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * getter for column 创建时间
     */
    public Date getGmtCreate() {
        return this.gmtCreate;
    }

    /**
     * setter for column 序号
     */
    public void setSerialNum(long serialNum) {
        this.serialNum = serialNum;
    }

    /**
     * getter for column 序号
     */
    public long getSerialNum() {
        return this.serialNum;
    }

    /**
     * setter for column 揽件码数据，一期范围100,000~999,999
     */
    public void setPickupNum(long pickupNum) {
        this.pickupNum = pickupNum;
    }

    /**
     * getter for column 揽件码数据，一期范围100,000~999,999
     */
    public long getPickupNum() {
        return this.pickupNum;
    }
}
