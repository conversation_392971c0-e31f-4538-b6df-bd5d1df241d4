package com.cainiao.waybill.bridge.enterprise.authority.configuration;

import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 钉钉客户端
 *
 * <AUTHOR>
 */
@Configuration
public class DingTalkAuthAccessClient {

    /**
     * <b>description</b> :
     * <p>使用 Token 初始化账号Client</p>
     * @return Client
     *
     * @throws Exception
     */
    @Bean
    public Client dingTalkAuthClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new Client(config);
    }

    @Bean
    public com.aliyun.dingtalkcontact_1_0.Client dingTalkContractClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkcontact_1_0.Client(config);
    }


}
