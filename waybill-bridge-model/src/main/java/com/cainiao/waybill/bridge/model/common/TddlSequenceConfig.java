package com.cainiao.waybill.bridge.model.common;

import java.util.List;

/**
 * Created by nut on 16/3/24.
 */
public class TddlSequenceConfig {
    /**
     * 数据源key,是我们存放sequence的四个库,不要随意修改
     */
    private List<String> dbGroupKeys;
    /**
     * TDDL APP_NAME,不要随意修改
     */
    private String       appName;

    /**
     * 步长,表示一次从db取多少个sequence到本地内存缓存中,千万不要随意改,有可能会重复面单号.
     */
    private Integer      innerStep;

    /**
     * 是否自适应,参见com.taobao.tddl.model.sequence.impl.GroupSequenceDao#adjust
     */
    private boolean      adjust;

    /**
     * 失败重试次数
     */
    private Integer      retryTimes;

    /**
     * 库中sequence表的表名
     */
    private String       tableName;

    /**
     * sequence表中的sequence_name的列名
     */
    private String       nameColumnName;

    /**
     * sequence表中的sequence_value的列名
     */
    private String       valueColumnName;

    /**
     * sequence表中的gmtModified的列名
     */
    private String       gmtModifiedColumnName;

    public List<String> getDbGroupKeys() {
        return dbGroupKeys;
    }

    public void setDbGroupKeys(List<String> dbGroupKeys) {
        this.dbGroupKeys = dbGroupKeys;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Integer getInnerStep() {
        return innerStep;
    }

    public void setInnerStep(Integer innerStep) {
        this.innerStep = innerStep;
    }

    public boolean isAdjust() {
        return adjust;
    }

    public void setAdjust(boolean adjust) {
        this.adjust = adjust;
    }

    public Integer getRetryTimes() {
        return retryTimes;
    }

    public void setRetryTimes(Integer retryTimes) {
        this.retryTimes = retryTimes;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getNameColumnName() {
        return nameColumnName;
    }

    public void setNameColumnName(String nameColumnName) {
        this.nameColumnName = nameColumnName;
    }

    public String getValueColumnName() {
        return valueColumnName;
    }

    public void setValueColumnName(String valueColumnName) {
        this.valueColumnName = valueColumnName;
    }

    public String getGmtModifiedColumnName() {
        return gmtModifiedColumnName;
    }

    public void setGmtModifiedColumnName(String gmtModifiedColumnName) {
        this.gmtModifiedColumnName = gmtModifiedColumnName;
    }

    @Override
    public String toString() {
        return "TddlSequenceConfig{" + "dbGroupKeys=" + dbGroupKeys + ", appName='" + appName + '\'' + ", innerStep=" + innerStep + ", adjust=" + adjust + ", retryTimes="
                + retryTimes + ", tableName='" + tableName + '\'' + ", nameColumnName='" + nameColumnName + '\'' + ", valueColumnName='" + valueColumnName + '\''
                + ", gmtModifiedColumnName='" + gmtModifiedColumnName + '\'' + '}';
    }
}
