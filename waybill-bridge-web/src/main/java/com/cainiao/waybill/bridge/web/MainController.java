/*
 * @Author: <PERSON><PERSON>
 * @Date: 2022-04-19 21:19:39
 * @LastEditors: <PERSON><PERSON>
 * @LastEditTime: 2022-04-19 21:25:26
 */
package com.cainiao.waybill.bridge.web;

import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.web.pickup.admin.config.diamond.TwCdnVersionDiamondConfig.TagEnum;
import com.cainiao.waybill.bridge.web.pickup.admin.config.diamond.TwVmMicroDiamondConfig;
import com.cainiao.waybill.bridge.web.pickup.admin.config.diamond.TwCdnVersionDiamondConfig;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * Spring Mvc的根路径、健康检查等。
 * <p>
 * 其中使用了velocity，@VelocityLayout声明了页面使用的layout。详见
 * http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-velocity
 *
 * <AUTHOR>
 */
@Controller
public class MainController {

    /**
     * 健康检查
     */
    @GetMapping("/checkpreload.htm")
    public @ResponseBody
    String checkPreload() {
        return "success";
    }

    @GetMapping("/admin/**")
    public ModelAndView toffeeIndex() {
        ModelAndView modelAndView = new ModelAndView();
        PickUpLogUtil.info("进入跳转到VM的控制器");
        // 如果main.vm被占用可以新建一个vm页面
        modelAndView.setViewName("admin");
        PickUpLogUtil.info("microConfig : " + TwVmMicroDiamondConfig.getMicroConfig());
        String resourceVersion = TwCdnVersionDiamondConfig.getLink(TwCdnVersionDiamondConfig.TagEnum.ADMIN_WEB.name());
        PickUpLogUtil.info("resourceVersion : " + resourceVersion);
        modelAndView.addObject("microConfig", TwVmMicroDiamondConfig.getMicroConfig());
        modelAndView.addObject("resourceVersion", resourceVersion);
        return modelAndView;
    }

    @GetMapping("/onefound/**")
    public ModelAndView toOneFoundIndex() {
        ModelAndView modelAndView = new ModelAndView();
        PickUpLogUtil.info("进入跳转到VM的控制器");
        // 如果main.vm被占用可以新建一个vm页面
        modelAndView.setViewName("charity");
        PickUpLogUtil.info("microConfig : " + TwVmMicroDiamondConfig.getMicroConfig());
        String resourceVersion = TwCdnVersionDiamondConfig.getLink(TagEnum.ONE_FOUND.name());
        PickUpLogUtil.info("resourceVersion : " + resourceVersion);
        modelAndView.addObject("microConfig", TwVmMicroDiamondConfig.getMicroConfig());
        modelAndView.addObject("resourceVersion", resourceVersion);
        return modelAndView;
    }

    @GetMapping("/manager/**")
    public ModelAndView toManager() {
        ModelAndView modelAndView = new ModelAndView();
        PickUpLogUtil.info("进入跳转到VM的控制器");
        // 如果main.vm被占用可以新建一个vm页面
        modelAndView.setViewName("manager");
        PickUpLogUtil.info("microConfig : " + TwVmMicroDiamondConfig.getMicroConfig());
        String resourceVersion = TwCdnVersionDiamondConfig.getLink(TagEnum.MANAGER.name());
        String cnuiVersion = TwCdnVersionDiamondConfig.getLink(TagEnum.CNUI_VERSION.name());
        PickUpLogUtil.info("resourceVersion : " + resourceVersion);
        modelAndView.addObject("microConfig", TwVmMicroDiamondConfig.getMicroConfig());
        modelAndView.addObject("webResourceVersion", resourceVersion);
        modelAndView.addObject("cnuiVersion", cnuiVersion);
        return modelAndView;
    }

    @GetMapping("/aliSocialOperations/**")
    public ModelAndView toAliSocialOperations() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("aliSocialOperations");
        String resourceVersion = TwCdnVersionDiamondConfig.getLink(TagEnum.ALI_SOCIAL_OPERATIONS.name());
        modelAndView.addObject("resourceVersion", resourceVersion);
        return modelAndView;
    }

    @GetMapping("/aliSocialCharityManager/**")
    public ModelAndView toAliSocialCharityManager() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("aliSocialCharityManager");
        String resourceVersion = TwCdnVersionDiamondConfig.getLink(TagEnum.ALI_SOCIAL_CHARITY_MANAGER.name());
        modelAndView.addObject("resourceVersion", resourceVersion);
        return modelAndView;
    }


    /**
     * 创建用于展示企业页面的模型和视图对象
     */
    @GetMapping("/enterprise/**")
    public ModelAndView toEnterprise() {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.setViewName("enterprise");
        String enterpriseWebVersion = TwCdnVersionDiamondConfig.getLink(TagEnum.ENTERPRISE_WEB_VERSION.name());
        String enterpriseWebUrl = TwCdnVersionDiamondConfig.getLink(TagEnum.ENTERPRISE_WEB_URL.name());
        modelAndView.addObject("enterpriseWebVersion", enterpriseWebVersion);
        modelAndView.addObject("enterpriseWebUrl", enterpriseWebUrl);
        return modelAndView;
    }
}
