package com.cainiao.waybill.bridge.model.domain;

import java.util.Date;

/**
 * Database Table Remarks:
 *   用户表
 *
 * <AUTHOR>
 */
public class WaybillBridgeUserInfoDO {
    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   账户昵称
     *
     *
     * @mbg.generated
     */
    private String nick;

    /**
     * Database Column Remarks:
     *   账户名称
     *
     *
     * @mbg.generated
     */
    private String phone;

    /**
     * Database Column Remarks:
     *   菜鸟会员id
     *
     *
     * @mbg.generated
     */
    private String cainiaoUserId;

    /**
     * Database Column Remarks:
     *   角色
     *
     *
     * @mbg.generated
     */
    private String role;

    /**
     * Database Column Remarks:
     *   业务场景编码：Ntb淘外，OneFound壹基金
     *
     *
     * @mbg.generated
     */
    private String scene;

    /**
     * Database Column Remarks:
     *   扩展字段
     *
     *
     * @mbg.generated
     */
    private String feature;

    /**
     * Database Column Remarks:
     *   状态，1在用，-1停用
     *
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * @return the value of waybill_bridge_user_info.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for waybill_bridge_user_info.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of waybill_bridge_user_info.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for waybill_bridge_user_info.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of waybill_bridge_user_info.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for waybill_bridge_user_info.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of waybill_bridge_user_info.nick
     *
     * @mbg.generated
     */
    public String getNick() {
        return nick;
    }

    /**
     *
     * @param nick the value for waybill_bridge_user_info.nick
     *
     * @mbg.generated
     */
    public void setNick(String nick) {
        this.nick = nick;
    }

    /**
     *
     * @return the value of waybill_bridge_user_info.phone
     *
     * @mbg.generated
     */
    public String getPhone() {
        return phone;
    }

    /**
     *
     * @param phone the value for waybill_bridge_user_info.phone
     *
     * @mbg.generated
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     *
     * @return the value of waybill_bridge_user_info.cainiao_user_id
     *
     * @mbg.generated
     */
    public String getCainiaoUserId() {
        return cainiaoUserId;
    }

    /**
     *
     * @param cainiaoUserId the value for waybill_bridge_user_info.cainiao_user_id
     *
     * @mbg.generated
     */
    public void setCainiaoUserId(String cainiaoUserId) {
        this.cainiaoUserId = cainiaoUserId;
    }

    /**
     *
     * @return the value of waybill_bridge_user_info.role
     *
     * @mbg.generated
     */
    public String getRole() {
        return role;
    }

    /**
     *
     * @param role the value for waybill_bridge_user_info.role
     *
     * @mbg.generated
     */
    public void setRole(String role) {
        this.role = role;
    }

    /**
     *
     * @return the value of waybill_bridge_user_info.scene
     *
     * @mbg.generated
     */
    public String getScene() {
        return scene;
    }

    /**
     *
     * @param scene the value for waybill_bridge_user_info.scene
     *
     * @mbg.generated
     */
    public void setScene(String scene) {
        this.scene = scene;
    }

    /**
     *
     * @return the value of waybill_bridge_user_info.feature
     *
     * @mbg.generated
     */
    public String getFeature() {
        return feature;
    }

    /**
     *
     * @param feature the value for waybill_bridge_user_info.feature
     *
     * @mbg.generated
     */
    public void setFeature(String feature) {
        this.feature = feature;
    }

    /**
     *
     * @return the value of waybill_bridge_user_info.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     *
     * @param status the value for waybill_bridge_user_info.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", nick=").append(nick);
        sb.append(", phone=").append(phone);
        sb.append(", cainiaoUserId=").append(cainiaoUserId);
        sb.append(", role=").append(role);
        sb.append(", scene=").append(scene);
        sb.append(", feature=").append(feature);
        sb.append(", status=").append(status);
        sb.append("]");
        return sb.toString();
    }
}
