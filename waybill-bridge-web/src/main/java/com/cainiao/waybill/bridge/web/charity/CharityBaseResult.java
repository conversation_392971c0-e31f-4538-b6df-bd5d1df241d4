package com.cainiao.waybill.bridge.web.charity;

import java.io.Serializable;

import lombok.Data;

/**
 * <AUTHOR> zouping.fzp
 * @Classname CharityBaseResult
 * @Description
 * @Date 2022/8/29 2:34 下午
 * @Version 1.0
 */
@Data
public class CharityBaseResult <T> implements Serializable {

    private static final long serialVersionUID = -2655944625760119468L;

    private String errorCode;
    private String errorMsg;
    private boolean success;
    private T data;

    public static <T> CharityBaseResult<T> success(T data) {
        CharityBaseResult<T> result = new CharityBaseResult<T>();
        result.setData(data);
        result.setSuccess(true);
        return result;
    }

    public static <T> CharityBaseResult<T> success() {
        CharityBaseResult<T> result = new CharityBaseResult<T>();
        result.setSuccess(true);
        return result;
    }

    public static <T> CharityBaseResult<T> fail(T data) {
        CharityBaseResult<T> result = new CharityBaseResult<T>();
        result.setData(data);
        result.setSuccess(false);
        return result;
    }

    public static <T> CharityBaseResult<T> bizFail(String errorCode, String errorMsg) {
        CharityBaseResult<T> result = new CharityBaseResult<T>();
        result.setSuccess(false);
        result.setErrorCode(errorCode);
        result.setErrorMsg(errorMsg);
        return result;
    }

    public static <T> CharityBaseResult<T> systemFail() {
        CharityBaseResult<T> result = new CharityBaseResult<T>();
        result.setSuccess(false);
        result.setErrorCode("-1");
        result.setErrorMsg("后台系统异常");
        return result;
    }
}
