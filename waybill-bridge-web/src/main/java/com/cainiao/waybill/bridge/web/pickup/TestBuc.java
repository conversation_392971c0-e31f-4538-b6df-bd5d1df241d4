package com.cainiao.waybill.bridge.web.pickup;

import com.alibaba.buc.sso.client.util.SimpleUserUtil;
import com.alibaba.buc.sso.client.vo.BucSSOUser;
import com.alibaba.fastjson.JSON;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021/11/9-下午5:39
 */
@RestController
@RequestMapping("/buc")
public class TestBuc {
    @Resource
    private HttpServletRequest httpServletRequest;

    @GetMapping
    public String test() throws IOException, ServletException {
        BucSSOUser user = SimpleUserUtil.getBucSSOUser(httpServletRequest);
        return JSON.toJSONString(user);
    }
}
