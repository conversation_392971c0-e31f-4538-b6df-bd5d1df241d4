package com.cainiao.waybill.bridge.model.domain;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/1/17-下午4:16
 */
@Data
public class WaybillLdOutRecordDO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    private String cpCode;

    private String mailNo;

    private String orderChannel;

    private Integer sub;

    private Integer bizType;

    private String fromAppKey;

    private String fromResCode;

    /**
     * jsonObject
     * key {@link com.cainiao.waybill.bridge.biz.pickup.constants.PickUpConstants.TraceFeatureKey}
     */
    private String feature;
}
