package com.cainiao.waybill.bridge.web.pickup.admin;

import com.cainiao.cnlogin.api.context.CnUserInfo;
import com.cainiao.cnlogin.api.context.CnUserInfoUtil;
import com.cainiao.waybill.bridge.biz.middleware.BridgeSwitch;
import com.cainiao.waybill.bridge.common.util.ListUtil;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PermissionCheck
 * @Description
 * @Date 2022/11/16 10:19 上午
 * @Version 1.0
 */
public class PermissionCheck {

    public static boolean checkReportPermission(){
        CnUserInfo userInfo = CnUserInfoUtil.getLoginContext();
        String phone = userInfo.getAccount();
        return !ListUtil.non(BridgeSwitch.reportWhiteUserList).contains(phone);
    }

    public static boolean checkCustomerServicePermission(){
        CnUserInfo userInfo = CnUserInfoUtil.getLoginContext();
        String phone = userInfo.getAccount();
        return !ListUtil.non(BridgeSwitch.customerServiceWhiteUserList).contains(phone);
    }

    public static String getUserPhone(){
        CnUserInfo userInfo = CnUserInfoUtil.getLoginContext();
        String phone = userInfo.getAccount();
        return phone;
    }
}
