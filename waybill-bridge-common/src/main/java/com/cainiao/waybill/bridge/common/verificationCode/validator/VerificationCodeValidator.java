package com.cainiao.waybill.bridge.common.verificationCode.validator;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.exception.BridgeValidationException;
import com.cainiao.waybill.bridge.common.validator.BridgeBaseValidator;
import com.cainiao.waybill.bridge.common.verificationCode.dto.request.SendVerificationCodeRequest;
import com.cainiao.waybill.bridge.common.verificationCode.dto.request.VerifyVirificationCodeRequest;

/**
 *
 * 验证码服务参数校验
 *
 * Created by shouyuan.lzl on 2017-04-27 6:34 PM.
 */
public class VerificationCodeValidator extends BridgeBaseValidator {


    /**
     * 发送验证码参数校验
     * @param request
     * @param clientInfoDTO
     * @throws BridgeValidationException
     */
    public static void validateSendVerificationCodeRequest(SendVerificationCodeRequest request, ClientInfoDTO clientInfoDTO)
            throws BridgeValidationException {
        /**
         * request 校验
         */
        validate( objectNotNull(request) ,"request 不能为空");

        validate( longGtZero(request.getSellerId()),"sellerId需要大于0");
        validate( longGtZero(request.getCourierId()), "courierId需要大于0");
        validate(stringHasChar(request.getCourierMobile()) , "courierMobile需要有值");
        validate(stringHasChar(request.getSellerNick()),"sellerNick需要有值");
        validateVerificationCodeType(request.getVerificationCodeType());

        /**
         * clientInfoDTO 校验
         */
        validateClientInfoDTO(clientInfoDTO);
    }

    /**
     * 校验验证码入参校验
     * @param request
     * @param clientInfoDTO
     */
    public static void validateVerifyVerificationCodeRequest(VerifyVirificationCodeRequest request, ClientInfoDTO clientInfoDTO)
            throws BridgeValidationException {

        /**
         * request 校验
         */
        validate(objectNotNull(request), "request不能为空");
        validate(longGtZero(request.getSellerId()), "selllerId需要大于0");
        validate(longGtZero(request.getCourierId()), "courierId需要大于0");
        validate(stringHasChar(request.getCourierMobile()),"courierMobile需要有值");
        validate(stringHasChar(request.getVerificationCode()),"verificationCode需要有值");
        validateVerificationCodeType(request.getVerificationCodeType());

        /**
         * clientInfoDTO校验
         */
        validateClientInfoDTO(clientInfoDTO);
    }

    /**
     * 校验verificationCodeType
     *
     * @param verificationCodeType
     * @throws BridgeValidationException
     */
    private static void validateVerificationCodeType(Integer verificationCodeType) throws BridgeValidationException {
        boolean valid = integerGtZero(verificationCodeType) && verificationCodeType == 1 ;
        validate( valid,"verificationType不合法");
    }
}
