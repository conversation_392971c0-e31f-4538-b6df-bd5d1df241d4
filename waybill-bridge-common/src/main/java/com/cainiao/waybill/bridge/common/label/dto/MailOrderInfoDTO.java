package com.cainiao.waybill.bridge.common.label.dto;


import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * 寄件订单信息
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/4/25.
 */
public class MailOrderInfoDTO extends BaseDTO {

    private static final long serialVersionUID = -8417713730995990229L;

    /**
     * 发件人信息
     */
    private UserInfoDTO sender;

    /**
     * 收件人信息
     */
    private UserInfoDTO recipient;

    /**
     * 交易订单信息
     */
    private OrderInfoDTO orderInfoDTO;

    /**
     * 包裹信息
     * <note>不能为空，里面的数据可以为空</note>
     */
    private PackageInfoDTO packageInfo;

    /**
     * 物流服务信息，json格式
     */
    private String logisticsServices;

    public UserInfoDTO getSender() {
        return sender;
    }

    public void setSender(UserInfoDTO sender) {
        this.sender = sender;
    }

    public UserInfoDTO getRecipient() {
        return recipient;
    }

    public void setRecipient(UserInfoDTO recipient) {
        this.recipient = recipient;
    }

    public OrderInfoDTO getOrderInfoDTO() {
        return orderInfoDTO;
    }

    public void setOrderInfoDTO(OrderInfoDTO orderInfoDTO) {
        this.orderInfoDTO = orderInfoDTO;
    }

    public PackageInfoDTO getPackageInfo() {
        return packageInfo;
    }

    public void setPackageInfo(PackageInfoDTO packageInfo) {
        this.packageInfo = packageInfo;
    }

    public String getLogisticsServices() {
        return logisticsServices;
    }

    public void setLogisticsServices(String logisticsServices) {
        this.logisticsServices = logisticsServices;
    }
}
