package com.cainiao.waybill.bridge.biz.utils.igraph;

import com.taobao.igraph.client.core.IGraphClient;
import com.taobao.igraph.client.core.IGraphClientBuilder;

/**
 *
 * iGraph查询服务
 * 文档链接：https://aliyuque.antfin.com/igraph/axecp0/mp53om
 * <AUTHOR>
 * @date 2024/5/20 下午9:42
 **/
public class GraphClientSingleton {

    private static volatile IGraphClient clientInstance;

    private GraphClientSingleton() {

    }

    /**
     * 构造客户端
     * @param searchDomain 查询服务的VIPServer Domain
     * @param updateDomain 更新服务的VIPServer Domain
     * @param src 请求调用者的名称 用户自己定义的场景名称
     * @return
     */
    public static IGraphClient getInstance(String searchDomain, String updateDomain, String src) {
        if (clientInstance == null) {
            synchronized (GraphClientSingleton.class) {
                if (clientInstance == null) {
                    clientInstance = createIGraphClient(searchDomain, updateDomain, src);
                }
            }
        }
        return clientInstance;
    }

    /**
     * 创建客户端
     * @param searchDomain
     * @param updateDomain
     * @param src
     * @return
     */
    private static IGraphClient createIGraphClient(String searchDomain, String updateDomain, String src) {
        IGraphClientBuilder builder = IGraphClientBuilder.create();
        builder.setSocketTimeout(200);
        builder.setConnectTimeout(200);
        builder.setConnectionRequestTimeout(200);
        builder.setSearchCluster("DEFAULT");
        builder.setUpdateCluster("DEFAULT");
        builder.setMaxConnTotal(200);
        builder.setMaxConnPerRoute(8);
        return builder.build(src, searchDomain, updateDomain);
    }
}
