/*
package com.cainiao.waybill.bridge.web.charity.controller;

import static org.mockito.Mockito.*;

import java.util.Collections;
import java.util.List;

import com.base.BaseTest;
import com.cainiao.waybill.bridge.biz.charity.request.CharityOrgUserRelationSaveRequest;
import com.cainiao.waybill.bridge.biz.charity.response.CharityOrgResponse;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityOrgService;
import com.cainiao.waybill.bridge.biz.charity.service.WaybillCharityOrgUserRelationService;
import com.cainiao.waybill.bridge.biz.common.user.WaybillBridgeUserInfoService;
import com.cainiao.waybill.bridge.model.charity.DTO.WaybillCharityOrgDTO;
import com.cainiao.waybill.bridge.web.charity.CharityBaseResult;
import org.apache.commons.compress.utils.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

*/
/**
 * <a href="https://alidocs.dingtalk.com/i/nodes/dpYLaezmVNRMGX56C1aKjlBOVrMqPxX6">单元测试编写&生成FAQ</a>
 * Created By Thub IDEA插件
 *//*

public class WaybillBridgeOrgUserRelationControllerTest extends BaseTest {

    @InjectMocks
    private WaybillBridgeOrgUserRelationController waybillBridgeOrgUserRelationController;
    @Mock
    private WaybillCharityOrgUserRelationService orgUserRelationService;
    @Mock
    private WaybillCharityOrgService charityOrgService;
    @Mock
    private WaybillBridgeUserInfoService userInfoService;

    @Before
    public void setUp() throws Exception {MockitoAnnotations.initMocks(this);}

    @Test
    public void testCreate(){
        // Setup, prepare mock data

        CharityOrgResponse orgResponse = new CharityOrgResponse();
        orgResponse.setProvince("330000");
        orgResponse.setOrgCode("wm1008580132056576");

        Mockito.when(charityOrgService.findByCode(anyString(), anyString()))
            .thenReturn(orgResponse);
        WaybillCharityOrgDTO userCharityOrg = new WaybillCharityOrgDTO();
        userCharityOrg.setType((byte)1);
        userCharityOrg.setProvince("330000");
        Mockito.when(charityOrgService.listOrgByUserId(anyString(), anyLong()))
            .thenReturn(Collections.singletonList(userCharityOrg));

        // Prepare method arguments

        // Run the method to be tested
        CharityOrgUserRelationSaveRequest request = new CharityOrgUserRelationSaveRequest();
        request.setRole("other");
        request.setProject("warm");
        request.setOrgCode("wm1008580132056576");
        request.setLoginUserId(1296L);
        CharityBaseResult<Void> result = waybillBridgeOrgUserRelationController.create(request);

        Assert.assertTrue(result.isSuccess());

        // Verify the result
    }
}*/
