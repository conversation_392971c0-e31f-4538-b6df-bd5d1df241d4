package com.cainiao.waybill.bridge.model.dao;

import java.util.Date;

import com.cainiao.waybill.bridge.model.domain.AccountBalanceAlarmDO;
import com.taobao.common.dao.persistence.exception.DAOException;

/**
 * 账户余额告警配置 DAO
 * <AUTHOR>
 * @since 2017/05/05
 */
public interface AccountBalanceAlarmDAO {
	
    /**
     * 获取余额提醒规则表数据
     * 
     * @param cpId
     * @param branchCode
     * @param accountId
     * @return
     */
    AccountBalanceAlarmDO getAccountBalanceAlarm(Long cpId, String branchCode, Long accountId, String segmentCode)
    		throws DAOException;

    /**
     * 插入余额提醒规则表数据
     * 
     * @param accountBalanceAlarmDO
     * @return
     */
    int insert(AccountBalanceAlarmDO accountBalanceAlarmDO) throws DAOException;

    /**
     * 更新余额配置表数据
     * 
     * @param cpId
     * @param branchCode
     * @param accountId
     * @param alarmQuantity
     * @param intervalHour
     * @return
     */
    int updateRule(Long cpId, String branchCode, Long accountId, String segmentCode, int alarmQuantity,
                   int intervalHour, String phone) throws DAOException;

    /**
     * 更新最后一次发送时间
     * 
     * @param cpId
     * @param branchCode
     * @param accountId
     * @param lastAlarmTime
     * @return
     */
    int updateDate(Long cpId, String branchCode, Long accountId, String segmentCode,Date lastAlarmTime) throws DAOException;
}
