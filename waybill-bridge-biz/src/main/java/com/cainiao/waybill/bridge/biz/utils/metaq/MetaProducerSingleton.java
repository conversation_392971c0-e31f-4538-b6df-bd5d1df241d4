package com.cainiao.waybill.bridge.biz.utils.metaq;

import com.alibaba.rocketmq.client.exception.MQClientException;

import com.taobao.metaq.client.MetaProducer;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/9/10 18:15
 **/
public class MetaProducerSingleton {

    private static volatile MetaProducer clientInstance;

    private MetaProducerSingleton() {

    }

    /**
     * 构造客户端
     * @return
     */
    public static MetaProducer getInstance(String producerName) {
        if (clientInstance == null) {
            synchronized (MetaProducerSingleton.class) {
                if (clientInstance == null) {
                    clientInstance = createMetaProducer(producerName);
                    /**
                     * Producer对象在使用之前必须要调用start初始化，初始化一次即可<br>
                     * 注意：切记不可以在每次发送消息时，都调用start方法
                     */
                    try {
                        clientInstance.start();
                    } catch (MQClientException e) {
                        e.fillInStackTrace();
                    }
                }
            }
        }
        return clientInstance;
    }

    /**
     * 创建客户端
     * @return
     */
    private static MetaProducer createMetaProducer(String producerName) {
        if(StringUtils.isBlank(producerName)){
            producerName = "waybill-bridge-test-producer-group";
        }
        return new MetaProducer(producerName);
    }

}