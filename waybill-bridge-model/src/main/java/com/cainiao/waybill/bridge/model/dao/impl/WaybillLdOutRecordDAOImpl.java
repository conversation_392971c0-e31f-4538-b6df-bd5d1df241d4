package com.cainiao.waybill.bridge.model.dao.impl;

import com.cainiao.waybill.bridge.common.waybill.pickup.dto.WaybillLdSubRecordCreateTimeQueryDTO;
import com.cainiao.waybill.bridge.common.waybill.pickup.dto.WaybillLdSubRecordQueryDTO;
import com.cainiao.waybill.bridge.model.common.AbstractDAO;
import com.cainiao.waybill.bridge.model.dao.WaybillLdOutRecordDAO;
import com.cainiao.waybill.bridge.model.domain.WaybillLdOutRecordDO;
import com.cainiao.waybill.bridge.model.dto.WaybillLdOutRecordUpdateDTO;
import com.taobao.cainiao.waybill.constants.WaybillErrorConstant;
import com.taobao.common.dao.persistence.exception.DAOException;
import com.taobao.tddl.client.sequence.exception.SequenceException;
import com.taobao.tddl.client.sequence.impl.GroupSequence;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/1/17-下午4:14
 */
public class WaybillLdOutRecordDAOImpl extends AbstractDAO implements WaybillLdOutRecordDAO {

    @Resource
    private GroupSequence waybillLdOutRecordSequence;

    @Override
    public Integer updateId(WaybillLdOutRecordUpdateDTO updateDTO) throws DAOException {
        return this.executeUpdate("waybillLdOutRecord.updateId", updateDTO, getBridgeTDDLDBRoute());
    }

    @Override
    public Integer insert(WaybillLdOutRecordDO ldOutRecordDO) throws DAOException {
        ldOutRecordDO.setId(getId());
        return this.executeUpdate("waybillLdOutRecord.insert", ldOutRecordDO, getBridgeTDDLDBRoute());
    }

    @Override
    public List<WaybillLdOutRecordDO> queryWithMailNo(String mailNo) throws DAOException {
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put("mailNo", mailNo);
        List list = this.executeQueryForList("waybillLdOutRecord.queryWithMailNo", paramMap, getBridgeTDDLDBRoute());
        return dealQueryResult(list);
    }

    @Override
    public List<WaybillLdOutRecordDO> queryWithCreateTime(WaybillLdSubRecordCreateTimeQueryDTO queryDTO) throws DAOException {
        List list = this.executeQueryForList("waybillLdOutRecord.queryWithCreateTime", queryDTO, getBridgeTDDLDBRoute());
        return dealQueryResult(list);
    }

    @Override
    public List<WaybillLdOutRecordDO> query(WaybillLdSubRecordQueryDTO ldSubRecordQueryDTO) throws DAOException {
        List list = this.executeQueryForList("waybillLdOutRecord.query", ldSubRecordQueryDTO, getBridgeTDDLDBRoute());
        return dealQueryResult(list);
    }

    @Override
    public Integer countWithId(Long id) throws DAOException {
        Object rowCount = this.executeQueryForObject("waybillLdOutRecord.countWithId", id, getBridgeTDDLDBRoute());
        System.out.println("countWithId, id=" + id + ", rowCount=" + rowCount);
        return (Integer) rowCount;
    }

    private List<WaybillLdOutRecordDO> dealQueryResult(List list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<WaybillLdOutRecordDO> result = new ArrayList<>(list.size());
        for (Object item : list) {
            result.add((WaybillLdOutRecordDO) item);
        }
        return result;
    }

    @Override
    public Long getId() throws DAOException {
        return nextId();
    }

    //通过TDDL获取唯一主键
    private Long nextId() throws DAOException {
        try {
            return waybillLdOutRecordSequence.nextValue();
        } catch (SequenceException e) {
            throw new DAOException(WaybillErrorConstant.SystemError.TDDL_SEQUENCE_ERROR.getErrorMsg(), e);
        } catch (Throwable e) {
            throw new DAOException(e);
        }
    }
}
