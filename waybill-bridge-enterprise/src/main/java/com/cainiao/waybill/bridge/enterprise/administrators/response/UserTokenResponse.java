package com.cainiao.waybill.bridge.enterprise.administrators.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 用户详情
 * <AUTHOR>
 * @date 2025-04-16 10:35:36
 */
@Data
public class UserTokenResponse implements Serializable {

    private static final long serialVersionUID = -7221166732291084225L;
    /**
     * 用户昵称
     */
    private String nick;

    /**
     * 头像
     */
    private String avatarUrl;

    /**
     * 手机号
     */
    private Boolean mobile;

    /**
     * openId
     */
    private String openId;

    /**
     * 唯一ID
     */
    private String unionId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 国家编码
     */
    private String stateCode;

    /**
     *  是否访客
     */
    private Boolean visitor;


}
