package com.cainiao.waybill.bridge.biz.link.pickup.service.impl.prequery;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSONObject;

import com.cainiao.waybill.bridge.biz.link.pickup.service.impl.check.PickUpCheckAvailableManager;
import com.cainiao.waybill.bridge.biz.link.pickup.service.impl.check.PickUpCheckResult;
import com.cainiao.waybill.bridge.biz.pickup.dto.PickUpBaseInfoRequest;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpCpConfigInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.config.PickUpCpPreQueryInfo;
import com.cainiao.waybill.bridge.biz.pickup.dto.create.pre.PickUpCreatePreQueryResponse;
import com.cainiao.waybill.bridge.biz.pickup.dto.create.pre.PickUpCreatePreTdAppointTimeDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.create.pre.PickUpCreatePreTdAppointTimeSlotDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.create.pre.PickUpCreatePreTdTimeSelectDTO;
import com.cainiao.waybill.bridge.biz.pickup.dto.route.RouteReachableResult;
import com.cainiao.waybill.bridge.biz.utils.pickup.OrderAppointDealUtil;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.common.exception.BridgeBusinessException;
import com.cainiao.waybill.bridge.common.util.BridgeDateUtil;
import com.cainiao.waybill.bridge.common.util.ListUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zouping.fzp
 * @Classname PickUpPreQueryManager
 * @Description
 * @Date 2023/8/11 10:21 上午
 * @Version 1.0
 */
@Component
public class PickUpPreQueryManager {

    @Resource
    private PickUpCheckAvailableManager pickUpCheckAvailableManager;

    public PickUpCreatePreQueryResponse preQuery(PickUpBaseInfoRequest request) {

        // 校验cp和是否可达
        PickUpCheckResult pickUpCheckResult = pickUpCheckAvailableManager.checkAvailable(request, true);

        PickUpLogUtil.info("可达性校验完成.pickUpCheckResult:{}", JSONObject.toJSONString(pickUpCheckResult));

        if (CollectionUtils.isEmpty(pickUpCheckResult.getAvailableCpList())) {
            throw new BridgeBusinessException("cp_not_available", pickUpCheckResult.getUnavailableReason());
        }

        boolean selected = Boolean.TRUE.equals(pickUpCheckResult.getAvailable()) && CollectionUtils.isNotEmpty(
            pickUpCheckResult.getAvailableCpList());

        PickUpCreatePreQueryResponse preQueryResponse = new PickUpCreatePreQueryResponse();

        // 这里任选一个cp的配置，返回其预约时间信息
        PickUpCpConfigInfo pickUpCpConfigInfo = pickUpCheckResult.getAvailableCpList().get(0);
        PickUpCpPreQueryInfo pickUpCpPreQueryInfo = pickUpCpConfigInfo.getPickUpCpPreQueryInfo();

        // 填充预约时间
        if (pickUpCpPreQueryInfo != null && Boolean.FALSE.equals(pickUpCpPreQueryInfo.getFixTimeScale())) {
            // 非固定时间段
            RouteReachableResult routeReachableResult = pickUpCheckResult.getRouteReachableResult().get(
                pickUpCpConfigInfo);
            if (routeReachableResult != null) {
                convertDateFormat(routeReachableResult.getTdTimeSelectDTOList());
                preQueryResponse.setCpTimeSelectList(routeReachableResult.getTdTimeSelectDTOList());
            }
        } else if (pickUpCpPreQueryInfo != null) {
            // 固定时间段
            PickUpCreatePreQueryResponse response = OrderAppointDealUtil.getAppointTime(
                pickUpCpPreQueryInfo.getRealTimeScale(),
                pickUpCpPreQueryInfo.getRealTimeTip(),
                pickUpCpPreQueryInfo.getLastHour(),
                Integer.parseInt(pickUpCpPreQueryInfo.getAppointDays()),
                pickUpCpPreQueryInfo.getAppointTimeScale(),
                selected);
            preQueryResponse.setCpTimeSelectList(response.getCpTimeSelectList());
        }
        return preQueryResponse;
    }

    private void convertDateFormat(List<PickUpCreatePreTdTimeSelectDTO> list) {
        for (PickUpCreatePreTdTimeSelectDTO pickUpCreatePreTdTimeSelectDTO : ListUtil.non(list)) {
            for (PickUpCreatePreTdAppointTimeDTO pickUpCreatePreTdAppointTimeDTO : ListUtil.non(
                pickUpCreatePreTdTimeSelectDTO.getAppointTimes())) {
                for (PickUpCreatePreTdAppointTimeSlotDTO pickUpCreatePreTdAppointTimeSlotDTO : ListUtil.non(
                    pickUpCreatePreTdAppointTimeDTO.getTimeList())) {
                    convertStartTime(pickUpCreatePreTdAppointTimeSlotDTO);
                    convertEndTime(pickUpCreatePreTdAppointTimeSlotDTO);
                }
            }
        }
    }

    private void convertStartTime(PickUpCreatePreTdAppointTimeSlotDTO appointTimeSlotDTO) {
        if (StringUtils.isBlank(appointTimeSlotDTO.getStartTime())) {
            return;
        }
        Date date = BridgeDateUtil.strToDate(appointTimeSlotDTO.getStartTime(),
            BridgeDateUtil.pattern24NoSeconds);
        if (date == null) {
            return;
        }
        appointTimeSlotDTO.setStartTime(
            BridgeDateUtil.dateToStr(date, BridgeDateUtil.patternHourMinute));
    }

    private void convertEndTime(PickUpCreatePreTdAppointTimeSlotDTO appointTimeSlotDTO) {
        if (StringUtils.isBlank(appointTimeSlotDTO.getEndTime())) {
            return;
        }
        Date date = BridgeDateUtil.strToDate(appointTimeSlotDTO.getEndTime(),
            BridgeDateUtil.pattern24NoSeconds);
        if (date == null) {
            return;
        }
        appointTimeSlotDTO.setEndTime(
            BridgeDateUtil.dateToStr(date, BridgeDateUtil.patternHourMinute));
    }
}
