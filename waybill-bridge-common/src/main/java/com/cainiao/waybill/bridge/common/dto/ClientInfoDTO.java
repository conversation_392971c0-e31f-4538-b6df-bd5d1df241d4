package com.cainiao.waybill.bridge.common.dto;


/**
 * 调用方信�?
 *
 * <AUTHOR>
 * @since Jan 27, 2016 2:44:51 PM
 */
public class ClientInfoDTO extends BaseDTO {

    private static final long serialVersionUID = -7807434368320742657L;
    private String ip;                                                                             // 源调用ip
    private String appName;                                                                        // 应用名字
    private String operator;                                                                       // 操作人
    private String description;                                                                    //调用方描述
    private String version;                                                                        // 客户端版本号

    public ClientInfoDTO() {
        super();
    }

    public ClientInfoDTO(String appName, String operator) {
        super();
        this.appName = appName;
        this.operator = operator;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    @Override
    public int hashCode() {
        int result = ip != null ? ip.hashCode() : 0;
        result = 31 * result + (appName != null ? appName.hashCode() : 0);
        result = 31 * result + (operator != null ? operator.hashCode() : 0);
        result = 31 * result + (description != null ? description.hashCode() : 0);
        result = 31 * result + (version != null ? version.hashCode() : 0);
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        ClientInfoDTO that = (ClientInfoDTO) o;

        if (ip != null ? !ip.equals(that.ip) : that.ip != null) {
            return false;
        }
        if (appName != null ? !appName.equals(that.appName) : that.appName != null) {
            return false;
        }
        if (operator != null ? !operator.equals(that.operator) : that.operator != null) {
            return false;
        }
        if (description != null ? !description.equals(that.description) : that.description != null) {
            return false;
        }
        return !(version != null ? !version.equals(that.version) : that.version != null);

    }
}
