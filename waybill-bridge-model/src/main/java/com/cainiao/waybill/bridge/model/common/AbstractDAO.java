package com.cainiao.waybill.bridge.model.common;

import com.cainiao.waybill.bridge.common.util.Page;
import com.google.common.collect.Maps;
import com.taobao.cainiao.waybill.utils.StringUtils;
import com.taobao.common.dao.persistence.DBRoute;
import com.taobao.common.dao.persistence.SqlMapBaseDAOSupport;
import com.taobao.common.dao.persistence.exception.DAOException;

import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA. User: z<PERSON>tianfeng Date: 12-3-30 Time: 上午10:17 To change this
 * template use File | Settings | File Templates.
 */
public abstract class AbstractDAO extends SqlMapBaseDAO {


    @Override
    public void setSqlDaoBaseSupport(SqlMapBaseDAOSupport sqlDaoBaseSupport) {
        super.setSqlDaoBaseSupport(sqlDaoBaseSupport);
    }

    protected DBRoute getDBRoute() {
        DBRoute back = new DBRoute();
        back.setXid("WAYBILL");
        return back;
    }

    protected DBRoute getTDDLDBRoute() {
        DBRoute back = new DBRoute();
        back.setXid("MAGNETO_TDDL");
        return back;
    }

    protected DBRoute getBridgeTDDLDBRoute() {
        DBRoute back = new DBRoute();
        back.setXid("BRIDGE_TDDL");
        return back;
    }

    protected Object insert(String statementName, Object parameterObject) throws DAOException {
        return insert(statementName, parameterObject, getDBRoute());
    }

    protected Object insert(String statementName, Object parameterObject, DBRoute dr) throws DAOException {
        if (dr == null) {
            return super.executeInsert(statementName, parameterObject, getDBRoute());
        } else {
            return super.executeInsert(statementName, parameterObject, dr);
        }
    }

    @Override
    protected Object executeInsertOrUpdate(String countStatementName, String insertStatementName, String updateStatementName, Object parameterObject, DBRoute dr)
            throws DAOException {
        if (dr == null) {
            return super.executeInsertOrUpdate(countStatementName, insertStatementName, updateStatementName, parameterObject, getDBRoute());
        } else {
            return super.executeInsertOrUpdate(countStatementName, insertStatementName, updateStatementName, parameterObject, dr);
        }
    }

    @Override
    @SuppressWarnings("rawtypes")
    protected List executeQueryForList(String statementName, Object parameterObject, DBRoute dr) throws DAOException {
        if (dr == null) {
            return super.executeQueryForList(statementName, parameterObject, getDBRoute());
        } else {
            return super.executeQueryForList(statementName, parameterObject, dr);
        }
    }

    @Override
    @SuppressWarnings("rawtypes")
    protected List executeQueryForMergeSortList(String statementName, Object parameterObject, int startResult, int maxResults, String orderByMethodString, DBRoute dr)
            throws DAOException {
        if (dr == null) {
            return super.executeQueryForMergeSortList(statementName, parameterObject, startResult, maxResults, orderByMethodString, getDBRoute());
        } else {
            return super.executeQueryForMergeSortList(statementName, parameterObject, startResult, maxResults, orderByMethodString, dr);
        }
    }

    protected Object queryForObject(String statementName, Object parameterObject) throws DAOException {
        return super.executeQueryForObject(statementName, parameterObject, getDBRoute(), Boolean.FALSE);
    }

    protected Object queryForObject(String statementName, Object parameterObject, DBRoute dr, boolean isExistsExit) throws DAOException {
        if (dr == null) {
            return super.executeQueryForObject(statementName, parameterObject, getDBRoute(), isExistsExit);
        } else {
            return super.executeQueryForObject(statementName, parameterObject, dr, isExistsExit);
        }
    }

    @Override
    protected Object executeQueryForObject(String statementName, Object parameterObject, DBRoute dr) throws DAOException {
        if (dr == null) {
            return super.executeQueryForObject(statementName, parameterObject, getDBRoute());
        } else {
            return super.executeQueryForObject(statementName, parameterObject, dr);
        }
    }

    @Override
    protected Map executeQueryForMap(String statementName, Object parameterObject, DBRoute dr, String key) throws DAOException {
        if (dr == null) {
            return super.executeQueryForMap(statementName, parameterObject, getDBRoute(), key);
        } else {
            return super.executeQueryForMap(statementName, parameterObject, dr, key);
        }
    }

    protected int update(String statementName, Object parameterObject) throws DAOException {
        return super.executeUpdate(statementName, parameterObject, getDBRoute());
    }

    protected int update(String statementName, Object parameterObject, DBRoute dr) throws DAOException {
        if (dr == null) {
            return super.executeUpdate(statementName, parameterObject, getDBRoute());
        } else {
            return super.executeUpdate(statementName, parameterObject, dr);
        }
    }

    protected int toQueryCount(List<Integer> countList) {
        Integer totalNumber = 0;
        if (countList != null) {
            for (int count : countList) {
                totalNumber += count;
            }
        }
        return totalNumber;
    }

    protected Map<String, Object> getDBAndTBIndex(Long sellerId, Map<String, Object> param) {
        if (param == null) {
            param = Maps.newHashMap();
        }

        Long tbIndex = sellerId % 4096;
        Long dbIndex = tbIndex / 128;

        param.put("dbIndex", StringUtils.complementString(dbIndex.toString(), 4, '0'));
        param.put("tbIndex", StringUtils.complementString(tbIndex.toString(), 4, '0'));
        return param;
    }

    protected <E> Page<E> executeQueryForPage(String statementName, Map<String, Object> parameterObject, Integer pageSize, Integer pageIndex, DBRoute dr) throws DAOException {
        if (dr == null) {
            return super.executeQueryForPage(statementName, statementName + "_count", parameterObject, pageSize, pageIndex, getDBRoute());
        } else {
            return super.executeQueryForPage(statementName, statementName + "_count", parameterObject, pageSize, pageIndex, dr);
        }
    }

    protected <E> Page<E> executeQueryForPageWithCount(String statementName, Map<String, Object> parameterObject, Integer pageSize, Integer pageIndex, DBRoute dr)
            throws DAOException {
        if (dr == null) {
            return super.executeQueryForPageWithCount(statementName, statementName + "_count", parameterObject, pageSize, pageIndex, getDBRoute());
        } else {
            return super.executeQueryForPageWithCount(statementName, statementName + "_count", parameterObject, pageSize, pageIndex, dr);
        }
    }
}
