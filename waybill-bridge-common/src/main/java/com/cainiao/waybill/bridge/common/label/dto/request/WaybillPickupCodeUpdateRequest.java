package com.cainiao.waybill.bridge.common.label.dto.request;


import com.cainiao.waybill.bridge.common.dto.BaseDTO;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/4/25.
 */
public class WaybillPickupCodeUpdateRequest extends BaseDTO {
    private static final long serialVersionUID = -3707748585493190962L;

    /**
     * cp 编码
     */
    private String cpCode;

    /**
     * 电子面单编码
     */
    private String waybillCode;

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public String getWaybillCode() {
        return waybillCode;
    }

    public void setWaybillCode(String waybillCode) {
        this.waybillCode = waybillCode;
    }
}
