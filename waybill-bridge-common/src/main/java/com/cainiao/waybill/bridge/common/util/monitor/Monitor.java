package com.cainiao.waybill.bridge.common.util.monitor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 监控埋点工具
 * 使用方法：
 * 1） 绝大部分场景使用 stat(....) 方式
 * <pre>
 *      Monitor.stat("not_expire", "2", newWheel ? "new" : "old").count();
 * </pre>
 * 2） 对于调用量高，key值已知的场景，可以使用register(....) 方式拿到单例，用于 优化gc 和 map的读取计算
 * <pre>
 *      private static final Stat TPP_CONTRACT_PRE_CAL_NULL = Monitor.register("tpp_contract_pre_cal", "null");
 *      ...
 *      ...
 *      TPP_CONTRACT_PRE_CAL_NULL.count();
 * </pre>
 * <AUTHOR> on 2019-07-22.
 */
public final class Monitor {
    private final static Logger log = LoggerFactory.getLogger("biz_monitor");

    private final static AtomicReference<Map<Key, Stat>> STATS_REF = new AtomicReference<>(new ConcurrentHashMap<>(16));
    private static int statsCount = 16;

    private static final ScheduledExecutorService SCHEDULED = new ScheduledThreadPoolExecutor(1);

    private final static Map<Key, StatHolder> STATS_HOLDERS = new ConcurrentHashMap<>(16);

    static {
        SCHEDULED.scheduleAtFixedRate(schedule(), 5, 15, TimeUnit.SECONDS);
        Runtime.getRuntime().addShutdownHook(new Thread(() -> schedule().run()));
    }

    private static Runnable schedule() {
        return () -> {
            final Map<Key, Stat> pre = STATS_REF.getAndSet(new ConcurrentHashMap<>(statsCount));
            statsCount = pre.size();

            // copy old
            Map<Key, Stat> copy = STATS_HOLDERS.entrySet().stream().filter(entry -> entry.getValue().hasStated()).collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().renew()));

            // promise stats cool down
            try {
                Thread.sleep(200);
            } catch (InterruptedException ignore) {
            }

            pre.forEach((k, v) -> log.info("{}|{}", k, v));

            copy.forEach((k, v) -> log.info("{}|{}", k, v));

        };
    }
    private Monitor() {}

    public static Stat stat(String key1, String key2, String key3) {
        return getStat(new Key(key1, key2, key3));
    }

    public static Stat stat(String key1, String key2, String key3, String key4) {
        return getStat(new Key(key1, key2, key3, key4));
    }

    public static Stat stat(String key1, String key2) {
        return getStat(new Key(key1, key2, ""));
    }

    public static Stat stat(String key1) {
        return getStat(new Key(key1, "", ""));
    }

    public static Stat register(String key1, String key2, String key3, String key4) {
        return register(new Key(key1, key2, key3, key4));
    }

    public static Stat register(String key1, String key2, String key3) {
        return register(new Key(key1, key2, key3));
    }

    public static Stat register(String key1, String key2) {
        return register(new Key(key1, key2, ""));
    }

    public static Stat register(String key1) {
        return register(new Key(key1, "", ""));
    }

    private static Stat getStat(Key key) {
        final Map<Key, Stat> stats = STATS_REF.get();
        Stat stat = stats.get(key);
        if (stat == null) {
            stat = new StatInner();
            final Stat s = stats.putIfAbsent(key, stat);
            if (s != null) {
                return s;
            }
        }
        return stat;
    }

    private static Stat register(Key key) {
        StatHolder stat = STATS_HOLDERS.get(key);
        if (stat == null) {
            stat = new StatHolder();
            final Stat s = STATS_HOLDERS.putIfAbsent(key, stat);
            if (s != null) {
                return s;
            }
        }
        return stat;
    }
}
