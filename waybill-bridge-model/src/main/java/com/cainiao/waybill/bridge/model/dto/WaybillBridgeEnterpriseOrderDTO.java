package com.cainiao.waybill.bridge.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * This class was generated by Ali-Generator
 * <AUTHOR>
 */
@Data
public class WaybillBridgeEnterpriseOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *   主键
     */
    private Long id;

    /**
     *   创建时间
     */
    private Date gmtCreate;

    /**
     *   修改时间
     */
    private Date gmtModified;

    /**
     *   企业id
     */
    private String corpId;

    /**
     *   订单业务号（业务唯一）
     */
    private String outerOrderCode;

    /**
     *   打印机编码
     */
    private String printerId;

    /**
     *   运单号
     */
    private String waybillCode;

    /**
     *   运力服务提供方
     */
    private String cpCode;

    /**
     *  cpName
     */
    private String cpName;


    /**
     *   场地唯一ID 外键
     */
    private String locationId;

    /**
     *   场地名称
     */
    private String locationName;

    /**
     *   小邮局唯一ID 外键
     */
    private String postId;

    /**
     *   月结账号
     */
    private String waybillAccountNo;

    /**
     *   寄件人姓名
     */
    private String senderName;

    /**
     *   寄件人电话
     */
    private String senderPhone;

    /**
     *   寄件人手机
     */
    private String senderMobile;

    /**
     *   寄件人地址
     */
    private AddressInfo senderAddress;

    /**
     *   收件人姓名
     */
    private String consigneeName;

    /**
     *   收件人电话
     */
    private String consigneePhone;

    /**
     *   收件人手机
     */
    private String consigneeMobile;

    /**
     *   收件人地址
     */
    private AddressInfo consigneeAddress;

    /**
     *   订单状态
     * com.cainiao.waybill.bridge.enterprise.common.enums.EnterpriseOrderStatusEnum
     */
    private Integer status;

    /**
     *   订单状态
     */
    private String statusDesc;

    /**
     *   物流状态
     */
    private Integer action;

    /**
     *   物流状态
     */
    private String actionDesc;

    /**
     *   物品类型
     */
    private Integer item;

    /**
     *   物品类型
     */
    private String itemDesc;

    /**
     *   重量
     */
    private Integer weight;

    /**
     *   寄件结算类型
     */
    private Integer product;

    /**
     *   寄件结算类型
     */
    private String productDesc;

    /**
     *   备注
     */
    private String remark;

    /**
     *   扩展字段
     */
    private String feature;

    /**
     *   费用归属
     */
    private String costAllocation;

    /**
     *   业务类型
     */
    private Integer businessType;

    /**
     *   用户id
     */
    private String userId;


    /**
     *   揽收时间
     */
    private Date gotTime;

    /**
     *   称重时间
     *
     */
    private Date weighingTime;

    /**
     *   取消时间
     *
     */
    private Date cancelTime;

    /**
     *   支付时间
     */
    private Date payTime;


    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 操作人
     */
    private String userName;

    /**
     * 小邮局名称
     */
    private String postName;

    /**
     * 是否保价
     * 0-不保价 1-保价
     */
    private Integer insured;

    /**
     * 保价金额
     */
    private String insuredValue;
}