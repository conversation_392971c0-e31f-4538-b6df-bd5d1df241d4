package com.cainiao.waybill.bridge.biz.fee.service;

import com.cainiao.waybill.bridge.model.domain.WaybillPickUpDetailDO;
import com.cainiao.waybill.number.common.Result;
import org.springframework.web.multipart.MultipartFile;

/**
 * 淘外预充值服务
 * <AUTHOR>
 * @date 2024/9/4 16:52
 **/
public interface PrePayService {

    /**
     * 预充值扣款
     * @param existDO
     * @param cpPrice 菜鸟价格 单位:元
     * @param remark
     */
    void doWtPay(WaybillPickUpDetailDO existDO, String cpPrice, String remark);

    /**
     * 上传文件执行调账
     * @param file
     * @return
     */
    Result<String> uploadRecharge(MultipartFile file);
}
