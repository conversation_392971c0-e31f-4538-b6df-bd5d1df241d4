package com.cainiao.waybill.bridge.biz.fast.enums;

/**
 * <AUTHOR>
 * @date 2024/8/16 15:44
 * 收寄人信息查询类型
 */
public enum OrderQueryTypeEnum {

    /**
     * 1 寄件人信息
     */
    SENDER(1),
    /**
     * 2 收件人信息
     */
    RECEIVE(2);

    private Integer code;

    OrderQueryTypeEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

}
