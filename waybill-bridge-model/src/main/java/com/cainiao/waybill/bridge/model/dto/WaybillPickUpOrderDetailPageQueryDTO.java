package com.cainiao.waybill.bridge.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/18-上午11:29
 */
@Data
public class WaybillPickUpOrderDetailPageQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Date startTime;
    private Date endTime;

    private String fromAppKey;
    private String cpCode;
    private List<Integer> status;

    private Integer limitIndex;
    private Integer limitSize;
}
