// 右键菜单，可以一键生成哦。
// 配置格式参见 https://yuque.antfin-inc.com/xd6pvi/dr962p 
{
	"anno":true,
	"antZdal":false,
	"appList":[
		"CAINIAO_WAYBILL_BRIDGE_APP$CAINIAO_WAYBILL_BRIDGE_GROUP"
	],
	"appName":"CAINIAO_WAYBILL_BRIDGE_APP",
	"blobEnable":true,
	"businessPackageName":"",
	"businessTargetProject":"waybill-bridge-start/src/main/java",
	"catlog":"cainiao_waybill_bridge",
	"columnName":"id",
	"connectionURL":"",
	"createBaseType":false,
	"disableNamespace":false,
	"domainObjectName":"BillAdjustInfo",
	"driverClass":"",
	"enableDeleteMethod":true,
	"fileEncode":"UTF-8",
	"generateBusiness":false,
	"generateKey":false,
	"groupKey":"CAINIAO_WAYBILL_BRIDGE_GROUP",
	"ignoreColumn":false,
	"ignoreColumnValue":"id",
	"javaClientGeneratorTargetPackage":"com.cainiao.waybill.bridge.model.mapper",
	"javaClientGeneratorTargetProject":"waybill-bridge-model/src/main/java",
	"javaClientGeneratorType":"XMLMAPPER",
	"javaModelGeneratorTargetPackage":"com.cainiao.waybill.bridge.model.domain",
	"javaModelGeneratorTargetProject":"waybill-bridge-model/src/main/java",
	"lastBizPackage":"",
	"lastBizProjectPath":"waybill-bridge-start/src/main/java",
	"lastTypeHadler":"",
	"lastTypeJava":"",
	"mapperName":"BillAdjustInfoMapper",
	"mergePolicy":1,
	"modelType":"flat",
	"myDataBase":null,
	"password":"",
	"pattern":"_[0-9]+$",
	"replaceIs":false,
	"saveParameter":true,
	"saveParameterPath":".aligenerator",
	"security":true,
	"serializable":false,
	"simpleMethod":true,
	"split":false,
	"sqlMapGeneratorTargetPackage":"com.cainiao.waybill.bridge.model.mapper",
	"sqlMapGeneratorTargetProject":"waybill-bridge-model/src/main/resources",
	"sqlStatement":"JDBC",
	"tableName":"bill_adjust_info",
	"tableSchema":"",
	"targetRuntime":"MyBatis3",
	"tddl":true,
	"tddlMap":{
		"CAINIAO_WAYBILL_BRIDGE_APP":"CAINIAO_WAYBILL_BRIDGE_GROUP"
	},
	"typeHandlerMap":{},
	"typeMap":{},
	"useBaseDao":false,
	"useDBTime":false,
	"useDao":false,
	"useLombok":false,
	"useSqlCreate":false,
	"userId":"",
	"whenPresent":true,
	"generatedBy":"Aligenerator",
	"schemaVersion":"1.0"
}