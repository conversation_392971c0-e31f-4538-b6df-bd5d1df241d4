package com.cainiao.waybill.bridge.enterprise.administrators.service;

import com.cainiao.waybill.bridge.enterprise.administrators.request.EnterpriseSettleAccountRequest;
import com.cainiao.waybill.bridge.model.dto.BridgePagingDTO;
import com.cainiao.waybill.bridge.model.dto.WaybillEnterpriseSettleAccountDTO;

import java.util.List;


/**
 * 该类用于管理月结账号的服务接口
 * <AUTHOR>
 * @date 2025-04-23 15:18:59
 */
public interface EnterpriseSettleAccountService {
    /**
     * 获取月结账号列表
     *
     * @param request request
     * @return 用户信息
     */
    BridgePagingDTO<WaybillEnterpriseSettleAccountDTO> pageList(EnterpriseSettleAccountRequest request);


    /**
     * 获取办公场地列表
     *
     * @param request request
     * @return 用户信息
     */
    List<WaybillEnterpriseSettleAccountDTO> list(EnterpriseSettleAccountRequest request);

    /**
     * 新增月结账号
     * @param request request
     * @return 是否成功
     */
    int addSettleAccount(EnterpriseSettleAccountRequest request);

    /**
     * 编辑月结账号
     * @param request
     * @return 是否成功
     */
    int editSettleAccount(EnterpriseSettleAccountRequest request);

    /**
     * 删除月结账号
     * @param id id
     * @return 是否成功
     */
    int deleteById(Long id);

    /**
     * 根据id获取月结账号
     * @param waybillAccountId waybillAccountId
     * @return
     */
    WaybillEnterpriseSettleAccountDTO getByWaybillAccountIdAndCorpId(String waybillAccountId, String corpId);

    /**
     * 删除月结账号
     * @param waybillAccountId id
     * @return 是否成功
     */
    int removeByWaybillAccountId(String waybillAccountId, String corpId);


}