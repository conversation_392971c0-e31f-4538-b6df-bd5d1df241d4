package com.cainiao.waybill.bridge.innersupport.ad.util;

import com.alibaba.common.lang.ExceptionUtil;
import com.cainiao.waybill.bridge.innersupport.ad.constant.commom.config.diamond.WaybillAdWorkOrderDiamondConfig;
import com.cainiao.waybill.bridge.innersupport.ad.constant.emums.AdCustomRobotGroupTypeEnum;
import com.cainiao.waybill.bridge.innersupport.ad.constant.emums.AdUserRoleEnum;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.taobao.api.ApiException;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * 钉钉群自定义机器人群消息
 */
public class CustomRobotGroupMessage {
    public static final Logger infoLogger = LoggerFactory.getLogger("WAYBILL_ADWORKORDER_INFO");

    /**
     * 发送群消息
     */
    public static void sendGroupMessage(String adCustomRobotGroupType, String adCustomRobotGroupTarget, String atUserRoleType, String messageContent) {
        WaybillAdWorkOrderDiamondConfig.WaybillAdWorkOrderConfig config = WaybillAdWorkOrderDiamondConfig.getConfig();
        List<WaybillAdWorkOrderDiamondConfig.CainiaoCustomerService> cainiaoCustomerServiceList = config.getCainiaoCustomerServiceList();
        List<WaybillAdWorkOrderDiamondConfig.Advertiser> advertiserList = config.getAdvertiserList();
        List<WaybillAdWorkOrderDiamondConfig.CP> cpList = config.getCpList();
        Boolean customRobotGroupMessageEnableSwitch = config.getCustomRobotGroupMessageEnableSwitch();
        List<String> customRobotGroupMessageAllowAdvertiserIdList = config.getCustomRobotGroupMessageAllowAdvertiserIdList();
        List<String> customRobotGroupMessageAllowCpCodeList = config.getCustomRobotGroupMessageAllowCpCodeList();
        WaybillAdWorkOrderDiamondConfig.Advertiser targetAdvertiser = null;
        WaybillAdWorkOrderDiamondConfig.CP targetCP = null;
        WaybillAdWorkOrderDiamondConfig.CainiaoCustomerService customerService = cainiaoCustomerServiceList.get(0);
        // 是否允许发送钉钉群消息开关
        if (!customRobotGroupMessageEnableSwitch) {
            // 未配置菜鸟客服校验
            return;
        }
        // 未配置菜鸟客服校验
        if (customerService == null) {
            return;
        }
        // 菜鸟&广告主所在的钉钉群
        if (adCustomRobotGroupType.equals(AdCustomRobotGroupTypeEnum.CAINIAO_AND_ADVERTISER_DING_TALK_GROUP.getValue())) {
            if (customRobotGroupMessageAllowAdvertiserIdList.contains(adCustomRobotGroupTarget)) {
                // 去获取钉钉群的配置
                for (WaybillAdWorkOrderDiamondConfig.Advertiser advertiser : advertiserList) {
                    String advertiserId = advertiser.getAdvertiserId();
                    if (adCustomRobotGroupTarget.equals(advertiserId)) {
                        targetAdvertiser = advertiser;
                        break;
                    }
                }
                if (targetAdvertiser != null) {
                    // 发送钉钉信息
                    String customRobotSecret = targetAdvertiser.getCustomRobotSecret();
                    String customRobotToken = targetAdvertiser.getCustomRobotToken();
                    List<String> atUserMobileList = targetAdvertiser.getLoginMobileList();
                    if (atUserRoleType.equals(AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue())) {
                        atUserMobileList = customerService.getLoginMobileList();
                    }
                    dingTalkClientSendGroupMessage(customRobotSecret, customRobotToken, atUserMobileList, messageContent);
                }
            }
        }
        // 菜鸟客服&快递公司钉钉群
        if (adCustomRobotGroupType.equals(AdCustomRobotGroupTypeEnum.CAINIAO_AND_COURIER_COMPANY_DING_TALK_GROUP.getValue())) {
            if (customRobotGroupMessageAllowCpCodeList.contains(adCustomRobotGroupTarget)) {
                // 去获取钉钉群的配置
                for (WaybillAdWorkOrderDiamondConfig.CP cp : cpList) {
                    String cpCode = cp.getCpCode();
                    if (adCustomRobotGroupTarget.equals(cpCode)) {
                        targetCP = cp;
                        break;
                    }
                }
                if (targetCP != null) {
                    // 发送钉钉信息
                    String customRobotSecret = targetCP.getCustomRobotSecret();
                    String customRobotToken = targetCP.getCustomRobotToken();
                    List<String> atUserMobileList = targetCP.getLoginMobileList();
                    if (atUserRoleType.equals(AdUserRoleEnum.CAINIAO_CUSTOMER_SERVICE_ROLE.getValue())) {
                        atUserMobileList = customerService.getLoginMobileList();
                    }
                    dingTalkClientSendGroupMessage(customRobotSecret, customRobotToken, atUserMobileList, messageContent);
                }
            }
        }
    }

    /**
     * 使用钉钉，发送群消息
     */
    public static void dingTalkClientSendGroupMessage(String customRobotSecret, String customRobotToken, List<String> atUserMobileList, String messageContent) {
        try {
            if (StringUtils.isBlank(customRobotSecret)) {
                return;
            }
            if (StringUtils.isBlank(customRobotToken)) {
                return;
            }
            if (atUserMobileList == null || atUserMobileList.size() < 1) {
                return;
            }
            if (StringUtils.isBlank(messageContent)) {
                return;
            }
            Long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + customRobotSecret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(customRobotSecret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
            // sign字段和timestamp字段必须拼接到请求URL上，否则会出现 310000 的错误信息
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/robot/send?sign=" + sign + "&timestamp=" + timestamp);
            OapiRobotSendRequest req = new OapiRobotSendRequest();
            // 定义文本内容
            OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
            text.setContent(messageContent);
            // 定义 @ 对象
            OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
            at.setAtMobiles(atUserMobileList);
            // 设置消息类型
            req.setMsgtype("text");
            req.setText(text);
            req.setAt(at);
            client.execute(req, customRobotToken);
        } catch (ApiException e) {
            infoLogger.info("钉钉群消息发送失败：" + messageContent + ExceptionUtil.getStackTrace(e));
            e.printStackTrace();
        } catch (UnsupportedEncodingException e) {
            infoLogger.info("钉钉群消息发送失败：" + messageContent + ExceptionUtil.getStackTrace(e));
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            infoLogger.info("钉钉群消息发送失败：" + messageContent + ExceptionUtil.getStackTrace(e));
            throw new RuntimeException(e);
        } catch (InvalidKeyException e) {
            infoLogger.info("钉钉群消息发送失败：" + messageContent + ExceptionUtil.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }
}