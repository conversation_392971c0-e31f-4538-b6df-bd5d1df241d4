package com.cainiao.waybill.bridge.biz.label.manager.impl;

import java.text.SimpleDateFormat;
import java.util.Date;

import com.cainiao.waybill.bridge.biz.label.manager.LogManager;
import com.cainiao.waybill.common.dto.ClientInfoDTO;
import com.taobao.eagleeye.EagleEye;
import com.taobao.hsf.util.RequestCtxUtil;
import org.slf4j.Logger;
import org.springframework.stereotype.Service;

/**
 * 通用日志管理
 * 
 * <AUTHOR>
 * @since Feb 17, 2016 1:55:23 PM
 */
@Service
public class CommonLogManager implements LogManager {
	private static final int	LEVEL_INFO		= 1;
	private static final int	LEVEL_WARN		= 2;
	private static final int	LEVEL_ERROR		= 3;
	private static final int	LEVEL_EXCEPTION	= 4;
	private static final String	SEPARATOR		= "|";
	private static final String	DEPLOY_FLAG		= "-" + new SimpleDateFormat("dd").format(new Date());

	@Override
	public void logExceptionMessage(String action, String message, Throwable e, ClientInfoDTO clientInfo, Logger logger) {
		if (logger.isErrorEnabled()) {
			doLog(LEVEL_EXCEPTION, action, message, null, e, clientInfo, logger);
		}
	}

	@Override
	public void logExceptionMessage(String action, String message, Throwable e, Logger logger) {
		logExceptionMessage(action, message, e, null, logger);
	}
	@Override
	public void logErrorMessage(String action, String message, ClientInfoDTO clientInfo, Logger logger) {
		logErrorMessage(action, message, null, clientInfo, logger);
	}

	@Override
	public void logErrorMessage(String action, String message, Logger logger) {
		logErrorMessage(action, message, null, null, logger);
	}

	@Override
	public void logErrorMessage(String action, String messageFormat, Object[] argArray, ClientInfoDTO clientInfo,
			Logger logger) {
		if (logger.isErrorEnabled()) {
			doLog(LEVEL_ERROR, action, messageFormat, argArray, null, clientInfo, logger);
		}
	}

	@Override
	public void logErrorMessage(String action, String messageFormat, Object[] argArray, Logger logger) {
		logErrorMessage(action, messageFormat, argArray, null, logger);
	}

	@Override
	public void logWarnMessage(String action, String message, ClientInfoDTO clientInfo, Logger logger) {
		logWarnMessage(action, message, null, clientInfo, logger);
	}

	@Override
	public void logWarnMessage(String action, String message, Logger logger) {
		logWarnMessage(action, message, null, null, logger);
	}

	@Override
	public void logWarnMessage(String action, String messageFormat, Object[] argArray, ClientInfoDTO clientInfo,
                               Logger logger) {
		if (logger.isWarnEnabled()) {
			doLog(LEVEL_WARN, action, messageFormat, argArray, null, clientInfo, logger);
		}
	}

	@Override
	public void logWarnMessage(String action, String messageFormat, Object[] argArray, Logger logger) {
		logWarnMessage(action, messageFormat, argArray, null, logger);
	}

	@Override
	public void logInfoMessage(String action, String message, ClientInfoDTO clientInfo, Logger logger) {
		logInfoMessage(action, message, null, clientInfo, logger);
	}

	@Override
	public void logInfoMessage(String action, String message, Logger logger) {
		logInfoMessage(action, message, null, null, logger);
	}

	@Override
	public void logInfoMessage(String action, String messageFormat, Object[] argArray, ClientInfoDTO clientInfo,
			Logger logger) {
		if (logger.isInfoEnabled()) {
			doLog(LEVEL_INFO, action, messageFormat, argArray, null, clientInfo, logger);
		}
	}

	@Override
	public void logInfoMessage(String action, String messageFormat, Object[] argArray, Logger logger) {
		logInfoMessage(action, messageFormat, argArray, null, logger);
	}

	private void doLog(int logLevel, String action, String messageFormat, Object[] argArray, Throwable e,
					   ClientInfoDTO clientInfo, Logger logger) {
		StringBuilder sb = new StringBuilder();
		sb.append("traceId=").append(EagleEye.getTraceId()).append(DEPLOY_FLAG).append(SEPARATOR).append("action=")
				.append(action);
		if (e != null) {
			sb.append(SEPARATOR).append("exception=").append(e.getClass().getSimpleName());
		}
		sb.append(SEPARATOR).append("ip=").append(RequestCtxUtil.getClientIp()).append(SEPARATOR).append("clientInfo=")
				.append(clientInfo).append(SEPARATOR).append(messageFormat);

		String logMessage = sb.toString();
		if (argArray == null && LEVEL_EXCEPTION != logLevel) {
			argArray = new Object[] {};
		}
		if (LEVEL_INFO == logLevel) {
			logger.info(logMessage, argArray);
		} else if (LEVEL_WARN == logLevel) {
			logger.warn(logMessage, argArray);
		} else if (LEVEL_ERROR == logLevel) {
			logger.error(logMessage, argArray);
		} else if (LEVEL_EXCEPTION == logLevel) {
			logger.error(logMessage, e);
		} else {
			logger.error(logMessage, argArray);
		}
	}
}
