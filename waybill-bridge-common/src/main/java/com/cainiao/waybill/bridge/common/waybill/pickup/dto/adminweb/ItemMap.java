package com.cainiao.waybill.bridge.common.waybill.pickup.dto.adminweb;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 订单渠道实体
 *
 * <AUTHOR>
 * @date 2021/11/15-下午4:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItemMap implements Serializable {
    private static final long serialVersionUID = 1L;

    private String key;
    private String value;
}
