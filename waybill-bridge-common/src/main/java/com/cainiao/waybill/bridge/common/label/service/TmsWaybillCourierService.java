package com.cainiao.waybill.bridge.common.label.service;

import com.cainiao.waybill.bridge.common.dto.ClientInfoDTO;
import com.cainiao.waybill.bridge.common.result.BaseResultDTO;

/**
 * 通过link平台调用cp接口，获取小件员的信息
 * 
 * <AUTHOR>
 *
 */
public interface TmsWaybillCourierService {
	/**
	 * 获取小件员的id
	 * 
	 * @param cpCode
	 * @param qrCodeStr
	 * @param clientAppInfoO
	 * @return
	 */
	public BaseResultDTO<Long> getCourierIdForQianNiu(String cpCode, String qrCodeStr, ClientInfoDTO clientAppInfo);
}
