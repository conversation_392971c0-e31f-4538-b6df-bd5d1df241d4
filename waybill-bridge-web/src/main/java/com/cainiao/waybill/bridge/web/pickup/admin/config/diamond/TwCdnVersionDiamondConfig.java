package com.cainiao.waybill.bridge.web.pickup.admin.config.diamond;

import com.alibaba.common.lang.ExceptionUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONArray;
import com.cainiao.waybill.bridge.biz.utils.pickup.PickUpLogUtil;
import com.cainiao.waybill.bridge.web.pickup.constant.PickUpWebAdminConstant;
import com.google.common.collect.Maps;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/26-下午3:16
 */
public class TwCdnVersionDiamondConfig {

    public enum TagEnum{
        /**
         * 淘外小二工作台前端资源信息
         */
        ADMIN_WEB,
        ONE_FOUND,
        MANAGER,
        ALI_SOCIAL_OPERATIONS,
        ALI_SOCIAL_CHARITY_MANAGER,
        CNUI_VERSION,
        ENTERPRISE_WEB_VERSION,
        ENTERPRISE_WEB_URL,
    }

    private static final Map<String, String> CONFIG_INFO_MAP = Maps.newHashMap();

    private static void initConfig() {
        // 启动只用一次场景，直接get获取配置值
        try {
            String configInfo = Diamond
                    .getConfig(PickUpWebAdminConstant.DiamondConfig.PICK_UP_ORDER_REPORT_DATA_ID,
                            PickUpWebAdminConstant.DiamondConfig.PICK_UP_ORDER_REPORT_GROUP_ID, 3000);
            parseVmVersionConfig(configInfo);
        } catch (IOException e1) {
            PickUpLogUtil.info("获取diamond中配置的vm版本信息失败：" + ExceptionUtil.getStackTrace(e1));
        }
        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(PickUpWebAdminConstant.DiamondConfig.PICK_UP_ORDER_REPORT_DATA_ID, PickUpWebAdminConstant.DiamondConfig.PICK_UP_ORDER_REPORT_GROUP_ID,
                new ManagerListenerAdapter() {
                    @Override
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            parseVmVersionConfig(configInfo);
                        } catch (Exception e) {
                            PickUpLogUtil.info("ManagerListenerAdapter获取diamond中配置的vm版本信息失败：" + ExceptionUtil.getStackTrace(e));
                        }
                    }
                });
    }

    static {
        initConfig();
    }

    private static void parseVmVersionConfig(String configInfo) {
        if (StringUtil.isBlank(configInfo)) {
            return;
        }
        List<VmBean> vmBeans = JSONArray.parseArray(configInfo, VmBean.class);
        if (CollectionUtils.isEmpty(vmBeans)) {
            return;
        }
        for (VmBean vmBean : vmBeans) {
            CONFIG_INFO_MAP.put(vmBean.getTag(), vmBean.getLink());
        }
    }


    @Data
    static class VmBean {
        private String tag;
        private String link;
    }

    public static String getLink(String tag) {
        return CONFIG_INFO_MAP.get(tag);
    }
}
